{"compilerOptions": {"esModuleInterop": true, "skipLibCheck": true, "target": "es2022", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "strict": true, "noUncheckedIndexedAccess": true, "checkJs": true, "lib": ["dom", "dom.iterable", "ES2022"], "noEmit": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "preserve", "plugins": [{"name": "next"}], "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["jest", "./src/types/svg.d.ts"]}, "include": [".eslintrc.cjs", "next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.cjs", "**/*.js", ".next/types/**/*.ts", "src/components/ui/SisiensePatientDetails.old", "src/instrumentation.ts", "next.config.mjs", "next.config.js.old", "src/app/(platform)/measures/measure-details/loading.old"], "exclude": ["node_modules"]}