const config = require('./jest.config.cjs')

module.exports = {
  ...config,
  testTimeout: 30_000, // Set default timeout to 30 seconds for all tests
  transform: {
    '^.+\\.ts?$': [
      'ts-jest',
      {
        useESM: true,
      },
    ],
  },
  slowTestThreshold: 90,
  extensionsToTreatAsEsm: ['.ts'],
  testEnvironment: 'node',
  testRegex: '/tests/integration/.*\\.(test|spec)?\\.(ts|tsx)$',
}
