const dayjs = require('dayjs')
const { DateRange } = require('./src/lib/dateRange')
const { ScorecardView } = require('./src/enums/scorecardView')

const startDate = dayjs('10/1/2023', 'MM/DD/YYYY')
const endDate = dayjs('12/31/2024', 'MM/DD/YYYY')

const scorecardView = ScorecardView.Monthly

console.log(`ScorecardView: ${scorecardView}`) // Log the value of scorecardView

const result = DateRange.getColumnIntervalsByCategory(
  scorecardView,
  startDate,
  endDate
)

result.forEach((item, index) => {
  console.log(`    [${index}]: "${item}"`)
})
