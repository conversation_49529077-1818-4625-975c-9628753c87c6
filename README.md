# Platform nextGen

If you are not familiar with the different technologies used in this project, please refer to the respective docs.

- [Next.js](https://nextjs.org)
- [Auth.js](https://authjs.dev/)
- [Prisma](https://prisma.io)
- [Tailwind CSS](https://tailwindcss.com)
- [ShadCN](https://ui.shadcn.com/)
- [Zustand](https://zustand.docs.pmnd.rs/getting-started/introduction)

OIDC Well-known for citC: https://concertqa.medisolvdev.com/.well-known/openid-configuration

<!-- All pages and layouts render server side -->
<!-- Try to keep components as server side unless you need a hook ex. useEffect ect -->
<!-- Components should call data via zuztand store -->
<!-- Stores should call API routes -->
<!-- API routes should use a service class to request data from API -->
<!-- EC is used any time we are dealing with Ambulatory Measures -->
<!-- Filter comp: EC (Eligible Clinician) - Ambulatory Measures -->

<!-- Things to implement from Next 15:
instrumentation.js
after() api

next.config.ts
ESLint 9

cookies and headers are now async -->

<!-- When making an api request where you need the auth session:
- if call originates from client session is avail via await auth()
- if call originates from server you need to first include all cookies then call await auth() in the handler -->

Extra Credit

- Write a script that will run after a prisma db pull and populate given fields with @id. Currently since our DB does not explicitly set primary keys for some tables they are not introspected as such and we have to manually fix them.

API/Swaggers Used
[Medisolv Measures](https://medisolv-measures.azurewebsites.net/swagger/index.html)
[CitC API](https://concertqa.medisolvdev.com//swagger/index.html)
[Platform Adm API](https://platformadmapis.medisolvdev.com/swagger/index.html)
