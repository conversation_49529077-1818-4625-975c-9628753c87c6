# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# database
/prisma/db.sqlite
/prisma/db.sqlite-journal
db.sqlite

# next.js
/.next/
/out/
next-env.d.ts

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo

# idea files
.idea

certificates

output-directory
openapitools.json

# Ignore everything in prisma/generated
prisma/generated/*

# Un-ignore specific .node files
!prisma/generated/adm/libquery_engine-debian-openssl-3.0.x.so.node
!prisma/generated/hub/libquery_engine-debian-openssl-3.0.x.so.node
/.run/
*.orig
*.log
