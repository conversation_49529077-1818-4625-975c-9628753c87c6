{"name": "platform.web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "db:studio": "prisma studio", "db:pull:adm": "prisma db pull --schema=./prisma/admSchema.prisma", "db:pull:hub": "prisma db pull --schema=./prisma/hubSchema.prisma", "db:generate:adm": "prisma generate --schema=./prisma/admSchema.prisma", "db:generate:hub": "prisma generate --schema=./prisma/hubSchema.prisma", "db:studio:hub": "prisma studio --schema prisma/hubSchema.prisma", "db:studio:adm": "prisma studio --schema prisma/admSchema.prisma", "dev:https": "next dev --experimental-https", "dev": "next dev", "postinstall": "npm run db:generate:adm && npm run db:generate:hub", "lint": "next lint", "start": "next start", "prepare": "husky", "rebuild": "rm -rf node_modules package-lock.json && npm install --force", "dev:windows": "next dev", "npm:clean": " npm cache clean --force", "start_azure_debug": "NODE_OPTIONS='--inspect=0.0.0.0:$APPSVC_TUNNEL_PORT' ./node_modules/next/dist/bin/next start ", "build:standalone": "next build && node copy-assets.cjs", "start:standalone": "node .next/standalone/server.js", "test": "npm run test:all", "test:unit": "jest --config=jest.config.cjs --testPathIgnorePatterns=integration", "test:integration": "cross-env NODE_OPTIONS=--experimental-vm-modules NODE_NO_WARNINGS=1 jest --config=jest.config.integration.cjs --testPathPattern=integration", "test:all": "npm run test:unit && npm run test:integration"}, "dependencies": {"@auth/prisma-adapter": "^1.6.0", "@azure/data-tables": "^13.3.0", "@azure/identity": "^4.10.0", "@azure/monitor-query": "^1.3.2", "@azure/storage-blob": "^12.25.0", "@fontsource/source-sans-pro": "^5.1.0", "@microsoft/applicationinsights-clickanalytics-js": "^3.3.3", "@microsoft/applicationinsights-react-js": "^17.3.3", "@microsoft/applicationinsights-web": "^3.3.3", "@monaco-editor/react": "^4.7.0", "@prisma/client": "^5.22.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.2", "@sendgrid/mail": "^8.1.5", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.50.0", "@tanstack/react-table": "^8.20.5", "@trpc/client": "^11.0.0-rc.648", "@trpc/react-query": "^11.0.0-rc.648", "@trpc/server": "^11.0.0-rc.648", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.7", "@types/xlsx": "^0.0.35", "ag-charts-community": "^11.2.4", "ag-charts-react": "^11.1.1", "applicationinsights": "^2.9.6", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "geist": "^1.3.0", "jsonwebtoken": "^9.0.2", "ldrs": "^1.0.2", "lucide-react": "^0.439.0", "monaco-editor": "^0.52.2", "mssql": "^11.0.1", "next": "~15.1.3", "next-auth": "^5.0.0-beta.25", "plotly": "^1.0.6", "plotly.js": "^2.29.1", "posthog-js": "^1.215.1", "react": "^19.0.0", "react-day-picker": "^9.6.1", "react-dom": "^19.0.0", "react-icons": "^5.3.0", "react-is": "^19.0.0", "react-loader-spinner": "^6.1.6", "react-plotly.js": "^2.6.0", "react-virtualized": "^9.22.6", "redis": "^4.7.0", "rsuite": "^5.72.0", "server-only": "^0.0.1", "snowflake-sdk": "^2.0.3", "superjson": "^2.2.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tanstack-table-export-to-csv": "^1.0.5", "use-broadcast-ts": "^2.0.1", "xlsx": "^0.18.5", "zod": "^3.23.3", "zustand": "^5.0.1"}, "devDependencies": {"@babel/preset-react": "^7.26.3", "@prisma/nextjs-monorepo-workaround-plugin": "^5.22.0", "@simbathesailor/use-what-changed": "^2.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/eslint": "^8.56.10", "@types/jest": "^29.5.14", "@types/mssql": "^9.1.7", "@types/node": "^20.14.10", "@types/react-dom": "^19.1.2", "@types/react-plotly.js": "^2.6.3", "@types/react-virtualized": "^9.22.0", "@types/testing-library__jest-dom": "^5.14.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "eslint": "^8.57.0", "eslint-config-next": "15.0.3", "eslint-plugin-perfectionist": "^4.1.2", "husky": "^9.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "pm2": "^5.4.2", "postcss": "^8.4.39", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "prisma": "^5.22.0", "tailwindcss": "^3.4.3", "ts-jest": "^29.2.6", "typescript": "^5.8.2"}, "ct3aMetadata": {"initVersion": "7.37.0"}, "packageManager": "npm@10.8.1", "overrides": {"react": "^19.0.0", "react-dom": "^19.0.0", "jsdom": "^26.0.0", "ajv": "^8.17.1"}}