#!/bin/bash

# Set the download URL
URL="https://binaries.prisma.sh/all_commits/23fdc5965b1e05fc54e5f26ed3de66776b93de64/debian-openssl-3.0.x/libquery_engine.so.node.gz"

# Download the file
wget "$URL"

# Decompress the file
gzip -d libquery_engine.so.node.gz

# Rename the file
mv libquery_engine.so.node libquery_engine-debian-openssl-3.0.x.so.node

# Create directories if they don't exist
mkdir -p ./prisma/generated/adm ./prisma/generated/hub

# Copy to specified locations
cp libquery_engine-debian-openssl-3.0.x.so.node ./prisma/
cp libquery_engine-debian-openssl-3.0.x.so.node ./prisma/generated/adm/
cp libquery_engine-debian-openssl-3.0.x.so.node ./prisma/generated/hub/

rm libquery_engine-debian-openssl-3.0.x.so.node

echo "File copied successfully."