import { toast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'
import {
  defaultShouldDehydrateQuery,
  QueryCache,
  QueryClient,
} from '@tanstack/react-query'
import { TRPCError } from '@trpc/server'
import SuperJSON from 'superjson'

export const createQueryClient = () => {
  const qc = new QueryClient({
    // queryCache: new QueryCache({
    //   // onError: (error) => {
    //   //   console.log('some error', error.message)
    //   //   console.log('error', JSON.stringify(error))
    //   //   return {
    //   //     success: false,
    //   //     error: error.message,
    //   //   }
    //   //   //try clearing the cache
    //   //   clearCache(qc)
    //   //   if (typeof window !== 'undefined') {
    //   //     //ensure this is only called from te client
    //   //     toast({
    //   //       className: cn('bg-red-600 text-white border-red-600'),
    //   //       variant: 'destructive',
    //   //       title: 'Something went wrong',
    //   //       description: error.message,
    //   //     })
    //   //   }
    //   //   // TODO: if we never see this again as we are not handling unauth in middleware, remove this block and keep the toast to show all trpc errors via toast
    //   //   if (error.message === 'Unauthorized') {
    //   //     console.log("unauth error (middleware didn't catch)", error.message)
    //   //     throw new TRPCError({
    //   //       code: 'UNAUTHORIZED',
    //   //       message: error.message,
    //   //       cause: {
    //   //         statusCode: 401,
    //   //         message: error.message,
    //   //         error: error,
    //   //       },
    //   //     })
    //   //   } else {
    //   //     throw new TRPCError({
    //   //       code: 'BAD_REQUEST',
    //   //       message: error.message,
    //   //       cause: {
    //   //         statusCode: 400,
    //   //         message: error.message,
    //   //         stackTrace: error.stack,
    //   //         error: error,
    //   //       },
    //   //     })
    //   //   }
    //   // },
    // }),
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 3 * 1000,
      },
      dehydrate: {
        serializeData: SuperJSON.serialize,
        shouldDehydrateQuery: (query) =>
          defaultShouldDehydrateQuery(query) ||
          query.state.status === 'pending',
      },
      hydrate: {
        deserializeData: SuperJSON.deserialize,
      },
    },
  })
  return qc
}

function clearCache(qc: QueryClient) {
  //qc?.clear()
}
