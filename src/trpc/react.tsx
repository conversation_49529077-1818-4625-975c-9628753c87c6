'use client'

import { QueryClientProvider, type QueryClient } from '@tanstack/react-query'
import { httpLink, loggerLink, httpBatchStreamLink, splitLink } from '@trpc/client'
import { createTRPCReact } from '@trpc/react-query'
import { type inferRouterInputs, type inferRouterOutputs } from '@trpc/server'
import { useState } from 'react'
import SuperJSON from 'superjson'

import { type AppRouter } from '@/server/api/root'
import { createQueryClient } from './query-client'
import { type RequestCookie } from 'next/dist/compiled/@edge-runtime/cookies'

let clientQueryClientSingleton: QueryClient | undefined = undefined
const getQueryClient = () => {
  if (typeof window === 'undefined') {
    // Server: always make a new query client
    return createQueryClient()
  }
  // Browser: use singleton pattern to keep the same query client
  return (clientQueryClientSingleton ??= createQueryClient())
}

export const api = createTRPCReact<AppRouter>()

/**
 * Inference helper for inputs.
 *
 * @example type HelloInput = RouterInputs['example']['hello']
 */
export type RouterInputs = inferRouterInputs<AppRouter>

/**
 * Inference helper for outputs.
 *
 * @example type HelloOutput = RouterOutputs['example']['hello']
 */
export type RouterOutputs = inferRouterOutputs<AppRouter>

export function TRPCReactProvider(props: {
  children: React.ReactNode
  cookies?: RequestCookie[]
}) {
  const queryClient = getQueryClient()

  const trpcOptions = {
    transformer: SuperJSON,
    url: getBaseUrl() + '/api/trpc',
    headers: () => {
      const headers = new Headers()

      headers.set('x-trpc-source', 'nextjs-react')

      const cookies = props.cookies
        ?.map((cookie) => `${cookie.name}=${cookie.value}`)
        .join('; ')

      // Set the "Cookie" header if cookies exist
      if (cookies) {
        headers.set('Cookie', cookies)
      }

      return headers
    },
  }

  const [trpcClient] = useState(() =>
    process.env.NODE_ENV === 'development'
      ? api.createClient({
          links: [
            loggerLink({
              enabled: (op) => false,
            }),
            httpLink(trpcOptions),
          ],
        })
      : api.createClient({
          links: [
            loggerLink({
              enabled: (op) =>
                op.direction === 'down' && op.result instanceof Error,
            }),
            splitLink({
              condition(op) {
                return Boolean(op.context.skipBatch);
              },
              true: httpLink(trpcOptions),
              false: httpBatchStreamLink(trpcOptions),
            }),
          ],
        })
  )

  return (
    <QueryClientProvider client={queryClient}>
      <api.Provider client={trpcClient} queryClient={queryClient}>
        {props.children}
      </api.Provider>
    </QueryClientProvider>
  )
}

function getBaseUrl() {
  if (typeof window !== 'undefined') return window.location.origin
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`
  return `http://localhost:${process.env.PORT ?? 3000}`
}
