// eslint-disable-next-line @typescript-eslint/no-unused-vars
import NextAuth from 'next-auth'

export type CookiesSession = {
  role: CitcUserRoles[]
  given_name: string
  family_name: string
  Organization: string | string[]
  'role:Organization': string[]
}

/**
 * Extend Session
 *
 * auth.js adds values from claims that are not defined
 * in the default interface. We can add them here
 */
declare module 'next-auth' {
  interface Session {
    accessToken?: string
    idToken?: string
    uid?: string
    error?: string
    status?: string
    expires?: string
  }
}
