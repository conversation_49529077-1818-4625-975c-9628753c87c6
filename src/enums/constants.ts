export enum Constants {
  SessionKey = 'hubUserSession',
  DevConnectionStringKey = 'MedisolvHubDevConnection',
  DevOrgIdKey = 'DeveloperSettings:OrganizationId',
  DevOrgNameKey = 'DeveloperSettings:OrganizationName',
  OrganizationClaimKey = 'Organization',
  OrganizationRoleClaimKey = 'role:Organization',
  FirstNameKey = 'given_name',
  LastNameKey = 'family_name',
  NameKey = 'name',
  EmailKey = 'email',
  WidgetsCacheKey = 'medisolvHub__All_Widgets',
  ClientId = 'client_id',
  UserId = 'uid',
  OrganizationDetailsPrefix = 'CitcOrgDetails-',
  SessionSavedFilter = '__session__filters',
  DefaultOrganization = 'Default Organization',
  DefaultPartner = 'Default Display',
  PatientDetailAccessRole = 'Patient Detail Access',
  AccessToken = 'access_token',
  RefreshToken = 'refresh_token',
  IdToken = 'id_token',
  CitcSettingsAuthority = 'citcSettings:identityServerUrl',
  CitcSettingsClientId = 'citcSettings:clientId',
  CitcSettingsClientSecret = 'citcSettings:clientSecret',
  WorkspaceId = 'WorkspaceId',
  ReportId = 'PowerBIReport',
  DashboardDataSource = 'platform:DashboardDataSource',
  NoticeContent = 'platform:NoticeContent',
  ShowNotice = 'platform:ShowNotice',
  IsGlobalPartner = 'global:Partner',
  ScorecardManager = 'Scorecard Manager',
  PatientExplorer = 'Patient Explorer',
  Reporting = 'Reporting',
  ProviderDetailAccessRole = 'ProviderDetailsAccess',
  PrimaryMeasureType = 'PrimaryMeasureType',
  EcOverallGroupId = 'platform:EcOverallGroupId',
  EntityId = 'EntityId',
  DisableExports = 'platform:DisableExports',
  ExportMode = 'platform:ExportMode',
  UseSpecialEntityStructure = 'platform:UseSpecialEntityStructure',
  PerformanceGoals = 'PerformanceGoals',
  //CurrentSelectedData = 'CurrentSelectedData',
  Reports = 'Reports',
  SendGridApiKey = 'SendGridApiKey',
  IncludeMedisolvUser = 'platform:IncludeMedisolvUser',
}
