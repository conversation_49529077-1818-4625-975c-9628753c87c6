/**
 * The PrimaryMeasureTypeConstants define four different types of measures:
 * Ambulatory Measures: These measures are typically used to assess the quality of care provided in outpatient or ambulatory settings, such as physician offices, clinics, and same-day surgery centers. They focus on aspects like preventive care, chronic disease management, and patient safety in these settings.
 * Hospital Measures: These measures are designed to evaluate the quality of care delivered in hospitals. They cover a wide range of areas, including inpatient care, surgical procedures, medication safety, and patient experience during hospital stays.
 * Registry Measures: These measures are used to track and assess the performance of healthcare providers or organizations based on data collected in registries. Registries are organized systems that collect and maintain clinical data on specific patient populations or conditions. Registry measures can be used to monitor trends, identify areas for improvement, and benchmark performance against other providers or organizations.
 * Abstracted Measures: These measures involve manually extracting data from medical records or other sources to assess performance on specific quality indicators. Abstraction can be a time-consuming and resource-intensive process, but it allows for the measurement of quality metrics that may not be readily available in electronic formats.
 *
 * Differences:
 * The main differences between these measure types lie in the setting of care they apply to (ambulatory vs. hospital), the data sources used (registries vs. abstracted data), and the specific aspects of quality they focus on. For example, ambulatory measures might focus on preventive care services like vaccinations, while hospital measures might focus on readmission rates or surgical complications. Registry measures might track the incidence of specific diseases, while abstracted measures might assess the adherence to clinical guidelines for a particular condition.
 * In essence, each measure type serves a distinct purpose in evaluating and improving the quality of healthcare delivery across different settings and aspects of care.
 */
export enum PrimaryMeasureTypeConstants {
  None = '',
  AmbulatoryMeasures = 'Ambulatory Measures',
  HospitalMeasures = 'Hospital Measures',
  RegistryMeasures = 'Registry Measures',
  AbstractedMeasures = 'Abstracted Measures',
}
