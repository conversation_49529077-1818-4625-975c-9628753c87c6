import { PrimaryMeasureTypeConstants } from '../enums/primaryMeasureTypeConstants'
import { env } from '@/env'
import { Measure } from '../types/measure'
import { OrganizationPaidMeasure } from '../types/organizationPaidMeasure'
import { SimplifiedParameter } from '../types/simplifiedParameter'
import dayjs, { Dayjs } from 'dayjs'
import { activeMeasuresQuery } from './activeMeasuresQuery'
import { getMeasureTitle } from '../lib/getMeasureTitle'
import utc from 'dayjs/plugin/utc'
import { mapleMeasureQuery } from '@/services/maple/mapleMeasuresQuery'
import { config } from '@/config'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import MeasureResultsService from '@/services/measureResults'
import { activeMeasureFilter } from '@/lib/activeMeasureFilter'
import appInsights from '@/lib/applicationInsights'
import { tryCache } from '@/lib/redis'
import MapleMeasuresService from '@/services/mapleMeasures'

dayjs.extend(utc)
dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

export const getOrganizationPaidMeasuresQuery = async (
  measureResultsService: MeasureResultsService,
  startDate: Dayjs,
  endDate: Dayjs,
  primaryMeasureType: string,
  accessToken: string,
  parameters: SimplifiedParameter[],
  organizationId: string
) => {
  const cacheKey = `getOrganizationPaidMeasuresQuery.${organizationId}-${primaryMeasureType}-${startDate.format('YYYYMMDD')}-${endDate.format('YYYYMMDD')}`
  return await tryCache(cacheKey, async () => {
    const measures: OrganizationPaidMeasure[] = []
    let selectedMapleMeasures: Measure[] = []

    appInsights.trackEvent({
      name: 'executing getOrganizationPaidMeasuresQuery',
      properties: {
        startDate,
        endDate,
        primaryMeasureType,
        accessToken,
        parameters,
        organizationId,
      },
    })

    const mapleMeasuresService = new MapleMeasuresService(
      accessToken,
      measureResultsService
    )
    const mapleMeasures = await mapleMeasureQuery(
      mapleMeasuresService,
      organizationId
    )

    appInsights.trackEvent({
      name: 'result of getOrganizationPaidMeasuresQuery.mapleMeasures',
      properties: {
        mapleMeasures,
      },
    })

    const appName = primaryMeasureType.replace(' ', ' e')

    selectedMapleMeasures = mapleMeasures.filter(
      (x) => x.applicationName == appName
    )

    if (primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures) {
      const encorAParameterName = env.ENCOR_A_PARAMETER_NAME
      let encorAParamValue

      if (encorAParameterName) {
        encorAParamValue = parameters?.find(
          (x) => x.key === encorAParameterName
        )?.value
      } else {
        encorAParamValue = parameters?.find(
          (x) => x.key == 'platform:EnableEncorAMeasures'
        )?.value
      }

      const enableEncorAMeasures = !encorAParamValue
        ? false
        : encorAParamValue.toLowerCase() === 'true'

      if (enableEncorAMeasures) {
        const abstractedMeasures = mapleMeasures.filter(
          (x) => x.applicationName == 'Abstracted Measures'
        )

        selectedMapleMeasures.push(...abstractedMeasures)
      }
    } else {
      const registryMeasure = mapleMeasures.filter(
        (x) =>
          x.applicationName ==
          (PrimaryMeasureTypeConstants.RegistryMeasures as string)
      )

      selectedMapleMeasures.push(...registryMeasure)
    }

    selectedMapleMeasures = Object.values(
      selectedMapleMeasures.reduce(
        (acc, curr) => {
          if (!acc[curr.measureIdentifier]) {
            acc[curr.measureIdentifier] = curr
          }
          return acc
        },
        {} as Record<string, Measure>
      )
    )

    appInsights.trackEvent({
      name: 'result of getOrganizationPaidMeasuresQuery.selectedMapleMeasures',
      properties: {
        selectedMapleMeasures,
      },
    })

    const activeMeasures = await activeMeasuresQuery(
      measureResultsService,
      mapleMeasuresService,
      organizationId
    )

    appInsights.trackEvent({
      name: 'result of getOrganizationPaidMeasuresQuery.activeMeasures',
      properties: {
        activeMeasures,
      },
    })

    let validActiveMeasures = activeMeasures.filter((x) =>
      activeMeasureFilter(x, startDate, endDate)
    )

    appInsights.trackEvent({
      name: 'result of getOrganizationPaidMeasuresQuery.validActiveMeasures',
      properties: {
        validActiveMeasures,
      },
    })

    const excludedMeasures = config.customMeasures.excludedMeasuresList

    validActiveMeasures = validActiveMeasures.filter(
      (x) =>
        !excludedMeasures.some(
          (excludedMeasure) =>
            excludedMeasure.toLowerCase() === x.measureIdentifier.toLowerCase()
        )
    )

    appInsights.trackEvent({
      name: 'result of getOrganizationPaidMeasuresQuery.validActiveMeasures',
      properties: {
        validActiveMeasures,
      },
    })

    const result: OrganizationPaidMeasure[] = selectedMapleMeasures
      .filter((mapleMeasure) => {
        const measure = validActiveMeasures.find(
          (m) =>
            m.measureIdentifier.toLowerCase() ===
            mapleMeasure.measureIdentifier.toLowerCase()
        )

        return (
          !!measure &&
          !(
            mapleMeasure.versionNumber.toLowerCase().endsWith('s') &&
            mapleMeasure.subId === 'a'
          )
        )
      })
      .map((x) => ({
        ...x,
        measureTitle: getMeasureTitle(x),
        measureIdentifier:
          validActiveMeasures.find(
            (value) =>
              value.measureIdentifier.toLowerCase() ==
              x.measureIdentifier.toLowerCase()
          )?.measureIdentifier ?? '',
        applicationName: x.applicationName,
      }))

    measures.push(...result)

    appInsights.trackEvent({
      name: 'result of getOrganizationPaidMeasuresQuery.measures',
      properties: {
        measures,
      },
    })

    return measures
  })
}
