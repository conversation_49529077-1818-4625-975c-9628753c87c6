import { tryCache } from '@/lib/redis'
import MapleMeasuresService from '../services/mapleMeasures'
import { ActiveMeasure } from '@/types/activeMeasure'
import dayjs from 'dayjs'
import MeasureResultsService from '@/services/measureResults'
import appInsights from '@/lib/applicationInsights'

export const activeMeasuresQuery = async (
  measureResultsService: MeasureResultsService,
  mapleMeasuresService: MapleMeasuresService,
  organizationId: string
): Promise<ActiveMeasure[]> => {
  appInsights.trackEvent({
    name: 'executing activeMeasuresQuery',
    properties: {
      organizationId,
    },
  })

  const cacheKey = `active-measures-${organizationId || ''}`
  return await tryCache(cacheKey, async () => {
    const activeMeasures: ActiveMeasure[] = []

    const mapleMeasures =
        await mapleMeasuresService.getAllMeasures(organizationId)

    appInsights.trackEvent({
      name: 'result of activeMeasuresQuery.mapleMeasures',
      properties: {
        mapleMeasures,
      },
    })

    const mecaMeasures = await measureResultsService.findAllMeasures()

    appInsights.trackEvent({
      name: 'result of activeMeasuresQuery.mecaMeasures',
      properties: {
        mecaMeasures,
      },
    })

    // Get detailed active measures
    const measureGroupList =  await measureResultsService.findAllDetailedActiveMeasures()

    appInsights.trackEvent({
      name: 'result of activeMeasuresQuery.measureGroupList',
      properties: {
        measureGroupList,
      },
    })

    // Group the results by MeasureGUID, ProcessingStartDate, and SourceContainerIdentifier
    const groupedMeasures = measureGroupList.reduce(
        (acc, measure) => {
          const groupKey = `${measure.MeasureGUID}_${`${dayjs
              .utc(measure.ProcessingStartDate)
              .format('YYYYMMDD')}`}_${measure.SourceContainerIdentifier}`
          if (!acc.has(groupKey)) {
            acc.set(groupKey, {
              MeasureGUID: measure.MeasureGUID,
              ProcessingStartDate: measure.ProcessingStartDate!,
              SourceContainerIdentifier: measure.SourceContainerIdentifier,
            })
          }

          return acc
        },
        new Map<
            string,
            {
              MeasureGUID: string
              ProcessingStartDate: Date | string
              SourceContainerIdentifier: string | null | undefined
            }
        >()
    )

    appInsights.trackEvent({
      name: 'result of activeMeasuresQuery.groupedMeasures',
      properties: {
        groupedMeasures: Object.fromEntries(groupedMeasures),
      },
    })

    // Loop through grouped results and find matching details in mapleMeasures
    for (const [key, value] of groupedMeasures) {
      const item = groupedMeasures.get(key)
      const mapleMeasuresDetails = mapleMeasures.find(
          (m) =>
              m.measureIdentifier.toLowerCase() ===
              item?.MeasureGUID.toLocaleLowerCase()
      )

      if (mapleMeasuresDetails) {
        activeMeasures.push({
          measureIdentifier: item?.MeasureGUID!,
          measureName: mapleMeasuresDetails.measureName,
          measureSubId: mapleMeasuresDetails.subId,
          strata: mapleMeasuresDetails.strata,
          denominatorQualifyingType: mecaMeasures.some(
              (m) => m.MedisolvMeasureId === item?.MeasureGUID
          )
              ? mecaMeasures.find((m) => m.MedisolvMeasureId === item?.MeasureGUID)
                  ?.DenominatorQualifyingType!
              : null,
          processingDate: item?.ProcessingStartDate!,
          sourceContainerIdentifier: item?.SourceContainerIdentifier!,
        })
      }
    }

    appInsights.trackEvent({
      name: 'result of activeMeasuresQuery.activeMeasures',
      properties: {
        activeMeasures,
      },
    })

    return activeMeasures
  })
}
