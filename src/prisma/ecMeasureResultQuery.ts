import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { ScorecardView } from '@/enums/scorecardView'
import { SelectionType } from '@/enums/selectionType'
import { OrganizationPaidMeasure } from '@/types/organizationPaidMeasure'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import dayjs, { Dayjs } from 'dayjs'
import { getOrganizationPaidMeasuresQuery } from './getOrganizationPaidMeasuresQuery'
import { DateRange } from '@/lib/dateRange'
import { ECMeasureResultOptions } from '@/types/eCMeasureResultOptions'
import MeasureResultsService from '@/services/measureResults'
import { DetailsList } from '@/types/detailsList'
import TrendCSSHelper from '@/lib/trendCSSHelper'
import { CalculatedMeasure } from '@/types/calculatedMeasure'
import { getSubmissionGroupsByOrganizationQuery } from '@/services/submissionGroups/getSubmissionGroupsByOrganizationQuery'
import { ECMeasureResultSummary } from '@/types/eCMeasureResultSummary'
import { ManageUserRole } from '@/types/manageUserRole'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { manageUserRolesQuery } from '@/services/adm/users/manageUserRolesQuery'
import { OrganizationRole } from '@/types/organizationRole'
import { hasLimitedAccessForOrganization } from '@/lib/hasLimitedAccessForOrganization'
import { getPartnersByUserCommand } from '@/services/partners/getPartnersByUserCommand'
import { Organization } from '@/types/organization'
import { Partner } from '@/types/partner'
import { INotation } from '@/enums/iNotation'

export const ecMeasureResultQuery = async (
  measureResultsService: MeasureResultsService,
  scorecardView: ScorecardView,
  organizationId: string,
  startDate: Dayjs,
  endDate: Dayjs,
  selectionType: SelectionType,
  primaryMeasureType: PrimaryMeasureTypeConstants,
  accessToken: string,
  parameters: SimplifiedParameter[],
  userId: string,
  orgRoles: OrganizationRole[],
  organizations: string,
  subOrgs: string[],
  enabledLevel1EntityForOrg: boolean,
  partners: Partner[]
) => {
  const modelData: Partial<CalculatedMeasure>[] = []

  try {
    const measureList: OrganizationPaidMeasure[] = []

    const mapleMeasures = await getOrganizationPaidMeasuresQuery(
      measureResultsService,
      startDate,
      endDate,
      primaryMeasureType,
      accessToken,
      parameters,
      organizationId
    )

    measureList.push(
      ...mapleMeasures.sort((a, b) =>
        a.measureTitle.localeCompare(b.measureTitle)
      )
    )

    const dateRangeSpan = DateRange.getColumnIntervalsByCategory(
      scorecardView,
      startDate,
      endDate
    )

    const admMeasures =
      await measureResultsService.getAllMeasures(organizationId)

    let partnerOfOrganization: Partner | undefined

    if (enabledLevel1EntityForOrg) {
      partnerOfOrganization = partners?.find((x) =>
        x.organizations?.some((org) => org.organizationId === organizationId)
      )
    }

    const options: ECMeasureResultOptions = {
      organizationId: enabledLevel1EntityForOrg
        ? partnerOfOrganization?.id!
        : organizationId,
      periodType: scorecardView,
      submissionGroupId: ['*'],
      startDate,
      endDate,
      isPartner:
        selectionType === SelectionType.Partner || enabledLevel1EntityForOrg
          ? true
          : false,
      isSubmissionGroupLevel: true,
      sourceContainerIdentifier: '',
      measureIdentifier: '',
    }

    const aggregateResultsResponse =
      await measureResultsService.getECMeasureResults(options)

    const measureRates =
      aggregateResultsResponse?.ecMeasureResultSummaryList ?? []

    const groupedMeasures = measureList.reduce(
      (acc, measure) => {
        const key = measure.measureIdentifier
        if (!acc[key]) acc[key] = []
        acc[key].push(measure)
        return acc
      },
      {} as Record<string, OrganizationPaidMeasure[]>
    )

    let type3MeasureResults: ECMeasureResultSummary[] | undefined

    if (
      scorecardView == ScorecardView.Yearly &&
      admMeasures.some(
        (x) => x.SmallestInterval == 'Y' && !x.DenominatorQualifyingType
      )
    ) {
      type3MeasureResults = await getIAPIMeasureResultsForType3(
        measureResultsService,
        admMeasures
          .filter(
            (x) => x.SmallestInterval == 'Y' && !x.DenominatorQualifyingType
          )
          .map((m) => m.MedisolvMeasureId),
        organizationId,
        startDate,
        endDate,
        selectionType,
        userId,
        accessToken,
        hasLimitedAccessForOrganization(orgRoles, organizationId),
        organizations,
        subOrgs
      )
    }

    for (const [key, group] of Object.entries(groupedMeasures)) {
      if (!group || group.length === 0) {
        continue
      }

      const admMeasureDetails = admMeasures.find(
        (x) => x.MedisolvMeasureId.toLowerCase() === key.toLowerCase()
      )
      const isIAPIMeasure =
        admMeasureDetails?.SmallestInterval == 'Y' &&
        !admMeasureDetails?.DenominatorQualifyingType

      try {
        // const myObj: { [key: string]: any } = {}
        const myObj: Partial<CalculatedMeasure> = {}
        const currentMeasureRates = measureRates.filter(
          (x) => x.measureGUID.toLowerCase() === key.toLowerCase()
        )
        let isEmptyIndicator = true

        myObj['measureIdentifier'] = admMeasureDetails?.MedisolvMeasureId
        myObj['measureTitle'] = group[0]?.measureTitle!

        const detailList: DetailsList[] = []

        for (const item of dateRangeSpan) {
          const span = item.replace('-', '_') as
            | `Q${number}_${number}`
            | `CY_${number}`
            | `${Capitalize<string>}_${number}`
          const period = DateRange.getDateFromDateRangeSpan(scorecardView, span)

          if (!admMeasureDetails || admMeasureDetails.NullRate) {
            let displayText = '-'
            if (
              scorecardView == ScorecardView.Yearly &&
              isIAPIMeasure &&
              type3MeasureResults?.some(
                (x) =>
                  x.measureGUID == key &&
                  dayjs(x.startDate).isSame(period.startDate, 'day') &&
                  dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
              )
            )
              displayText = 'N/A'

            myObj[span] = displayText
            detailList.push({ performance: null, denominator: null })
            continue
          }

          const rate = currentMeasureRates.find(
            (x) =>
              dayjs(x.startDate).isSame(period.startDate, 'day') &&
              dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
          )

          if (rate) {
            detailList.push({
              performance: rate.performance,
              denominator: admMeasureDetails?.NullDenominator
                ? null
                : rate.denominator,
            })

            myObj[span] =
              rate.performance === -1 || rate.performance === null
                ? '-'
                : rate.performance === 100
                  ? rate.performance.toString()
                  : rate.performance?.toFixed(2)

            if (rate.performance != null) isEmptyIndicator = false
          } else {
            myObj[span] = '-'
            detailList.push({ performance: null, denominator: null })
          }
        }

        const trendName =
          INotation[
            group[0]?.iNotationName! as unknown as keyof typeof INotation
          ]

        const trendCSS = TrendCSSHelper.getTrendSlopeCss(
          detailList,
          trendName,
          scorecardView
        )
        myObj['trendCss'] = trendCSS

        myObj['measureDescription'] = group[0]?.measureDescription.trim()
        myObj['friendlyName'] = group[0]?.measureFriendlyName
        myObj['subDomain'] = group[0]?.subDomainName
        myObj['type'] = group[0]?.typeName
        myObj['domain'] = group[0]?.domainName
        myObj['cmsId'] = group[0]?.cMSId ?? '-'
        myObj['subType'] = group[0]?.subTypeName
        myObj['application'] = group[0]?.applicationName
        myObj['programName'] = group[0]?.programName
          ? group[0]?.programName
          : '-'
        myObj['smallestInterval'] = admMeasureDetails?.SmallestInterval ?? 'M'

        myObj['isEmptyIndicator'] = isEmptyIndicator
        myObj['isIAPIMeasure'] = isIAPIMeasure

        modelData.push(myObj)
      } catch (error) {
        // TODO: Log it
        continue
      }
    }
  } catch (error) {
    throw error
  }

  return modelData
}

const getIAPIMeasureResultsForType3 = async (
  measureResultsService: MeasureResultsService,
  iAPIMeasureIds: string[],
  organizationId: string,
  startDate: Dayjs,
  endDate: Dayjs,
  selectionType: SelectionType,
  userId: string,
  accessToken: string,
  hasLimitedAccess: boolean,
  organizations: string,
  subOrgs: string[]
) => {
  let userRoles: ManageUserRole[] = []
  let canAccessibleRole: string[] = []
  if (hasLimitedAccess) {
    userRoles = await manageUserRolesQuery(measureResultsService, {
      organizationId: organizationId,
      userId: userId,
      isPartner: selectionType == SelectionType.Partner,
      organizationType: EntityOrganizationTypeConstants.SubmissionGroupLevel,
    })

    canAccessibleRole = userRoles
      .filter((x) => x.canAccess)
      .map((x) => x.entitiesId)
      .map((x) => x.toString()) // NOTE: I don't think this is being used as entity code seems to come from the entity itself https://gitkraken.dev/link/dnNjb2RlOi8vZWFtb2Rpby5naXRsZW5zL2xpbmsvci83N2M2N2UwZDliMWRkMmMyODkyYWRhMzY0ZTg4ZDlhMTY2ZDU4MzJjL2Yvc3JjL3NlcnZpY2VzL2FkbS91c2Vycy9tYW5hZ2VVc2VyUm9sZXNRdWVyeS50cz91cmw9bWVkaXNvbHYlNDB2cy1zc2gudmlzdWFsc3R1ZGlvLmNvbSUzQXYzJTJGbWVkaXNvbHYlMkZwbGF0Zm9ybS1uZXh0Z2VuJTJGcGxhdGZvcm0tbmV4dGdlbiZsaW5lcz04NQ%3D%3D?origin=gitlens
      .filter((x): x is string => x != null && x !== undefined)
  }

  let submissionGroups = await getSubmissionGroupsByOrganizationQuery(
    accessToken,
    measureResultsService,
    userId,
    {
      startDate: startDate,
      endDate: endDate,
      organizationId: organizationId,
      isPartner: selectionType == SelectionType.Partner,
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
    }
  )

  if (selectionType == SelectionType.Organization) {
    const options: ECMeasureResultOptions = {
      organizationId,
      periodType: 'Y',
      submissionGroupId: submissionGroups.map((x) => x.submissionGroupId),
      startDate,
      endDate,
      isPartner: false,
      isSubmissionGroupLevel: true,
      measureIdentifier: '',
    }

    const aggregateResultsResponse =
      await measureResultsService.getECMeasureResults(options)

    const measureRates =
      aggregateResultsResponse?.ecMeasureResultSummaryList ?? []
    return measureRates.filter((x) => iAPIMeasureIds.includes(x.measureGUID))
  } else {
    const parnerList = await getPartnersByUserCommand(userId, accessToken)
    const partner = parnerList.find((x) => x.id == organizationId)

    let partnerOrganizations = organizations
      ? partner?.organizations?.filter((x: Organization) =>
          organizations?.split(',').includes(x.organizationId!)
        )!
      : partner?.organizations!

    const orgnizationIds = partnerOrganizations
      .map((c) => c.organizationId)
      .filter((x) => x != null && x != undefined)

    orgnizationIds.push('ALLORGANIZATION')

    const ecoptions: ECMeasureResultOptions = {
      organizationId: organizationId,
      periodType: 'Y',
      submissionGroupId: orgnizationIds,
      startDate: startDate,
      endDate: endDate,
      isPartner: selectionType == SelectionType.Partner,
      isSubmissionGroupLevel: true,
      measureIdentifier: '',
    }

    const response = await measureResultsService.getECMeasureResults(ecoptions)
    const measureResultsForAllOrgs = response?.ecMeasureResultSummaryList ?? []

    let subOrgIds: string[] = []
    subOrgIds = subOrgs
      ? subOrgs
          .map((x) => x.split('~')[0])
          .filter((x) => x !== undefined && x !== null)
      : []

    if (subOrgIds.length == 0) {
      subOrgIds = submissionGroups.map((x) => x.submissionGroupId)
    } else {
      submissionGroups = submissionGroups.filter((x) =>
        subOrgIds.includes(x.submissionGroupId)
      )
      subOrgIds = submissionGroups.map((x) => x.submissionGroupId)
    }

    if (hasLimitedAccess) {
      subOrgIds = subOrgIds.filter((x) => canAccessibleRole.includes(x))
      submissionGroups = submissionGroups.filter((x) =>
        canAccessibleRole.includes(x.submissionGroupId)
      )
    }

    const options: ECMeasureResultOptions = {
      organizationId: organizationId,
      periodType: 'Y',
      submissionGroupId: subOrgIds,
      startDate: startDate,
      endDate: endDate,
      isPartner: selectionType == SelectionType.Partner,
      isSubmissionGroupLevel: true,
      measureIdentifier: '',
    }

    const aggregateResultsResponse =
      await measureResultsService.getECMeasureResults(options)
    let measureResultsforSuborgs =
      aggregateResultsResponse?.ecMeasureResultSummaryList ?? []
    const measureResults = measureResultsforSuborgs.concat(
      measureResultsForAllOrgs
    )
    return measureResults.filter((x) => iAPIMeasureIds.includes(x.measureGUID))
  }
}
