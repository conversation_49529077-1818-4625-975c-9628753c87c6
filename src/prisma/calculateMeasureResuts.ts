import type { ScorecardView } from '@/enums/scorecardView'
import type { Dayjs } from 'dayjs'
import { SelectionType } from '@/enums/selectionType'
import type { CalculatedMeasure } from '@/types/calculatedMeasure'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { ecMeasureResultQuery } from './ecMeasureResultQuery'
import { measuresPageContentQuery } from './measuresPageContentQuery'
import { OrganizationRole } from '@/types/organizationRole'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { Partner } from '@/types/partner'
import MeasureResultsService from '@/services/measureResults'
import CitCOrganizationService from '@/services/citc/citCOrganizations'

export const calculateMeasureResuts = async (
  measureResultsService: MeasureResultsService,
  citcOrganizationService: CitCOrganizationService,
  aggregationType: ScorecardView,
  startDate: Dayjs,
  endDate: Dayjs,
  primaryMeasureType: PrimaryMeasureTypeConstants,
  organizationId: string,
  selectionType: SelectionType,
  accessToken: string,
  parameters: SimplifiedParameter[],
  userId: string,
  orgRoles: OrganizationRole[],
  organizations: string,
  subOrgs: string[],
  enabledLevel1EntityForOrg: boolean,
  partners: Partner[]
) => {
  let measureResult: CalculatedMeasure[]

  if (
    primaryMeasureType == PrimaryMeasureTypeConstants.AmbulatoryMeasures ||
    primaryMeasureType == PrimaryMeasureTypeConstants.RegistryMeasures
  ) {
    measureResult = await ecMeasureResultQuery(
      measureResultsService,
      aggregationType,
      organizationId,
      startDate,
      endDate,
      selectionType,
      primaryMeasureType,
      accessToken,
      parameters,
      userId,
      orgRoles,
      organizations,
      subOrgs,
      enabledLevel1EntityForOrg,
      partners
    )
  } else {
    measureResult = await measuresPageContentQuery(
      measureResultsService,
      citcOrganizationService,
      aggregationType,
      organizationId,
      startDate,
      endDate,
      selectionType,
      primaryMeasureType,
      accessToken,
      parameters,
      enabledLevel1EntityForOrg,
      partners
    )
  }

  return measureResult
}
