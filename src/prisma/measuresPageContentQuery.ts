import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { ScorecardView } from '@/enums/scorecardView'
import { SelectionType } from '@/enums/selectionType'
import { OrganizationPaidMeasure } from '@/types/organizationPaidMeasure'
import dayjs, { Dayjs } from 'dayjs'
import { getOrganizationPaidMeasuresQuery } from './getOrganizationPaidMeasuresQuery'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { DateRange } from '@/lib/dateRange'
import { MeasureResultOptions } from '@/types/measureResultOptions'
import MeasureResultsService from '@/services/measureResults'
import { DetailsList } from '@/types/detailsList'
import TrendCSSHelper from '@/lib/trendCSSHelper'
import { CalculatedMeasure } from '@/types/calculatedMeasure'
import { config } from '@/config'
import { Partner } from '@/types/partner'
import { INotation } from '@/enums/iNotation'
import CitCOrganizationService from '@/services/citc/citCOrganizations'

export const measuresPageContentQuery = async (
  measureResultsService: MeasureResultsService,
  citcOrganizationService: CitCOrganizationService,
  scorecardView: ScorecardView,
  organizationId: string,
  startDate: Dayjs,
  endDate: Dayjs,
  selectionType: SelectionType,
  primaryMeasureType: PrimaryMeasureTypeConstants,
  accessToken: string,
  parameters: SimplifiedParameter[],
  enabledLevel1EntityForOrg: boolean,
  partners: Partner[]
) => {
  const modelData: Partial<CalculatedMeasure>[] = []
  try {
    const measureList: OrganizationPaidMeasure[] = []

    const mapleMeasures = await getOrganizationPaidMeasuresQuery(
      measureResultsService,
      startDate,
      endDate,
      primaryMeasureType,
      accessToken,
      parameters,
      organizationId
    )

    measureList.push(
      ...mapleMeasures.sort((a, b) =>
        a.measureTitle.localeCompare(b.measureTitle)
      )
    )

    const dateRangeSpan = DateRange.getColumnIntervalsByCategory(
      scorecardView,
      startDate,
      endDate
    )

    const admMeasures =
      await measureResultsService.getAllMeasures(organizationId)

    let partnerOfOrganization: Partner | undefined

    if (enabledLevel1EntityForOrg) {
      partnerOfOrganization = partners?.find((x) =>
        x.organizations?.some((org) => org.organizationId === organizationId)
      )
    }

    let isPartner: boolean | undefined
    let isCombinedGroup: boolean | undefined
    if (selectionType === SelectionType.Partner || enabledLevel1EntityForOrg) {
      isPartner = true
      isCombinedGroup = false
    } else {
      isPartner = false
      var subOrgs =
        await citcOrganizationService.getSubOrganizationsByOrganizationId(
          organizationId
        )
      isCombinedGroup = subOrgs?.length !== 1
    }

    const options: MeasureResultOptions = {
      organizationId: enabledLevel1EntityForOrg
        ? partnerOfOrganization?.id!
        : organizationId,
      periodType: scorecardView,
      subOrganizationId: ['*'],
      startDate,
      endDate,
      isPartner,
      isCombinedGroup,
      isFacilityLevel: false,
      measureIdentifier: '',
    }

    const aggregateResultsResponse =
      await measureResultsService.getMeasureResults(options)
    const measureRates =
      aggregateResultsResponse?.measureResultSummaryList ?? []

    const groupedMeasures = measureList.reduce(
      (acc, measure) => {
        const key = measure.measureIdentifier.toLowerCase()
        if (!acc[key]) acc[key] = []
        acc[key].push(measure)
        return acc
      },
      {} as { [key: string]: OrganizationPaidMeasure[] }
    )

    for (const [key, group] of Object.entries(groupedMeasures)) {
      if (!group || group.length === 0) {
        continue
      }

      try {
        const admMeasureDetails = admMeasures.find(
          (x) => x.MedisolvMeasureId.toLowerCase() === key
        )

        const isRatioMeasure = config.customMeasures.ratioMeasures.some(
          (x) => x.toLowerCase() === key
        )
        const myObj: Partial<CalculatedMeasure> = {}

        myObj['measureIdentifier'] = admMeasureDetails?.MedisolvMeasureId
        myObj['measureTitle'] = group[0]?.measureTitle!

        let currentMeasureRates = measureRates?.filter(
          (x) => x.measureGUID.toLowerCase() === key
        )
        currentMeasureRates = currentMeasureRates?.sort((a, b) =>
          dayjs(b.startDate).diff(dayjs(a.startDate))
        )

        let isEmptyIndicator = true
        const detailList: DetailsList[] = []

        for (const item of dateRangeSpan) {
          const span = item.replace('-', '_') as
            | `Q${number}_${number}`
            | `CY_${number}`
            | `${Capitalize<string>}_${number}`
          const period = DateRange.getDateFromDateRangeSpan(scorecardView, span)

          if (!admMeasureDetails || admMeasureDetails.NullRate) {
            myObj[span] = '-'

            detailList.push({ performance: null, denominator: null })
            continue
          }

          const rate = currentMeasureRates?.find(
            (x) =>
              dayjs(x.startDate).isSame(period.startDate, 'day') &&
              dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
          )

          if (rate) {
            detailList.push({
              performance: rate.performance,
              denominator: admMeasureDetails.NullDenominator
                ? null
                : rate.denominator,
            })

            if (isRatioMeasure) {
              myObj[span] =
                rate.performance === -1 || rate.performance === null
                  ? '-'
                  : rate.performance === 100
                    ? `${rate.numeratorValue}/${rate.denominatorValue}</br>${rate.performance}`
                    : `${rate.numeratorValue}/${rate.denominatorValue}</br>${rate.performance?.toFixed(2)}`
            } else {
              myObj[span] =
                rate.performance === -1 || rate.performance === null
                  ? '-'
                  : rate.performance === 100
                    ? rate.performance.toString()
                    : rate.performance?.toFixed(2)
            }

            if (rate.performance !== null) {
              isEmptyIndicator = false
            }
          } else {
            myObj[span] = '-'
            detailList.push({ performance: null, denominator: null })
          }
        }

        const trendName =
          INotation[
            group[0]?.iNotationName! as unknown as keyof typeof INotation
          ]

        const trendCSS = TrendCSSHelper.getTrendSlopeCss(
          detailList,
          trendName,
          scorecardView
        )
        myObj['trendCss'] = trendCSS

        myObj['measureDescription'] = group[0]?.measureDescription.trim()!
        myObj['friendlyName'] = group[0]?.measureFriendlyName!
        myObj['subDomain'] = group[0]?.subDomainName!
        myObj['type'] = group[0]?.typeName!
        myObj['domain'] = group[0]?.domainName!
        myObj['cmsId'] = group[0]?.cMSId ?? '-'
        myObj['subType'] = group[0]?.subTypeName!
        myObj['application'] = group[0]?.applicationName!
        myObj['programName'] = group[0]?.programName
          ? group[0]?.programName
          : '-'
        myObj['smallestInterval'] = admMeasureDetails?.SmallestInterval ?? 'M'
        myObj['ccnNumber'] = ''

        myObj['isEmptyIndicator'] = isEmptyIndicator

        modelData.push(myObj)
      } catch (error) {
        // TODO: Log it
        continue
      }
    }
  } catch (error) {
    throw error
  }

  return modelData
}
