import type { Dayjs } from 'dayjs'
import type { GroupMetadata } from './groupMetadata'
import type { SubOrganization } from './subOrganization'

export type Group = {
  groupId: string
  groupName: string
  userId: string
  displayName: string
  organizationId: string
  organizationName: string
  createdDate: Dayjs
  modifiedDate?: Dayjs
  subOrganizations: SubOrganization[]
  groupMetadata: GroupMetadata[]
}
