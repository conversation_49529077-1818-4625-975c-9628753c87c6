import type { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import type { ScorecardView } from '@/enums/scorecardView'
import type { SelectionType } from '@/enums/selectionType'
import type { Dayjs } from 'dayjs'

import type { OrganizationRole } from './organizationRole'
import type { SubOrganization } from './subOrganization'
import { ExpansionConfiguration } from './expansionConfiguration'
import { Partner } from './partner'

export type MeasureResultsByHospitalsModel = {
  primaryMeasureType: PrimaryMeasureTypeConstants
  startDate: Dayjs
  endDate: Dayjs
  hideEmptyIndicators: boolean
  measureIdentifier: string
  selectedSpan: ScorecardView
  entities: string[]
  organizations?: string
  providers?: string[]
  submissionGroups?: string[]
  organizationId: string
  selectionType: SelectionType
  userId: string
  orgRoles: OrganizationRole[] | null
  expansionConfiguration: ExpansionConfiguration[]
  partners: Partner[]
  useSpecialEntityStructure: boolean
  subOrganizations: SubOrganization[]
}
