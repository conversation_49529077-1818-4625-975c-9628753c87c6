import type { ScorecardView } from '@/enums/scorecardView'
import type { Dayjs } from 'dayjs'

import type { Organization } from './organization'
import type { ScorecardPeriodType } from '@/enums/scorecardPeriodType'

export type ExportECResultsBySubmissionGroupsQuery = {
  scorecardView: ScorecardView
  organizationId: string
  startDate: Dayjs
  endDate: Dayjs
  measureIdentifiers: string[]
  periodType: ScorecardPeriodType
  entities: string[]
  organizations: Organization[]
  isPartner: boolean
  primaryMeasureType: string
  providers: string[]
}
