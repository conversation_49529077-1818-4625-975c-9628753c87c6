import type { INotation } from '@/enums/iNotation'

export type MeasureResultSummary = {
  id: bigint
  cdmDataSourceName: string
  measureGUID: string
  measureSubId: string
  entityCode?: string
  entityName?: string
  sourceContainerIdentifier: string
  startDate: Date
  endDate: Date
  period: string
  inDenominatorOnly: number | null
  numerator: number | null
  denominator: number | null
  numeratorExclusion: number | null
  iNotationName?: INotation
  denominatorExclusion: number | null
  denominatorException: number | null
  incompleteCases: number | null
  ipp: number | null
  numeratorValue: number | null
  denominatorValue: number | null
  performance: number | null
  percentile: number | null
  processingStartDate: Date | null
  processingEndDate: Date | null
  hoverText: string
}
