import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface ApplicationRole
 */
export interface ApplicationRole {
  /**
   *
   * @type {number}
   * @memberof ApplicationRole
   */
  id?: number
  /**
   *
   * @type {string}
   * @memberof ApplicationRole
   */
  applicationId?: string | null
  /**
   *
   * @type {Array<string>}
   * @memberof ApplicationRole
   */
  roles?: Array<string> | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfApplicationRole = (
  value: object
): value is ApplicationRole => {
  return instanceOfType<ApplicationRole>(value)
}
