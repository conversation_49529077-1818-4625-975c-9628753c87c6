import { TableEntity } from '@azure/data-tables'

export type LoadStatus = TableEntity<{
  organizationId: string
  startDateTime: string
  endDateTime: string
  status: string
  categoryAssignmentCount: number
  count: number
  isEhLoadsDefective: boolean
  ehCalculationsCompletionDate: string
  isEcLoadsDefective: boolean
  ecCalculationsCompletionDate: string
  cdmLastLoadDate: string
  ccn?: string
  type: 'EC' | 'EH'
  processingType: string
  organizationName: string
  hospitalName: string
  mspLastPublishDate: Date
  cdmLastLoadDateDisplay: string
  mspLastPublishDateDisplay: string
  admLoadStartDate: string
  admLoadEndDate: string
  startDateTimeDisplay: string
  endDateTimeDisplay: string
  duration: string
  ehCalculationsCompletionDateDisplay: string
  ecCalculationsCompletionDateDisplay: string
}>
