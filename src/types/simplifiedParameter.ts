import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface SimplifiedParameter
 */
export interface SimplifiedParameter {
  /**
   *
   * @type {string}
   * @memberof SimplifiedParameter
   */
  key?: string | null
  /**
   *
   * @type {string}
   * @memberof SimplifiedParameter
   */
  value?: string | null
  /**
   *
   * @type {string}
   * @memberof SimplifiedParameter
   */
  valueType?: string | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfSource = (
  value: object
): value is SimplifiedParameter => {
  return instanceOfType<SimplifiedParameter>(value)
}
