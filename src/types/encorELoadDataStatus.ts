import { instanceOfType } from '@/lib/instanceOfType'

export type EncorELoadDataStatus = {
  startDate: string
  endDate: string
  isDataAvailable: boolean
  dataStatusTitle: string
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfEncorELoadDataStatus = (
  value: object
): value is EncorELoadDataStatus => {
  return instanceOfType<EncorELoadDataStatus>(value)
}
