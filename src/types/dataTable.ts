// Define the Column type with name and type (for simplicity, using string as a placeholder for type)
type DataColumn<T = any> = {
    name: string;   // Column name
    type: string;   // Column type as string (could be 'string', 'number', 'boolean', etc.)
    value?: T;      // The value of the column (optional, will be used in rows)
};

// Define the Row type, where values are indexed by column names
type DataRow = { [key: string]: any };

// Define the DataTable type
type DataTable = {
    columns: DataColumn[];     // List of columns
    rows: DataRow[];           // List of rows
};