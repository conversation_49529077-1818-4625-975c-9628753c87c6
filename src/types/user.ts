import { instanceOfType } from '@/lib/instanceOfType'

import type { ApplicationRole } from './applicationRole'
import type { OrganizationRole } from './organizationRole'
import type { PartnerRole } from './partnerRole'

/**
 *
 * @export
 * @interface User
 */
export interface User {
  /**
   *
   * @type {string}
   * @memberof User
   */
  userId?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  displayName?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  firstName?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  lastName?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  emailAddress?: string | null
  /**
   *
   * @type {boolean}
   * @memberof User
   */
  isActive?: boolean
  /**
   *
   * @type {string}
   * @memberof User
   */
  tenantName?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  tenantId?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  objectId?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  workPhone?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  cellPhone?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  homePhone?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  profileImage?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  application?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  organizationId?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  organizationName?: string | null
  /**
   *
   * @type {Date}
   * @memberof User
   */
  lastLoginDate?: Date | null
  /**
   *
   * @type {Date}
   * @memberof User
   */
  deactivationDate?: Date | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  deactivationReason?: string | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  deactivatedByUserId?: string | null
  /**
   *
   * @type {Date}
   * @memberof User
   */
  reactivationDate?: Date | null
  /**
   *
   * @type {string}
   * @memberof User
   */
  reactivatedByUserId?: string | null
  /**
   *
   * @type {Array<string>}
   * @memberof User
   */
  roles?: Array<string> | null
  /**
   *
   * @type {Array<string>}
   * @memberof User
   */
  applicationRoles?: Array<string> | null
  /**
   *
   * @type {Array<OrganizationRole>}
   * @memberof User
   */
  organizationRoles?: Array<OrganizationRole> | null
  /**
   *
   * @type {Record<string, string> | null}
   * @memberof User
   */
  subOrgnizationRoles?: Record<string, string> | null
  /**
   *
   * @type {Array<ApplicationRole>}
   * @memberof User
   */
  allApplicationRoles?: Array<ApplicationRole> | null
  /**
   *
   * @type {Array<PartnerRole>}
   * @memberof User
   */
  partnerRoles?: Array<PartnerRole> | null
  /**
   *
   * @type {boolean}
   * @memberof User
   */
  isDeleted?: boolean
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfUser = (value: object): value is User => {
  return instanceOfType<User>(value)
}
