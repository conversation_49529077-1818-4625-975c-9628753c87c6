import { instanceOfType } from '@/lib/instanceOfType'

export type LoadStatusInfo = {
  endDate: string
  startDate: string
  successStatus: 'Success' | 'Failure'
  timeDuration: string
  procedureRunningStatus: string
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfLoadStatusInfo = (
  value: object
): value is LoadStatusInfo => {
  return instanceOfType<LoadStatusInfo>(value)
}
