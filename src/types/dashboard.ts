import { TableEntity } from "@azure/data-tables";

export type Dashboard = TableEntity<{
    name: string,
    description: string,
    configurations: string,
    sharing?: DashboardDetails[]
}>

export type DashboardDetails = TableEntity<{
    dashboardRowKey: string,
    userId: string,
    isDefault: boolean,
    isShared: boolean,
    isFavorite: boolean,
    organizationId: string
}>

export type AddDashboardRequest = {
    name: string,
    description: string,
    configuration: string,
}

export type ShareDashboardRequest = {
    dashboardId: string,
    userId: string,
}

