import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface Location
 */
export interface Location {
  /**
   *
   * @type {number}
   * @memberof Location
   */
  id?: number
  /**
   *
   * @type {string}
   * @memberof Location
   */
  locationId?: string | null
  /**
   *
   * @type {string}
   * @memberof Location
   */
  locationName?: string | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfLocation = (value: object): value is Location => {
  return instanceOfType<Location>(value)
}
