import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { TableEntity } from '@azure/data-tables'
import { ExtensionLevels } from "@/enums/extensionLevels";

export type ExpansionConfiguration = TableEntity<{
  label: string
  level: ExtensionLevels
  measureType: PrimaryMeasureTypeConstants
  entries: string
  selectedLevel: EntityLevelConstants
}>

export enum EntityLevelConstants {
  None = '',
  DoNotDisplay = 'Do not display',
  TopLevel = 'Top level',
  SecondLevel = '2nd level',
  Thirdlevel = '3rd level',
}
