import type { Dayjs } from 'dayjs'

import type { ScorecardView } from '@/enums/scorecardView'
import type { Organization } from './organization'

export type ExportFacilityResultsQuery = {
  organizationID: string
  scorecardView: ScorecardView
  startDate: Dayjs
  endDate: Dayjs
  isPartner: boolean
  primaryMeasureType: string
  measureIdentifiers: string[]
  hospitalId: string[]
  organizations: Organization[]
}
