/**
 * Represents the SavedFilterModel type.
 *
 * @export
 * @type SavedFilterModel
 */
export type SavedFilterModel = {
  /**
   * Identifier for the saved filter.
   *
   * @type {string}
   */
  id: string | null

  /**
   * Name of the filter.
   *
   * @type {string}
   */
  filterName: string | null

  /**
   * Page associated with the filter.
   *
   * @type {string}
   */
  page: string | null

  /**
   * List of measures included in the filter.
   *
   * @type {string[]}
   */
  measures: string[] | null

  /**
   * List of hospitals included in the filter.
   *
   * @type {string[]}
   */
  hospitals: string[] | null

  /**
   * List of providers included in the filter.
   *
   * @type {string[]}
   */
  providers: string[] | null

  /**
   * List of groups included in the filter.
   *
   * @type {string[]}
   */
  groups: string[] | null

  /**
   * List of organizations included in the filter.
   *
   * @type {string[]}
   */
  organizations: string[] | null

  /**
   * List of facilities included in the filter.
   *
   * @type {string[]}
   */
  facilities?: string[] | null

  /**
   * Indicates if the filter is a view filter.
   *
   * @type {boolean}
   */
  isViewFilter: boolean | null
}
