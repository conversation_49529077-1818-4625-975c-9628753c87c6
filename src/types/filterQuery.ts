import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { TableEntity } from '@azure/data-tables'

export type FilterQuery = {
  filterName: string
  page: string
  measures: string[]
  hospitals: string[]
  groups: string[]
  providers: string[]
  organizations: string[]
  facilities: string[]
  userId: string
  partnerId: string
  isPartner: boolean
  measureType: PrimaryMeasureTypeConstants
}

export type ChunkMetadata = TableEntity<{
  mainEntityId?: string
  isChunk?: boolean // Identifies if this is a chunk
  chunkIndex?: number // Order of chunks
  chunkData?: string // Partial data
}>
