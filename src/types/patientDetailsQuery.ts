import type { EntityDetailType } from '@/enums/entityDetailType'
import type { ScorecardView } from '@/enums/scorecardView'

export interface PatientDetailsQuery {
  organizationId: string
  scorecardView: ScorecardView
  periodSpan: string
  primaryMeasureType: string
  entityType: EntityDetailType
  entityId: string
  measureIdentifier: string
  isPartner: boolean
  isPatientLevelAccessAvailable: boolean
  category: string
  sourceContainerIdentifier: string
}
