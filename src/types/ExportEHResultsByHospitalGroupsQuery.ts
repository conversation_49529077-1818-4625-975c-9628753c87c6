import type { ScorecardView } from '@/enums/scorecardView'
import type { Dayjs } from 'dayjs'
import type { SubOrganization } from './subOrganization'
import type { ScorecardPeriodType } from '@/enums/scorecardPeriodType'

export type ExportEHResultsByHospitalGroupsQuery = {
  scorecardView: ScorecardView
  organizationId: string
  startDate: Dayjs
  endDate: Dayjs
  measureIdentifiers: string[]
  periodType: ScorecardPeriodType
  subOrganizations: SubOrganization[]
  primaryMeasureType: string
  isPartner: boolean
}
