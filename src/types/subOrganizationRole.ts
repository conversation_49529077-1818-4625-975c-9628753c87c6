import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface SubOrganizationRole
 */
export interface SubOrganizationRole {
  /**
   *
   * @type {string}
   * @memberof SubOrganizationRole
   */
  subOrganizationId?: string | null
  /**
   *
   * @type {Array<string>}
   * @memberof SubOrganizationRole
   */
  roles?: Array<string> | null
}

/**
 * Check if a given object implements the SubOrganizationRole interface.
 */
export const instanceOfSubOrganizationRole = (
  value: object
): value is SubOrganizationRole => {
  return instanceOfType<SubOrganizationRole>(value)
}
