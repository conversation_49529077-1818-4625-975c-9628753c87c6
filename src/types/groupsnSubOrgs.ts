/**
 * Represents the GroupsnSubOrgs type.
 *
 * @export
 * @type GroupsnSubOrgs
 */
export type GroupsnSubOrgs = {
  /**
   * Identifier for the sub-organization.
   *
   * @type {string}
   */
  subOrganizationId: string

  /**
   * Name of the sub-organization.
   *
   * @type {string}
   */
  subOrganizationName: string

  /**
   * CMS Certification Number (CCN) of the sub-organization.
   *
   * @type {string}
   */
  ccnNumber: string

  /**
   * Indicates if this is an organization group.
   *
   * @type {boolean}
   */
  isOrganizationGroup: boolean
}
