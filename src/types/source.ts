import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface Source
 */
export interface Source {
  /**
   *
   * @type {number}
   * @memberof Source
   */
  id?: number
  /**
   *
   * @type {string}
   * @memberof Source
   */
  sourceId?: string | null
  /**
   *
   * @type {string}
   * @memberof Source
   */
  sourceName?: string | null
  /**
   *
   * @type {string}
   * @memberof Source
   */
  sourceTimeZone?: string | null
  /**
   *
   * @type {string}
   * @memberof Source
   */
  destinationTimeZone?: string | null
  /**
   *
   * @type {string}
   * @memberof Source
   */
  taxIdentificationNumber?: string | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfSource = (value: object): value is Source => {
  return instanceOfType<Source>(value)
}
