import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface Product
 */
export interface Product {
  /**
   *
   * @type {string}
   * @memberof Product
   */
  id?: string | null
  /**
   *
   * @type {string}
   * @memberof Product
   */
  name?: string | null
  /**
   *
   * @type {string}
   * @memberof Product
   */
  friendlyName?: string | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfProduct = (value: object): value is Product => {
  return instanceOfType<Product>(value)
}
