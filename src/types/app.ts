import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface App
 */
export interface App {
  /**
   *
   * @type {number}
   * @memberof App
   */
  appId?: number
  /**
   *
   * @type {string}
   * @memberof App
   */
  id?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  name?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  description?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  secret?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  baseUrl?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  corsOrigins?: string | null
  /**
   *
   * @type {boolean}
   * @memberof App
   */
  isWebApp?: boolean
  /**
   *
   * @type {boolean}
   * @memberof App
   */
  isSinglePageApp?: boolean
  /**
   *
   * @type {string}
   * @memberof App
   */
  product?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  isInMaintenance?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  maintenanceMessage?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  allowsBeingDisplayedInFrame?: string | null
  /**
   *
   * @type {number}
   * @memberof App
   */
  numberOfSecrets?: number
  /**
   *
   * @type {string}
   * @memberof App
   */
  additionalApis?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  requireConsent?: string | null
  /**
   *
   * @type {string}
   * @memberof App
   */
  directLoginUrl?: string | null
  /**
   *
   * @type {boolean}
   * @memberof App
   */
  isOnPremApp?: boolean
  /**
   *
   * @type {boolean}
   * @memberof App
   */
  excludeFrom9Dots?: boolean
  /**
   *
   * @type {string}
   * @memberof App
   */
  excludeFrom9DotsStatus?: string | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfApp = (value: object): value is App => {
  return instanceOfType<App>(value)
}
