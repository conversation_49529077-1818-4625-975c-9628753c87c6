import type { EntityDetailType } from '@/enums/entityDetailType'
import type { ScorecardView } from '@/enums/scorecardView'
import type { Dayjs } from 'dayjs'

export type MeasureResultDetailsQuery = {
  measureIdentifier: string
  organizationId: string
  scorecardView: ScorecardView
  startDate: Dayjs
  endDate: Dayjs
  entityId: string
  entityDetailType: EntityDetailType // Replace 'any' with the actual type
  patientDetailAccessSuborgs?: string[]
  haveOrgLevelPatientDetailAccess?: boolean
  isPartner: boolean
}
