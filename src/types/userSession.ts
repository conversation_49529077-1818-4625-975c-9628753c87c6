import { type PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { type SelectionType } from '@/enums/selectionType'
import { type PatientDetailAccess } from '@/types/patientDetailAccess'
import { ExpansionConfiguration } from './expansionConfiguration'
import { CitcUserRoles } from '@/shared/roles'

export type UserSession = {
  displayName: string
  organizationId: string | null
  organizationName: string | null
  selectionType: SelectionType
  patientDetailAccess: PatientDetailAccess
  hideExcelExport: boolean
  useSpecialEntityStructure: boolean
  exportMode: string
  primaryMeasureType: PrimaryMeasureTypeConstants
  hasPatientExplorerAccessAtPartner: boolean
  hasGlobalPatientExplorerAccess: boolean
  expansionConfiguration: ExpansionConfiguration[]
  permissions: Set<CitcUserRoles>
}
