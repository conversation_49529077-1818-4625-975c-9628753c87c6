export type CalculatedMeasure = {
  measureIdentifier?: string
  measureTitle?: string
  trendCss?: string
  measureDescription?: string
  friendlyName?: string
  subDomain?: string
  type?: string
  domain?: string
  cmsId?: string
  subType?: string
  application?: string
  programName?: string
  smallestInterval?: string
  ccnNumber?: string
  isEmptyIndicator?: boolean
  isExpandable?: boolean
  sourceContainerIdentifier?: string
  note?: string
  isIAPIMeasure?: boolean
} & {
  [key in
  | `Q${number}_${number}`
  | `CY_${number}`
  | `${Capitalize<string>}_${number}`]?: string
}

export type CalculatedMeasureEntity = {
  order?: number
  entityCode?: string | null
  entity?: string
  entityNameForDetails?: string
  isIAPIMeasure?: boolean
} & CalculatedMeasure

export type CalculatedMeasureHospital = {
  hospitalId?: string
  hospital?: string
  entityNameForDetails?: string
  isIAPIMeasure?: boolean
} & CalculatedMeasure

export type ProviderResult = {
  npi?: string
  providerName?: string
  providerWithEntityName?: string
  submissionGroupId?: string
} & {
  [key in
  | `Q${number}_${number}`
  | `CY_${number}`
  | `${Capitalize<string>}_${number}`]?: string
} & CalculatedMeasure

export type MeasureResultByHospital = CalculatedMeasureHospital

export type ECMeasureBySubmissionGroup = CalculatedMeasureEntity &
  CalculatedMeasure

export type FacilityResultByCCN = {
  facilityId?: string,
  facilityName?: string,
  facilityWithEntityName?: string,
  hospitalId?: string,
  isIAPIMeasure?: boolean
} & CalculatedMeasure