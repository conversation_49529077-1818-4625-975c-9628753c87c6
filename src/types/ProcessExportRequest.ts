import type { ScorecardView } from '@/enums/scorecardView'

import type { Dayjs } from 'dayjs'
import type { Organization } from './organization'

export type ProcessExportRequest = {
  scorecardView: ScorecardView
  organizationId: string
  lastSelectedOrganizationId: string
  startDate: Dayjs
  endDate: Dayjs
  subOrgs: string[]
  organizations: Organization[]
  isPartner: boolean
  primaryMeasureType: string
  emailAddress: string
  sendGridAPIKey: string
  organizationName: string
  visibleColumns: string[]
  hideEmptyIndicators: boolean
  measureIdentifiers: string[]
  providers: string[]
  userId: string
}
