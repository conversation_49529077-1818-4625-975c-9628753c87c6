import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface Service
 */
export interface Service {
  /**
   *
   * @type {number}
   * @memberof Service
   */
  id?: number
  /**
   *
   * @type {string}
   * @memberof Service
   */
  serviceId?: string | null
  /**
   *
   * @type {string}
   * @memberof Service
   */
  serviceName?: string | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfService = (value: object): value is Service => {
  return instanceOfType<Service>(value)
}
