import type { ScorecardPeriodType } from '@/enums/scorecardPeriodType'
import type { ScorecardView } from '@/enums/scorecardView'
import type { Dayjs } from 'dayjs'

import type { Organization } from './organization'
import { ExpansionConfiguration } from './expansionConfiguration'
import { Partner } from './partner'

export type ECMeasureResultBySubmissionGroupQuery = {
  scorecardView: ScorecardView
  organizationId: string
  startDate: Dayjs
  endDate: Dayjs
  measureIdentifier: string
  periodType?: ScorecardPeriodType
  entites: string[]
  isPartner: boolean
  measureType: string
  userId: string
  hasLimitedAccess: boolean
  organizationList: Organization[]
  expansionConfiguration: ExpansionConfiguration[]
  partners: Partner[]
}
