import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface PartnerRole
 */
export interface PartnerRole {
  /**
   *
   * @type {string}
   * @memberof PartnerRole
   */
  partnerId?: string | null
  /**
   *
   * @type {Array<string>}
   * @memberof PartnerRole
   */
  roles?: Array<string> | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfPartnerRole = (value: object): value is PartnerRole => {
  return instanceOfType<PartnerRole>(value)
}
