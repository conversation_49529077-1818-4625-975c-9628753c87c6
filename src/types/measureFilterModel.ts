/**
 * Represents the MeasureFilterModel type.
 *
 * @export
 * @type MeasureFilterModel
 */
export type MeasureFilterModel = {
  /**
   * Identifier for the measure.
   *
   * @type {string}
   */
  measureId: string

  /**
   * Name of the measure.
   *
   * @type {string}
   */
  measureName: string

  /**
   * Type of denominator qualifying the measure.
   *
   * @type {string}
   */
  denominatorQualifyingType: string

  /**
   * The smallest interval applicable to the measure.
   *
   * @type {string}
   */
  smallestInterval: string

  /**
   * Equity strata related to the measure.
   *
   * @type {string}
   */
  equityStrata: string

  applicationName: string

  isIAPIMeasure?: boolean
}
