/**
 * Represents the Provider type.
 *
 * @export
 * @type Provider
 */
export type Provider = {
  /**
   * National Provider Identifier (NPI).
   *
   * @type {string}
   */
  npi: string

  /**
   * Name of the provider.
   *
   * @type {string}
   */
  providerName: string

  /**
   * Identifier for the submission group.
   *
   * @type {string}
   */
  submissionGroupId?: string

  /**
   * Identifier for the submission group container.
   *
   * @type {string}
   */
  submissionGroupContainerIdentifier?: string

  /**
   * Combined provider and entity name.
   *
   * @type {string}
   */
  providerWithEntityName: string
}
