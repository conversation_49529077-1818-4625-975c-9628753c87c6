import { instanceOfType } from '@/lib/instanceOfType'
import type { SubOrganizationRole } from './subOrganizationRole'

/**
 *
 * @export
 * @interface OrganizationRole
 */
export interface OrganizationRole {
  /**
   *
   * @type {string}
   * @memberof OrganizationRole
   */
  organizationId?: string | null
  /**
   *
   * @type {string}
   * @memberof OrganizationRole
   */
  organizationName?: string | null
  /**
   *
   * @type {number}
   * @memberof OrganizationRole
   */
  applicationId?: number
  /**
   *
   * @type {string}
   * @memberof OrganizationRole
   */
  applicationClientId?: string | null
  /**
   *
   * @type {string}
   * @memberof OrganizationRole
   */
  applicationUrl?: string | null
  /**
   *
   * @type {Array<string>}
   * @memberof OrganizationRole
   */
  roles?: Array<string>
  /**
   *
   * @type {Array<SubOrganizationRole>}
   * @memberof OrganizationRole
   */
  subOrganizationRoles?: SubOrganizationRole[] | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfOrganizationRole = (
  value: object
): value is OrganizationRole => {
  return instanceOfType<OrganizationRole>(value)
}
