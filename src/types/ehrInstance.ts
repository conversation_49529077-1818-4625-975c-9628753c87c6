import { instanceOfType } from '@/lib/instanceOfType'

/**
 *
 * @export
 * @interface EhrInstance
 */
export interface EhrInstance {
  /**
   *
   * @type {number}
   * @memberof EhrInstance
   */
  id?: number
  /**
   *
   * @type {string}
   * @memberof EhrInstance
   */
  ehrInstanceId?: string | null
  /**
   *
   * @type {string}
   * @memberof EhrInstance
   */
  ehrInstanceName?: string | null
  /**
   *
   * @type {string}
   * @memberof EhrInstance
   */
  sourceTimeZone?: string | null
  /**
   *
   * @type {string}
   * @memberof EhrInstance
   */
  destinationTimeZone?: string | null
  /**
   *
   * @type {string}
   * @memberof EhrInstance
   */
  taxIdentificationNumber?: string | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfEhrInstance = (value: object): value is EhrInstance => {
  return instanceOfType<EhrInstance>(value)
}
