import { instanceOfType } from '@/lib/instanceOfType'

import type { EhrInstance } from './ehrInstance'
import type { Facility } from './facility'
import type { Location } from './location'
import type { Service } from './service'
import type { Source } from './source'

/**
 *
 * @export
 * @interface SubOrganization
 */
export interface SubOrganization {
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  subOrganizationId?: string | null
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  subOrganizationName?: string | null
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  organizationId?: string | null
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  organizationName?: string | null
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  ccnNumber?: string | null
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  privateCcnNumber?: string | null
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  sourceTimeZone?: string | null
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  destinationTimeZone?: string | null
  /**
   *
   * @type {boolean}
   * @memberof SubOrganization
   */
  isHybrid?: boolean
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  taxIdentificationNumber?: string | null
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  entryType?: string | null
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  alternateId?: string | null
  /**
   *
   * @type {boolean}
   * @memberof SubOrganization
   */
  isDeleted?: boolean
  /**
   *
   * @type {string}
   * @memberof SubOrganization
   */
  hybridStatus?: string | null
  /**
   *
   * @type {Array<string>}
   * @memberof SubOrganization
   */
  tags?: Array<string> | null
  /**
   *
   * @type {Array<Facility>}
   * @memberof SubOrganization
   */
  facilities?: Array<Facility> | null
  /**
   *
   * @type {Array<Location>}
   * @memberof SubOrganization
   */
  locations?: Array<Location> | null
  /**
   *
   * @type {Array<Service>}
   * @memberof SubOrganization
   */
  services?: Array<Service> | null
  /**
   *
   * @type {Array<Source>}
   * @memberof SubOrganization
   */
  sources?: Array<Source> | null
  /**
   *
   * @type {Array<EhrInstance>}
   * @memberof SubOrganization
   */
  ehrInstances?: Array<EhrInstance> | null
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfSubOrganization = (
  value: object
): value is SubOrganization => {
  return instanceOfType<SubOrganization>(value)
}
