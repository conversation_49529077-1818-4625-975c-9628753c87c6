import type { Organization } from './organization'
import { instanceOfType } from '@/lib/instanceOfType'
import { CitcUserRoles } from '@/shared/roles'

/**
 *
 * @export
 * @interface Partner
 */
export interface Partner {
  /**
   *
   * @type {string}
   * @memberof Partner
   */
  id?: string | null
  /**
   *
   * @type {string}
   * @memberof Partner
   */
  name?: string | null
  /**
   *
   * @type {Array<Organization>}
   * @memberof Partner
   */
  organizations?: Array<Organization> | null
  /**
   *
   * @type {Array<string>}
   * @memberof Partner
   */
  roles?: Array<CitcUserRoles>
  /**
   *
   * @type {boolean}
   * @memberof Partner
   */
  isFullPartner?: boolean
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfPartner = (value: object): value is Partner => {
  return instanceOfType<Partner>(value)
}
