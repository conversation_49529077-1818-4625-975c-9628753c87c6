import type { INotation } from '@/enums/iNotation'

import type { PopulationIds } from './populationIds'

export type SnowflakeMeasure = {
  Id: number
  MedisolvMeasureId: string
  DenominatorQualifyingType: string
  StratifcationDescription: string | null
  Stratification: boolean | null
  MeasureSubId: string | null
  MeasureScoring: string | null
  InverseMeasureFlag: boolean | null
  MedisolvApplication: string | null
  DataCompletenessRequired: boolean | null
  DataCompletenessThreshold: number | null
  CalculationFactor: string | null
  DecimalPointsCount: number | null
  Name: string | null
  EquityStrata: string | null
  Cohort: string | null
  Outcome: string | null
  EncounterType: string | null
  MeasureType: string | null
  HighIsGood: boolean | null
  IgnoreMinimumCaseThreshold: boolean | null
  LastUpdateDateTime: Date | null
  IsActive: boolean | null
  StoredProcedureName: string | null
  SmallestInterval: string
  LongMeasureName: string | null
  CompositeMeasCNT: number | null
  NullRate: boolean | null
  NullIPP: boolean | null
  NullDenomOnly: boolean | null
  NullDenExcl: boolean | null
  NullNumerator: boolean | null
  NullDenominator: boolean | null
  NullException: boolean | null
  NullNumExcl: boolean | null
  NoDrill: boolean | null
  CompositeType: string | null

}

export type Measure = {
  uniqueMeasureIdentifier: string
  versionIdentifier: string
  measureIdentifier: string
  versionMeasureMapId: number
  measureName: string
  measureDescription: string
  measureFriendlyName: string
  startPeriod: string
  endPeriod: string
  specificationURL_HTML: string
  specificationURL_PDF: string
  strata: string
  subId: string
  nqfId: string
  hqmfId: string
  cMSId: string
  tjcId: string
  typeName: string
  applicationName: string
  applicationFriendlyName: string
  subTypeName: string
  topicName: string
  collectionName: string
  categoryName: string
  iNotationName: INotation
  domainName: string
  subDomainName: string
  friendlyVersionName: string
  measureVersionName: string
  versionNumber: string
  versionName: string
  episodeOfCare: string
  registryQualityId: string
  calculationType: string
  calculationFormula: string
  programType: number
  programName: string
  populationIds: PopulationIds
  populationCriteria: string
  equityStrata: string
  coreMeasureIdentifier: string
  coreMeasureId: string
}


export type AvailableMeasure = {
  measureIdentifier: string
  measureName: string
  measureSubId: string | null
  strata: string | null
  denominatorQualifyingType: string | null
  sourceContainerIdentifier: string | null
  smallestInterval: string | null
  applicationName:string | null
}