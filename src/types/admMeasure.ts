export type ADMMeasure = {
  Id: number
  MedisolvMeasureId: string
  DenominatorQualifyingType: string
  StratifcationDescription: string | null
  Stratification: boolean | null
  MeasureSubId: string | null
  MeasureScoring: string | null
  InverseMeasureFlag: boolean | null
  MedisolvApplication: string | null
  DataCompletenessRequired: boolean | null
  DataCompletenessThreshold: number | null
  CalculationFactor: string | null
  DecimalPointsCount: number | null
  Name: string | null
  EquityStrata: string | null
  Cohort: string | null
  Outcome: string | null
  EncounterType: string | null
  MeasureType: string | null
  HighIsGood: boolean | null
  IgnoreMinimumCaseThreshold: boolean | null
  LastUpdateDateTime: Date | null
  IsActive: boolean | null
  StoredProcedureName: string | null
  SmallestInterval: string
  LongMeasureName: string | null
  CompositeMeasCNT: number | null
  NullRate: boolean | null
  NullIPP: boolean | null
  NullDenomOnly: boolean | null
  NullDenExcl: boolean | null
  NullNumerator: boolean | null
  NullDenominator: boolean | null
  NullException: boolean | null
  NullNumExcl: boolean | null
  NoDrill: boolean | null
  CompositeType: string | null
}
