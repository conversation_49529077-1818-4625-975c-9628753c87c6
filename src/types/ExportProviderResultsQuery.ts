import type { ScorecardView } from '@/enums/scorecardView'
import type { Dayjs } from 'dayjs'
import type { Organization } from './organization'

export type ExportProviderResultsQuery = {
  organizationID: string
  scorecardView: ScorecardView
  startDate: Dayjs
  endDate: Dayjs
  isPartner: boolean
  primaryMeasureType: string
  measureIdentifiers: string[]
  submissionGroups: string[]
  organizations: Organization[]
}
