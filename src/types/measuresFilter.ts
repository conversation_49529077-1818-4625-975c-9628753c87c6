import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { Facility } from './facility'
import { FilterItem } from './filterItem'
import { GroupsnSubOrgs } from './groupsnSubOrgs'
import { MeasureFilterModel } from './measureFilterModel'
import { Organization } from './organization'
import { Provider } from './provider'
import { SavedFilterModel } from './savedFilterModel'
import { SubmissionGroup } from './submissionGroup'

export type MeasuresFilter = {
  appliedFilters: Record<
    | 'measures'
    | 'subOrganizations'
    | 'submissionGroups'
    | 'providers'
    | 'organizations'
    | 'facilities',
    string[]
  >
  measures: MeasureFilterModel[]
  checkedMeasures: FilterItem[]
  subOrganizations: GroupsnSubOrgs[]
  checkedSubOrganizations: FilterItem[]
  savedFilters: SavedFilterModel[]
  checkedSavedFilters: FilterItem[]
  submissionGroups: SubmissionGroup[]
  checkedSubmissionGroups: FilterItem[]
  providers: Provider[]
  checkedProviders: FilterItem[]
  organizations: Organization[]
  checkedOrganizations: FilterItem[]
  facilities: Facility[]
  checkedFacilities: FilterItem[]
  useSpecialEntityStructure: boolean
  hideEmptyIndicators: boolean
  expandedSections: string[]
}
