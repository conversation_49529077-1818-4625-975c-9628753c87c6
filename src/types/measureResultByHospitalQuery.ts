import type { ScorecardPeriodType } from '@/enums/scorecardPeriodType'
import type { ScorecardView } from '@/enums/scorecardView'
import type { Dayjs } from 'dayjs'

import type { SubOrganization } from './subOrganization'
import { Organization } from './organization'
import { ExpansionConfiguration } from './expansionConfiguration'
import { Partner } from './partner'

export type MeasureResultByHospitalQuery = {
  scorecardView: ScorecardView
  organizationId: string
  startDate: Dayjs
  endDate: Dayjs
  measureIdentifier: string
  periodType?: ScorecardPeriodType
  subOrganization: SubOrganization[]
  isPartner: boolean
  useSpecialEntityStructure: boolean
  organizationList: Organization[]
  expansionConfiguration: ExpansionConfiguration[]
  partners: Partner[]
}
