/** @deprecated - use LoadStatus with move to azure table storage (src/enums/loadStatus.ts) */
export type LoadStatusModel = {
  processingType: string
  organizationId: string
  organizationName: string
  hospitalName: string
  ccn: string
  mspLastPublishDate: Date | null
  cdmLastLoadDate: Date
  cdmLastLoadDateDisplay: string
  mspLastPublishDateDisplay: string
  startDateTime: Date
  endDateTime: Date
  admLoadStartDate: string
  admLoadEndDate: string
  startDateTimeDisplay: string
  endDateTimeDisplay: string
  duration: string
  status: string
  categoryAssignmentCount: string
  count: number
  ehCalculationsCompletionDate?: Date | null
  ecCalculationsCompletionDate?: Date | null
  ehCalculationsCompletionDateDisplay: string
  ecCalculationsCompletionDateDisplay: string
}
