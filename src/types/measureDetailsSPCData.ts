export interface SPCChartData {
  dates: string[]
  performance: number[]
  centerLine: number[]
  upperLimit: number[]
  lowerLimit: number[]
  upperWarning: number[]
  lowerWarning: number[]
  ruleViolationPoints: boolean[]
  excludedPoints: boolean[]
  phaseChanges: number[]
  ruleViolations: {
    date: string
    description: string
  }[]
}

export interface RawSPCData {
  dates: string[]
  performance: number[]
  newPhases: boolean[]
  excludedPoints: boolean[]
  measureScoring: string
  chartType: string
  denominators?: number[] // Optional to maintain backward compatibility
}

export interface ProcessedSPCData {
  dates: string[]
  performance: number[]
  centerLine: number[]
  upperLimit: number[]
  lowerLimit: number[]
  upperWarning: number[]
  lowerWarning: number[]
  ruleViolationPoints: boolean[]
  excludedPoints: boolean[]
  phaseChanges: number[]
  ruleViolations: {
    date: string
    description: string
  }[]
  measureScoring: string
  chartType: string
}
