import { instanceOfType } from '@/lib/instanceOfType'

import { type Product } from './product'
import { type SubOrganization } from './subOrganization'

/**
 *
 * @export
 * @interface Organization
 */
export type Organization = {
  /**
   *
   * @type {string}
   * @memberof Organization
   */
  organizationId: string
  /**
   *
   * @type {string}
   * @memberof Organization
   */
  organizationName: string
  /**
   *
   * @type {string}
   * @memberof Organization
   */
  clientId?: string | null
  /**
   *
   * @type {string}
   * @memberof Organization
   */
  hubSpotId?: string | null
  /**
   *
   * @type {boolean}
   * @memberof Organization
   */
  brandingImageUploaded?: boolean
  /**
   *
   * @type {string}
   * @memberof Organization
   */
  brandingImageUploadedState?: string | null
  /**
   *
   * @type {boolean}
   * @memberof Organization
   */
  whitelabelUploaded?: boolean
  /**
   *
   * @type {string}
   * @memberof Organization
   */
  whitelabelUploadedState?: string | null
  /**
   *
   * @type {boolean}
   * @memberof Organization
   */
  isDeleted?: boolean
  /**
   *
   * @type {SubOrganization[]}
   * @memberof Organization
   */
  subOrganizations?: SubOrganization[] | null
  /**
   *
   * @type {Product[]}
   * @memberof Organization
   */
  products?: Product[] | null
  /**
   *
   * @type {string}
   * @memberof Organization
   */
  productListing?: string | null
  /**
   *
   * @type {string}
   * @memberof Organization
   */
  environmentName?: string | null
  organizationAddress: string
  organizationNotes: string
  brandingImageContent: string
  whitelabelImageContent: string
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfOrganization = (
  value: object
): value is Organization => {
  return instanceOfType<Organization>(value)
}
