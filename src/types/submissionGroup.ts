import type { Dayjs } from 'dayjs'

import type { Provider } from './provider'

/**
 * Represents the SubmissionGroup type.
 *
 * @export
 * @type SubmissionGroup
 */
export type SubmissionGroup = {
  /**
   * Identifier for the submission group.
   *
   * @type {string}
   */
  submissionGroupId: string

  /**
   * Name of the submission group.
   *
   * @type {string}
   */
  submissionGroupName: string

  /**
   * Date when the processing starts.
   *
   * @type {Dayjs}
   */
  processingStartDate: Dayjs

  /**
   * Date when the processing ends.
   *
   * @type {Dayjs}
   */
  processingEndDate: Dayjs

  /**
   * Tax Identification Number (TIN).
   *
   * @type {string}
   */
  tin: string

  /**
   * List of providers associated with the submission group.
   *
   * @type {Provider[]}
   */
  providerList: Provider[]
}
