import { TableEntity } from '@azure/data-tables'

export type PerformanceGoal = {
  measureIdentifier: string
  entityId: string
  startDate: string
  endDate: string
  goalLower?: number
  goalUpper?: number
  benchmark?: number
  isYellowZoneFixedNumber?: boolean
  yellowZone?: number
  isExceptionalPerformanceNumber?: boolean
  exceptionalPerformance?: number
  lastUpdatedByUserId?: string
  lastUpdatedDateTime?: Date
} & TableEntity
