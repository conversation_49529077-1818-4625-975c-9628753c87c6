export type PerformanceGoal = {
  partitionKey: string // Composite key of Organization Id, Entity Id, Measure Identifier
  rowKey: string
  measureIdentifier: string
  entityId: string
  startDate: string
  endDate: string
  goalLower?: number
  goalUpper?: number
  benchmark?: number
  isYellowZoneFixedNumber?: boolean
  yellowZone?: number
  isExceptionalPerformanceNumber?: boolean
  exceptionalPerformance?: number
  lastUpdatedByUserId?: string
  lastUpdatedDateTime?: Date
}
