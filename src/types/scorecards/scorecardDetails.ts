import { Performance } from '@/enums/performance'
import { Dayjs } from 'dayjs'

export interface ScorecardDetails {
  id?: number
  columnName?: string
  performance?: Performance
  goal?: number | null
  rate?: number | null
  denominatorCount?: number | null
  numeratorCount?: number | null
  ippCount?: number | null
  excludedCount?: number | null
  populationCount?: number | null
  rejectedCount?: number | null
  startDate?: Dayjs
  endDate?: Dayjs
  numeratorValue?: number | null
  denominatorValue?: number | null
  denominatorException?: number | null
  rateTitle?: string
}
