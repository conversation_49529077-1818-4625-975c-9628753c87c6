import { ScorecardView } from '@/enums/scorecardView'
import { CalculatedMeasure } from '../calculatedMeasure'
import { ScorecardDetails } from './scorecardDetails'
import { INotation } from '@/enums/iNotation'

export type ScorecardResult = {
  scorecardDetailsList?: ScorecardDetails[]
  scorecardView?: ScorecardView
  inotation?: INotation
} & CalculatedMeasure

export type ScorecardResultByHospital = {
  id?: string
  name?: string
  scorecardView?: ScorecardView
  scorecardDetailsList?: ScorecardDetails[]
  inotation?: INotation
} & CalculatedMeasure

export type HospitalSummary = {
  id?: string
  name?: string
  summary?: string
  scorecardView?: ScorecardView
  sortId?: number
  isEmptyIndicator?: boolean
}
