import { instanceOfType } from '@/lib/instanceOfType'

export type ProcedureStatus = {
  jobId: number
  jobName: string
  applicationUserId: string
  startDate: Date
  endDate: Date
  successStatus: boolean
  procedureRunningStatus: string
}

/**
 * Check if a given object implements the App interface.
 */
export const instanceOfProcedureStatus = (
  value: object
): value is ProcedureStatus => {
  return instanceOfType<ProcedureStatus>(value)
}
