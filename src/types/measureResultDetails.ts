export interface MeasureResultDetails {
  id: number
  measureGUID: string | null
  measureSubId: string
  patientsId: number
  encountersId: number
  referenceDate: Date | null
  categoryAssignmentsId: number
  numeratorValue: number | null
  denominatorValue: number | null
  note: string
  patientIdentifier: string
  caseIdentifier: string
  encounterId: number
  patientName: string
  age: number
  sex: string
  facilityId: string
  result: string
  dischargeDateTime: Date | null
}
