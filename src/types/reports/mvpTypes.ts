export interface MVPSummary {
  Entity: string
  MVP: string
  Quality: number
  QualityMax: number
  IA: number
  IAMax: number
  PIScore: number
  PIScoreMax: number
  Cost: number
  CostMax: number
  Score: number
}

export interface QualityScoreByMVP {
  Period: string
  MVP: string
  Score: number
}

export interface Score {
  Score: number
  Min?: number
  Max?: number
}

export interface QualityMeasuresScore {
  Period: string
  MeasureName: string
  Score: number
}

export interface ProviderPerformance {
  ProviderName: string
  MeasureCount: number
  Points: number
}

export interface QualityMeasures {
  MeasureName: string
  Rank: number
  Numerator: number
  Denominator: number
  Performance: number
  PTile: number
  Points: number
}

export interface IAPIMeasure {
  MeasureName: string
  Completed: boolean
  Points: number
}

export interface MVP {
  Id: number
  MVPCategory: string
  MVPCategoryLink: string
  MVPCategory_Short: string
}

export interface ProviderDetail {
  MeasureName: string
  Numerator: number
  Denominator: number
  Performance: number
  PTile: number
  Points: number
}
