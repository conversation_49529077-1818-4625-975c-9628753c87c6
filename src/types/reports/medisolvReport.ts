import { IntervalType } from '@/types/intervalType'

export type MedisolvReport = {
  MedisolvReportId: string
  MedisolvReportName: string
  ReportStatus: string
  ReportType: string
  LastUpdatedBy: string
  LastUpdatedDate: Date
  medisolvReportOptions: MedisolvReportOptions[]
  IsMedisolvReportAssociated: boolean
  ReportLevelHoverText: string
  IsMedisolvOptionRequired: boolean
  reportingContentType: string
}

export type MedisolvReportOptions = {
  fieldName: string
  values: string[]
  isDisplayed: string
  hoverText: string
  isValuePopulatedFromDB: boolean
}

export type Report = {
  partitionKey: string
  rowKey: string
  name: string
  isActive: boolean
  description?: string
  isCustom?: boolean
  isFavorite?: boolean
  lastModified?: string
  reportType?: string
  timestamp?: string
  reportConfig?: string
  userId?: string
  sharedBy?: string
  mainEntityId?: string // Used for chunking
  isChunk?: boolean // Flag to identify chunks
}

// Type for the report configuration that will be stored as JSON
export interface ReportConfiguration {
  intervalType?: string
  startDate?: string
  selectedMeasures?: string[]
  organizationType?: string
  universe?: string
  filterByUniverse?: string
  filterByCode?: number
  selectedEntityId?: string
  runtime?: number
}

export type OrganizationTypesRequest = {
  intervalType: IntervalType
  startDate: Date
  endDate: Date
  measureIdentifiers: string[]
  code?: number
  universe?: string
}

export type ReportRequest = {
  intervalType: IntervalType
  startDate: Date
  endDate: Date
  measureIdentifiers: string[]
  sourceContainerIdentifier: string
  universe?: string
  entityDescription?: string
  code?: number
  runtime?: number
}

export type EntityDescriptionRequest = {
  intervalType: IntervalType
  startDate: Date
  endDate: Date
  measureIdentifiers: string[]
  universe?: string
}

export interface EntityDescriptionResponse {
  Description: string
  SourceContainerIdentifier: string
}

export type ACOMeasuresRequest = {
  codeNumeric: string
  intervalType: IntervalType
  startDate: Date
  endDate: Date
}
