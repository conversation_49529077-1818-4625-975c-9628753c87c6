import { TableEntity } from '@azure/data-tables'

export type UserNotification = TableEntity<{
  // Partition key will be userId for efficient querying per user
  partitionKey: string // userId
  // Row key will be NotificationId
  rowKey: string // NotificationId

  // UserNotifications fields
  message: string
  ownerId: string
  sentDateTime: Date

  // UserNotificationMap fields
  readDateTime: Date | null

  senderName?: string
  receiverNames?: string // Comma-separated list of receiver names
}>

export type SenderNotification = {
  Message: string
  UserIds: string[]
}
