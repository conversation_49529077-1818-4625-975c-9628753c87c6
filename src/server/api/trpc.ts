/**
 * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:
 * 1. You want to modify request context (see Part 1).
 * 2. You want to create a new middleware or type of procedure (see Part 3).
 *
 * TLDR - This is where all the tRPC server stuff is created and plugged in. The pieces you will
 * need to use are documented accordingly near the end.
 */

import { initTRPC, TRPCError } from '@trpc/server'
import superjson from 'superjson'
import { ZodError } from 'zod'

import { auth } from '@/auth'
import { getPrismaClientsByOrgId } from '@/server/db'
import CitCParametersService from '@/services/citc/citcParameters'
import { SelectionType } from '@/enums/selectionType'
import CitCPartnersService from '@/services/citc/citCPartners'
import CitCOrganizationService from '@/services/citc/citCOrganizations'
import { getSessionSubOrganizations } from '@/services/subOrganizations/getSessionSubOrganizations'
import appInsights from '@/lib/applicationInsights'
import { ExpansionConfiguration } from '@/types/expansionConfiguration'
import { cookies } from 'next/headers'
import CitCUsersService from '@/services/citc/citCUsers'
import { env } from 'process'
import { Session } from 'next-auth'
import { Partner } from '@/types/partner'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { Organization } from '@/types/organization'
import { OrganizationRole } from '@/types/organizationRole'
import { CookiesSession } from '@/next-auth'
import { decodedToken } from '@/lib/decodeAccessToken'
import MeasureResultsService from '@/services/measureResults'
import { prioritizeHospitalMeasures } from '@/lib/prioritizeHospitalMeasures'
import { redisHelper } from '@/lib/redis'
import { User } from '@/types/user'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import type { SubOrganization } from '@/types/subOrganization'
import {
  Factory as SnowflakeRepositoryFactory,
  factory as repositoryFactory,
} from '@/services/snowflake/SnowflakeRepositoryFactory'
import snowflake from 'snowflake-sdk'
import { EntityOrganizationTypeService } from '@/services/entityOrganizationType/EntityOrganizationTypeService'
import { ACOPerformanceAnalysisService } from '@/services/reports/ACOPerformanceAnalysisService'
import { ACOMeasureService } from '@/services/reports/ACOMeasureService'
import { MeasureSummaryService } from '@/services/measureSummary/MeasureSummaryService'
import { getAllExpansionConfigs } from '@/services/expansionConfigs/getAllExpansionConfigs'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import { MVPService } from '@/services/reports/MVPService'
import { CitcUserRoles } from '@/shared/roles'

export type Context = {
  headers: Headers
  session: (Session & CookiesSession) | null
  organizations: Organization[]
  partners: Partner[]
  globalParameters: SimplifiedParameter[]
  parameters?: SimplifiedParameter[]
  citCParametersService: CitCParametersService
  snowflakeRepositoryFactory?: SnowflakeRepositoryFactory
  measureResultsService?: MeasureResultsService
  entityOrganizationTypeService?: EntityOrganizationTypeService
  acoPerformanceAnalysisService?: ACOPerformanceAnalysisService
  acoMeasureService?: ACOMeasureService
  measureSummaryService?: MeasureSummaryService
  organizationId?: string
  selectionType?: SelectionType
  organizationRoles?: OrganizationRole[]
  partnerRoles?: OrganizationRole[]
  isCached: boolean
}

type CachedSession = {
  organizationId: string
  selectionType: SelectionType
  subOrganizations: SubOrganization[]
  userDetails: User
  organizationRoles?: OrganizationRole[]
  partnerRoles?: OrganizationRole[]
  parameters: SimplifiedParameter[]
  useSpecialEntityStructure: boolean
  primaryMeasureTypes: PrimaryMeasureTypeConstants[]
  orgRoles: OrganizationRole[]
  expansionConfiguration: ExpansionConfiguration[]
}

/**
 * 1. CONTEXT
 *
 * This section defines the "contexts" that are available in the backend API.
 *
 * These allow you to access things when processing a request, like the database, the session, etc.
 *
 * This helper generates the "internals" for a tRPC context. The API handler and RSC clients each
 * wrap this and provides the required context.
 *
 * @see https://trpc.io/docs/server/context
 */
export const createTRPCContext = async (opts: {
  headers: Headers
}): Promise<Context> => {
  try {
    const authSession = await auth()

    const decodedTokenData = decodedToken.toJSON(
      authSession?.accessToken as string
    )

    const session = { ...authSession, ...decodedTokenData }

    const citCOrganizationService = new CitCOrganizationService(
      session?.accessToken!
    )

    const citCPartnersService = new CitCPartnersService(session?.accessToken!)
    const [organizations, partners] = await Promise.all([
      citCOrganizationService.getOrganizationsByUser(session.uid!),
      citCPartnersService.getPartnersByUserAsync(session.uid!),
    ])
    const citCParametersService = new CitCParametersService(
      session?.accessToken!
    )

    const globalParameters =
      await citCParametersService.getGlobalParametersAsync()

    return {
      session,
      organizations,
      partners,
      globalParameters,
      citCParametersService,
      isCached: false,
      ...opts,
    }
  } catch (error) {
    appInsights.trackException({ exception: error as Error })
    throw error
  }
}

/**
 * 2. INITIALIZATION
 *
 * This is where the tRPC API is initialized, connecting the context and transformer. We also parse
 * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation
 * errors on the backend.
 */
const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    }
  },
})

/**
 * Create a server-side caller.
 *
 * @see https://trpc.io/docs/server/server-side-calls
 */
export const createCallerFactory = t.createCallerFactory

/**
 * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)
 *
 * These are the pieces you use to build your tRPC API. You should import these a lot in the
 * "/src/server/api/routers" directory.
 */

/**
 * This is how you create new routers and sub-routers in your tRPC API.
 *
 * @see https://trpc.io/docs/router
 */
export const createTRPCRouter = t.router

async function getOrgIdAndSelectionType(ctx: Context): Promise<{
  organizationId: string | null | undefined
  selectionType: SelectionType
}> {
  const cookieStore = await cookies()

  const organizationId = cookieStore.get('x-organization-id')?.value
  const selectionType = cookieStore.get('x-organization-selection-type')
    ?.value as SelectionType
  return { organizationId, selectionType }
}

async function getCacheKey(ctx: Context): Promise<string | null> {
  if (ctx.session?.accessToken) {
    const { organizationId, selectionType } =
      await getOrgIdAndSelectionType(ctx)
    return `${ctx.session?.accessToken}.${selectionType ?? SelectionType.Organization}.${organizationId ?? 'default'}.`
  }
  return null
}

const sessionCachingMiddleware = t.middleware(async ({ next, ctx }) => {
  const cacheKey = await getCacheKey(ctx)
  if (cacheKey) {
    const cachedSessionJson = await redisHelper.get(cacheKey)
    if (cachedSessionJson) {
      const cachedSession = JSON.parse(cachedSessionJson!) as CachedSession
      return next({
        ctx: {
          ...ctx,
          ...cachedSession,
          isCached: true,
        },
      })
    }
  }
  return next({
    ctx: {
      ...ctx,
      isCached: false,
    },
  })
})

const orgSelectionMiddleware = t.middleware(async ({ next, ctx }) => {
  if (ctx.isCached) return await next()

  let { organizationId, selectionType } = await getOrgIdAndSelectionType(ctx)

  // First try to get organization with "Default Organization" role
  const defaultOrg = ctx.organizationRoles?.find((role) =>
    role.roles?.includes(CitcUserRoles.DEFAULT_ORGANIZATION)
  )

  // If default org exists, use it, otherwise fall back to first org or current value
  const finalOrganizationId =
    organizationId ??
    defaultOrg?.organizationId ??
    ctx.organizationRoles?.[0]?.organizationId
  const finalSelectionType = selectionType ?? SelectionType.Organization

  if (!finalOrganizationId) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'No Organization Found',
      cause: {
        type: 'ORG_ID_NOT_SET',
      },
    })
  }

  // Set cookies if they weren't already set or if we're using defaults
  const cookieStore = await cookies()
  if (finalOrganizationId && finalOrganizationId !== organizationId) {
    cookieStore.set('x-organization-id', finalOrganizationId, {
      httpOnly: true,
      sameSite: 'lax',
      path: '/',
    })
  }

  if (finalSelectionType && finalSelectionType !== selectionType) {
    cookieStore.set('x-organization-selection-type', finalSelectionType, {
      httpOnly: true,
      sameSite: 'lax',
      path: '/',
    })
  }
  
  const subOrganizations = await getSessionSubOrganizations(
    finalSelectionType,
    ctx.session?.accessToken!,
    finalOrganizationId,
    ctx.organizationRoles!,
    ctx.session?.uid!
  )

  return next({
    ctx: {
      ...ctx,
      organizationId: finalOrganizationId,
      selectionType: finalSelectionType,
      subOrganizations,
    },
  })
})

const expansionManagerMiddleware = t.middleware(async ({ next, ctx }) => {
  if (ctx.isCached) return await next()
  let expansionConfiguration: ExpansionConfiguration[] = []

  try {
    expansionConfiguration = await getAllExpansionConfigs(
      new AzureTableStorageWrapper(StorageTables.ExpansionConfigs),
      ctx.organizationId!
    )
  } catch (error) {
    console.error(
      'Error reading expansion configuration for :',
      ctx.organizationId
    )
  }

  return next({
    ctx: {
      expansionConfiguration,
    },
  })
})

const sessionFinalizerMiddleware = t.middleware(async ({ next, ctx }) => {
  if (!ctx.isCached) {
    const cacheKey = await getCacheKey(ctx)
    if (cacheKey) {
      const cachableSession = ctx as unknown as CachedSession
      const cachedSession: CachedSession = {
        organizationId: cachableSession.organizationId!,
        selectionType: cachableSession.selectionType!,
        subOrganizations: cachableSession.subOrganizations,
        userDetails: cachableSession.userDetails,
        organizationRoles: cachableSession.organizationRoles,
        partnerRoles: cachableSession.partnerRoles,
        parameters: cachableSession.parameters,
        useSpecialEntityStructure: cachableSession.useSpecialEntityStructure,
        primaryMeasureTypes: cachableSession.primaryMeasureTypes,
        orgRoles: cachableSession.orgRoles,
        expansionConfiguration: cachableSession.expansionConfiguration,
      }
      await redisHelper.set(
        cacheKey,
        JSON.stringify(cachedSession),
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )
    }
  }

  return next({
    ctx,
  })
})

const userDetailsMiddleware = t.middleware(async ({ next, ctx }) => {
  if (ctx.isCached) return await next()

  const citcUserService = new CitCUsersService(ctx.session?.accessToken!)

  const userDetails = await citcUserService.getUserDetailsAsync(
    ctx.session?.uid!
  )

  const organizationRoles =
    userDetails?.organizationRoles
      ?.filter(
        (orgRoles) => orgRoles.applicationClientId === env.CITC_CLIENT_ID
      )
      .map((org) => ({
        organizationId: org.organizationId,
        roles: org.roles,
      })) ?? []

  const partnerRoles =
    userDetails?.partnerRoles?.map((partner) => ({
      organizationId: partner.partnerId,
      roles: partner.roles,
    })) ?? []

  return next({
    ctx: {
      ...ctx,
      userDetails,
      organizationRoles,
      partnerRoles,
    },
  })
})

const parametersMiddleware = t.middleware(async ({ next, ctx }) => {
  if (ctx.isCached) return await next()

  if (!ctx.organizationId) {
    throw new Error('No organizationId set in context')
  }

  const parameters =
    ctx.selectionType === SelectionType.Partner
      ? await ctx.citCParametersService.getPartnerParametersAsync(
        ctx.organizationId
      )
      : await ctx.citCParametersService.getOrganizationParametersAsync(
        ctx.organizationId
      )

  if (!parameters) {
    throw new Error('No parameters for organizationId')
  }

  // TODO: Can be it's own middleware ex. ParamCheckMiddleware
  const useSpecialEntityStructureConfig = parameters.find(
    (x) => x.key == 'platform:UseSpecialEntityStructure'
  )

  const useSpecialEntityStructure =
    useSpecialEntityStructureConfig?.value === 'true'

  return next({
    ctx: {
      ...ctx,
      parameters,
      useSpecialEntityStructure,
    },
  })
})

const dbConnectionMiddleware = t.middleware(async ({ next, ctx }) => {
  if (!ctx.organizationId) {
    throw new Error('No organizationId set in context')
  }

  const orgIds = [
    {
      id: ctx.organizationId,
      isPartner: ctx.selectionType === SelectionType.Partner,
      parameters: ctx.parameters ?? [],
    },
  ]

  // Get the prisma clients for the given orgId
  const db = getPrismaClientsByOrgId(orgIds, ctx.globalParameters)

  const prisma = db[ctx.organizationId]

  if (!prisma) {
    throw new Error('No prisma clients found')
  }

  function getParm(parm: string): string {
    return ctx.parameters?.find((x) => x.key === parm)?.value!
  }

  const snowflakeConfiguration: snowflake.ConnectionOptions = {
    account: getParm('platform:SnowFlakeAccount'),
    username: getParm('platform:SnowFlakeUser'),
    password: getParm('platform:SnowFlakePassword'),
    database: getParm('platform:SnowFlakeDatabase'),
    warehouse: getParm('platform:SnowFlakeWarehouse'),
    role: getParm('platform:SnowFlakeRole'),
  }

  appInsights.trackEvent({
    name: 'SnowflakeConnection',
    properties: {
      organizationId: ctx.organizationId,
      account: snowflakeConfiguration.account,
      username: snowflakeConfiguration.username,
      database: snowflakeConfiguration.database,
      warehouse: snowflakeConfiguration.warehouse,
      role: snowflakeConfiguration.role,
    },
  })

  const snowflakeRepositoryFactory = repositoryFactory(snowflakeConfiguration)
  const measureResultsService = new MeasureResultsService(
    ctx.session?.accessToken!,
    snowflakeRepositoryFactory.createMeasureRepository(),
    snowflakeRepositoryFactory.createActiveMeasureRepository(),
    snowflakeRepositoryFactory.createProvidersRepository(),
    snowflakeRepositoryFactory.createProcedureRepository(),
    snowflakeRepositoryFactory.createSisenseMeasureSummaryRepository(),
    snowflakeRepositoryFactory.createEntitiesRepository(),
    snowflakeRepositoryFactory.createSubmissionGroupRepository(),
    snowflakeRepositoryFactory.createFacilityRepository(),
    snowflakeRepositoryFactory.createOrganizationTypeRepository(),
    snowflakeRepositoryFactory.createAvailableMeasuresRepository(),
    new CitCParametersService(ctx.session?.accessToken!)
  )

  const entityOrganizationTypeService = new EntityOrganizationTypeService(
    snowflakeRepositoryFactory.createEntityOrganizationTypeRepository()
  )

  const acoPerformanceAnalysisService = new ACOPerformanceAnalysisService(
    snowflakeRepositoryFactory.createSisenseMeasureSummaryRepository()
  )

  const acoMeasureService = new ACOMeasureService(
    snowflakeRepositoryFactory.createACOMeasureRepository()
  )

  const measureSummaryService = new MeasureSummaryService(
    snowflakeRepositoryFactory.createMeasureSummaryRepository()
  )

  const mvpService = new MVPService(
    snowflakeRepositoryFactory.createMVPSnowflakeRepository()
  )

  return next({
    ctx: {
      prisma,
      snowflakeRepositoryFactory,
      measureResultsService,
      entityOrganizationTypeService,
      acoPerformanceAnalysisService,
      acoMeasureService,
      measureSummaryService,
      mvpService,
    },
  })
})

const orgRoleManagerMiddleware = t.middleware(async ({ next, ctx }) => {
  if (ctx.isCached) return await next()

  const orgRoles: OrganizationRole[] = [
    {
      organizationId: ctx.organizationId,
      roles: Array.isArray(ctx.session?.role)
        ? ctx.session?.role
        : [ctx.session?.role!],
    },
  ]

  return next({
    ctx: {
      ...ctx,
      orgRoles,
    },
  })
})

const primaryMeasureTypeMiddleware = t.middleware(async ({ next, ctx }) => {
  if (ctx.isCached) return await next()

  const primaryMeasureTypes =
    await ctx.measureResultsService!.getPrimaryMeasureTypes({
      organizationId: ctx.organizationId!,
      selectionType: ctx.selectionType!,
      isDevelopementEnv: process.env.NODE_ENV === 'development',
    })

  return next({
    ctx: {
      ...ctx,
      primaryMeasureTypes: prioritizeHospitalMeasures(primaryMeasureTypes),
    },
  })
})

/**
 * Middleware for timing procedure execution and adding an artificial delay in development.
 *
 * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating
 * network latency that would occur in production but not in local development.
 */
const timingMiddleware = t.middleware(async ({ next, path }) => {
  const start = Date.now()

  // if (t._config.isDev) {
  //   // artificial delay in dev
  //   const waitMs = Math.floor(Math.random() * 400) + 100
  //   await new Promise((resolve) => setTimeout(resolve, waitMs))
  // }

  const result = await next()

  const end = Date.now()
  console.log(`[TRPC] ${path} took ${end - start}ms to execute`)
  appInsights.trackEvent({
    name: `[TRPC] ${path}`,
    properties: {
      start,
      end,
      duration: end - start,
    },
  })

  return result
})

/**
 * Public (unauthenticated) procedure
 *
 * This is the base piece you use to build new queries and mutations on your tRPC API. It does not
 * guarantee that a user querying is authorized, but you can still access user session data if they
 * are logged in.
 */
export const publicProcedure = t.procedure.use(timingMiddleware)

/**
 * Protected (authenticated) procedure
 *
 * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies
 * the session is valid and guarantees `ctx.session.user` is not null.
 *
 * @see https://trpc.io/docs/procedures
 */
export const protectedProcedure = t.procedure
  .use(timingMiddleware)
  .use(sessionCachingMiddleware)
  .use(userDetailsMiddleware)
  .use(orgSelectionMiddleware)
  .use(expansionManagerMiddleware)
  .use(parametersMiddleware)
  .use(dbConnectionMiddleware)
  .use(orgRoleManagerMiddleware)
  .use(primaryMeasureTypeMiddleware)
  .use(({ ctx, next }) => {
    if (!ctx.session || !ctx.session.uid) {
      throw new TRPCError({ code: 'UNAUTHORIZED', message: 'No session found' })
    }

    if (ctx.session.error || ctx.session.status === 'INVALID') {
      // Handle invalid token state gracefully
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'Session expired',
        // Add custom metadata that your client can use
        cause: {
          type: 'SESSION_EXPIRED',
          shouldReauth: true,
        },
      })
    }

    return next({
      ctx: {
        // infers the `session` as non-nullable
        session: { ...ctx.session, user: ctx.session.user },
      },
    })
  })
  .use(sessionFinalizerMiddleware)

export const requirePermissionsMiddleware = (
  requiredRoles: CitcUserRoles[],
  ctx: Context,
  next: () => Promise<any>
) => {
  const currentOrgId = ctx.organizationId

  if (!currentOrgId) {
    console.log('No current organization ID found in context')
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'No current organization selected',
    })
  }

  const currentOrgRoles =
    ctx.organizationRoles?.find((role) => role.organizationId === currentOrgId)
      ?.roles || []
  const currentPartnerRoles =
    ctx.partnerRoles?.find((role) => role.organizationId === currentOrgId)
      ?.roles || []

  const allRoles = new Set([...currentOrgRoles, ...currentPartnerRoles])
  const hasRequiredRole = requiredRoles.some((requiredRole) =>
    allRoles.has(requiredRole)
  )

  if (!hasRequiredRole) {
    console.log(
      `Permission check failed. Required roles: ${requiredRoles.join(', ')}, User roles: ${Array.from(allRoles).join(', ')} for organization: ${currentOrgId}`
    )
    throw new TRPCError({
      code: 'FORBIDDEN',
      message:
        'You do not have the required permissions for this action in the current organization',
    })
  }

  return next()
}

const requirePermissionsMiddlewareFactory = (requiredRoles: CitcUserRoles[]) =>
  t.middleware(({ ctx, next }) =>
    requirePermissionsMiddleware(requiredRoles, ctx, next)
  )

export const permissionBasedProcedure = (roles: CitcUserRoles[]) =>
  protectedProcedure.use(requirePermissionsMiddlewareFactory(roles))
