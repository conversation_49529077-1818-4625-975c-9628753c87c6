import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import UserNotificationsService from '@/services/userNotifications'
import CitCUsersService from '@/services/citc/citCUsers'

export const notificationsRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(5),
        order: z.enum(['asc', 'desc']).default('desc'),
      })
    )
    .query(async ({ ctx, input }) => {
      const userNotificationsService = new UserNotificationsService()
      const citCUsersService = new CitCUsersService(ctx.session?.accessToken!)

      const [notifications, orgUsers] = await Promise.all([
        userNotificationsService.getNotifications(
          new AzureTableStorageWrapper(StorageTables.UserNotifications),
          ctx.session?.uid!,
          ctx.session?.accessToken!,
          input.limit,
          input.order,
          ctx.organizationId!,
          ctx.selectionType!
        ),
        citCUsersService.GetAllUsersByOrganizationAsync(ctx.organizationId!),
      ])

      return (
        notifications?.map((notification) => ({
          ...notification,
          senderName:
            orgUsers.find((user) => user.userId === notification.ownerId)
              ?.displayName ?? 'Unknown User',
        })) ?? null
      )
    }),

  getSentByMe: protectedProcedure.query(async ({ ctx }) => {
    const userNotificationsService = new UserNotificationsService()
    const citCUsersService = new CitCUsersService(ctx.session?.accessToken!)

    const [notifications, orgUsers] = await Promise.all([
      userNotificationsService.getSentNotifications(
        new AzureTableStorageWrapper(StorageTables.UserNotifications),
        ctx.session?.uid!
      ),
      citCUsersService.GetAllUsersByOrganizationAsync(ctx.organizationId!),
    ])

    return (
      notifications?.map((notification) => ({
        ...notification,
        receiverNames: notification.partitionKey
          ? (orgUsers.find((user) => user.userId === notification.partitionKey)
              ?.displayName ?? 'Unknown User')
          : 'Unknown User',
      })) ?? null
    )
  }),

  markAsRead: protectedProcedure
    .input(
      z.object({
        notificationId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.UserNotifications
      )

      await tableStorageWrapper.updateEntity({
        partitionKey: ctx.session?.uid!,
        rowKey: input.notificationId,
        readDateTime: new Date().toISOString(),
      })

      return { success: true }
    }),
})
