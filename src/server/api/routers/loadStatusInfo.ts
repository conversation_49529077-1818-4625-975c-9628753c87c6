import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { SelectionType } from '@/enums/selectionType'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import MeasureResultsService from '@/services/measureResults'
import { type LoadStatusInfo } from '@/types/loadStatusInfo'
import dayjs from 'dayjs'
import { redisHelper } from '@/lib/redis'
import { env } from '@/env'

export const loadStatusInfoRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        primaryMeasureType: z
          .nativeEnum(PrimaryMeasureTypeConstants)
          .default(PrimaryMeasureTypeConstants.HospitalMeasures),
      })
    )
    .query(async ({ ctx, input }) => {

      const loadProcedureOptions = {
        isPartner: ctx.selectionType === SelectionType.Partner,
        organizationId: ctx.organizationId!,
      }

      //add redis caching as the load time is driving me nuts
      const cacheKey = `${ctx.organizationId}-loadStatusInfo:${JSON.stringify(loadProcedureOptions)}`

      const cachedLoadStatusInfo = await redisHelper.get(cacheKey)

      if (cachedLoadStatusInfo) {
        console.log(
          `Cache Hit for load status info: ${cachedLoadStatusInfo.substring(0, Math.min(100, cachedLoadStatusInfo.length))}`
        )
        return JSON.parse(cachedLoadStatusInfo)
      }

      const measureResults =
        await ctx.measureResultsService!.getProcedureLoadStatus(loadProcedureOptions)

      let loadStatusInfo: LoadStatusInfo | null = null
      let showLoadStatus = false
      let areLoadsRunning = true

      if (measureResults?.jobName) {
        const startDate = dayjs(measureResults.startDate)
        const endDate = dayjs(measureResults.endDate)
        const timeDuration = `${endDate
          .diff(startDate, 'minute', true)
          .toFixed(2)}m`

        loadStatusInfo = {
          endDate: endDate.format('MM/DD/YYYY hh:mm A'),
          startDate: startDate.format('MM/DD/YYYY hh:mm A'),
          successStatus: measureResults.successStatus ? 'Success' : 'Failure',
          timeDuration,
          procedureRunningStatus: measureResults.procedureRunningStatus,
        }

        areLoadsRunning = false
        showLoadStatus = true
      }

      // Get additional load data status
      const encorELoadDataStatus =
        await ctx.measureResultsService!.getEncorELoadStatus(
          ctx.organizationId!,
          input.primaryMeasureType
        )

      const result = {
        loadStatusInfo,
        showLoadStatus,
        areLoadsRunning,
        encorELoadDataStatus,
      }

      await redisHelper.set(
        cacheKey,
        JSON.stringify(result),
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      ) //cache for an hour

      return result
    }),
})
