import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc'
import {
  OrganizationTypesRequest,
  EntityDescriptionRequest,
} from '@/types/reports/medisolvReport'

export const organizationTypeRouter = createTRPCRouter({
  getOrganizationTypes: protectedProcedure
    .input(
      z.object({
        intervalType: z.string(),
        startDate: z.date(),
        endDate: z.date(),
        measureIdentifiers: z.string().array(),
      }) as z.ZodType<OrganizationTypesRequest>
    )
    .query(async ({ ctx, input }) => {
      const organizationTypes =
        await ctx.entityOrganizationTypeService!.getOrganizationTypes(input)

      return {
        organizationTypes,
      }
    }),

  getHigherLevelOrganizationTypes: protectedProcedure
    .input(
      z.object({
        intervalType: z.string(),
        startDate: z.date(),
        endDate: z.date(),
        measureIdentifiers: z.string().array(),
        code: z.number(),
        universe: z.string(),
      }) as z.ZodType<OrganizationTypesRequest>
    )
    .query(async ({ ctx, input }) => {
      const organizationTypes =
        await ctx.entityOrganizationTypeService!.getHigherLevelOrganizationTypes(
          input
        )

      return {
        organizationTypes,
      }
    }),

  getEntityDescription: protectedProcedure
    .input(
      z.object({
        intervalType: z.string(),
        startDate: z.date(),
        endDate: z.date(),
        measureIdentifiers: z.string().array(),
        universe: z.string().optional(),
      }) as z.ZodType<EntityDescriptionRequest>
    )
    .query(async ({ ctx, input }) => {
      const entityDescriptions =
        await ctx.entityOrganizationTypeService!.getEntityDescription(input)

      return {
        entityDescriptions,
      }
    }),
})
