import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, permissionBasedProcedure } from '../trpc'
import { TRPCError } from '@trpc/server'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { SelectionType } from '@/enums/selectionType'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardsService } from '@/services/dashboards'
import { StorageTables } from '@/enums/storageTables'
import { Report } from '@/types/reports/medisolvReport'
import AuditLogService from '@/services/admin/AuditLog/AuditLogService'
import { ApiAuditLogData, AuditLog } from '@/types/auditLog'
import { env } from '@/env'
import CitCUsersService from '@/services/citc/citCUsers'
import { getDropDownValues } from '@/services/admin/expansionConfiguration/getDropDownValues'
import { upsertExpansionConfigurations } from '@/services/expansionConfigs/upsertExpansionConfigs'
import { EntityLevelConstants } from '@/types/expansionConfiguration'
import { standardReports } from '@/data/reports'
import { CitcUserRoles } from '@/shared/roles'
import { ExtensionLevels } from "@/enums/extensionLevels"
import { getAllDataLoadStatus } from '@/services/measures/getAllDataLoadStatus'

function getDashboardService(ctx: any) {
  // TODO: Enhancement* Move into trpc context
  const dashboardTableWrapper = new AzureTableStorageWrapper(
    StorageTables.Dashboards
  )

  // TODO: Enhancement* Move into trpc context
  const dashboardDetailsTableWrapper = new AzureTableStorageWrapper(
    StorageTables.DashboardDetails
  )

  return new DashboardsService(
    dashboardTableWrapper,
    dashboardDetailsTableWrapper,
    ctx.organizationId!
  )
}

const reportAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (!ctx.session?.role?.includes('Report Administrator')) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'You must be a Report Administrator to access this resource',
    })
  }
  return next()
})

// Convert time range to ISO 8601 duration format
function convertTimeRangeToTimespan(timeRange: string): string {
  switch (timeRange) {
    case '1h': return 'PT1H'
    case '24h': return 'P1D'
    case '7d': return 'P7D'
    case '30d': return 'P30D'
    default: return 'P1D'
  }
}

// Mock data generator for Application Insights queries
function generateMockAppInsightsData(query: string, timeRange?: string) {
  const now = new Date()
  const hoursBack = timeRange === '1h' ? 1 : timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 720

  // Detect query type and generate appropriate mock data
  if (query.toLowerCase().includes('requests')) {
    return {
      tables: [{
        name: 'PrimaryResult',
        columns: [
          { name: 'timestamp', type: 'datetime' },
          { name: 'name', type: 'string' },
          { name: 'url', type: 'string' },
          { name: 'resultCode', type: 'string' },
          { name: 'duration', type: 'real' },
          { name: 'success', type: 'bool' },
          { name: 'customDimensions', type: 'dynamic' },
          { name: 'operation_Id', type: 'string' }
        ],
        rows: Array.from({ length: 20 }, (_, i) => [
          new Date(now.getTime() - (i * 3600000)).toISOString(),
          `GET /api/endpoint${i % 5}`,
          `https://localhost:3000/api/endpoint${i % 5}`,
          i % 10 === 0 ? '500' : '200',
          Math.random() * 1000 + 50,
          i % 10 !== 0,
          {
            userId: `user-${i % 5}`,
            sessionId: `session-${Math.random().toString(36).substr(2, 9)}`,
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ipAddress: `192.168.1.${100 + (i % 50)}`,
            requestHeaders: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer token...',
              'X-Forwarded-For': `10.0.0.${i % 255}`
            }
          },
          `operation-${Math.random().toString(36).substr(2, 9)}`
        ])
      }]
    }
  } else if (query.toLowerCase().includes('exceptions')) {
    return {
      tables: [{
        name: 'PrimaryResult',
        columns: [
          { name: 'timestamp', type: 'datetime' },
          { name: 'type', type: 'string' },
          { name: 'outerMessage', type: 'string' },
          { name: 'method', type: 'string' },
          { name: 'assembly', type: 'string' }
        ],
        rows: Array.from({ length: 10 }, (_, i) => [
          new Date(now.getTime() - (i * 7200000)).toISOString(),
          'System.Exception',
          `Error message ${i + 1}: Something went wrong`,
          `Method${i % 3}`,
          'MyApp.dll'
        ])
      }]
    }
  } else if (query.toLowerCase().includes('traces')) {
    return {
      tables: [{
        name: 'PrimaryResult',
        columns: [
          { name: 'timestamp', type: 'datetime' },
          { name: 'message', type: 'string' },
          { name: 'severityLevel', type: 'int' },
          { name: 'operation_Name', type: 'string' },
          { name: 'customDimensions', type: 'dynamic' },
          { name: 'customMeasurements', type: 'dynamic' }
        ],
        rows: Array.from({ length: 25 }, (_, i) => [
          new Date(now.getTime() - (i * 1800000)).toISOString(),
          `Trace message ${i + 1}: Application event occurred with detailed context information that might be quite long and require expansion to view properly`,
          Math.floor(Math.random() * 4) + 1,
          `Operation${i % 4}`,
          {
            component: `Component${i % 3}`,
            method: `method${i % 5}`,
            userId: `user-${i % 10}`,
            correlationId: `corr-${Math.random().toString(36).substr(2, 9)}`,
            environment: 'production',
            version: '1.2.3',
            metadata: {
              source: 'application-logs',
              category: 'business-logic',
              tags: ['important', 'user-action', 'audit']
            }
          },
          {
            executionTime: Math.random() * 1000,
            memoryUsage: Math.random() * 100,
            cpuUsage: Math.random() * 50,
            requestCount: Math.floor(Math.random() * 100)
          }
        ])
      }]
    }
  } else {
    // Generic mock data with complex objects
    return {
      tables: [{
        name: 'PrimaryResult',
        columns: [
          { name: 'timestamp', type: 'datetime' },
          { name: 'data', type: 'string' },
          { name: 'count', type: 'long' },
          { name: 'customDimensions', type: 'dynamic' },
          { name: 'properties', type: 'dynamic' }
        ],
        rows: Array.from({ length: 15 }, (_, i) => [
          new Date(now.getTime() - (i * 3600000)).toISOString(),
          `Sample data ${i + 1} with a very long description that should be expandable because it exceeds the 100 character limit and contains detailed information about the event`,
          Math.floor(Math.random() * 100) + 1,
          {
            eventType: `Event${i % 3}`,
            source: 'application',
            level: i % 4 + 1,
            details: {
              component: `Component${i % 2}`,
              action: `Action${i % 3}`,
              metadata: {
                version: '1.0.0',
                environment: 'production',
                tags: ['important', 'business-logic']
              }
            }
          },
          {
            userId: `user-${i % 10}`,
            sessionId: `session-${Math.random().toString(36).substr(2, 9)}`,
            browser: {
              name: 'Chrome',
              version: '91.0.4472.124',
              platform: 'Windows'
            },
            location: {
              country: 'US',
              region: 'CA',
              city: 'San Francisco'
            }
          }
        ])
      }]
    }
  }
}

export const adminRouter = createTRPCRouter({
  //#region Manage Expansion Configurations
  saveLevelConfigurations: protectedProcedure
    .input(
      z.object({
        configs: z.array(
          z.object({
            rowKey: z.string(),
            level: z.nativeEnum(ExtensionLevels),
            selectedLevel: z.nativeEnum(EntityLevelConstants),
            measureType: z.nativeEnum(PrimaryMeasureTypeConstants),
          })
        ),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.ExpansionConfigs
      )

      await Promise.all(
        input.configs.map((x) =>
          upsertExpansionConfigurations(tableStorageWrapper, {
            partitionKey: ctx.organizationId!,
            level: x.level,
            rowKey: x.rowKey,
            label: `${x.selectedLevel} Entities`,
            measureType: x.measureType,
            selectedLevel: x.selectedLevel,
            entries: '',
          })
        )
      )

      return {
        organizationId: ctx.organizationId,
        config: input.configs,
      }
    }),

  getLevelConfigurations: protectedProcedure.query(async ({ ctx }) => {
    return getDropDownValues(
      new AzureTableStorageWrapper(StorageTables.ExpansionConfigs),
      ctx.measureResultsService!,
      ctx.selectionType == SelectionType.Partner,
      ctx.organizationId!,
      ctx.primaryMeasureTypes
    )
  }),

  addDashboard: protectedProcedure
    .input(
      z.object({
        name: z.string(),
        description: z.string(),
        configuration: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const dashboardService = getDashboardService(ctx)
      return await dashboardService.addDashboard(input)
    }),

  shareDashboard: protectedProcedure
    .input(
      z.object({
        dashboardId: z.string(),
        userId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const dashboardService = getDashboardService(ctx)
      return await dashboardService.shareDashboard(input)
    }),

  getAllDashboards: protectedProcedure.query(async ({ ctx }) => {
    const dashboardService = getDashboardService(ctx)
    return await dashboardService.getAllDashboards()
  }),

  getDashboardWithSharings: protectedProcedure.query(async ({ ctx }) => {
    const dashboardService = getDashboardService(ctx)
    return await dashboardService.getAllDashboardsWithSharingDetails()
  }),

  getStandardReports: permissionBasedProcedure([CitcUserRoles.REPORT_ADMINISTRATOR]).query(async ({ ctx }) => {
    const tableStorageWrapper = new AzureTableStorageWrapper(
      StorageTables.Reports
    )

    const clientReports = await tableStorageWrapper.queryEntities<Report>(
      tableStorageWrapper.generateODataQuery({
        PartitionKey: ctx.organizationId,
        isCustom: false,
      })
    )

    if (clientReports.length) {
      return clientReports
    } else {
      const standardReportsList = standardReports
        .filter((x) => x.isActive)
        .map((x) => ({
          ...x,
          rowKey: crypto.randomUUID(),
          partitionKey: ctx.organizationId!,
        }))
      await Promise.all(
        standardReportsList.map((x) =>
          tableStorageWrapper.insertEntity<Report>(x)
        )
      )

      return standardReportsList
    }
  }),

  getAuditLogs: protectedProcedure
    .input(
      z.object({
        startTimeMMDDYYYY: z.string(),
        endTimeMMDDYYYY: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.session?.accessToken) {
        throw new Error('Unauthorized: Access token is missing.')
      }

      try {
        const auditLogs = new AuditLogService(ctx.session.accessToken)

        let apiAuditLogsData: ApiAuditLogData[] = [] as ApiAuditLogData[]

        apiAuditLogsData = await auditLogs.getAuditLogs(
          ctx.organizationId!,
          env.CITC_CLIENT_ID.toString(),
          input.startTimeMMDDYYYY,
          input.endTimeMMDDYYYY
        )

        let mapApiAuditLogToAuditLog = (
          apiLogs: ApiAuditLogData[]
        ): AuditLog[] => {
          return apiLogs.map((log) => ({
            userName: log.userEmail,
            userEmail: log.userEmail,
            organization: env.CITC_CLIENT_ID.toString(),
            url: log.url,
            auditDateTime: new Date(log.logUTCTime).toLocaleString(),
          }))
        }

        let data = mapApiAuditLogToAuditLog(apiAuditLogsData)

        const getUrl = (url: string): string => {
          if (!url) return '/'

          const urlSegments = url.split('/')
          const lastSegment = urlSegments[urlSegments.length - 1] || ''
          const secondLastSegment =
            urlSegments.length > 1 ? urlSegments[urlSegments.length - 2] : ''

          if (lastSegment.indexOf('?') === 0) {
            return `/${secondLastSegment}`
          } else if (lastSegment.indexOf('?') > 0) {
            return `/${lastSegment.substring(0, lastSegment.indexOf('?'))}`
          } else {
            return `/${lastSegment}`
          }
        }
        //Retrieving all users
        const citCUsersService = new CitCUsersService(ctx.session.accessToken!)
        let users = await citCUsersService.GetAllUsersByOrganizationAsync(
          ctx.organizationId!
        )
        let auditLogsGridData = data.map((log) => {
          const user = users.find(
            (user) =>
              user.emailAddress?.toLowerCase() == log.userEmail.toLowerCase()
          )
          return {
            ...log, //keep the existing item
            userName: user?.displayName ?? log.userEmail,
            url: getUrl(log.url),
          }
        })
        return auditLogsGridData ?? [] //Ensure a valid return value
      } catch (error) {
        console.error('Error fetching audit logs: ', error)
        throw new Error('Failed to retrieve audit logs.')
      }
    }),

  postAuditLogs: protectedProcedure
    .input(
      z.object({
        url: z.string(),
        userAgent: z.string(),
        pageTitle: z.string(),
        actionType: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.session?.accessToken) {
        throw new Error('Unauthorized: Access token is missing.')
      }

      try {

        const auditLogService = new AuditLogService(ctx.session.accessToken)

        const auditLogData = {
          ...input,
          organizationId: ctx.organizationId!,
          userEmail: ctx.userDetails?.emailAddress || '',
          userId: ctx.userDetails?.userId || '',
          applicationId: env.CITC_CLIENT_ID.toString(),
          currentRoles: JSON.stringify(ctx.session.role),
          productId: env.PRODUCT_ID?.toString() || '',
        }

        const success = await auditLogService.postAuditLogs(auditLogData)
        return { success }
      } catch (error) {
        console.error('Error posting audit log: ', error)
        throw new Error('Failed to post audit log.')
      }
    }),

  getAllDataLoadStatus: protectedProcedure
    .input(
      z.object({
        loaddate: z.string(),
        //primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
      })
    )
    .query(async ({ ctx, input }) => {

      //console.log(ctx.session);
      return await getAllDataLoadStatus(ctx, input,
        new AzureTableStorageWrapper(StorageTables.PlatformJobStatus),
        ctx.organizationId!,
        ctx.primaryMeasureTypes)


    }),

  executeAppInsightsQuery: protectedProcedure
    .input(
      z.object({
        query: z.string(),
        timeRange: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the Application ID from the connection string
        const connectionString = process.env.NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING
        if (!connectionString) {
          throw new Error('Application Insights connection string not configured')
        }

        // Extract Application ID from connection string
        const appIdMatch = connectionString.match(/ApplicationId=([^;]+)/)
        if (!appIdMatch) {
          throw new Error('Could not extract Application ID from connection string')
        }

        const applicationId = appIdMatch[1]

        console.log(`Executing Application Insights query for app ${applicationId}:`, input.query)

        try {
          // Use Azure Monitor Query client for real data
          const { DefaultAzureCredential } = await import('@azure/identity')
          const { LogsQueryClient } = await import('@azure/monitor-query')

          const credential = new DefaultAzureCredential()
          const logsClient = new LogsQueryClient(credential)

          // Use ISO 8601 duration format for Azure SDK
          const timespan = input.timeRange ? convertTimeRangeToTimespan(input.timeRange) : 'P1D'

          const result = await logsClient.queryWorkspace(
            applicationId,
            input.query,
            { timespan }
          )

          return {
            tables: result.tables.map(table => ({
              name: table.name,
              columns: table.columns.map(col => ({
                name: col.name,
                type: col.type
              })),
              rows: table.rows
            }))
          }
        } catch (azureError) {
          console.warn('Azure SDK failed, falling back to REST API:', azureError)

          // Fallback to REST API approach
          const apiKey = process.env.APPINSIGHTS_API_KEY
          if (!apiKey) {
            console.warn('No API key found, using mock data')
            return generateMockAppInsightsData(input.query, input.timeRange)
          }

          try {
            // Calculate timespan for REST API (ISO 8601 duration format)
            const timespan = input.timeRange ? convertTimeRangeToTimespan(input.timeRange) : 'P1D'

            const response = await fetch(`https://api.applicationinsights.io/v1/apps/${applicationId}/query`, {
              method: 'POST',
              headers: {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                query: input.query,
                timespan: timespan
              })
            })

            if (!response.ok) {
              const errorText = await response.text()
              console.error(`Application Insights API error: ${response.status} ${response.statusText}`, errorText)
              throw new Error(`Application Insights API error: ${response.status} ${response.statusText}`)
            }

            const data = await response.json()
            console.log('Successfully retrieved data from Application Insights REST API')
            return data
          } catch (restError) {
            console.error('REST API also failed, using mock data:', restError)
            return generateMockAppInsightsData(input.query, input.timeRange)
          }
        }
      } catch (error) {
        console.error('Application Insights query error:', error)
        throw new Error(`Failed to execute query: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }),
})
