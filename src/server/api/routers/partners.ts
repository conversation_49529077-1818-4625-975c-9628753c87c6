import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc'
import CitCPartnersService from '@/services/citc/citCPartners'

export const partnersRouter = createTRPCRouter({
  getByUser: protectedProcedure.query(async ({ ctx }) => {
    if (ctx.partners.length > 0) return ctx.partners

    const citCPartnersService = new CitCPartnersService(
      ctx.session!.accessToken!
    )

    const partners = await citCPartnersService.getPartnersByUserAsync(
      ctx.session!.uid!
    )

    return partners ?? null
  }),
})
