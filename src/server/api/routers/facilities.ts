import { Facility } from '@/types/facility'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { z } from 'zod'
import { getFacilitiesByCCN } from '@/services/facilities/getFacilitiesByCCN'
import { ScorecardView } from '@/enums/scorecardView'
import dayjs from 'dayjs'
import { SelectionType } from '@/enums/selectionType'
import appInsights from '@/lib/applicationInsights'
import { tryCache } from '@/lib/redis'
import { env } from '@/env'

export const facilitiesRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
      })
    )
    .query(async ({ ctx, input }) => {
      let facilities: Facility[] = []
      if (
        ctx.useSpecialEntityStructure &&
        input.primaryMeasureType ===
        PrimaryMeasureTypeConstants.HospitalMeasures
      ) {

        const cacheKey = `get-facilities-${ctx.organizationId}`

        facilities = await tryCache(
          cacheKey,
          async () => {
            return await ctx.measureResultsService!.getFacilityByOrganization(
              ctx.organizationId!
            )
          },
          Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
        )

        const facilityGroups = facilities.reduce<Record<string, Facility[]>>((groups, item) => {
          if (!groups[item.facilityCode!]) {
            groups[item.facilityCode!] = []
          }
          groups[item.facilityCode!]?.push(item)
          return groups
        }, {})

        facilities = Object.entries(facilityGroups).map(([key, group]) => {
          return group[0]!
        })
      }

      return {
        useSpecialEntityStructure:
          ctx.useSpecialEntityStructure &&
          input.primaryMeasureType ===
          PrimaryMeasureTypeConstants.HospitalMeasures,
        facilities,
      }
    }),

  getFacilitiesByCCN: protectedProcedure
    .input(
      z.object({
        hospitalId: z.string(),
        scorecardView: z.nativeEnum(ScorecardView),
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
        measureIdentifier: z.string(),
        isIAPIMeasure: z.boolean(),
      })
    )
    .query(async ({ ctx, input }) => {

      if (!ctx.organizationId || !ctx.selectionType) {
        const message = !ctx.organizationId
          ? 'No organizationId provided'
          : !ctx.selectionType
            ? 'No selectionType provided'
            : 'unnkown issue'

        appInsights.trackException({
          exception: new Error(message),
          properties: {
            ...ctx,
          },
        })

        return []
      }

      const cacheKey = `get-facilities-by-ccn-${ctx.organizationId}-${ctx.selectionType}-${input.hospitalId}-${input.scorecardView}-${input.startDate}-${input.endDate}-${input.measureIdentifier}-${input.isIAPIMeasure}`

      const result = tryCache(
        cacheKey,
        async () => {
          return await getFacilitiesByCCN(
            ctx.session?.accessToken!,
            ctx.measureResultsService!,
            input.hospitalId,
            input.scorecardView,
            ctx.organizationId!,
            dayjs.utc(input.startDate).startOf('month'),
            dayjs.utc(input.endDate).endOf('month'),
            input.measureIdentifier,
            ctx.selectionType == SelectionType.Partner,
            input.isIAPIMeasure
          )
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result
    }),
})
