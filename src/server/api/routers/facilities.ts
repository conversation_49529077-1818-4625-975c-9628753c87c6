import { Facility } from '@/types/facility'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { z } from 'zod'

export const facilitiesRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
      })
    )
    .query(async ({ ctx, input }) => {
      let facilities: Facility[] = []

      if (
        ctx.useSpecialEntityStructure &&
        input.primaryMeasureType ===
          PrimaryMeasureTypeConstants.HospitalMeasures
      ) {
        facilities = await ctx.measureResultsService!.getFacilityByOrganization(
          ctx.organizationId!
        )
      }

      return {
        useSpecialEntityStructure:
          ctx.useSpecialEntityStructure &&
          input.primaryMeasureType ===
            PrimaryMeasureTypeConstants.HospitalMeasures,
        facilities,
      }
    }),
})
