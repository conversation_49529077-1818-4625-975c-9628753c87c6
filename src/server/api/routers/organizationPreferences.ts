import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import { OrganizationPreference } from '@/types/organizationPreference'
import { TRPCError } from '@trpc/server'
import { getAllOrganizationPreferences } from '@/services/organizationPreferences/getAlllOrganizationPreferences'
import { getOrganizationPreferences } from '@/services/organizationPreferences/getOrganizationPreferences'
import { OrganizationPreferenceKey } from '@/enums/organizationPreferenceKey'
import { upsertOrganizationPreference } from '@/services/organizationPreferences/upsertOrganizationPreference'
import { deleteOrganizationPreference } from '@/services/organizationPreferences/deleteOrganizationPreference'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { prioritizeHospitalMeasures } from '@/lib/prioritizeHospitalMeasures'

export const organizationPreferencesRouter = createTRPCRouter({
  getPreference: protectedProcedure
    .input(
      z.object({
        key: z.nativeEnum(OrganizationPreferenceKey),
      })
    )
    .query(async ({ ctx, input }) => {
      const orgPreference = await getOrganizationPreferences(
        new AzureTableStorageWrapper(StorageTables.OrganizationPreferences), // TODO: Move into trpc context
        ctx.organizationId!,
        input.key,
        {
          organizationId: ctx.organizationId!,
          selectionType: ctx.selectionType!,
        }
      )

      const primaryMeasureTypes = orgPreference.map(
        (x) => x.value
      ) as PrimaryMeasureTypeConstants[]

      return prioritizeHospitalMeasures(primaryMeasureTypes)
    }),
  getAllPreferences: protectedProcedure.query(async ({ ctx }) => {
    const orgPreferences = await getAllOrganizationPreferences(
      new AzureTableStorageWrapper(StorageTables.OrganizationPreferences),
      ctx.organizationId!,
      {
        organizationId: ctx.organizationId!,
        selectionType: ctx.selectionType!,
      }
    )

    return orgPreferences
  }),
  UpsertPreference: protectedProcedure
    .input(z.object({ key: z.string(), value: z.string(), rowKey: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const preference: OrganizationPreference = {
        partitionKey: ctx.organizationId!,
        rowKey: input.rowKey,
        key: input.key,
        value: input.value,
      }

      const upsertedView = await upsertOrganizationPreference(
        new AzureTableStorageWrapper(StorageTables.OrganizationPreferences),
        preference
      )

      return upsertedView
    }),
  deletePreference: protectedProcedure
    .input(
      z.object({
        rowKey: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        await deleteOrganizationPreference(
          new AzureTableStorageWrapper(StorageTables.OrganizationPreferences),
          ctx.organizationId!,
          input.rowKey
        )

        return {
          success: true,
          message: 'Preference deleted successfully',
        }
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete preference',
        })
      }
    }),
})
