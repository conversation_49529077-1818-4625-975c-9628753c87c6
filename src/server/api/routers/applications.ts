import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc'
import CitCOrganizationService from '@/services/citc/citCOrganizations'
import CitCUsersService from '@/services/citc/citCUsers'
import { type App } from '@/types/app'
import { type OrganizationRole } from '@/types/organizationRole'

export const applicationsRouter = createTRPCRouter({
  getAll: protectedProcedure.query(async ({ ctx }) => {
    const citCOrganizationService = new CitCOrganizationService(
      ctx.session?.accessToken!
    )
    const organizationApplications =
      await citCOrganizationService.getOrganizationsApplications(
        ctx.organizationId!
      )

    const citcUserService = new CitCUsersService(ctx.session?.accessToken!)
    const userInfo = await citcUserService.getUserDetailsAsync(
      ctx.session?.uid!
    )

    const filterApplications = (
      organizationRoles: OrganizationRole[],
      organizationApplications: App[]
    ) => {
      return organizationApplications.filter(
        (app) =>
          organizationRoles.some((role) => role.applicationId === app.appId) &&
          !app.excludeFrom9Dots
      )
    }

    return filterApplications(
      userInfo?.organizationRoles ?? [],
      organizationApplications ?? []
    )
  }),
})
