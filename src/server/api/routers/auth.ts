import { createTRPCRouter, protectedProcedure } from '../trpc'
import { env } from '@/env'
import { auth, signOut, clearUserTokenCache, tokenCache} from '@/auth'
import { decodeToken } from '@/lib/decodeAccessToken'
import CitCWellKnownConfig from '@/services/citc/citCWellKnownConfig'
import { redisHelper } from '@/lib/redis'
import { cookies } from 'next/headers'

export const authRouter = createTRPCRouter({
  getSession: protectedProcedure.query(async ({ ctx }) => {
    return {
      uid: ctx.session?.uid,
      user: ctx.session?.user,
      expires: ctx.session?.expires,
    }
  }),
  logout: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      const citcConfig = CitCWellKnownConfig.getInstance()

      // Get the current session
      const session = await auth()
      const idToken = session?.idToken
      const accessToken = session?.accessToken
      let refreshToken

      // Clear the token cache for this user
      if (accessToken) {
        const decodedToken = decodeToken(accessToken)
        if (decodedToken && decodedToken.sub) {
          const cachedTokens = await tokenCache.get(decodedToken.sub)
          refreshToken = cachedTokens?.refresh_token
          // If we found a refresh token, revoke it
          if (refreshToken) {
            await citcConfig.revokeToken(refreshToken, 'refresh_token')
          }
          clearUserTokenCache(decodedToken.sub)
        }
      }

      // Clear the cache
      const cacheKeyPattern = `${accessToken}.*`
      await redisHelper.clearByPattern(cacheKeyPattern)
      
      // Revoke access token
      if (accessToken) {
        await citcConfig.revokeToken(accessToken, 'access_token')
      }

      await signOut({ redirect: false })
        
      const cookieStore = await cookies()

      const allCookies = cookieStore.getAll()
      
      allCookies.forEach(async cookie => {
        const name = cookie.name
        
        // Clear base session tokens
        if (name === 'authjs.session-token' || name === '__Secure-authjs.session-token') {
          cookieStore.delete(name)
        }
        
        // Clear chunked session tokens (authjs.session-token.0, authjs.session-token.1, etc.)
        if (name.startsWith('authjs.session-token.') || name.startsWith('__Secure-authjs.session-token.')) {
          cookieStore.delete(name)
        }
        
        // Clear other Auth.js cookies
        if (name.startsWith('authjs.') || name.startsWith('__Secure-authjs.')) {
          cookieStore.delete(name)
        }

        if (name.startsWith('x-organization') || name.startsWith('__Secure-x-organization')) {
          cookieStore.delete(name)
        }
      })
      // Get the end session URL
      const logoutUrl = await citcConfig.getEndSessionUrl(idToken)
      // Return the redirect URL for client-side navigation
      return { redirectUrl: logoutUrl }
    } catch (error) {
      console.error('Error in logout function:', error)
      // Return the fallback redirect URL
      return { redirectUrl: env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT }
    }
  }),
})
