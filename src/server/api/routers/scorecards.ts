import { createTRPCRouter, protectedProcedure } from '../trpc'
import { z } from 'zod'
import { SelectionType } from '@/enums/selectionType'
import { getScorecardByHospital } from '@/services/scorecard/getScorecardByHospital'
import { ScorecardView } from '@/enums/scorecardView'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import dayjs from 'dayjs'
import { SubOrganization } from '@/types/subOrganization'
import { redisHelper } from '@/lib/redis'
import {
  HospitalSummary,
  ScorecardResult,
  ScorecardResultByHospital,
} from '@/types/scorecards/scorecards'
import MeasureResultsService from '@/services/measureResults'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import CitCOrganizationService from '@/services/citCOrganizations'
import { env } from '@/env'
import { getScorecardByMeasure } from '@/services/scorecard/getScorecardByMeasure'
import { getECScorecardByMeasure } from '@/services/scorecard/getScorecardsByECMeasures'
import { getScorecardsBySubmissionGroups } from '@/services/scorecard/getScorecardsBySubmissionGroups'
import { Organization } from '@/types/organization'
import { OrganizationRole } from '@/types/organizationRole'
import { hasLimitedAccessForOrganization } from '@/lib/hasLimitedAccessForOrganization'
import { getMeasuresForPerformanceGoals } from '@/services/performanceGoals/getMeasuresForPerformanceGoals'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { PerformanceGoal } from '@/types/scorecards/performanceGoal'
import { PerformanceGoalMeasure } from '@/types/scorecards/performanceGoalMeasure'
import { upsertPerformanceGoals } from '@/services/performanceGoals/upsertPerformanceGoals'
import { StatusResponse } from '@/types/statusResponse'
import { setPerformanceGoals } from '@/services/performanceGoals/setPerformanceGoals'
import { StorageTables } from '@/enums/storageTables'
import { MigrationConfig } from '@/types/migrationConfig'
import { migrateData } from '@/services/migration/migrateData'
import { ADMPrismaClient } from '@/server/db'
import appInsights from '@/lib/applicationInsights'

export const scorecardsRouter = createTRPCRouter({
  getScorecardByMeasureResults: protectedProcedure
    .input(
      z.object({
        hospitalId: z.string().optional(),
        scorecardView: z.nativeEnum(ScorecardView),
        hideEmptyIndicators: z.boolean(),
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
        measureIdentifiers: z.string().array(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      let result: ScorecardResult[] = []

      const cacheKey =
        `${ctx.organizationId}-${input.scorecardView}-${input.primaryMeasureType}` +
        `-${input.startDate}-${input.endDate}-${input.hospitalId ?? ''}`

      const cachedResult = await redisHelper.get(cacheKey)
      if (cachedResult) {
        result = JSON.parse(cachedResult) as ScorecardResult[]
      } else {
        if (
          input.primaryMeasureType ===
          PrimaryMeasureTypeConstants.HospitalMeasures
        ) {
          result = await getScorecardByMeasure(
            ctx.measureResultsService!,
            input.scorecardView,
            ctx.organizationId!,
            ctx.selectionType == SelectionType.Partner,
            dayjs.utc(input.startDate).startOf('month'),
            dayjs.utc(input.endDate).endOf('month'),
            input.hospitalId ? [input.hospitalId] : ['*'],
            input.primaryMeasureType,
            ctx.session?.accessToken!
          )
        } else {
          result = await getECScorecardByMeasure(
            ctx.measureResultsService!,
            input.scorecardView,
            ctx.organizationId!,
            ctx.selectionType == SelectionType.Partner,
            dayjs.utc(input.startDate).startOf('month'),
            dayjs.utc(input.endDate).endOf('month'),
            input.hospitalId ? [input.hospitalId] : ['*'],
            input.primaryMeasureType,
            ctx.session?.accessToken!
          )
        }
      }

      result = await setPerformanceGoals(
        ctx.organizationId!,
        result,
        input.scorecardView
      )

      if (
        input.measureIdentifiers.length > 0 &&
        !input.measureIdentifiers.includes('*')
      )
        result = result.filter((x) =>
          input.measureIdentifiers.includes(x.measureIdentifier!)
        )

      if (input.hideEmptyIndicators)
        result = result.filter((x) => !x.isEmptyIndicator)

      await redisHelper.set(
        cacheKey,
        JSON.stringify(result),
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result ?? null
    }),

  getScorecardByHospitalResults: protectedProcedure
    .input(
      z.object({
        measureIdentifier: z.string().optional(),
        scorecardView: z.nativeEnum(ScorecardView),
        hideEmptyIndicators: z.boolean(),
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
        scorecardDisplayType: z.string(),
        subOrganizationIds: z.string().array().optional(),
        submissionGroupIds: z.string().array().optional(),
        measureIdentifiers: z.string().array(),
        selectedOrganizations: z.string().array().optional(),
        facilities: z.string().array().optional(),
        organizations: z.string().array().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      let cacheKey = ''
      if (
        input.primaryMeasureType ===
        PrimaryMeasureTypeConstants.HospitalMeasures
      )
        cacheKey =
          `${ctx.organizationId}-${input.scorecardDisplayType}-${input.subOrganizationIds?.join(',')}-${input.scorecardView}-${input.measureIdentifiers.join(',')}` +
          `-${input.facilities?.join(',')}-${input.startDate}-${input.endDate}-${input.measureIdentifier ? input.measureIdentifier : ''}`
      else
        cacheKey =
          `${ctx.organizationId}-${input.scorecardDisplayType}-${input.submissionGroupIds?.join(',')}-${input.scorecardView}-${input.measureIdentifiers.join(',')}` +
          `-${input.selectedOrganizations?.join(',')}-${input.startDate}-${input.endDate}-${input.measureIdentifier ? input.measureIdentifier : ''}`

      let cachedResult = await redisHelper.get(cacheKey)

      if (cachedResult) {
        appInsights.trackEvent({
          name: 'result of getScorecardByHospitalResults.cachedResult',
          properties: {
            cachedResult,
          },
        })

        if (input.measureIdentifier) {
          let hospitalResults = JSON.parse(
            cachedResult
          ) as ScorecardResultByHospital[]

          if (input.hideEmptyIndicators)
            hospitalResults = hospitalResults.filter((x) => !x.isEmptyIndicator)

          hospitalResults = await setPerformanceGoals(
            ctx.organizationId!,
            hospitalResults,
            input.scorecardView
          )

          return hospitalResults
        } else {
          let summaryResult = JSON.parse(cachedResult) as HospitalSummary[]

          if (input.hideEmptyIndicators)
            summaryResult = summaryResult.filter(
              (x) => !(x.summary == '0 Measures')
            )

          return summaryResult
        }
      }

      let result: unknown[] = []

      if (
        input.primaryMeasureType ===
        PrimaryMeasureTypeConstants.HospitalMeasures
      ) {
        let subOrganizations: SubOrganization[] = []
        if (ctx.useSpecialEntityStructure) {
          const entities = await ctx.measureResultsService!.getEntities({
            organizationId: ctx.organizationId!,
            isPartner: ctx.selectionType == SelectionType.Partner,
          })

          const type3Entities = entities.filter(
            (x) =>
              x.organizationTypeCode ==
                EntityOrganizationTypeConstants.HospitalLevel &&
              x.entityTypeName == EntityTypeConstants.Facility
          )

          type3Entities.forEach((x) => {
            subOrganizations?.push({
              subOrganizationId: x.code,
              subOrganizationName: x.entityName,
            })
          })

          const type4Entities = entities
            .filter(
              (x) =>
                x.organizationTypeCode ==
                  EntityOrganizationTypeConstants.FacilityLevel &&
                x.entityTypeName == EntityTypeConstants.Facility
            )
            .sort((a, b) => a.entityName!.localeCompare(b.entityName!))

          type4Entities.forEach((x) => {
            subOrganizations?.push({
              subOrganizationId: x.code,
              subOrganizationName: x.entityName,
            })
          })

          if (
            input.subOrganizationIds &&
            input.subOrganizationIds?.length &&
            !input.subOrganizationIds.includes('*')
          ) {
            subOrganizations = subOrganizations.filter((x) =>
              input.subOrganizationIds?.includes(x.subOrganizationId!)
            )
          }

          if (
            input.facilities &&
            input.facilities?.length &&
            !input.facilities.includes('*')
          ) {
            subOrganizations = subOrganizations.filter((x) =>
              input.facilities?.includes(x.subOrganizationId!)
            )
          }
        } else {
          const organizationService = new CitCOrganizationService(
            ctx.session?.accessToken!
          )

          if (ctx.subOrganizations?.length > 0) {
            subOrganizations = ctx.subOrganizations
          } else {
            subOrganizations =
              await organizationService.getSubOrganizationsByOrganizationId(
                ctx.organizationId!
              )
          }

          if (
            input.subOrganizationIds &&
            input.subOrganizationIds?.length > 0 &&
            !input.subOrganizationIds.includes('*')
          ) {
            const groups =
              await organizationService.getGroupsByOrganizationIdAsync({
                organizationId: ctx.organizationId!,
              })

            if (
              groups &&
              input.subOrganizationIds &&
              groups.some((x) => input.subOrganizationIds?.includes(x.groupId))
            )
              subOrganizations = groups.find(
                (x) => x.groupId == input.subOrganizationIds![0]
              )?.subOrganizations!
            else
              subOrganizations = subOrganizations.filter((x) =>
                input.subOrganizationIds?.includes(x.subOrganizationId!)
              )
          }
        }

        result = await getScorecardByHospital(
          ctx.measureResultsService!,
          input.scorecardView,
          ctx.organizationId!,
          ctx.selectionType == SelectionType.Partner,
          dayjs.utc(input.startDate).startOf('month'),
          dayjs.utc(input.endDate).endOf('month'),
          input.primaryMeasureType,
          input.scorecardDisplayType,
          subOrganizations,
          input.measureIdentifiers,
          ctx.session?.accessToken!,
          input.measureIdentifier
        )
      } else {
        let selectedOrganizations: Organization[] = []

        const partner = ctx.partners.find((x) => x.id == ctx.organizationId)
        if (ctx.selectionType == SelectionType.Partner) {
          selectedOrganizations = input.selectedOrganizations
            ? partner?.organizations?.filter((x) =>
                input.selectedOrganizations?.includes(x.organizationId!)
              )!
            : partner?.organizations!
        }

        const orgRoles: OrganizationRole[] = [
          {
            organizationId: ctx.organizationId,
            roles: Array.isArray(ctx.session?.role)
              ? ctx.session?.role
              : [ctx.session?.role!],
          },
        ]

        const hasLimitedAccess = hasLimitedAccessForOrganization(
          orgRoles,
          ctx.organizationId!
        )

        result = await getScorecardsBySubmissionGroups(
          ctx.measureResultsService!,
          input.scorecardView,
          ctx.organizationId!,
          ctx.selectionType == SelectionType.Partner,
          dayjs.utc(input.startDate).startOf('month'),
          dayjs.utc(input.endDate).endOf('month'),
          input.primaryMeasureType,
          input.scorecardDisplayType,
          input.submissionGroupIds!,
          input.measureIdentifiers,
          selectedOrganizations,
          ctx.selectionType == SelectionType.Partner
            ? partner?.organizations!
            : [],
          ctx.session?.accessToken!,
          ctx.session?.uid!,
          hasLimitedAccess,
          input.measureIdentifier
        )
      }

      await redisHelper.set(
        cacheKey,
        JSON.stringify(result),
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      if (input.measureIdentifier) {
        let hospitalResults = result as ScorecardResultByHospital[]

        if (input.hideEmptyIndicators)
          hospitalResults = hospitalResults.filter((x) => !x.isEmptyIndicator)

        hospitalResults = await setPerformanceGoals(
          ctx.organizationId!,
          hospitalResults,
          input.scorecardView
        )

        return hospitalResults
      } else {
        let summaryResult = result as HospitalSummary[]

        if (input.hideEmptyIndicators)
          summaryResult = summaryResult.filter(
            (x) => !(x.summary == '0 Measures')
          )

        return summaryResult
      }
    }),

  //#region performance goals

  getMeasuresForPerformanceGoals: protectedProcedure.query(async ({ ctx }) => {
    let result: PerformanceGoalMeasure[] = []
    let cacheKey = `${ctx.organizationId}-measures-for-performance-goals`

    let cachedResult = await redisHelper.get(cacheKey)

    if (cachedResult)
      result = JSON.parse(cachedResult) as PerformanceGoalMeasure[]
    else {
      result = await getMeasuresForPerformanceGoals(
        ctx.organizationId!,
        ctx.measureResultsService!,
        ctx.selectionType == SelectionType.Partner,
        ctx.session?.accessToken!
      )

      await redisHelper.set(
        cacheKey,
        JSON.stringify(result),
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )
    }

    // TODO: Enhancement* Move into trpc context
    const tableStorageProvider = new AzureTableStorageWrapper(
      StorageTables.PerformanceGoals
    )

    const migrationConfig: MigrationConfig = {
      organizationId: ctx.organizationId!,
      selectionType: ctx.selectionType!,
    }

    await migrateData({
      storage: tableStorageProvider,
      migrationConfig,
      prismaClientType: 'admClient',
      prismaQuery: (prismaClient: ADMPrismaClient) =>
        prismaClient.performanceGoals.findMany(),
      upsertFunction: async (storage, data) =>
        await Promise.all(
          data.map((goal) =>
            upsertPerformanceGoals({
              organizationId: migrationConfig.organizationId,
              isPartner: migrationConfig.selectionType == SelectionType.Partner,
              isEditProcess: false,
              measureIdentifier: goal.MeasureIdentifier,
              entityId: goal.EntitiesId.toString(),
              startDate: goal.StartDate,
              endDate: goal.EndDate,
              goalLower: goal.GoalLower?.toNumber() ?? undefined,
              goalUpper: goal.GoalUpper?.toNumber() ?? undefined,
              benchmark: goal.Benchmark?.toNumber() ?? undefined,
              isYellowZoneFixedNumber:
                goal.IsYellowZoneFixedNumber ?? undefined,
              yellowZone: goal.YellowZone?.toNumber() ?? undefined,
              isExceptionalPerformanceNumber:
                goal.IsExceptionalPerformanceNumber ?? undefined,
              exceptionalPerformance:
                goal.ExceptionalPerformance?.toNumber() ?? undefined,
              userId: ctx.session?.uid!,
            })
          )
        ),
    })

    const performanceGoals =
      await tableStorageProvider.queryEntities<PerformanceGoal>(
        tableStorageProvider.generateODataQuery({
          PartitionKey: `${ctx.organizationId}`,
        })
      )

    result.forEach((measure) => {
      measure.goalExists = performanceGoals.some(
        (x) =>
          x.measureIdentifier === measure.measureIdentifier &&
          x.entityId === measure.entityId.toString()
      )
    })

    return result
  }),

  upsertPerformanceGoals: protectedProcedure
    .input(
      z.object({
        id: z.string().optional(),
        startDate: z.date(),
        endDate: z.date(),
        goalLower: z.number().optional(),
        goalUpper: z.number().optional(),
        exceptionalPerformance: z.number().optional(),
        isExceptionalPerformanceNumber: z.boolean(),
        isYellowZoneFixedNumber: z.boolean(),
        yellowZone: z.number().optional(),
        isEditProcess: z.boolean(),
        measureIdentifier: z.string(),
        entityId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await upsertPerformanceGoals({
        isEditProcess: input.isEditProcess,
        organizationId: ctx.organizationId!,
        isPartner: ctx.selectionType == SelectionType.Partner,
        startDate: input.startDate,
        endDate: input.endDate,
        goalLower: input.goalLower,
        goalUpper: input.goalUpper,
        exceptionalPerformance: input.exceptionalPerformance,
        isExceptionalPerformanceNumber: input.isExceptionalPerformanceNumber,
        isYellowZoneFixedNumber: input.isYellowZoneFixedNumber,
        yellowZone: input.yellowZone,
        measureIdentifier: input.measureIdentifier,
        entityId: input.entityId,
        userId: ctx.session?.uid!,
      })
    }),

  getPerformanceGoals: protectedProcedure
    .input(
      z.object({
        measureIdentifier: z.string(),
        entityId: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      // TODO: Enhancement* Move into trpc context
      const tableStorageProvider = new AzureTableStorageWrapper(
        StorageTables.PerformanceGoals
      )

      const migrationConfig: MigrationConfig = {
        organizationId: ctx.organizationId!,
        selectionType: ctx.selectionType!,
      }

      await migrateData({
        storage: tableStorageProvider,
        migrationConfig,
        prismaClientType: 'admClient',
        prismaQuery: (prismaClient: ADMPrismaClient) =>
          prismaClient.performanceGoals.findMany({
            where: {
              MeasureIdentifier: input.measureIdentifier,
              EntitiesId: BigInt(input.entityId),
            },
          }),
        upsertFunction: async (storage, data) =>
          await Promise.all(
            data.map((goal) =>
              upsertPerformanceGoals({
                organizationId: migrationConfig.organizationId,
                isPartner:
                  migrationConfig.selectionType == SelectionType.Partner,
                isEditProcess: false,
                measureIdentifier: goal.MeasureIdentifier,
                entityId: goal.EntitiesId.toString(),
                startDate: goal.StartDate,
                endDate: goal.EndDate,
                goalLower: goal.GoalLower?.toNumber() ?? undefined,
                goalUpper: goal.GoalUpper?.toNumber() ?? undefined,
                benchmark: goal.Benchmark?.toNumber() ?? undefined,
                isYellowZoneFixedNumber:
                  goal.IsYellowZoneFixedNumber ?? undefined,
                yellowZone: goal.YellowZone?.toNumber() ?? undefined,
                isExceptionalPerformanceNumber:
                  goal.IsExceptionalPerformanceNumber ?? undefined,
                exceptionalPerformance:
                  goal.ExceptionalPerformance?.toNumber() ?? undefined,
                userId: ctx.session?.uid!,
              })
            )
          ),
      })

      const performanceGoals =
        await tableStorageProvider.queryEntities<PerformanceGoal>(
          tableStorageProvider.generateODataQuery({
            PartitionKey: `${ctx.organizationId}`,
            measureIdentifier: `${input.measureIdentifier}`,
            entityId: `${input.entityId}`,
          })
        )

      return performanceGoals.sort(
        (a, b) =>
          new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
      )
    }),

  deletePerformanceGoals: protectedProcedure
    .input(
      z.object({
        rowKeys: z.string().array(),
        measureIdentifier: z.string(),
        entityId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // TODO: Enhancement* Move into trpc context
      const tableStorageProvider = new AzureTableStorageWrapper(
        StorageTables.PerformanceGoals
      )

      input.rowKeys.forEach((entry) => {
        tableStorageProvider.deleteEntity(ctx.organizationId!, entry)
      })

      return {
        success: true,
        message: 'Goals deleted successfully',
      } as StatusResponse
    }),

  updatePerformanceGoals: protectedProcedure
    .input(
      z.object({
        performanceGoals: z.array(
          z.object({
            partitionKey: z.string(),
            rowKey: z.string(),
            measureIdentifier: z.string(),
            entityId: z.string(),
            startDate: z.string(),
            endDate: z.string(),
            goalLower: z.number().optional(),
            goalUpper: z.number().optional(),
            benchmark: z.number().optional(),
            isYellowZoneFixedNumber: z.boolean().optional(),
            yellowZone: z.number().optional(),
            isExceptionalPerformanceNumber: z.boolean().optional(),
            exceptionalPerformance: z.number().optional(),
            lastUpdatedByUserId: z.string().optional(),
            lastUpdatedDateTime: z.date().optional(),
          })
        ),
      })
    )
    .mutation(async ({ input }) => {
      // TODO: Enhancement* Move into trpc context
      const tableStorageProvider = new AzureTableStorageWrapper(
        StorageTables.PerformanceGoals
      )

      input.performanceGoals.forEach((entry) => {
        tableStorageProvider.updateEntity(entry as PerformanceGoal)
      })

      return {
        success: true,
        message: 'Goals updated successfully',
      } as StatusResponse
    }),

  //#endregion
})
