import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { SelectionType } from '@/enums/selectionType'
import CitCParametersService from '@/services/citc/citcParameters'
import { Constants } from '@/enums/constants'

type NoticeContentModel = {
  isNoticeDisplay: boolean
  noticeContent: string
}

const isNullOrEmpty = (value: string | undefined | null): boolean => {
  return !value || value.trim() === ''
}

export const noticeRouter = createTRPCRouter({
  check: protectedProcedure.query(async ({ ctx }) => {
    const model: NoticeContentModel = {
      isNoticeDisplay: false,
      noticeContent: '',
    }

    let parameters: SimplifiedParameter[] = []

    const parameterservice = new CitCParametersService(
      ctx.session?.accessToken!
    )

    if (ctx.selectionType === SelectionType.Organization) {
      parameters = await parameterservice.getOrganizationParametersAsync(
        ctx.organizationId!
      )
    } else {
      parameters = await parameterservice.getPartnerParametersAsync(
        ctx.organizationId!
      )
    }

    const globalParameters = await parameterservice.getGlobalParametersAsync()

    // Check if notice should be displayed
    const isDisplayNotice = Boolean(
      parameters.find((x) => x.key === Constants.ShowNotice)?.value
    )

    const orgNotice = parameters.find(
      (x) => x.key == Constants.NoticeContent
    )?.value
    const globalNotice = globalParameters.find(
      (x) => x.key == Constants.NoticeContent
    )?.value

    model.noticeContent = isNullOrEmpty(orgNotice)
      ? isNullOrEmpty(globalNotice)
        ? ''
        : globalNotice!
      : orgNotice!
    model.isNoticeDisplay = isNullOrEmpty(model.noticeContent)
      ? false
      : isDisplayNotice

    return model
  }),
})
