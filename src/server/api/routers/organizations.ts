import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc'
import CitCOrganizationService from '@/services/citCOrganizations'
import { TRPCError } from '@trpc/server'
import { SelectionType } from '@/enums/selectionType'
import CitCPartnersService from '@/services/citCPartners'
import { type Partner } from '@/types/partner'
import { Organization } from '@/types/organization'
import { GroupsnSubOrgs } from '@/types/groupsnSubOrgs'
import { getSessionSubOrganizations } from '@/services/subOrganizations/getSessionSubOrganizations'
import { getSubOrganizationsQuery } from '@/services/subOrganizations/getSubOrganizationsQuery'
import { getGroupsByOrganizationQuery } from '@/services/groups/getGroupsByOrganizationQuery'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { z } from 'zod'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import appInsights from '@/lib/applicationInsights'

export const organizationsRouter = createTRPCRouter({
  getByUser: protectedProcedure.query(async ({ ctx }) => {
    if (ctx.organizations?.length > 0) return ctx.organizations

    const citCOrganizationService = new CitCOrganizationService(
      ctx.session?.accessToken!
    )

    const organizations = await citCOrganizationService.getOrganizationsByUser(
      ctx.session?.uid!
    )

    return organizations ?? null
  }),
  getSelected: protectedProcedure.query(async ({ ctx }) => {
    let organization
    let organizationDetails

    appInsights.trackEvent({
      name: 'executing organizations.getSelected',
      properties: {
        ...ctx,
      },
    })

    if (ctx.selectionType === SelectionType.Organization) {
      const citCOrganizationService = new CitCOrganizationService(
        ctx.session?.accessToken!
      )

      organizationDetails =
        await citCOrganizationService.getOrganizationDetailsAsync(
          ctx.organizationId!
        )
    } else if (ctx.selectionType === SelectionType.Partner) {
      const citCPartnersService = new CitCPartnersService(
        ctx.session?.accessToken!
      )
      const partners = await citCPartnersService.getPartnersByUserAsync(
        ctx.session?.uid!
      )

      organizationDetails = partners.find((x) => x.id == ctx.organizationId)
    }

    organization =
      ctx.selectionType === SelectionType.Organization
        ? (organizationDetails as Organization)
        : (organizationDetails as Partner)

    appInsights.trackEvent({
      name: 'results of organizations.getSelected',
      properties: {
        ...ctx,
        organization,
      },
    })

    return {
      id:
        ctx.selectionType === SelectionType.Organization
          ? (organization as Organization).organizationId
          : (organization as Partner)?.id,
      name:
        ctx.selectionType === SelectionType.Organization
          ? (organization as Organization).organizationName
          : (organization as Partner)?.name,
      selectionType: ctx.selectionType,
      parameters: ctx.parameters,
      globalParameters: ctx.globalParameters,
      expansionConfiguration: ctx.expansionConfiguration,
      orgRoles: ctx.organizationRoles,
      primaryMeasureTypes: ctx.primaryMeasureTypes,
    }
  }),
  // TODO: How can I make use of query? I need to stage the hook but not call until ready to call ex while changing org
  fetchOrganizationDetails: protectedProcedure.mutation(async ({ ctx }) => {
    const parameters = ctx.parameters

    if (ctx.selectionType === SelectionType.Organization) {
      const orgFromContext = ctx.organizations.find(
        (x) => x.organizationId == ctx.organizationId
      )

      if (orgFromContext) {
        return {
          ...orgFromContext,
          parameters,
          globalParameters: ctx.globalParameters,
          expansionConfiguration: ctx.expansionConfiguration,
          organizationRoles: ctx.organizationRoles,
          primaryMeasureTypes: ctx.primaryMeasureTypes,
        }
      }

      const citCOrganizationService = new CitCOrganizationService(
        ctx.session?.accessToken!
      )

      const organizationDetails =
        await citCOrganizationService.getOrganizationDetailsAsync(
          ctx.organizationId!
        )

      return {
        ...organizationDetails,
        parameters,
        globalParameters: ctx.globalParameters,
        expansionConfiguration: ctx.expansionConfiguration,
        organizationRoles: ctx.organizationRoles,
        primaryMeasureTypes: ctx.primaryMeasureTypes,
      }
    } else if (ctx.selectionType === SelectionType.Partner) {
      const partnerFromContext = ctx.partners.find(
        (x) => x.id == ctx.organizationId
      )

      if (partnerFromContext) {
        return {
          ...partnerFromContext,
          parameters,
          globalParameters: ctx.globalParameters,
          expansionConfiguration: ctx.expansionConfiguration,
          organizationRoles: ctx.partnerRoles,
          primaryMeasureTypes: ctx.primaryMeasureTypes,
        }
      }
      const citCPartnersService = new CitCPartnersService(
        ctx.session?.accessToken!
      )

      const fetchedPartners = await citCPartnersService.getPartnersByUserAsync(
        ctx.session?.uid!
      )

      const partners: Partner[] = []

      if (fetchedPartners != null)
        partners.push(
          ...fetchedPartners.filter((x) => x.roles?.includes('Platform Ready'))
        )

      const partner = partners.find((x) => x.id == ctx.organizationId) ?? null

      return {
        ...partner,
        parameters,
        globalParameters: ctx.globalParameters,
        expansionConfiguration: ctx.expansionConfiguration,
        organizationRoles: ctx.partnerRoles,
        primaryMeasureTypes: ctx.primaryMeasureTypes,
      }
    } else {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Invalid selection type',
      })
    }
  }),
  get: protectedProcedure
    .input(
      z.object({
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
      })
    )
    .query(async ({ ctx, input }): Promise<Organization[]> => {
      const response: Organization[] = []

      if (ctx.selectionType === SelectionType.Partner) {
        const entities = await ctx.measureResultsService!.getEntities({
          organizationId: ctx.organizationId!,
          isPartner: true,
        })

        const partner = ctx.partners.find((x) => x.id === ctx.organizationId)

        for (const item of partner?.organizations!) {
          let combinedGroupName = entities.find(
            (x) =>
              x.entityTypeName === EntityTypeConstants.RenderingProvider &&
              x.organizationTypeCode ===
                EntityOrganizationTypeConstants.CombinedGroupLevel &&
              x.sourceContainerIdentifier.startsWith(item.organizationId!)
          )?.entityName

          if (!combinedGroupName) {
            combinedGroupName = entities.find(
              (x) =>
                x.entityTypeName === EntityTypeConstants.RenderingProvider &&
                x.organizationTypeCode ===
                  EntityOrganizationTypeConstants.SubmissionGroupLevel &&
                x.sourceContainerIdentifier.startsWith(item.organizationId!)
            )?.entityName
          }

          if (combinedGroupName) item.organizationName = combinedGroupName
        }
        response.push(...partner?.organizations!)
      }
      return response
    }),
  getSubOrganizations: protectedProcedure.query(
    async ({ ctx }): Promise<GroupsnSubOrgs[]> => {
      const response: GroupsnSubOrgs[] = []

      let subOrganizations = await getSessionSubOrganizations(
        ctx.selectionType!,
        ctx.session?.accessToken!,
        ctx.organizationId!,
        ctx.organizationRoles ?? [],
        ctx.session?.uid!
      )

      if (!subOrganizations || subOrganizations.length === 0) {
        subOrganizations = await getSubOrganizationsQuery(
          ctx.session?.accessToken!,
          ctx.organizationId!
        )
      }

      response.push(
        ...subOrganizations
          .sort(
            (a, b) =>
              a.subOrganizationName?.localeCompare(b.subOrganizationName!)!
          )
          .map((x) => ({
            isOrganizationGroup: false,
            subOrganizationId: x.subOrganizationId!,
            subOrganizationName: x.subOrganizationName!,
            ccnNumber: x.ccnNumber!,
          }))
      )

      const groups = await getGroupsByOrganizationQuery(
        ctx.session?.accessToken!,
        ctx.organizationId!
      )

      response.push(
        ...groups
          .sort((a, b) => a.groupName.localeCompare(b.groupName))
          .map((group) => ({
            isOrganizationGroup: true,
            subOrganizationId: group.groupId,
            subOrganizationName: group.groupName,
            ccnNumber: '',
          }))
      )

      return response
    }
  ),
})
