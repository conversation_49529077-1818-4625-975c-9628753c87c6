import { SubmissionGroup } from '@/types/submissionGroup'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import dayjs from 'dayjs'
import { SelectionType } from '@/enums/selectionType'
import { ManageUserRole } from '@/types/manageUserRole'
import { manageUserRolesQuery } from '@/services/adm/users/manageUserRolesQuery'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { hasLimitedAccessForOrganization } from '@/lib/hasLimitedAccessForOrganization'
import { redisHelper } from '@/lib/redis'
import { getSubmissionGroupsByOrganizationQuery } from '@/services/submissionGroups/getSubmissionGroupsByOrganizationQuery'
import { OrganizationRole } from '@/types/organizationRole'
import { calculateCurrentQuarter } from '@/lib/calculateCurrentQuarter'
import z from 'zod'

export const submissionGroupsRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        startDate: z.date().optional(),
        endDate: z.date().optional(),
      })
    )
    .query(async ({ ctx, input }): Promise<SubmissionGroup[]> => {
      
         const startDate =dayjs(input.startDate) 
         const endDate = dayjs(input.endDate) 

        //const selectedSubmissionGroups = input.submissionGroups || []
        const isPartner = ctx.selectionType == SelectionType.Partner
     
      const cacheKey = `org-submissionGrp-${ctx.organizationId}-${ctx.session?.uid}-${startDate}-${endDate}`
      let userRoles: ManageUserRole[] = []
      let canAccessibleRole: string[] = []

      // TODO: is this fine for partners?
      const orgRoles: OrganizationRole[] = [
        {
          organizationId: ctx.organizationId,
          roles: ctx.session?.role
            ? Array.isArray(ctx.session.role)
              ? ctx.session.role
              : [ctx.session.role]
            : [],
        },
      ]

      if (hasLimitedAccessForOrganization(orgRoles, ctx.organizationId!)) {
        userRoles = await manageUserRolesQuery(ctx.measureResultsService!, {
          organizationId: ctx.organizationId!,
          userId: ctx.session?.uid!,
          isPartner: isPartner,
          organizationType: EntityOrganizationTypeConstants.SubmissionGroupLevel,
        })

        if (userRoles.length == 0) {
          return []
        }

        canAccessibleRole = userRoles
          .filter((x) => x.canAccess)
          .map((x) => x.entityCode)
          .filter((x): x is string => x != null && x !== undefined)
      }

      const cachedData = await redisHelper.get(cacheKey)

      if (cachedData && JSON.parse(cachedData).length > 0) {
        return JSON.parse(cachedData)
      }

      let submissionGroups = (
        await getSubmissionGroupsByOrganizationQuery(
          ctx.session?.accessToken!,
          ctx.measureResultsService!,
          ctx.session?.uid!,
          {
            startDate: startDate,
            endDate: endDate,
            organizationId: ctx.organizationId!,
            isPartner: isPartner,
          }
        )
      ).sort((a, b) => a.submissionGroupName.localeCompare(b.submissionGroupName))

      if (hasLimitedAccessForOrganization(orgRoles, ctx.organizationId!)) {
        submissionGroups = submissionGroups.filter((x) =>
          canAccessibleRole.includes(x.submissionGroupId)
        )
      }

      await redisHelper.set(cacheKey, JSON.stringify(submissionGroups))

      const groups = submissionGroups.reduce<SubmissionGroup[]>(
        (unique, group) => {
          if (
            !unique.some((g) => g.submissionGroupId === group.submissionGroupId)
          ) {
            unique.push(group)
          }
          return unique
        },
        []
      )

      return groups
    }),
})
