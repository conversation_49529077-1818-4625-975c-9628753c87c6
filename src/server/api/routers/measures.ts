import { type OrganizationPaidMeasure } from '@/types/organizationPaidMeasure'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import dayjs from 'dayjs'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { z } from 'zod'
import { ScorecardView } from '@/enums/scorecardView'
import { getOrganizationPaidMeasuresQuery } from '@/prisma/getOrganizationPaidMeasuresQuery'
import { calculateMeasureResuts } from '@/prisma/calculateMeasureResuts'
import { filterEmptyIndicators } from '@/lib/filterEmptyIndicators'
import { filterResults } from '@/lib/filterResults'
import MapleMeasuresService from '@/services/mapleMeasures'
import { EntityDetailType } from '@/enums/entityDetailType'
import { redisHelper, tryCache } from '@/lib/redis'
import { env } from '@/env'
import type { CalculatedMeasure } from '@/types/calculatedMeasure'
import { getIAPIMeasureDetails } from '@/services/measures/getIAPIMeasureDetails'
import { MeasureFilterModel } from '@/types/measureFilterModel'
import { calculateCurrentQuarter } from '@/lib/calculateCurrentQuarter'
import appInsights from '@/lib/applicationInsights'
import { SelectionType } from '@/enums/selectionType'
import { EntityLevelConstants } from '@/types/expansionConfiguration'
import type { PerformanceRatesQuery } from '@/types/performanceRatesQuery'
import { TimePeriod } from '@/types/TimePeriod'
import { ExtensionLevels } from '@/enums/extensionLevels'
import CitCOrganizationService from '@/services/citc/citCOrganizations'

export const measuresRouter = createTRPCRouter({
  getMeasureResultDetails: protectedProcedure
    .input(
      z.object({
        measureId: z.string(),
        // measureId: z.string().min(1, 'measureId is required'),
        startDate: z.date().optional().nullable(),
        endDate: z.date().optional().nullable(),
        entityId: z.string().min(1, 'entityId is required'),
        entityDetailType: z.nativeEnum(EntityDetailType),
        patientDetailAccessSuborgs: z
          .string()
          .min(1, 'patientDetailAccessSuborgs is invalid')
          .optional(),
        haveOrgLevelPatientDetailAccess: z
          .string()
          .min(1, 'haveOrgLevelPatientDetailAccess is invalid')
          .optional(),
        aggregationType: z
          .nativeEnum(ScorecardView)
          .default(ScorecardView.Monthly),
        measureType: z
          .nativeEnum(PrimaryMeasureTypeConstants)
          .default(PrimaryMeasureTypeConstants.HospitalMeasures),
      })
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.organizationId) {
        appInsights.trackException({
          exception: new Error(
            `No organizationId found for user ${ctx.session?.uid}`
          ),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      //this condition was added to the prefetching scenarios where startdate and enddate my take a moment to hydrate
      if (!input.startDate || !input.endDate || !input.measureId) {
        return []
      }

      if (input.measureType === PrimaryMeasureTypeConstants.HospitalMeasures) {
        return await ctx.measureResultsService!.getHospitalMeasureResultDetails(
          {
            organizationId: ctx.organizationId,
            measureIdentifier: input.measureId,
            startDate: dayjs(input.startDate),
            endDate: dayjs(input.endDate),
            entityId: input.entityId,
            entityDetailType: input.entityDetailType,
            scorecardView: input.aggregationType,
            isPartner: ctx.selectionType === SelectionType.Partner,
          },
          new CitCOrganizationService(ctx.session?.accessToken!)
        )
      }

      if (
        input.measureType === PrimaryMeasureTypeConstants.AmbulatoryMeasures
      ) {
        return await ctx.measureResultsService!.getAmbulatoryMeasureResultDetails(
          ctx.session?.uid!,
          {
            organizationId: ctx.organizationId,
            measureIdentifier: input.measureId,
            startDate: dayjs(input.startDate),
            endDate: dayjs(input.endDate),
            entityId: input.entityId,
            entityDetailType: input.entityDetailType,
            scorecardView: input.aggregationType,
            //patientDetailAccessSuborgs: input.patientDetailAccessSuborgs,
            //haveOrgLevelPatientDetailAccess: input.haveOrgLevelPatientDetailAccess,
            isPartner: ctx.selectionType === SelectionType.Partner,
          }
        )
      }
      //not implemented yet
      return []
    }),
  getMeasureMetaData: protectedProcedure
    .input(
      z.object({
        measureId: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.organizationId) {
        appInsights.trackException({
          exception: new Error(
            `No organizationId found for user ${ctx.session?.uid}`
          ),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      if (!input.measureId) {
        return []
      }

      return new MapleMeasuresService(
        ctx.session?.accessToken!,
        ctx.measureResultsService!
      ).getMeasureMetadata(ctx.organizationId, input.measureId)
    }),
  get: protectedProcedure
    .input(
      z.object({
        primaryMeasureType: z
          .nativeEnum(PrimaryMeasureTypeConstants)
          .default(PrimaryMeasureTypeConstants.HospitalMeasures),
        periodStartDate: z.date().optional(),
        periodEndDate: z.date().optional(),
      })
    )
    .query(async ({ ctx, input }): Promise<MeasureFilterModel[]> => {
      if (!ctx.organizationId) {
        appInsights.trackException({
          exception: new Error(
            `No organizationId found for user ${ctx.session?.uid}`
          ),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      const today = dayjs()

      const endMonthsOfQuarters: number[] = [3, 6, 9, 12]
      const currentQuarter = calculateCurrentQuarter(today)

      let endDate = dayjs(
        new Date(
          today.year(),
          (endMonthsOfQuarters[currentQuarter - 1] ?? 1) - 1, // -1 because months are 0-based
          1
        )
      ).endOf('month')
      let startDate = endDate.add(-15, 'month').add(1, 'day')

      appInsights.trackEvent({
        name: 'executing measures.get',
        properties: {
          ...ctx,
        },
      })

      if (input.periodStartDate && input.periodEndDate) {
        startDate = dayjs(input.periodStartDate)
        endDate = dayjs(input.periodEndDate)
      }

      const measures = await getOrganizationPaidMeasuresQuery(
        ctx.measureResultsService!,
        startDate,
        endDate,
        input.primaryMeasureType,
        ctx.session?.accessToken!,
        ctx.parameters ?? [],
        ctx.organizationId
      )

      appInsights.trackEvent({
        name: 'result of measures.get',
        properties: {
          ...ctx,
          measures,
        },
      })

      const measureFilterModels: MeasureFilterModel[] = Array.from(
        measures
          .sort((a, b) => a.measureTitle.localeCompare(b.measureTitle)) // OrderBy MeasureTitle
          .reduce((acc, curr) => {
            // GroupBy MeasureIdentifier
            if (!acc.has(curr.measureIdentifier)) {
              acc.set(curr.measureIdentifier, [])
            }
            acc.get(curr.measureIdentifier)?.push(curr)
            return acc
          }, new Map<string, OrganizationPaidMeasure[]>()) // Using a Map to group by MeasureIdentifier
      )
        .map(([_, group]) => ({
          measureId: group[0]?.measureIdentifier ?? '',
          measureName: group[0]?.measureTitle ?? '',
          denominatorQualifyingType: '',
          smallestInterval: '',
          equityStrata: '',
          applicationName: group[0]?.applicationName ?? '',
        }))
        .sort((a, b) => {
          // Sort again by MeasureTitle and subgroups
          if (a.measureName !== b.measureName) {
            return a.measureName.localeCompare(b.measureName)
          }
          return a.equityStrata.localeCompare(b.equityStrata)
        })

      appInsights.trackEvent({
        name: 'result of measures.get.measureFilterModels',
        properties: {
          ...ctx,
          measureFilterModels,
        },
      })

      return measureFilterModels
    }),
  calculateResults: protectedProcedure
    .input(
      z.object({
        aggregationType: z.nativeEnum(ScorecardView),
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
        hideEmptyIndicators: z.boolean(),
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
        filters: z.object({
          organizations: z.string().array().optional(),
          subOrganizations: z.string().array(),
          measures: z.string().array(),
        }),
      })
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.organizationId || !ctx.selectionType) {
        const message = !ctx.organizationId
          ? 'No organizationId provided'
          : !ctx.selectionType
            ? 'No selectionType provided'
            : 'unnkown issue'

        appInsights.trackException({
          exception: new Error(message),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      if (!ctx.primaryMeasureTypes.includes(input.primaryMeasureType)) {
        return []
      }

      const type1EntityDisplayLevel = ctx.expansionConfiguration.find(
        (x) => x.level === ExtensionLevels.level1
      )?.selectedLevel
      const cacheKey = `${ctx.organizationId}-${input.primaryMeasureType}-${input.aggregationType}-${input.startDate}-${input.endDate}-${input.filters.subOrganizations.join(',')}-${input.filters.measures.join(',')}-${input.hideEmptyIndicators}-${type1EntityDisplayLevel ?? ''}`

      return await tryCache(cacheKey, async () => {
        let measuresResult = await calculateMeasureResuts(
          ctx.measureResultsService!,
          new CitCOrganizationService(ctx.session?.accessToken!),
          input.aggregationType,
          dayjs(input.startDate).utc(),
          dayjs(input.endDate).utc(),
          input.primaryMeasureType,
          ctx.organizationId!,
          ctx.selectionType!,
          ctx.session?.accessToken!,
          ctx.parameters ?? [],
          ctx.session?.uid!,
          [...(ctx.partnerRoles ?? []), ...(ctx.organizationRoles ?? [])],
          input.filters.organizations
            ? input.filters.organizations.join(',')
            : '',
          input.filters.subOrganizations ? input.filters.subOrganizations : [],
          ctx.selectionType === SelectionType.Organization &&
            type1EntityDisplayLevel === EntityLevelConstants.TopLevel,
          ctx.partners
        )

        if (
          input.filters.measures.length !== 0 &&
          !input.filters.measures.includes('*')
        ) {
          measuresResult = filterResults<CalculatedMeasure>(
            measuresResult,
            'measureIdentifier',
            input.filters.measures
          )
        }

        if (input.hideEmptyIndicators) {
          measuresResult =
            filterEmptyIndicators<CalculatedMeasure>(measuresResult)
        }

        return measuresResult
      })
    }),
  getMeasureResultsByHospital: protectedProcedure
    .input(
      z.object({
        measureIdentifier: z.string(),
        hideEmptyIndicators: z.boolean(),
        scorecardView: z.nativeEnum(ScorecardView),
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
        filters: z.object({
          organizations: z.string().array().optional(),
          subOrgs: z.string().array(),
          providers: z.string().array(),
          submissionGroups: z.array(z.string()).optional(),
        }),
      })
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.organizationId || !ctx.selectionType) {
        const message = !ctx.organizationId
          ? 'No organizationId provided'
          : !ctx.selectionType
            ? 'No selectionType provided'
            : 'unnkown issue'

        appInsights.trackException({
          exception: new Error(message),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })
        return []
      }

      return await ctx.measureResultsService!.getMeasureResultsByHospital({
        primaryMeasureType: input.primaryMeasureType,
        startDate: dayjs(input.startDate),
        endDate: dayjs(input.endDate),
        hideEmptyIndicators: input.hideEmptyIndicators ?? false,
        measureIdentifier: input.measureIdentifier,
        selectedSpan: input.scorecardView,
        entities: input.filters.subOrgs ?? [],
        organizations: input.filters.organizations?.join(','),
        providers: input.filters.providers ?? [],
        submissionGroups: input.filters.submissionGroups ?? [], // Pass submissionGroups to service
        organizationId: ctx.organizationId,
        selectionType: ctx.selectionType,
        userId: ctx.session?.uid!,
        orgRoles: [...(ctx.partnerRoles ?? []), ...(ctx.organizationRoles ?? [])],
        expansionConfiguration: ctx.expansionConfiguration!,
        partners: ctx.partners,
        useSpecialEntityStructure: ctx.useSpecialEntityStructure,
        subOrganizations: ctx.subOrganizations,
      })
    }),
  getProviderResults: protectedProcedure
    .input(
      z.object({
        scorecardView: z.nativeEnum(ScorecardView),
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
        providers: z.string().array(),
        measureIdentifier: z.string(),
        entityId: z.string(),
        hideEmptyIndicators: z.boolean(),
      })
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.organizationId || !ctx.selectionType) {
        const message = !ctx.organizationId
          ? 'No organizationId provided'
          : !ctx.selectionType
            ? 'No selectionType provided'
            : 'unnkown issue'

        appInsights.trackException({
          exception: new Error(message),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      return await ctx.measureResultsService!.getProviderResults(
        ctx.organizationId,
        [...(ctx.partnerRoles ?? []), ...(ctx.organizationRoles ?? [])],
        ctx.selectionType,
        input.scorecardView,
        input.measureIdentifier,
        dayjs(input.startDate),
        dayjs(input.endDate),
        input.entityId,
        input.hideEmptyIndicators,
        ctx.session?.uid!,
        input.providers,
        ctx.partners
      )
    }),

  getPrimaryMeasureTypes: protectedProcedure
    // .input(
    //   z.object({
    //     isDevelopementEnv: z.boolean().default(false),
    //   })
    // )
    .query(async ({ ctx }): Promise<PrimaryMeasureTypeConstants[]> => {
      return ctx.primaryMeasureTypes
    }),

  getIAPIMeasureDetails: protectedProcedure
    .input(
      z.object({
        startDate: z.date(),
        endDate: z.date(),
        measureIdentifier: z.string(),
        entityId: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.organizationId || !ctx.selectionType) {
        const message = !ctx.organizationId
          ? 'No organizationId provided'
          : !ctx.selectionType
            ? 'No selectionType provided'
            : 'unnkown issue'

        appInsights.trackException({
          exception: new Error(message),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      return await getIAPIMeasureDetails(
        dayjs(input.startDate),
        dayjs(input.endDate),
        ctx.organizationId,
        ctx.selectionType,
        input.entityId,
        input.measureIdentifier,
        ctx.measureResultsService!
      )
    }),

  getExportResults: protectedProcedure
    .input(
      z.object({
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
        measureIdentifiers: z.string().array(),
        scorecardView: z.nativeEnum(ScorecardView),
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
        includeLevel4: z.boolean(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const cacheKey = `${ctx.organizationId}-${input.primaryMeasureType}-${input.measureIdentifiers.join(',')}-${input.startDate}-${input.endDate}-${input.scorecardView}-${input.includeLevel4}`

      let cachedResult = await redisHelper.get(cacheKey)

      if (cachedResult && cachedResult !== '{}') {
        return JSON.parse(cachedResult)
      }

      const result = await ctx.measureSummaryService!.exportMeasureResults(
        input.scorecardView,
        dayjs(input.startDate).utc(),
        dayjs(input.endDate).utc(),
        input.primaryMeasureType,
        input.measureIdentifiers,
        input.includeLevel4
      )

      await redisHelper.set(
        cacheKey,
        JSON.stringify(result),
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result
    }),

  getPerformanceRates: protectedProcedure
    .input(
      z.object({
        medisolvMeasureId: z.string(),
        period: z.nativeEnum(TimePeriod),
        dateStart: z.date(),
        dateEnd: z.date(),
      })
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.organizationId) {
        appInsights.trackException({
          exception: new Error(
            `No organizationId found for user ${ctx.session?.uid}`
          ),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      if (!input.medisolvMeasureId) {
        return []
      }

      // Find all the hospital entities for the Organization
      const entities =
        await ctx.measureResultsService!.getEntitiesForOrganizationByIdAndEntityTypeId(
          {
            organizationId: ctx.organizationId,
            entityType: 1,
          }
        )

      // This will select either the rollup hospital or the first non-rollup
      const entity = entities!
        .filter(
          (value) =>
            value.OrganizationTypeId == 2 || value.OrganizationTypeId == 1
        )
        .sort((a, b) =>
          (a.OrganizationTypeId ?? 0) <= (b.OrganizationTypeId ?? 0) ? 1 : -1
        )[0]

      return await ctx.measureResultsService!.getPerformanceRatesByDateAndMeasureForHospital(
        {
          ...input,
          entityId: entity?.Id ?? BigInt(0),
          organizationId: ctx.organizationId,
        } as PerformanceRatesQuery
      )
    }),
})
