import { createTRPCRouter, protectedProcedure } from '../trpc'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { z } from 'zod'
import { ScorecardView } from '@/enums/scorecardView'
import { getPatientDetails } from '@/services/patient/getPatientDetails'
import { EntityDetailType } from '@/enums/entityDetailType'
import { SelectionType } from '@/enums/selectionType'
import { PatientExplorerQuery } from '@/types/patientExplorerQuery'
import { PatientExplorerResult } from '@/types/patientExplorerResult'
import { getPatientExplorerQuery } from '@/services/patient/getPatientExplorerQuery'
import { env } from '@/env'
import { tryCache } from '@/lib/redis'

export const patientsRouter = createTRPCRouter({
  getPatientDetails: protectedProcedure
    .input(
      z.object({
        measureId: z.string().min(1, 'measureId is required'),
        periodSpan: z.string(),
        entityId: z.string().min(1, 'entityId is required'),
        entityDetailType: z.nativeEnum(EntityDetailType),
        sourceContainerIdentifier: z.string().default(''),
        primaryMeasureType: z
          .nativeEnum(PrimaryMeasureTypeConstants)
          .default(PrimaryMeasureTypeConstants.HospitalMeasures),
        isPatientLevelAccessAvailable: z.boolean(),
        scorecardView: z
          .nativeEnum(ScorecardView)
          .default(ScorecardView.Monthly),
        category: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-patient-details-${ctx.organizationId}-${input.measureId}-${input.periodSpan}-${input.entityId}-${input.entityDetailType}-${input.sourceContainerIdentifier}-${input.primaryMeasureType}-${input.isPatientLevelAccessAvailable}-${input.scorecardView}`

      if (!global.__inFlightRequests) {
        global.__inFlightRequests = new Map<string, Promise<any>>()
      }
      const inFlightRequests: Map<
        string,
        Promise<any>
      > = global.__inFlightRequests

      if (inFlightRequests.has(cacheKey)) {
        const cachedResult = await inFlightRequests.get(cacheKey)!
        return input.category === '*'
          ? cachedResult
          : cachedResult.filter((x: any) => x.result === input.category)
      }

      const requestPromise = tryCache(
        cacheKey,
        async () => {
          return await getPatientDetails(
            ctx.measureResultsService!,
            ctx.parameters!,
            ctx.session!.accessToken!,
            {
              entityId: input.entityId,
              periodSpan: input.periodSpan,
              measureIdentifier: input.measureId,
              organizationId: ctx.organizationId!,
              entityType: input.entityDetailType,
              isPartner: ctx.selectionType === SelectionType.Partner,
              primaryMeasureType: input.primaryMeasureType,
              isPatientLevelAccessAvailable:
                input.isPatientLevelAccessAvailable,
              scorecardView: input.scorecardView,
              sourceContainerIdentifier: input.sourceContainerIdentifier,
            }
          )
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      inFlightRequests.set(cacheKey, requestPromise)

      const result = await requestPromise
      inFlightRequests.delete(cacheKey)
      return input.category === '*'
        ? result
        : result.filter((x) => x.result === input.category)
    }),
  search: protectedProcedure
    .input(
      z.object({
        query: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      const searchText = input.query.trim()

      if (!searchText) {
        return []
      }

      const query: PatientExplorerQuery = {
        searchText,
        organizationId: ctx.organizationId!,
        isPartner: ctx.selectionType === SelectionType.Partner,
      }

      const result: PatientExplorerResult[] =
        await getPatientExplorerQuery(query)

      const response = result.filter(
        (x) => x.displayPatientId && x.displayPatientId.trim() !== ''
      )

      return response
    }),
})
