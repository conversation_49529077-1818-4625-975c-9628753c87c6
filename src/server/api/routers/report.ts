import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { getReports } from '@/services/reports/getReports'
import { getMeasureInsightReports } from '@/services/reports/getMeasureInsightReports'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import appInsights from '@/lib/applicationInsights'
import {
  ACOMeasuresRequest,
  Report,
  ReportRequest,
} from '@/types/reports/medisolvReport'
import { SisenseMeasureSummary } from '@/services/sisense/SisenseMeasureSummaryRepository'
import { SelectionType } from '@/enums/selectionType'
import { standardReports } from '@/data/reports'
import UserNotificationsService from '@/services/userNotifications'
import sendEmail from '@/lib/sendEmail'
import { Constants } from '@/enums/constants'
import MapleMeasuresService from '@/services/mapleMeasures'
import { getMeasureTitle } from '@/lib/getMeasureTitle'
import { AvailableMeasure } from '@/types/measure'
import { redisHelper, tryCache } from '@/lib/redis'
import { env } from '@/env'

export const reportRouter = createTRPCRouter({
  // #region Manange Reports

  saveReport: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, 'Report name is required'),
        description: z.string().optional().default(''),
        reportConfig: z.string().optional(),
        userId: z.string().optional(),
        reportType: z.string().optional(),
        reportId: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const tableStorageWrapper = new AzureTableStorageWrapper(
          StorageTables.Reports
        )

        if (input.reportId) {
          const existingReport = await tableStorageWrapper.getEntity<Report>(
            ctx.organizationId!,
            input.reportId
          )

          if (existingReport) {
            existingReport.reportConfig = input.reportConfig || ''
            existingReport.lastModified = new Date().toISOString()

            await tableStorageWrapper.updateEntity(existingReport)

            return {
              success: true,
              existingReport,
              isUpdate: true,
            }
          } else {
            return {
              success: false,
              error: 'Report not found',
            }
          }
        }

        const existingReports = await tableStorageWrapper.queryEntities<Report>(
          tableStorageWrapper.generateODataQuery({
            PartitionKey: ctx.organizationId,
            userId: ctx.session?.uid,
            name: input.name,
            isDeleted: false,
          })
        )

        if (existingReports.length > 0) {
          return {
            success: false,
            error: 'Report with this name already exists',
          }
        }

        // Create a new report
        const newReport: Report = {
          partitionKey: ctx.organizationId!,
          rowKey: crypto.randomUUID(),
          name: input.name,
          description: input.description || '',
          reportConfig: input.reportConfig || '',
          isActive: true,
          isCustom: true,
          isFavorite: false,
          userId: ctx.session?.uid || input.userId,
          lastModified: new Date().toISOString(),
          reportType: input.reportType,
        }

        await tableStorageWrapper.upsertEntity(newReport)

        return {
          success: true,
          newReport,
          isUpdate: false,
        }
      } catch (err) {
        console.error('Error in saveReport:', err)
        appInsights.trackException({
          exception:
            err instanceof Error
              ? err
              : new Error('Unknown error in saveReport'),
          properties: input,
        })

        return {
          success: false,
          error:
            err instanceof Error
              ? err.message
              : 'Failed to save report. Please try again later.',
        }
      }
    }),

  updateFavoriteReport: protectedProcedure
    .input(
      z.object({
        reportId: z.string(),
        isFavorite: z.boolean().default(false),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.Reports
      )

      const report = await tableStorageWrapper.getEntity<Report>(
        ctx.organizationId!,
        input.reportId
      )

      if (!report) {
        throw new Error(`Report with ID ${input.reportId} not found`)
      }

      tableStorageWrapper.updateEntity({
        ...report,
        isFavorite: input.isFavorite,
      } as Report)
    }),

  shareReport: protectedProcedure
    .input(
      z.object({
        reportId: z.string(),
        reportType: z.string(),
        users: z
          .object({
            id: z.string(),
            email: z.string(),
            name: z.string(),
          })
          .array(),
        message: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.Reports
      )

      const report = await tableStorageWrapper.getEntity<Report>(
        ctx.organizationId!,
        input.reportId
      )

      if (!report) {
        throw new Error(`Report with ID ${input.reportId} not found`)
      }

      input.users.forEach(async (user) => {
        await tableStorageWrapper.insertEntity<Report>({
          ...report,
          userId: user.id,
          sharedBy: ctx.session?.uid!,
          rowKey: crypto.randomUUID(),
          lastModified: new Date().toISOString(),
        } as Report)
      })

      const notificationMessage = input.message
        ? `${ctx.session?.given_name || 'Someone'} has shared a report with you :- ${input.message}`
        : `${ctx.session?.given_name || 'Someone'} has shared a report with you`

      const userNotificationsService = new UserNotificationsService()

      userNotificationsService.sendNotification(
        new AzureTableStorageWrapper(StorageTables.UserNotifications),
        ctx.session?.uid!,
        notificationMessage,
        input.users.map((x) => x.id)
      )

      const parameters =
        await ctx.citCParametersService.getGlobalParametersAsync()
      const apiKey = parameters.find(
        (x) => x.key === Constants.SendGridApiKey
      )?.value!

      let host = ''
      if (process.env.VERCEL_URL) host = `https://${process.env.VERCEL_URL}`
      else host = `http://localhost:${process.env.PORT ?? 3000}`

      sendEmail(
        {
          users: input.users,
          subject: 'Medisolv Report Shared',
          templateName: 'notification',
          templateData: {
            redirectUrl: `${host}/reports/${input.reportType}?reportId=${input.reportId}`,
            actionText: 'View Report',
          },
        },
        apiKey
      )

      return { success: true }
    }),

  getReportsList: protectedProcedure.query(async () => {
    // TODO: May remove
    const result = await getReports()
    return result
  }),

  getUserReports: protectedProcedure.query(async ({ ctx }) => {
    const tableStorageWrapper = new AzureTableStorageWrapper(
      StorageTables.Reports
    )

    const reports = await tableStorageWrapper.queryEntities<Report>(
      tableStorageWrapper.generateODataQuery({
        PartitionKey: ctx.organizationId,
      })
    )

    const clientReports = reports.filter((x) => !x.isCustom)

    const standardReportsList = standardReports
      .filter((x) => x.isActive)
      .map((x) => ({
        ...x,
        rowKey: crypto.randomUUID(),
        partitionKey: ctx.organizationId!,
      }))

    const missingReports = standardReportsList.filter(
      (x) => !clientReports.find((y) => y.reportType === x.reportType)
    )

    if (missingReports.length) {
      await Promise.all(
        missingReports.map((x) => tableStorageWrapper.insertEntity<Report>(x))
      )

      reports.push(...missingReports)
    }

    const userReports = reports.filter(
      (report) =>
        report.isActive &&
        (!report.isCustom || report.userId === ctx.session?.uid)
    )

    return userReports
  }),

  getReportById: protectedProcedure
    .input(
      z.object({
        id: z.string().min(1, 'Report ID is required'),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        // Get the report by ID with its configuration
        const tableStorageWrapper = new AzureTableStorageWrapper(
          StorageTables.Reports
        )

        // Get the report entity from Azure Table Storage
        const report = await tableStorageWrapper.getEntity<Report>(
          ctx.organizationId!,
          input.id
        )

        console.log('report', report)
        if (!report) {
          throw new Error(`Report with ID ${input.id} not found`)
        }

        // Check if this is a custom report and if the user has access to it
        if (
          report.isCustom &&
          report.userId &&
          report.userId !== ctx.session?.uid
        ) {
          throw new Error(`You do not have access to this report`)
        }

        // Return the report with its configuration
        return report
      } catch (error) {
        console.error(`Error fetching report with ID ${input.id}:`, error)
        throw new Error(
          `Failed to fetch report: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      }
    }),

  saveReportsStatus: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        reportType: z.string(),
        status: z.boolean(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      var tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.Reports
      )

      let report = await tableStorageWrapper.getEntity<Report>(
        ctx.organizationId!,
        input.id
      )

      if (!report) {
        throw new Error(`Report with type ${input.reportType} not found`)
      }

      report.isActive = input.status
      report.lastModified = new Date().toISOString()
      await tableStorageWrapper.updateEntity(report)
    }),

  updateReportOwnership: protectedProcedure
    .input(
      z.object({
        reportId: z.string(),
        userId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // TODO: Enhancement* Move into trpc context
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.Reports
      )

      let report = await tableStorageWrapper.getEntity<Report>(
        ctx.organizationId!,
        input.reportId
      )

      if (report) {
        // Only update if the report is custom and doesn't already have a userId
        if (report.isCustom && !report.userId) {
          // Update the userId field
          report.userId = input.userId

          // Update the lastModified timestamp to reflect the change
          report.lastModified = new Date().toISOString()

          // Update the entity in Azure Table Storage
          await tableStorageWrapper.updateEntity(report)

          return { success: true }
        }
      }

      return { success: false }
    }),

  deleteReport: protectedProcedure
    .input(
      z.object({
        reportId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const tableStorageWrapper = new AzureTableStorageWrapper(
          StorageTables.Reports
        )

        await tableStorageWrapper.deleteEntity(
          ctx.organizationId!,
          input.reportId
        )

        return { success: true }
      } catch (error) {
        console.error(`Error deleting report with ID ${input.reportId}:`, error)

        // If the error is about permissions, rethrow it
        if (error instanceof Error && error.message.includes('permission')) {
          throw error
        }

        // Otherwise, return a generic error
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      }
    }),

  getMeasureInsightReports: protectedProcedure.query(async ({ ctx }) => {
    const result = await getMeasureInsightReports(
      ctx.organizationId!,
      ctx.selectionType!
    )
    return result
  }),

  // #endregion

  getACOMeasurePerformanceByUniverse: protectedProcedure
    .input(
      z.object({
        intervalType: z.string(),
        startDate: z.date(),
        endDate: z.date(),
        measureIdentifiers: z.string().array(),
        sourceContainerIdentifier: z.string(),
        universe: z.string(),
        runtime: z.number().optional(),
      }) as z.ZodType<ReportRequest>
    )
    .query(
      async ({ ctx, input }): Promise<SisenseMeasureSummary[] | undefined> => {
        try {
          const cacheKey = `get-aco-measure-performance-by-universe-${ctx.organizationId}-${input.intervalType}-${input.startDate}-${input.endDate}-${input.measureIdentifiers.join(',')}-${input.sourceContainerIdentifier}-${input.universe}`

          const cachedData = await redisHelper.get(cacheKey)
          if (cachedData) {
            return JSON.parse(cachedData) as SisenseMeasureSummary[]
          }

          const mapleMeasuresService = new MapleMeasuresService(
            ctx.session?.accessToken!,
            ctx.measureResultsService!
          )

          const allMapleMeasures = await mapleMeasuresService.getAllMeasures(
            ctx.organizationId!
          )

          const measureMap = new Map(
            allMapleMeasures.map((m) => [m.measureIdentifier.toLowerCase(), m])
          )

          const measurePerformanceByUniverse =
            await ctx.acoPerformanceAnalysisService?.getACOMeasurePerformanceByUniverse(
              input
            )

          const result: SisenseMeasureSummary[] = (
            measurePerformanceByUniverse ?? []
          ).map((element) => {
            const measure = measureMap.get(
              element.MeasureIdentifier.toLowerCase()
            )
            return {
              ...element,
              MeasureName: measure
                ? getMeasureTitle(measure)
                : element.MeasureName,
              ApplicationName: measure?.applicationName,
            }
          })

          result.sort((a, b) =>
            a.EntityDescription.localeCompare(b.EntityDescription)
          )

          await redisHelper.set(
            cacheKey,
            JSON.stringify(result),
            Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
          )

          return result
        } catch (err) {
          console.error('Error in getACOMeasurePerformanceByUniverse:', err)
          appInsights.trackException({
            exception:
              err instanceof Error
                ? err
                : new Error(
                    'Unknown error in getACOMeasurePerformanceByUniverse'
                  ),
            properties: input,
          })

          return []
        }
      }
    ),

  getACOMeasurePerformanceByOrgCode: protectedProcedure
    .input(
      z.object({
        intervalType: z.string(),
        startDate: z.date(),
        endDate: z.date(),
        measureIdentifiers: z.string().array(),
        sourceContainerIdentifier: z.string(),
        code: z.number(),
        runtime: z.number().optional(),
      }) as z.ZodType<ReportRequest>
    )
    .query(
      async ({ ctx, input }): Promise<SisenseMeasureSummary[] | undefined> => {
        try {
          const cacheKey = `get-aco-measure-performance-by-org-code-${ctx.organizationId}-${input.intervalType}-${input.startDate}-${input.endDate}-${input.measureIdentifiers.join(',')}-${input.sourceContainerIdentifier}-${input.code}`

          const cachedData = await redisHelper.get(cacheKey)
          if (cachedData) {
            return JSON.parse(cachedData) as SisenseMeasureSummary[]
          }

          const mapleMeasuresService = new MapleMeasuresService(
            ctx.session?.accessToken!,
            ctx.measureResultsService!
          )

          const allMapleMeasures = await mapleMeasuresService.getAllMeasures(
            ctx.organizationId!
          )

          const measureMap = new Map(
            allMapleMeasures.map((m) => [m.measureIdentifier.toLowerCase(), m])
          )

          const measurePerformanceByOrgCode =
            await ctx.acoPerformanceAnalysisService?.getACOMeasurePerformanceByOrgCode(
              input
            )

          const result: SisenseMeasureSummary[] = (
            measurePerformanceByOrgCode ?? []
          ).map((element) => {
            const measure = measureMap.get(
              element.MeasureIdentifier.toLowerCase()
            )
            return {
              ...element,
              MeasureName: measure
                ? getMeasureTitle(measure)
                : element.MeasureName,
              ApplicationName: measure?.applicationName,
            }
          })

          result.sort((a, b) =>
            a.EntityDescription.localeCompare(b.EntityDescription)
          )

          await redisHelper.set(
            cacheKey,
            JSON.stringify(result),
            Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
          )

          return result
        } catch (err) {
          console.error('Error in getACOMeasurePerformanceByOrgCode:', err)
          appInsights.trackException({
            exception:
              err instanceof Error
                ? err
                : new Error(
                    'Unknown error in getACOMeasurePerformanceByOrgCode'
                  ),
            properties: input,
          })
          return []
        }
      }
    ),

  getACOMeasures: protectedProcedure
    .input(
      z.object({
        codeNumeric: z.string().min(1, 'codeNumeric is required'),
        intervalType: z.string(),
        startDate: z.date(),
        endDate: z.date(),
      }) as z.ZodType<ACOMeasuresRequest>
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.organizationId) {
        appInsights.trackException({
          exception: new Error(
            `No organizationId found for user ${ctx.session?.uid}`
          ),
          properties: {
            ...ctx,
          },
        })

        return {
          measures: null,
          error: 'No organization ID found',
        }
      }

      try {
        const cacheKey = `get-aco-measures-${ctx.organizationId}-${input.codeNumeric}-${input.intervalType}-${input.startDate}-${input.endDate}`
        const cachedData = await redisHelper.get(cacheKey)
        if (cachedData) {
          return {
            measures: JSON.parse(cachedData) as AvailableMeasure[],
            error: null,
          }
        }

        let measures: AvailableMeasure[] = []

        const allMeasures =
          await ctx.measureResultsService?.getAvailableMeasures(
            ctx.organizationId!
          )!

        if (ctx.selectionType === SelectionType.Organization) {
          const suffixParameter = ctx.parameters?.find(
            (x) => x.key === 'platform:SuffixSourceContainerIdentifiers'
          )
          if (suffixParameter && suffixParameter.value === 'true') {
            measures = allMeasures?.filter(
              (x) =>
                x.sourceContainerIdentifier === `${ctx.organizationId!}_EC` ||
                x.sourceContainerIdentifier === `${ctx.organizationId!}_EH`
            )
          } else {
            measures = allMeasures?.filter(
              (x) => x.sourceContainerIdentifier === ctx.organizationId!
            )
          }

          measures = Object.values(
            measures.reduce(
              (acc, curr) => {
                if (!acc[curr.measureIdentifier]) {
                  acc[curr.measureIdentifier] = curr
                }
                return acc
              },
              {} as Record<string, AvailableMeasure>
            )
          )
        } else {
          const partner = ctx.partners.find((x) => x.id === ctx.organizationId)
          measures = allMeasures?.filter((x) =>
            partner?.organizations?.some(
              (org) =>
                org.organizationId ===
                x.sourceContainerIdentifier
                  ?.replace('_EC', '')
                  ?.replace('_EH', '')
            )
          )!

          measures = Object.values(
            measures.reduce(
              (acc, curr) => {
                if (!acc[curr.measureIdentifier]) {
                  acc[curr.measureIdentifier] = curr
                }
                return acc
              },
              {} as Record<string, AvailableMeasure>
            )
          )
        }

        measures.sort((a, b) => a.measureName.localeCompare(b.measureName))

        await redisHelper.set(
          cacheKey,
          JSON.stringify(measures),
          Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
        )

        return {
          measures: measures,
          error: null,
        }
      } catch (err) {
        console.error('Error in getACOMeasures:', err)
        appInsights.trackException({
          exception:
            err instanceof Error
              ? err
              : new Error('Unknown error in getACOMeasures'),
          properties: {
            organizationId: ctx.organizationId,
            input,
          },
        })

        return {
          measures: null,
          error:
            err instanceof Error
              ? err.message
              : 'Failed to fetch measures. Please try again later.',
        }
      }
    }),

  getMVPs: protectedProcedure.input(z.object({})).query(async ({ ctx }) => {
    const cacheKey = `get-mvps-${ctx.organizationId}`

    const result = await tryCache(
      cacheKey,
      async () => {
        return await ctx.mvpService!.getMVPs()
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )

    return result
  }),

  getSubmissionGroups: protectedProcedure
    .input(z.object({}))
    .query(async ({ ctx }) => {
      return await ctx.measureResultsService!.getSubmissionGroupsByOrganization(
        {
          organizationId: ctx.organizationId!,
          isPartner: ctx.selectionType === SelectionType.Partner,
        }
      )
    }),

  getMVPSummaryResults: protectedProcedure
    .input(z.object({ year: z.number() }))
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-mvp-summary-results-${ctx.organizationId}-${input.year}`

      const result = await tryCache(
        cacheKey,
        async () => {
          return await ctx.mvpService!.getMVPSummary(input.year)
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result
    }),

  getQualityScoreByMVP: protectedProcedure
    .input(z.object({ year: z.number(), periodType: z.string() }))
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-quality-score-by-mvp-${ctx.organizationId}-${input.year}-${input.periodType}`

      const result = await tryCache(
        cacheKey,
        async () => {
          return await ctx.mvpService!.getQualityScoreByMVP(
            input.year,
            input.periodType
          )
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result
    }),

  getScoreByMeasureType: protectedProcedure
    .input(
      z.object({
        year: z.number(),
        mvp: z.string(),
        entity: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-score-by-measure-type-${ctx.organizationId}-${input.year}-${input.mvp}-${input.entity}`

      const result = await tryCache(
        cacheKey,
        async () => {
          return await ctx.mvpService!.getScoreByMeasureType(
            input.year,
            input.mvp,
            input.entity
          )
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result
    }),

  getOverallWeightedScore: protectedProcedure
    .input(z.object({ year: z.number(), mvp: z.string(), entity: z.string() }))
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-overall-weighted-score-${ctx.organizationId}-${input.year}-${input.mvp}-${input.entity}`

      const result = await tryCache(
        cacheKey,
        async () => {
          return await ctx.mvpService!.getOverallWeightedScore(
            input.year,
            input.mvp,
            input.entity
          )
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )
      return result
    }),

  getQualityMeasuresScore: protectedProcedure
    .input(
      z.object({
        year: z.number(),
        mvp: z.string(),
        entity: z.string(),
        periodType: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-quality-measures-score-${ctx.organizationId}-${input.year}-${input.mvp}-${input.entity}-${input.periodType}`

      const result = await tryCache(
        cacheKey,
        async () => {
          return await ctx.mvpService!.getQualityMeasuresScore(
            input.year,
            input.mvp,
            input.entity,
            input.periodType
          )
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result
    }),

  getProviderPerformance: protectedProcedure
    .input(z.object({ year: z.number(), mvp: z.string(), entity: z.string() }))
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-provider-performance-${ctx.organizationId}-${input.year}-${input.mvp}-${input.entity}`

      const result = await tryCache(
        cacheKey,
        async () => {
          return await ctx.mvpService!.getProviderPerformance(
            input.year,
            input.mvp,
            input.entity
          )
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result
    }),

  getQualityMeasures: protectedProcedure
    .input(z.object({ year: z.number(), mvp: z.string(), entity: z.string() }))
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-quality-measures-${ctx.organizationId}-${input.year}-${input.mvp}-${input.entity}`

      const result = await tryCache(
        cacheKey,
        async () => {
          return await ctx.mvpService!.getQualityMeasures(
            input.year,
            input.mvp,
            input.entity
          )
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result
    }),

  getIAPIMeasures: protectedProcedure
    .input(z.object({ year: z.number(), mvp: z.string(), entity: z.string() }))
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-ia-pi-measures-${ctx.organizationId}-${input.year}-${input.mvp}-${input.entity}`

      const result = await tryCache(
        cacheKey,
        async () => {
          const IAMeasures = await ctx.mvpService!.getIAPIMeasures(
            input.year,
            input.mvp,
            input.entity,
            'IA'
          )

          const PIMeasures = await ctx.mvpService!.getIAPIMeasures(
            input.year,
            input.mvp,
            input.entity,
            'PI'
          )

          return {
            IAMeasures,
            PIMeasures,
          }
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )

      return result
    }),

  getProviderDetails: protectedProcedure
    .input(z.object({ year: z.number(), providerName: z.string() }))
    .query(async ({ ctx, input }) => {
      const cacheKey = `get-provider-details-${ctx.organizationId}-${input.year}-${input.providerName}`
      const result = await tryCache(
        cacheKey,
        async () => {
          return await ctx.mvpService!.getProviderDetails(
            input.year,
            input.providerName
          )
        },
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )
      return result
    }),
})
