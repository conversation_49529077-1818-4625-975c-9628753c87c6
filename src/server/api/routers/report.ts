import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { getReports } from '@/services/reports/getReports'
import { getReportPreferences } from '@/services/reports/getReportPreferences'
import { getMeasureInsightReports } from '@/services/reports/getMeasureInsightReports'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import appInsights from '@/lib/applicationInsights'
import {
  ACOMeasuresRequest,
  Report,
  ReportRequest,
} from '@/types/reports/medisolvReport'
import { SisenseMeasureSummary } from '@/services/sisense/SisenseMeasureSummaryRepository'
import { SelectionType } from '@/enums/selectionType'
import { standardReports } from '@/data/reports'
import UserNotificationsService from '@/services/userNotifications'
import sendEmail from '@/lib/sendEmail'
import { Constants } from '@/enums/constants'

export const reportRouter = createTRPCRouter({
  // #region Manange Reports

  saveReport: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, 'Report name is required'),
        description: z.string().optional().default(''),
        reportConfig: z.string().optional(),
        userId: z.string().optional(),
        reportType: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // Create a new AzureTableStorageWrapper for the Reports table
        const tableStorageWrapper = new AzureTableStorageWrapper(
          StorageTables.Reports
        )

        // Check if a report with the same name already exists
        const existingReports = await tableStorageWrapper.queryEntities<Report>(
          tableStorageWrapper.generateODataQuery({
            PartitionKey: ctx.organizationId,
            name: input.name,
            isDeleted: false,
          })
        )

        let report: Report

        if (existingReports.length > 0) {
          // Get the existing report
          report = existingReports[0]!

          // Update the report properties
          report.description = input.description || ''
          report.reportConfig = input.reportConfig || ''
          report.lastModified = new Date().toISOString()

          // Update userId for custom reports
          if (report.isCustom) {
            report.userId = ctx.session?.uid || input.userId
          }

          // Update the entity in Azure Table Storage
          await tableStorageWrapper.updateEntity(report)
        } else {
          // Create a new report
          const newReport: Report = {
            partitionKey: ctx.organizationId!,
            rowKey: crypto.randomUUID(),
            name: input.name,
            description: input.description || '',
            reportConfig: input.reportConfig || '',
            isActive: true,
            isCustom: true,
            isFavorite: false,
            userId: ctx.session?.uid || input.userId,
            lastModified: new Date().toISOString(),
            reportType: input.reportType,
          }

          // Create the entity in Azure Table Storage
          await tableStorageWrapper.upsertEntity(newReport)

          report = newReport
        }

        return {
          success: true,
          report,
          isUpdate: existingReports.length > 0,
        }
      } catch (err) {
        console.error('Error in saveReport:', err)
        appInsights.trackException({
          exception:
            err instanceof Error
              ? err
              : new Error('Unknown error in saveReport'),
          properties: input,
        })

        return {
          success: false,
          error:
            err instanceof Error
              ? err.message
              : 'Failed to save report. Please try again later.',
        }
      }
    }),

  updateFavoriteReport: protectedProcedure
    .input(
      z.object({
        reportId: z.string(),
        isFavorite: z.boolean().default(false),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.Reports
      )

      const report = await tableStorageWrapper.getEntity<Report>(
        ctx.organizationId!,
        input.reportId
      )

      if (!report) {
        throw new Error(`Report with ID ${input.reportId} not found`)
      }

      tableStorageWrapper.updateEntity({
        ...report,
        isFavorite: input.isFavorite,
      } as Report)
    }),

  shareReport: protectedProcedure
    .input(
      z.object({
        reportId: z.string(),
        reportType: z.string(),
        users: z
          .object({
            id: z.string(),
            email: z.string(),
            name: z.string(),
          })
          .array(),
        message: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.Reports
      )

      const report = await tableStorageWrapper.getEntity<Report>(
        ctx.organizationId!,
        input.reportId
      )

      if (!report) {
        throw new Error(`Report with ID ${input.reportId} not found`)
      }

      input.users.forEach(async (user) => {
        await tableStorageWrapper.insertEntity<Report>({
          ...report,
          userId: user.id,
          sharedBy: ctx.session?.uid!,
          rowKey: crypto.randomUUID(),
          lastModified: new Date().toISOString(),
        } as Report)
      })

      const userNotificationsService = new UserNotificationsService()

      userNotificationsService.sendNotification(
        new AzureTableStorageWrapper(StorageTables.UserNotifications),
        ctx.session?.uid!,
        input.message,
        input.users.map((x) => x.id)
      )

      const parameters =
        await ctx.citCParametersService.getGlobalParametersAsync()
      const apiKey = parameters.find(
        (x) => x.key === Constants.SendGridApiKey
      )?.value!

      let host = ''
      if (process.env.VERCEL_URL) host = `https://${process.env.VERCEL_URL}`
      else host = `http://localhost:${process.env.PORT ?? 3000}`

      sendEmail(
        {
          users: input.users,
          subject: 'Medisolv Report Shared',
          templateName: 'notification',
          templateData: {
            redirectUrl: `${host}/reports/${input.reportType}?reportId=${input.reportId}`,
            actionText: 'View Report',
          },
        },
        apiKey
      )

      return { success: true }
    }),

  getReportsList: protectedProcedure.query(async () => {
    const result = await getReports()
    return result
  }),

  getUserReports: protectedProcedure.query(async ({ ctx }) => {
    const tableStorageWrapper = new AzureTableStorageWrapper(
      StorageTables.Reports
    )

    const reports = await tableStorageWrapper.queryEntities<Report>(
      tableStorageWrapper.generateODataQuery({
        PartitionKey: ctx.organizationId,
      })
    )

    const clientReports = reports.filter((x) => !x.isCustom)

    const standardReportsList = standardReports
      .filter((x) => x.isActive)
      .map((x) => ({
        ...x,
        rowKey: crypto.randomUUID(),
        partitionKey: ctx.organizationId!,
      }))

    const missingReports = standardReportsList.filter(
      (x) => !clientReports.find((y) => y.reportType === x.reportType)
    )

    if (missingReports.length) {
      await Promise.all(
        missingReports.map((x) => tableStorageWrapper.insertEntity<Report>(x))
      )

      reports.push(...missingReports)
    }

    const userReports = reports.filter(
      (report) =>
        report.isActive &&
        (!report.isCustom || report.userId === ctx.session?.uid)
    )

    return userReports
  }),

  getReportById: protectedProcedure
    .input(
      z.object({
        id: z.string().min(1, 'Report ID is required'),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        // Get the report by ID with its configuration
        const tableStorageWrapper = new AzureTableStorageWrapper(
          StorageTables.Reports
        )

        // Get the report entity from Azure Table Storage
        const report = await tableStorageWrapper.getEntity<Report>(
          ctx.organizationId!,
          input.id
        )

        if (!report) {
          throw new Error(`Report with ID ${input.id} not found`)
        }

        // Check if this is a custom report and if the user has access to it
        if (
          report.isCustom &&
          report.userId &&
          report.userId !== ctx.session?.uid
        ) {
          throw new Error(`You do not have access to this report`)
        }

        // Return the report with its configuration
        return report
      } catch (error) {
        console.error(`Error fetching report with ID ${input.id}:`, error)
        throw new Error(
          `Failed to fetch report: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      }
    }),

  saveReportsStatus: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        reportType: z.string(),
        status: z.boolean(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      var tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.Reports
      )

      let report = await tableStorageWrapper.getEntity<Report>(
        ctx.organizationId!,
        input.id
      )

      if (!report) {
        throw new Error(`Report with type ${input.reportType} not found`)
      }

      report.isActive = input.status
      report.lastModified = new Date().toISOString()
      await tableStorageWrapper.updateEntity(report)
    }),

  updateReportOwnership: protectedProcedure
    .input(
      z.object({
        reportId: z.string(),
        userId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // TODO: Enhancement* Move into trpc context
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.Reports
      )

      let report = await tableStorageWrapper.getEntity<Report>(
        ctx.organizationId!,
        input.reportId
      )

      if (report) {
        // Only update if the report is custom and doesn't already have a userId
        if (report.isCustom && !report.userId) {
          // Update the userId field
          report.userId = input.userId

          // Update the lastModified timestamp to reflect the change
          report.lastModified = new Date().toISOString()

          // Update the entity in Azure Table Storage
          await tableStorageWrapper.updateEntity(report)

          return { success: true }
        }
      }

      return { success: false }
    }),

  deleteReport: protectedProcedure
    .input(
      z.object({
        reportId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const tableStorageWrapper = new AzureTableStorageWrapper(
          StorageTables.Reports
        )

        await tableStorageWrapper.deleteEntity(
          ctx.organizationId!,
          input.reportId
        )

        return { success: true }
      } catch (error) {
        console.error(`Error deleting report with ID ${input.reportId}:`, error)

        // If the error is about permissions, rethrow it
        if (error instanceof Error && error.message.includes('permission')) {
          throw error
        }

        // Otherwise, return a generic error
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      }
    }),

  getMeasureInsightReports: protectedProcedure.query(async ({ ctx }) => {
    const result = await getMeasureInsightReports(
      ctx.organizationId!,
      ctx.selectionType!
    )
    return result
  }),

  // #endregion

  getACOMeasurePerformanceByUniverse: protectedProcedure
    .input(
      z.object({
        intervalType: z.string(),
        startDate: z.date(),
        endDate: z.date(),
        measureNames: z.string().array(),
        sourceContainerIdentifier: z.string(),
        universe: z.string(),
        hideEmptyIndicators: z.boolean().optional(),
        runtime: z.number().optional(),
      }) as z.ZodType<ReportRequest>
    )
    .query(
      async ({ ctx, input }): Promise<SisenseMeasureSummary[] | undefined> => {
        try {
          // Use the ACOPerformanceAnalysisService from the context
          return await ctx.acoPerformanceAnalysisService?.getACOMeasurePerformanceByUniverse(
            input
          )
        } catch (err) {
          console.error('Error in getACOMeasurePerformanceByUniverse:', err)
          appInsights.trackException({
            exception:
              err instanceof Error
                ? err
                : new Error(
                    'Unknown error in getACOMeasurePerformanceByUniverse'
                  ),
            properties: input,
          })

          return []
        }
      }
    ),

  getACOMeasurePerformanceByOrgCode: protectedProcedure
    .input(
      z.object({
        intervalType: z.string(),
        startDate: z.date(),
        endDate: z.date(),
        measureNames: z.string().array(),
        sourceContainerIdentifier: z.string(),
        code: z.number(),
        hideEmptyIndicators: z.boolean().optional(),
        runtime: z.number().optional(),
      }) as z.ZodType<ReportRequest>
    )
    .query(
      async ({ ctx, input }): Promise<SisenseMeasureSummary[] | undefined> => {
        try {
          // Use the ACOPerformanceAnalysisService from the context
          return await ctx.acoPerformanceAnalysisService?.getACOMeasurePerformanceByOrgCode(
            input
          )
        } catch (err) {
          console.error('Error in getACOMeasurePerformanceByOrgCode:', err)
          appInsights.trackException({
            exception:
              err instanceof Error
                ? err
                : new Error(
                    'Unknown error in getACOMeasurePerformanceByOrgCode'
                  ),
            properties: input,
          })
          return []
        }
      }
    ),

  getACOMeasures: protectedProcedure
    .input(
      z.object({
        codeNumeric: z.string().min(1, 'codeNumeric is required'),
        intervalType: z.string(),
        startDate: z.date(),
        endDate: z.date(),
      }) as z.ZodType<ACOMeasuresRequest>
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.organizationId) {
        appInsights.trackException({
          exception: new Error(
            `No organizationId found for user ${ctx.session?.uid}`
          ),
          properties: {
            ...ctx,
          },
        })

        return {
          measures: null,
          error: 'No organization ID found',
        }
      }

      try {
        // Use the ACOMeasureService from the context
        const measures = await ctx.acoMeasureService!.getACOMeasures(input)

        return {
          measures,
          error: null,
        }
      } catch (err) {
        console.error('Error in getACOMeasures:', err)
        appInsights.trackException({
          exception:
            err instanceof Error
              ? err
              : new Error('Unknown error in getACOMeasures'),
          properties: {
            organizationId: ctx.organizationId,
            input,
          },
        })

        return {
          measures: null,
          error:
            err instanceof Error
              ? err.message
              : 'Failed to fetch measures. Please try again later.',
        }
      }
    }),

  getMVPs: protectedProcedure.input(z.object({})).query(async ({ ctx }) => {
    return await ctx.mvpService!.getMVPs()
  }),

  getSubmissionGroups: protectedProcedure
    .input(z.object({}))
    .query(async ({ ctx }) => {
      return await ctx.measureResultsService!.getSubmissionGroupsByOrganization(
        {
          organizationId: ctx.organizationId!,
          isPartner: ctx.selectionType === SelectionType.Partner,
        }
      )
    }),

  getMVPSummaryResults: protectedProcedure
    .input(z.object({ year: z.number() }))
    .query(async ({ ctx, input }) => {
      return await ctx.mvpService!.getMVPSummary(input.year)
    }),

  getQualityScoreByMVP: protectedProcedure
    .input(z.object({ year: z.number(), periodType: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.mvpService!.getQualityScoreByMVP(
        input.year,
        input.periodType
      )
    }),

  getScoreByMeasureType: protectedProcedure
    .input(
      z.object({
        year: z.number(),
        mvp: z.string(),
        entity: z.string(),
        measureType: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.mvpService!.getScoreByMeasureType(
        input.year,
        input.mvp,
        input.entity,
        input.measureType
      )
    }),

  getOverallWeightedScore: protectedProcedure
    .input(z.object({ year: z.number(), mvp: z.string(), entity: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.mvpService!.getOverallWeightedScore(
        input.year,
        input.mvp,
        input.entity
      )
    }),

  getQualityMeasuresScore: protectedProcedure
    .input(
      z.object({
        year: z.number(),
        mvp: z.string(),
        entity: z.string(),
        periodType: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.mvpService!.getQualityMeasuresScore(
        input.year,
        input.mvp,
        input.entity,
        input.periodType
      )
    }),

  getProviderPerformance: protectedProcedure
    .input(z.object({ year: z.number(), mvp: z.string(), entity: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.mvpService!.getProviderPerformance(
        input.year,
        input.mvp,
        input.entity
      )
    }),

  getQualityMeasures: protectedProcedure
    .input(z.object({ year: z.number(), mvp: z.string(), entity: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.mvpService!.getQualityMeasures(
        input.year,
        input.mvp,
        input.entity
      )
    }),

  getIAPIMeasures: protectedProcedure
    .input(z.object({ year: z.number(), mvp: z.string(), entity: z.string() }))
    .query(async ({ ctx, input }) => {
      const IAMeasures = await ctx.mvpService!.getIAPIMeasures(
        input.year,
        input.mvp,
        input.entity,
        'IA'
      )

      const PIMeasures = await ctx.mvpService!.getIAPIMeasures(
        input.year,
        input.mvp,
        input.entity,
        'PI'
      )

      return {
        IAMeasures,
        PIMeasures,
      }
    }),

  getProviderDetails: protectedProcedure
    .input(z.object({ year: z.number(), providerName: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.mvpService!.getProviderDetails(
        input.year,
        input.providerName
      )
    }),
})
