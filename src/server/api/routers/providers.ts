import { SelectionType } from '@/enums/selectionType'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { ManageUserRole } from '@/types/manageUserRole'
import dayjs from 'dayjs'
import { hasLimitedAccessForOrganization } from '@/lib/hasLimitedAccessForOrganization'
import { manageUserRolesQuery } from '@/services/adm/users/manageUserRolesQuery'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { Provider } from '@/types/provider'
import { Partner } from '@/types/partner'
import { getPartnersByUserCommand } from '@/services/partners/getPartnersByUserCommand'
import { OrganizationRole } from '@/types/organizationRole'
import appInsights from '@/lib/applicationInsights'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { z } from 'zod'
import { sanitizeProviderName } from '@/lib/sanitizeProviderName'
import { calculateCurrentQuarter } from '@/lib/calculateCurrentQuarter'
import { CitcUserRoles } from '@/shared/roles'

export const providersRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
        enabled: z.boolean(),
        startDate: z.date().optional(),
        endDate: z.date().optional()
      })
    )
    .query(async ({ ctx, input }): Promise<Provider[]> => {
      try {
        if (
          !ctx.primaryMeasureTypes.includes(input.primaryMeasureType) ||
          !input.enabled
        ) {
          return []
        }

        if (
          input.primaryMeasureType ===
          PrimaryMeasureTypeConstants.HospitalMeasures
        ) {
          return []
        }

        const isPartner = ctx.selectionType === SelectionType.Partner
         
         const startDate = dayjs(input.startDate)
         const endDate = dayjs(input.endDate) 

        let userRoles: ManageUserRole[] = []
        let canAccessibleRole: string[] = []

        // TODO: is this fine for partners?
        const orgRoles: OrganizationRole[] = [
          {
            organizationId: ctx.organizationId,
            roles: Array.isArray(ctx.session?.role)
              ? ctx.session?.role
              : [ctx.session?.role!],
          },
        ]
        if (hasLimitedAccessForOrganization(orgRoles, ctx.organizationId!)) {
          userRoles = await manageUserRolesQuery(ctx.measureResultsService!, {
            organizationId: ctx.organizationId!,
            userId: ctx.session?.uid!,
            isPartner: isPartner,
            organizationType: EntityOrganizationTypeConstants.ProviderLevel,
          })

          if (userRoles.length === 0) {
            return []
          }

          canAccessibleRole = userRoles
            .filter((x) => x.canAccess)
            .map((x) => x.entityCode)
            .filter((x): x is string => x != null && x !== undefined)
        }

        let providers: Provider[] = []

        if (isPartner) {
          const partners: Partner[] = []
          const fetchedPartners = await getPartnersByUserCommand(
            ctx.session?.uid!,
            ctx.session?.accessToken!
          )

          if (fetchedPartners != null) {
            partners.push(
              ...fetchedPartners.filter((x) =>
                x.roles?.includes(CitcUserRoles.PLATFORM_READY)
              )
            )
          }

          const partner = partners.find((x) => x.id == ctx.organizationId)

          for (const item of partner?.organizations || []) {
            const providerBySubmissionGrp =
              await ctx.measureResultsService!.getActiveProvidersBySubmissionGroup(
                {
                  organizationId: item.organizationId!,
                  submissionGroupId: '*',
                  isPartner: false,
                  startDate: startDate,
                  endDate: endDate,
                }
              )

            const existingNPIs = providers.map((x) => x.npi)

            // TODO: is this right?
            providerBySubmissionGrp.forEach((x) => {
              x.providerName = sanitizeProviderName(x.providerName)
            })

            providers.push(
              ...providerBySubmissionGrp.filter(
                (x) => !existingNPIs.includes(x.npi)
              )
            )
          }
        } else {
          providers =
            await ctx.measureResultsService!.getActiveProvidersBySubmissionGroup(
              {
                organizationId: ctx.organizationId!,
                submissionGroupId: '*',
                isPartner: isPartner,
                startDate: startDate,
                endDate: endDate,
              }
            )

          // TODO: is this right?
          providers.forEach((x) => {
            x.providerName = sanitizeProviderName(x.providerName)
          })
        }

        providers = [...new Set(providers)].sort((a, b) =>
          a.providerName.localeCompare(b.providerName)
        )

        if (hasLimitedAccessForOrganization(orgRoles, ctx.organizationId!)) {
          providers = providers.filter((x) => canAccessibleRole.includes(x.npi))
        }

        return providers
      } catch (error) {
        console.log(error)
        if (error instanceof Error) {
          appInsights.trackException({ exception: error })
        }
        throw error
      }
    }),
})
