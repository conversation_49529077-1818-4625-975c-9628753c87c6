import { SavedFilterModel } from '@/types/savedFilterModel'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { SelectionType } from '@/enums/selectionType'
import { z } from 'zod'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { savedUserFiltersQuery } from '@/services/filter/savedUserFiltersQuery'
import { filterQuery } from '@/services/filter/filterQuery'
import { deleteFilterQuery } from '@/services/filter/deleteFilterQuery'
import appInsights from '@/lib/applicationInsights'

type FilterMetaData = {
  page: string
  measures: string[]
  hospitals: string[]
  groups: string[]
  providers: string[]
  organizations: string[]
  facilities: string[]
  measureType: string
}

export const filtersRouter = createTRPCRouter({
  getSavedFilters: protectedProcedure
    .input(
      z.object({
        path: z.string(),
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
      })
    )
    .query(async ({ ctx, input }): Promise<SavedFilterModel[]> => {
      const result: SavedFilterModel[] = []

      if (!ctx.organizationId) {
        appInsights.trackException({
          exception: new Error(
            `No organizationId found for user ${ctx.session?.uid}`
          ),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      const savedFilters = await savedUserFiltersQuery(
        ctx.organizationId,
        ctx.session?.uid!,
        ctx.selectionType!
      )

      const page = input.path.includes('measure') ? 'measures' : 'scorecards'

      for (const filter of savedFilters) {
        try {
          const metadata: FilterMetaData = JSON.parse(filter.filterMetadata)

          if (metadata.page != page) continue

          if (metadata.measureType === input.primaryMeasureType) {
            const savedFilterModel: SavedFilterModel = {
              filterName: filter.filterName,
              hospitals: metadata.hospitals,
              groups: metadata.groups,
              id: filter.rowKey,
              measures: metadata.measures,
              providers: metadata.providers,
              organizations: metadata.organizations,
              page: metadata.page,
              isViewFilter: filter.filterName.includes(ctx.session?.uid || ''),
            }

            result.push(savedFilterModel)
          }
        } catch (Exception) {
          continue
          //throw;
        }
      }

      return result
    }),
  saveFilters: protectedProcedure
    .input(
      z.object({
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
        page: z.string(),
        filterName: z.string(),
        hospitals: z.array(z.string()),
        measures: z.array(z.string()),
        groups: z.array(z.string()),
        providers: z.array(z.string()),
        organizations: z.array(z.string()),
        facilities: z.array(z.string()),
      })
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.organizationId) {
        appInsights.trackException({
          exception: new Error(
            `No organizationId found for user ${ctx.session?.uid}`
          ),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      const result = await filterQuery(
        {
          hospitals: input.hospitals,
          measures: input.measures,
          groups: input.groups,
          providers: input.providers,
          organizations: input.organizations,
          page: input.page,
          filterName: input.filterName,
          facilities: input.facilities,
          userId: ctx.session?.uid!,
          partnerId: ctx.organizationId!,
          isPartner: ctx.selectionType === SelectionType.Partner,
          measureType: input.primaryMeasureType,
        },
        ctx.organizationId
      )

      return result
    }),
  deleteFilter: protectedProcedure
    .input(
      z.object({
        filterId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const result = await deleteFilterQuery({
        filterId: input.filterId,
        isPartner: ctx.selectionType === SelectionType.Partner,
        organizationId: ctx.organizationId!,
        userId: ctx.session?.uid!,
      })

      return result
    }),
})
