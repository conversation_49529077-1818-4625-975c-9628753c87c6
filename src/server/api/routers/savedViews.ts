import { createTRPCRouter, protectedProcedure } from '../trpc'
import { z } from 'zod'
import getViews from '@/services/userViews/getViews'
import { updateFavoriteView } from '@/services/userViews/updateFavoriteView'
import saveView from '@/services/userViews/saveView'
import deleteView from '@/services/userViews/deleteView'
import shareView from '@/services/userViews/shareView'
import updateDefaultView from '@/services/userViews/updateDefaultView'
import appInsights from '@/lib/applicationInsights'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import UserNotificationsService from '@/services/userNotifications'
import dayjs from 'dayjs'
import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'

export const savedViewsRouter = createTRPCRouter({
  getSavedViews: protectedProcedure
    .input(
      z.object({
        currentPage: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.organizationId || !ctx.selectionType) {
        const message = !ctx.organizationId
          ? 'No organizationId provided'
          : !ctx.selectionType
            ? 'No selectionType provided'
            : 'unnkown issue'

        appInsights.trackException({
          exception: new Error(message),
          properties: {
            ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
          },
        })

        return []
      }

      const measureTypes =
        await ctx.measureResultsService!.getPrimaryMeasureTypes({
          organizationId: ctx.organizationId,
          selectionType: ctx.selectionType,
          isDevelopementEnv: false,
        })

      // TODO: Enhancement* Move into trpc context
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.SavedViews
      )

      const result = await getViews(
        tableStorageWrapper,
        ctx.organizationId,
        ctx.session?.uid!,
        input.currentPage,
        ctx.selectionType,
        measureTypes
      )

      return result ?? null
    }),
  updateFavoriteView: protectedProcedure
    .input(
      z.object({
        viewId: z.string(),
        isFavorite: z.boolean().default(false),
        page: z.string(),
        primaryMeasureType: z.nativeEnum(PrimaryMeasureTypeConstants),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // TODO: Enhancement* Move into trpc context
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.SavedViews
      )

      const result = await updateFavoriteView(
        tableStorageWrapper,
        ctx.session?.uid!,
        ctx.organizationId!,
        input.viewId,
        input.isFavorite,
        input.page,
        input.primaryMeasureType
      )

      return result ?? null
    }),

  saveView: protectedProcedure
    .input(
      z.object({
        viewName: z.string().min(1, 'view name is required'),
        page: z.string(),
        periodType: z.string(),
        settings: z.string(),
        isCurrentView: z.boolean(),
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // TODO: Enhancement* Move into trpc context
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.SavedViews
      )

      const result = await saveView(
        tableStorageWrapper,
        ctx.session?.uid!,
        ctx.organizationId!,
        input.viewName,
        input.page,
        input.periodType,
        input.settings,
        input.isCurrentView,
        [dayjs(input.startDate).utc(), dayjs(input.endDate).utc()]
      )

      return result ?? null
    }),

  deleteView: protectedProcedure
    .input(
      z.object({
        viewId: z.string(),
        page: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // TODO: Enhancement* Move into trpc context
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.SavedViews
      )

      const result = await deleteView(
        tableStorageWrapper,
        ctx.session?.uid!,
        ctx.organizationId!,
        input.viewId,
        input.page
      )

      return result ?? null
    }),

  shareView: protectedProcedure
    .input(
      z.object({
        viewId: z.string(),
        userIds: z.array(z.string()),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // TODO: Enhancement* Move into trpc context
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.SavedViews
      )

      const result = await shareView(
        tableStorageWrapper,
        ctx.session?.uid!,
        input.viewId,
        input.userIds,
        ctx.session?.given_name!
      )

      const userNotificationsService = new UserNotificationsService()

      const message = `${ctx.session?.given_name} has shared view with you :- `

      await userNotificationsService.sendNotification(
        // TODO: Enhancement* Move into trpc context
        new AzureTableStorageWrapper(StorageTables.UserNotifications),
        ctx.session?.uid!,
        message,
        input.userIds
      )

      return result ?? null
    }),

  markAsDefault: protectedProcedure
    .input(
      z.object({
        viewId: z.string(),
        page: z.string(),
        measureType: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const tableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.SavedViews
      )

      const result = await updateDefaultView(
        tableStorageWrapper,
        ctx.session?.uid!,
        ctx.organizationId!, // Make sure this is being passed
        input.viewId,
        input.page,
        input.measureType
      )

      return result ?? null
    }),
})
