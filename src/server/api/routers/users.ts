import { tryCache } from '@/lib/redis'
import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc'
import { env } from '@/env'
import CitCUsersService from '@/services/citc/citCUsers'
import { User } from '@/types/user'
import appInsights from '@/lib/applicationInsights'
import { OrganizationRole } from '@/types/organizationRole'
import CitCOrganizationService from '@/services/citc/citCOrganizations'
import { z } from 'zod'
import { CitcUserProfleUpdateOptions } from '@/types/citcUserProfileUpdateOptions'
import CitCParametersService from '@/services/citc/citcParameters'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { SelectionType } from '@/enums/selectionType'
import { Constants } from '@/enums/constants'

export const usersRouter = createTRPCRouter({
  getOrganizationUsers: protectedProcedure.query(async ({ ctx }) => {
    const cacheKey = `${ctx.organizationId}-users`
    const orgUsers = await tryCache(
      cacheKey,
      async () => {
        if (!ctx.session?.accessToken) {
          throw new Error('Access token is required.')
        }
        if (!ctx.organizationId) {
          appInsights.trackException({
            exception: new Error(
              `No organizationId found for user ${ctx.session!.uid}`
            ),
            properties: {
              ...ctx, // TODO: if entire ctx cannot be displayed in AI, remove some keys here
            },
          })

          return []
        }
        let parameters: SimplifiedParameter[] = []

        const parameterservice = new CitCParametersService(
          ctx.session?.accessToken!
        )

        if (ctx.selectionType === SelectionType.Organization) {
          parameters = await parameterservice.getOrganizationParametersAsync(
            ctx.organizationId!
          )
        } else {
          parameters = await parameterservice.getPartnerParametersAsync(
            ctx.organizationId!
          )
        }
        const includeUser = parameters.find(
          (x) => x.key === Constants.IncludeMedisolvUser
        )?.value
        const includeMedisolvUser = includeUser?.toLowerCase() == 'true'

        const citCUsersService = new CitCUsersService(ctx.session!.accessToken!)

        const users = await citCUsersService.GetAllUsersByOrganizationAsync(
          ctx.organizationId
        )

        // Filter out Medisolv users if IncludeMedisolvUser is false
        return includeMedisolvUser
          ? users
          : users.filter(
              (user) => user.tenantName?.toLowerCase() !== 'medisolv'
            )
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )

    return orgUsers ?? ([] as User[])
  }),
  getOrgRolesByUser: protectedProcedure.query(async ({ ctx }) => {
    return [...(ctx.partnerRoles ?? []), ...(ctx.organizationRoles ?? [])]
  }),

  getUserAcceses: protectedProcedure.query(async ({ ctx }) => {
    const roles = ctx.session?.['role:Organization'] as string[]
    ctx.organizations

    const organizationService = new CitCOrganizationService(
      ctx.session!.accessToken!
    )

    let userAccesses = [] as OrganizationRole[]
    if (roles.length) {
      for (const role of roles) {
        const orgRole = JSON.parse(role) as any
        const organizationDetails =
          await organizationService.getOrganizationDetailsAsync(orgRole.Id)

        userAccesses.push({
          organizationId: orgRole.Id,
          organizationName: organizationDetails?.organizationName,
          roles: orgRole.Roles,
        })
      }
    }

    return userAccesses
  }),

  getUserInfo: protectedProcedure.query(async ({ ctx }) => {
    const userService = new CitCUsersService(ctx.session!.accessToken!)
    const userInfo = await userService.getUserDetailsAsync(ctx.session!.uid!)
    return userInfo
  }),

  updateUserInfo: protectedProcedure
    .input(
      z.object({
        displayName: z.string(),
        emailAddress: z.string(),
        cellPhoneNumber: z.string().optional().nullable(),
        workPhoneNumber: z.string().optional().nullable(),
        homePhoneNumber: z.string().optional().nullable(),
        profileImageUrl: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const userService = new CitCUsersService(ctx.session!.accessToken!)
      const userInfo = await userService.updateUserProfileAsync({
        userId: ctx.session!.uid!,
        displayName: input.displayName,
        emailAddress: input.emailAddress,
        cellPhone: input.cellPhoneNumber,
        workPhone: input.workPhoneNumber,
        homePhone: input.homePhoneNumber,
        profileImage: input.profileImageUrl,
      } as CitcUserProfleUpdateOptions)

      return userInfo
    }),
})
