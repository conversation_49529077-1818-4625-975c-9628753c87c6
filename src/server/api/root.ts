import { pagesRouter } from '@/server/api/routers/pages'
import { createCallerFactory, createTRPCRouter } from '@/server/api/trpc'
import { organizationsRouter } from './routers/organizations'
import { partnersRouter } from './routers/partners'
import { applicationsRouter } from './routers/applications'
import { notificationsRouter } from './routers/notifications'
import { loadStatusInfoRouter } from './routers/loadStatusInfo'
import { measuresRouter } from './routers/measures'
import { patientsRouter } from './routers/patient'
import { reportRouter } from './routers/report'
import { authRouter } from './routers/auth'
import { savedViewsRouter } from './routers/savedViews'
import { filtersRouter } from './routers/filter'
import { providersRouter } from './routers/providers'
import { facilitiesRouter } from './routers/facilities'
import { submissionGroupsRouter } from './routers/submissionGroups'
import { noticeRouter } from './routers/notice'
import { usersRouter } from './routers/users'
import { adminRouter } from './routers/admin'
import { encountersRouter } from './routers/encounters'
import { explorerRouter } from './routers/explorer'
import { scorecardsRouter } from './routers/scorecards'
import { organizationTypeRouter } from './routers/organizationType'
import { organizationPreferencesRouter } from './routers/organizationPreferences'
import { contactListRouter } from './routers/contacts'
/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  pages: pagesRouter,
  applications: applicationsRouter,
  notifications: notificationsRouter,
  organizations: organizationsRouter,
  partners: partnersRouter,
  providers: providersRouter,
  facilities: facilitiesRouter,
  submissionGroups: submissionGroupsRouter,
  loadStatusInfo: loadStatusInfoRouter,
  measures: measuresRouter,
  patients: patientsRouter,
  encounters: encountersRouter,
  report: reportRouter,
  auth: authRouter,
  savedViews: savedViewsRouter,
  filters: filtersRouter,
  notices: noticeRouter,
  users: usersRouter,
  explorer: explorerRouter,
  admin: adminRouter,
  scorecards: scorecardsRouter,
  organizationType: organizationTypeRouter,
  organizationPreferences: organizationPreferencesRouter,
  contacts: contactListRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter)
