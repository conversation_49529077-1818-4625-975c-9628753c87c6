'use server'

import { auth, signOut } from '@/auth'
import { env } from '@/env'
import { redirect } from 'next/navigation'

const logout = async () => {
  const wellKnown = await fetch(env.NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG!).then(
    (res) => res.json()
  )

  const endSessionUrl = wellKnown.end_session_endpoint

  const session = await auth()

  const idToken = session?.idToken

  // Clear the local session with AuthJS
  await signOut({ redirect: false })

  if (!idToken) {
    // Handle missing ID token gracefully
    return redirect(env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT)
  }

  redirect(
    `${endSessionUrl}?id_token_hint=${idToken}&post_logout_redirect_uri=${encodeURIComponent(env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT)}`
  )
}

export default logout
