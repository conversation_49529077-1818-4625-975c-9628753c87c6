'use server'

import { auth, signOut, tokenCache } from '@/auth'
import { env } from '@/env'
import { redirect } from 'next/navigation'
import { cookies } from 'next/headers'

const logout = async () => {
  console.log('🚪 Starting logout process...')

  const wellKnown = await fetch(env.NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG!).then(
    (res) => res.json()
  )

  const endSessionUrl = wellKnown.end_session_endpoint
  const session = await auth()
  const idToken = session?.idToken

  console.log('🔍 Session data:', {
    hasSession: !!session,
    hasIdToken: !!idToken,
    uid: session?.uid,
    endSessionUrl
  })

  // Clear token cache for this user
  if (session?.uid) {
    console.log('🗑️ Clearing token cache for user:', session.uid)
    tokenCache.delete(session.uid)
  }

  // Clear all token cache entries (more aggressive approach)
  console.log('🗑️ Clearing all token cache entries')
  tokenCache.clear()

  // Clear the local session with AuthJS
  console.log('🔐 Calling NextAuth signOut...')
  await signOut({ redirect: false })

  // Clear all auth-related cookies
  const cookieStore = cookies()
  const allCookies = cookieStore.getAll()

  console.log('🍪 Found cookies:', allCookies.map(c => c.name))

  allCookies.forEach(cookie => {
    if (cookie.name.startsWith('next-auth') ||
      cookie.name.includes('token') ||
      cookie.name === 'x-organization-id' ||
      cookie.name.includes('session')) {
      console.log('🗑️ Deleting cookie:', cookie.name)
      cookieStore.delete({
        name: cookie.name,
        path: '/',
        domain: cookie.domain
      })
    }
  })

  if (!idToken) {
    console.log('⚠️ No ID token found, redirecting to signout URL directly')
    return redirect(env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT)
  }

  const logoutUrl = `${endSessionUrl}?id_token_hint=${idToken}&post_logout_redirect_uri=${encodeURIComponent(env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT)}`
  console.log('🌐 Redirecting to OIDC logout URL:', logoutUrl)

  redirect(logoutUrl)
}

export default logout
