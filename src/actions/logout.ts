'use server'

import { auth, signOut, tokenCache, loggedOutUsers } from '@/auth'
import { env } from '@/env'
import { redirect } from 'next/navigation'
import { cookies } from 'next/headers'

const logout = async () => {
  console.log('🚪 Starting logout process...')

  const wellKnown = await fetch(env.NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG!).then(
    (res) => res.json()
  )

  const endSessionUrl = wellKnown.end_session_endpoint
  const session = await auth()
  const idToken = session?.idToken

  console.log('🔍 Session data:', {
    hasSession: !!session,
    hasIdToken: !!idToken,
    uid: session?.uid,
    endSessionUrl
  })

  // Add user to logged out users set to prevent re-authentication
  if (session?.uid) {
    console.log('🚫 Adding user to logged out set:', session.uid)
    loggedOutUsers.add(session.uid)
  }

  // Also try to get user ID from access token
  if (session?.accessToken) {
    try {
      const payload = JSON.parse(Buffer.from(session.accessToken.split('.')[1]!, 'base64').toString())
      const userId = payload.sub || payload.jti || payload.uid
      if (userId) {
        console.log('🚫 Adding user ID from token to logged out set:', userId)
        loggedOutUsers.add(userId)
      }
    } catch (e) {
      console.warn('Could not extract user ID from access token:', e)
    }
  }

  // Clear token cache for this user
  if (session?.uid) {
    console.log('🗑️ Clearing token cache for user:', session.uid)
    tokenCache.delete(session.uid)
  }

  // Clear all token cache entries (more aggressive approach)
  console.log('🗑️ Clearing all token cache entries')
  tokenCache.clear()

  // Clear the local session with AuthJS
  console.log('🔐 Calling NextAuth signOut...')
  await signOut({ redirect: false })

  // Clear all auth-related cookies
  const cookieStore = await cookies()
  const allCookies = cookieStore.getAll()

  console.log('🍪 Found cookies:', allCookies.map((c: any) => c.name))

  // Clear specific auth cookies
  const authCookieNames = [
    'next-auth.session-token',
    'next-auth.csrf-token',
    'next-auth.callback-url',
    'next-auth.pkce.code_verifier',
    'x-organization-id'
  ]

  authCookieNames.forEach(cookieName => {
    if (cookieStore.has(cookieName)) {
      console.log('🗑️ Deleting cookie:', cookieName)
      cookieStore.delete(cookieName)
    }
  })

  // Also try to delete any cookies that match our patterns
  allCookies.forEach((cookie: any) => {
    if (cookie.name.startsWith('next-auth') ||
      cookie.name.includes('token') ||
      cookie.name === 'x-organization-id' ||
      cookie.name.includes('session')) {
      console.log('🗑️ Deleting cookie:', cookie.name)
      cookieStore.delete(cookie.name)
    }
  })

  if (!idToken) {
    console.log('⚠️ No ID token found, redirecting to signout URL directly')
    return redirect(env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT)
  }

  const logoutUrl = `${endSessionUrl}?id_token_hint=${idToken}&post_logout_redirect_uri=${encodeURIComponent(env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT)}`
  console.log('🌐 Redirecting to OIDC logout URL:', logoutUrl)

  redirect(logoutUrl)
}

export default logout
