'use server'

import { redirect, RedirectType } from 'next/navigation'

type ParamKeys = 'orgId' | 'isPartner' // TODO: Use params from types?

/** @deprecated */
export const updateParams = async (
  url: string,
  params: Record<ParamKeys, string | number | boolean>
) => {
  const currentUrl = new URL(url)
  const searchParams = currentUrl.searchParams

  // Set or update the parameters
  for (const [key, value] of Object.entries(params) as [
    ParamKeys,
    string | number | boolean,
  ][]) {
    // Directly set the values now that `key` is ensured to be of `Param<PERSON><PERSON>s` type
    if (value !== undefined && value !== null) {
      searchParams.set(key, String(value))
    }
  }

  // Reconstruct the full URL path with updated query params
  const newUrl = `${currentUrl.pathname}?${searchParams.toString()}`

  // Redirect to the new URL
  redirect(newUrl, RedirectType.replace)
}
