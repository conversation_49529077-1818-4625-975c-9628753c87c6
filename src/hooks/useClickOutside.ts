import { RefObject, useEffect } from 'react'

export const useClickOutside = (
  ref: RefObject<HTMLDivElement | null>,
  callback: (event: MouseEvent) => void
) => {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Get the window width and click position
      const windowWidth = document.documentElement.clientWidth
      const clickX = event.clientX

      // Check if click is on scrollbar (occurs when click is past the viewport width)
      const isClickOnScrollbar = clickX >= windowWidth

      if (
        ref.current &&
        !ref.current.contains(event.target as Node) &&
        !isClickOnScrollbar
      ) {
        callback(event)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [ref, callback])
}
