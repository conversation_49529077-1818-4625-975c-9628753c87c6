import { useState, useEffect, useMemo } from 'react'
import { CitcUserRoles } from '@/shared/roles'
import { useUserSessionStore } from '@/stores/userSession'
import { hasPermission } from '@/lib/permissions'

export const useHasPermission = () => {
  const [isLoaded, setIsLoaded] = useState(false)
  const permissions = useUserSessionStore(state => state.permissions)
  
  useEffect(() => {
    if (permissions.size > 0) {
      setIsLoaded(true)
    }
  }, [permissions])

  const checkPermission = useMemo(() => (role: CitcUserRoles) => {
    if (!isLoaded) {
      return false
    }
    return hasPermission(permissions, role)
  }, [isLoaded, permissions])

  return { checkPermission, isLoaded }
}