export enum CitcUserRoles {
  USER = 'User',
  ADMINISTRATOR = 'Administrator',
  SITE_ADMINISTRATOR = 'Site Administrator',
  ORGANIZATION_ADMINISTRATOR = 'Organization Administrator',
  USER_ADMINISTRATOR = 'User Administrator',
  NETWORK_ADMINISTRATOR = 'Network Administrator',
  REPORT_ADMINISTRATOR = 'Report Administrator',
  DASHBOARD_ADMINISTRATOR = 'Dashboard Administrator',
  CONFIGURATION_MANAGER = 'Configuration Manager',
  DASHBOARD_USER = 'Dashboard User',
  LIMITED_USER = 'Limited User',
  PATIENT_EXPLORER = 'Patient Explorer',
  REPORTING = 'Reporting',
  APPLICATION_ADMINISTRATOR = 'Application Administrator',
  ADVANCED_FEATURES = 'Advanced Features',
  SCORECARD_MANAGER = 'Scorecard Manager',
  PATIENT_ADMINISTRATOR = 'Patient Administrator',
  PATIENT_DETAIL_ACCESS = 'Patient Detail Access',
  PROVIDER_DETAILS_ACCESS = 'ProviderDetailsAccess',
  DEFAULT_ORGANIZATION = 'Default Organization',
}

export type CitcUserRole = keyof typeof CitcUserRoles
