import { Dayjs } from 'dayjs'

/**
 * Calculates the current quarter (1-4) based on the provided date
 *
 * @param today - A Dayjs object representing the date to calculate the quarter for
 * @returns {number} A number from 1-4 representing the calendar quarter
 *
 * @example
 * // Returns 1 for January-March
 * calculateCurrentQuarter(dayjs('2024-01-15'))
 *
 * @example
 * // Returns 4 for October-December
 * calculateCurrentQuarter(dayjs('2024-12-25'))
 */
export const calculateCurrentQuarter = (today: Dayjs) => {
  const month = today.month() + 1
  return Math.ceil(month / 3)
}
