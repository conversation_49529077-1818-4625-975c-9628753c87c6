import { type Dayjs } from 'dayjs'

/**
 * Calculates the time duration between two dates in minutes, and formats it as a string.
 *
 * @param {Dayjs} startDate The start date
 * @param {Dayjs} endDate The end date
 * @returns {string} A string representing the time duration in minutes, e.g. '1.23m'
 */
export const calculateTimeDuration = (
  startDate: Dayjs,
  endDate: Dayjs
): string => {
  const durationMinutes = startDate.diff(endDate, 'minutes', true)
  return `${durationMinutes.toFixed(2)}m`
}
