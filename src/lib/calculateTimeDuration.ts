import { type Dayjs } from 'dayjs'

/**
 * Calculates the time duration between two dates in minutes, and formats it as a string.
 *
 * @param {Dayjs} startDate The start date
 * @param {Dayjs} endDate The end date
 * @returns {string} A string representing the time duration in minutes, e.g. '1.23m'
 */
export const calculateTimeDuration = (
  startDate: Dayjs,
  endDate: Dayjs
): string => {
  // Convert both dates to milliseconds and get the difference

  const diffInMilliseconds = endDate.valueOf() - startDate.valueOf();

  // Convert milliseconds to seconds
  const diffInSeconds = Math.floor(diffInMilliseconds / 1000);

  // If there's no difference or negative difference, return "0 seconds"
  if (diffInSeconds <= 0) {
    return "0 seconds";
  }

  // Convert to hours, minutes, seconds
  const hours = Math.floor(diffInSeconds / 3600);
  const remainingSeconds = diffInSeconds % 3600;
  const minutes = Math.floor(remainingSeconds / 60);
  const seconds = remainingSeconds % 60;

  // Format the duration string
  const parts: string[] = [];

  if (hours > 0) {
    parts.push(`${hours} ${hours === 1 ? 'hour' : 'hours'}`);
  }
  if (minutes > 0) {
    parts.push(`${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`);
  }
  if (seconds > 0 || parts.length === 0) {
    parts.push(`${seconds} ${seconds === 1 ? 'second' : 'seconds'}`);
  }

  return parts.join(', ');
}
