import { INotation } from '@/enums/iNotation'
import { ScorecardView } from '@/enums/scorecardView'
import type { DetailsList } from '@/types/detailsList'

const TrendCSSHelper = {
  getTrendSlopeCss(
    detailsList: DetailsList[],
    trend: INotation,
    view: ScorecardView
  ): string {
    let Sx = 0
    let Sy = 0
    let Sxx = 0
    let Sxy = 0
    let slope: number | null = null
    let n = 0

    // Calculate Sx, Sy, Sxx, Sxy and n
    for (let i = 0; i < detailsList.length; i++) {
      const detail = detailsList[i]
      if (detail?.performance !== undefined && detail.performance !== null) {
        if (
          detail.performance !== 0 ||
          (detail.denominator && detail.denominator > 0)
        ) {
          n++
          Sx += i
          Sy += detail.performance
          Sxx += i * i
          Sxy += i * detail.performance
        }
      }
    }

    // Calculate slope
    if (n !== 0) {
      slope = (n * Sxy - Sx * Sy) / (n * Sxx - Sx * Sx)
      switch (view) {
        case ScorecardView.Monthly:
        default:
          slope = slope / 1
          break
        case ScorecardView.Quarterly:
          slope = slope / 3
          break
        case ScorecardView.Yearly:
          slope = slope / 12
          break
      }
    }

    // Determine color and angle based on slope
    let color = 'yellow'
    let angle = '0'

    if (slope !== null) {
      if (slope < -3) {
        angle = 'n90'
        color = 'red'
      } else if (slope > -3 && slope < -0.9) {
        angle = 'n60'
        color = 'red'
      } else if (slope > -0.9 && slope < -0.4) {
        angle = 'n30'
        color = 'red'
      } else if (slope > -0.4 && slope < 0.4) {
        angle = '0'
        color = 'yellow'
      } else if (slope > 0.4 && slope < 0.9) {
        angle = '30'
        color = 'green'
      } else if (slope > 0.9 && slope < 3) {
        angle = '60'
        color = 'green'
      } else if (slope > 3) {
        angle = '90'
        color = 'green'
      }
    }

    // Adjust color if trend is 'Lower'
    if (trend === INotation.Lower && color !== 'yellow') {
      if (color === 'green') {
        color = 'red'
      } else if (color === 'red') {
        color = 'green'
      }
    }

    return `${color}-${angle}`
  },
}

export default TrendCSSHelper
