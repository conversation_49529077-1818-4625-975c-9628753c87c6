import { AdmPredefinedKeys } from '@/enums/admPredefinedKeys'
import { BuilderMode } from '@/enums/builderMode'
import { CdmPredefinedKeys } from '@/enums/cdmPredefinedKeys'
import { CitcPredefinedKeys } from '@/enums/citcPredefinedKeys'
import { DataManagementPredefinedKeys } from '@/enums/dataManagementPredefinedKeys'
import type { SimplifiedParameter } from '@/types/simplifiedParameter'
import appInsights from './applicationInsights'

class SqlConnectionStringBuilder {
  public UserInstance: boolean
  public LoadBalanceTimeout: number
  public MaxPoolSize: number
  public MinPoolSize: number
  public MultipleActiveResultSets: boolean
  public MultiSubnetFailover: boolean
  public PacketSize: number
  public Password: string | null
  public Replication: boolean
  public TransactionBinding: string
  public TrustServerCertificate: boolean
  public TypeSystemVersion: string
  public UserID: string | null
  public PersistSecurityInfo: boolean
  public Pooling: boolean
  public IntegratedSecurity: boolean
  public InitialCatalog: string | null
  public ApplicationIntent: string
  public ApplicationName: string
  public WorkstationID: string
  public ConnectRetryCount: number
  public ConnectRetryInterval: number
  public AttachDBFilename: string
  public CurrentLanguage: string
  public DataSource: string
  public Encrypt: boolean
  public Enlist: boolean
  public FailoverPartner: string
  public ConnectTimeout: number
  public PoolBlockingPeriod: string

  constructor() {
    this.UserInstance = false
    this.LoadBalanceTimeout = 0
    this.MaxPoolSize = 100
    this.MinPoolSize = 0
    this.MultipleActiveResultSets = false
    this.MultiSubnetFailover = false
    this.PacketSize = 8000
    this.Password = ''
    this.Replication = false
    this.TransactionBinding = ''
    this.TrustServerCertificate = false
    this.TypeSystemVersion = ''
    this.UserID = ''
    this.PersistSecurityInfo = false
    this.Pooling = true
    this.IntegratedSecurity = false
    this.InitialCatalog = ''
    this.ApplicationIntent = ''
    this.ApplicationName = 'nextGen SqlClient Data Provider'
    this.WorkstationID = ''
    this.ConnectRetryCount = 1
    this.ConnectRetryInterval = 10
    this.AttachDBFilename = ''
    this.CurrentLanguage = ''
    this.DataSource = ''
    this.Encrypt = false
    this.Enlist = true
    this.FailoverPartner = ''
    this.ConnectTimeout = 15
    this.PoolBlockingPeriod = ''
  }

  // Dynamically constructs the connection string
  get ConnectionString(): string {
    const keyValuePairs = [
      `Data Source=${this.DataSource}`,
      `Initial Catalog=${this.InitialCatalog}`,
      `User ID=${this.UserID}`,
      `Password=${this.Password}`,
      `Encrypt=${this.Encrypt}`,
      `TrustServerCertificate=${this.TrustServerCertificate}`,
      `Pooling=${this.Pooling}`,
      `Max Pool Size=${this.MaxPoolSize}`,
      `Min Pool Size=${this.MinPoolSize}`,
      `MultipleActiveResultSets=${this.MultipleActiveResultSets}`,
      `Connect Timeout=${this.ConnectTimeout}`,
      `Application Name=${this.ApplicationName}`,
      this.PersistSecurityInfo ? `Persist Security Info=True` : ``,
    ]

    // Filter out empty or undefined values
    return keyValuePairs.filter((pair) => pair !== '').join('; ')
  }

  get PrismaConnectionString(): string {
    // Ensure all fields are properly handled for undefined values
    const userId = this.UserID ?? ''
    const password = this.Password ?? ''
    const initialCatalog = this.InitialCatalog ?? ''
    const encrypt = this.Encrypt ?? 'true' // Default to 'true' if not provided
    const trustServerCertificate = this.TrustServerCertificate ?? ''

    // Wrap userId and password in `{}` if they contain special characters
    const specialCharactersRegex = /[:=;/[\]{}]/
    const encodedUserId = specialCharactersRegex.exec(userId)
      ? `{${userId}}`
      : userId

    const encodedPassword = specialCharactersRegex.exec(password)
      ? `{${password}}`
      : encodeURIComponent(password)

    // Clean the DataSource to remove tcp: prefix and port number if present
    let cleanDataSource: string = this.DataSource ?? ''

    // Remove "tcp:" if present
    if (cleanDataSource.startsWith('tcp:')) {
      cleanDataSource = cleanDataSource.replace('tcp:', '')
    }

    // Remove port if present (e.g., ",1433")
    cleanDataSource = cleanDataSource.split(',')[0]!

    // Construct the base URL in the format required by Prisma
    const baseUrl = `sqlserver://${cleanDataSource}:1433`

    // Create the full connection string
    const connectionString = [
      `${baseUrl};user=${encodedUserId}`, // Base URL with username and host with port
      `password=${encodedPassword}`, // Password wrapped in `{}` if needed
      `database=${initialCatalog}`, // Database name
      `encrypt=${encrypt}`, // Encryption setting
      trustServerCertificate
        ? `trustServerCertificate=${trustServerCertificate}`
        : '',
    ]
      .filter((part) => part !== '')
      .join(';') // Join all parts with semicolons

    return connectionString
  }
}

class ConnectionStringBuilder {
  private readonly orgParameters: SimplifiedParameter[]
  private readonly globalParameters: SimplifiedParameter[] = []
  private readonly builderMode: BuilderMode
  private connectionStringBuilder: SqlConnectionStringBuilder =
    new SqlConnectionStringBuilder()

  get connectionString(): string {
    return this.connectionStringBuilder.ConnectionString
  }

  get prismaConnectionString(): string {
    const logMessage = `setting prisma connection string: ${this.connectionStringBuilder.PrismaConnectionString}`
    const properties = {
      orgParameters: this.orgParameters,
      connnectionString: this.connectionStringBuilder.PrismaConnectionString,
    }

    appInsights.trackTrace({
      message: logMessage,
      properties: properties,
    })

    return this.connectionStringBuilder.PrismaConnectionString
  }

  constructor(
    orgParameters: SimplifiedParameter[],
    globalParameters?: SimplifiedParameter[],
    builderMode: BuilderMode = BuilderMode.Default
  ) {
    this.orgParameters = orgParameters
    globalParameters = globalParameters
    this.builderMode = builderMode
    this.init()
  }

  private init() {
    try {
      if (this.builderMode == BuilderMode.Default) {
        const valueByKey = this.getValueByKey(CitcPredefinedKeys.DataSource)
        this.connectionStringBuilder.DataSource = `${valueByKey}.database.windows.net`

        this.connectionStringBuilder.InitialCatalog = this.getValueByKey(
          CitcPredefinedKeys.InitialCatalog
        )

        this.connectionStringBuilder.UserID = this.getValueByKey(
          CitcPredefinedKeys.Username
        )

        this.connectionStringBuilder.Password = this.getValueByKey(
          CitcPredefinedKeys.Password
        )
      } else if (this.builderMode == BuilderMode.Cdm) {
        const valueByKey2 = this.getValueByKey(CdmPredefinedKeys.DataSource)
        this.connectionStringBuilder.DataSource =
          'tcp:' + valueByKey2 + '.database.windows.net,1433'
        this.connectionStringBuilder.InitialCatalog = this.getValueByKey(
          CdmPredefinedKeys.InitialCatalog
        )
        this.connectionStringBuilder.UserID = this.getValueByKey(
          CdmPredefinedKeys.Username
        )
        this.connectionStringBuilder.Password = this.getValueByKey(
          CdmPredefinedKeys.Password
        )
      } else if (this.builderMode == BuilderMode.DataManagement) {
        const valueByKey3 = this.getValueByKey(
          DataManagementPredefinedKeys.DataSource
        )
        this.connectionStringBuilder.DataSource =
          'tcp:' + valueByKey3 + '.database.windows.net,1433'
        this.connectionStringBuilder.InitialCatalog = this.getValueByKey(
          DataManagementPredefinedKeys.InitialCatalog
        )
        this.connectionStringBuilder.UserID = this.getValueByKey(
          DataManagementPredefinedKeys.Username
        )
        this.connectionStringBuilder.Password = this.getValueByKey(
          DataManagementPredefinedKeys.Password
        )
      } else {
        const valueByKey4 = this.getValueByKey(AdmPredefinedKeys.DataSource)
        this.connectionStringBuilder.DataSource =
          'tcp:' + valueByKey4 + '.database.windows.net,1433'
        this.connectionStringBuilder.InitialCatalog = this.getValueByKey(
          AdmPredefinedKeys.InitialCatalog
        )
        this.connectionStringBuilder.UserID = this.getValueByKey(
          AdmPredefinedKeys.Username
        )
        this.connectionStringBuilder.Password = this.getValueByKey(
          AdmPredefinedKeys.Password
        )
      }

      this.connectionStringBuilder.PersistSecurityInfo = false
      this.connectionStringBuilder.MultipleActiveResultSets = false
      this.connectionStringBuilder.Encrypt = true
      this.connectionStringBuilder.TrustServerCertificate = false
      this.connectionStringBuilder.ConnectTimeout = 30
    } catch (error) {
      appInsights.trackException({
        exception: error as Error,
        properties: {
          message: 'Failed to initialize ConnectionStringBuilder',
          orgParameters: JSON.stringify(this.orgParameters),
          globalParameters: JSON.stringify(this.globalParameters),
          builderMode: this.builderMode,
        },
      })
    }
  }

  private getValueByKey(key: string): string | null {
    let simplifiedParameter: SimplifiedParameter | undefined =
      this.orgParameters.find((o) => o.key == key)
    if (!simplifiedParameter) {
      simplifiedParameter = this.globalParameters.find((g) => g.key == key)
    }

    if (!simplifiedParameter) {
      throw new Error(`Unable to find an entry for ${key}`)
    }

    return simplifiedParameter.value ?? null
  }
}

export default ConnectionStringBuilder
