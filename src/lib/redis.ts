import { env } from '@/env'
import { createClient, RedisClientType } from 'redis'
import { compressToBase64, decompress<PERSON>romBase64 } from '@/lib/zlibUtils'
import appInsights from '@/lib/applicationInsights'

export type RedisHelper = {
  set(
    key: string,
    value: string,
    expirySeconds?: number,
    compress?: boolean
  ): Promise<void>
  get(key: string, compress?: boolean): Promise<string | null>
  del(key: string): Promise<void>
  clear(key: string): Promise<void>
  clearOrgKeys(orgId: string): Promise<number>
  clearClientCache(orgId: string): Promise<number>
}

const sleep = (delay: number) =>
  new Promise((resolve) => setTimeout(resolve, delay))

// Singleton Redis client instance
let redisClient: RedisClientType | null = null
let redisAvailable = true
const inMemoryStore = new Map()

const getRedisDatabaseIndex = () => {
  return env.REDIS_DATABASE_INDEX ? Number(env.REDIS_DATABASE_INDEX) : 0
}

// Function to get or initialize the Redis client
const getRedisClient = async (): Promise<RedisClientType> => {
  if (redisClient === null) {
    console.log('Initializing Redis client for the first time')
    redisClient = createClient({
      url: env.REDIS_URL,
      password: env.REDIS_KEY,
      pingInterval: 3000,
      database: getRedisDatabaseIndex(),
    })

    // Set up error handling
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    redisClient.on('error', async (err): Promise<void> => {
      console.log('Redis Client Error', err)

      switch (err.message) {
        // SOLUTION
        case 'Socket closed unexpectedly':
          console.log('Closing Redis connection...')
          await redisClient!.disconnect()
          await sleep(3000)
          console.log('Trying reconnecting Redis...')
          await redisClient!.connect()
          console.log('Reconnected to Redis')
          break
        default:
          break
      }

      if (err instanceof Error && err.message.includes('Connection timeout')) {
        redisAvailable = false
        console.warn(
          'Redis connection timed out. Falling back to in-memory store.'
        )
      } else {
        throw err
      }
    })

    // Set up connect event handler
    redisClient.on('connect', () => {
      console.log('Connected to Redis')
      redisAvailable = true
    })

    // Connect to Redis
    try {
      await redisClient.connect()
    } catch (err) {
      if (err instanceof Error && err.message.includes('Connection timeout')) {
        redisAvailable = false
        console.warn(
          'Redis connection timed out. Falling back to in-memory store.'
        )
      } else {
        throw err
      }
    }
  }

  return redisClient
}

export const redisHelper: RedisHelper = {
  async set(
    key: string,
    value: string,
    expirySeconds?: number,
    compress: boolean = true
  ): Promise<void> {
    // Skip if caching is disabled
    if (env.SERVER_CACHING_DISABLED === 'true') {
      return
    }

    let valueToStore = value

    // Compress the value if compression is enabled
    if (compress) {
      try {
        valueToStore = await compressToBase64(value)
      } catch (error) {
        console.warn(
          `Compression failed for key ${key}, storing uncompressed value`
        )
        // If compression fails, store the original value
      }
    }

    if (redisAvailable) {
      const client = await getRedisClient()
      if (expirySeconds) {
        await client.set(key, valueToStore, { EX: expirySeconds })
      } else {
        await client.set(key, valueToStore)
      }
    } else {
      inMemoryStore.set(key, valueToStore)
      if (expirySeconds) {
        setTimeout(() => {
          inMemoryStore.delete(key)
        }, expirySeconds * 1000)
      }
    }
  },

  async get(key: string, compress: boolean = true): Promise<string | null> {
    // Return null if caching is disabled
    if (env.SERVER_CACHING_DISABLED === 'true') {
      return null
    }

    let cachedData: string | null = null

    if (redisAvailable) {
      const client = await getRedisClient()
      cachedData = (await client.get(key)) || null
    } else {
      cachedData = inMemoryStore.get(key) || null
    }

    if (cachedData) {
      // Only attempt to decompress if compress flag is true
      if (compress) {
        try {
          // Try to decompress the data
          return await decompressFromBase64(cachedData)
        } catch (error) {
          // If decompression fails, assume the data was not compressed
          return cachedData
        }
      } else {
        // Return the data as is without attempting decompression
        return cachedData
      }
    }

    return null
  },

  async del(key: string): Promise<void> {
    if (redisAvailable) {
      const client = await getRedisClient()
      await client.del(key)
    } else {
      inMemoryStore.delete(key)
    }
  },

  async clear(): Promise<void> {
    if (redisAvailable) {
      const client = await getRedisClient()
      await client.flushAll()
    } else {
      inMemoryStore.clear()
    }
  },

  async clearClientCache(orgId: string): Promise<number> {
    if (typeof window !== 'undefined') {
      const keys = Array.from(clientCache.keys()).filter((key) =>
        key.includes(orgId)
      )
      clientCache = new Map<string, string>()
      return keys.length
    }
    return 0
  },

  async clearOrgKeys(orgId: string): Promise<number> {
    if (redisAvailable) {
      const client = await getRedisClient()
      const keys = await client.keys(`*${orgId}*`)
      if (keys.length > 0) {
        await client.del(keys)
        return keys.length
      }
      return 0
    } else {
      let count = 0
      for (const key of inMemoryStore.keys()) {
        if (key.includes(orgId)) {
          inMemoryStore.delete(key)
          count++
        }
      }
      return count
    }
  },
}

let clientCache = new Map<string, string>()
export const tryCache = async <T>(
  key: string,
  func: () => Promise<T>,
  expirySeconds?: number,
  invalidateCache?: boolean
): Promise<T> => {
  // If Redis caching is disabled, just execute the function
  if (env.SERVER_CACHING_DISABLED === 'true') {
    return await func()
  }

  const startTime = new Date()
  appInsights.trackEvent({
    name: `tryCache: Enter`,
    properties: {
      key,
    },
  }) //if this was called on the client, just return the call back
  console.log(`tryCache: Enter, key=${key}`, startTime)
  if (typeof window !== 'undefined') {
    if (clientCache.has(key)) {
      const results = JSON.parse(clientCache.get(key)!) as T
      const endTime = new Date()
      appInsights.trackEvent({
        name: `tryCache(Memory): Exit`,
        properties: {
          key,
          hit: true,
          duration: endTime.getTime() - startTime.getTime(),
        },
      }) //if this was called on the client, just return the call back
      console.log(
        `tryCache(Memory): Exit, key=${key}, hit, elapsed=${endTime.getTime() - startTime.getTime()}`,
        endTime
      )
      return results
    }
    const result = await func()
    clientCache.set(key, JSON.stringify(result))
    const endTime = new Date()
    appInsights.trackEvent({
      name: `tryCache(Memory): Exit`,
      properties: {
        key,
        hit: false,
        duration: endTime.getTime() - startTime.getTime(),
      },
    }) //if this was called on the client, just return the call back
    console.log(
      `tryCache(Memory): Exit, key=${key}, miss, elapsed=${endTime.getTime() - startTime.getTime()}`,
      endTime
    )
    return result
  }

  if (invalidateCache) {
    appInsights.trackEvent({
      name: `tryCache(Redis): Invalidate`,
      properties: {
        key,
      },
    }) //if this was called on the client, just return the call back
    console.log(`tryCache(Redis): Invalidate, key=${key}`)
    await redisHelper.del(key)
  }
  const cachedData = await redisHelper.get(key)
  if (cachedData) {
    try {
      const result = JSON.parse(cachedData) as T
      const endTime = new Date()
      appInsights.trackEvent({
        name: `tryCache(Redis): Exit`,
        properties: {
          key,
          hit: true,
          success: true,
          duration: endTime.getTime() - startTime.getTime(),
        },
      }) //if this was called on the client, just return the call back
      console.log(
        `tryCache(Redis): Exit, key=${key}, hit, elapsed=${endTime.getTime() - startTime.getTime()}`,
        endTime
      )
      return result
    } catch (error) {
      const endTime = new Date()
      appInsights.trackEvent({
        name: `tryCache(Redis): Exit`,
        properties: {
          key,
          hit: true,
          success: false,
          duration: endTime.getTime() - startTime.getTime(),
        },
      }) //if this was called on the client, just return the call back
      console.log(
        `tryCache(Redis): Exit, key=${key}, hit, error, elapsed=${endTime.getTime() - startTime.getTime()}`,
        endTime
      )
      throw error
    }
  }
  const data = await func()

  // Store the JSON string using the redisHelper.set with compression
  setTimeout(async () => {
    const jsonString = JSON.stringify(data)
    await redisHelper.set(key, jsonString, expirySeconds, true)
  }, 5)

  const endTime = new Date()
  appInsights.trackEvent({
    name: `tryCache(Redis): Exit`,
    properties: {
      key,
      hit: false,
      success: true,
      duration: endTime.getTime() - startTime.getTime(),
    },
  }) //if this was called on the client, just return the call back
  console.log(
    `tryCache(Redis): Exit, key=${key}, miss, elapsed=${endTime.getTime() - startTime.getTime()}`,
    endTime
  )
  return data
}
