import { NextResponse } from 'next/server'

/**
 * Force logout by clearing all authentication cookies
 * This is used as a fallback when other logout mechanisms fail
 */
export function forceLogoutResponse(baseUrl: string, redirectPath: string = '/api/auth/signin-oidc'): NextResponse {
  const response = NextResponse.redirect(new URL(redirectPath, baseUrl))

  // Clear all possible NextAuth cookies
  const cookiesToClear = [
    'next-auth.session-token',
    'next-auth.csrf-token',
    'next-auth.callback-url',
    'next-auth.pkce.code_verifier',
    '__Secure-next-auth.session-token',
    '__Host-next-auth.csrf-token',
    'x-organization-id'
  ]

  cookiesToClear.forEach(cookieName => {
    // Clear with different path and domain combinations
    response.cookies.set(cookieName, '', {
      maxAge: 0,
      path: '/',
      httpOnly: true,
      secure: false
    })

    response.cookies.set(cookieName, '', {
      maxAge: 0,
      path: '/',
      domain: 'localhost',
      httpOnly: true,
      secure: false
    })
  })

  return response
}

/**
 * Check if a request should be force logged out
 */
export function shouldForceLogout(auth: any): boolean {
  // Force logout if we have auth but it's marked as invalid
  if (auth && (auth.error === 'UserLoggedOut' || auth.status === 'INVALID')) {
    return true
  }

  // Force logout if we have auth but no access token
  if (auth && auth.user && !auth.accessToken) {
    return true
  }

  return false
}
