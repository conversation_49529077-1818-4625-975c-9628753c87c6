import { CitcUserRoles } from '@/shared/roles'
import type { OrganizationRole } from '@/types/organizationRole'

export const hasGlobalPatientExplorerAccess = (
  orgRoles: OrganizationRole[],
  organizationId: string
) => {
  const hasOrganizationLevelAccess = orgRoles.some((roles) => {
    return (
      roles.organizationId === organizationId &&
      roles.roles?.includes(CitcUserRoles.PATIENT_EXPLORER)
    )
  })

  return hasOrganizationLevelAccess
}
