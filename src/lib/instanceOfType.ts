/**
 * Generalized method to check if an object implements a given interface type.
 * @param value The object to check
 */
export const instanceOfType = <T extends object>(value: object): value is T => {
  // Ensure the value is an object
  if (typeof value !== 'object' || value === null) return false

  // Get the keys of the value
  const valueKeys = Object.keys(value) as (keyof T)[]

  // Check if the object contains all keys expected by the target interface type T
  for (const key of valueKeys) {
    if (!(key in value)) {
      return false
    }

    // Optionally: Perform a more advanced type check here based on your expected structure.
    // Example: if you expect a certain key to be of type string, you can add more checks
    // if (typeof value[key] !== 'string') return false;
  }

  return true
}
