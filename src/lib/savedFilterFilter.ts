import { FilterMetaData } from '@/types/filterMetaData'
import { FilterQuery } from '@/types/filterQuery'
import { SavedFilter } from '@/types/savedFilter'

export const savedFilterFilter = (x: SavedFilter, query: FilterQuery) => {
  return (
    x.filterName === query.filterName &&
    (JSON.parse(x.filterMetadata) as FilterMetaData).page === query.page &&
    (JSON.parse(x.filterMetadata) as FilterMetaData).measureType ===
      query.measureType
  )
}
