import { type Session } from 'next-auth'

export const isSessionAuthenticated = (auth: Session | null): boolean => {
    if (!auth) return false
    if (!auth.user) return false
    if (!auth.uid) return false
    if (!auth.accessToken) return false

    // Check if token is valid JWT format
    try {
        const parts = auth.accessToken.split('.')
        if (parts.length !== 3) return false

        // Basic check that it's a valid JWT structure
        const payload = JSON.parse(Buffer.from(parts[1]!, 'base64').toString())
        if (!payload.exp) return false

        // Check if token is expired
        const now = Math.floor(Date.now() / 1000)
        if (payload.exp < now) return false

        return true
    } catch (e) {
        console.error('Error validating session token:', e)
        return false
    }
}
