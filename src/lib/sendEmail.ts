import { Email } from '@/types/email'
import * as sgMail from '@sendgrid/mail'
import fs from 'fs'
import path from 'path'

// Define email templates as a dictionary
const EMAIL_TEMPLATES: any = {
  notification: (data: any) => `
      <p>Hello {receiverName},</p>
      <p>A new item has been shared with you in the Medisolv Platform. Click the button below to securely access it:</p>
      <a href="${data.redirectUrl}" style="padding:10px 20px; background-color:#0070f3; color:white; text-decoration:none; border-radius:5px;">
        ${data.actionText || 'View Notification'}
      </a>
  `,
}

export default async function sendEmail(email: Email, apiKey: string) {
  if (!apiKey) {
    throw new Error('API key is required')
  }

  sgMail.setApiKey(apiKey)

  let emailTemplate = ''
  if (email.templateName && EMAIL_TEMPLATES[email.templateName]) {
    const templatePath = path.join(
      process.cwd(),
      'src',
      'lib',
      'templates',
      'emailTemplate.html'
    )
    emailTemplate = fs.readFileSync(templatePath, 'utf-8')

    let html = EMAIL_TEMPLATES[email.templateName](email.templateData)

    emailTemplate = emailTemplate.replace('{data.fullMessage}', html)
  }
  const messages = email.users.map((user) => ({
    to: user.email,
    from: '<EMAIL>',
    subject: email.subject,
    html: emailTemplate.replace('{receiverName}', user.name),
    text: email.body || undefined,
  }))

  return sgMail.send(messages, true)
}
