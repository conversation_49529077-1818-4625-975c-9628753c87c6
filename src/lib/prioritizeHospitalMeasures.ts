import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'

/**
 * Reorders the given array so that "Hospital Measures" is first if present
 *
 * @param measureTypes - An array of measure type strings
 * @returns A new array with "Hospital Measures" (if present) at the front
 */
export function prioritizeHospitalMeasures(
  measureTypes: PrimaryMeasureTypeConstants[]
): PrimaryMeasureTypeConstants[] {
  // Make a copy so we don't mutate the original array
  const result = [...measureTypes]

  const hospitalIndex = result.indexOf(
    PrimaryMeasureTypeConstants.HospitalMeasures
  )
  if (hospitalIndex !== -1) {
    // Remove "Hospital Measures" from current position
    result.splice(hospitalIndex, 1)
    // Insert at the front
    result.unshift(PrimaryMeasureTypeConstants.HospitalMeasures)
  }

  return result
}
