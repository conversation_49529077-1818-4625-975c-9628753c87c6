import { FilterFn } from '@tanstack/react-table'

export const customStringFilter = <T>(): FilterFn<T> => {
  return (row, columnId, filterValue) => {
    // If no filter value, return all rows
    if (!filterValue || typeof filterValue !== 'object') return true

    const { conditions, conjunctions } = filterValue

    // If no conditions or empty conditions array, return all rows
    if (!conditions || !Array.isArray(conditions) || conditions.length === 0) return true

    // Get the cell value
    const rawValue = row.getValue(columnId)
    const cellValue = rawValue === null || rawValue === undefined
      ? ''
      : String(rawValue).toLowerCase()

    // Helper function to evaluate a single condition
    const evaluateCondition = (condition: { operator: string, value: string }) => {
      const { operator, value } = condition

      // For operators that don't need a value, we can ignore the value check
      const valueToCheck = value ? value.toLowerCase() : ''

      // For numeric comparisons
      const numericCellValue = parseFloat(cellValue)
      const numericValueToCheck = parseFloat(valueToCheck)
      const isNumericCell = !isNaN(numericCellValue)
      const isNumericValue = !isNaN(numericValueToCheck)

      switch (operator) {
        case 'eq':
          return cellValue === valueToCheck
        case 'neq':
          return cellValue !== valueToCheck
        case 'contains':
          return cellValue.includes(valueToCheck)
        case 'gt':
          // Handle greater than - requires numeric values
          if (isNumericCell && isNumericValue) {
            return numericCellValue > numericValueToCheck
          }
          // For non-numeric values, use string comparison
          return cellValue > valueToCheck
        case 'lt':
          // Handle less than - requires numeric values
          if (isNumericCell && isNumericValue) {
            return numericCellValue < numericValueToCheck
          }
          // For non-numeric values, use string comparison
          return cellValue < valueToCheck
        default:
          return true
      }
    }

    // If there's only one condition, just return its result
    if (conditions.length === 1) {
      return evaluateCondition(conditions[0])
    }

    // For multiple conditions, we need to evaluate them in sequence with the conjunctions
    let result = evaluateCondition(conditions[0])

    for (let i = 1; i < conditions.length; i++) {
      const nextResult = evaluateCondition(conditions[i])

      // Apply conjunction (AND/OR)
      // Make sure we don't go out of bounds on the conjunctions array
      const conjunctionType = conjunctions && i - 1 < conjunctions.length
        ? conjunctions[i - 1]
        : 'and'

      if (conjunctionType === 'and') {
        result = result && nextResult
      } else {
        result = result || nextResult
      }
    }

    return result
  }
}
