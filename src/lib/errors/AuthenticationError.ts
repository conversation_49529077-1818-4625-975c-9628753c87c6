/** The thinking here was to have different error handlers to render in error.ts but the page does not inherit the name. It is always Error */
export class AuthenticationError extends Error {
  statusCode: number

  constructor(message: string) {
    super(message)
    this.name = 'AuthenticationError'
    this.statusCode = 401

    // Ensures the stack trace is captured correctly (especially in V8 environments)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AuthenticationError)
    }

    // Setting the prototype explicitly for `instanceof` to work
    Object.setPrototypeOf(this, AuthenticationError.prototype)
  }
}
