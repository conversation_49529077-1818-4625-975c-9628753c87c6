/**
 * Converts a string to camel case with the first letter in lowercase
 * @param str The string to convert
 * @returns The camel case version of the string with a lowercase first letter
 */
const toCamelCase = (str: string): string => {
  return str
    .replace(/-([a-z])/gi, (match, letter) => letter.toUpperCase())
    .replace(/^([A-Z])/, (match) => match.toLowerCase())
}

type Primitive = string | number | boolean | null | undefined
export type Transformable =
  | Primitive
  | Transformable[]
  | { [key: string]: Transformable }

/**
 * Recursively converts all keys in an object to camel case
 * @param obj The object to transform
 * @returns A new object with all keys in camel case
 */
export const keysToCamelCase = (obj: Transformable): Transformable => {
  if (Array.isArray(obj)) {
    return obj.map((item) => keysToCamelCase(item))
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce(
      (acc, key) => {
        acc[toCamelCase(key)] = keysToCamelCase(obj[key])
        return acc
      },
      {} as Record<string, Transformable>
    )
  }
  return obj
}
