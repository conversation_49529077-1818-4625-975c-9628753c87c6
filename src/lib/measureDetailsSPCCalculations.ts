import type { RawSPCData, ProcessedSPCData } from "@/types/measureDetailsSPCData"
import { TimePeriod } from "@/types/TimePeriod"

// Calculate SPC limits based on raw data
export function calculateSPCLimits(
  rawData: RawSPCData,
  period: TimePeriod
): ProcessedSPCData {
  // Use the period to adjust calculations if needed
  const periodFactor = period === TimePeriod.Monthly ? 1 : period === TimePeriod.Quarterly ? 3 : 12;
  const {
    dates = [],
    performance = [],
    newPhases = [],
    excludedPoints = [],
    measureScoring = "",
    chartType = "",
  } = rawData

  // Find phase change indices
  const phaseChanges: number[] = []
  newPhases.forEach((isNewPhase, index) => {
    if (isNewPhase) {
      phaseChanges.push(index)
    }
  })

  // Always add start of data (index 0) as the first phase
  if (phaseChanges.length === 0 || phaseChanges[0] !== 0) {
    phaseChanges.unshift(0)
  }

  // Create arrays sized to match performance data
  const len = performance.length
  const centerLine: number[] = Array(len).fill(0)
  const upperLimit: number[] = Array(len).fill(0)
  const lowerLimit: number[] = Array(len).fill(0)
  const upperWarning: number[] = Array(len).fill(0)
  const lowerWarning: number[] = Array(len).fill(0)
  const ruleViolationPoints: boolean[] = Array(len).fill(false)

  // Process each phase
  for (let i = 0; i < phaseChanges.length; i++) {
    // phaseStart and phaseEnd must exist since i < phaseChanges.length
    // and we push 0 as a fallback start; so use non-null assertion `!`
    const phaseStart = phaseChanges[i]!
    const phaseEnd =
      i < phaseChanges.length - 1 ? phaseChanges[i + 1]! : performance.length

    // Get performance data for this phase (excluding any "excluded" points)
    const phaseData = performance
      .slice(phaseStart, phaseEnd)
      .filter((_, idx) => !excludedPoints[phaseStart + idx])

    // Calculate center line (mean) — guard division by zero
    const phaseMean =
      phaseData.reduce((sum, val) => sum + val, 0) / (phaseData.length || 1)

    let phaseStdDev: number

    // For p-chart or u-chart
    if (chartType === "p-chart" || chartType === "u-chart") {
      // Use average denominator for this phase if available, otherwise use 100
      const phaseDenominators = rawData.denominators?.slice(phaseStart, phaseEnd)
        .filter((_, idx) => !excludedPoints[phaseStart + idx]) || []

      const avgDenominator = phaseDenominators.length > 0
        ? phaseDenominators.reduce((sum, val) => sum + val, 0) / phaseDenominators.length
        : 100

      phaseStdDev = Math.sqrt((phaseMean * (1 - phaseMean)) / avgDenominator)
    } else {
      // For measurement data (simplified)
      const squaredDiffs = phaseData.map((val) => (val - phaseMean) ** 2)
      const variance =
        squaredDiffs.reduce((sum, val) => sum + val, 0) /
        (phaseData.length || 1)
      phaseStdDev = Math.sqrt(variance)
    }

    // Control limits (±3 sigma) & warning limits (±2 sigma)
    const upperControlLimit = phaseMean + 3 * phaseStdDev
    const lowerControlLimit = Math.max(0, phaseMean - 3 * phaseStdDev)
    const upperWarningLimit = phaseMean + 2 * phaseStdDev
    const lowerWarningLimit = Math.max(0, phaseMean - 2 * phaseStdDev)

    // Fill arrays for this phase
    for (let j = phaseStart; j < phaseEnd; j++) {
      // We know j is valid in the loop range => non-null assertions
      centerLine[j] = phaseMean
      upperLimit[j] = upperControlLimit
      lowerLimit[j] = lowerControlLimit
      upperWarning[j] = upperWarningLimit
      lowerWarning[j] = lowerWarningLimit

      // Check for rule violations
      const value = performance[j]!
      ruleViolationPoints[j] = value > upperControlLimit || value < lowerControlLimit
    }
  }

  // Generate rule violations for out-of-control points
  const ruleViolations = ruleViolationPoints
    .map((isViolation, index) => {
      if (isViolation) {
        // Non-null assertion or fallback for array lookups
        const value = performance[index]!
        const ucl = upperLimit[index]!
        const lcl = lowerLimit[index]!
        let description = ""

        if (value > ucl) {
          description = "Rule 1: 1 point > 3 sigma above center line"
        } else if (value < lcl) {
          description = "Rule 1: 1 point > 3 sigma below center line"
        }

        // Fallback if dates[index] is missing
        return {
          date: dates[index] ?? "",
          description,
        }
      }
      return null
    })
    .filter(Boolean) as { date: string; description: string }[]

  return {
    dates,
    performance,
    centerLine,
    upperLimit,
    lowerLimit,
    upperWarning,
    lowerWarning,
    ruleViolationPoints,
    excludedPoints,
    phaseChanges,
    ruleViolations,
    measureScoring,
    chartType,
  }
}

// Additional SPC rule checking functions
export function checkSPCRules(data: ProcessedSPCData): ProcessedSPCData {
  // If any of these might be undefined, default them:
  const {
    dates = [],
    performance = [],
    centerLine = [],
    upperLimit = [],
    lowerLimit = [],
    upperWarning = [],
    lowerWarning = [],
    ruleViolationPoints: existingViolationFlags = [],
    ruleViolations: existingViolationDescs = [],
  } = data

  // Make copies so we don’t mutate original arrays
  const ruleViolationPoints = [...existingViolationFlags]
  const ruleViolations = [...existingViolationDescs]

  // Rule 2: 9 points in a row on the same side of the center line
  for (let i = 8; i < performance.length; i++) {
    const ninePoints = performance.slice(i - 8, i + 1)
    const cl = centerLine[i]!
    if (ninePoints.every((point) => point > cl) || ninePoints.every((point) => point < cl)) {
      if (!ruleViolationPoints[i]) {
        ruleViolationPoints[i] = true
        ruleViolations.push({
          date: dates[i] ?? "",
          description: "Rule 2: 9 points in a row on same side of center line",
        })
      }
    }
  }

  // Rule 3: 6 points in a row steadily increasing or decreasing
  for (let i = 5; i < performance.length; i++) {
    const sixPoints = performance.slice(i - 5, i + 1)
    let increasing = true
    let decreasing = true

    // When we do sixPoints[j - 1] or sixPoints[j], TypeScript sees potential for undefined.
    // Use non-null assertions, since we know j runs from 1..(sixPoints.length - 1).
    for (let j = 1; j < sixPoints.length; j++) {
      const prev = sixPoints[j - 1]! // non-null assertion
      const curr = sixPoints[j]!     // non-null assertion

      if (curr <= prev) increasing = false
      if (curr >= prev) decreasing = false
    }

    if (increasing || decreasing) {
      if (!ruleViolationPoints[i]) {
        ruleViolationPoints[i] = true
        ruleViolations.push({
          date: dates[i] ?? "",
          description: "Rule 3: 6 points in a row steadily increasing or decreasing",
        })
      }
    }
  }

  // Rule 4: 14 points in a row alternating up and down
  for (let i = 13; i < performance.length; i++) {
    const fourteenPoints = performance.slice(i - 13, i + 1)
    let alternating = true

    for (let j = 1; j < fourteenPoints.length; j++) {
      const prev = fourteenPoints[j - 1]! // non-null assertion
      const curr = fourteenPoints[j]!     // non-null assertion

      // Simplistic up/down alternation check.
      if (
        (j % 2 === 1 && curr > prev) ||
        (j % 2 === 0 && curr < prev)
      ) {
        alternating = false
        break
      }
    }

    if (alternating) {
      if (!ruleViolationPoints[i]) {
        ruleViolationPoints[i] = true
        ruleViolations.push({
          date: dates[i] ?? "",
          description: "Rule 4: 14 points in a row alternating up and down",
        })
      }
    }
  }

  return {
    ...data,
    ruleViolationPoints,
    ruleViolations,
  }
}
