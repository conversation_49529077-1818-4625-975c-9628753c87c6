import { MeasuresSourceForTable } from '@/enums/measureSource'
import { MeasureTypeByApplication } from '@/enums/measureTypeByApplication'

export const getMeasureSource = (applicationName: string) => {
  let sourceLabel = ''

  switch (applicationName) {
    case MeasureTypeByApplication.RegistryMeasures:
      sourceLabel = MeasuresSourceForTable.CQMS
      break
    case MeasureTypeByApplication.AbstractedMeasures:
      sourceLabel = MeasuresSourceForTable.ABSTR
      break
    case MeasureTypeByApplication.HospitalMeasures:
    case MeasureTypeByApplication.AmbulatoryMeasures:
      sourceLabel = MeasuresSourceForTable.ECQMS
      break
    default:
      break
  }

  return sourceLabel
}
