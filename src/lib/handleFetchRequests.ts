import { redirect } from 'next/navigation'

type FetchResult<T> = {
  data?: T
  error?: { code: number; message: string; response?: unknown }
}

/** @deprecated - no longer being used at the moment */
export const handleFetchRequests = async <T extends unknown[]>(
  requests: { url: string; options?: RequestInit }[]
): Promise<{ [K in keyof T]: FetchResult<T[K]> }> => {
  const fetchPromises = requests.map((req) => fetch(req.url, req.options))

  const results = await Promise.allSettled(fetchPromises)

  return Promise.all(
    results.map(async (result, _index) => {
      if (result.status === 'fulfilled') {
        const response = result.value

        if (response.ok) {
          const data = await response
            .json()
            .then((data) => data.data as T[typeof _index])
          return { data: data as T[typeof _index] } // Ensure type is correctly associated
        } else if (response.status === 401) {
          redirect('/session/expired')
          return { error: { code: 401, message: 'Session expired' } }
        } else {
          const errorResponse: unknown = await response
            .json()
            .catch(() => null as unknown)
          return {
            error: {
              code: response.status,
              message: response.statusText,
              response: errorResponse,
            },
          }
        }
      } else {
        return {
          error: {
            code: 500,
            message: result.reason?.message ?? 'Unknown error',
          },
        }
      }
    })
  ) as Promise<{ [K in keyof T]: FetchResult<T[K]> }>
}
