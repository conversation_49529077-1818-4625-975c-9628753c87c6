import { CookiesSession } from '@/next-auth'
import { decode as jwtDecode, JwtPayload } from 'jsonwebtoken'

export const decodeToken = (accessToken: string) => {
  try {
    return jwtDecode(accessToken) as JwtPayload
  } catch (error) {
    return null
  }
}

export const decodedToken = {
  toJSON: (accessToken: string): CookiesSession => ({
    ...(decodeToken(accessToken) as unknown as CookiesSession),
  }),

  getUserId: (accessToken: string) => {
    return decodeToken(accessToken)?.uid
  },

  getUserEmail: (accessToken: string) => {
    return decodeToken(accessToken)?.email
  },

  getUserRoles: (accessToken: string) => {
    return decodeToken(accessToken)?.roles
  },
}
