import { type Measure } from '@/types/measure'

export const getMeasureTitle = (measure: Measure) => {
  let result
  let suffix = ''

  switch (measure.applicationName) {
    case 'Registry Measures':
      suffix = 'Q'
      break
    case 'Abstracted Measures':
      suffix = 'A'
      break
    case 'Hospital eMeasures':
    case 'Ambulatory eMeasures':
      suffix = 'E'
      break
    default:
      break
  }

  const equityStrata = measure.equityStrata ? ` (${measure.equityStrata})` : ''
  const name = measure.measureName
    .replace(`(${suffix})`, '')
    .replace(`(${suffix})`, '')
    .replace(equityStrata, '')

  const strata = measure.strata ? measure.strata.replace(`(${suffix})`, '') : ''

  if (!measure.strata) {
    result = suffix
      ? `${name}${equityStrata} (${suffix})`
      : `${measure.measureName}${equityStrata}`
  } else {
    result = suffix
      ? `${name} (${strata})${equityStrata} (${suffix})`
      : `${name} (${measure.strata})${equityStrata}`
  }

  return result
}
