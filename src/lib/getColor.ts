import { Cell, Row } from '@tanstack/react-table'
import { Performance } from '@/enums/performance'
import appInsights from '@/lib/applicationInsights'

export const getColor = <T extends { scorecardDetailsList?: any[] }>(
  cell: Cell<T, unknown>,
  row: Row<T>,
  scorecardviewType?: string
) => {
  const columnId = cell.column.id

  const isCY = /^CY_[0-9]{4}$/.test(columnId)
  const isMonth = /^[A-Za-z]{3}_[0-9]{4}$/.test(columnId)
  const isQuarter =
    /^Q\d{1}_[0-9]{4}$/.test(columnId) && String(cell.getValue()) !== '-'

  const shouldCheckPerformance =
    (isCY && scorecardviewType !== 'Hospital') || isMonth || isQuarter

  if (shouldCheckPerformance) {
    const performance = row.original.scorecardDetailsList?.find(
      (x) => x.columnName === columnId
    )?.performance

    if (performance > 0) {
      appInsights.trackEvent({
        name: 'getColor',
        properties: {
          columnId,
          cell,
          scorecardDetailsList: row.original.scorecardDetailsList,
          isCY,
          isMonth,
          isQuarter,
          scorecardviewType,
          performance,
        },
      })
    }

    switch (performance) {
      case Performance.Poor:
        return 'bg-[#fc4e47]'
      case Performance.Caution:
        return 'bg-[#f7ec9b]'
      case Performance.Good:
        return 'bg-[#9dde89]'
      case Performance.Exceptional:
        return 'bg-[#57d348]'
      default:
        return 'bg-transparent'
    }
  }

  return row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
}
