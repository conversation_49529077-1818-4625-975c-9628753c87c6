import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'

dayjs.extend(timezone)

/**
 * Given a Date object, returns a string representing the date in the user's current
 * timezone. If the input is null, returns 'Unknown'.
 *
 * Note: We use EST/EDT time zone (America/New_York) for formatting.
 *
 * @param {Date | null} value A Date object or null
 * @returns {string} A string representing the date
 */
export const getDateString = (value: Date | null) => {
  if (!value) {
    return 'Unknown'
  }

  // Convert the value to a dayjs object
  const easternTime = dayjs(value).tz('America/New_York') // EST/EDT time zone

  // Format the date string
  return easternTime.isValid() ? easternTime.format() : 'Unknown'
}
