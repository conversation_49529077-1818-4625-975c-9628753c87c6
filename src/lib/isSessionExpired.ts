import dayjs from 'dayjs'
import { type Session } from 'next-auth'
import { decode as jwtDecode } from 'jsonwebtoken'

/**
 * Checks if the given session has expired. This is a critical function that affects
 * whether or not the user is considered logged in.
 *
 * @param {Session | null} session The session to check. If the user is not logged
 * in, or the session is invalid, this will be null.
 * @returns {boolean} True if the session has expired, false if it is still valid.
 */
export const isSessionExpired = (session: Session | null): boolean => {
  // If there is no session object or no access token, treat it as expired
  if (!session?.accessToken) {
    console.log('No session or no access token—session is considered expired.')
    return true
  }

  try {
    // Decode the JWT to get its claims
    const { exp } = jwtDecode(session.accessToken) as { exp: number }

    // If the token has no exp claim, treat it as expired
    if (!exp) {
      console.log('Token has no exp claim—session is considered expired.')
      return true
    }

    // Convert 'exp' (seconds since epoch) into a Day.js object
    const expiration = dayjs.unix(exp).utc()

    // Current time
    const now = dayjs().utc()

    const isExpired = now.isAfter(expiration)

    return isExpired
  } catch (error) {
    // If decoding fails, treat the session as expired
    console.error('Error decoding token:', error)
    return true
  }
}
