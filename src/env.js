import { createEnv } from '@t3-oss/env-nextjs'
import { z } from 'zod'

export const env = createEnv({
  /**
   * Specify your server-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars.
   */
  server: {
    ADM_DATABASE_URL: z.string(),
    HUB_DATABASE_URL: z.string(),
    NODE_ENV: z
      .enum(['development', 'test', 'production'])
      .default('development'),
    // NEXTAUTH_SECRET:
    //   process.env.NODE_ENV === "production"
    //     ? z.string()
    //     : z.string().optional(),
    // NEXTAUTH_URL: z.preprocess(
    //   // This makes Vercel deployments not fail if you don't set NEXTAUTH_URL
    //   // Since NextAuth.js automatically uses the VERCEL_URL if present.
    //   (str) => process.env.VERCEL_URL ?? str,
    //   // VERCEL_URL doesn't include `https` so it cant be validated as a URL
    //   process.env.VERCEL ? z.string() : z.string().url()
    // ),

    CITC_CLIENT_ID: z.string(),
    CITC_CLIENT_SECRET: z.string().optional(),
    CITC_ISSUER_URL: z.string().optional(),
    PORT: z.string().optional(),
    REDIS_URL: z.string().optional(),
    REDIS_KEY: z.string().optional(),
    REDIS_DATABASE_INDEX: z.string().optional(),
    APPINSIGHTS_INSTRUMENTATIONKEY: z.string().optional(),
    // AUTH_SECRET: z.string(),
    ENCOR_A_PARAMETER_NAME: z.string().optional(),
    REDIS_CACHE_TIME_DURATION_SECONDS: z.string(),
    MAPLE_BLOB_STORAGE_CONN_STRING: z.string(),
    SERVER_CACHING_DISABLED: z.string().optional(),

    SNOWFLAKE_ACCOUNT: z.string().optional(),
    SNOWFLAKE_HOST: z.string().optional(),
    SNOWFLAKE_USERNAME: z.string().optional(),
    SNOWFLAKE_PASSWORD: z.string().optional(),
    SNOWFLAKE_WAREHOUSE: z.string().optional(),
    SNOWFLAKE_DATABASE: z.string().optional(),
    SNOWFLAKE_SCHEMA: z.string().optional(),
    SNOWFLAKE_LOGGING_LEVEL: z.string().optional(),
    PRODUCT_ID:z.string().optional(),
  },

  /**
   * Specify your client-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars. To expose them to the client, prefix them with
   * `NEXT_PUBLIC_`.
   */
  client: {
    // NEXT_PUBLIC_CLIENTVAR: z.string(),
    NEXT_PUBLIC_OIDC_AUTHORITY: z.string(),
    NEXT_PUBLIC_OIDC_CLIENT_ID: z.string(),
    NEXT_PUBLIC_OIDC_CLIENT_SECRET: z.string(),
    NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG: z.string(),
    NEXT_PUBLIC_OIDC_RESPONSE_TYPE: z.string(),
    NEXT_PUBLIC_OIDC_SCOPE: z.string(),
    NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT: z.string(),
    NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNIN: z.string(),
    NEXT_PUBLIC_OIDC_AUDIENCE: z.string(),
    NEXT_PUBLIC_APP_VERSION: z.string(),
    NEXT_PUBLIC_PLATFORM_ADM_SERVER_URL: z.string(),
    NEXT_PUBLIC_MAPLE_SERVER_URL: z.string(),
    NEXT_PUBLIC_API_BASE_URL: z.string(),
    NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING: z.string(),
    NEXT_PUBLIC_SISENSE_SETTINGS_PARAMETER_NAME: z.string(),
    NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING: z.string(),
    NEXT_PUBLIC_APPINSIGHTS_INSTRUMENTATIONKEY: z.string(),
    NEXT_PUBLIC_AUTH_SECRET: z.string(),
    NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL: z.string(),
    NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL_IDENTIFIER: z.string(),
    NEXT_PUBLIC_SISENSE_SETTINGS_USERNAME_IDENTIFIER: z.string(),
    NEXT_PUBLIC_SISENSE_SETTINGS_PASSWORD_IDENTIFIER: z.string(),
    NEXT_PUBLIC_SISENSE_SETTINGS_TOKEN_URL_SUFFIX: z.string(),
    NEXT_PUBLIC_POSTHOG_HOST: z.string(),
    NEXT_PUBLIC_POSTHOG_KEY: z.string(),
    NEXT_PUBLIC_HYDRA_SERVER_URL:z.string(),
    NEXT_PUBLIC_AUDIT_LOG_SERVER_URL:z.string(),
  },

  /**
   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
   * middlewares) or client-side so we need to destruct manually.
   */
  runtimeEnv: {
    SNOWFLAKE_ACCOUNT: process.env.SNOWFLAKE_ACCOUNT,
    SNOWFLAKE_HOST: process.env.SNOWFLAKE_HOST,
    SNOWFLAKE_USERNAME: process.env.SNOWFLAKE_USERNAME,
    SNOWFLAKE_PASSWORD: process.env.SNOWFLAKE_PASSWORD,
    SNOWFLAKE_WAREHOUSE: process.env.SNOWFLAKE_WAREHOUSE,
    SNOWFLAKE_DATABASE: process.env.SNOWFLAKE_DATABASE,
    SNOWFLAKE_SCHEMA: process.env.SNOWFLAKE_SCHEMA,
    SNOWFLAKE_LOGGING_LEVEL: process.env.SNOWFLAKE_LOGGING_LEVEL,
    ADM_DATABASE_URL: process.env.ADM_DATABASE_URL,
    HUB_DATABASE_URL: process.env.HUB_DATABASE_URL,
    NODE_ENV: process.env.NODE_ENV,
    ENCOR_A_PARAMETER_NAME: process.env.ENCOR_A_PARAMETER_NAME,
    CITC_CLIENT_ID: process.env.CITC_CLIENT_ID,
    CITC_CLIENT_SECRET: process.env.CITC_CLIENT_SECRET,
    CITC_ISSUER_URL: process.env.CITC_ISSUER_URL,
    PRODUCT_ID:process.env.PRODUCT_ID,
    NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG:
      process.env.NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG,
    NEXT_PUBLIC_OIDC_AUTHORITY: process.env.NEXT_PUBLIC_OIDC_AUTHORITY,
    NEXT_PUBLIC_OIDC_CLIENT_ID: process.env.NEXT_PUBLIC_OIDC_CLIENT_ID,
    NEXT_PUBLIC_OIDC_CLIENT_SECRET: process.env.NEXT_PUBLIC_OIDC_CLIENT_SECRET,
    NEXT_PUBLIC_OIDC_RESPONSE_TYPE: process.env.NEXT_PUBLIC_OIDC_RESPONSE_TYPE,
    NEXT_PUBLIC_OIDC_SCOPE: process.env.NEXT_PUBLIC_OIDC_SCOPE,
    NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNIN:
      process.env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNIN,
    NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT:
      process.env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT,
    NEXT_PUBLIC_OIDC_AUDIENCE: process.env.NEXT_PUBLIC_OIDC_AUDIENCE,
    PORT: process.env.PORT,
    NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION ?? '0.0.0-dev',
    NEXT_PUBLIC_PLATFORM_ADM_SERVER_URL:
      process.env.NEXT_PUBLIC_PLATFORM_ADM_SERVER_URL,
    NEXT_PUBLIC_MAPLE_SERVER_URL: process.env.NEXT_PUBLIC_MAPLE_SERVER_URL,
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING:
      process.env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING,
    NEXT_PUBLIC_SISENSE_SETTINGS_PARAMETER_NAME:
      process.env.NEXT_PUBLIC_SISENSE_SETTINGS_PARAMETER_NAME,
    NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING:
      process.env.NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING,
    NEXT_PUBLIC_APPINSIGHTS_INSTRUMENTATIONKEY:
      process.env.NEXT_PUBLIC_APPINSIGHTS_INSTRUMENTATIONKEY,
    // AUTH_SECRET: process.env.AUTH_SECRET,
    NEXT_PUBLIC_AUTH_SECRET: process.env.NEXT_PUBLIC_AUTH_SECRET,
    NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL:
      process.env.NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL,
    NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL_IDENTIFIER:
      process.env.NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL_IDENTIFIER,
    NEXT_PUBLIC_SISENSE_SETTINGS_USERNAME_IDENTIFIER:
      process.env.NEXT_PUBLIC_SISENSE_SETTINGS_USERNAME_IDENTIFIER,
    NEXT_PUBLIC_SISENSE_SETTINGS_PASSWORD_IDENTIFIER:
      process.env.NEXT_PUBLIC_SISENSE_SETTINGS_PASSWORD_IDENTIFIER,
    NEXT_PUBLIC_SISENSE_SETTINGS_TOKEN_URL_SUFFIX:
      process.env.NEXT_PUBLIC_SISENSE_SETTINGS_TOKEN_URL_SUFFIX,
    // NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    //DISCORD_CLIENT_ID: process.env.DISCORD_CLIENT_ID,
    //DISCORD_CLIENT_SECRET: process.env.DISCORD_CLIENT_SECRET,
    REDIS_URL: process.env.REDIS_URL,
    REDIS_KEY: process.env.REDIS_KEY,
    REDIS_DATABASE_INDEX: process.env.REDIS_DATABASE_INDEX,
    REDIS_CACHE_TIME_DURATION_SECONDS:
      process.env.REDIS_CACHE_TIME_DURATION_SECONDS,
    APPINSIGHTS_INSTRUMENTATIONKEY:
      process.env.NEXT_PUBLIC_APPINSIGHTS_INSTRUMENTATIONKEY,
    MAPLE_BLOB_STORAGE_CONN_STRING: process.env.MAPLE_BLOB_STORAGE_CONN_STRING,
    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    SERVER_CACHING_DISABLED: process.env.SERVER_CACHING_DISABLED,
    NEXT_PUBLIC_HYDRA_SERVER_URL: process.env.NEXT_PUBLIC_HYDRA_SERVER_URL,
    NEXT_PUBLIC_AUDIT_LOG_SERVER_URL: process.env.NEXT_PUBLIC_AUDIT_LOG_SERVER_URL,
  },
  /**
   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
   * useful for Docker builds.
   */
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
  /**
   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and
   * `SOME_VAR=''` will throw an error.
   */
  emptyStringAsUndefined: true,
})
