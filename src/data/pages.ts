import { Pages } from '@/types/pages'

/** This is used to create the initial records in the azure table storage if the org / partner doesnt have any pages */
export const staticpagesData: Pages[] = [
  {
    name: 'Dashboards',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/dashboard',
    isExternal: false,
    message: 'NULL',
    sortOrder: 1,
    isVisible: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'Measures',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/measures',
    isExternal: false,
    message: 'NULL',
    sortOrder: 2,
    isVisible: true,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'Scorecards',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/scorecards',
    isExternal: false,
    message: 'NULL',
    sortOrder: 3,
    isVisible: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'Populations',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/populations',
    isExternal: false,
    message: 'NULL',
    sortOrder: 4,
    isVisible: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'Analytics',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/analytics',
    isExternal: false,
    message: 'NULL',
    sortOrder: 5,
    isVisible: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'Alerts',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/alerts',
    isExternal: false,
    message: 'NULL',
    sortOrder: 6,
    isVisible: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'Collaborate',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/collaborate',
    isExternal: false,
    message: 'NULL',
    sortOrder: 7,
    isVisible: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'Help',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/help',
    isExternal: false,
    message: 'NULL',
    sortOrder: 8,
    isVisible: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'Reports',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/reports',
    isExternal: false,
    message: 'NULL',
    sortOrder: 9,
    isVisible: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'Explorer',
    ownerId: 'NULL',
    isPrimary: false,
    routeOrExternalUrl: '/explorer',
    isExternal: false,
    message: 'NULL',
    sortOrder: 10,
    isVisible: false,
    partitionKey: '',
    rowKey: '',
  },
] as const
