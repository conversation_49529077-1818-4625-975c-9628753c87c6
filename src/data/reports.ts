import { Report } from '@/types/reports/medisolvReport'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

/** This is used to create the initial records in the azure table storage if the org / partner doesnt have any pages */
export const standardReports: Report[] = [
  {
    name: 'ACO Performance Analysis',
    isActive: true,
    isCustom: false,
    isFavorite: false,
    lastModified: dayjs.utc().toISOString(),
    reportType: 'aco-performance-analysis',
    timestamp: dayjs.utc().toISOString(),
    reportConfig: '',
    userId: '',
    mainEntityId: '',
    isChunk: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'MVP Report',
    isActive: true,
    isCustom: false,
    isFavorite: false,
    lastModified: dayjs.utc().toISOString(),
    reportType: 'mvp',
    timestamp: dayjs.utc().toISOString(),
    reportConfig: '',
    userId: '',
    mainEntityId: '',
    isChunk: false,
    partitionKey: '',
    rowKey: '',
  },
] as const
