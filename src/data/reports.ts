import { Report } from '@/types/reports/medisolvReport'

/** This is used to create the initial records in the azure table storage if the org / partner doesnt have any pages */
export const standardReports: Report[] = [
  {
    name: 'ACO Performance Analysis',
    isActive: true,
    isCustom: false,
    isFavorite: false,
    lastModified: new Date().toISOString(),
    reportType: 'aco-performance-analysis',
    timestamp: new Date().toISOString(),
    reportConfig: '',
    userId: '',
    mainEntityId: '',
    isChunk: false,
    partitionKey: '',
    rowKey: '',
  },
  {
    name: 'MVP Report',
    isActive: true,
    isCustom: false,
    isFavorite: false,
    lastModified: new Date().toISOString(),
    reportType: 'mvp',
    timestamp: new Date().toISOString(),
    reportConfig: '',
    userId: '',
    mainEntityId: '',
    isChunk: false,
    partitionKey: '',
    rowKey: '',
  },
] as const
