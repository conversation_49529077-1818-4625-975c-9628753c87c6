import { create } from 'zustand'
import { shared } from 'use-broadcast-ts'
import {
  IAPIMeasure,
  MVPSummary,
  ProviderPerformance,
  QualityMeasures,
  QualityMeasuresScore,
  QualityScoreByMVP,
  Score,
} from '@/types/reports/mvpTypes'

export type MVPConfig = {
  year: number
  submissionGroup?: string
  mvp?: string
  runtime?: number
}

type MVPState = {
  mvpConfig: MVPConfig
  setMVPConfig: (config: MVPConfig) => void
  openConfigureReport: boolean
  setConfigureReportOpen: (open: boolean) => void
  summaryData?: MVPSummary[]
  setSummaryData: (data: MVPSummary[]) => void
  chartData?: QualityScoreByMVP[]
  setChartData: (data: QualityScoreByMVP[]) => void
  detailsData: {
    overallScore: Score | undefined
    qualityScore: Score | undefined
    iaScore: Score | undefined
    piScore: Score | undefined
    costScore: Score | undefined
    qualityMeasures: QualityMeasuresScore[]
    providerPerformance: ProviderPerformance[]
    qualityMeasuresData: QualityMeasures[]
    iaMeasuresData: IAPIMeasure[]
    piMeasuresData: IAPIMeasure[]
  }
  setDetailsData: (data: {
    overallScore: Score | undefined
    qualityScore: Score | undefined
    iaScore: Score | undefined
    piScore: Score | undefined
    costScore: Score | undefined
    qualityMeasures: QualityMeasuresScore[]
    providerPerformance: ProviderPerformance[]
    qualityMeasuresData: QualityMeasures[]
    iaMeasuresData: IAPIMeasure[]
    piMeasuresData: IAPIMeasure[]
  }) => void
}

export const useMVPStore = create(
  shared<MVPState>(
    (set) => ({
      mvpConfig: {
        year: 0,
      },
      setMVPConfig: (config: MVPConfig) => {
        set({ mvpConfig: config })
      },
      openConfigureReport: false,
      setConfigureReportOpen: (open: boolean) => {
        set({ openConfigureReport: open })
      },
      summaryData: [],
      setSummaryData: (data: MVPSummary[]) => {
        set({ summaryData: data })
      },
      chartData: [],
      setChartData: (data: QualityScoreByMVP[]) => {
        set({ chartData: data })
      },
      detailsData: {
        overallScore: undefined,
        qualityScore: undefined,
        iaScore: undefined,
        piScore: undefined,
        costScore: undefined,
        qualityMeasures: [],
        providerPerformance: [],
        qualityMeasuresData: [],
        iaMeasuresData: [],
        piMeasuresData: [],
      },
      setDetailsData: (data: {
        overallScore: Score | undefined
        qualityScore: Score | undefined
        iaScore: Score | undefined
        piScore: Score | undefined
        costScore: Score | undefined
        qualityMeasures: QualityMeasuresScore[]
        providerPerformance: ProviderPerformance[]
        qualityMeasuresData: QualityMeasures[]
        iaMeasuresData: IAPIMeasure[]
        piMeasuresData: IAPIMeasure[]
      }) => {
        set({ detailsData: data })
      },
    }),
    {
      name: 'mvp.ui', // name of the item in the storage (must be unique)
      // storage: createJSONStorage(() => sessionStorage), // (optional) by default, 'localStorage' is used
    }
  )
)
