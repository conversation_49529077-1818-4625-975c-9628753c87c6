import type { SavedViewModel } from '@/types/savedViewModel'
import type { TableState } from '@tanstack/react-table'
import { create } from 'zustand'

type ViewState = {
  tableState?: TableState
  setTableState: (tableState: TableState) => void

  visibleColumnsForView: string[]
  setVisibleColumns: (visibleColumns: string[]) => void

  currentView: SavedViewModel | undefined
  setCurrentView: (view: SavedViewModel | undefined) => void
}

export const useViewStore = create<ViewState>((set) => ({
  tableState: undefined,
  setTableState: (tableState: TableState) => {
    set({ tableState: tableState })
  },

  visibleColumnsForView: [],
  setVisibleColumns: (visibleColumns: string[]) => {
    set({ visibleColumnsForView: visibleColumns })
  },

  currentView: undefined,
  setCurrentView: (view: SavedViewModel | undefined) => {
    set({ currentView: view })
  },
}))
