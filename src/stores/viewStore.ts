import { DatePickerMode } from '@/types/datePickerMode'
import type { SavedViewModel } from '@/types/savedViewModel'
import type { TableState } from '@tanstack/react-table'
import { create } from 'zustand'

type ViewState = {
  tableState?: TableState
  setTableState: (tableState: TableState) => void

  visibleColumnsForView: string[]
  setVisibleColumns: (visibleColumns: string[]) => void

  currentView: SavedViewModel | undefined
  setCurrentView: (view: SavedViewModel | undefined) => void

  measureTypeSwitchProcessing: boolean | undefined
  setMeasureTypeSwitchProcessing: (bool: boolean | undefined) => void

  viewType: DatePickerMode | undefined
  setViewType: (viewType: DatePickerMode | undefined) => void
}

export const useViewStore = create<ViewState>((set, get) => ({
  tableState: undefined,
  setTableState: (tableState: TableState) => {
    set({ tableState: tableState })
  },

  visibleColumnsForView: [],
  setVisibleColumns: (visibleColumns: string[]) => {
    set({ visibleColumnsForView: visibleColumns })
  },

  currentView: undefined,
  setCurrentView: (view: SavedViewModel | undefined) => {
    set((state) => {
      // If view is undefined, just clear the current view
      if (!view) {
        return { currentView: undefined }
      }

      // Check if this is after a page reload
      const isPageReload =
        typeof window !== 'undefined' &&
        window.performance &&
        window.performance.navigation.type === 1

      // If there's no current view or it's a page reload, use the new view as is
      if (!state.currentView || isPageReload) {
        return { currentView: view }
      }

      // Otherwise, create a new view object with preserved date range
      return {
        currentView: {
          ...view,
          from: state.currentView.from || view.from,
          to: state.currentView.to || view.to,
        },
      }
    })
  },

  measureTypeSwitchProcessing: false,
  setMeasureTypeSwitchProcessing: (bool: boolean | undefined) => {
    set({ measureTypeSwitchProcessing: bool })
  },

  viewType: undefined,
  setViewType: (viewType: DatePickerMode | undefined) => {
    set({ viewType: viewType })
  },
}))
