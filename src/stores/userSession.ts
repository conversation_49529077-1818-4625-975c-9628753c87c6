import { env } from '@/env'
import { SelectionType } from '@/enums/selectionType'
import { useMemo, useEffect, useState } from 'react'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'

import { getDisplayName } from '@/lib/getDisplayName'
import { hasGlobalPatientExplorerAccess } from '@/lib/hasGlobalPatientExplorerAccess'
import { hasLimitedAccessForOrganization } from '@/lib/hasLimitedAccessForOrganization'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

import type { Session } from 'next-auth'
import type { BaseActions } from '@/types/baseActions'
import { type OrganizationRole } from '@/types/organizationRole'
import type { Partner } from '@/types/partner'
import type { PatientDetailAccess } from '@/types/patientDetailAccess'
import type { SimplifiedParameter } from '@/types/simplifiedParameter'
import type { UserSession } from '@/types/userSession'

import { ExpansionConfiguration } from '@/types/expansionConfiguration'
import { CitcUserRoles } from '@/shared/roles'
import { hasPermission } from '@/lib/permissions'

type State = UserSession & {
  permissions: Set<CitcUserRoles>
}

type Actions = {
  setUserAccesses: (
    organizationId: string,
    orgRoles: OrganizationRole[]
  ) => void
  switchToOrganization: (
    organizationId: string,
    organizationName: string
  ) => void
  setOrganizationPreferences: (parameters: SimplifiedParameter[]) => void
  setDisplayName: (session: Session) => void
  setOrganization: (
    session: Session,
    organization: {
      id: string
      name: string
      expansionConfiguration: ExpansionConfiguration[]
      parameters?: SimplifiedParameter[]
    },
    selectionType: SelectionType,
    globalParameters: SimplifiedParameter[],
    orgRoles: OrganizationRole[],
    primaryMeasureType: PrimaryMeasureTypeConstants
  ) => void
  setPrimaryMeasureType: (
    primaryMeasureType: PrimaryMeasureTypeConstants
  ) => void
  hasPermission: (role: CitcUserRoles) => boolean
} & BaseActions

export type UserSessionState = State & Actions

// export const hasPermission = (
//   permissions: Set<CitcUserRoles> | Record<CitcUserRoles, boolean>,
//   role: CitcUserRoles
// ): boolean => {
//   if (permissions instanceof Set) {
//     return permissions.has(role)
//   }
//   return !!permissions[role]
// }

export const processUserAccesses = (
  organizationId: string,
  orgRoles: OrganizationRole[],
  selectionType: SelectionType
): {
  permissions: Set<CitcUserRoles>
  patientDetailAccess: PatientDetailAccess
  hasPatientExplorerAccessAtPartner: boolean
  hasGlobalPatientExplorerAccess: boolean
} => {
  const patientDetails: PatientDetailAccess = {
    IsOrgLevelAccess: false,
    SubOrgs: [],
  }
  const permissions = new Set<CitcUserRoles>()
  const organization = orgRoles.find(
    (org) => org.organizationId === organizationId
  )

  if (organization?.roles) {
    organization.roles.forEach(citcUserRole => {
      if (Object.values(CitcUserRoles).includes(citcUserRole as CitcUserRoles)) {
        permissions.add(citcUserRole as CitcUserRoles)
      }
    })
  }

  const citcUserRoles = organization?.roles || []

  if (selectionType === SelectionType.Organization) {
    patientDetails.SubOrgs = []

    for (const SubOrg of organization?.subOrganizationRoles ?? []) {
      if (SubOrg.roles?.includes(CitcUserRoles.PATIENT_DETAIL_ACCESS))
        patientDetails.SubOrgs.push(SubOrg.subOrganizationId!)
    }

    if (
      patientDetails.SubOrgs === null ||
      patientDetails.SubOrgs.length == 0
    ) {
      if (organization?.roles?.includes(CitcUserRoles.PATIENT_DETAIL_ACCESS))
        patientDetails.IsOrgLevelAccess =
          !hasLimitedAccessForOrganization(orgRoles, organizationId)
      else patientDetails.IsOrgLevelAccess = false
    } else {
      patientDetails.IsOrgLevelAccess = false
    }

    return {
      permissions,
      patientDetailAccess: patientDetails,
      hasPatientExplorerAccessAtPartner: false, //TODO: implement additional conditions for user session
      hasGlobalPatientExplorerAccess: hasGlobalPatientExplorerAccess(orgRoles, organizationId),
    }
  } else {
    const isAccess =
      citcUserRoles.includes(CitcUserRoles.PATIENT_DETAIL_ACCESS) &&
      !hasLimitedAccessForOrganization(orgRoles, organizationId)

    return {
      permissions,
      patientDetailAccess: {
        IsOrgLevelAccess: !!isAccess,
        SubOrgs: [],
      },
      hasPatientExplorerAccessAtPartner: organization?.roles?.includes(CitcUserRoles.PATIENT_EXPLORER) || false,
      hasGlobalPatientExplorerAccess: false,
    }
  }
}

const initialState: State = {
  displayName: 'Hub User',
  organizationId: null,
  organizationName: null,
  selectionType: SelectionType.Organization,
  patientDetailAccess: {
    IsOrgLevelAccess: false,
    SubOrgs: [],
  },
  hasPatientExplorerAccessAtPartner: false,
  hasGlobalPatientExplorerAccess: false,
  hideExcelExport: false,
  exportMode: 'UI',
  useSpecialEntityStructure: false,
  primaryMeasureType: PrimaryMeasureTypeConstants.None,
  expansionConfiguration: [],
  permissions: new Set<CitcUserRoles>(),
}

export const useUserSessionStore = create<
  UserSessionState,
  [['zustand/persist', UserSessionState]]
>(
  persist(
    (set, get) => ({
      ...initialState,
      setDisplayName: (session: Session) =>
        set({
          displayName: getDisplayName(session),
        }),
      switchToOrganization: (
        organizationId: string,
        organizationName: string
      ) => {
        set({
          ...initialState,
          organizationId,
          organizationName,
        })
      },
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      setOrganization: async (
        session,
        organization,
        selectionType,
        globalParameters,
        orgRoles,
        primaryMeasureType
      ) => {
        try {
          get().setDisplayName(session)

          set((state) => ({
            ...state,
            organizationId: organization.id,
            organizationName: organization.name,
            expansionConfiguration: organization.expansionConfiguration,
            selectionType,
            primaryMeasureType:
              primaryMeasureType ?? PrimaryMeasureTypeConstants.None,
          }))

          if (selectionType === SelectionType.Organization) {
            get().setUserAccesses(get().organizationId!, orgRoles)
          } else if (selectionType === SelectionType.Partner) {
            get().setUserAccesses(get().organizationId!, [
              {
                organizationId: organization.id,
                roles: (organization as Partner).roles,
              },
            ])
          }

          get().setOrganizationPreferences(organization.parameters!)
        } catch (err) {
          console.error(err)
        }
      },
      hasPermission: (role: CitcUserRoles) => {
        const state = get()
        return hasPermission(state.permissions, role)
      },
      setUserAccesses: (
        organizationId: string,
        orgRoles: OrganizationRole[]
      ) => {
        const result = processUserAccesses(organizationId, orgRoles, get().selectionType)
        set(result)
      },
      /** @deprecated - may not be needed. Need to review code to make sure and double check where this was used. */
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      setOrganizationPreferences: async (parameters) => {
        const requiredParameterForHideExport = parameters.find(
          (x) => x.key === 'platform:DisableExports'
        )
        const requiredParameterForExportMode = parameters.find(
          (x) => x.key === 'platform:ExportMode'
        )
        const useSpecialEntityStructure = parameters.find(
          (x) => x.key === 'platform:UseSpecialEntityStructure'
        )

        set({
          hideExcelExport: requiredParameterForHideExport
            ? requiredParameterForHideExport.value === 'true' // Boolean parsing
            : false,
          exportMode: requiredParameterForExportMode?.value ?? 'UI',
          useSpecialEntityStructure: useSpecialEntityStructure
            ? useSpecialEntityStructure.value === 'true' // Boolean parsing
            : false,
        })
      },
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      setPrimaryMeasureType: async (
        primaryMeasureType: PrimaryMeasureTypeConstants
      ) => {
        set({ primaryMeasureType })
      },
      getCurrentState: () => get(),
    }),
    {
      name: 'medisolv.userSession', // name of the item in the storage (must be unique)
    }
  )
)
