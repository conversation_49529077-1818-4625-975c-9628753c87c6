import dayjs, { Dayjs } from 'dayjs'
import { create } from 'zustand'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

type State = {
  patient: {
    patientName: string
    age: number
    dob: Dayjs
    gender: string
    patientIdentifier: string
    sourcePatientIdentifier: string
    race: string
    ethnicity: string
    maritalStatus: string
    address1: string
    address2: string
    city: string
    state: string
    zipcode: string
    phone: string
  }
  encounter: {
    caseIdentifier: string
    sourceEncounterIdentifier: string
    encounterTypeText: string
    admitPriority: string
    facility: string
    edArrivalDateTime: string
    edEndDateTime: string
    admitDateTime: Dayjs
    dischargeDate: Dayjs
    admittingMD: string
    attendingMD: string
    dischargeMD: string
    dischargeDisposition: string
    principalDxCode: string
    principalDxDescription: string
    dischargeName: string
    principalPxCode: string
    principalPxDescription: string
    los: string
    totalCharges: string
    msDrg: string
    msDrgDescription: string
  }
  encounterHistory: {
    caseIdentifier: string
    admitDateTime: Dayjs
    dischargeDate: Dayjs
    encounterId: string
    encounterType: string
    facility: string
    patientId: string
  }[]
  encounterMeasures: {
    caseIdentifier: string
    measureName: string
    standardOfCareMet: number
    excluded: number
    continuous: number
    hoverText: string
    yearOfMeasure: number
    categoryAssignment: string
  }[]
  currentEncounterMeasures: {
    description: string
    facility: string
    measureName: string
    standardOfCareMet: number
    excluded: number
    continuous: number
    hoverText: string
  }[]
  patientMeasures: {
    measureName: string
    standardOfCareMet: number
    excluded: number
    continuous: number
    hoverText: string
    yearOfMeasure: number
    categoryAssignment: string
  }[]
  // For Insurance tab
  insurance: {}[]
  // For Diagnoses tab
  diagnoses: {
    codeType: string
    diagnosis: string
    diagnosisDescription: string
    ordinality: number
    poaStatus: string
  }[]
  // For Procedures tab
  procedures: {
    procedureCode: string
    codeSystem: string
    procedureDescription: string
    ordinality: number
    procedureProvider: string
    procedureDate: Dayjs
    codeType: string
  }[]
}

type Actions = {}

type ExplorerState = State & Actions

const initialState: State = {
  patient: {
    patientName: '*Not Recorded',
    age: 0,
    dob: dayjs.utc(),
    gender: '*Not Recorded',
    patientIdentifier: '*Not Recorded',
    sourcePatientIdentifier: '*Not Recorded',
    race: '*Not Recorded',
    ethnicity: '*Not Recorded',
    maritalStatus: '*Not Recorded',
    address1: '*Not Recorded',
    address2: '*Not Recorded',
    city: '*Not Recorded',
    state: '*Not Recorded',
    zipcode: '*Not Recorded',
    phone: '*Not Recorded',
  },
  encounter: {
    caseIdentifier: '*Not Recorded',
    sourceEncounterIdentifier: '*Not Recorded',
    encounterTypeText: '*Not Recorded',
    admitPriority: '*Not Recorded',
    facility: '*Not Recorded',
    edArrivalDateTime: '*Not Recorded',
    edEndDateTime: '*Not Recorded',
    admitDateTime: dayjs.utc(),
    dischargeDate: dayjs.utc(),
    admittingMD: '*Not Recorded',
    attendingMD: '*Not Recorded',
    dischargeMD: '*Not Recorded',
    dischargeDisposition: '*Not Recorded',
    principalDxCode: '*Not Recorded',
    principalDxDescription: '*Not Recorded',
    dischargeName: '*Not Recorded',
    principalPxCode: '*Not Recorded',
    principalPxDescription: '*Not Recorded',
    los: '*Not Recorded',
    totalCharges: '*Not Recorded',
    msDrg: '*Not Recorded',
    msDrgDescription: '*Not Recorded',
  },
  encounterHistory: [],
  currentEncounterMeasures: [],
  encounterMeasures: [],
  patientMeasures: [],
  insurance: [],
  diagnoses: [],
  procedures: [],
}

export const useExplorerStore = create<ExplorerState>((set) => ({
  ...initialState,
}))
