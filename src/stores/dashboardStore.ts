import { create } from 'zustand'

type DashboardState = {
    currentDashboardId: string
    setCurrentDashboardId: (dashboardId: string) => void
    showDashboardConfiguration: boolean
    setShowDashboardConfiguration: (show: boolean) => void
}

export const useDashboardStore = create<DashboardState>((set) => ({
    currentDashboardId: "",
    setCurrentDashboardId: (dashboardId: string) => {
        set({ currentDashboardId: dashboardId })
    },
    showDashboardConfiguration: false,
    setShowDashboardConfiguration: (show: boolean) => {
        set({ showDashboardConfiguration: show })
    },
}))
