import { create } from 'zustand'
import { persist } from 'zustand/middleware'

import type { BaseActions } from '@/types/baseActions'
import type { GroupsnSubOrgs } from '@/types/groupsnSubOrgs'
import type { MeasureFilterModel } from '@/types/measureFilterModel'
import type { Provider } from '@/types/provider'
import type { SubmissionGroup } from '@/types/submissionGroup'
import type { SavedFilterModel } from '@/types/savedFilterModel'

import { calculateQuarterDates, useDateStore } from './dates'
import { MeasuresFilter } from '@/types/measuresFilter'
import { useViewStore } from './viewStore'
import { ViewSettingsModel } from '@/types/savedViewModel'
import { Periods } from '@/types/periods'
import { DatePickerMode } from '@/types/datePickerMode'
import dayjs, { Dayjs } from 'dayjs'
import { Organization } from '@/types/organization'
import { Facility } from '@/types/facility'

type State = MeasuresFilter & {
  inFlux: boolean
}

type Actions = {
  applyFilters: () => void
  clearAll: () => void
  clearFilters: (viewSetting?: ViewSettingsModel) => void
  setHideEmptyIndicators: (hideEmptyIndicators: boolean) => void
  setExpandedSections: (sections: string) => void

  // Saved Filters
  setSavedFilters: (savedFilters: SavedFilterModel[]) => void
  addSavedFilter: (savedFilter: SavedFilterModel) => void
  removeSavedFilter: (savedFilterId: string) => void
  removeAllSavedFilters: () => void

  // Measures
  setMeasures: (measures: MeasureFilterModel[]) => void
  addMeasure: (measure: MeasureFilterModel) => void
  /** @deprecated - may no longer be used */
  addMeasures: (measures: MeasureFilterModel[]) => void
  removeMeasure: (measureId: string) => void
  /** @deprecated - may no longer be used */
  removeMeasures: (measures: MeasureFilterModel[]) => void
  addAllMeasures: (ids?: string[] | null) => void
  removeAllMeasures: () => void

  // Submission Groups
  setSubmissionGroups: (submissionGroups: SubmissionGroup[]) => void
  addSubmissionGroup: (submissionGroup: SubmissionGroup) => void
  removeSubmissionGroup: (submissionGroupId: string) => void
  addAllSubmissionGroups: (ids?: string[] | null) => void
  removeAllSubmissionGroups: () => void

  // Providers
  setProviders: (providers: Provider[]) => void
  addProvider: (provider: Provider) => void
  removeProvider: (npi: string) => void
  addAllProviders: (npis?: string[] | null) => void
  removeAllProviders: () => void

  // Sub Organizations
  setSubOrganizations: (subOrganizations: GroupsnSubOrgs[]) => void
  addSubOrganization: (subOrg: GroupsnSubOrgs) => void
  removeSubOrganization: (subOrgId: string) => void
  addAllSubOrganizations: (ids?: string[] | null) => void
  removeAllSubOrganizations: () => void

  // Organizations
  setOrganizations: (organizations: Organization[]) => void
  addOrganization: (organization: Organization) => void
  removeOrganization: (organizationId: string) => void
  addAllOrganizations: (ids?: string[] | null) => void
  removeAllOrganizations: () => void

  // Facilities
  setFacilities: (facilities: Facility[]) => void
  addFacility: (facility: Facility) => void
  removeFacility: (facilityId: string) => void
  addAllFacilities: (ids?: string[] | null) => void
  removeAllFacilities: () => void

  setInFlux: (influx: boolean) => void
} & BaseActions

type FilterState = State & Actions

const initialState: State = {
  appliedFilters: {
    measures: [],
    subOrganizations: [],
    submissionGroups: [],
    providers: [],
    organizations: [],
    facilities: [],
  },
  measures: [],
  checkedMeasures: [],
  expandedSections: [],
  subOrganizations: [],
  checkedSubOrganizations: [],
  savedFilters: [],
  checkedSavedFilters: [],
  submissionGroups: [],
  checkedSubmissionGroups: [],
  providers: [],
  checkedProviders: [],
  organizations: [],
  checkedOrganizations: [],
  facilities: [],
  checkedFacilities: [],
  useSpecialEntityStructure: false,
  hideEmptyIndicators: false,
  inFlux: true,
}

export const useFilterStore = create<
  FilterState,
  [['zustand/persist', FilterState]]
>(
  persist(
    (set, get) => ({
      ...initialState,
      clearAll: () => {
        const { setSelectedPeriod, setSelectedRange, setMode } =
          useDateStore.getState()

        const { currentView } = useViewStore.getState()

        const { startDate, endDate } = calculateQuarterDates()

        const newRange: [Dayjs | null, Dayjs | null] =
          currentView?.from && currentView?.to
            ? [dayjs(currentView.from).utc(), dayjs(currentView.to).utc()]
            : [startDate, endDate]

        setSelectedRange(newRange)

        // TODO: How to merge the next two lines?
        setSelectedPeriod((currentView?.viewType as Periods) ?? 'Quarterly')
        setMode((currentView?.viewType as DatePickerMode) ?? 'Quarterly')

        get().clearFilters(currentView?.settings || undefined)

        set({
          hideEmptyIndicators:
            currentView?.settings?.hideEmptyIndicators ?? false,
        })
      },
      setInFlux: (inFlux: boolean) => {
        set({ inFlux })
      },
      setHideEmptyIndicators: (hideEmptyIndicators: boolean) =>
        set({ hideEmptyIndicators }),
      applyFilters: () => {
        set((state) => ({
          appliedFilters: {
            measures:
              state.checkedMeasures.length === state.measures.length
                ? ['*']
                : state.checkedMeasures.map((measure) => measure.id),
            subOrganizations:
              state.checkedSubOrganizations.length ===
              state.subOrganizations.length
                ? ['*']
                : state.checkedSubOrganizations.map(
                    (subOrganization) => subOrganization.id
                  ),
            submissionGroups:
              state.checkedSubmissionGroups.length ===
              state.submissionGroups.length
                ? ['*']
                : state.checkedSubmissionGroups.map(
                    (submissionGroup) => submissionGroup.id
                  ),
            providers:
              state.checkedProviders.length === state.providers.length
                ? ['*']
                : state.checkedProviders.map((provider) => provider.id),
            organizations:
              state.checkedOrganizations.length === state.organizations.length
                ? ['*']
                : state.checkedOrganizations.map(
                    (organization) => organization.id
                  ),
            facilities:
              state.checkedFacilities.length === state.facilities.length
                ? ['*']
                : state.checkedFacilities.map((facility) => facility.id),
          },
        }))
      },

      clearFilters: (viewSettings) => {
        if (viewSettings) {
          const { measures, providers, submissionGroups, hospitals } =
            viewSettings

          get().addAllMeasures(measures)
          get().addAllSubmissionGroups(submissionGroups)
          get().addAllProviders(providers)
          get().addAllSubOrganizations(hospitals)
        } else {
          get().addAllMeasures()
          get().addAllSubmissionGroups()
          get().addAllProviders()
          get().addAllSubOrganizations()
        }
      },

      setExpandedSections: (section) => {
        set((state) => ({
          expandedSections: state.expandedSections.includes(section)
            ? [] // Collapse if the same section is clicked
            : [section], // Expand the new section
        }))
      },

      // Saved Filters
      setSavedFilters: (savedFilters) => {
        set({ savedFilters })
      },
      addSavedFilter: (savedFilter) => {
        set((state) => {
          const isCurrentlyChecked = state.checkedSavedFilters.some(
            (checkedSavedFilter) => checkedSavedFilter.id === savedFilter.id
          )

          return {
            checkedSavedFilters: isCurrentlyChecked
              ? [] // Toggle off if the same filter is clicked
              : [{ id: savedFilter.id!, label: savedFilter.filterName! }], // Replace with the new filter
          }
        })
      },
      removeSavedFilter: (savedFilterId) => {
        set((state) => ({
          checkedSavedFilters: state.checkedSavedFilters.filter(
            (checkedSavedFilter) => checkedSavedFilter.id !== savedFilterId
          ),
        }))
      },
      removeAllSavedFilters: () => {
        set(() => ({
          checkedSavedFilters: [],
        }))
      },

      // Provider
      setProviders: (providers) => {
        set({ providers })
      },
      addProvider: (provider) => {
        set((state) => ({
          checkedProviders: state.checkedProviders.find(
            (checkedProvider) => checkedProvider.id === provider.npi
          )
            ? state.checkedProviders
            : [
                ...state.checkedProviders,
                { id: provider.npi, label: provider.providerName },
              ],
        }))
      },
      removeProvider: (providerId) => {
        set((state) => ({
          checkedProviders: state.checkedProviders.filter(
            (checkedProvider) => checkedProvider.id !== providerId
          ),
        }))
      },
      addAllProviders: (npis) => {
        set((state) => {
          const providersToAdd =
            npis && npis.length > 0 && !npis.includes('*')
              ? state.providers.filter((provider) =>
                  npis.includes(provider.npi)
                )
              : state.providers

          return {
            checkedProviders: providersToAdd.map((provider) => ({
              id: provider.npi,
              label: provider.providerName,
            })),
          }
        })
      },
      removeAllProviders: () => {
        set(() => ({
          checkedProviders: [],
        }))
      },

      // Submission Groups
      setSubmissionGroups: (submissionGroups) => {
        set({ submissionGroups })
      },
      addSubmissionGroup: (submissionGroup) => {
        set((state) => ({
          checkedSubmissionGroups: state.checkedSubmissionGroups.find(
            (checkedSubmissionGroups) =>
              checkedSubmissionGroups.id === submissionGroup.submissionGroupId
          )
            ? state.checkedSubmissionGroups
            : [
                ...state.checkedSubmissionGroups,
                {
                  id: submissionGroup.submissionGroupId,
                  label: submissionGroup.submissionGroupName,
                },
              ],
        }))
      },
      removeSubmissionGroup: (submissionGroupId) => {
        set((state) => ({
          checkedSubmissionGroups: state.checkedSubmissionGroups.filter(
            (checkedSubmissionGroup) =>
              checkedSubmissionGroup.id !== submissionGroupId
          ),
        }))
      },
      addAllSubmissionGroups: (ids) => {
        set((state) => {
          const submissionGroupsToAdd =
            ids && ids.length > 0 && !ids.includes('*')
              ? state.submissionGroups.filter((submissionGroup) =>
                  ids.includes(submissionGroup.submissionGroupId)
                )
              : state.submissionGroups

          return {
            checkedSubmissionGroups: submissionGroupsToAdd.map(
              (submissionGroup) => ({
                id: submissionGroup.submissionGroupId,
                label: submissionGroup.submissionGroupName,
              })
            ),
          }
        })
      },
      removeAllSubmissionGroups: () => {
        set(() => ({
          checkedSubmissionGroups: [],
        }))
      },

      // Organizations
      setOrganizations: (organizations) => {
        set({ organizations })
      },
      addOrganization: (organization) => {
        set((state) => ({
          checkedOrganizations: state.checkedOrganizations.find(
            (checkedOrganizations) =>
              checkedOrganizations.id === organization.organizationId
          )
            ? state.checkedOrganizations
            : [
                ...state.checkedOrganizations,
                {
                  id: organization.organizationId!,
                  label: organization.organizationName!,
                },
              ],
        }))
      },
      removeOrganization: (organizationId) => {
        set((state) => ({
          checkedOrganizations: state.checkedOrganizations.filter(
            (checkedOrganization) => checkedOrganization.id !== organizationId
          ),
        }))
      },
      addAllOrganizations: (ids) => {
        set((state) => {
          const organizationsToAdd =
            ids && ids.length > 0 && !ids.includes('*')
              ? state.organizations.filter((organization) =>
                  ids.includes(organization.organizationId!)
                )
              : state.organizations

          return {
            checkedOrganizations: organizationsToAdd.map((organization) => ({
              id: organization.organizationId!,
              label: organization.organizationName!,
            })),
          }
        })
      },
      removeAllOrganizations: () => {
        set(() => ({
          checkedOrganizations: [],
        }))
      },

      // Measures
      setMeasures: (measures) => {
        set({ measures })
      },
      addMeasures: (measures) => {
        set((state) => ({
          checkedMeasures: [
            ...state.checkedMeasures,
            ...measures.map((measure) => ({
              id: measure.measureId,
              label: measure.measureName,
              applicationName: measure.applicationName,
            })),
          ],
        }))
      },
      removeMeasures: (measures) => {
        set((state) => ({
          checkedMeasures: state.checkedMeasures.filter(
            (measure) => !measures.find((m) => m.measureId === measure.id)
          ),
        }))
      },
      addMeasure: (measure) => {
        set((state) => ({
          checkedMeasures: state.checkedMeasures.find(
            (checkedMeasure) => checkedMeasure.id === measure.measureId
          )
            ? state.checkedMeasures
            : [
                ...state.checkedMeasures,
                {
                  id: measure.measureId,
                  label: measure.measureName,
                  applicationName: measure.applicationName,
                },
              ],
        }))
      },
      removeMeasure: (measureId) => {
        set((state) => ({
          checkedMeasures: state.checkedMeasures.filter(
            (measure) => measure.id !== measureId
          ),
        }))
      },
      addAllMeasures: (ids) => {
        set((state) => {
          const measuresToAdd =
            ids && ids.length > 0 && !ids.includes('*')
              ? state.measures.filter((measure) =>
                  ids.includes(measure.measureId)
                )
              : state.measures

          return {
            checkedMeasures: measuresToAdd.map((measure) => ({
              id: measure.measureId,
              label: measure.measureName,
              applicationName: measure.applicationName,
            })),
          }
        })
      },
      removeAllMeasures: () => {
        set(() => ({
          checkedMeasures: [],
        }))
      },

      // Sub Organizations
      setSubOrganizations: (subOrganizations) => {
        set({ subOrganizations })
      },
      addSubOrganization: (subOrganization) => {
        set((state) => ({
          checkedSubOrganizations: state.checkedSubOrganizations.find(
            (checkedSubOrganization) =>
              checkedSubOrganization.id === subOrganization.subOrganizationId
          )
            ? state.checkedSubOrganizations
            : [
                ...state.checkedSubOrganizations,
                {
                  id: subOrganization.subOrganizationId,
                  label: subOrganization.subOrganizationName,
                },
              ],
        }))
      },
      removeSubOrganization: (subOrganizationId) => {
        set((state) => ({
          checkedSubOrganizations: state.checkedSubOrganizations.filter(
            (checkedSubOrganization) =>
              checkedSubOrganization.id !== subOrganizationId
          ),
        }))
      },
      addAllSubOrganizations: (ids) => {
        set((state) => {
          const subOrganizationsToAdd =
            ids && ids.length > 0 && !ids.includes('*')
              ? state.subOrganizations.filter((subOrganization) =>
                  ids.includes(subOrganization.subOrganizationId)
                )
              : state.subOrganizations

          return {
            checkedSubOrganizations: subOrganizationsToAdd.map(
              (subOrganization) => ({
                id: subOrganization.subOrganizationId,
                label: subOrganization.subOrganizationName,
              })
            ),
          }
        })
      },
      removeAllSubOrganizations: () => {
        set(() => ({
          checkedSubOrganizations: [],
        }))
      },

      // Facilities
      setFacilities: (facilities) => {
        set({ facilities })
      },
      addFacility: (facility) => {
        set((state) => ({
          checkedFacilities: state.checkedFacilities.find(
            (checkedFacility) => checkedFacility.id === facility.facilityCode
          )
            ? state.checkedFacilities
            : [
                ...state.checkedFacilities,
                { id: facility.facilityCode!, label: facility.facilityName! },
              ],
        }))
      },
      removeFacility: (facilityId) => {
        set((state) => ({
          checkedFacilities: state.checkedFacilities.filter(
            (checkedFacility) => checkedFacility.id !== facilityId
          ),
        }))
      },
      addAllFacilities: (ids) => {
        set((state) => {
          const facilitiesToAdd =
            ids && ids.length > 0 && !ids.includes('*')
              ? state.facilities.filter((facility) =>
                  ids.includes(facility.facilityCode!)
                )
              : state.facilities

          return {
            checkedFacilities: facilitiesToAdd.map((facility) => ({
              id: facility.facilityCode!,
              label: facility.facilityName!,
            })),
          }
        })
      },
      removeAllFacilities: () => {
        set(() => ({
          checkedFacilities: [],
        }))
      },
    }),
    {
      name: 'medisolv.filter', // name of the item in the storage (must be unique)
    }
  )
)
