import { CalculatedMeasure } from '@/types/calculatedMeasure'
import { create } from 'zustand'
import { AggregatedMeasureDetails } from "@/types/aggregatedMeasureDetails";

type State = {
  measureResults: CalculatedMeasure[]
  aggregatedMeasureDetails: AggregatedMeasureDetails[]
}

type Actions = {
  setMeasureResults: (results: CalculatedMeasure[]) => void
  setAggregateMeasureDetails: (aggregatedMeasureDetails: AggregatedMeasureDetails[]) => void
}

type MeasureResultsState = State & Actions

const initialState: State = {
  measureResults: [],
  aggregatedMeasureDetails: [],
}

export const useMeasureResultsStore = create<MeasureResultsState>((set) => ({
  ...initialState,
  setMeasureResults: (measureResults: CalculatedMeasure[]) =>
    set({ measureResults }),
  setAggregateMeasureDetails: (aggregatedMeasureDetails: AggregatedMeasureDetails[]) =>
    set({ aggregatedMeasureDetails }),
}))
