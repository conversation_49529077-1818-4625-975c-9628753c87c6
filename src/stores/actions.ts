import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

interface ActionsState {
  selectedOptions: string[]
  toggleOption: (option: string) => void
  selectAllOptions: () => void
  deselectAllOptions: () => void
  setSelectedOptions: (options: string[]) => void
  options: string[]
  ShowProgress: boolean
  SetShowProgress: (ShowProgress: boolean) => void
  setOptions: (options: string[]) => void
}

export const useActionsStore = create(
  persist<ActionsState>(
    (set) => ({
      selectedOptions: [],
      toggleOption: (option: string) =>
        set((state) => ({
          selectedOptions: state.selectedOptions.includes(option)
            ? state.selectedOptions.filter((item) => item !== option)
            : [...state.selectedOptions, option],
        })),
      selectAllOptions: () =>
        set((state) => ({
          selectedOptions: state.options,
        })),
      deselectAllOptions: () => set({ selectedOptions: [] }),
      setSelectedOptions: (options: string[]) =>
        set({ selectedOptions: options }),
      options: [],
      ShowProgress: false,
      SetShowProgress: (ShowProgress: boolean) =>
        set({ ShowProgress: ShowProgress }),
      setOptions: (options: string[]) => set({ options: options }),
    }),
    {
      name: 'medisolv.actions',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
)
