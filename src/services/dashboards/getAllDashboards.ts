import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { Dashboard } from '@/types/dashboard'
import { migrateData } from '../migration/migrateData'
import { MigrationConfig } from '@/types/migrationConfig'
import { HUBPrismaClient } from '@/server/db'
import { upsertDashboard } from './upsertDashboard'

export const getAllDashboards = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  organizationId: string,
  migrationConfig?: MigrationConfig
): Promise<Dashboard[]> => {
  // TODO: Enhancement* Add try/catch w/ app insights
  const existingDashboards = await tableStorageWrapper.queryEntities<Dashboard>(
    tableStorageWrapper.generateODataQuery({
      PartitionKey: organizationId,
    })
  )

  if (migrationConfig && existingDashboards.length === 0) {
    // TODO: Enhancement* Add try/catch w/ app insights
    await migrateData({
      storage: tableStorageWrapper,
      migrationConfig,
      prismaClientType: 'hubClient',
      prismaQuery: (prismaClient: HUBPrismaClient) =>
        prismaClient.dashboards.findMany(),
      upsertFunction: async (storage, data) =>
        // TODO: Enhancement* Add try/catch w/ app insights
        await Promise.all(
          data.map((dashboard) =>
            upsertDashboard(storage, {
              partitionKey: organizationId,
              rowKey: dashboard.DashboardId,
              name: dashboard.Name,
              description: dashboard.Description ?? '',
              configurations: '',
            })
          )
        ),
    })
  }

  return tableStorageWrapper.queryEntities(
    tableStorageWrapper.generateODataQuery({
      PartitionKey: organizationId,
    })
  )
}
