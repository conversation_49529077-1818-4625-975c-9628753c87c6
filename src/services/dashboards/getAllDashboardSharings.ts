import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardDetails } from '@/types/dashboard'
import { MigrationConfig } from '@/types/migrationConfig'
import { migrateData } from '../migration/migrateData'
import { ADMPrismaClient } from '@/server/db'
import { upsertDashboardSharings } from './upsertDashboardSharings'

export const getAllDashboardSharings = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  organizationId: string,
  migrationConfig?: MigrationConfig
): Promise<DashboardDetails[]> => {
  const existingDashboards =
    await tableStorageWrapper.queryEntities<DashboardDetails>(
      tableStorageWrapper.generateODataQuery({
        PartitionKey: organizationId,
      })
    )

  if (migrationConfig && existingDashboards.length === 0) {
    await migrateData({
      storage: tableStorageWrapper,
      migrationConfig,
      prismaClientType: 'admClient',
      prismaQuery: (prismaClient: ADMPrismaClient) =>
        prismaClient.dashboardDetails.findMany(),
      upsertFunction: async (storage, data) =>
        await Promise.all(
          data.map((dashboard) =>
            upsertDashboardSharings(storage, {
              dashboardRowKey: dashboard.DashboardId.toString(),
              userId: dashboard.UserId,
              isDefault: dashboard.IsDefault,
              isShared: dashboard.IsShared,
              isFavorite: dashboard.IsFavorite ?? false,
              organizationId: migrationConfig.organizationId,
              partitionKey: migrationConfig.organizationId,
              rowKey: dashboard.DashboardId.toString(),
            })
          )
        ),
    })
  }

  return tableStorageWrapper.queryEntities(
    tableStorageWrapper.generateODataQuery({
      PartitionKey: organizationId,
    })
  )
}
