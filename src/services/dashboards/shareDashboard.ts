import { AzureTableStorageWrapper } from "@/services/azure/tableStorageWrapper"
import { DashboardDetails} from "@/types/dashboard"
import { uuidv7 } from "posthog-js/lib/src/uuidv7"

export const shareDashboard = async (
    tableStorage: AzureTableStorageWrapper,
    dashboardRowKey: string,
    userId: string,
    organizationId: string
) => {
  return await tableStorage.insertEntity<DashboardDetails>({
    dashboardRowKey,
    userId,
    organizationId,
    isDefault: false,
    isShared: true,
    isFavorite: false,
    partitionKey: organizationId,
    rowKey: uuidv7()
  })
}
