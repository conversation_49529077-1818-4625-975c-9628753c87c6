import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { Dashboard } from '@/types/dashboard'
import { uuidv7 } from 'posthog-js/lib/src/uuidv7'

export const insertDashboard = async (
  tableStorage: AzureTableStorageWrapper,
  name: string,
  description: string,
  configurations: string,
  organizationId: string
): Promise<Dashboard> => {
  return await tableStorage.insertEntity({
    name,
    description,
    configurations,
    partitionKey: organizationId,
    rowKey: uuidv7(),
  } as Dashboard)
}
