import { redisHelper } from '@/lib/redis'
import CitCOrganizationService from '../citCOrganizations'
import { Group } from '@/types/group'

export const getGroupsByOrganizationQuery = async (
  accessToken: string,
  organizationId: string
): Promise<Group[]> => {
  const cacheKey = `group-list-${organizationId}`
  const cachedData = await redisHelper.get(cacheKey)

  if (cachedData && JSON.parse(cachedData).length > 0) {
    console.log(
      `Cache hit for group list: ${cachedData.substring(0, Math.min(100, cachedData.length))}`
    )
    return JSON.parse(cachedData)
  }

  const citcOrganizationService = new CitCOrganizationService(accessToken)

  const subOrganizations =
    await citcOrganizationService.getGroupsByOrganizationIdAsync({
      organizationId,
    })

  await redisHelper.set(cacheKey, JSON.stringify(subOrganizations))

  return subOrganizations
}
