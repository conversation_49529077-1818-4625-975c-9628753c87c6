import { HUBPrismaClient } from '@/server/db'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { upsertDashboardFavoritesReport } from './upsertDashboardFavoritesReport'
import { migrateData } from '../migration/migrateData'
import { MigrationConfig } from '@/types/migrationConfig'
import { DashboardFavoritesReport } from '@/types/dashboardFavoritesReport'

export const getAllDashboardFavoritesReport = async (
  tableStorage: AzureTableStorageWrapper,
  userId: string,
  migrationConfig?: MigrationConfig
) => {
  const existingFavorites =
    await tableStorage.queryEntities<DashboardFavoritesReport>(
      tableStorage.generateODataQuery({
        PartitionKey: userId,
      })
    )

  if (migrationConfig && existingFavorites.length === 0) {
    await migrateData({
      storage: tableStorage,
      migrationConfig,
      prismaClientType: 'hubClient',
      prismaQuery: (prismaClient: HUBPrismaClient) =>
        prismaClient.dashboardFavouritesReport.findMany({
          where: {
            UserId: userId,
          },
        }),
      upsertFunction: async (storage, data) =>
        await Promise.all(
          data.map((favoritesReport) =>
            upsertDashboardFavoritesReport(storage, {
              partitionKey: userId,
              rowKey: favoritesReport.Id.toString(),
              dashboardId: favoritesReport.DashboardId,
              isAddedToFavourites: favoritesReport.IsAddedToFavourites,
              userId: favoritesReport.UserId,
            })
          )
        ),
    })
  }

  return tableStorage.queryEntities<DashboardFavoritesReport>(
    tableStorage.generateODataQuery({
      PartitionKey: userId,
    })
  )
}
