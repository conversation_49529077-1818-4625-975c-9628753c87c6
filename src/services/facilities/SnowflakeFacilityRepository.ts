import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'
import { type Connection, type Pool, type Bind } from 'snowflake-sdk'
import { Facility, SnowflakeFacility } from '@/types/facility'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'

export class SnowflakeFacilityRepository extends SnowflakeRepository<SnowflakeFacility> {
  constructor(connectionPool: Pool<Connection>, tableName: string) {
    super(connectionPool, tableName, 'FacilityId')
  }

  async findFacilitiesByOrganization(
    organizationId: string
  ): Promise<Facility[]> {
    const sql = `
      SELECT
        EF."EntitiesId",
        E."Description" AS "EntityName",
        E."Code" AS "EntityCode",
        EF."FacilitiesId" AS "FacilityID",
        E."SourceContainerIdentifier",
        F."SourceCode",
        F."SourceDescription",
        F."SourceFacilityAddress1",
        F."SourceFacilityAddress2",
        F."SourceFacilityCity",
        F."SourceFacilityState",
        F."SourceFacilityZipPostal",
        F."CCN",
        F."DataSourceId",
        F."EhrInstance",
        F."EhrTypeId"
      FROM
        MECA."EntityFacilities" EF
        JOIN CSRC."Facilities" F ON EF."FacilitiesId" = F."Id"
        JOIN MECA."Entities" E ON EF."EntitiesId" = E."Id"
        JOIN MECA."OrganizationTypes" OT ON E."OrganizationTypeId" = OT."Id"
        JOIN MECA."EntityTypes" ET ON E."EntityTypeId" = ET."Id"
      WHERE
        ET."Code" = '${EntityTypeConstants.Facility}'
        AND OT."Code" = '${EntityOrganizationTypeConstants.HospitalLevel}'
        AND E."SourceContainerIdentifier" LIKE ?
    `

    const params: Bind[] = [organizationId + '%']

    const results = await this.execute(sql, params)

    return (
      (results?.filter(
        (facility: any) =>
          facility.EntityName !== null && facility.EntityName !== ''
      ) as Facility[]) || ([] as Facility[])
    )
  }
}
