import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'
import { type Connection, type Pool, type Bind } from 'snowflake-sdk'
import { Facility, SnowflakeFacility } from '@/types/facility'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'

export class FacilityRepository extends SnowflakeRepository<SnowflakeFacility> {
  constructor(connectionPool: Pool<Connection>, tableName: string) {
    super(connectionPool, tableName, 'Id')
  }

  async findFacilitiesByOrganization(
    organizationId: string
  ): Promise<Facility[]> {
    const sql = `
      WITH EntityTypeId AS (
          SELECT "Id" 
          FROM MECA."EntityTypes" 
          WHERE "Code" = ?
        ),
        OrgTypeHospital AS (
          SELECT "Id" 
          FROM MECA."OrganizationTypes" 
          WHERE "Code" = ?
        ),
        FacilityList AS (
          SELECT 
            EF."FacilitiesId" AS "Id",
            E."Id" AS "FacilityId",
            E."Description" AS "FacilityName",
            E."Code" AS "FacilityCode"
          FROM MECA."EntityFacilities" EF
          JOIN MECA."Entities" E ON EF."EntitiesId" = E."Id"
          JOIN MECA."OrganizationTypes" OT ON E."OrganizationTypeId" = OT."Id"
          WHERE OT."Code" = ?
          AND E."SourceContainerIdentifier" LIKE ?
        )
        SELECT 
          entityFacility."EntitiesId" AS "EntitiesId",
          entities."Description" AS "EntityName",
          entities."Code" AS "EntityCode",
          entityFacility."FacilitiesId" AS "FacilityID",
          entities."SourceContainerIdentifier" AS "SourceContainerIdentifier",
          facilities."SourceCode" AS "SourceCode",
          facilities."SourceDescription" AS "SourceDescription",
          facilities."SourceFacilityAddress1" AS "SourceFacilityAddress1",
          facilities."SourceFacilityAddress2" AS "SourceFacilityAddress2",
          facilities."SourceFacilityCity" AS "SourceFacilityCity",
          facilities."SourceFacilityState" AS "SourceFacilityState",
          facilities."SourceFacilityZipPostal" AS "SourceFacilityZipPostal",
          facilities."CCN" AS "CCN",
          facilities."DataSourceId" AS "DataSourceId",
          facilities."EhrInstance" AS "EhrInstance",
          facilities."EhrTypeId" AS "EhrTypeId",
          FL."FacilityName" AS "FacilityName",
          FL."FacilityCode" AS "FacilityCode"
        FROM MECA."EntityFacilities" entityFacility
        JOIN CSRC."Facilities" facilities ON entityFacility."FacilitiesId" = facilities."Id"
        JOIN MECA."Entities" entities ON entityFacility."EntitiesId" = entities."Id"
        LEFT JOIN FacilityList FL ON FL."Id" = entityFacility."FacilitiesId"
        WHERE entities."EntityTypeId" IN (SELECT "Id" FROM EntityTypeId)
        AND entities."OrganizationTypeId" IN (SELECT "Id" FROM OrgTypeHospital)
        AND entities."SourceContainerIdentifier" LIKE ?
    `

    const params: Bind[] = [
      EntityTypeConstants.Facility,
      EntityOrganizationTypeConstants.HospitalLevel,
      EntityOrganizationTypeConstants.FacilityLevel,
      organizationId + '%',
      organizationId + '%'
    ]

    const results = await this.execute<Facility>(sql, params)

    return (
      (results?.filter(
        (facility: any) =>
          facility.FacilityName !== null && facility.FacilityName !== ''
      ).map(
        (facility: any) =>
          ({
            id: facility.FacilityID,
            facilityId: facility.FacilityID,
            facilityName: facility.FacilityName,
            ehrInstance: facility.EhrInstance,
            entitiesId: facility.EntitiesId,
            entityName: facility.EntityName,
            facilityID: facility.FacilityID,
            facilityNameLong: facility.FacilityName,
            facilityCode: facility.FacilityCode,
            entityCode: facility.EntityCode,
            sourceCode: facility.SourceCode,
            sourceDescription: facility.SourceDescription,
            sourceFacilityAddress1: facility.SourceFacilityAddress1,
            sourceFacilityAddress2: facility.SourceFacilityAddress2,
            sourceFacilityCity: facility.SourceFacilityCity,
            sourceFacilityState: facility.SourceFacilityState,
            sourceFacilityZipPostal: facility.SourceFacilityZipPostal,
            ccn: facility.CCN,
            sourceContainerIdentifier: facility.SourceContainerIdentifier,
            dataSourceId: facility.DataSourceId,
            ehrTypeId: facility.EhrTypeId,
            ehrInstanceInt: facility.EhrInstance,
          }) as Facility
      ) || ([] as Facility[])

      )
    )
  }
}
