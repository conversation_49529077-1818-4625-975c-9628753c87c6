// External Libraries
import dayjs, { Dayjs } from 'dayjs'

// Absolute Imports
import { config } from '@/config'
import { DateRange } from '@/lib/dateRange'
import TrendCSSHelper from '@/lib/trendCSSHelper'
import type { DetailsList } from '@/types/detailsList'
import type { FacilityResultByCCN } from '@/types/calculatedMeasure'
import { MeasureResultSummary } from '@/types/measureResultSummary'
import type { MeasureResultOptions } from '@/types/measureResultOptions'

// Relative Imports
import { mapleMeasureQuery } from '../maple/mapleMeasuresQuery'
import MeasureResultsService from '../measureResults'
import MapleMeasuresService from '../mapleMeasures'
import { INotation } from '@/enums/iNotation'
import { ScorecardView } from '@/enums/scorecardView'

export const getFacilitiesByCCN = async (
    accessToken: string,
    measureResultsService: MeasureResultsService,
    hospitalId: string,
    scorecardView: ScorecardView,
    organizationId: string,
    startDate: Dayjs,
    endDate: Dayjs,
    measureIdentifier: string,
    isPartner: boolean,
    isIAPIMeasure: boolean,
) => {
    const modelData: FacilityResultByCCN[] = []

    const admMeasures = await measureResultsService.getAllMeasures(
        organizationId
    )

    const dateRangeSpan = DateRange.getColumnIntervalsByCategory(
        scorecardView,
        startDate,
        endDate
    )

    const mapleMeasuresService = new MapleMeasuresService(
        accessToken,
        measureResultsService
    )

    const measureDetails = await mapleMeasureQuery(
        mapleMeasuresService,
        organizationId,
        [measureIdentifier]
    )

    const isRatioMeasure = config.customMeasures.ratioMeasures.some(
        (x) => x.toLowerCase() == measureIdentifier.toLowerCase()
    )
    const admMeasureDetails = admMeasures.find(
        (x) => x.MedisolvMeasureId == measureIdentifier
    )

    let facilities = await measureResultsService.getFacilityByOrganization(
        organizationId
    )

    if (hospitalId !== '*') {
        facilities = facilities.filter((x) => x.entityCode === hospitalId)
    }

    const facilityCodes = [...new Set(facilities.map((x) => x.facilityCode!))]

    const options: MeasureResultOptions = {
        organizationId: organizationId,
        periodType: scorecardView,
        subOrganizationId: facilityCodes,
        startDate: startDate,
        endDate: endDate,
        isPartner: isPartner,
        measureIdentifier: measureIdentifier,
        isFacilityLevel: true,
    }

    const aggregateResultsResponse =
        await measureResultsService.getMeasureResults(options)
    const measureResults =
        aggregateResultsResponse?.measureResultSummaryList ?? []


    const emptyFacilities = facilityCodes.filter(
        (code) => !measureResults.some((result) => result.entityCode === code)
    )


    if (emptyFacilities.length) {
        const newResults = emptyFacilities.map((facilityCode) => {

            const result = {
                entityCode: facilityCode,
                entityName: facilities.find((x) => x.facilityCode === facilityCode)
                    ?.facilityName ?? '',
                startDate: dayjs(0).toDate(),
                endDate: dayjs(0).toDate(),
                denominator: null,
                performance: null,
                measureGUID: '',
                measureSubId: '',
                sourceContainerIdentifier: '',
            } as MeasureResultSummary

            return result
        })

        measureResults.push(...newResults)
    }


    const facilitygrps = measureResults!
        .filter((x) => x.measureGUID == measureIdentifier)
        .reduce<Record<string, MeasureResultSummary[]>>((groups, item) => {
            if (!groups[item.entityCode!]) {
                groups[item.entityCode!] = []
            }
            groups[item.entityCode!]?.push(item)
            return groups
        }, {})

    for (const item of Object.entries(facilitygrps)) {
        const [key, group] = item

        let isEmptyIndicator = true
        const myObj: Partial<FacilityResultByCCN> = {}

        myObj.facilityId = key
        myObj.facilityName = group[0]?.entityName ?? ''
        myObj.facilityWithEntityName = group[0]?.entityName ?? ''

        const detailList: DetailsList[] = []
        for (const item of dateRangeSpan) {
            const span = item.replace('-', '_') as
                | `Q${number}_${number}`
                | `CY_${number}`
                | `${Capitalize<string>}_${number}`
            const period = DateRange.getDateFromDateRangeSpan(
                scorecardView,
                span
            )

            if (admMeasureDetails?.NullRate) {
                myObj[span] = '-'
                detailList.push({ performance: null, denominator: null })
                continue
            }

            const rate = group.find(
                (x) =>
                    dayjs(x.startDate).isSame(period.startDate, 'day') &&
                    dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
            )

            if (rate) {
                detailList.push({
                    performance: rate.performance,
                    denominator: admMeasureDetails?.NullDenominator
                        ? null
                        : rate.denominator,
                })

                if (isRatioMeasure) {
                    myObj[span] =
                        rate.performance === null || rate.performance === -1
                            ? '-'
                            : rate.performance === 100
                                ? `${rate.numeratorValue}/${rate.denominatorValue}<br>${rate.performance}`
                                : `${rate.numeratorValue}/${rate.denominatorValue}<br>${rate.performance.toFixed(
                                    2
                                )}`
                } else {
                    myObj[span] =
                        rate.performance === null || rate.performance === -1
                            ? '-'
                            : rate.performance === 100
                                ? rate.performance.toString()
                                : rate.performance.toFixed(2)
                }

                if (rate.performance !== null) isEmptyIndicator = false
            } else {
                myObj[span] = '-'
                detailList.push({ performance: null, denominator: null })
            }
        }

        const trendName =
            INotation[
            measureDetails[0]?.iNotationName! as unknown as keyof typeof INotation
            ]

        const trendCSS = TrendCSSHelper.getTrendSlopeCss(
            detailList,
            trendName,
            scorecardView
        )

        myObj.trendCss = trendCSS

        myObj.measureDescription = measureDetails[0]?.measureDescription.trim()!
        myObj.friendlyName = measureDetails[0]?.measureFriendlyName!
        myObj.subDomain = measureDetails[0]?.subDomainName
        myObj.type = measureDetails[0]?.typeName
        myObj.domain = measureDetails[0]?.domainName
        myObj.cmsId = measureDetails[0]?.cMSId ?? '-'
        myObj.subType = measureDetails[0]?.subTypeName
        myObj.application = measureDetails[0]?.applicationName
        myObj.programName = measureDetails[0]?.programName ?? '-'
        myObj.measureIdentifier = admMeasureDetails?.MedisolvMeasureId
        myObj.isEmptyIndicator = isEmptyIndicator
        myObj.isIAPIMeasure = isIAPIMeasure
        myObj.sourceContainerIdentifier = organizationId

        modelData.push(myObj)
    }

    modelData.sort((a, b) => a.facilityName!.localeCompare(b.facilityName!))

    return modelData
}
