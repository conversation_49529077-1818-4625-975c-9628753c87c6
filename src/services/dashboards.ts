import { getAllDashboards } from "@/services/dashboards/getAllDashboards"
import { getAllDashboardSharings } from "@/services/dashboards/getAllDashboardSharings"
import { insertDashboard } from "@/services/dashboards/insertDashboard"
import { AzureTableStorageWrapper } from "@/services/azure/tableStorageWrapper";
import { AddDashboardRequest, Dashboard, DashboardDetails, ShareDashboardRequest } from "@/types/dashboard";
import {shareDashboard} from "@/services/dashboards/shareDashboard";

export class DashboardsService {
    constructor(private readonly dashboardTableStorageWrapper: AzureTableStorageWrapper,
                private readonly dashboardDetailsTableStorageWrapper: AzureTableStorageWrapper,
                private readonly organizationId: string,) {
    }

    public async addDashboard(addDashboardRequest: AddDashboardRequest) {
        return await insertDashboard(
            this.dashboardTableStorageWrapper,
            addDashboardRequest.name,
            addDashboardRequest.description,
            addDashboardRequest.configuration,
            this.organizationId
        );
    }

    public async shareDashboard(shareDashboardRequest: ShareDashboardRequest) {
        // TODO: Apply business rule to ensure that the user has access to the Dashboard in order to share
        return await shareDashboard(
            this.dashboardDetailsTableStorageWrapper,
            shareDashboardRequest.dashboardId,
            shareDashboardRequest.userId,
            this.organizationId
        );
    }

    public async getAllDashboards(): Promise<Dashboard[]> {
        return await getAllDashboards(
            this.dashboardTableStorageWrapper,
            this.organizationId)
    }

    public async getAllDashboardsWithSharingDetails(): Promise<Dashboard[]> {
        const dashboards = await getAllDashboards(
            this.dashboardTableStorageWrapper,
            this.organizationId)
        const sharings = await getAllDashboardSharings(
            this.dashboardDetailsTableStorageWrapper,
            this.organizationId
        )
        const sharingMap = sharings
            .reduce((acc: Record<string, DashboardDetails[]>, obj) => {
                const key = obj.dashboardRowKey; // Key by category (or any other string property)
                if (!acc[key]) {
                    acc[key] = []; // Initialize array if not exists
                }
                acc[key].push(obj); // Add object to the corresponding array
                return acc;
            }, {})
        return dashboards.map(dashboard => {
            return { ...dashboard, sharing: sharingMap[dashboard.rowKey] }
        })
    }
}
