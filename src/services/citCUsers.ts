import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { Service } from './service'
import { env } from '@/env'
import { type User } from '@/types/user'
import CitCParametersService from './citcParameters'
import { SelectionType } from '@/enums/selectionType'
import { CitcUserProfleUpdateOptions } from '@/types/citcUserProfileUpdateOptions'
import { AzureTableStorageWrapper } from './azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import { tryCache } from '@/lib/redis'
import { OrganizationPreferenceKey } from '@/enums/organizationPreferenceKey'
import { getOrganizationPreferences } from './organizationPreferences/getOrganizationPreferences'

class CitCUsersService extends Service {
  constructor(accessToken: string) {
    super(accessToken)
  }

  async getUserDetailsAsync(userId: string) {
    const cacheKey = `getUserDetailsAsync.${userId}`
    return await tryCache(
      cacheKey,
      async () => {
        return (
          (
            await this.fetchWithType<User>(
              `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/users/user/${userId}/details`
            )
          ).data ?? null
        )
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
  }

  async GetAllUsersByOrganizationAsync(
    organizationId: string
  ): Promise<User[]> {
    return (
      (
        await this.fetchWithType<User[]>(
          `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/users/organization/${organizationId}`
        )
      ).data ?? []
    )
  }

  async getPrimaryMeasureType(
    selectionType: SelectionType,
    organizationId: string
  ): Promise<PrimaryMeasureTypeConstants> {
    // TODO: Use parameters from ctx (pass in)
    const citCParametersService = new CitCParametersService(this.accessToken)

    const orgParameters =
      selectionType === SelectionType.Partner
        ? await citCParametersService.getPartnerParametersAsync(organizationId)
        : await citCParametersService.getOrganizationParametersAsync(
            organizationId
          )

    const primaryMeasureTypes = orgParameters
      .filter((x) => x.key === OrganizationPreferenceKey.PRIMARYMEASURETYPE)
      .flatMap((x) => x.value?.split(',') ?? [])
      .map((x) => x.trim())

    if (
      selectionType === SelectionType.Partner ||
      env.NODE_ENV === 'development'
    ) {
      return primaryMeasureTypes[0]! as PrimaryMeasureTypeConstants
    } else {
      const azureTableStorageWrapper = new AzureTableStorageWrapper(
        StorageTables.OrganizationPreferences
      )

      const orgPreferences = await getOrganizationPreferences(
        azureTableStorageWrapper,
        organizationId,
        OrganizationPreferenceKey.PRIMARYMEASURETYPE
      )

      if (orgPreferences.length > 0) {
        return (
          (orgPreferences[0]?.Value as PrimaryMeasureTypeConstants) ??
          PrimaryMeasureTypeConstants.HospitalMeasures
        )
      } else {
        return PrimaryMeasureTypeConstants.HospitalMeasures // Default if no primary measure type is found
      }
    }
  }

  async updateUserProfileAsync(
    user: CitcUserProfleUpdateOptions
  ): Promise<User | null> {
    return (
      (
        await this.fetchWithType<User>(
          `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/users/user/update`,
          {
            method: 'POST',
            body: JSON.stringify(user),
          }
        )
      ).data ?? null
    )
  }
}

export default CitCUsersService
