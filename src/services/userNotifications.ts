import type { UserNotification } from '@/types/userNotification'
import { StatusResponse } from '@/types/statusResponse'
import { GuidGenerator } from '@/lib/utils'
import { getUserNotifications } from './userNotifications/getUserNotifications'
import { AzureTableStorageWrapper } from './azure/tableStorageWrapper'
import { sendNotification } from './userNotifications/sendNotification'
import dayjs from 'dayjs'
import { SelectionType } from '@/enums/selectionType'

class UserNotificationsService {
  async getNotifications(
    tableStorage: AzureTableStorageWrapper,
    userId: string,
    accessToken: string,
    limit: number,
    order: 'asc' | 'desc',
    organizationId: string,
    selectionType: SelectionType
  ): Promise<UserNotification[]> {
 // Define a controlled limit constant to avoid unbounded fetches
    const MAX_NOTIFICATIONS = 1000;
    const notifications = await getUserNotifications(
      tableStorage,
      userId,
      MAX_NOTIFICATIONS,
      {
        organizationId,
        selectionType,
      }
    )

    // Clone the notifications array and sort in strict descending order
    const sortedNotifications = [...notifications].sort((a, b) =>
      new Date(b.sentDateTime).getTime() - new Date(a.sentDateTime).getTime()
    )
    // Then apply limit
    return sortedNotifications.slice(0, limit)
  }

  async sendNotification(
    tableStorage: AzureTableStorageWrapper,
    senderUserId: string,
    message: string,
    receiverIDs: string[]
  ): Promise<StatusResponse> {
    let notificationId = GuidGenerator.standard()

    await Promise.all(
      receiverIDs.map((userId) =>
        sendNotification(tableStorage, {
          partitionKey: userId,
          rowKey: notificationId,
          ownerId: senderUserId,
          message,
          sentDateTime: dayjs.utc().toDate(),
          readDateTime: null,
        } as UserNotification)
      )
    )

    return {
      success: true,
      message: 'Notifications have been sent successfully',
    }
  }

  async getSentNotifications(
    tableStorage: AzureTableStorageWrapper,
    userId: string
  ): Promise<UserNotification[]> {
    const notifications = await tableStorage.queryEntities<UserNotification>(
      tableStorage.generateODataQuery({
        ownerId: userId,
      })
    )

    return notifications.sort(
      (a, b) =>
        new Date(b.sentDateTime).getTime() - new Date(a.sentDateTime).getTime()
    )
  }
}

export default UserNotificationsService
