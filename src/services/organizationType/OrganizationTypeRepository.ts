import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'
import { type Connection, type Pool } from 'snowflake-sdk'
import { OrganizationType } from '@/types/organizationType'

export class OrganizationTypeRepository extends SnowflakeRepository<OrganizationType> {
  constructor(connectionPool: Pool<Connection>, tableName: string) {
    super(connectionPool, tableName, 'Id')
  }

  async findOrganizationTypesByEntityTypes() {
    const sql = `
     Select 
       Distinct(ot."Code") as "OrganizationTypeCode",
       et."Code" AS "EntityTypeCode"
     from MECA."OrganizationTypes" AS ot
     inner join MECA."Entities" AS e on e."OrganizationTypeId" = ot."Id"
     inner join MECA."EntityTypes" AS et on et."Id" = e."EntityTypeId"
     `
    return (await this.execute<{ OrganizationTypeCode: string, EntityTypeCode: string }>(sql, []))!
  }
}
