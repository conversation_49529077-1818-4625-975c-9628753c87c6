import { UserNotification } from '@/types/userNotification'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { MigrationConfig } from '@/types/migrationConfig'
import { PartnerStorageType } from '@/enums/partnerStorageType'
import CitCUsersService from '../citCUsers'
import { upsertUserNotification } from './upsertNotification'
import { migrateData } from '../migration/migrateData'
import { HUBPrismaClient } from '@/server/db'

export const getUserNotifications = async (
  storageTable: AzureTableStorageWrapper,
  userId: string,
  limit: number,
  migrationConfig?: MigrationConfig
) => {
  const existingNotifications =
    await storageTable.queryEntities<UserNotification>(
      storageTable.generateODataQuery({
        PartitionKey: userId,
      })
    )

  if (migrationConfig && existingNotifications.length === 0) {
    await migrateData({
      storage: storageTable,
      migrationConfig,
      prismaClientType: 'hubClient',
      partnerStorageType: PartnerStorageType.Notifications,
      prismaQuery: (prismaClient: HUBPrismaClient) =>
        prismaClient.userNotificationMap.findMany({
          where: { UserId: userId },
          include: { UserNotifications: true },
          take: limit,
        }),
      upsertFunction: async (storage, data) => {
        const { organizationId, accessToken } = migrationConfig

        const citCUsersService = new CitCUsersService(accessToken!)

        const orgUsers =
          await citCUsersService.GetAllUsersByOrganizationAsync(organizationId)

        return await Promise.all(
          data.map((notification) => {
            const user = orgUsers.find(
              (user) => user.userId === notification.UserNotifications.OwnerId
            )

            return upsertUserNotification(storage, {
              partitionKey: notification.UserId,
              rowKey: notification.NotificationId,
              message: notification.UserNotifications.Message,
              ownerId: user?.userId ?? 'n/a',
              readDateTime: notification.ReadDateTime ?? null,
              sentDateTime: notification.UserNotifications.SentDateTime,
            })
          })
        )
      },
    })
  }

  return storageTable.queryEntities<UserNotification>(
    storageTable.generateODataQuery({
      PartitionKey: userId,
    }),
    ['*'],
    limit
  )
}
