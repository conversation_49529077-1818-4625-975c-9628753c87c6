import {
  EntityOrganizationTypeRepository,
  EntityCodeType,
} from './EntityOrganizationTypeRepository'
import {
  OrganizationTypesRequest,
  EntityDescriptionRequest,
  EntityDescriptionResponse,
} from '@/types/reports/medisolvReport'

export class EntityOrganizationTypeService {
  private readonly repository: EntityOrganizationTypeRepository

  constructor(repository: EntityOrganizationTypeRepository) {
    this.repository = repository
  }

  async getOrganizationTypes(
    reportRequest: OrganizationTypesRequest
  ): Promise<EntityCodeType[]> {
    try {
      return await this.repository.findEntityUniversesByMeasureAndStartDate(
        reportRequest.intervalType,
        reportRequest.startDate,
        reportRequest.endDate,
        reportRequest.measureIdentifiers
      )
    } catch (error) {
      console.error('Error fetching organization types:', error)
      return []
    }
  }

  async getHigherLevelOrganizationTypes(
    reportRequest: OrganizationTypesRequest
  ): Promise<EntityCodeType[]> {
    try {
      return await this.repository.findHigherLevelOrganizationTypes(
        reportRequest.code!,
        reportRequest.intervalType,
        reportRequest.startDate,
        reportRequest.endDate,
        reportRequest.measureIdentifiers,
        reportRequest.universe!
      )
    } catch (error) {
      console.error('Error fetching organization types:', error)
      return []
    }
  }

  async getEntityDescription(
    request: EntityDescriptionRequest
  ): Promise<EntityDescriptionResponse[]> {
    try {
      // If universe is not provided or empty, return empty array
      if (!request.universe) {
        console.warn('Universe is not provided for getEntityDescription')
        return []
      }

      return await this.repository.findEntityDescriptionByUniverseAndIntervalAndMeasure(
        request.universe,
        request.intervalType,
        request.startDate,
        request.endDate,
        request.measureIdentifiers
      )
    } catch (error) {
      console.error('Error fetching entity descriptions:', error)
      return []
    }
  }
}
