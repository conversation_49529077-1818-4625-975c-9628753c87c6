import { IntervalType } from '@/types/intervalType'
import dayjs from 'dayjs'
import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'

export interface EntityOrganizationType {
  EntityId: number
  OrganizationTypeId: string
  Universe: string
}

export interface EntityCodeType {
  Universe: string
  Code: string
}

export class EntityOrganizationTypeRepository extends SnowflakeRepository<EntityOrganizationType> {
  async findEntityUniversesByMeasureAndStartDate(
    intervalType: IntervalType,
    startDate: Date,
    endDate: Date,
    measureIdentifiers: string[]
  ) {
    const fromDate = dayjs(startDate).format('YYYY-MM-DD')
    const toDate = dayjs(endDate).format('YYYY-MM-DD')
    const placeholders = measureIdentifiers.map(() => '?').join(', ')

    const query = `
            SELECT DISTINCT
                eot."Universe" AS "Universe",
                MAX(CAST(ot."Code" AS FLOAT)) AS "Code"
            FROM
                "MECA"."OrganizationTypes" ot
                    JOIN "MECA"."Entities" e
                         ON ot."Id" = e."OrganizationTypeId"
                    JOIN "MPRE"."EntityOrganizationType" eot
                         ON e."Id" = eot."EntityId"
            WHERE
                e."Id" IN (
                    SELECT ss."EntitiesId"
                    FROM "MPRE"."SisenseMeasSumm" ss
                             JOIN "MPRE"."SisenseMeasures" sm
                                  ON ss."MeasureGUID" = sm."MedisolvMeasureId"
                    WHERE
                        ss."Period" = ?
                      AND ss."StartDate" >= ?
                      AND ss."StartDate" < ?
                      AND sm."MedisolvMeasureId" IN (${placeholders})
                )
              AND eot."Universe" <> 'ACO'
            GROUP BY
                eot."Universe"
            ORDER BY
                MAX(CAST(ot."Code" AS FLOAT))
                `

    const binds = [intervalType, fromDate, toDate, ...measureIdentifiers]

    return (await this.execute<EntityCodeType>(query, binds))!
  }

  async findHigherLevelOrganizationTypes(
    code: number,
    intervalType: IntervalType,
    startDate: Date,
    endDate: Date,
    measureIdentifiers: string[],
    universe: string
  ) {
    const fromDate = dayjs(startDate).format('YYYY-MM-DD')
    const toDate = dayjs(endDate).format('YYYY-MM-DD')
    const placeholders = measureIdentifiers.map(() => '?').join(', ')

    const query = `
            SELECT DISTINCT
                eot."Universe" AS "Universe",
                MAX(CAST(ot."Code" AS FLOAT)) AS "Code"
            FROM MECA."OrganizationTypes" ot
                     JOIN "MECA"."Entities" e
                          ON ot."Id" = e."OrganizationTypeId"
                     JOIN MPRE."EntityOrganizationType" eot
                          ON e."Id" = eot."EntityId"
            WHERE CAST(ot."Code" AS FLOAT) < ?
              AND e."Id" IN (SELECT DISTINCT s."EntitiesId"
                               FROM MPRE."SisenseMeasSumm" s
                                        JOIN MPRE."SisenseMeasures" m
                                             ON s."MeasureGUID" = m."MedisolvMeasureId"
                               WHERE s."Period" = ?
                                 AND s."StartDate" >= ?
                                 AND s."StartDate" < ?
                                 AND m."MedisolvMeasureId" IN (${placeholders}))
              AND eot."Universe" <> ?
              AND eot."Universe" <> 'Hospital'
              AND eot."Universe" <> 'ACO'
            GROUP BY eot."Universe"
            ORDER BY MAX(CAST(ot."Code" AS FLOAT))`

    const binds = [
      code,
      intervalType,
      fromDate,
      toDate,
      ...measureIdentifiers,
      universe,
    ]

    return (await this.execute<EntityCodeType>(query, binds))!
  }

  async findEntityDescriptionByUniverseAndIntervalAndMeasure(
    universe: string,
    intervalType: IntervalType,
    startDate: Date,
    endDate: Date,
    measureIdentifiers: string[]
  ) {
    const fromDate = dayjs(startDate).format('YYYY-MM-DD')
    const toDate = dayjs(endDate).format('YYYY-MM-DD')
    const placeholders = measureIdentifiers.map(() => '?').join(', ')

    const query = `
            SELECT
                "Description",
                "SourceContainerIdentifier"
            FROM
                "MECA"."Entities"
            WHERE
                "Id" IN (
                    SELECT
                        "EntityId"
                    FROM
                        "MPRE"."EntityOrganizationType"
                    WHERE
                        "Universe" = ?
                )
              AND "Id" IN (
                SELECT
                    "EntitiesId"
                FROM
                    "MPRE"."SisenseMeasSumm"
                WHERE
                    "Period" = ?
                  AND "StartDate" >= ?
                  AND "StartDate" < ?
                  AND "MeasureGUID" IN (
                    SELECT
                        "MedisolvMeasureId"
                    FROM
                        "MPRE"."SisenseMeasures"
                    WHERE
                        "MedisolvMeasureId" IN (${placeholders})
                )
            )
            GROUP BY
                "Description",
                "SourceContainerIdentifier"
            ORDER BY
                "Description",
                "SourceContainerIdentifier"
        `

    const binds = [
      universe,
      intervalType,
      fromDate,
      toDate,
      ...measureIdentifiers,
    ]

    return (await this.execute<{
      Description: string
      SourceContainerIdentifier: string
    }>(query, binds))!
  }
}
