import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'
import { IntervalType } from '@/types/intervalType'
import { type Bind } from 'snowflake-sdk'
import { TimePeriod } from '@/types/TimePeriod'
import { PerformanceRateByMeasureAndDateResult } from '@/types/performanceRateByMeasureAndDateResult'
import { applyFilters } from '@/services/snowflake/SnowflakeHelper'
import dayjs from 'dayjs'

/**
 * Type representing a filter condition for SQL queries
 */
type FilterCondition = {
  value: any
  column: string
  operator?: string
}

/**
 * Interface representing the SisenseMeasureSummary data structure
 */
export interface SisenseMeasureSummary {
  Period: string
  StartDate: string
  MeasureName: string
  EntityId: number
  EntityDescription: string
  Organization: string
  SourceContainerIdentifier: string
  Performance: number
  Numerator: number
  PerformanceDenominator: number
  PTile: number
  PercentileSourceCode: string
  SummaryDataRowCount: number
  MeasureIdentifier: string
  ApplicationName?: string
}

/**
 * Interface representing the SisenseMeasureByPercentile data structure
 */
export interface SisenseMeasureByPercentile {
  PercentileCode: string
  Period: string
  StartDate: string
  MeasureName: string
  EntityDescription: string
  Organization: string
  Performance: number
  Numerator: number
  PerformanceDenominator: number
  PTile: number
}

/**
 * Repository for accessing SisenseMeasureSummary data from Snowflake
 */
export class SisenseMeasureSummaryRepository extends SnowflakeRepository<SisenseMeasureSummary> {
  constructor(connectionPool: any, tableName: string = 'MPRE.SisenseMeasSumm') {
    // Use a dummy identity field since we'll be using custom SQL queries
    super(connectionPool, tableName, 'Id')
  }

  /**
   * Fetches measure summary data based on provided parameters
   *
   * @param intervalType - The period type (M: Monthly, Q: Quarterly, Y: Yearly)
   * @param startDate - The start date in YYYY-MM-DD format
   * @param measure - The measure name
   * @param codeNumeric - The organization type code
   * @param entityId - Optional entity ID filter
   * @param sourceContainer - Optional source container identifier filter
   * @returns Promise resolving to an array of SisenseMeasureSummary objects or undefined
   */
  async findMeasurePerformanceByUniverse(
    intervalType: IntervalType,
    startDate: Date,
    endDate: Date,
    measureNames: string[],
    universe: string,
    sourceContainerIdentifier?: string
  ): Promise<SisenseMeasureSummary[] | undefined> {
    const fromDate = dayjs(startDate).format('YYYY-MM-DD')
    const toDate = dayjs(endDate).format('YYYY-MM-DD')
    const placeholders = measureNames.map(() => '?').join(', ')
    const sourceContainerIdentifierPredicate = sourceContainerIdentifier
      ? 'AND ent."SourceContainerIdentifier" = ?'
      : ''

    let sqlText = `
      SELECT
        ent."Id"                                                AS "EntityId",
        ent."Description"                                       AS "EntityDescription",
        sm."Organization"                                       AS "Organization",
        DATE_TRUNC('MONTH', sm."StartDate")                     AS "StartDate",
        sm."Period"                                             AS "Period",
        m."Long Measure Name"                                   AS "MeasureName",
        sm."Performance"                                        AS "Performance",
        sm."Numerator"                                          AS "Numerator",
        sm."PerformanceDenominator"                             AS "PerformanceDenominator",
        ps."Code"                                               AS "PercentileSourceCode",
        SUM(sm."PTile" / 10)                                    AS "PTile",
        sm."MeasureGUID"                                        AS "MeasureIdentifier"
      FROM
        "MPRE"."SisenseMeasSumm" sm
          JOIN "MPRE"."SisenseMeasures" m
               ON sm."MeasureGUID" = m."MedisolvMeasureId"
          JOIN "MECA"."PercentileSources" ps
               ON sm."PercentileSourceId" = ps."Id"
          JOIN "MECA"."Entities" ent
               ON sm."EntitiesId" = ent."Id"
      WHERE
        sm."Period" = ?
        AND sm."StartDate" >= ?
        AND sm."StartDate" < ?
        AND sm."SummaryDataRowCount" = 1
        AND m."MedisolvMeasureId" IN (${placeholders})
        AND ent."Id" IN (
          SELECT "EntityId"
          FROM "MPRE"."EntityOrganizationType"
          WHERE "Universe" = ?
        )
        ${sourceContainerIdentifierPredicate}
        GROUP BY
          ent."Id", ent."Description", sm."Organization",
          DATE_TRUNC('MONTH', sm."StartDate") ,
          sm."Period", m."Long Measure Name",
          sm."Performance", sm."Numerator",
          sm."PerformanceDenominator", ps."Code",
          sm."MeasureGUID"
        ORDER BY
          ent."Id", ent."Description", sm."Organization",
          DATE_TRUNC('MONTH', sm."StartDate") ,
          sm."Period", m."Long Measure Name",
          sm."Performance", sm."Numerator",
          sm."PerformanceDenominator", ps."Code"
    `

    // Create the binds array for the parameters
    const binds = [intervalType, fromDate, toDate, ...measureNames, universe]
    if (sourceContainerIdentifier) {
      binds.push(sourceContainerIdentifier)
    }

    // Execute the query
    return this.execute(sqlText, binds)
  }

  async findMeasurePerformanceByOrgCode(
    intervalType: IntervalType,
    startDate: Date,
    endDate: Date,
    measureNames: string[],
    sourceContainerIdentifier: string,
    code: number
  ): Promise<SisenseMeasureSummary[] | undefined> {
    const fromDate = dayjs(startDate).format('YYYY-MM-DD')
    const toDate = dayjs(endDate).format('YYYY-MM-DD')
    const placeholders = measureNames.map(() => '?').join(', ')

    let sqlText = `
      SELECT
        ent."Id"                                                AS "EntityId",
        ent."Description"                                       AS "EntityDescription",
        sm."Organization"                                       AS "Organization",
        DATE_TRUNC('MONTH', sm."StartDate")                     AS "StartDate",
        sm."Period"                                             AS "Period",
        meas."Long Measure Name"                                AS "MeasureName",
        sm."Performance"                                        AS "Performance",
        sm."Numerator"                                          AS "Numerator",
        sm."PerformanceDenominator"                             AS "PerformanceDenominator",
        ps."Code"                                               AS "PercentileSourceCode",
        SUM(sm."PTile" / 10)                                    AS "PTile",
        sm."MeasureGUID"                                        AS "MeasureIdentifier"
      FROM "MPRE"."SisenseMeasSumm" sm
             JOIN "MPRE"."SisenseMeasures" meas
                  ON sm."MeasureGUID" = meas."MedisolvMeasureId"
             JOIN "MECA"."PercentileSources" ps
                  ON sm."PercentileSourceId" = ps."Id"
             JOIN "MECA"."Entities" ent
                  ON sm."EntitiesId" = ent."Id"
      WHERE sm."Period" = ?
        AND sm."StartDate" >= ? 
        AND sm."StartDate" < ?
        AND sm."SummaryDataRowCount" = 1
        AND meas."MedisolvMeasureId" IN (${placeholders})
        AND ent."SourceContainerIdentifier" = ?
        AND ent."OrganizationTypeId" IN (
          SELECT "Id"
          FROM "MECA"."OrganizationTypes"
          WHERE TRY_CAST("Code" AS FLOAT) = ?
      )
      GROUP BY
        ent."Id",
        ent."Description",
        sm."Organization",
        DATE_TRUNC('MONTH', sm."StartDate"),
        sm."Period",
        meas."Long Measure Name",
        sm."Performance",
        sm."Numerator",
        sm."PerformanceDenominator",
        ps."Code",
        sm."MeasureGUID"
      ORDER BY
        ent."Description"
    `

    const binds = [
      intervalType,
      fromDate,
      toDate,
      ...measureNames,
      sourceContainerIdentifier,
      code,
    ]

    // Execute the query
    return this.execute(sqlText, binds)
  }

  /**
   * Find performance rates by date and measure for hospital
   * @param entityId The entity ID
   * @param medisolvMeasureId The Medisolv measure ID
   * @param timePeriod The time period (Monthly, Quarterly, Yearly)
   * @param dateStart The start date
   * @param dateEnd The end date
   * @returns A list of performance rate results
   */
  findPerformanceRatesByDateAndMeasureForHospital = async (
    entityId: bigint,
    medisolvMeasureId: string,
    timePeriod: TimePeriod,
    dateStart: Date,
    dateEnd: Date
  ): Promise<PerformanceRateByMeasureAndDateResult[]> => {
    let periodUnit: string
    let periodValue: string

    switch (timePeriod) {
      case TimePeriod.Monthly:
        periodUnit = 'MONTH'
        periodValue = 'M'
        break
      case TimePeriod.Quarterly:
        periodUnit = 'QUARTER'
        periodValue = 'Q'
        break
      case TimePeriod.Yearly:
        periodUnit = 'YEAR'
        periodValue = 'Y'
        break
      default:
        throw `Invalid time period, ${timePeriod}`
    }

    // Build the SQL query - Snowflake dialect
    const sql = `
            SELECT 
                "ReferenceDate" as "date",
                MAX("Performance") AS "performance",
                MAX("Numerator") AS "numerator",
                MAX("DenominatorOnly") AS "denominator"
            FROM (
                SELECT 
                    DATEADD(
                        ${periodUnit}, 
                        DATEDIFF(${periodUnit}, '1970-01-01', "StartDate"), 
                        '1970-01-01'
                    ) AS "ReferenceDate",
                    "Performance",
                    "Numerator",
                    "DenominatorOnly"
                FROM 
                    ${this.tableName}
                WHERE 
                    "Period" = ?
                    AND ("StartDate" >= ? AND "StartDate" < ?)
                    AND "EntitiesId" ILIKE ?
                    AND "MeasureGUID" ILIKE ?
            )
            GROUP BY "ReferenceDate"
            ORDER BY "ReferenceDate"
            LIMIT 50000
        `

    // Prepare parameters
    const params: Bind[] = [
      periodValue,
      dateStart.toISOString(),
      dateEnd.toISOString(),
      entityId.toString(),
      medisolvMeasureId,
    ]

    // Execute the query
    const results = await this.execute<PerformanceRateByMeasureAndDateResult>(
      sql,
      params
    )

    // Ensure date is properly converted to Date object
    return results!.map((result) => ({
      ...result,
      date: new Date(result.date),
    }))
  }
}
