import { UserPreference } from '@/types/userPreference'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { migrateData } from '../migration/migrateData'
import { MigrationConfig } from '@/types/migrationConfig'
import { ADMPrismaClient } from '@/server/db'
import { upsertUserPreference } from './upsertUserPreference'

export const getAllUserPreferences = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  userId: string,
  migrationConfig?: MigrationConfig
): Promise<UserPreference[]> => {
  // TODO: Enhancement* Add try/catch w/ app insights
  const existingPreferences =
    await tableStorageWrapper.queryEntities<UserPreference>(
      tableStorageWrapper.generateODataQuery({
        PartitionKey: userId,
      })
    )

  if (migrationConfig && existingPreferences.length === 0) {
    // TODO: Enhancement* Add try/catch w/ app insights
    await migrateData({
      storage: tableStorageWrapper,
      migrationConfig,
      prismaClientType: 'admClient',
      prismaQuery: (prismaClient: ADMPrismaClient) =>
        prismaClient.userPreferences.findMany({
          where: {
            UserId: userId,
          },
        }),
      upsertFunction: async (storage, data) =>
        // TODO: Enhancement* Add try/catch w/ app insights
        await Promise.all(
          data.map((preference) =>
            upsertUserPreference(storage, {
              partitionKey: userId,
              rowKey: preference.Id.toString(),
              userId,
              defaultModuleId: preference.DefaultModuleId,
              dashboardEnabledOverride: preference.DashboardEnabledOverride,
            })
          )
        ),
    })
  }

  return tableStorageWrapper.queryEntities(
    tableStorageWrapper.generateODataQuery({
      PartitionKey: userId,
    })
  )
}
