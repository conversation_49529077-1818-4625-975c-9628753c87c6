import { MeasureGroup } from '@/types/measureGroup'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { migrateData } from '../migration/migrateData'
import { MigrationConfig } from '@/types/migrationConfig'
import { ADMPrismaClient } from '@/server/db'
import { upsertMeasureGroup } from './upsertMeasureGroup'

export const getAllMeasureGroups = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  measureGuid: string,
  migrationConfig?: MigrationConfig
) => {
  const existingMeasureGroups =
    await tableStorageWrapper.queryEntities<MeasureGroup>(
      tableStorageWrapper.generateODataQuery({
        PartitionKey: measureGuid,
      })
    )

  if (migrationConfig && existingMeasureGroups.length === 0) {
    await migrateData({
      storage: tableStorageWrapper,
      migrationConfig,
      prismaClientType: 'admClient',
      prismaQuery: (prismaClient: ADMPrismaClient) =>
        prismaClient.measureGroup.findMany({
          where: {
            MeasureGuid: measureGuid,
          },
        }),
      upsertFunction: async (storage, data) =>
        await Promise.all(
          data.map((measureGroup) =>
            upsertMeasureGroup(storage, {
              partitionKey: measureGuid,
              rowKey: measureGroup.Id.toString(),
              groupId: measureGroup.GroupId,
              measureGuid: measureGroup.MeasureGuid,
              percentileSourceId: measureGroup.PercentileSourceId,
            })
          )
        ),
    })
  }

  return tableStorageWrapper.queryEntities<MeasureGroup>(
    tableStorageWrapper.generateODataQuery({
      PartitionKey: measureGuid,
    })
  )
}
