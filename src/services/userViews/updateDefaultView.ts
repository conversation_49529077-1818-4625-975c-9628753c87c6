import type { SavedView } from '@/types/savedView'
import { ViewMetaData } from '@/types/savedViewModel'
import { StatusResponse } from '@/types/statusResponse'
import { getAllSavedViews } from '../savedViews/getAllSavedViews'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { safeParse } from '@/lib/safeParse'
import { upsertSavedView } from '../savedViews/upsertSavedView'
import { DefaultNames } from '@/enums/defaultNames'

const updateDefaultView = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  userId: string,
  organizationId: string,
  viewId: string,
  page: string,
  measureType: string
): Promise<StatusResponse> => {
  let views: SavedView[] = []

  views = await getAllSavedViews(tableStorageWrapper, userId, organizationId)

  views = views.filter((x) => x.viewMetadata.includes('version'))

  let currentDefaultView = views.find(
    (x) => x.isDefault && safeParse<ViewMetaData>(x.viewMetadata)?.Page == page
  )

  if (currentDefaultView) {
    currentDefaultView.isDefault = false
    await upsertSavedView(tableStorageWrapper, currentDefaultView)
  }

  if (viewId === '0') {
    // Find the Medisolv Default View for this page
    const medisolvDefaultView = views.find(
      (x) =>
        x.viewName === DefaultNames.SavedView &&
        safeParse<ViewMetaData>(x.viewMetadata)?.Page == page
    )

    if (medisolvDefaultView) {
      medisolvDefaultView.isDefault = true
      await upsertSavedView(tableStorageWrapper, medisolvDefaultView)
    } else {
      // If Medisolv Default View doesn't exist for this page, create one
      const metadata: ViewMetaData = {
        Page: page,
        Settings: JSON.stringify({
          version: 'nextGen',
          measureType: measureType,
        }),
        ViewType: 'Quarterly',
        From: new Date(),
        To: new Date(),
        IsFavorite: false,
      }

      const newDefaultView: SavedView = {
        partitionKey: userId,
        rowKey: '0',
        userId,
        organizationId, // Include organizationId
        viewName: DefaultNames.SavedView,
        viewMetadata: JSON.stringify(metadata),
        isDefault: true,
        isShared: false,
      }

      await upsertSavedView(tableStorageWrapper, newDefaultView)
    }

    return {
      success: true,
      message: 'Medisolv Default View was marked as the default View',
    } as StatusResponse
  }

  const targetView = views.find((x) => x.rowKey === viewId)

  if (!targetView)
    return {
      success: false,
      message: 'An unknown view id was passed',
    } as StatusResponse

  targetView.isDefault = true

  await upsertSavedView(tableStorageWrapper, targetView)

  return {
    success: true,
    message: `${targetView.viewName} was marked as the default View`,
  } as StatusResponse
}

export default updateDefaultView
