import type {
  SavedViewModel,
  ViewMetaData,
  ViewSettingsModel,
} from '@/types/savedViewModel'
import type { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { DefaultNames } from '@/enums/defaultNames'
import { safeParse } from '@/lib/safeParse'
import { calculateQuarterDates } from '@/stores/dates'
import dayjs from 'dayjs'
import { getAllSavedViews } from '../savedViews/getAllSavedViews'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { SelectionType } from '@/enums/selectionType'

const getViews = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  organizationId: string,
  userId: string,
  currentPage: string,
  selectionType: SelectionType,
  measureTypes: PrimaryMeasureTypeConstants[]
): Promise<SavedViewModel[]> => {
  let result = await getAllSavedViews(
    tableStorageWrapper,
    userId,
    organizationId,

    {
      organizationId,
      selectionType,
    }
  )

  // Filter out views whose metadata doesn't have 'version' in Settings.
  result = result.filter((view) => {
    const metadata = safeParse<ViewMetaData>(view.viewMetadata)
    return metadata?.Settings && metadata.Settings.indexOf('version') >= 0
  })

  const views: SavedViewModel[] = []

  const { startDate, endDate } = calculateQuarterDates()

  // Push default "Quarterly" views for each measureType if they don't exist
  measureTypes.forEach((primaryMeasureType) => {
    // Pre-filter views for this page and measure type
    const pageAndMeasureTypeViews = result.filter(
      (view) =>
        view.viewMetadata.includes(currentPage) &&
        view.viewMetadata.includes(primaryMeasureType)
    )

    // Extract default condition into a dedicated variable
    const isDefaultMedisolvView = pageAndMeasureTypeViews.some(
      (view) => view.isDefault && view.viewName === DefaultNames.SavedView
    )

    // Check if any view is set as default for this page and measureType
    const isOtherDefaultViewSet = pageAndMeasureTypeViews.some(
      (view) => view.isDefault
    )

    const isFavorite = pageAndMeasureTypeViews.some(
      (view) =>
        safeParse<ViewMetaData>(view.viewMetadata)?.IsFavorite &&
        view.viewName === DefaultNames.SavedView
    )

    // Add the default - 'Medisolv Default View'
    views.push({
      id: '0',
      settings: { measureType: primaryMeasureType } as ViewSettingsModel,
      viewName: DefaultNames.SavedView,
      viewType: 'Quarterly',
      page: currentPage,
      from: startDate,
      to: endDate,
      isDefault: isDefaultMedisolvView || !isOtherDefaultViewSet,
      isFavorite: isFavorite,
      isShared: false,
    })
  })

  // Process any additional views (not the default quarterly ones)
  result
    .filter((x) => x.viewName !== DefaultNames.SavedView)
    .forEach((item) => {
      try {
        const metadata = safeParse<ViewMetaData>(item.viewMetadata)
        if (metadata) {
          const viewItem: SavedViewModel = {
            id: item.rowKey!,
            settings: JSON.parse(metadata.Settings) as ViewSettingsModel,
            viewName: item.viewName,
            viewType: metadata.ViewType,
            page: metadata.Page,

            from: dayjs(metadata.From).utc(),
            to: dayjs(metadata.To).utc(),

            isDefault: item.isDefault ?? false,
            isShared: item.isShared ?? false,
            isFavorite: metadata.IsFavorite,
          }
          views.push(viewItem)
        }
      } catch (error) {
        console.error('Error parsing ViewMetadata:', error)
      }
    })

  // Sort favorites first, then by viewName
  const response = views.sort((a, b) => {
    if (a.isFavorite === b.isFavorite) {
      return a.viewName.localeCompare(b.viewName)
    }
    return a.isFavorite ? -1 : 1
  })

  return response
}

export default getViews
