import type { ViewMetaData } from '@/types/savedViewModel'
import { DefaultNames } from '@/enums/defaultNames'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { upsertSavedView } from '../savedViews/upsertSavedView'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { findSavedViewById } from '../savedViews/findSavedViewById'
import { findSavedViewByName } from '../savedViews/findSavedViewByName'
import { getAllSavedViews } from '../savedViews/getAllSavedViews'
import dayjs from 'dayjs'
import { safeParse } from '@/lib/safeParse'

export const updateFavoriteView = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  userId: string,
  organizationId: string,
  viewId?: string,
  isFavorite?: boolean,
  page?: string,
  primaryMeasureType?: PrimaryMeasureTypeConstants
) => {
  try {
    const isDefaultView = viewId === '0'
    let targetView

    if (isDefaultView) {
      targetView = await handleDefaultView(
        tableStorageWrapper,
        userId,
        organizationId,
        page!,
        primaryMeasureType!,
        isFavorite
      )
    } else {
      targetView = await findSavedViewById(tableStorageWrapper, userId, viewId!)
      if (!targetView) {
        return {
          statusMesasge: 'An unknown view id was passed',
          isSuccess: false,
        }
      }
    }

    const metadata = safeParse<ViewMetaData>(targetView.viewMetadata)
    if (!metadata) {
      return {
        statusMesasge: 'Invalid view metadata format',
        isSuccess: false,
      }
    }

    metadata.IsFavorite = !!isFavorite

    const { ...viewToUpdate } = targetView
    viewToUpdate.viewMetadata = JSON.stringify(metadata)

    await upsertSavedView(tableStorageWrapper, viewToUpdate)

    return {
      statusMesasge: isFavorite
        ? `"${targetView.viewName}" was marked as a favorite View`
        : `"${targetView.viewName}" was removed from favorite views.`,
      isSuccess: true,
    }
  } catch (error) {
    console.log(
      `Unable to update favorite for view ${viewId}`,
      error instanceof Error ? error.message : 'Unknown error'
    )
    throw error
  }
}

const handleDefaultView = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  userId: string,
  organizationId: string,
  page: string,
  primaryMeasureType: PrimaryMeasureTypeConstants,
  isFavorite: boolean | undefined
) => {
  const viewsByName = await findSavedViewByName(
    tableStorageWrapper,
    userId,
    organizationId,
    DefaultNames.SavedView
  )

  const existingView = viewsByName.find(
    (view) =>
      view.viewMetadata.includes(`"page":"${page}"`) ||
      view.viewMetadata.includes(`"Page":"${page}"`)
  )

  if (existingView) {
    return existingView
  }

  const defaultViewMetadata: ViewMetaData = {
    Page: page,
    Settings: JSON.stringify({
      version: 'nextGen',
      measureType: primaryMeasureType,
    }),
    From: dayjs.utc('0001-01-01').toDate(),
    To: dayjs.utc('0001-01-01').toDate(),
    IsFavorite: true,
    ViewType: 'Quarterly',
  }

  return upsertSavedView(tableStorageWrapper, {
    partitionKey: userId,
    rowKey: '0',
    userId,
    viewName: DefaultNames.SavedView,
    viewMetadata: JSON.stringify(defaultViewMetadata),
    isDefault: false,
    isShared: false,
  })
}
