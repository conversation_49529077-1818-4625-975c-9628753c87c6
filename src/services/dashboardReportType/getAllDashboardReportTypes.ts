import { DashboardReportType } from '@/types/dashboardReportType'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { MigrationConfig } from '@/types/migrationConfig'
import { upsertDashboardReportType } from './upsertDashboardReportType'
import { migrateData } from '../migration/migrateData'
import { HUBPrismaClient } from '@/server/db'

export const getAllDashboardReportTypes = async (
  tableStorage: AzureTableStorageWrapper,
  organizationId: string,
  migrationConfig?: MigrationConfig
) => {
  const existingReportTypes =
    await tableStorage.queryEntities<DashboardReportType>(
      tableStorage.generateODataQuery({
        PartitionKey: organizationId,
      })
    )

  if (migrationConfig && existingReportTypes.length === 0) {
    await migrateData({
      storage: tableStorage,
      migrationConfig,
      prismaClientType: 'hubClient',
      prismaQuery: (prismaClient: HUBPrismaClient) =>
        prismaClient.dashboardReportType.findMany(),
      upsertFunction: async (storage, data) =>
        await Promise.all(
          data.map((reportType) =>
            upsertDashboardReportType(storage, {
              partitionKey: organizationId,
              rowKey: reportType.ReportId.toString(),
              reportId: reportType.ReportId.toString(),
              reportType: reportType.ReportType,
            })
          )
        ),
    })
  }

  return tableStorage.queryEntities<DashboardReportType>(
    tableStorage.generateODataQuery({
      PartitionKey: organizationId,
    })
  )
}
