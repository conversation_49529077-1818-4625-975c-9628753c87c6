import { LoadStatus } from '@/types/loadStatus'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { env } from '@/env'

dayjs.extend(utc)

export const getLoadStatus = async (
  tableStorage: AzureTableStorageWrapper,
  organizationId: string,
  loadType: string,
): Promise<LoadStatus[]> => {
  const typePrefix = loadType.toLowerCase()

  const fileCount =
    loadType === loadType && env.EHLOADSTATUSRECENTFILESCOUNT
      ? env.EHLOADSTATUSRECENTFILESCOUNT
      : loadType === loadType && env.ECLOADSTATUSRECENTFILESCOUNT
        ? env.ECLOADSTATUSRECENTFILESCOUNT
        : 99

  // Get all records for this organization
  const allRecords = await tableStorage.queryEntities<LoadStatus>(
    tableStorage.generateODataQuery({
      PartitionKey: organizationId,
      type: loadType,
    })
  )

  // Filter and group rowKeys that match our pattern
  const groupedByDate: Record<string, LoadStatus[]> = {}

  for (const record of allRecords) {
    if (!record.rowKey?.startsWith(`timed_${typePrefix}_status_`)) continue

    const match = record.rowKey?.match(/^timed_[a-z]+_status_(\d{8})/)
    if (match?.[1]) {
      const date = match[1] // now inferred as string
      if (!groupedByDate[date]) groupedByDate[date] = []
      groupedByDate[date].push(record)
    }
  }

  const recentDates = Object.keys(groupedByDate)
    .sort((a, b) => parseInt(b) - parseInt(a)) // descending order
    .slice(0, fileCount)

  const finalStatuses = recentDates
    .flatMap((date) => groupedByDate[date])
    .filter(Boolean) as LoadStatus[]

  return finalStatuses
}
