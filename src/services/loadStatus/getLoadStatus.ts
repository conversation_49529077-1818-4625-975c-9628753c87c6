import { LoadStatus } from '@/types/loadStatus'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

export const getLoadStatus = async (
  tableStorage: AzureTableStorageWrapper,
  organizationId: string,
  loadType: 'EC' | 'EH',
  loadDate: string
) => {
  const typePrefix = loadType.toLowerCase()
  const rowKeyPrefix = `${typePrefix}${loadDate}`

  // Create range query for RowKey
  const rowKeyStart = rowKeyPrefix.toLowerCase()
  const rowKeyEnd = rowKeyPrefix.toLowerCase() + '\uffff'

  return tableStorage.queryEntities<LoadStatus>(
    tableStorage.generateODataQuery({
      PartitionKey: organizationId,
      RowKey: {
        value: [rowKeyStart, rowKeyEnd],
        range: true,
      },
    })
  )
}
