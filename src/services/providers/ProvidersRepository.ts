import { SnowflakeRepository } from "@/services/snowflake/SnowflakeRepository"
import { type Connection, type Pool, type Bind } from 'snowflake-sdk'
import { SnowflakeCSRCProvider } from "@/services/providers/CSRCProvider"
import { Provider } from "@/types/provider"
import { EntityTypeConstants } from "@/enums/entityTypeConstants"
import { Dayjs } from "dayjs"

export class ProvidersRepository extends SnowflakeRepository<SnowflakeCSRCProvider> {
    constructor(connectionPool: Pool<Connection>, tableName: string) {
        super(connectionPool, tableName, 'npi')
    }

    /**
     * Find providers by submission group
     * @param organizationId The organization ID
     * @param submissionGroupId The submission group ID
     * @param checkSourceContainerIdentifier Whether to check source container identifier
     * @param ecOverallGroupId The EC overall group ID
     * @param isPartner Whether the organization is a partner
     * @param suffix The suffix to append to the organization ID for source container identifier
     * @returns A list of providers
     */
    findProvidersBySubmissionGroup = async (
        organizationId: string,
        submissionGroupId: string,
        checkSourceContainerIdentifier: boolean,
        ecOverallGroupId: string,
        isPartner: boolean,
        suffix: string = ''
    ): Promise<Provider[]> => {

        let sourceContainerIdentifierPredicates = ''
        let sourceContainerIdentifierPredicateParameters: any[] = []
        if (checkSourceContainerIdentifier) {
            sourceContainerIdentifierPredicates = `AND e."SourceContainerIdentifier" = ?`
            sourceContainerIdentifierPredicateParameters = [`${organizationId}${suffix}`]
        }

        let entityIdsPredicates = ''
        let entityIdsPredicateParameters: any[] = []
        if (submissionGroupId === "*") {
            if (isPartner) {
                entityIdsPredicates = `
                        JOIN MECA."ActiveMeasures" am ON am."EntitiesId" = e."Id"
                        `
                entityIdsPredicateParameters = []
            } else {
                entityIdsPredicates = `
                        JOIN MECA."ActiveMeasures" am ON am."EntitiesId" = e."Id"
                        WHERE e."Code" LIKE CONCAT(?, '%')
                        
                        UNION
                        
                        SELECT DISTINCT e."Id", e."Code"
                        FROM AllGroupEntities e
                        JOIN MECA."ActiveMeasures" am ON am."EntitiesId" = e."Id"
                        WHERE e."Code" NOT LIKE '%^_%' ESCAPE '^'
                        `
                entityIdsPredicateParameters = [ecOverallGroupId]
            }
        } else {
            entityIdsPredicates = `
                    WHERE e."Code" = ?
                    AND EXISTS (
                        SELECT 1 
                        FROM MECA."ActiveMeasures" am 
                        WHERE am."EntitiesId" = e."Id"
                    )
            `
            entityIdsPredicateParameters = [submissionGroupId]
        }

        // Build the SQL query
        const sql = `
            WITH AllGroupEntities AS (
                -- Get all group entities
                SELECT 
                    e."Id", 
                    e."Code", 
                    e."Description",
                    e."SourceContainerIdentifier", 
                    ot."Code" as "OrganizationTypeCode"
                FROM MECA."Entities" e
                JOIN MECA."OrganizationTypes" ot ON e."OrganizationTypeId" = ot."Id"
            ),
            EntityIds AS (
                -- Get entity IDs based on submission group
                SELECT DISTINCT e."Id", e."Code"
                FROM AllGroupEntities e
                ${entityIdsPredicates}
                ${sourceContainerIdentifierPredicates}
            ),
            ProviderResults AS (
                -- Get provider results
                SELECT
                    p."ProviderNPI" AS "npi",
                    MIN(e2."Description") AS "providerName",
                    MIN(e."Code") AS "submissionGroupId",
                    MIN(e."SourceContainerIdentifier") AS "submissionGroupContainerIdentifier",
                    MIN(e2."Description") AS "providerWithEntityName"
                FROM MECA."EntityProviders" ep
                JOIN CSRC."Providers" p ON ep."ProvidersId" = p."Id"
                JOIN AllGroupEntities e ON ep."EntitiesId" = e."Id"  
                JOIN AllGroupEntities e2 ON p."ProviderNPI" = e2."Code"
                JOIN EntityIds ei ON ep."EntitiesId" = ei."Id"
                GROUP BY p."ProviderNPI"
            )
            -- Final result set
            SELECT * FROM ProviderResults ORDER BY "npi"
        `

        // Prepare parameters
        let params: Bind[] = [
            ...entityIdsPredicateParameters,
            ...sourceContainerIdentifierPredicateParameters,
        ]

        // Execute the query
        const results = (await this.execute(sql, params)) as unknown as Provider[]
        return results
    }

    /**
     * Find providers by submission group
     * @param organizationId The organization ID
     * @param submissionGroupId The submission group ID
     * @param startDate The start date
     * @param endDate The end date
     * @param checkSourceContainerIdentifier Whether to check source container identifier
     * @param ecOverallGroupId The EC overall group ID
     * @param isPartner
     * @returns A list of providers
     */
    findActiveProvidersBySubmissionGroup = async (
        organizationId: string,
        submissionGroupId: string,
        startDate: Dayjs,
        endDate: Dayjs,
        checkSourceContainerIdentifier: boolean,
        ecOverallGroupId: string,
        isPartner: boolean
    ): Promise<Provider[]> => {

        const fromDate = startDate.format('YYYY-MM-DD')
        const toDate = endDate.format('YYYY-MM-DD')

        let sourceContainerIdentifierPredicates = ''
        let sourceContainerIdentifierPredicateParameters: any[] = []
        if (checkSourceContainerIdentifier) {
            sourceContainerIdentifierPredicates = `AND e."SourceContainerIdentifier" = ?`
            sourceContainerIdentifierPredicateParameters = [organizationId]
        }

        let entityIdsPredicates = ''
        let entityIdsPredicateParameters: any[] = []
        if (submissionGroupId === "*") {
            if (isPartner) {
                entityIdsPredicates = `
                        JOIN MECA."ActiveMeasures" am ON am."EntitiesId" = e."Id"
                        WHERE YEAR(am."ProcessingStartDate") >= YEAR(TO_DATE(?))
                        AND YEAR(am."ProcessingStartDate") <= YEAR(TO_DATE(?))
                        `
                entityIdsPredicateParameters = [fromDate, toDate]
            } else {
                entityIdsPredicates = `
                        JOIN MECA."ActiveMeasures" am ON am."EntitiesId" = e."Id"
                        WHERE e."Code" LIKE CONCAT(?, '%')
                        AND YEAR(am."ProcessingStartDate") >= YEAR(TO_DATE(?))
                        AND YEAR(am."ProcessingStartDate") <= YEAR(TO_DATE(?))
                        
                        UNION
                        
                        SELECT DISTINCT e."Id", e."Code"
                        FROM AllGroupEntities e
                        JOIN MECA."ActiveMeasures" am ON am."EntitiesId" = e."Id"
                        WHERE e."Code" NOT LIKE '%^_%' ESCAPE '^'
                        AND YEAR(am."ProcessingStartDate") >= YEAR(TO_DATE(?))
                        AND YEAR(am."ProcessingStartDate") <= YEAR(TO_DATE(?))
                        `
                entityIdsPredicateParameters = [ecOverallGroupId, fromDate, toDate, fromDate, toDate]
            }
        } else {
            entityIdsPredicates = `
                    WHERE e."Code" = ?
                    AND EXISTS (
                        SELECT 1 
                        FROM MECA."ActiveMeasures" am 
                        WHERE am."EntitiesId" = e."Id"
                        AND YEAR(am."ProcessingStartDate") >= YEAR(TO_DATE(?))
                        AND YEAR(am."ProcessingStartDate") <= YEAR(TO_DATE(?))
                    )
            `
            entityIdsPredicateParameters = [submissionGroupId, fromDate, toDate]
        }

        // Build the SQL query
        const sql = `
            WITH AllGroupEntities AS (
                -- Get all group entities
                SELECT 
                    e."Id", 
                    e."Code", 
                    e."Description",
                    REPLACE(e."SourceContainerIdentifier", '_EC', '') as "SourceContainerIdentifier", 
                    ot."Code" as "OrganizationTypeCode"
                FROM MECA."Entities" e
                JOIN MECA."OrganizationTypes" ot ON e."OrganizationTypeId" = ot."Id"
            ),
            EntityIds AS (
                -- Get entity IDs based on submission group and date range
                SELECT DISTINCT e."Id", e."Code"
                FROM AllGroupEntities e
                ${entityIdsPredicates}
                ${sourceContainerIdentifierPredicates}
            ),
            ProviderResults AS (
                -- Get provider results
                SELECT
                    p."ProviderNPI" AS "npi",
                    MIN(e2."Description") AS "providerName",
                    MIN(e."Code") AS "submissionGroupId",
                    MIN(e."SourceContainerIdentifier") AS "submissionGroupContainerIdentifier",
                    MIN(e2."Description") AS "providerWithEntityName"
                FROM MECA."EntityProviders" ep
                JOIN CSRC."Providers" p ON ep."ProvidersId" = p."Id"
                JOIN AllGroupEntities e ON ep."EntitiesId" = e."Id"  
                JOIN AllGroupEntities e2 ON p."ProviderNPI" = e2."Code"
                JOIN EntityIds ei ON ep."EntitiesId" = ei."Id"
                GROUP BY p."ProviderNPI"
            )
            -- Final result set
            SELECT * FROM ProviderResults ORDER BY "npi"
        `

        // Prepare parameters
        let params: Bind[] = [
            ...entityIdsPredicateParameters,
            ...sourceContainerIdentifierPredicateParameters,
        ]

        // Execute the query
        const results = (await this.execute(sql, params)) as unknown as Provider[]
        return results
    }
}
