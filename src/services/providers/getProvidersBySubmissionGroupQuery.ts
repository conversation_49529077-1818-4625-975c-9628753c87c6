// External Libraries
import dayjs from 'dayjs'

// Absolute Imports
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { DateRange } from '@/lib/dateRange'
import TrendCSSHelper from '@/lib/trendCSSHelper'

// Relative Imports
import { getProvidersBySubmissionGroupAsync } from './getProvidersBySubmissionGroupAsync'
import { mapleMeasureQuery } from '@/services/maple/mapleMeasuresQuery'
import { manageUserRolesQuery } from '@/services/adm/users/manageUserRolesQuery'
import MeasureResultsService from '@/services/measureResults'
import MapleMeasuresService from '@/services/mapleMeasures'

// Type Imports
import type {
  CalculatedMeasure,
  ProviderResult,
} from '@/types/calculatedMeasure'
import type { DetailsList } from '@/types/detailsList'
import type { ECMeasureResultOptions } from '@/types/eCMeasureResultOptions'
import type { ECMeasureResultSummary } from '@/types/eCMeasureResultSummary'
import type { GetProvidersBySubmissionGroupQuery } from '@/types/getProvidersBySubmissionGroupQuery'
import type { ManageUserRole } from '@/types/manageUserRole'
import type { Provider } from '@/types/provider'
import { INotation } from '@/enums/iNotation'

type ProvidersBySubmissionGroup = ProviderResult & CalculatedMeasure

export const getProvidersBySubmissionGroupQuery = async (
  accessToken: string,
  measureResultsService: MeasureResultsService,
  request: GetProvidersBySubmissionGroupQuery
) => {
  const response: ProvidersBySubmissionGroup[] = []

  let userRoles: ManageUserRole[] = []
  let canAccessibleRole: string[] = []

  if (request.hasLimitedAccess) {
    userRoles = await manageUserRolesQuery(measureResultsService, {
      organizationId: request.organizationId,
      userId: request.userId,
      isPartner: request.isPartner,
      organizationType: EntityOrganizationTypeConstants.ProviderLevel,
    })

    if (userRoles.length === 0 || !userRoles.some((x) => x.canAccess)) {
      response.push({
        note: 'You do not have access to view practice and provider information. Please contact an administrator to get access',
      })

      return response
    }

    canAccessibleRole = userRoles
      .filter((role) => role.canAccess)
      .map((role) => role.entitiesId)
      .filter((x): x is string => x != null && x !== undefined)
  }

  const admMeasures = await measureResultsService.getAllMeasures(
    request.organizationId
  )

  // TODO: If numbers are incorrect, consider using this below
  // const periodList = DateRange.getColumnIntervalsByCategory(
  //   request.scorecardView,
  //   request.startDate,
  //   request.endDate
  // )

  const periodList = DateRange.getColumnIntervalsWithDateRange(
    request.scorecardView,
    request.startDate,
    request.endDate
  )

  let distinctNPIS: string[] = []
  let sourceContainerIdentifier = ''
  let providers: Provider[] = []

  if (request.submissionGroupId === '*') {
    sourceContainerIdentifier = request.organizationId

    let providerBySubmissionGrp: Provider[] = []
    providerBySubmissionGrp =
      await measureResultsService.getActiveProvidersBySubmissionGroup({
        organizationId: request.organizationId,
        submissionGroupId: '*',
        isPartner: false,
        startDate: request.startDate,
        endDate: request.endDate,
      })

    distinctNPIS = providerBySubmissionGrp
      .sort((a, b) => a.providerName.localeCompare(b.providerName)) // Order by ProviderName
      .map((x) => x.npi) // Map to NPI
      .filter((value, index, self) => self.indexOf(value) === index) // Distinct

    providers = providerBySubmissionGrp.map((x) => ({
      npi: x.npi,
      providerName: x.providerName,
      providerWithEntityName: x.providerWithEntityName,
    }))
  } else {
    const providerResponse = await getProvidersBySubmissionGroupAsync(
      measureResultsService,
      request.organizationId,
      request.isPartner,
      request.submissionGroupId
    )

    providers = providerResponse.map((x) => ({
      npi: x.npi,
      providerName: x.providerName,
      providerWithEntityName: x.providerWithEntityName,
    }))

    distinctNPIS = providerResponse
      .sort((a, b) => a.providerName.localeCompare(b.providerName)) // Order by ProviderName
      .map((x) => x.npi) // Map to NPI
      .filter((value, index, self) => self.indexOf(value) === index) // Distinct

    sourceContainerIdentifier =
      providerResponse[0] && providerResponse.length > 0
        ? providerResponse[0].submissionGroupContainerIdentifier!
        : ''

    if (request.hasLimitedAccess)
      distinctNPIS = distinctNPIS.filter((x) => canAccessibleRole.includes(x))
  }

  if (request.hasLimitedAccess) {
    distinctNPIS = distinctNPIS.filter((x) => canAccessibleRole.includes(x))
  }

  const options: ECMeasureResultOptions = {
    organizationId: request.organizationId,
    periodType: request.scorecardView,
    submissionGroupId: distinctNPIS,
    startDate: request.startDate,
    endDate: request.endDate,
    isPartner: request.isPartner,
    isSubmissionGroupLevel: false,
    sourceContainerIdentifier,
    measureIdentifier: request.measureIdentifier,
  }

  const aggregateResultsResponse =
    await measureResultsService.getECMeasureResults(options)

  const measureRatesResult =
    aggregateResultsResponse?.ecMeasureResultSummaryList ?? []

  if (measureRatesResult.length === 0) return response

  const mapleMeasuresService = new MapleMeasuresService(
    accessToken,
    measureResultsService
  )

  const measureDetails = await mapleMeasureQuery(
    mapleMeasuresService,
    request.organizationId,
    [request.measureIdentifier]
  )

  const admMeasureDetails = admMeasures.find(
    (x) =>
      x.MedisolvMeasureId?.toString().toLowerCase() ===
      request.measureIdentifier.toLowerCase()
  )

  const trendName =
    INotation[
      measureDetails[0]?.iNotationName! as unknown as keyof typeof INotation
    ]

  const emptyNpis = distinctNPIS.filter(
    (npi) => !measureRatesResult.some((result) => result.entityCode === npi)
  )

  if (emptyNpis.length != 0) {
    const newResults = emptyNpis.map((NPI) => {
      const provider = providers.find((x) => x.npi === NPI)

      const result: ECMeasureResultSummary = {
        entityCode: NPI,
        entityName: provider?.providerWithEntityName ?? '',
        startDate: dayjs(0), // Equivalent to DateTime.MinValue - but why?
        endDate: dayjs(0), // Equivalent to DateTime.MinValue - but why?
        denominator: null,
        performance: null,
        id: 0,
        cDMDataSourceName: '',
        measureGUID: '',
        measureSubId: '',
        sourceContainerIdentifier: '',
        period: '',
        hoverText: '',
      }

      return result
    })

    measureRatesResult.push(...newResults)
  }

  const groupedRecords = measureRatesResult.reduce(
    (acc, record) => {
      acc[record.entityCode] = acc[record.entityCode] ?? []
      acc[record.entityCode]?.push(record)
      return acc
    },
    {} as Record<string, ECMeasureResultSummary[]>
  )

  for (const [entityCode, records] of Object.entries(groupedRecords)) {
    const firstRecord = records[0]

    const myObj: Partial<ProvidersBySubmissionGroup> = {
      npi: firstRecord?.entityCode,
      providerName: firstRecord?.entityName.includes(' (')
        ? firstRecord?.entityName.split(' (')[0]
        : firstRecord?.entityName,
      providerWithEntityName: firstRecord?.entityName,
    }

    let isEmptyIndicator = true
    const detailList: DetailsList[] = []

    for (const [item, period] of periodList) {
      const span = item.replace('-', '_') as
        | `Q${number}_${number}`
        | `CY_${number}`
        | `${Capitalize<string>}_${number}`

      if (admMeasureDetails?.NullRate) {
        detailList.push({ performance: null, denominator: null })
        myObj[span] = '-'
        continue
      }

      const providerRates = records.find(
        (x) =>
          dayjs.utc(x.startDate).isSame(period.startDate, 'day') &&
          dayjs.utc(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
      )

      if (providerRates) {
        detailList.push({
          performance: providerRates.performance,
          denominator: admMeasureDetails?.NullDenominator
            ? null
            : providerRates.denominator,
        })

        myObj[span] =
          providerRates.performance === -1 || providerRates.performance === null
            ? '-'
            : providerRates.performance === 100
              ? providerRates.performance.toString()
              : providerRates.performance?.toFixed(2)

        if (providerRates.performance != null) isEmptyIndicator = false
      } else {
        myObj[span] = '-'
        detailList.push({ performance: null, denominator: null })
      }
    }

    const trendCSS = TrendCSSHelper.getTrendSlopeCss(
      detailList,
      trendName,
      request.scorecardView
    )

    Object.assign(myObj, {
      trendCss: trendCSS,
      measureDescription: measureDetails[0]?.measureDescription.trim(),
      measureIdentifier: admMeasureDetails?.MedisolvMeasureId,
      friendlyName: measureDetails[0]?.measureFriendlyName,
      subDomain: measureDetails[0]?.subDomainName,
      type: measureDetails[0]?.typeName,
      domain: measureDetails[0]?.domainName,
      cmsId: measureDetails[0]?.cMSId,
      subType: measureDetails[0]?.subTypeName,
      application: measureDetails[0]?.applicationName,
      programName: measureDetails[0]?.programName ?? '-',
      sourceContainerIdentifier: sourceContainerIdentifier,
      isEmptyIndicator: isEmptyIndicator,
    })

    response.push(myObj)
  }

  return response.sort(
    (a, b) => a.providerName?.localeCompare(b.providerName!)!
  )
}
