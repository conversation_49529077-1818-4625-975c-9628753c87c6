import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { EncounterSearchQuery } from '@/types/encounterSearchQuery'
import { EncounterSearchResult } from '@/types/encounterSearchResult'

dayjs.extend(utc)

/** @deprecated - Is this never used? */
export const getEncounterSearchQuery = async (
  query: EncounterSearchQuery
): Promise<EncounterSearchResult[]> => {
  try {
    const procedureName = '[MPRE].[EELoadEncounters]'

    // Ensure query.searchText is defined
    const searchText = query.searchText ? query.searchText.trim() : ''

    /**
     * Commenting out adm dependency for now as it is not used in the code.
     * Need to migrate to Snowflake in future
     */

    // Execute stored procedure using Prisma's raw SQL
    // const result = await prismaClient.$queryRaw<
    //   {
    //     PatientId: number
    //     EncounterId: number
    //     AccountNumber: string
    //     SourceEncounterID: string
    //     EncounterType: string
    //     Facility: string
    //     StartDate: Date
    //     EndDate: Date
    //     DischargeDate: Date
    //     SourcePatientID: string
    //     DisplayPatientID: string
    //     LastName: string
    //     FirstName: string
    //     Birthdate: Date
    //     Gender: string
    //   }[]
    // >(
    //   Prisma.sql`
    //     EXEC ${Prisma.raw(procedureName)} @stringInput = ${searchText}
    //   `
    // )

    const result: any = []

    return result
      .map((x: any) => ({
        encounterId: x.EncounterId.toString(),
        patientId: x.PatientId.toString(),
        accountNumber: x.AccountNumber,
        sourceEncounterId: x.SourceEncounterID,
        encounterType: x.EncounterType,
        facility: x.Facility,
        startDate: dayjs.utc(x.StartDate),
        sourcePatientID: x.SourcePatientID,
        displayPatientID: x.DisplayPatientID,
        lastName: x.LastName,
        firstName: x.FirstName,
        birthDate: dayjs.utc(x.Birthdate),
        gender: x.Gender,
        dischargeDate: dayjs.utc(x.DischargeDate),
      }))
      .filter((x: any) => x.accountNumber && x.accountNumber.trim() !== '')
  } catch (error) {
    return []
  }
}
