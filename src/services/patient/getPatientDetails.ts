import dayjs from 'dayjs'

import { config } from '@/config'
import { EntityDetailType } from '@/enums/entityDetailType'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { DateRange } from '@/lib/dateRange'
import type { PatientDetailsQuery } from '@/types/patientDetailsQuery'
import MeasureResultsService from '../measureResults'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import CitCOrganizationService from '../citCOrganizations'

export async function getPatientDetails(
  measureResultsService: MeasureResultsService,
  parameters: SimplifiedParameter[],
  accessToken: string,
  request: PatientDetailsQuery
) {
  const dateRange = DateRange.getDateFromDateRangeSpan(
    request.scorecardView,
    request.periodSpan.replace('-', '_')
  )

  if (
    request.primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures
  ) {
    const organizationService = new CitCOrganizationService(accessToken)
    const subOrganizations =
      await organizationService.getSubOrganizationsByOrganizationId(
        request.organizationId
      )
    const subOrganizationIds = subOrganizations.map((x) => x.subOrganizationId!)

    const groups = await organizationService.getGroupsByOrganizationIdAsync({
      organizationId: request.organizationId,
    })

    let measureResults = await measureResultsService.getEHPatientDetails(
      request.organizationId,
      request.isPartner,
      dateRange.startDate.utc(),
      dateRange.endDate.utc(),
      request.measureIdentifier,
      request.entityType === EntityDetailType.Measure
        ? ['*']
        : [request.entityId],
      subOrganizationIds,
      groups,
      parameters
    )

    const isRatioMeasure = config.customMeasures.ratioMeasures.some(
      (x) => x.toLowerCase() === request.measureIdentifier.toLowerCase()
    )

    const isVolumeMeasure = config.customMeasures.volumeMeasures.some(
      (x) => x.toLowerCase() === request.measureIdentifier.toLowerCase()
    )

    measureResults = isVolumeMeasure
      ? measureResults.filter((x) => x.result === 'Numerator')
      : measureResults

    if (isRatioMeasure) {
      const response = measureResults
        .sort((a, b) => {
          const dateA = a.dischargeDateTime
            ? new Date(a.dischargeDateTime)
            : new Date(0)
          const dateB = b.dischargeDateTime
            ? new Date(b.dischargeDateTime)
            : new Date(0)
          return dateB.getTime() - dateA.getTime()
        })
        .map((x) => ({
          patientName: request.isPatientLevelAccessAvailable
            ? x.patientName.replace(',', '')
            : `${x.patientName.substring(0, 1)}${x.patientName
                .substring(x.patientName.length)
                .padStart(x.patientName.length, '*')}`,
          age: x.age,
          gender: x.sex,
          result: x.result,
          dischargeDate: x.dischargeDateTime
            ? dayjs(x.dischargeDateTime).format('MM/DD/YYYY HH:mm:ss')
            : '-',
          measureObservation: `${x.numeratorValue ?? '-'}:${
            x.denominatorValue ?? '-'
          }`,
        }))
      return response
    } else {
      const response = measureResults
        .sort((a, b) => {
          const dateA = a.dischargeDateTime
            ? new Date(a.dischargeDateTime)
            : new Date(0)
          const dateB = b.dischargeDateTime
            ? new Date(b.dischargeDateTime)
            : new Date(0)
          return dateB.getTime() - dateA.getTime()
        })
        .map((x) => ({
          patientName: request.isPatientLevelAccessAvailable
            ? x.patientName.replace(',', '')
            : `${x.patientName.substring(0, 1)}${x.patientName
                .substring(x.patientName.length)
                .padStart(x.patientName.length, '*')}`,
          age: x.age,
          gender: x.sex,
          result: x.result,
          dischargeDate: x.dischargeDateTime
            ? dayjs(x.dischargeDateTime).format('MM/DD/YYYY HH:mm:ss')
            : '-',
        }))
      return response
    }
  } else {
    const entites = await measureResultsService.getEntities({
      isPartner: request.isPartner,
      organizationId: request.organizationId,
    })

    const activeMeasures =
      await measureResultsService.findAllDetailedActiveMeasures()
    const activeSubmissionGroupIds: number[] = activeMeasures
      .filter(
        (measure: any) =>
          dayjs(measure.ProcessingStartDate).year() >=
            dateRange.startDate.utc().year() &&
          dayjs(measure.ProcessingStartDate).year() <=
            dateRange.endDate.utc().year()
      )
      .map((measure: any) => measure.EntitiesId)

    const measureResults = await measureResultsService.getECPatientDetails(
      request.organizationId,
      request.isPartner,
      dateRange.startDate.utc(),
      dateRange.endDate.utc(),
      request.measureIdentifier,
      entites,
      request.entityType === EntityDetailType.Measure
        ? ['*']
        : [request.entityId],
      activeSubmissionGroupIds,
      parameters
    )

    const response = measureResults
      .sort((a, b) => {
        const dateA = a.dischargeDateTime
          ? new Date(a.dischargeDateTime)
          : new Date(0)
        const dateB = b.dischargeDateTime
          ? new Date(b.dischargeDateTime)
          : new Date(0)
        return dateB.getTime() - dateA.getTime()
      })
      .map((x) => ({
        patientName: request.isPatientLevelAccessAvailable
          ? x.patientName.replace(',', '')
          : `${x.patientName.substring(0, 1)}${x.patientName
              .substring(x.patientName.length)
              .padStart(x.patientName.length, '*')}`,
        age: x.age,
        gender: x.sex,
        result: x.result,
        dischargeDate: x.dischargeDateTime
          ? dayjs(x.dischargeDateTime).format('MM/DD/YYYY HH:mm:ss')
          : '-',
      }))
    return response
  }
}
