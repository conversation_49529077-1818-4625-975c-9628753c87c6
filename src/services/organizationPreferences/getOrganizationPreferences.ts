import { OrganizationPreference } from '@/types/organizationPreference'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { MigrationConfig } from '@/types/migrationConfig'
import { migrateData } from '../migration/migrateData'
import { HUBPrismaClient } from '@/server/db'
import { upsertOrganizationPreference } from './upsertOrganizationPreference'
import { OrganizationPreferenceKey } from '@/enums/organizationPreferenceKey'

export const getOrganizationPreferences = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  organizationId: string,
  fieldKey: OrganizationPreferenceKey,
  migrationConfig?: MigrationConfig
) => {
  // TODO: Enhancement* Add try/catch w/ app insights
  const existingPreferences =
    await tableStorageWrapper.queryEntities<OrganizationPreference>(
      tableStorageWrapper.generateODataQuery({
        PartitionKey: organizationId,
        key: fieldKey,
      })
    )

  if (migrationConfig && existingPreferences.length === 0) {
    // TODO: Enhancement* Add try/catch w/ app insights
    await migrateData({
      storage: tableStorageWrapper,
      migrationConfig,
      prismaClientType: 'hubClient',
      prismaQuery: (prismaClient: HUBPrismaClient) =>
        prismaClient.organizationPreferences.findMany(),
      upsertFunction: async (storage, data) =>
        // TODO: Enhancement* Add try/catch w/ app insights
        await Promise.all(
          data.map((preference) =>
            upsertOrganizationPreference(storage, {
              key: preference.Key,
              value: preference.Value,
              partitionKey: organizationId,
              rowKey: preference.Id.toString(),
            })
          )
        ),
    })
  }

  return tableStorageWrapper.queryEntities<OrganizationPreference>(
    tableStorageWrapper.generateODataQuery({
      PartitionKey: organizationId,
      key: fieldKey,
    })
  )
}
