import { OrganizationPreference } from '@/types/organizationPreference'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { migrateData } from '../migration/migrateData'
import { MigrationConfig } from '@/types/migrationConfig'
import { HUBPrismaClient } from '@/server/db'
import { upsertOrganizationPreference } from './upsertOrganizationPreference'

export const getAllOrganizationPreferences = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  organizationId: string,
  migrationConfig?: MigrationConfig
) => {
  // TODO: Enhancement* Add try/catch w/ app insights
  const existingOrganizationPreferences =
    await tableStorageWrapper.queryEntities<OrganizationPreference>(
      tableStorageWrapper.generateODataQuery({
        PartitionKey: organizationId,
      })
    )

  if (migrationConfig && existingOrganizationPreferences.length === 0) {
    // TODO: Enhancement* Add try/catch w/ app insights
    await migrateData({
      storage: tableStorageWrapper,
      migrationConfig,
      prismaClientType: 'hubClient',
      prismaQuery: (prismaClient: HUBPrismaClient) =>
        prismaClient.organizationPreferences.findMany(),
      upsertFunction: async (storage, data) => {
        // TODO: Enhancement* Add try/catch w/ app insights
        // Use Promise.all to ensure all reports are migrated
        await Promise.all(
          data.map((preference) =>
            upsertOrganizationPreference(storage, {
              partitionKey: organizationId,
              rowKey: preference.Id.toString(),
              key: preference.Key,
              value: preference.Value,
            })
          )
        )
      },
    })
  }

  return tableStorageWrapper.queryEntities<OrganizationPreference>(
    tableStorageWrapper.generateODataQuery({
      PartitionKey: organizationId,
    })
  )
}
