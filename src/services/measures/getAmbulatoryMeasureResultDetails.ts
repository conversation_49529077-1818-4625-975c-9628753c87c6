import dayjs from 'dayjs'

import { EntityDetailType } from '@/enums/entityDetailType'
import { DateRange } from '@/lib/dateRange'

import CitCPartnersService from '../citc/citCPartners'

import type { AggregatedMeasureDetails } from '@/types/aggregatedMeasureDetails'
import type { ECMeasureResultOptions } from '@/types/eCMeasureResultOptions'
import type { MeasureResultDetailsQuery } from '@/types/measureResultDetailsQuery'
import MeasureResultsService from "@/services/measureResults";

export async function getAmbulatoryMeasureResultDetails(
  uid: string,
  measureResultsService: MeasureResultsService,
  accessToken: string,
  request: MeasureResultDetailsQuery
): Promise<AggregatedMeasureDetails[]> {
  console.log(
    'ECMeasureResultDetailsQueryHandler::Handle:: Started',
    new Date().toString()
  )

  const dateRangeSpan = DateRange.getColumnIntervalsByCategory(
    request.scorecardView,
    request.startDate,
    request.endDate
  )

  const measure = await measureResultsService.getMeasureByIdFromDb(
    request.measureIdentifier,
    request.organizationId
  )

  if (!measure) {
    console.log(
      'ECMeasureResultDetailsQueryHandler::Handle:: No measure found',
      request.measureIdentifier,
      request.organizationId
    )
    return []
  }

  //this was in the C# code but never referenced. Its ported just in case
  // const subOrganizations = await new CitCOrganizationService(
  //   this.accessToken
  // ).getSubOrganizationsByOrganizationId(request.organizationId)

  let options: ECMeasureResultOptions = {
    organizationId: request.organizationId,
    periodType: request.scorecardView.toString().substring(0, 1),
    submissionGroupId:
      request.entityDetailType === EntityDetailType.Measure
        ? ['*']
        : [request.entityId],
    startDate: request.startDate,
    endDate: request.endDate,
    isPartner: request.isPartner,
    isSubmissionGroupLevel:
      request.entityDetailType === EntityDetailType.SubmissionGroup ||
      request.entityDetailType === EntityDetailType.Measure,
    sourceContainerIdentifier: request.organizationId,
    measureIdentifier: request.measureIdentifier,
  }

  if (request.isPartner) {
    // Assuming 'partnerList' is fetched similarly to 'subOrganizations'
    const citCPartnersService = new CitCPartnersService(accessToken)
    const partnerList = await citCPartnersService.getPartnersByUserAsync(uid)
    const partner = partnerList.find((x) => x.id === request.organizationId)

    if (
      partner &&
      partner.organizations?.some((x) => x.organizationId === request.entityId)
    ) {
      const orgId = partner.organizations.find(
        (x) => x.organizationId === request.entityId
      )?.organizationId

      if (orgId) {
        options = {
          organizationId: orgId,
          periodType: request.scorecardView.toString().substring(0, 1),
          submissionGroupId: ['*'],
          startDate: dayjs(request.startDate),
          endDate: dayjs(request.endDate),
          isPartner: false,
          isSubmissionGroupLevel:
            request.entityDetailType === EntityDetailType.SubmissionGroup ||
            request.entityDetailType === EntityDetailType.Measure,
          sourceContainerIdentifier: orgId,
          measureIdentifier: request.measureIdentifier,
        }
      }
    }
  }

  const aggregateResultsResponse = await measureResultsService.getECMeasureResults(
    options
  )

  const result = (
    aggregateResultsResponse?.ecMeasureResultSummaryList ?? []
  ).filter((x) => x.measureGUID === request.measureIdentifier)

  console.log(
    'ECMeasureResultDetailsQueryHandler::Handle::  Call to ADM API Measure results-',
    result?.length,
    'records'
  )

  const measureDetailResult: AggregatedMeasureDetails[] = []

  for (const item of dateRangeSpan) {
    const span = item.replace('-', '_')
    const period = DateRange.getDateFromDateRangeSpan(
      request.scorecardView,
      span
    )

    const record = result?.find(
      (x) =>
        dayjs(x.startDate).isSame(period.startDate, 'day') &&
        dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
    )

    if (record) {
      measureDetailResult.push({
        date: item,
        fullDate: period.startDate.format('MM/DD/YYYY'),
        rate: measure.NullRate
          ? '-'
          : record.performance !== null
            ? record.performance === 100
              ? record.performance.toString()
              : (record.performance?.toFixed(2) ?? '-')
            : '-',
        goal: '-',
        benchmark: '-',
        population: measure.NullIPP
          ? '-'
          : (record.ipp?.toString() ?? '-'),
        inDenominatorOnly: measure.NullDenomOnly
          ? '-'
          : (record.inDenominatorOnly?.toString() ?? '-'),
        denominatorExclusion: measure.NullDenExcl
          ? '-'
          : (record.denominatorExclusion?.toString() ?? '-'),
        numerator: measure.NullNumerator
          ? '-'
          : (record.numerator?.toString() ?? '-'),
        denominator: measure.NullDenominator
          ? '-'
          : (record.denominator?.toString() ?? '-'),
        exception: measure.NullException
          ? '-'
          : (record.denominatorException?.toString() ?? '-'),
        endDate: period.endDate?.format('MM/DD/YYYY'),
        noDrill: measure.NoDrill ?? false,
        isPatientLevelAccessAvailable: false, // Replace with actual value
      })
    } else {
      measureDetailResult.push({
        date: item,
        numerator: '-',
        fullDate: period.startDate.format('MM/DD/YYYY'),
        denominator: '-',
        rate: '-',
        goal: '-',
        benchmark: '-',
        denominatorExclusion: '-',
        exception: '-',
        inDenominatorOnly: '-',
        population: '-',
        endDate: period.endDate?.format('MM/DD/YYYY'),
        noDrill: measure.NoDrill ?? false,
        isPatientLevelAccessAvailable: false, // Replace with actual value
      })
    }
  }

  console.log(
    'ECMeasureResultDetailsQueryHandler::Handle:: Completed',
    new Date().toString()
  )
  return measureDetailResult
}
