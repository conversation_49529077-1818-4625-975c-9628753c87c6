import { manageUserRolesQuery } from '@/services/adm/users/manageUserRolesQuery'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import type { ADMPrismaClient } from '@/server/db'
import type { ECMeasureResultBySubmissionGroupQuery } from '@/types/eCMeasureResultBySubmissionGroupQuery'
import { DateRange } from '@/lib/dateRange'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import type { DetailsList } from '@/types/detailsList'
import type { ECMeasureResultOptions } from '@/types/eCMeasureResultOptions'
import type { ECMeasureResultSummary } from '@/types/eCMeasureResultSummary'
import dayjs from 'dayjs'
import MeasureResultsService from '../measureResults'
import { mapleMeasureQuery } from '../maple/mapleMeasuresQuery'
import MapleMeasuresService from '../mapleMeasures'
import type { ECMeasureBySubmissionGroup } from '@/types/calculatedMeasure'
import TrendCSSHelper from '@/lib/trendCSSHelper'
import { getSubmissionGroupsByOrganizationQuery } from '@/services/submissionGroups/getSubmissionGroupsByOrganizationQuery'
import { EntityLevelConstants } from '@/types/expansionConfiguration'
import { Partner } from '@/types/partner'
import { INotation } from '@/enums/iNotation'
import { ManageUserRole } from '@/types/manageUserRole'
import { ExtensionLevels } from '@/enums/extensionLevels'

export const getECMeasureResultBySubmissionGroupQuery = async (
  accessToken: string,
  measureResultsService: MeasureResultsService,
  request: ECMeasureResultBySubmissionGroupQuery
) => {
  const modelData: ECMeasureBySubmissionGroup[] = []
  const type2Results: ECMeasureBySubmissionGroup[] = []

  let userRoles: ManageUserRole[] = []
  let canAccessibleRole: string[] = []

  if (request.hasLimitedAccess) {
    userRoles = await manageUserRolesQuery(measureResultsService, {
      organizationId: request.organizationId,
      userId: request.userId,
      isPartner: request.isPartner,
      organizationType: EntityOrganizationTypeConstants.SubmissionGroupLevel,
    })

    if (userRoles.length == 0 || !userRoles.some((x) => x.canAccess)) {
      modelData.push({
        note: 'You do not have access to view practice and provider information. Please contact an administrator to get access',
      })

      return modelData
    }

    canAccessibleRole = userRoles
      .filter((x) => x.canAccess)
      .map((x) => x.entitiesId)
      .map((x) => x.toString()) // NOTE: I don't think this is being used as entity code seems to come from the entity itself https://gitkraken.dev/link/dnNjb2RlOi8vZWFtb2Rpby5naXRsZW5zL2xpbmsvci83N2M2N2UwZDliMWRkMmMyODkyYWRhMzY0ZTg4ZDlhMTY2ZDU4MzJjL2Yvc3JjL3NlcnZpY2VzL2FkbS91c2Vycy9tYW5hZ2VVc2VyUm9sZXNRdWVyeS50cz91cmw9bWVkaXNvbHYlNDB2cy1zc2gudmlzdWFsc3R1ZGlvLmNvbSUzQXYzJTJGbWVkaXNvbHYlMkZwbGF0Zm9ybS1uZXh0Z2VuJTJGcGxhdGZvcm0tbmV4dGdlbiZsaW5lcz04NQ%3D%3D?origin=gitlens
      .filter((x): x is string => x != null && x !== undefined)
  }

  const periodList = DateRange.getColumnIntervalsByCategory(
    request.scorecardView,
    request.startDate,
    request.endDate
  )

  const admMeasures = await measureResultsService.getAllMeasures(
    request.organizationId
  )

  const mapleMeasuresService = new MapleMeasuresService(
    accessToken,
    measureResultsService
  )

  const measureDetails = await mapleMeasureQuery(
    mapleMeasuresService,
    request.organizationId,
    [request.measureIdentifier]
  )

  const admMeasureDetails = admMeasures.find(
    (x) => x.MedisolvMeasureId == request.measureIdentifier
  )

  const trendName =
    INotation[
      measureDetails[0]?.iNotationName! as unknown as keyof typeof INotation
    ]
  const isIAPIMeasure =
    admMeasureDetails?.SmallestInterval == 'Y' &&
    !admMeasureDetails?.DenominatorQualifyingType

  const type2EntityDisplayLevel = request.expansionConfiguration.find(
    (x) => x.level === ExtensionLevels.level2
  )

  if (
    type2EntityDisplayLevel?.selectedLevel ===
      EntityLevelConstants.SecondLevel ||
    request.isPartner
  ) {
    const entities = await measureResultsService.getEntities({
      organizationId: request.organizationId,
      isPartner: request.isPartner,
    })

    let partnerOfOrganization: Partner | undefined

    if (!request.isPartner) {
      partnerOfOrganization = request.partners.find((x) =>
        x.organizations?.some(
          (org) => org.organizationId === request.organizationId
        )
      )
    }
    const orgnizationIds = !request.isPartner
      ? [request.organizationId]
      : request.organizationList
          .map((c) => c.organizationId)
          .filter((x) => x != null && x != undefined)

    orgnizationIds.push('ALLORGANIZATION')

    const ecoptions: ECMeasureResultOptions = {
      organizationId: request.isPartner
        ? request.organizationId
        : partnerOfOrganization?.id!,
      periodType: request.scorecardView,
      submissionGroupId: orgnizationIds,
      startDate: request.startDate,
      endDate: request.endDate,
      isPartner: true,
      isSubmissionGroupLevel: true,
      measureIdentifier: request.measureIdentifier,
    }

    const response = await measureResultsService.getECMeasureResults(ecoptions)
    const measureResultsForAllOrgs = response?.ecMeasureResultSummaryList || []

    const organizationList = request.isPartner
      ? request.organizationList
      : partnerOfOrganization?.organizations?.filter(
          (x) => x.organizationId === request.organizationId
        )!

    const emptyResultOrgs = organizationList
      .map((x) => x.organizationId)
      .filter((x) => x != null && x != undefined)
      .filter(
        (orgId) =>
          !measureResultsForAllOrgs
            .map((x) => x.sourceContainerIdentifier.replace('_EC', ''))
            .includes(orgId)
      )

    if (emptyResultOrgs.length > 0) {
      emptyResultOrgs.forEach((orgId) => {
        measureResultsForAllOrgs.push({
          startDate: dayjs(0),
          endDate: dayjs(0),
          denominator: null,
          performance: null,
          sourceContainerIdentifier: orgId,
        } as ECMeasureResultSummary)
      })
    }

    const groupedMeasureResultsForAllOrgs = measureResultsForAllOrgs.reduce(
      (groups, item) => {
        if (!groups[item.sourceContainerIdentifier]) {
          groups[item.sourceContainerIdentifier] = []
        }
        groups[item.sourceContainerIdentifier]?.push(item)
        return groups
      },
      {} as Record<string, ECMeasureResultSummary[]>
    )

    for (const [key, measureRates] of Object.entries(
      groupedMeasureResultsForAllOrgs
    )) {
      let combinedGroupName =
        entities.find(
          (x) =>
            x.entityTypeName === EntityTypeConstants.RenderingProvider &&
            x.organizationTypeCode ===
              EntityOrganizationTypeConstants.CombinedGroupLevel &&
            x.sourceContainerIdentifier.startsWith(key)
        )?.entityName ??
        entities.find(
          (x) =>
            x.entityTypeName === EntityTypeConstants.RenderingProvider &&
            x.organizationTypeCode ===
              EntityOrganizationTypeConstants.SubmissionGroupLevel &&
            x.sourceContainerIdentifier.startsWith(key)
        )?.entityName

      const organization = request.organizationList.find((x) =>
        key.startsWith(x.organizationId!)
      )

      if (!combinedGroupName) {
        combinedGroupName = organization?.organizationName ?? ''
      }

      let isEmptyIndicator = true

      const myObj: Partial<ECMeasureBySubmissionGroup> = {}

      myObj.order = 1
      myObj.entityCode = organization?.organizationId ?? request.organizationId
      myObj.entity = combinedGroupName
      myObj.entityNameForDetails = organization?.organizationName?.replace(
        '&',
        '[AND]'
      )

      const detailList: DetailsList[] = []

      for (const item of periodList) {
        const span = item.replace('-', '_') as
          | `Q${number}_${number}`
          | `CY_${number}`
          | `${Capitalize<string>}_${number}`

        const period = DateRange.getDateFromDateRangeSpan(
          request.scorecardView,
          span
        )

        if (
          !isIAPIMeasure &&
          (!admMeasureDetails || admMeasureDetails.NullRate)
        ) {
          myObj[span] = '-'
          detailList.push({ performance: null, denominator: null })
          continue
        }

        const measureResultItem = measureRates.find(
          (x) =>
            dayjs.utc(x.startDate).isSame(period.startDate, 'day') &&
            dayjs.utc(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
        )

        if (measureResultItem) {
          if (isIAPIMeasure) {
            detailList.push({ performance: null, denominator: null })
            myObj[span] = !measureResultItem.hoverText
              ? '-'
              : (JSON.parse(measureResultItem.hoverText).Status ??
                JSON.parse(measureResultItem.hoverText).Result)
            continue
          }

          detailList.push({
            performance: measureResultItem.performance,
            denominator: admMeasureDetails?.NullDenominator
              ? null
              : measureResultItem.denominator,
          })

          myObj[span] =
            measureResultItem.performance === -1 ||
            measureResultItem.performance === null
              ? '-'
              : measureResultItem.performance === 100
                ? measureResultItem.performance.toString()
                : measureResultItem.performance?.toFixed(2)

          if (measureResultItem.performance !== null) {
            isEmptyIndicator = false
          }
        } else {
          myObj[span] = '-'
          detailList.push({ performance: null, denominator: null })
        }
      }

      const trendCSS = TrendCSSHelper.getTrendSlopeCss(
        detailList,
        trendName,
        request.scorecardView
      )

      myObj.trendCss = trendCSS
      myObj.measureDescription = measureDetails[0]?.measureDescription.trim()
      myObj.friendlyName = measureDetails[0]?.measureFriendlyName
      myObj.subDomain = measureDetails[0]?.subDomainName
      myObj.type = measureDetails[0]?.typeName
      myObj.domain = measureDetails[0]?.domainName
      myObj.cmsId = measureDetails[0]?.cMSId ?? '-'
      myObj.subType = measureDetails[0]?.subTypeName
      myObj.application = measureDetails[0]?.applicationName
      myObj.programName =
        measureDetails[0]?.programName == null
          ? '-'
          : measureDetails[0]?.programName
      myObj.measureIdentifier = admMeasureDetails?.MedisolvMeasureId
      myObj.isEmptyIndicator = isEmptyIndicator

      // const isExpandable =
      //     facilities.filter(x => x.entityCode === key).length > 1;
      // myObj["isExpandable"] = isExpandable;

      type2Results.push(myObj)
    }
  }

  let subOrgIds: string[] = []
  if (request.isPartner) {
    subOrgIds = request.entites
      .map((x) => x.split('~')[0])
      .filter((x) => x !== undefined && x !== null)
  } else {
    subOrgIds.push(...request.entites)
  }

  let submissionGroups = await getSubmissionGroupsByOrganizationQuery(
    accessToken,
    measureResultsService,
    request.userId,
    {
      startDate: request.startDate,
      endDate: request.endDate,
      organizationId: request.organizationId,
      isPartner: request.isPartner,
      measureType: request.measureType,
    }
  )

  if (request.measureType == 'Q') {
    submissionGroups =
      await measureResultsService.getSubmissionGroupsByOrganizationByMeasureType(
        {
          organizationId: request.organizationId,
          isPartner: request.isPartner,
          measureType: request.measureType,
        }
      )
  }

  if (request.entites.length == 0 || request.entites.includes('*')) {
    subOrgIds = submissionGroups.map((x) => x.submissionGroupId)
  } else {
    submissionGroups = submissionGroups.filter((x) =>
      request.entites.includes(x.submissionGroupId)
    )
    subOrgIds = submissionGroups.map((x) => x.submissionGroupId)
  }

  if (request.hasLimitedAccess) {
    subOrgIds = subOrgIds.filter((x) => canAccessibleRole.includes(x))
    submissionGroups = submissionGroups.filter((x) =>
      canAccessibleRole.includes(x.submissionGroupId)
    )
  }
  const options: ECMeasureResultOptions = {
    organizationId: request.organizationId,
    periodType: request.scorecardView,
    submissionGroupId: subOrgIds,
    startDate: request.startDate,
    endDate: request.endDate, //Adm end date is start date of next month
    isPartner: request.isPartner,
    isSubmissionGroupLevel: true,
    measureIdentifier: request.measureIdentifier,
  }

  const aggregateResultsResponse =
    await measureResultsService.getECMeasureResults(options)
  const measureResults =
    aggregateResultsResponse?.ecMeasureResultSummaryList ?? []

  if (measureResults.length === 0) return modelData

  const emptySubmissionGroups = subOrgIds.filter(
    (id) => !measureResults.some((result) => result.entityCode === id)
  )

  if (emptySubmissionGroups.length !== 0) {
    measureResults.push(
      ...emptySubmissionGroups.map((submissionGroupId) => {
        const submissionGroup = submissionGroups.find(
          (x) => x.submissionGroupId === submissionGroupId
        )

        return {
          entityCode: submissionGroupId,
          entityName: submissionGroup?.submissionGroupName ?? '',
          startDate: dayjs(0),
          endDate: dayjs(0),
          denominator: null,
          performance: null,
        } as ECMeasureResultSummary
      })
    )
  }

  for (const [entityCode, measureResultGroup] of Object.entries(
    measureResults.reduce<Record<string, ECMeasureResultSummary[]>>(
      (groups, item) => {
        if (!groups[item.entityCode]) {
          groups[item.entityCode] = []
        }
        groups[item.entityCode]?.push(item)
        return groups
      },
      {}
    )
  )) {
    const submissionGroup = submissionGroups.find(
      (x) => x.submissionGroupId === entityCode
    )

    if (!submissionGroup) continue

    let isEmptyIndicator = true
    const myObj: Partial<ECMeasureBySubmissionGroup> = {}

    myObj.order = 2
    myObj.entityCode = submissionGroup.submissionGroupId
    myObj.entity = submissionGroup.submissionGroupName
    myObj.entityNameForDetails = submissionGroup.submissionGroupName.replace(
      '&',
      '[AND]'
    )

    const detailList: DetailsList[] = []

    for (const item of periodList) {
      const span = item.replace('-', '_') as
        | `Q${number}_${number}`
        | `CY_${number}`
        | `${Capitalize<string>}_${number}`

      const period = DateRange.getDateFromDateRangeSpan(
        request.scorecardView,
        span
      )

      if (!isIAPIMeasure && admMeasureDetails?.NullRate) {
        myObj[span] = '-'
        detailList.push({ performance: null, denominator: null })
        continue
      }

      const measureResultItem = measureResultGroup.find(
        (x) =>
          dayjs.utc(x.startDate).isSame(period.startDate, 'day') &&
          dayjs.utc(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
      )

      if (measureResultItem) {
        if (isIAPIMeasure) {
          detailList.push({ performance: null, denominator: null })
          myObj[span] = !measureResultItem.hoverText
            ? '-'
            : (JSON.parse(measureResultItem.hoverText).Status ??
              JSON.parse(measureResultItem.hoverText).Result)
          continue
        }

        detailList.push({
          performance: measureResultItem.performance,
          denominator: admMeasureDetails?.NullDenominator
            ? null
            : measureResultItem.denominator,
        })

        myObj[span] =
          measureResultItem.performance === -1 ||
          measureResultItem.performance === null
            ? '-'
            : measureResultItem.performance === 100
              ? measureResultItem.performance.toString()
              : measureResultItem.performance?.toFixed(2)

        if (measureResultItem.performance !== null) isEmptyIndicator = false
      } else {
        myObj[span] = '-'
        detailList.push({ performance: null, denominator: null })
      }
    }

    const trendCSS = TrendCSSHelper.getTrendSlopeCss(
      detailList,
      trendName,
      request.scorecardView
    )

    myObj.trendCss = trendCSS
    myObj.measureDescription = measureDetails[0]?.measureDescription.trim()
    myObj.friendlyName = measureDetails[0]?.measureFriendlyName
    myObj.subDomain = measureDetails[0]?.subDomainName
    myObj.type = measureDetails[0]?.typeName
    myObj.domain = measureDetails[0]?.domainName
    myObj.cmsId = measureDetails[0]?.cMSId ?? '-'
    myObj.subType = measureDetails[0]?.subTypeName
    myObj.application = measureDetails[0]?.applicationName
    myObj.programName = measureDetails[0]?.programName ?? '-'
    myObj.measureIdentifier = admMeasureDetails?.MedisolvMeasureId
    myObj.isEmptyIndicator = isEmptyIndicator

    modelData.push(myObj)
  }

  type2Results.sort((a, b) => a.entity!.localeCompare(b.entity!))
  modelData.sort((a, b) => a.entity!.localeCompare(b.entity!))
  modelData.unshift(...type2Results)

  return modelData
}
