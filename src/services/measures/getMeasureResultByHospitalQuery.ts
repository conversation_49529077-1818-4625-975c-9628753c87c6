// External Libraries
import dayjs from 'dayjs'

// Absolute Imports
import { config } from '@/config'
import { DateRange } from '@/lib/dateRange'
import TrendCSSHelper from '@/lib/trendCSSHelper'
import type { DetailsList } from '@/types/detailsList'
import type { Facility } from '@/types/facility'
import type { MeasureResultByHospital } from '@/types/calculatedMeasure'
import type { MeasureResultByHospitalQuery } from '@/types/measureResultByHospitalQuery'
import { MeasureResultSummary } from '@/types/measureResultSummary'
import type { MeasureResultOptions } from '@/types/measureResultOptions'

// Relative Imports
import { mapleMeasureQuery } from '../maple/mapleMeasuresQuery'
import MeasureResultsService from '../measureResults'
import MapleMeasuresService from '../mapleMeasures'
import { EntityLevelConstants } from '@/types/expansionConfiguration'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { INotation } from '@/enums/iNotation'
import { ExtensionLevels } from '@/enums/extensionLevels'

export const getMeasureResultByHospitalQuery = async (
  accessToken: string,
  measureResultsService: MeasureResultsService,
  request: MeasureResultByHospitalQuery
) => {
  const modelData: MeasureResultByHospital[] = []
  const type2Results: MeasureResultByHospital[] = []

  const admMeasures = await measureResultsService.getAllMeasures(
    request.organizationId
  )

  const dateRangeSpan = DateRange.getColumnIntervalsByCategory(
    request.scorecardView,
    request.startDate,
    request.endDate
  )

  const mapleMeasuresService = new MapleMeasuresService(
    accessToken,
    measureResultsService
  )

  const measureDetails = await mapleMeasureQuery(
    mapleMeasuresService,
    request.organizationId,
    [request.measureIdentifier]
  )

  const isRatioMeasure = config.customMeasures.ratioMeasures.some(
    (x) => x.toLowerCase() == request.measureIdentifier.toLowerCase()
  )
  const admMeasureDetails = admMeasures.find(
    (x) => x.MedisolvMeasureId == request.measureIdentifier
  )

  const type2EntityDisplayLevel = request.expansionConfiguration.find(
    (x) => x.level === ExtensionLevels.level2
  )

  if (
    type2EntityDisplayLevel?.selectedLevel! ===
      EntityLevelConstants.SecondLevel &&
    !request.isPartner
  ) {
    const entities = await measureResultsService.getEntities({
      organizationId: request.organizationId,
      isPartner: request.isPartner,
    })

    let partnerOfOrganization = request.partners.find((x) =>
      x.organizations?.some(
        (org) => org.organizationId === request.organizationId
      )
    )

    const orgnizationIds = [request.organizationId, 'ALLORGANIZATION']

    const options: MeasureResultOptions = {
      organizationId: partnerOfOrganization?.id!,
      periodType: request.scorecardView,
      subOrganizationId: orgnizationIds,
      startDate: request.startDate,
      endDate: request.endDate,
      isPartner: true,
      measureIdentifier: admMeasureDetails?.MedisolvMeasureId,
    }

    const response = await measureResultsService.getMeasureResults(options)
    const measureResultsForAllOrgs = response?.measureResultSummaryList || []

    const emptyResultOrgs = partnerOfOrganization?.organizations
      ?.filter((x) => x.organizationId === request.organizationId)
      .map((x) => x.organizationId)
      .filter((x) => x != null && x != undefined)
      .filter(
        (orgId) =>
          !measureResultsForAllOrgs
            .map((x) => x.sourceContainerIdentifier.replace('_EH', ''))
            .includes(orgId)
      )

    if (emptyResultOrgs && emptyResultOrgs.length > 0) {
      emptyResultOrgs.forEach((orgId) => {
        measureResultsForAllOrgs.push({
          startDate: dayjs(0),
          endDate: dayjs(0),
          denominator: null,
          performance: null,
          sourceContainerIdentifier: orgId,
        } as unknown as MeasureResultSummary)
      })
    }

    const groupedMeasureResultsForAllOrgs = measureResultsForAllOrgs.reduce(
      (groups, item) => {
        if (!groups[item.sourceContainerIdentifier]) {
          groups[item.sourceContainerIdentifier] = []
        }
        groups[item.sourceContainerIdentifier]?.push(item)
        return groups
      },
      {} as Record<string, MeasureResultSummary[]>
    )

    for (const [key, measureRates] of Object.entries(
      groupedMeasureResultsForAllOrgs
    )) {
      let isEmptyIndicator = true
      const myObj: Partial<MeasureResultByHospital> = {}

      let combinedGroupName =
        entities.find(
          (x) =>
            x.entityTypeName === EntityTypeConstants.Facility &&
            x.organizationTypeCode ===
              EntityOrganizationTypeConstants.CombinedGroupLevel &&
            x.sourceContainerIdentifier.startsWith(key)
        )?.entityName ??
        entities.find(
          (x) =>
            x.entityTypeName === EntityTypeConstants.Facility &&
            x.organizationTypeCode ===
              EntityOrganizationTypeConstants.HospitalLevel &&
            x.sourceContainerIdentifier.startsWith(key)
        )?.entityName

      const organization = request.organizationList.find((x) =>
        key.startsWith(x.organizationId!)
      )

      if (!combinedGroupName) {
        combinedGroupName = organization?.organizationName ?? ''
      }

      myObj.hospital = combinedGroupName
      myObj.hospitalId = organization?.organizationId ?? request.organizationId
      myObj.entityNameForDetails = measureRates[0]?.entityName?.replace(
        '&',
        '[AND]'
      )
      myObj.ccnNumber = '-'

      const detailList: DetailsList[] = []
      for (const item of dateRangeSpan) {
        const span = item.replace('-', '_') as
          | `Q${number}_${number}`
          | `CY_${number}`
          | `${Capitalize<string>}_${number}`
        const period = DateRange.getDateFromDateRangeSpan(
          request.scorecardView,
          span
        )

        if (admMeasureDetails?.NullRate) {
          myObj[span] = '-'
          detailList.push({ performance: null, denominator: null })
          continue
        }

        const rate = measureRates.find(
          (x) =>
            dayjs(x.startDate).isSame(period.startDate, 'day') &&
            dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
        )

        if (rate) {
          detailList.push({
            performance: rate.performance,
            denominator: admMeasureDetails?.NullDenominator
              ? null
              : rate.denominator,
          })

          if (isRatioMeasure) {
            myObj[span] =
              rate.performance === null || rate.performance === -1
                ? '-'
                : rate.performance === 100
                  ? `${rate.numeratorValue}/${rate.denominatorValue}<br>${rate.performance}`
                  : `${rate.numeratorValue}/${rate.denominatorValue}<br>${rate.performance.toFixed(
                      2
                    )}`
          } else {
            myObj[span] =
              rate.performance === null || rate.performance === -1
                ? '-'
                : rate.performance === 100
                  ? rate.performance.toString()
                  : rate.performance.toFixed(2)
          }

          if (rate.performance !== null) isEmptyIndicator = false
        } else {
          myObj[span] = '-'
          detailList.push({ performance: null, denominator: null })
        }
      }

      const trendName =
        INotation[
          measureDetails[0]?.iNotationName! as unknown as keyof typeof INotation
        ]

      const trendCSS = TrendCSSHelper.getTrendSlopeCss(
        detailList,
        trendName,
        request.scorecardView
      )

      myObj.trendCss = trendCSS

      myObj.measureDescription = measureDetails[0]?.measureDescription.trim()!
      myObj.friendlyName = measureDetails[0]?.measureFriendlyName!
      myObj.subDomain = measureDetails[0]?.subDomainName
      myObj.type = measureDetails[0]?.typeName
      myObj.domain = measureDetails[0]?.domainName
      myObj.cmsId = measureDetails[0]?.cMSId ?? '-'
      myObj.subType = measureDetails[0]?.subTypeName
      myObj.application = measureDetails[0]?.applicationName
      myObj.programName = measureDetails[0]?.programName ?? '-'
      myObj.measureIdentifier = admMeasureDetails?.MedisolvMeasureId
      myObj.isEmptyIndicator = isEmptyIndicator
      myObj.isExpandable = false

      type2Results.push(myObj)
    }
  }

  const subOrgIds = request.subOrganization
    .sort((a, b) =>
      a.subOrganizationName!.localeCompare(b.subOrganizationName!)
    )
    .map((x) => x.subOrganizationId)
    .filter((x) => x !== undefined && x !== null)

  let facilities: Facility[] = []

  if (request.useSpecialEntityStructure)
    facilities = await measureResultsService.getFacilityByOrganization(
      request.organizationId
    )

  const options: MeasureResultOptions = {
    organizationId: request.organizationId,
    periodType: request.scorecardView,
    subOrganizationId: subOrgIds,
    startDate: request.startDate,
    endDate: request.endDate,
    isPartner: request.isPartner,
    measureIdentifier: request.measureIdentifier,
  }

  const aggregateResultsResponse =
    await measureResultsService.getMeasureResults(options)
  const measureResults =
    aggregateResultsResponse?.measureResultSummaryList ?? []

  const hospitalgrps = measureResults!
    .filter((x) => x.measureGUID == request.measureIdentifier)
    .reduce<Record<string, MeasureResultSummary[]>>((groups, item) => {
      if (!groups[item.entityCode!]) {
        groups[item.entityCode!] = []
      }
      groups[item.entityCode!]?.push(item)
      return groups
    }, {})

  for (const item of Object.entries(hospitalgrps)) {
    const [key, group] = item

    if (key === 'ALLORGANIZATION') continue

    let isEmptyIndicator = true
    const myObj: Partial<MeasureResultByHospital> = {}
    let ccnNumber: string
    let hospitalName: string

    if (
      request.isPartner &&
      request.subOrganization.some(
        (x) => x.subOrganizationId === 'ALLORGANIZATION'
      )
    ) {
      const subOrg = request.subOrganization.find(
        (x) => x.subOrganizationId === group[0]?.sourceContainerIdentifier
      )
      hospitalName = subOrg?.subOrganizationName ?? ''
      ccnNumber = subOrg?.subOrganizationId ?? ''
      myObj.hospitalId = group[0]?.sourceContainerIdentifier
    } else {
      const subOrg = request.subOrganization.find(
        (x) => x.subOrganizationId === key
      )
      ccnNumber = subOrg?.ccnNumber ?? ''
      hospitalName = subOrg?.subOrganizationName ?? ''
      myObj.hospitalId = key
    }

    myObj.hospital = group[0]?.entityName ?? ''
    myObj.entityNameForDetails = hospitalName.replace('&', '[AND]')
    myObj.ccnNumber = ccnNumber

    const detailList: DetailsList[] = []
    for (const item of dateRangeSpan) {
      const span = item.replace('-', '_') as
        | `Q${number}_${number}`
        | `CY_${number}`
        | `${Capitalize<string>}_${number}`
      const period = DateRange.getDateFromDateRangeSpan(
        request.scorecardView,
        span
      )

      if (admMeasureDetails?.NullRate) {
        myObj[span] = '-'
        detailList.push({ performance: null, denominator: null })
        continue
      }

      const rate = group.find(
        (x) =>
          dayjs(x.startDate).isSame(period.startDate, 'day') &&
          dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
      )

      if (rate) {
        detailList.push({
          performance: rate.performance,
          denominator: admMeasureDetails?.NullDenominator
            ? null
            : rate.denominator,
        })

        if (isRatioMeasure) {
          myObj[span] =
            rate.performance === null || rate.performance === -1
              ? '-'
              : rate.performance === 100
                ? `${rate.numeratorValue}/${rate.denominatorValue}<br>${rate.performance}`
                : `${rate.numeratorValue}/${rate.denominatorValue}<br>${rate.performance.toFixed(
                    2
                  )}`
        } else {
          myObj[span] =
            rate.performance === null || rate.performance === -1
              ? '-'
              : rate.performance === 100
                ? rate.performance.toString()
                : rate.performance.toFixed(2)
        }

        if (rate.performance !== null) isEmptyIndicator = false
      } else {
        myObj[span] = '-'
        detailList.push({ performance: null, denominator: null })
      }
    }

    const trendName =
      INotation[
        measureDetails[0]?.iNotationName! as unknown as keyof typeof INotation
      ]

    const trendCSS = TrendCSSHelper.getTrendSlopeCss(
      detailList,
      trendName,
      request.scorecardView
    )

    myObj.trendCss = trendCSS

    myObj.measureDescription = measureDetails[0]?.measureDescription.trim()!
    myObj.friendlyName = measureDetails[0]?.measureFriendlyName!
    myObj.subDomain = measureDetails[0]?.subDomainName
    myObj.type = measureDetails[0]?.typeName
    myObj.domain = measureDetails[0]?.domainName
    myObj.cmsId = measureDetails[0]?.cMSId ?? '-'
    myObj.subType = measureDetails[0]?.subTypeName
    myObj.application = measureDetails[0]?.applicationName
    myObj.programName = measureDetails[0]?.programName ?? '-'
    myObj.measureIdentifier = admMeasureDetails?.MedisolvMeasureId
    myObj.isEmptyIndicator = isEmptyIndicator

    const isExpandable =
      facilities.filter((x) => x.entityCode === key).length > 1
    myObj.isExpandable = isExpandable

    modelData.push(myObj)
  }

  type2Results.sort((a, b) => a.hospital!.localeCompare(b.hospital!))
  modelData.sort((a, b) => a.hospital!.localeCompare(b.hospital!))
  modelData.unshift(...type2Results)

  return modelData
}
