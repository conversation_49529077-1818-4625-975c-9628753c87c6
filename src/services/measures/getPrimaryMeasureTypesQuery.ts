import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { SelectionType } from '@/enums/selectionType'
import { redisHelper } from '@/lib/redis'
import CitCParametersService from '../citcParameters'
import { env } from '@/env'
import { PrimaryMeasureTypesQuery } from '@/types/primaryMeasureTypesQuery'
import { getOrganizationPreferences } from '../organizationPreferences/getOrganizationPreferences'
import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { OrganizationPreferenceKey } from '@/enums/organizationPreferenceKey'

export const getPrimaryMeasureTypesQuery = async (
  accessToken: string,
  request: PrimaryMeasureTypesQuery
): Promise<PrimaryMeasureTypeConstants[]> => {
  const cacheKey = `get-primary-measure-types-query-${request.organizationId || ''}`
  const cachedData = await redisHelper.get(cacheKey)
  if (cachedData && JSON.parse(cachedData).length > 0) {
    console.log(
      `Cache Hit for primary measure types: ${cachedData.substring(0, Math.min(100, cachedData.length))}`
    )
    return JSON.parse(cachedData) as PrimaryMeasureTypeConstants[]
  }

  const citCParametersService = new CitCParametersService(accessToken)

  if (request.selectionType == SelectionType.Partner) {
    const orgParameters = await citCParametersService.getPartnerParametersAsync(
      request.organizationId
    )
    const partnerPrimaryMeasureTypes = orgParameters.filter(
      (x) => x.key == OrganizationPreferenceKey.PRIMARYMEASURETYPE
    )

    await redisHelper.set(
      cacheKey,
      JSON.stringify(partnerPrimaryMeasureTypes.map((x) => x.value)),
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )

    return partnerPrimaryMeasureTypes.map(
      (x) => x.value
    ) as PrimaryMeasureTypeConstants[]
  } else {
    const orgPreferences = await getOrganizationPreferences(
      new AzureTableStorageWrapper(StorageTables.OrganizationPreferences),
      request.organizationId,
      OrganizationPreferenceKey.PRIMARYMEASURETYPE,
      {
        organizationId: request.organizationId,
        selectionType: request.selectionType,
      }
    )

    if (orgPreferences.length != 0) {
      await redisHelper.set(
        cacheKey,
        JSON.stringify(orgPreferences.map((x) => x.value)),
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )
      return orgPreferences.map((x) => x.value) as PrimaryMeasureTypeConstants[]
    } else {
      await redisHelper.set(
        cacheKey,
        JSON.stringify([PrimaryMeasureTypeConstants.HospitalMeasures]),
        Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
      )
      return [PrimaryMeasureTypeConstants.HospitalMeasures]
    }
  }
}
