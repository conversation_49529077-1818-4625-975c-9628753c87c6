import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import type { EncorELoadDataStatus } from '@/types/encorELoadDataStatus'
import dayjs from 'dayjs'
import { getDateString } from '@/lib/getDateString'
import { RestError } from '@azure/storage-blob'
import { getLoadStatus } from '../loadStatus/getLoadStatus'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'

export const getEncorELoadStatus = async (
  tableStorage: AzureTableStorageWrapper,
  organizationId: string,
  primaryMeasureType: PrimaryMeasureTypeConstants
) => {
  const result: EncorELoadDataStatus = {
    startDate: '',
    isDataAvailable: false,
    endDate: '',
    dataStatusTitle: '',
  }

  let loadDate = dayjs().format('YYYYMMDD')
  let loadType: 'EC' | 'EH' = 'EC'

  if (
    primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures ||
    primaryMeasureType == PrimaryMeasureTypeConstants.None
  ) {
    loadType = 'EH'
  }

  try {
    const loadStatuses = await getLoadStatus(
      tableStorage,
      organizationId,
      loadType,
      loadDate
    )

    if (loadStatuses.some((x) => x.organizationId === organizationId)) {
      result.isDataAvailable = true

      const orgData = loadStatuses.find(
        (x) => x.organizationId === organizationId
      )

      if (orgData) {
        const completionDate =
          primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures ||
          primaryMeasureType === PrimaryMeasureTypeConstants.None
            ? dayjs.utc(orgData.ehCalculationsCompletionDate).toDate()
            : dayjs.utc(orgData.ecCalculationsCompletionDate).toDate()

        result.startDate = completionDate ? getDateString(completionDate) : ''
      }
    } else {
      result.isDataAvailable = false
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error('Error reading encor-e load status:', error.message)
    } else if (error instanceof RestError) {
      console.error('Error reading encor-e load status:', error.message)
    }

    // TODO: Log error

    try {
      loadDate = dayjs().startOf('week').format('YYYYMMDD')

      const loadStatuses = await getLoadStatus(
        tableStorage,
        organizationId,
        loadType,
        loadDate
      )

      if (loadStatuses.some((x) => x.organizationId === organizationId)) {
        result.isDataAvailable = true

        const orgData = loadStatuses.find(
          (x) => x.organizationId === organizationId
        )

        if (orgData) {
          const completionDate =
            primaryMeasureType ===
              PrimaryMeasureTypeConstants.HospitalMeasures ||
            primaryMeasureType === PrimaryMeasureTypeConstants.None
              ? dayjs.utc(orgData.ehCalculationsCompletionDate).toDate()
              : dayjs.utc(orgData.ecCalculationsCompletionDate).toDate()

          result.startDate = completionDate ? getDateString(completionDate) : ''
        }
      } else {
        result.isDataAvailable = false
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error('Error reading encor-e load status:', error.message)
      } else if (error instanceof RestError) {
        console.error('Error reading encor-e load status:', error.message)
      }

      // TODO: Log error

      result.isDataAvailable = false
    }
  }

  return result
}
