
import dayjs from 'dayjs';
import { calculateTimeDuration } from '../../lib/calculateTimeDuration';
import StorageService from '../storeage'
import type { LoadStatusModel } from '@/types/loadStatusModel'
import { env } from '@/env'
import CitCOrganizationService from '@/services/citc/citCOrganizations'
import HydraServices from '@/services/hydraServicesLastpublishdate'
import { SelectionType } from '@/enums/selectionType'
import MeasureResultsService from '@/services/measureResults'
import { MSPApiResult } from '@/types/mspApiResult'
import { ProcedureStatus } from '@/types/procedureStatus'
import { getDateString } from '@/lib/getDateString'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { Organization } from '@/types/organization'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { getLoadStatus } from '../loadStatus/getLoadStatus'
import { LoadStatus } from '@/types/loadStatus'
import { any } from 'zod';

export const getAllDataLoadStatus = async (

    ctx: any,
    input: any,
    tableStorage: AzureTableStorageWrapper,
    organizationId: string,
    primaryMeasureType: PrimaryMeasureTypeConstants[]


): Promise<LoadStatus[]> => {
    let data: LoadStatus[] = [];
    let dataEC: LoadStatus[] = [];
    let dataEH: LoadStatus[] = [];
    let fileDataResult: LoadStatus[] = [];
    let EhfileDataResult: LoadStatus[] = [];

    const formatDateToString = (dateString: string | undefined): string => {
        if (!dateString) return dayjs().format('YYYYMMDD');
        return dayjs(dateString, 'MM/DD/YYYY').format('YYYYMMDD');
    };

    const loadDate = formatDateToString(input.loaddate);
    //let loadDate = "20250323";
    const formatDate = (date: any) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}${month}${day}`;
    };

    // Add a helper function for consistent date formatting
    const formatDateTime = (dateValue: string | Date | null): string => {
        if (!dateValue) return '';
        try {
            // Handle ISO format strings specifically
            const date = dayjs(dateValue);
            return date.isValid() ? date.format('M/D/YYYY h:mm:ss A') : '';
        } catch {
            return '';
        }
    };




    //CallHydraAPI Result


    const hydraServices = new HydraServices(ctx.session.accessToken!)
    const hydraResult = await hydraServices.getLastpublishdate()

    //let claimOrganizations 
    const filterOrgs = ctx.session.Organization;

    //Cict 

    const citCOrganizationService = new CitCOrganizationService(ctx.session.accessToken!)
    //const measureResultsService = new MeasureResultsService(ctx.session.accessToken!)

    //processing EH data files
    try {
        // const date = dayjs(input, 'MM/DD/YYYY');

        //process EC data 
        dataEC = await getLoadStatus(
            tableStorage,
            organizationId,
            "EC",

        )


        dataEC.forEach(x => x.processingType = "Timed EC")
        dataEC.forEach(x => {
            try {
                // Ensure we're working with valid date strings
                if (x.startDateTime && x.endDateTime) {
                    // Parse dates using explicit format
                    const startDate = dayjs(x.startDateTime, 'M/D/YYYY h:mm:ss A');
                    const endDate = dayjs(x.endDateTime, 'M/D/YYYY h:mm:ss A');

                    // Verify both dates are valid before calculating duration
                    if (startDate.isValid() && endDate.isValid()) {
                        x.duration = calculateTimeDuration(startDate, endDate);
                    } else {
                        x.duration = '0 seconds';
                    }
                } else {
                    x.duration = '0 seconds';
                }
            } catch (error) {
                console.error('Error calculating duration:', error);
                x.duration = '0 seconds';
            }
        });
        fileDataResult.push(...dataEC);

        //processing ehdata to final data 

        if (fileDataResult) {

            try {
                let procedureloadstatus: ProcedureStatus | null;
                let orgDetails: Organization | null;
                let hospitalNamel: string;

                const combinedData = [];

                for (const item of fileDataResult) {
                    // Initialize hospitalNamel as "not available" by default
                    hospitalNamel = "not available";

                    // Safely get organization details
                    orgDetails = await citCOrganizationService.getOrganizationDetailsAsync(item.organizationId!)
                        .catch(error => {
                            console.error(`Failed to fetch organization details for ID ${item.organizationId}:`, error);
                            return null;
                        });

                    // Early return if no org details
                    if (!orgDetails) {
                        combinedData.push({
                            ...item,
                            hospitalName: "not available",
                            organizationId: item.organizationId,
                            ccn: item.ccn,
                            organizationName: "Not Available"
                        });
                        continue;
                    }

                    // Safe check for CCN matching
                    if (orgDetails.subOrganizations?.length > 0 && item.ccn) {
                        const normalizedItemCCN = item.ccn?.trim().replace(/\s+/g, '') || '';

                        const matchingSubOrg = orgDetails.subOrganizations.find(subOrg =>
                            subOrg?.ccnNumber &&
                            subOrg.ccnNumber.trim().replace(/\s+/g, '') === normalizedItemCCN
                        );

                        hospitalNamel = matchingSubOrg?.subOrganizationName || "not available";
                    }

                    // Safely get procedure load status
                    procedureloadstatus = await ctx.measureResultsService.getProcedureLoadStatus({
                        isPartner: ctx.selectiontype === SelectionType.Partner,
                        organizationId: item.organizationId!,

                    });

                    combinedData.push({
                        ...item,
                        hospitalName: hospitalNamel,
                        organizationId: item.organizationId,
                        ccn: item.ccn == null ? "not available" : item.ccn,
                        organizationName: orgDetails?.organizationName || "Not Available",
                        startDateTimeDisplay: formatDateTime(item.startDateTime),
                        endDateTimeDisplay: formatDateTime(item.endDateTime),
                        cdmLastLoadDateDisplay: formatDateTime(item.cdmLastLoadDate),
                        mspLastPublishDateDisplay: formatDateTime(item.mspLastPublishDate) || "Not Available",
                        ecCalculationsCompletionDateDisplay: formatDateTime(item.ecCalculationsCompletionDate),
                        ehCalculationsCompletionDateDisplay: formatDateTime(item.ehCalculationsCompletionDate),
                        admLoadStartDate: formatDateTime(procedureloadstatus?.startDate),
                        admLoadEndDate: formatDateTime(procedureloadstatus?.endDate),
                        duration: item.duration || "0 seconds"
                    });
                }

                data.push(...combinedData);
            }
            catch (err) {
                console.error("Error processing combined data:", err);
                throw err;
            }
        }

        //Process EH data 
        dataEH = await getLoadStatus(
            tableStorage,
            organizationId,
            "EH",

        )

        dataEH.forEach(x => x.processingType = "Timed EH")
        dataEH.forEach(x => {
            try {
                // Ensure we're working with valid date strings
                if (x.startDateTime && x.endDateTime) {
                    // Parse dates using explicit format
                    const startDate = dayjs(x.startDateTime, 'M/D/YYYY h:mm:ss A');
                    const endDate = dayjs(x.endDateTime, 'M/D/YYYY h:mm:ss A');

                    // Verify both dates are valid before calculating duration
                    if (startDate.isValid() && endDate.isValid()) {
                        x.duration = calculateTimeDuration(startDate, endDate);
                    } else {
                        x.duration = '0 seconds';
                    }
                } else {
                    x.duration = '0 seconds';
                }
            } catch (error) {
                console.error('Error calculating duration:', error);
                x.duration = '0 seconds';
            }
        });
        EhfileDataResult.push(...dataEH);


        //processing ehdata to final data 

        if (EhfileDataResult) {

            try {
                let procedureloadstatus: ProcedureStatus | null;
                let orgDetails: Organization | null;
                let hospitalNamel: string;

                const combinedData = [];

                for (const item of EhfileDataResult) {
                    // Initialize hospitalNamel as "not available" by default
                    hospitalNamel = "not available";

                    // Safely get organization details
                    orgDetails = await citCOrganizationService.getOrganizationDetailsAsync(item.organizationId!)
                        .catch(error => {
                            console.error(`Failed to fetch organization details for ID ${item.organizationId}:`, error);
                            return null;
                        });

                    // Early return if no org details
                    if (!orgDetails) {
                        combinedData.push({
                            ...item,
                            hospitalName: "not available",
                            organizationId: item.organizationId,
                            ccn: item.ccn,
                            organizationName: "Not Available"
                        });
                        continue;
                    }

                    // Safe check for CCN matching
                    if (orgDetails.subOrganizations?.length > 0 && item.ccn) {
                        const normalizedItemCCN = item.ccn?.trim().replace(/\s+/g, '') || '';

                        const matchingSubOrg = orgDetails.subOrganizations.find(subOrg =>
                            subOrg?.ccnNumber &&
                            subOrg.ccnNumber.trim().replace(/\s+/g, '') === normalizedItemCCN
                        );

                        hospitalNamel = matchingSubOrg?.subOrganizationName || "not available";
                    }

                    // Safely get procedure load status
                    procedureloadstatus = await ctx.measureResultsService.getProcedureLoadStatus({
                        isPartner: ctx.selectiontype === SelectionType.Partner,
                        organizationId: item.organizationId!,

                    });

                    combinedData.push({
                        ...item,
                        hospitalName: hospitalNamel,
                        organizationId: item.organizationId,
                        ccn: item.ccn == null ? "not available" : item.ccn,
                        organizationName: orgDetails?.organizationName || "Not Available",
                        startDateTimeDisplay: formatDateTime(item.startDateTime),
                        endDateTimeDisplay: formatDateTime(item.endDateTime),
                        cdmLastLoadDateDisplay: formatDateTime(item.cdmLastLoadDate),
                        mspLastPublishDateDisplay: formatDateTime(item.mspLastPublishDate) || "Not Available",
                        ecCalculationsCompletionDateDisplay: formatDateTime(item.ecCalculationsCompletionDate),
                        ehCalculationsCompletionDateDisplay: formatDateTime(item.ehCalculationsCompletionDate),
                        admLoadStartDate: formatDateTime(procedureloadstatus?.startDate),
                        admLoadEndDate: formatDateTime(procedureloadstatus?.endDate),
                        duration: item.duration || "0 seconds"
                    });
                }

                data.push(...combinedData);
            }
            catch (err) {
                console.error("Error processing combined data:", err);
                throw err;
            }
        }



    }
    catch (error) {
        console.error("Error fetching all load data status: ", error);
        throw new Error("Failed to retrieve all load data status.");
    }



    // Remove duplicates before returning
    return [...new Set(data.map(item => JSON.stringify(item)))].map(item => JSON.parse(item));
}
