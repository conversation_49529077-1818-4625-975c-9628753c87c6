import { SnowflakeMeasure } from '@/types/measure'
import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'
import { type Connection, type Pool, type Bind } from 'snowflake-sdk'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { ECMeasureResultSummary } from '@/types/eCMeasureResultSummary'
import { MeasureResultSummary } from '@/types/measureResultSummary'
import { Dayjs } from 'dayjs'
import { isPotentialSQLInjection } from '@/services/snowflake/SnowflakeHelper'
import { MeasureResultDetails } from '@/types/measureResultDetails'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { ParameterConstants } from '@/enums/parameterConstants'
import { Group } from '@/types/group'
import { IntervalType } from '@/types/intervalType'
import { Entity } from '@/types/entity'

export class MeasureRepository extends SnowflakeRepository<SnowflakeMeasure> {
  constructor(connectionPool: Pool<Connection>, tableName: string) {
    super(connectionPool, tableName, 'MedisolvMeasureId')
  }

    findById = async (id: string | number): Promise<SnowflakeMeasure | undefined> => {
        const results = await this.execute<SnowflakeMeasure>(
            `SELECT TOP 1 * FROM ${this.tableName} WHERE ${this.identityColumn} ILIKE ?`,
            [id]
        )
        if (!results) return undefined
        return results[0]
    }

    calculateMeasureResults = async (
        organizationId: string,
        isPartner: boolean,
        periodType: IntervalType,
        startDate: Dayjs,
        endDate: Dayjs,
        isFacilityLevel: boolean,
        isCombinedGroup: boolean,
        subOrganizationIds: string[],
        measureIdentifier?: string
    ): Promise<MeasureResultSummary[]> => {
    // Adjust dates based on period type
    let adjustedFromDate = startDate.clone()
    let adjustedToDate = endDate.clone()

    switch (periodType) {
      case 'Y':
        adjustedFromDate = startDate.startOf('year') // Jan 1
        adjustedToDate = endDate.add(1, 'year').startOf('year') // Jan 1 of next year
        break
      case 'Q':
        // Calculate quarter start month (0, 3, 6, 9)
        const fromQuarter = Math.floor(startDate.month() / 3)
        adjustedFromDate = startDate.month(fromQuarter * 3).startOf('month')

        // Calculate next quarter start
        const toQuarter = Math.floor(endDate.month() / 3)
        const nextQuarter = toQuarter + 1
        if (nextQuarter <= 3) {
          // 0-based quarters (0-3)
          adjustedToDate = endDate.month(nextQuarter * 3).startOf('month')
        } else {
          adjustedToDate = endDate.add(1, 'year').startOf('year') // Jan 1 of next year
        }
        break
      case 'M':
        adjustedFromDate = startDate.startOf('month')

        if (endDate.month() < 11) {
          // 0-based months (0-11)
          adjustedToDate = endDate.add(1, 'month').startOf('month')
        } else {
          adjustedToDate = endDate.add(1, 'year').startOf('year') // Jan 1 of next year
        }
        break
    }

    const placeholders = subOrganizationIds.map(() => '?').join(', ')

    let groupEntitySelect: string = ''
    let groupEntitySelectParameters: any[] = []

    // Organizational View
    if (!isPartner) {
      // All Sub Organizations
      if (subOrganizationIds.includes('*')) {
        const organizationTypeLevel = isFacilityLevel
          ? EntityOrganizationTypeConstants.FacilityLevel
          : isCombinedGroup
            ? EntityOrganizationTypeConstants.CombinedGroupLevel
            : EntityOrganizationTypeConstants.HospitalLevel

        // Check if there are sub-organizations
        groupEntitySelect = `
                    SELECT e."Id"
                    FROM AllEntities e
                    WHERE 
                        e."SourceContainerIdentifier" LIKE CONCAT(?, '%')
                        AND e."OrganizationTypeId" = ?
                `
        groupEntitySelectParameters = [organizationId, organizationTypeLevel]
      } else {
        groupEntitySelect = `
                    SELECT e."Id"
                    FROM AllEntities e
                    WHERE
                        e."Code" IN (${placeholders})
                `
        groupEntitySelectParameters = [...subOrganizationIds]
      }
      // Partner View
    } else {
      if (subOrganizationIds.includes('ALLORGANIZATION')) {
        // For all organizations under a partner
        const orgPlaceholders = subOrganizationIds
          .filter((id) => id !== 'ALLORGANIZATION')
          .map(() => '?')
          .join(', ')
        const values = subOrganizationIds
          .filter((id) => id !== 'ALLORGANIZATION')
          .map((value) => {
            if (isPotentialSQLInjection(value)) {
              throw `PotentialSQLInjection ${value}`
            }
            return `${value}%`
          })

        if (orgPlaceholders.length > 0) {
          groupEntitySelect = `
                        WITH SourceCountainerIdentifiers AS (
                            SELECT column1 AS "Val" FROM VALUES ${values.map((val) => `('${val}')`).join(',')}
                        ),
                        SCIOrgTypeId AS (
                            SELECT 
                                sci."Val" as "Val", 
                                (
                                    SELECT
                                        CASE
                                            WHEN EXISTS (
                                                SELECT 1
                                                FROM  AllEntities e
                                                WHERE e."OrganizationTypeId" = '${EntityOrganizationTypeConstants.CombinedGroupLevel}'
                                                  AND e."SourceContainerIdentifier" ILIKE sci."Val"
                                            ) THEN '${EntityOrganizationTypeConstants.CombinedGroupLevel}'
                                            ELSE '${EntityOrganizationTypeConstants.HospitalLevel}'
                                        END
                                ) as "OrgTypeId"
                            FROM SourceCountainerIdentifiers sci
                        )
                        SELECT 
                            *
                        FROM AllEntities e, SCIOrgTypeId sciorgtypeid
                        WHERE
                            e."SourceContainerIdentifier" ILIKE sciorgtypeid."Val"
                            AND e."OrganizationTypeId" = sciorgtypeid."OrgTypeId"
                    `
          groupEntitySelectParameters = []
        } else {
          console.log(
            'No sub organizations provided, will return empty - is this correct?'
          )
        }
      } else if (subOrganizationIds.includes('*')) {
        // All entities at partner roll-up level
        groupEntitySelect = `
                    SELECT e."Id"
                    FROM AllEntities e
                    WHERE
                        e."OrganizationTypeId" = '${EntityOrganizationTypeConstants.PatnerRollUpLevel}'
                `
        groupEntitySelectParameters = []
      } else if (subOrganizationIds.length > 0) {
        // Specific entities
        groupEntitySelect = `
                    SELECT e."Id"
                    FROM AllEntities e
                    WHERE
                        e."Code" IN (${placeholders})
                `
        groupEntitySelectParameters = [...subOrganizationIds]
      } else {
          return [] //
      }
    }

    // Build the SQL query
    const sql = `
            WITH Facility AS (
                -- Get entity type ID for Facility
                SELECT "Id" 
                FROM MECA."EntityTypes" 
                WHERE "Code" = '${EntityTypeConstants.Facility}'
            ),
            AllEntities AS (
                -- Get all entities
                SELECT 
                    e."Id", 
                    e."Code", 
                    e."Description", 
                    e."SourceContainerIdentifier", 
                    ot."Code" as "OrganizationTypeId"
                FROM MECA."Entities" e
                JOIN MECA."OrganizationTypes" ot ON e."OrganizationTypeId" = ot."Id"
                JOIN Facility f ON e."EntityTypeId" = f."Id"
            ),
            GroupEntityIds AS (${groupEntitySelect}),
            MeasureSummaryData AS (
                -- Get measure summary data
                SELECT DISTINCT
                    ent."Description" AS "EntityName",
                    TO_CHAR(m."StartDate", 'YYYY-MM-DD"T"HH24:MI:SS') as "StartDate",
                    TO_CHAR(m."EndDate", 'YYYY-MM-DD"T"HH24:MI:SS') as "EndDate",
                    ent."Id" AS "EntityId",
                    ent."Code" AS "EntityCode",
                    m."Id",
                    m."IncompleteCases",
                    m."MeasureGUID",
                    m."MeasureSubId",
                    m."IPP",
                    m."DenominatorOnly" AS "InDenominatorOnly",
                    m."DenominatorExclusion",
                    m."Numerator",
                    m."Denominator",
                    m."DenominatorException",
                    m."Performance",
                    m."Period",
                    m."NumeratorValue",
                    m."DenominatorValue",
                    ent."SourceContainerIdentifier",
                    m."HoverText"
                FROM MECA."MeasureSummary" m
                INNER JOIN MECA."Entities" ent ON m."EntitiesId" = ent."Id"
                INNER JOIN Facility f ON ent."EntityTypeId" = f."Id"
                INNER JOIN GroupEntityIds g ON m."EntitiesId" = g."Id"
                WHERE m."MeasureGUID" != '00000000-0000-0000-0000-000000000000'
                AND (? IS NULL OR ? = '' OR LOWER(m."MeasureGUID") = ?)
                AND m."Period" = ?
                AND (m."StartDate" >= ? AND m."EndDate" <= ?)
            )
            -- Final result set
            SELECT * FROM MeasureSummaryData
        `

    // Prepare parameters
    const params: Bind[] = [
      // Group Entity Id Parameters
      ...groupEntitySelectParameters,

      // MeasureSummaryData parameters
      measureIdentifier?.toLowerCase() || '',
      measureIdentifier?.toLowerCase() || '',
      measureIdentifier?.toLowerCase() || '',
      periodType,
      adjustedFromDate.toISOString(),
      adjustedToDate.toISOString(),
    ]

    // Execute the query
    const results = await this.execute(sql, params)

    // Transform results to match expected format
    return (
      results?.map(
        (row: any) =>
          ({
            id: row.Id,
            entityName: row.EntityName,
            cdmDataSourceName: row.CDMDataSourceName ?? '',
            measureGUID: row.MeasureGUID,
            measureSubId: row.MeasureSubId,
            entityCode: row.EntityCode,
            sourceContainerIdentifier: row.SourceContainerIdentifier,
            startDate: row.StartDate,
            endDate: row.EndDate,
            period: row.Period,
            ipp: row.IPP,
            inDenominatorOnly: row.InDenominatorOnly,
            denominatorExclusion: row.DenominatorExclusion,
            numerator: row.Numerator,
            denominator: row.Denominator,
            denominatorException: row.DenominatorException,
            performance: row.Performance,
            numeratorValue: row.NumeratorValue,
            denominatorValue: row.DenominatorValue,
            incompleteCases: row.IncompleteCases,
            hoverText: row.HoverText,
            // Adding missing properties required by MeasureResultSummary type
            numeratorExclusion: null,
            percentile: null,
            processingStartDate: null,
            processingEndDate: null,
          }) as MeasureResultSummary
      ) || ([] as MeasureResultSummary[])
    )
  }

  calculateECMeasureResults = async (
    organizationId: string,
    isPartner: boolean,
    periodType: string,
    startDate: Dayjs,
    endDate: Dayjs,
    isSubmissionGroupLevel: boolean,
    submissionGroupIds: string[],
    measureIdentifier?: string
  ): Promise<ECMeasureResultSummary[]> => {
    // Adjust dates based on period type
    let adjustedFromDate = startDate.clone()
    let adjustedToDate = endDate.clone()

    switch (periodType) {
      case 'Y':
        adjustedFromDate = startDate.startOf('year') // Jan 1
        adjustedToDate = endDate.add(1, 'year').startOf('year') // Jan 1 of next year
        break
      case 'Q':
        // Calculate quarter start month (0, 3, 6, 9)
        const fromQuarter = Math.floor(startDate.month() / 3)
        adjustedFromDate = startDate.month(fromQuarter * 3).startOf('month')

        // Calculate next quarter start
        const toQuarter = Math.floor(endDate.month() / 3)
        const nextQuarter = toQuarter + 1
        if (nextQuarter <= 3) {
          // 0-based quarters (0-3)
          adjustedToDate = endDate.month(nextQuarter * 3).startOf('month')
        } else {
          adjustedToDate = endDate.add(1, 'year').startOf('year') // Jan 1 of next year
        }
        break
      case 'M':
        adjustedFromDate = startDate.startOf('month')

        if (endDate.month() < 11) {
          // 0-based months (0-11)
          adjustedToDate = endDate.add(1, 'month').startOf('month')
        } else {
          adjustedToDate = endDate.add(1, 'year').startOf('year') // Jan 1 of next year
        }
        break
    }
    const placeholders = submissionGroupIds.map(() => '?').join(', ')
    const isSubmissionGroupLevelParm = isSubmissionGroupLevel ? 1 : 0

    let groupEntitySelect: string = ''
    let groupEntitySelectParameters: any[] = []

    // Organizational View
    if (!isPartner) {
      // All Submission Groups
      if (submissionGroupIds.includes('*')) {
        // Submission Group Level
        if (isSubmissionGroupLevel) {
          groupEntitySelect = `
                        SELECT e."Id"
                            FROM AllEntities e
                        WHERE 
                            (
                                EXISTS (
                                    SELECT 1
                                    FROM AllEntities
                                    WHERE "SourceContainerIdentifier" LIKE CONCAT(?, '%')
                                    AND "OrganizationTypeId" = '${EntityOrganizationTypeConstants.CombinedGroupLevel}'
                                )
                                AND
                                -- Pick combined Group Level for the organization
                                    e."SourceContainerIdentifier" LIKE CONCAT(?, '%')
                                AND
                                    e."OrganizationTypeId" = '${EntityOrganizationTypeConstants.CombinedGroupLevel}'
                            ) 
                           OR (
                                NOT EXISTS (
                                    SELECT 1
                                    FROM AllEntities
                                    WHERE "SourceContainerIdentifier" LIKE CONCAT(?, '%')
                                      AND "OrganizationTypeId" = '${EntityOrganizationTypeConstants.CombinedGroupLevel}'
                                )
                                AND
                                e."Id" IN (
                                    SELECT DISTINCT "EntitiesId"
                                    FROM MECA."ActiveMeasures"
                                    WHERE YEAR("ProcessingStartDate") >= YEAR(TO_DATE(?))
                                    AND YEAR("ProcessingStartDate") <= YEAR(TO_DATE(?))
                                )
                                AND e."SourceContainerIdentifier" LIKE CONCAT(?, '%')
                                AND e."OrganizationTypeId" = '${EntityOrganizationTypeConstants.SubmissionGroupLevel}'
                           )
                    `
          groupEntitySelectParameters = [
            organizationId,
            organizationId,
            organizationId,
            adjustedFromDate.toISOString(),
            adjustedToDate.toISOString(),
            organizationId,
          ]
        } else {
          groupEntitySelect = `
                        SELECT e."Id"
                            FROM AllEntities e
                        WHERE
                            e."SourceContainerIdentifier" LIKE CONCAT(?, '%')
                            AND e."OrganizationTypeId" = '${EntityOrganizationTypeConstants.ProviderLevel}'
                    `
          groupEntitySelectParameters = [organizationId]
        }
      } else {
        groupEntitySelect = `
                        SELECT e."Id"
                            FROM AllEntities e
                        WHERE
                            e."SourceContainerIdentifier" LIKE CONCAT(?, '%')
                            AND e."OrganizationTypeId" = (SELECT OrganizationTypeLevel FROM OrganizationTypeLevel)
                            AND e."Code" IN (${placeholders})
                `
        groupEntitySelectParameters = [organizationId, ...submissionGroupIds]
      }

      // Partner View
    } else {
      if (submissionGroupIds.includes('ALLORGANIZATION')) {
        if (isSubmissionGroupLevel) {
          groupEntitySelect = `
                        SELECT e."Id"
                            FROM AllEntities e
                        WHERE
                            (
                                e."OrganizationTypeId" = '${EntityOrganizationTypeConstants.CombinedGroupLevel}'
                                AND e."SourceContainerIdentifier" IN (${placeholders})
                            ) OR (
                                e."Id" IN (
                                    SELECT DISTINCT "EntitiesId"
                                    FROM MECA."ActiveMeasures"
                                    WHERE YEAR("ProcessingStartDate") >= YEAR(TO_DATE(?))
                                    AND YEAR("ProcessingStartDate") <= YEAR(TO_DATE(?))
                                )
                                AND REPLACE(e."SourceContainerIdentifier", '_EC', '') IN (${placeholders})
                                AND e."OrganizationTypeId" = '${EntityOrganizationTypeConstants.SubmissionGroupLevel}'                                
                            )`
          groupEntitySelectParameters = [
            ...submissionGroupIds,
            adjustedFromDate.toISOString(),
            adjustedToDate.toISOString(),
            ...submissionGroupIds,
          ]
        } else {
          groupEntitySelect = `
                        SELECT e."Id"
                            FROM AllEntities e
                        WHERE
                            e."OrganizationTypeId" = '${EntityOrganizationTypeConstants.ProviderLevel}'
                    `
          groupEntitySelectParameters = []
        }
      } else {
        if (submissionGroupIds.includes('*')) {
          groupEntitySelect = `
                        SELECT e."Id"
                            FROM AllEntities e
                        WHERE
                            e."OrganizationTypeId" = '${EntityOrganizationTypeConstants.PatnerRollUpLevel}'
                    `
          groupEntitySelectParameters = []
        } else {
          groupEntitySelect = `
                        SELECT e."Id"
                            FROM AllEntities e
                        WHERE
                            e."OrganizationTypeId" = (SELECT OrganizationTypeLevel FROM OrganizationTypeLevel)
                          AND e."Code" IN (${placeholders})
                    `
          groupEntitySelectParameters = submissionGroupIds
        }
      }
    }

    // Build the SQL query
    const sql = `
            WITH RenderingProvider AS (
                -- Get entity type ID for RenderingProvider
                SELECT "Id" 
                FROM MECA."EntityTypes" 
                WHERE "Code" = '${EntityTypeConstants.RenderingProvider}'
            ),
            OrganizationTypeLevel AS (
                -- Determine organization type level
                SELECT 
                    CASE 
                        WHEN ${isSubmissionGroupLevelParm} = 1 THEN '${EntityOrganizationTypeConstants.SubmissionGroupLevel}' 
                        ELSE '${EntityOrganizationTypeConstants.ProviderLevel}' 
                    END AS OrganizationTypeLevel
            ),
            AllEntities AS (
                -- Get all EC entities
                SELECT 
                    e."Id", 
                    e."Code", 
                    e."Description", 
                    e."SourceContainerIdentifier", 
                    ot."Code" as "OrganizationTypeId"
                FROM MECA."Entities" e
                JOIN MECA."OrganizationTypes" ot ON e."OrganizationTypeId" = ot."Id"
                JOIN RenderingProvider rp ON e."EntityTypeId" = rp."Id"
            ),
            GroupEntityIds AS (${groupEntitySelect}),
            MeasureSummaryData AS (
                -- Get measure summary data
                SELECT DISTINCT
                    ent."Description" AS "EntityName",
                    TO_CHAR(m."StartDate", 'YYYY-MM-DD"T"HH24:MI:SS') as "StartDate",
                    TO_CHAR(m."EndDate", 'YYYY-MM-DD"T"HH24:MI:SS') as "EndDate",
                    ent."Id" AS "EntityId",
                    ent."Code" AS "EntityCode",
                    m."Id",
                    m."IncompleteCases",
                    m."MeasureGUID",
                    m."MeasureSubId",
                    m."IPP",
                    m."DenominatorOnly" AS "InDenominatorOnly",
                    m."DenominatorExclusion",
                    m."Numerator",
                    m."Denominator",
                    m."DenominatorException",
                    m."Performance",
                    m."Period",
                    ent."SourceContainerIdentifier",
                    ent."OrganizationTypeId",
                    m."HoverText"
                FROM MECA."MeasureSummary" m
                INNER JOIN MECA."Entities" ent ON m."EntitiesId" = ent."Id"
                INNER JOIN RenderingProvider rp ON ent."EntityTypeId" = rp."Id"
                INNER JOIN GroupEntityIds g ON m."EntitiesId" = g."Id"
                WHERE m."MeasureGUID" != '00000000-0000-0000-0000-000000000000'
                AND (? IS NULL OR ? = '' OR LOWER(m."MeasureGUID") = ?)
                AND m."Period" = ?
                AND (m."StartDate" >= ? AND m."EndDate" <= ?)
            )
            -- Final result set
            SELECT * FROM MeasureSummaryData
        `

    // Convert boolean values to 1/0 for SQL compatibility
    const params: Bind[] = [
      // Group Entity Id Parameters
      ...groupEntitySelectParameters,

      // MeasureSummaryData parameters
      measureIdentifier?.toLowerCase() || '',
      measureIdentifier?.toLowerCase() || '',
      measureIdentifier?.toLowerCase() || '',
      periodType,
      adjustedFromDate.toISOString(),
      adjustedToDate.toISOString(),
    ]

    // Execute the query
    const results = await this.execute(sql, params)

    // Transform results to match expected format
    return (
      results?.map(
        (row: any) =>
          ({
            id: row.Id,
            entityName: row.EntityName,
            cdmDataSourceName: row.CDMDataSourceName ?? null,
            measureGUID: row.MeasureGUID,
            measureSubId: row.MeasureSubId,
            entityCode: row.EntityCode,
            sourceContainerIdentifier: row.SourceContainerIdentifier,
            startDate: row.StartDate,
            endDate: row.EndDate,
            period: row.Period,
            ipp: row.IPP,
            inDenominatorOnly: row.InDenominatorOnly,
            denominatorExclusion: row.DenominatorExclusion,
            numerator: row.Numerator,
            denominator: row.Denominator,
            denominatorException: row.DenominatorException,
            performance: row.Performance,
            percentile: row.Percentile ?? null,
            incompleteCases: row.IncompleteCases,
            processingStartDate: row.ProcessingStartDate ?? null,
            processingEndDate: row.ProcessingEndDate ?? null,
            organizationTypeId: row.OrganizationTypeId,
            entityId: row.EntityId,
            hoverText: row.HoverText,
          }) as ECMeasureResultSummary
      ) || ([] as ECMeasureResultSummary[])
    )
  }

  getEHPatientDetails = async (
    organizationId: string,
    isPartner: boolean,
    startDate: Dayjs,
    endDate: Dayjs,
    measureIdentifier: string,
    subOrganizationIds: string[],
    organizationParams: SimplifiedParameter[],
    subOrganizationIdsByOrganization: any[],
    groups: Group[],
    category: string,
  ): Promise<MeasureResultDetails[]> => {
    if (category !== '*' && isPotentialSQLInjection(category)) {
      throw `category ${category} is unsafe`
    }
    const suffix = organizationParams.some(
      (param) =>
        param.key === ParameterConstants.SuffixSourceContainerIdentifiers &&
        param.value?.toLowerCase() === 'true'
    )
      ? '_EH'
      : ''

    let entityWhereClause = ''
    const entityParams: Bind[] = []

    if (!isPartner && subOrganizationIds.includes('*')) {
      if (subOrganizationIdsByOrganization.length === 1) {
        entityWhereClause = `AND e."SourceContainerIdentifier" = ? AND ot."Code" = '${EntityOrganizationTypeConstants.HospitalLevel}'`
        entityParams.push(organizationId + suffix)
      } else {
        const requiredGroup = groups.find(
          (grp) =>
            grp.subOrganizations.length ===
            subOrganizationIdsByOrganization.length
        )
        if (groups.length > 0 && requiredGroup) {
          entityWhereClause = `AND e."Code" = ?`
          entityParams.push(requiredGroup.groupId)
        }
      }
    } else if (subOrganizationIds.includes('*')) {
      const entityIdParameter = organizationParams.find(
        (param) => param.key === ParameterConstants.EntityId
      )
      if (entityIdParameter) {
        entityWhereClause = `AND e."Code" = ?`
        entityParams.push(entityIdParameter.value!)
      }
    } else {
      if (subOrganizationIds.length > 0) {
        entityWhereClause = `AND e."Code" IN (${subOrganizationIds.map(() => '?').join(', ')})`
        entityParams.push(...subOrganizationIds)
      }
    }

    const categoryAssignmentPredicate = category === '*'
        ? ''
        : `AND category."Code"='${category}' `

    const sql = `
      SELECT
        vwEncounterSummary."MeasureTallyIdentifier",
        MIN(DATE_PART(year, CURRENT_DATE()) - DATE_PART(year, patient."BirthDateTime")) AS "Age",
        MIN(patient."Id") AS "PatientsId",
        MIN(vwEncounterSummary."CategoryAssignmentsId") AS "CategoryAssignmentsId",
        TO_CHAR(MIN(vwEncounterSummary."ReferenceDate"), 'YYYY-MM-DD"T"HH24:MI:SS') as "ReferenceDate",
        MIN(CASE WHEN vwEncounterSummary."EncountersId" IS NOT NULL THEN enc."Id" ELSE NULL END) AS "MinEncountersId",
        MIN(m."MedisolvMeasureId") AS "MeasureGUID",
        MIN(patient."DisplayPatientIdentifier") AS "PatientIdentifier",
        MIN(patient."FirstName") AS "FirstName",
        MIN(patient."LastName") AS "LastName",
        MIN(category."Code") AS "Result",
        MIN(gender."SourceCode") AS "PatientGender",
        MIN(vwEncounterSummary."NumeratorValue") AS "NumeratorValue",
        MIN(vwEncounterSummary."DenominatorValue") AS "DenominatorValue"
      FROM MECA."vwEncounterMeasureSummary" AS vwEncounterSummary
      INNER JOIN ADM."Patients" AS patient ON vwEncounterSummary."PatientsId" = patient."Id"
      LEFT JOIN ADM."Encounters" AS enc ON vwEncounterSummary."EncountersId" = enc."Id"
      INNER JOIN MECA."CategoryAssignments" AS category ON vwEncounterSummary."CategoryAssignmentsId" = category."Id"
      LEFT JOIN CSRC."PatientGenderCodes" AS gender ON patient."PatientGenderCodeId" = gender."Id"
      INNER JOIN MECA."Entities" e ON vwEncounterSummary."EntityId" = e."Id"
      INNER JOIN MECA."OrganizationTypes" ot ON e."OrganizationTypeId" = ot."Id"
      INNER JOIN MECA."Measures" m ON LOWER(vwEncounterSummary."MeasureGUID") = LOWER(m."MedisolvMeasureId")
      WHERE (((LOWER(vwEncounterSummary."MeasureGUID") = ?)
              AND vwEncounterSummary."ReferenceDate" IS NOT NULL)
             AND ((vwEncounterSummary."ReferenceDate" >= ?)
                  AND (vwEncounterSummary."ReferenceDate" <= ?)))
        ${entityWhereClause}
        ${categoryAssignmentPredicate}
      GROUP BY vwEncounterSummary."MeasureTallyIdentifier"
      ORDER BY MIN(patient."Id")
    `

    const params: Bind[] = [
      measureIdentifier.toLowerCase(),
      startDate.toISOString(),
      endDate.toISOString(),
      ...entityParams,
    ]

    const results = await this.execute(sql, params)

    return (
      results?.map(
        (row: any) =>
          ({
            age: row.Age,
            patientsId: row.PatientsId,
            referenceDate: row.ReferenceDate,
            dischargeDateTime: row.ReferenceDate,
            encountersId: row.MinEncountersId || 0,
            measureGUID: row.MeasureGUID,
            patientIdentifier: row.PatientIdentifier,
            patientName: `${row.FirstName} ${row.LastName}`,
            result: row.Result,
            sex: row.PatientGender,
            numeratorValue: row.NumeratorValue,
            denominatorValue: row.DenominatorValue,
          }) as MeasureResultDetails
      ) || ([] as MeasureResultDetails[])
    )
  }

  getECPatientDetails = async (
    organizationId: string,
    isPartner: boolean,
    startDate: Dayjs,
    endDate: Dayjs,
    measureIdentifier: string,
    entities: Entity[],
    entityCodes: string[],
    activeSubmissionGroupIds: number[],
    organizationParams: SimplifiedParameter[],
    category: string
  ): Promise<MeasureResultDetails[]> => {
    if (category !== '*' && isPotentialSQLInjection(category)) {
        throw `category ${category} is unsafe`
    }
    let suffix = organizationParams.some(
      (param) =>
        param.key === ParameterConstants.SuffixSourceContainerIdentifiers &&
        param.value?.toLowerCase() === 'true'
    )
      ? '_EC'
      : ''

    let entityIDs: number[] = []

    if (!isPartner) {
      if (entityCodes.includes('*')) {
        const groupEntities = entities.filter(
          (entity: Entity) =>
            entity.entityTypeName === EntityTypeConstants.RenderingProvider
        )
        let requiredEntities: Entity[] = []

        const type3Entities = groupEntities.filter(
          (entity: Entity) =>
            entity.organizationTypeCode ===
            EntityOrganizationTypeConstants.SubmissionGroupLevel
        )
        if (type3Entities.length == 1) {
          requiredEntities = groupEntities.filter(
            (x: Entity) =>
              x.organizationTypeCode ===
                EntityOrganizationTypeConstants.SubmissionGroupLevel &&
              x.sourceContainerIdentifier === organizationId + suffix
          )
        } else {
          requiredEntities = groupEntities.filter(
            (x: Entity) =>
              x.organizationTypeCode ===
                EntityOrganizationTypeConstants.CombinedGroupLevel &&
              x.sourceContainerIdentifier === organizationId + suffix
          )
        }
        if (!requiredEntities) {
          entityIDs = groupEntities
            .filter(
              (x: Entity) =>
                activeSubmissionGroupIds.includes(x.id) &&
                x.organizationTypeCode ===
                  EntityOrganizationTypeConstants.SubmissionGroupLevel
            )
            .map((x: any) => x.Id)
        } else {
          const entityCodes = requiredEntities.map((x: Entity) => x.code)
          entityIDs = groupEntities
            .filter((x: Entity) => entityCodes.includes(x.code))
            .map((x: Entity) => x.id)
        }
      } else {
        entityIDs = entities
          .filter(
            (entity: Entity) =>
              entityCodes.includes(entity.code) &&
              entity.sourceContainerIdentifier.includes(organizationId + suffix)
          )
          .map((entity: Entity) => entity.id)
      }
    } else {
      if (entityCodes.includes('*')) {
        const partnerEntityIdParam = organizationParams.find(
          (param) => param.key === ParameterConstants.EcOverallGroupId
        )?.value

        if (partnerEntityIdParam) {
          entityIDs = entities
            .filter((entity: Entity) => entity.code === partnerEntityIdParam)
            .map((entity: Entity) => entity.id)
        } else {
          console.warn('No EcOverallGroupId parameter available')
        }
      } else {
        entityIDs = entities
          .filter(
            (entity: Entity) =>
              entityCodes.includes(entity.code) &&
              entity.sourceContainerIdentifier.includes(organizationId + suffix)
          )
          .map((entity: Entity) => entity.id)
      }
    }

    const measure = await this.findById(measureIdentifier)
    const denominatorQualifyingType = measure?.DenominatorQualifyingType
    const categoryAssignmentPredicate = category === '*'
      ? ''
      : `AND category."Code"='${category}' `

    if (
      denominatorQualifyingType &&
      denominatorQualifyingType.includes('Encounter')
    ) {
      const sql = `
      SELECT
        vwEncounterSummary."MeasureTallyIdentifier",
        MIN(DATE_PART(year, CURRENT_DATE()) - DATE_PART(year, patient."BirthDateTime")) AS "Age",
        MIN(patient."Id") AS "PatientsId",
        MIN(vwEncounterSummary."CategoryAssignmentsId") AS "CategoryAssignmentsId",
        TO_CHAR(MIN(vwEncounterSummary."ReferenceDate"), 'YYYY-MM-DD"T"HH24:MI:SS') as "ReferenceDate",
        MIN(CASE WHEN vwEncounterSummary."EncountersId" IS NOT NULL THEN enc."Id" ELSE NULL END) AS "MinEncountersId",
        MIN(m."MedisolvMeasureId") AS "MeasureGUID",
        MIN(patient."DisplayPatientIdentifier") AS "PatientIdentifier",
        MIN(patient."FirstName") AS "FirstName",
        MIN(patient."LastName") AS "LastName",
        MIN(category."Code") AS "Result",
        MIN(gender."SourceCode") AS "PatientGender"
      FROM MECA."vwEncounterMeasureSummary" AS vwEncounterSummary
      INNER JOIN ADM."Patients" AS patient ON vwEncounterSummary."PatientsId" = patient."Id"
      LEFT JOIN ADM."Encounters" AS enc ON vwEncounterSummary."EncountersId" = enc."Id"
      INNER JOIN MECA."CategoryAssignments" AS category ON vwEncounterSummary."CategoryAssignmentsId" = category."Id"
      LEFT JOIN CSRC."PatientGenderCodes" AS gender ON patient."PatientGenderCodeId" = gender."Id"
      INNER JOIN MECA."Measures" m ON LOWER(vwEncounterSummary."MeasureGUID") = LOWER(m."MedisolvMeasureId")
      WHERE (((LOWER(vwEncounterSummary."MeasureGUID") = ?)
              AND vwEncounterSummary."ReferenceDate" IS NOT NULL)
             AND ((vwEncounterSummary."ReferenceDate" >= ?)
                  AND (vwEncounterSummary."ReferenceDate" <= ?)))
        AND vwEncounterSummary."EntityId" IN (${entityIDs.map(() => '?').join(', ')})
        ${categoryAssignmentPredicate}
      GROUP BY vwEncounterSummary."MeasureTallyIdentifier"
      ORDER BY MIN(patient."Id")
    `

      const params: Bind[] = [
        measureIdentifier.toLowerCase(),
        startDate.toISOString(),
        endDate.toISOString(),
        ...entityIDs,
      ]

      const results = await this.execute(sql, params)

      return (
        results?.map(
          (row: any) =>
            ({
              age: row.Age,
              patientsId: row.PatientsId,
              referenceDate: row.ReferenceDate,
              dischargeDateTime: row.ReferenceDate,
              encountersId: row.MinEncountersId || 0,
              measureGUID: row.MeasureGUID,
              patientIdentifier: row.PatientIdentifier,
              patientName: `${row.FirstName} ${row.LastName}`,
              result: row.Result,
              sex: row.PatientGender,
            }) as MeasureResultDetails
        ) || ([] as MeasureResultDetails[])
      )
    } else {
      const sql = `
        SELECT
            YEAR(CURRENT_TIMESTAMP()) - YEAR(p."BirthDateTime") AS "Age",
            v."CategoryAssignmentsId" AS "CategoryAssignmentsId",
            v."EncountersId" AS "MinEncountersId",
            m."MedisolvMeasureId" AS "MeasureGUID",
            p."DisplayPatientIdentifier" AS "PatientIdentifier",
            p."FirstName" AS "FirstName",
            p."LastName" AS "LastName",
            p."Id" AS "PatientsId",
            TO_CHAR(v."ReferenceDate", 'YYYY-MM-DD"T"HH24:MI:SS') as "ReferenceDate",
            category."Code" AS "Result",
            pgc."SourceCode" AS "Sex"
        FROM
            MECA."vwEncounterMeasureSummary" v
        JOIN
            ADM."Patients" p ON v."PatientsId" = p."Id"
        JOIN
            MECA."CategoryAssignments" category ON v."CategoryAssignmentsId" = category."Id"
        JOIN
            CSRC."PatientGenderCodes" pgc ON p."PatientGenderCodeId" = pgc."Id"
        JOIN 
            MECA."Measures" m ON LOWER(v."MeasureGUID") = LOWER(m."MedisolvMeasureId")
        WHERE
            LOWER(v."MeasureGUID") = ?
            AND v."ReferenceDate" IS NOT NULL
            AND v."ReferenceDate" >= ? AND v."ReferenceDate" <= ?
            AND v."EntityId" IN (${entityIDs.map(() => '?').join(', ')})
            ${categoryAssignmentPredicate}
            QUALIFY ROW_NUMBER() OVER (PARTITION BY p."Id" ORDER BY v."ReferenceDate") = 1
    `
      const params: Bind[] = [
        measureIdentifier.toLowerCase(),
        startDate.toISOString(),
        endDate.toISOString(),
        ...entityIDs,
      ]
      const results = await this.execute(sql, params)

      return (
        results?.map(
          (row: any) =>
            ({
              age: row.Age,
              patientsId: row.PatientsId,
              referenceDate: row.ReferenceDate,
              dischargeDateTime: row.ReferenceDate,
              encountersId: row.MinEncountersId || 0,
              measureGUID: row.MeasureGUID,
              patientIdentifier: row.PatientIdentifier,
              patientName: `${row.FirstName} ${row.LastName}`,
              result: row.Result,
              sex: row.Sex,
            }) as MeasureResultDetails
        ) || ([] as MeasureResultDetails[])
      )
    }
  }
}
