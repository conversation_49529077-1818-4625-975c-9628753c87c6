import { config } from '@/config'
import type { AggregatedMeasureDetails } from '@/types/aggregatedMeasureDetails'
import { DateRange } from '@/lib/dateRange'
import type { MeasureResultDetailsQuery } from '@/types/measureResultDetailsQuery'
import dayjs from 'dayjs'
import type { MeasureResultOptions } from '@/types/measureResultOptions'
import { EntityDetailType } from '@/enums/entityDetailType'
import MeasureResultsService from '../measureResults'
import { SelectionType } from '@/enums/selectionType'
import CitCOrganizationService from '@/services/citc/citCOrganizations'

export async function getHospitalMeasureResultDetails(
  measureResultsService: MeasureResultsService,
  citcOrganizationService: CitCOrganizationService,
  request: MeasureResultDetailsQuery
): Promise<AggregatedMeasureDetails[]> {
  console.log('getHospitalMeasureResultDetails Started', new Date().toString())

  const dateRangeSpan = DateRange.getColumnIntervalsByCategory(
    request.scorecardView,
    request.startDate,
    request.endDate
  ) // Provide default periodType

  const measure = await measureResultsService.getMeasureByIdFromDb(
    request.measureIdentifier,
    request.organizationId
  )

  if (!measure) {
    console.log(
      'MeasureResultDetailsQueryHandler::Handle:: No measure found',
      request.measureIdentifier,
      request.organizationId
    )
    return []
  }

  const isRatioMeasure = config.customMeasures.ratioMeasures.some(
    (x) => x.toLowerCase() === request.measureIdentifier.toLowerCase()
  )

  const isVolumeMeasure = config.customMeasures.volumeMeasures.some(
    (x) => x.toLowerCase() === request.measureIdentifier.toLowerCase()
  )

  let isCombinedGroup: boolean = false
  if (!request.isPartner) {
    const subOrgs =
      await citcOrganizationService.getSubOrganizationsByOrganizationId(
        request.organizationId
      )
    isCombinedGroup = subOrgs?.length > 1
  }

  console.log(
    'MeasureResultDetailsQueryHandler::Handle:: Call to ADM API Started',
    new Date().toString()
  )
  const options: MeasureResultOptions = {
    organizationId: request.organizationId,
    periodType: request.scorecardView,
    subOrganizationId:
      request.entityDetailType === EntityDetailType.Measure
        ? ['*']
        : [request.entityId],
    startDate: request.startDate,
    endDate: request.endDate.add(1, 'day'),
    isPartner: request.isPartner,
    isCombinedGroup,
    isFacilityLevel: false, // Replace with actual value
    measureIdentifier: null,
  }

  const response = await measureResultsService.getMeasureResults(options)
  const result = (response?.measureResultSummaryList ?? [])?.filter(
    (x) => x.measureGUID === request.measureIdentifier
  )

  console.log(
    'MeasureResultDetailsQueryHandler::Handle:: Call to ADM API completed.',
    new Date().toString(),
    'Measure results-',
    result?.length,
    'records'
  )

  const measureDetailResult: AggregatedMeasureDetails[] = []

  for (const item of dateRangeSpan) {
    const span = item.replace('-', '_')
    const period = DateRange.getDateFromDateRangeSpan(
      request.scorecardView,
      span
    )

    const record = result?.find(
      (x) =>
        dayjs(x.startDate).isSame(period.startDate, 'day') &&
        dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
    )

    if (record) {
      if (isVolumeMeasure) {
        measureDetailResult.push({
          date: item,
          fullDate: period.startDate.format('MM/DD/YYYY'),
          rate: measure.NullRate
            ? '-'
            : record.performance !== null
              ? record.performance === 100
                ? record.performance.toString()
                : record.performance.toFixed(2)
              : '-',
          goal: '-',
          benchmark: '-',
          population: '-',
          inDenominatorOnly: '-',
          denominatorExclusion: '-',
          numerator: measure.NullNumerator
            ? '-'
            : (record.numerator?.toString() ?? '-'),
          denominator: '-',
          exception: '-',
          endDate: period.endDate?.format('MM/DD/YYYY'),
          noDrill: measure.NoDrill ?? false,
          isPatientLevelAccessAvailable: false, // Replace with actual value
        })
      } else {
        let rate = ''
        if (isRatioMeasure) {
          rate =
            record.performance !== null
              ? `${record.numeratorValue ?? ''}/${
                  record.denominatorValue ?? ''
                }<br>${record.performance}`
              : '-'
        } else {
          rate =
            record.performance !== null
              ? record.performance === 100
                ? record.performance.toString()
                : record.performance.toFixed(2)
              : '-'
        }

        measureDetailResult.push({
          date: item,
          fullDate: period.startDate.format('MM/DD/YYYY'),
          rate: measure.NullRate ? '-' : rate,
          goal: '-',
          benchmark: '-',
          population: measure.NullIPP ? '-' : (record.ipp?.toString() ?? '-'),
          inDenominatorOnly: measure.NullDenomOnly
            ? '-'
            : (record.inDenominatorOnly?.toString() ?? '-'),
          denominatorExclusion: measure.NullDenExcl
            ? '-'
            : (record.denominatorExclusion?.toString() ?? '-'),
          numerator: measure.NullNumerator
            ? '-'
            : (record.numerator?.toString() ?? '-'),
          denominator: measure.NullDenominator
            ? '-'
            : (record.denominator?.toString() ?? '-'),
          exception: measure.NullException
            ? '-'
            : (record.denominatorException?.toString() ?? '-'),
          endDate: period.endDate?.format('MM/DD/YYYY'),
          noDrill: measure.NoDrill ?? false,
          isPatientLevelAccessAvailable: false, // Replace with actual value
        })
      }
    } else {
      measureDetailResult.push({
        date: item,
        numerator: '-',
        fullDate: period.startDate.format('MM/DD/YYYY'),
        denominator: '-',
        rate: '-',
        goal: '-',
        benchmark: '-',
        denominatorExclusion: '-',
        exception: '-',
        inDenominatorOnly: '-',
        population: '-',
        endDate: period.endDate?.format('MM/DD/YYYY'),
        noDrill: measure.NoDrill ?? false,
        isPatientLevelAccessAvailable: false, // Replace with actual value
      })
    }
  }

  console.log(
    'MeasureResultDetailsQueryHandler::Handle:: Completed',
    new Date().toString()
  )

  // redisHelper.set(
  //   cacheKey,
  //   JSON.stringify(measureDetailResult),
  //   Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
  // )

  return measureDetailResult
}
