import { getMeasureTitle } from "@/lib/getMeasureTitle"
import { SnowflakeRepository } from "@/services/snowflake/SnowflakeRepository"
import { SnowflakeActiveMeasure } from "@/types/activeMeasure"
import { AvailableMeasure, Measure } from "@/types/measure"
import { type Connection, type Pool } from 'snowflake-sdk'

export class SnowflakeAvailableMeasuresRepository extends SnowflakeRepository<SnowflakeActiveMeasure> {
  constructor(connectionPool: Pool<Connection>, tableName: string) {
    super(connectionPool, tableName, 'Id')
  }

  findAvailableMeasures = async (
    mapleMeasures: Measure[]
  ): Promise<AvailableMeasure[]> => {

    const mapleMeasuresMap = new Map<string, Measure>()
    mapleMeasures.forEach(measure => {
      mapleMeasuresMap.set(measure.measureIdentifier, measure)
    })

    const sql = `
      WITH ActiveMeasures AS (
        SELECT 
          AM."MeasureGUID",
          <PERSON>."SourceContainerIdentifier"
        FROM MECA."ActiveMeasures" AM
        JOIN MECA."Entities" E ON AM."EntitiesId" = E."Id"
      ),
      MecaMeasures AS (
        SELECT 
          M.*,
          MS."MeasureGUID"
        FROM MECA."Measures" M
        JOIN MECA."MeasureSummary" MS ON M."MedisolvMeasureId" = MS."MeasureGUID"
        QUALIFY ROW_NUMBER() OVER (PARTITION BY M."MedisolvMeasureId" ORDER BY M."Id") = 1
      )
      SELECT 
        MM."MedisolvMeasureId" AS "MeasureIdentifier",
        MM."Name" AS "MeasureName",
        NULL AS "MeasureSubId",
        NULL AS "Strata",
        MM."DenominatorQualifyingType" AS "DenominatorQualifyingType",
        AM."SourceContainerIdentifier" AS "SourceContainerIdentifier",
        MM."SmallestInterval" AS "SmallestInterval"
      FROM MecaMeasures MM
      LEFT JOIN ActiveMeasures AM ON MM."MedisolvMeasureId" = AM."MeasureGUID"
      WHERE MM."MedisolvMeasureId" IS NOT NULL
      ORDER BY MM."MedisolvMeasureId", AM."SourceContainerIdentifier"
    `

    const results = await this.execute(sql)

        if (!results) return []


    const AvailableMeasure = results?.map((row: any) => {
      return {
        measureIdentifier: row.MeasureIdentifier,
        measureName: row.MeasureName,
        measureSubId: row.MeasureSubId,
        strata: row.Strata,
        denominatorQualifyingType: row.DenominatorQualifyingType,
        sourceContainerIdentifier: row.SourceContainerIdentifier,
        smallestInterval: row.SmallestInterval,
      } as AvailableMeasure
    }) || []
    

    const processedResults: AvailableMeasure[] = []
    const processedMeasures = new Map<string, boolean>()

    for (const item of AvailableMeasure) {
      if (!item.measureIdentifier) continue

      const mapleMeasure = mapleMeasuresMap.get(item.measureIdentifier)
      
      if (mapleMeasure) {
        item.measureName = getMeasureTitle(mapleMeasure)
        item.measureSubId = mapleMeasure.subId
        item.strata = mapleMeasure.strata
        item.applicationName = mapleMeasure.applicationName
      }

      if (!item.sourceContainerIdentifier) {
        if (!processedMeasures.has(item.measureIdentifier)) {
          processedMeasures.set(item.measureIdentifier, true)
          processedResults.push(item)
        }
      } else {
        processedResults.push(item)
      }
    }

    return processedResults
  }
}