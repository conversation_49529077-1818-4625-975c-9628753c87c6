import MeasureResultsService from '../measureResults'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import CitCOrganizationService from '../citc/citCOrganizations'
import { redisHelper } from '@/lib/redis'
import { env } from '@/env'

export const getDefaultEntities = async (
  organizationId: string,
  isPartner: boolean,
  accessToken: string,
  measureTypes: PrimaryMeasureTypeConstants[],
  measureResultsService: MeasureResultsService,
): Promise<{ [key: string]: string | undefined }> => {
  let cacheKey = `${organizationId}_default_entities`

  let cachedResult = await redisHelper.get(cacheKey)

  if (cachedResult)
    return JSON.parse(cachedResult) as { [key: string]: string | undefined }

  let defaultEntities: { [key: string]: string | undefined } = {}

  const entities = await measureResultsService.getEntities({
    organizationId,
    isPartner,
  })

  for (const measureType of measureTypes) {
    if (isPartner) {
      const entityType =
        measureType === PrimaryMeasureTypeConstants.HospitalMeasures ||
          measureType === PrimaryMeasureTypeConstants.AbstractedMeasures
          ? EntityTypeConstants.Facility
          : EntityTypeConstants.RenderingProvider

      defaultEntities[measureType] = entities.find(
        (x) =>
          x.organizationTypeCode ===
          EntityOrganizationTypeConstants.PatnerRollUpLevel &&
          x.entityName == entityType
      )?.id
    } else {
      if (measureType === PrimaryMeasureTypeConstants.HospitalMeasures) {
        const organizationService = new CitCOrganizationService(accessToken)
        const subOrganizations =
          await organizationService.getSubOrganizationsByOrganizationId(
            organizationId
          )

        defaultEntities[measureType] = entities.find(
          (x) =>
            x.sourceContainerIdentifier.replace('_EH', '') === organizationId &&
            x.organizationTypeCode ===
            (subOrganizations.length === 1
              ? EntityOrganizationTypeConstants.HospitalLevel
              : EntityOrganizationTypeConstants.CombinedGroupLevel)
        )?.id
      } else {
        const defaultEntity = entities.find(
          (x) =>
            x.sourceContainerIdentifier.replace('_EC', '') === organizationId &&
            x.organizationTypeCode ===
            EntityOrganizationTypeConstants.CombinedGroupLevel
        )

        if (defaultEntity) defaultEntities[measureType] = defaultEntity.id
        else
          defaultEntities[measureType] = entities.find(
            (x) =>
              x.sourceContainerIdentifier.replace('_EC', '') ===
              organizationId &&
              x.organizationTypeCode ===
              EntityOrganizationTypeConstants.SubmissionGroupLevel
          )?.id
      }
    }
  }

  await redisHelper.set(
    cacheKey,
    JSON.stringify(defaultEntities),
    Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
  )

  return defaultEntities
}
