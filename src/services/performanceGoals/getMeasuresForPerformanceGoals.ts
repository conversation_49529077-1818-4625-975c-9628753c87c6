import { config } from '@/config'
import { activeMeasuresQuery } from '@/prisma/activeMeasuresQuery'
import { PerformanceGoalMeasure } from '@/types/scorecards/performanceGoalMeasure'
import { mapleMeasureQuery } from '../maple/mapleMeasuresQuery'
import { getMeasureTitle } from '@/lib/getMeasureTitle'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { getDefaultEntities } from './getDefaultEntities'
import MeasureResultsService from '@/services/measureResults'
import MapleMeasuresService from '@/services/mapleMeasures'

export const getMeasuresForPerformanceGoals = async (
  organizationId: string,
  measureResultsService: MeasureResultsService,
  isPartner: boolean,
  accessToken: string
): Promise<PerformanceGoalMeasure[]> => {
  const mapleMeasuresService = new MapleMeasuresService(
    accessToken,
    measureResultsService
  )

  const activeMeasures = await activeMeasuresQuery(
    measureResultsService,
    mapleMeasuresService,
    organizationId
  )

  const activeMeasureIds = activeMeasures.map((x) => x.measureIdentifier)

  const excludedMeasures = config.customMeasures.excludedMeasuresList

  const mapleMeasures = await mapleMeasureQuery(
    mapleMeasuresService,
    organizationId
  )

  const defaultEntities = await getDefaultEntities(
    organizationId,
    isPartner,
    accessToken,
    [
      ...new Set(
        mapleMeasures.map((x) =>
          x.applicationName === 'Hospital eMeasures' ||
          x.applicationName === 'Abstracted Measures'
            ? PrimaryMeasureTypeConstants.HospitalMeasures
            : PrimaryMeasureTypeConstants.AmbulatoryMeasures
        )
      ),
    ],
    measureResultsService
  )

  const requiredMeasures = mapleMeasures
    .filter((x) =>
      activeMeasureIds.some(
        (item) => item.toLowerCase() == x.measureIdentifier.toLowerCase()
      )
    )
    .filter(
      (x) =>
        !excludedMeasures.some(
          (item) => item.toLowerCase() == x.measureIdentifier.toLowerCase()
        )
    )
    .filter(
      (x, index, self) =>
        self.findIndex(
          (y) =>
            y.measureIdentifier.toLowerCase() ===
            x.measureIdentifier.toLowerCase()
        ) === index
    )
    .sort((a, b) => getMeasureTitle(a).localeCompare(getMeasureTitle(b)))
    .map(
      (x) =>
        ({
          measureIdentifier: activeMeasureIds.find(
            (value) => value.toLowerCase() == x.measureIdentifier.toLowerCase()
          ),
          measureName: getMeasureTitle(x),
          measureDescription: x.measureDescription,
          domainName: x.domainName,
          iNotationName: x.iNotationName,
          measureFriendlyName: x.measureFriendlyName,
          entityId:
            defaultEntities[
              x.applicationName === 'Hospital eMeasures' ||
              x.applicationName === 'Abstracted Measures'
                ? PrimaryMeasureTypeConstants.HospitalMeasures
                : PrimaryMeasureTypeConstants.AmbulatoryMeasures
            ],
          applicationName: x.applicationName,
        }) as PerformanceGoalMeasure
    )

  return requiredMeasures
}
