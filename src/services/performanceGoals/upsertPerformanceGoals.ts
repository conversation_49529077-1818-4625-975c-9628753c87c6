import { GuidGenerator } from '@/lib/utils'
import { PerformanceGoal } from '@/types/scorecards/performanceGoal'
import { StatusResponse } from '@/types/statusResponse'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import dayjs, { Dayjs } from 'dayjs'
import { StorageTables } from '@/enums/storageTables'

interface DateRange {
  startDate: Dayjs
  endDate: Dayjs
}

interface PerformanceGoalUpsertRequest {
  organizationId: string
  isPartner: boolean
  isEditProcess: boolean
  measureIdentifier: string
  entityId: string
  startDate: Date
  endDate: Date
  goalLower?: number
  goalUpper?: number
  benchmark?: number
  isYellowZoneFixedNumber?: boolean
  yellowZone?: number
  isExceptionalPerformanceNumber?: boolean
  exceptionalPerformance?: number
  userId: string
}

export const upsertPerformanceGoals = async (
  request: PerformanceGoalUpsertRequest
): Promise<StatusResponse> => {
  try {
    const tableStorageProvider = new AzureTableStorageWrapper(
      StorageTables.PerformanceGoals
    )

    const existingGoals =
      await tableStorageProvider.queryEntities<PerformanceGoal>(
        tableStorageProvider.generateODataQuery({
          PartitionKey: `${request.organizationId}`,
          measureIdentifier: `${request.measureIdentifier}`,
          entityId: `${request.entityId}`,
        })
      )

    const monthDiff = dayjs(request.endDate).diff(request.startDate, 'month')

    let goalForMonthExists = false
    if (monthDiff >= 0) {
      let monthCount = 0
      const periodList: DateRange[] = []
      const dateList: Dayjs[] = []

      let startDate = dayjs.utc(request.startDate)
      while (monthDiff >= monthCount) {
        periodList.push({
          startDate: startDate,
          endDate: startDate.add(1, 'month'),
        })

        dateList.push(startDate)
        startDate = startDate.add(1, 'month')
        monthCount += 1
      }

      goalForMonthExists =
        existingGoals.length > 0 &&
        existingGoals.filter((x) =>
          dateList.some((date) => dayjs.utc(x.startDate).isSame(date, 'date'))
        ).length > 0

      if (!goalForMonthExists) {
        for (const item of periodList) {
          request.startDate = item.startDate.toDate()
          request.endDate = item.endDate.toDate()
          await createGoals(0, request, tableStorageProvider)
        }
      } else {
        return {
          success: false,
          message: 'Goal for given month range already exists.',
        }
      }
    }
  } catch (e: any) {
    return { success: false, message: 'Some error occurred.' }
  }
  return { success: true, message: 'Goal set successfully' }
}

const createGoals = async (
  monthDiff: number,
  request: PerformanceGoalUpsertRequest,
  tableStorageProvider: AzureTableStorageWrapper
) => {
  let monthCount = 0

  while (monthDiff >= monthCount) {
    const performanceGoal: PerformanceGoal = {
      partitionKey: request.organizationId,
      rowKey: GuidGenerator.standard(),
      measureIdentifier: request.measureIdentifier,
      entityId: request.entityId,
      startDate: dayjs
        .utc(request.startDate)
        .add(monthCount, 'month')
        .toString(),
      endDate: dayjs
        .utc(request.startDate)
        .add(monthCount + 1, 'month')
        .toString(),
      goalLower: request.goalLower,
      goalUpper: request.goalUpper,
      benchmark: request.benchmark,
      isYellowZoneFixedNumber: request.isYellowZoneFixedNumber,
      yellowZone: request.yellowZone,
      isExceptionalPerformanceNumber: request.isExceptionalPerformanceNumber,
      exceptionalPerformance: request.exceptionalPerformance,
      lastUpdatedByUserId: request.userId,
      lastUpdatedDateTime: new Date(),
    }

    tableStorageProvider.insertEntity(performanceGoal)
    monthCount += 1
  }
}
