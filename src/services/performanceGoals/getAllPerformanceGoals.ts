import { MigrationConfig } from '@/types/migrationConfig'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { PerformanceGoal } from '@/types/scorecards/performanceGoal'
import { migrateData } from '../migration/migrateData'
import { ADMPrismaClient } from '@/server/db'
import { upsertPerformanceGoals } from './upsertPerformanceGoals'
import { SelectionType } from '@/enums/selectionType'
import PrismaADMClient from '../../../prisma/generated/adm'
import appInsights from '@/lib/applicationInsights'

export const getAllPerformanceGoals = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  organizationId: string,
  userId: string,
  measureIdentifier?: string,
  entityId?: string,
  migrationConfig?: MigrationConfig
) => {
  const query = tableStorageWrapper.generateODataQuery({
    PartitionKey: organizationId,
    ...(measureIdentifier
      ? { measureIdentifier: measureIdentifier.toLowerCase() }
      : {}),
    ...(entityId ? { entityId } : {}),
  })

  const existingPerformanceGoals =
    await tableStorageWrapper.queryEntities<PerformanceGoal>(query)

  if (migrationConfig && existingPerformanceGoals.length === 0) {
    // TODO: Enhancement* Add try/catch w/ app insights
    await migrateData({
      storage: tableStorageWrapper,
      migrationConfig,
      prismaClientType: 'admClient',
      prismaQuery: async (prismaClient: ADMPrismaClient) => {
        const whereClause: PrismaADMClient.Prisma.PerformanceGoalsWhereInput =
          {}

        if (measureIdentifier) {
          whereClause.MeasureIdentifier = measureIdentifier
        }

        if (entityId) {
          whereClause.EntitiesId = BigInt(entityId)
        }

        const performanceGoals = await prismaClient.performanceGoals.findMany({
          where: whereClause,
        })

        return performanceGoals
      },
      upsertFunction: async (storage, data) =>
        // TODO: Enhancement* Add try/catch w/ app insights
        await Promise.all(
          data.map((goal) =>
            upsertPerformanceGoals(storage, {
              organizationId: migrationConfig.organizationId,
              isPartner:
                migrationConfig.selectionType === SelectionType.Partner,
              isEditProcess: false,
              measureIdentifier: goal.MeasureIdentifier,
              entityId: goal.EntitiesId.toString(),
              startDate: goal.StartDate,
              endDate: goal.EndDate,
              goalLower:
                typeof goal.GoalLower === 'number'
                  ? goal.GoalLower
                  : (goal.GoalLower?.toNumber() ?? undefined),
              goalUpper:
                typeof goal.GoalUpper === 'number'
                  ? goal.GoalUpper
                  : (goal.GoalUpper?.toNumber() ?? undefined),
              benchmark:
                typeof goal.Benchmark === 'number'
                  ? goal.Benchmark
                  : (goal.Benchmark?.toNumber() ?? undefined),
              isYellowZoneFixedNumber:
                goal.IsYellowZoneFixedNumber ?? undefined,
              yellowZone:
                typeof goal.YellowZone === 'number'
                  ? goal.YellowZone
                  : (goal.YellowZone?.toNumber() ?? undefined),
              isExceptionalPerformanceNumber:
                goal.IsExceptionalPerformanceNumber ?? undefined,
              exceptionalPerformance:
                typeof goal.ExceptionalPerformance === 'number'
                  ? goal.ExceptionalPerformance
                  : (goal.ExceptionalPerformance?.toNumber() ?? undefined),
              userId,
            })
          )
        ),
    })
  }

  const results =
    existingPerformanceGoals.length > 0
      ? existingPerformanceGoals
      : tableStorageWrapper.queryEntities<PerformanceGoal>(query)

  appInsights.trackEvent({
    name: `getAllPerformanceGoals: Exit`,
    properties: {
      results,
    },
  })

  return results
}
