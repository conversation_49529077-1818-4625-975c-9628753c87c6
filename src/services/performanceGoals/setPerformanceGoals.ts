import {
  ScorecardResult,
  ScorecardResultByHospital,
} from '@/types/scorecards/scorecards'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { PerformanceGoal } from '@/types/scorecards/performanceGoal'
import { ScorecardDetails } from '@/types/scorecards/scorecardDetails'
import { INotation } from '@/enums/iNotation'
import { ScorecardView } from '@/enums/scorecardView'
import { Performance } from '@/enums/performance'
import dayjs, { Dayjs } from 'dayjs'
import { StorageTables } from '@/enums/storageTables'
import { getAllPerformanceGoals } from './getAllPerformanceGoals'
import { SelectionType } from '@/enums/selectionType'

export const setPerformanceGoals = async (
  organizationId: string,
  scorecardsResults: ScorecardResult[],
  scorecardView: ScorecardView,
  userId: string,
  selectionType: SelectionType
): Promise<ScorecardResult[] | ScorecardResultByHospital[]> => {
  const tableStorageProvider = new AzureTableStorageWrapper(
    StorageTables.PerformanceGoals
  )
  const performanceGoals = await getAllPerformanceGoals(
    tableStorageProvider,
    organizationId,
    userId,
    undefined,
    undefined,
    {
      organizationId,
      selectionType,
    }
  )

  for (const scorecardResult of scorecardsResults) {
    const requiredGoals = performanceGoals.filter(
      (x) =>
        x.measureIdentifier.toLowerCase() ===
        scorecardResult.measureIdentifier?.toLowerCase()
    )
    scorecardResult.scorecardDetailsList!.forEach((scorecard) => {
      if (scorecard.rate !== null && requiredGoals.length) {
        const { performance, goal } = GetPerformanceByGoal(
            requiredGoals,
            scorecard,
            scorecardResult.inotation!,
            scorecardView
        )

        scorecard.goal = goal
        scorecard.performance = performance
      } else {
        scorecard.goal = null
        scorecard.performance = Performance.NoData
      }
    })
  }

  return scorecardsResults
}

export const GetPerformanceByGoal = (
  performanceGoals: PerformanceGoal[],
  scorecardDetail: ScorecardDetails,
  iNotationName: INotation,
  scorecardView: ScorecardView
): { performance: Performance; goal: number | null } => {
  let performance: Performance = Performance.NoData
  let goalValue: null | number = null

  const goal = GetGoalForPeriod(
    dayjs(scorecardDetail.startDate!),
    dayjs(scorecardDetail.endDate!),
    performanceGoals,
    scorecardView
  )

  if (!goal) {
    return { performance: performance, goal: goalValue }
  }

  if (goal.goalLower) {
    goalValue = goal.goalLower!
    const performanceRange = {
      goalStart: goal.goalLower,
      goalEnd: goal.goalUpper,
      redZoneStart: 0,
      redZoneEnd: 0,
      yellowZoneStart: 0,
      yellowZoneEnd: 0,
      exceptionalZoneStart: 0,
      exceptionalZoneEnd: 0,
    }

    let yellowValue = (goal.yellowZone && !isNaN(goal.yellowZone))
        ? goal.yellowZone
        : undefined
    const isYellowZoneFixedNumber = goal.isYellowZoneFixedNumber ?? false
    const isExceptionValueFixedNumber =
      goal.isExceptionalPerformanceNumber ?? false

    if (!isYellowZoneFixedNumber) {
      yellowValue = goal.yellowZone
    }

    let exceptionalValue = (goal.exceptionalPerformance && !isNaN(goal.exceptionalPerformance))
        ? goal.exceptionalPerformance
        : undefined
    if (!isExceptionValueFixedNumber) {
      exceptionalValue = goal.exceptionalPerformance
    }

    switch (iNotationName) {
      case INotation.Higher:
        if (yellowValue != null) {
          performanceRange.redZoneStart = 0
          performanceRange.redZoneEnd = isYellowZoneFixedNumber
            ? (goal.yellowZone ?? goal.goalLower)
            : performanceRange.goalStart - yellowValue
          performanceRange.yellowZoneStart = isYellowZoneFixedNumber
            ? goal.yellowZone!
            : performanceRange.goalStart - yellowValue
          performanceRange.yellowZoneEnd = performanceRange.goalStart
        }

        if (exceptionalValue != null) {
          if (!isExceptionValueFixedNumber) {
            performanceRange.goalEnd =
              performanceRange.goalStart + exceptionalValue
            performanceRange.exceptionalZoneStart =
              performanceRange.goalStart + exceptionalValue
            performanceRange.exceptionalZoneEnd = Number.MAX_VALUE
          } else {
            performanceRange.goalEnd =
              goal.goalUpper != goal.goalLower
                ? goal.goalUpper!
                : goal.exceptionalPerformance
            performanceRange.exceptionalZoneStart = goal.exceptionalPerformance!
          }
        }

        if (
          exceptionalValue != null &&
          scorecardDetail.rate! >= performanceRange.exceptionalZoneStart
        ) {
          return { performance: Performance.Exceptional, goal: goalValue }
        } else if (scorecardDetail.rate! > performanceRange.goalEnd!) {
          return { performance: Performance.Good, goal: goalValue }
        }

        if (
          scorecardDetail.rate! >= performanceRange.goalStart &&
          scorecardDetail.rate! <= performanceRange.goalEnd!
        )
          return { performance: Performance.Good, goal: goalValue }
        else {
          if (yellowValue != null) {
            if (
              scorecardDetail.rate! >= performanceRange.yellowZoneStart &&
              scorecardDetail.rate! <= performanceRange.yellowZoneEnd
            )
              return { performance: Performance.Caution, goal: goalValue }
            else if (
              scorecardDetail.rate! >= performanceRange.redZoneStart &&
              scorecardDetail.rate! <= performanceRange.redZoneEnd
            )
              return { performance: Performance.Poor, goal: goalValue }
          } else {
            if (scorecardDetail.rate! < performanceRange.goalStart)
              return { performance: Performance.Poor, goal: goalValue }
          }
        }
        break

      case INotation.Lower:
        if (yellowValue != null) {
          performanceRange.redZoneStart = Number.MAX_VALUE
          performanceRange.redZoneEnd = isYellowZoneFixedNumber
            ? goal.yellowZone!
            : performanceRange.goalStart + yellowValue
          performanceRange.yellowZoneStart = isYellowZoneFixedNumber
            ? goal.yellowZone!
            : performanceRange.goalStart + yellowValue
          performanceRange.yellowZoneEnd = performanceRange.goalStart
        }

        if (exceptionalValue != null) {
          if (!isExceptionValueFixedNumber) {
            performanceRange.goalEnd =
              performanceRange.goalStart - exceptionalValue
            performanceRange.exceptionalZoneStart =
              performanceRange.goalStart - exceptionalValue
            performanceRange.exceptionalZoneEnd = Number.MIN_VALUE
          } else {
            performanceRange.goalEnd =
              goal.goalUpper != goal.goalLower
                ? (goal.goalUpper ?? 0)
                : goal.exceptionalPerformance!
            performanceRange.exceptionalZoneStart = goal.exceptionalPerformance!
          }
        }

        if (exceptionalValue != null) {
          if (scorecardDetail.rate! <= performanceRange.exceptionalZoneStart) {
            return { performance: Performance.Exceptional, goal: goalValue }
          }
        } else {
          if (scorecardDetail.rate! <= performanceRange.goalStart)
            return { performance: Performance.Good, goal: goalValue }
        }

        if (
          scorecardDetail.rate! <= performanceRange.goalStart &&
          scorecardDetail.rate! >= performanceRange.goalEnd!
        )
          return { performance: Performance.Good, goal: goalValue }

        if (yellowValue != null) {
          if (
            scorecardDetail.rate! <= performanceRange.yellowZoneStart &&
            scorecardDetail.rate! >= performanceRange.yellowZoneEnd
          )
            return { performance: Performance.Caution, goal: goalValue }
          if (
            scorecardDetail.rate! <= performanceRange.redZoneStart &&
            scorecardDetail.rate! >= performanceRange.redZoneEnd
          )
            return { performance: Performance.Poor, goal: goalValue }
        } else {
          if (scorecardDetail.rate! > performanceRange.goalStart)
            return { performance: Performance.Poor, goal: goalValue }
        }
        break
    }
  }
  return { performance: performance, goal: goalValue }
}

const GetGoalForPeriod = (
  startDate: Dayjs,
  endDate: Dayjs,
  goals: PerformanceGoal[],
  view: ScorecardView
): PerformanceGoal | null => {
  switch (view) {
    case ScorecardView.Monthly:
      const performance = goals.find(
        (m) =>
          dayjs(m.startDate).isSame(startDate, 'date') &&
          dayjs(m.endDate).isSame(endDate, 'date')
      )
      if (!performance) return null

      const goalStart = performance.goalLower
      const goalEnd = performance.goalUpper ?? performance.goalLower

      let yellowValue: number | undefined = undefined

      if (performance.yellowZone)
        yellowValue = performance.isYellowZoneFixedNumber
          ? performance.yellowZone
          : performance.goalLower! * (performance.yellowZone / 100)

      let exceptionalValue: number | undefined = undefined

      if (performance.exceptionalPerformance)
        exceptionalValue = performance.isExceptionalPerformanceNumber
          ? performance.exceptionalPerformance
          : performance.goalLower! * (performance.exceptionalPerformance / 100)

      const goalM: PerformanceGoal = {
        partitionKey: performance.partitionKey,
        rowKey: performance.rowKey,
        benchmark: performance.benchmark,
        goalLower: goalStart,
        goalUpper: goalEnd,
        isExceptionalPerformanceNumber:
          performance.isExceptionalPerformanceNumber,
        exceptionalPerformance: exceptionalValue,
        isYellowZoneFixedNumber: performance.isYellowZoneFixedNumber,
        yellowZone: yellowValue,
        startDate: performance.startDate,
        endDate: performance.endDate,
        entityId: performance.entityId,
        measureIdentifier: performance.measureIdentifier,
      }
      return goalM

    case ScorecardView.Quarterly:
    case ScorecardView.Yearly:
      const dates = GetDateRanges(startDate, endDate)
      const goalavailables: PerformanceGoal[] = []

      for (const item of dates) {
        const measureGoal = goals.find(
          (m) =>
            dayjs(m.startDate).isSame(item.startDate, 'date') &&
            dayjs(m.endDate).isSame(item.endDate, 'date')
        )
        if (!measureGoal) continue

        const goalStart = measureGoal.goalLower
        const goalEnd = measureGoal.goalUpper ?? measureGoal.goalLower

        let yellowVal: number | undefined = undefined

        if (measureGoal.yellowZone)
          yellowVal = measureGoal.isYellowZoneFixedNumber
            ? measureGoal.yellowZone
            : measureGoal.goalLower! * (measureGoal.yellowZone / 100)

        let exceptionalVal: number | undefined = undefined

        if (measureGoal.exceptionalPerformance)
          exceptionalVal = measureGoal.isExceptionalPerformanceNumber
            ? measureGoal.exceptionalPerformance
            : measureGoal.goalLower! *
              (measureGoal.exceptionalPerformance / 100)

        goalavailables.push({
          partitionKey: measureGoal.partitionKey,
          rowKey: measureGoal.rowKey,
          benchmark: measureGoal.benchmark,
          goalLower: goalStart,
          goalUpper: goalEnd,
          isExceptionalPerformanceNumber:
            measureGoal.isExceptionalPerformanceNumber,
          exceptionalPerformance: exceptionalVal,
          isYellowZoneFixedNumber: measureGoal.isYellowZoneFixedNumber,
          yellowZone: yellowVal,
          startDate: measureGoal.startDate,
          endDate: measureGoal.endDate,
          entityId: measureGoal.entityId,
          measureIdentifier: measureGoal.measureIdentifier,
        })
      }

      if (goalavailables.length == 0) return null

      const goalAverages = goalavailables.reduce((acc: any, curr) => {
        return {
          benchmark: (acc.benchmark || curr.benchmark)
              ? (acc.benchmark || 0) + (curr.benchmark || 0)
              : undefined,
          goalLower: (acc.goalLower || curr.goalLower)
              ? (acc.goalLower || 0) + (curr.goalLower || 0)
              : undefined,
          goalUpper: (acc.goalUpper || curr.goalUpper)
              ? (acc.goalUpper || 0) + (curr.goalUpper || 0)
              : undefined,
          yellowZone:  (acc.yellowZone || curr.yellowZone)
              ? (acc.yellowZone || 0) + (curr.yellowZone || 0)
              : undefined,
          exceptionalPerformance: (acc.exceptionalPerformance || curr.exceptionalPerformance)
              ? (acc.exceptionalPerformance || 0) + (curr.exceptionalPerformance || 0)
              : undefined
        }
      }, {})

      const goal: PerformanceGoal = {
        partitionKey: '',
        rowKey: '',
        goalLower: goalAverages.goalLower / goalavailables.length,
        goalUpper: goalAverages.goalUpper / goalavailables.length,
        benchmark: goalAverages.benchmark / goalavailables.length,
        isYellowZoneFixedNumber: goalavailables[0]!.isYellowZoneFixedNumber,
        isExceptionalPerformanceNumber:
          goalavailables[0]!.isExceptionalPerformanceNumber,
        yellowZone: goalAverages.yellowZone / goalavailables.length,
        exceptionalPerformance:
          goalAverages.exceptionalPerformance / goalavailables.length,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        entityId: goalavailables[0]!.entityId,
        measureIdentifier: goalavailables[0]!.measureIdentifier,
      }

      return goal

    default:
      return null
  }
}

interface DateRange {
  startDate: Dayjs
  endDate: Dayjs
}

const GetDateRanges = (startDate: Dayjs, endDate: Dayjs): DateRange[] => {
  const result: DateRange[] = []
  let currentDate = startDate

  while (currentDate < endDate) {
    result.push({
      startDate: currentDate,
      endDate: currentDate.add(1, 'month'),
    })
    currentDate = currentDate.add(1, 'month')
  }
  return result
}
