import { env } from '@/env'
import { Service } from './service'
import type { SimplifiedParameter } from '@/types/simplifiedParameter'
import type { SimplifiedParameterWithOrganizationId } from '@/types/SimplifiedParameterWithOrganizationId'
import { tryCache } from '@/lib/redis'
import appInsights from '@/lib/applicationInsights'

class CitCParametersService extends Service {

  constructor(accessToken: string) {
    super(accessToken)
  }

  async getOrganizationParametersAsync(
    organizationId: string
  ) {
    appInsights.trackEvent({
      name: 'executing getOrganizationParametersAsync',
      properties: {
        url: `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/Parameters/GetByOrganization/${organizationId}`,
        organizationId,
      },
    })

    const result = await tryCache(
      `getOrganizationParametersAsync.${organizationId}`,
      async () =>
        this.fetchData<SimplifiedParameter>(
          `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/Parameters/GetByOrganization/${organizationId}`
        )
    )

    appInsights.trackEvent({
      name: 'result of getOrganizationParametersAsync',
      properties: {
        result: { ...(await result) },
      },
    })

    return result
  }

  async getPartnerParametersAsync(
    organizationId: string
  ): Promise<SimplifiedParameter[]> {
    return tryCache(`getPartnerParametersAsync.${organizationId}`, async () =>
      this.fetchData<SimplifiedParameter>(
        `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/parameters/partner/${organizationId}`
      )
    )
  }

  async getGlobalParametersAsync(): Promise<SimplifiedParameter[]> {
    return tryCache(`getGlobalParametersAsync`, async () =>
        await this.fetchData<SimplifiedParameter>(
            `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/Parameters/Get`
        )
    )
  }

  async GetAllOrganizationParametersByKeyNameAsync(
    keyName: string
  ): Promise<SimplifiedParameterWithOrganizationId[]> {
    return await this.fetchData<SimplifiedParameterWithOrganizationId>(
      `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/parameters/parameter/${keyName}/organizations`
    )
  }

  private async fetchData<T>(url: string): Promise<T[]> {
    return (await this.fetchWithType<T[]>(url)).data!
  }
}

export default CitCParametersService
