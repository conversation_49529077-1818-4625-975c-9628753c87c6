import type { LoadProcedureStatusOptions } from '@/types/loadProcedureStatusOptions'
import type { ProcedureStatus } from '@/types/procedureStatus'
import { Service } from './service'
import { env } from '@/env'
import type { EncorELoadDataStatus } from '@/types/encorELoadDataStatus'
import type { SubmissionGroup } from '@/types/submissionGroup'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { ECMeasureResultOptions } from '@/types/eCMeasureResultOptions'
import { MeasureResultOptions } from '@/types/measureResultOptions'
import { MeasureResultDetailsQuery } from '@/types/measureResultDetailsQuery'
import { AggregatedMeasureDetails } from '@/types/aggregatedMeasureDetails'
import { getAmbulatoryMeasureResultDetails } from './measures/getAmbulatoryMeasureResultDetails'
import { getHospitalMeasureResultDetails } from './measures/getHospitalMeasureResultDetails'
import { AggregateResultsResponse } from '@/types/aggregatedResultsResponse'
import { GroupsByOrganizationByMeasureOptions } from '@/types/groupsByOrganizationByMeasureOptions'
import { ECAggregateResultsResponse } from '@/types/eCAggregateResultsResponse'
import { getEncorELoadStatus } from './measures/getEncorELoadStatus'
import { ADMMeasure } from '@/types/admMeasure'
import { GroupsByOrganizationOptions } from '@/types/groupsByOrganizationOptions'
import {
  MeasureResultByHospital,
  ProviderResult,
} from '@/types/calculatedMeasure'
import { filterEmptyIndicators } from '@/lib/filterEmptyIndicators'
import { hasLimitedAccessForOrganization } from '@/lib/hasLimitedAccessForOrganization'
import type { Dayjs } from 'dayjs'
import { ScorecardView } from '@/enums/scorecardView'
import { SelectionType } from '@/enums/selectionType'
import { OrganizationRole } from '@/types/organizationRole'
import { getProvidersBySubmissionGroupQuery } from './providers/getProvidersBySubmissionGroupQuery'
import { Provider } from '@/types/provider'
import { getMeasureResultByHospitalQuery } from './measures/getMeasureResultByHospitalQuery'
import { mapleMeasureQuery } from './maple/mapleMeasuresQuery'
import type { Organization } from '@/types/organization'
import { MeasureTypeByApplication } from '@/enums/measureTypeByApplication'
import { SubOrganization } from '@/types/subOrganization'
import { MeasureResultsByHospitalsModel } from '@/types/measureResultsByHospitalsModel'
import { getECMeasureResultBySubmissionGroupQuery } from './measures/getECMeasureResultBySubmissionGroupQuery'
import CitCOrganizationService from './citCOrganizations'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { Entity } from '@/types/entity'
import { getEntitiesQuery } from './entities/getEntitiesQuery'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { redisHelper, tryCache } from '@/lib/redis'
import { getPrimaryMeasureTypesQuery } from './measures/getPrimaryMeasureTypesQuery'
import { PrimaryMeasureTypesQuery } from '@/types/primaryMeasureTypesQuery'
import appInsights from '@/lib/applicationInsights'
import { Partner } from '@/types/partner'
import { PerformanceRatesQuery } from '@/types/performanceRatesQuery'
import { EntityByOrganizationIAndEntityTypeQuery } from '@/types/entityByOrganizationIAndEntityTypeQuery'
import { SnowflakeMeasureRepository } from '@/services/measures/SnowflakeMeasureRepository'
import { SnowflakeActiveMeasureRepository } from '@/services/measures/ActiveMeasureRepository'
import { ProvidersRepository } from '@/services/providers/ProvidersRepository'
import { ProcedureStatusRepository } from '@/services/procedureStatus/ProcedureStatusRepository'
import CitCParametersService from '@/services/citcParameters'
import { SnowflakeSubmissionGroupRepository } from './submissionGroups/SnowflakeSubmissionGroupRepository'
import { SnowflakeFacilityRepository } from './facilities/SnowflakeFacilityRepository'
import MapleMeasuresService from './mapleMeasures'
import { SisenseMeasureSummaryRepository } from '@/services/sisense/SisenseMeasureSummaryRepository'
import { EntitiesRepository } from '@/services/entities/EntitiesRepository'
import { SnowflakeMeasure } from '@/types/measure'
import { Group } from '@/types/group'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { MeasureResultDetails } from '@/types/measureResultDetails'
import { SnowflakeOrganizationTypeRepository } from './organizationType/SnowflakeOrganizationTypeRepository'
import { OrganizationType } from '@/types/organizationType'
import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from './azure/tableStorageWrapper'

/**
 * TODO
 * This file should be a bit more uniform. Returning data | error vs just error is
 * also a bit more useful than defaulting to .data ?? null | []
 */

type GroupsByProviderOptions = {
  organizationId: string
  isPartner: boolean
  providers: string[]
}

type EntitiesOptions = {
  organizationId: string
  isPartner: boolean
}

export type ProvidersByGroupOptions = {
  submissionGroupId: string
  organizationId: string
  isPartner: boolean
}

type ActiveProvidersByGroupOptions = {
  submissionGroupId: string
  organizationId: string
  isPartner: boolean
  startDate: Dayjs
  endDate: Dayjs
}

/**
 * Options for getting organization parameters
 */
export type OrganizationParametersOptions = {
  organizationId: string
  isPartner: boolean
}

export class MeasureResultsService extends Service {
  private readonly measureRepo: SnowflakeMeasureRepository
  private readonly activeMeasureRepository: SnowflakeActiveMeasureRepository
  private readonly providersRepository: ProvidersRepository
  private readonly procedureStatusRepository: ProcedureStatusRepository
  private readonly sisenseMeasureSummaryRepository: SisenseMeasureSummaryRepository
  private readonly entitiesRepository: EntitiesRepository
  private readonly submissionGroupRepository: SnowflakeSubmissionGroupRepository
  private readonly facilitiesRepository: SnowflakeFacilityRepository
  private readonly citcParameterService: CitCParametersService
  private readonly organizationTypeRepository: SnowflakeOrganizationTypeRepository
  constructor(
    accessToken: string,
    measureRepo: SnowflakeMeasureRepository,
    activeMeasureRepository: SnowflakeActiveMeasureRepository,
    providersRepository: ProvidersRepository,
    procedureStatusRepository: ProcedureStatusRepository,
    sisenseMeasureSummaryRepository: SisenseMeasureSummaryRepository,
    entitiesRepository: EntitiesRepository,
    submissionGroupRepo: SnowflakeSubmissionGroupRepository,
    facilitiesRepo: SnowflakeFacilityRepository,
    organizationTypeRepository: SnowflakeOrganizationTypeRepository,
    citcParameterService: CitCParametersService
  ) {
    super(accessToken)
    this.measureRepo = measureRepo
    this.activeMeasureRepository = activeMeasureRepository
    this.providersRepository = providersRepository
    this.procedureStatusRepository = procedureStatusRepository
    this.sisenseMeasureSummaryRepository = sisenseMeasureSummaryRepository
    this.entitiesRepository = entitiesRepository
    this.submissionGroupRepository = submissionGroupRepo
    this.facilitiesRepository = facilitiesRepo
    this.organizationTypeRepository = organizationTypeRepository
    this.citcParameterService = citcParameterService
  }

  async getFacilityByOrganization(organizationId: string) {
    return await tryCache(`${organizationId}_facilities_by_organization`, () =>
      this.facilitiesRepository.findFacilitiesByOrganization(organizationId)
    )
  }

  /**
   * Find providers by submission group using Snowflake
   * @param options The providers by group options
   * @returns A promise that resolves to an array of providers
   */
  async getProvidersBySubmissionGroup(
    options: ProvidersByGroupOptions
  ): Promise<Provider[]> {
    // Get organization parameters
    const { checkSourceContainerIdentifier, ecOverallGroupId } =
      await this.getOrganizationParameters({
        organizationId: options.organizationId,
        isPartner: options.isPartner,
      })

    // Get parameters to check for suffix
    const parameters = options.isPartner
      ? await this.citcParameterService.getPartnerParametersAsync(
          options.organizationId
        )
      : await this.citcParameterService.getOrganizationParametersAsync(
          options.organizationId
        )

    // Check if we need to add a suffix to the source container identifier
    let suffix = ''
    if (
      !options.isPartner &&
      parameters.some(
        (x) => x.key === 'platform:SuffixSourceContainerIdentifiers'
      )
    ) {
      const isSuffix = parameters.find(
        (x) => x.key === 'platform:SuffixSourceContainerIdentifiers'
      )?.value
      if (isSuffix && isSuffix.toLowerCase() === 'true') {
        suffix = '_EC'
      }
    }

    // Use the providers repository to find providers by submission group
    return this.providersRepository.findProvidersBySubmissionGroup(
      options.organizationId,
      options.submissionGroupId,
      checkSourceContainerIdentifier,
      ecOverallGroupId,
      options.isPartner,
      suffix
    )
  }

  /**
   * Find providers by submission group using Snowflake
   * @param providersRepo The providers repository
   * @param options The active providers by group options
   * @returns A promise that resolves to an array of providers
   */
  async getActiveProvidersBySubmissionGroup(
    options: ActiveProvidersByGroupOptions
  ): Promise<Provider[]> {
    // Get organization parameters
    const { checkSourceContainerIdentifier, ecOverallGroupId } =
      await this.getOrganizationParameters({
        organizationId: options.organizationId,
        isPartner: options.isPartner,
      })

    // Use the providers repository to find providers by submission group
    return this.providersRepository.findActiveProvidersBySubmissionGroup(
      options.organizationId,
      options.submissionGroupId,
      options.startDate,
      options.endDate,
      checkSourceContainerIdentifier,
      ecOverallGroupId,
      options.isPartner
    )
  }

  /**
   * Get procedure load status using the ProcedureRepository
   * @param options The load procedure status options
   * @returns A promise that resolves to a ProcedureStatus object or null
   */
  async getProcedureLoadStatus(options: LoadProcedureStatusOptions) {
    const cacheKey = `${options.organizationId}-${options.isPartner}-getProcedureLoadStatus`

    return await tryCache(
      cacheKey,
      async () => {
        // Get the most recent OAK2ADMLoad job
        const oakLoadJobs =
          await this.procedureStatusRepository.findByJobName('OAK2ADMLoad')
        const oakLoadJob = oakLoadJobs?.length! > 0 ? oakLoadJobs![0] : null

        // Check if there's a running "Precompile Reports" job
        const precompileJob =
          await this.procedureStatusRepository.isReportPrecompiling()

        // Map the repository results to the ProcedureStatus type
        if (oakLoadJob) {
          const response: ProcedureStatus = {
            jobId: Number(oakLoadJob.Id),
            jobName: oakLoadJob.JobName,
            applicationUserId: oakLoadJob.JobRunId || '',
            startDate: oakLoadJob.StartDateTime,
            endDate: oakLoadJob.EndDateTime || new Date(0),
            successStatus: oakLoadJob.SuccessStatus,
            procedureRunningStatus: precompileJob
              ? 'Report Precompile Running'
              : '',
          }

          return response
        }

        return null
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
  }

  async getSubmissionGroupsByOrganization(
    options: GroupsByOrganizationOptions
  ): Promise<SubmissionGroup[]> {
    return await tryCache(
      `${options.organizationId}_${options.isPartner}_submission_groups_by_organization`,
      async () =>
        await this.submissionGroupRepository.findSubmissionGroupsByOrganization(
          options.organizationId,
          options.isPartner
        )
    )
  }

  async getSubmissionGroupsByOrganizationByMeasureType(
    options: GroupsByOrganizationByMeasureOptions
  ) {
    const cacheKey = `${options.organizationId}-${options.isPartner}_submission_groups_by_organization_by_measure_type_${options.measureType}`

    return await tryCache(
      cacheKey,
      async () => {
        const mapleMeasuresService = new MapleMeasuresService(
          this.accessToken,
          this
        )
        const mapleMeasures = await mapleMeasuresService.getAllMeasures(
          options.organizationId
        )

        let filteredMeasureByTypesIds = mapleMeasures
          .filter((x) => {
            if (
              !options.measureType ||
              options.measureType.toLowerCase() === 'e'
            ) {
              return x.applicationName === 'Ambulatory eMeasures'
            } else if (options.measureType.toLowerCase() === 'q') {
              return x.applicationName === 'Registry Measures'
            }
            return false
          })
          .map((x) => x.measureIdentifier)

        return await this.submissionGroupRepository.findSubmissionGroupsByOrganizationAndMeasureType(
          options.organizationId,
          options.isPartner,
          filteredMeasureByTypesIds
        )
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
  }

  async getSubmissionGroupsByProviders(
    options: GroupsByProviderOptions
  ): Promise<SubmissionGroup[]> {
    return await tryCache(
      `${options.organizationId}_${options.isPartner}_submission_groups_by_providers_${options.providers.join(',')}`,
      async () =>
        await this.submissionGroupRepository.findSubmissionGroupsByOrganizationAndProviders(
          options.organizationId,
          options.isPartner,
          options.providers
        )
    )
  }

  async getEncorELoadStatus(
    organizationId: string,
    primaryMeasureType: PrimaryMeasureTypeConstants
  ): Promise<EncorELoadDataStatus> {
    const tableStorage = new AzureTableStorageWrapper(
      StorageTables.PlatformJobStatus
    )

    return getEncorELoadStatus(tableStorage, organizationId, primaryMeasureType)
  }

  async getHospitalMeasureResultDetails(
    request: MeasureResultDetailsQuery,
    citcOrganizationService: CitCOrganizationService
  ): Promise<AggregatedMeasureDetails[]> {
    return getHospitalMeasureResultDetails(
      this,
      citcOrganizationService,
      request
    )
  }

  async getAllMeasures(organizationId: string) {
    return await tryCache(`allMeasures.${organizationId}`, () =>
      this.measureRepo.findAll()
    )
  }

  async findAllMeasures(): Promise<any[]> {
    try {
      appInsights.trackEvent({
        name: 'executing findAllMeasures',
        properties: {},
      })

      const result = await this.measureRepo.findAll()

      appInsights.trackEvent({
        name: 'result of findAllMeasures',
        properties: { result },
      })

      return result || []
    } catch (error) {
      appInsights.trackEvent({
        name: 'error from findAllMeasures',
        properties: { error },
      })

      return []
    }
  }

  async findAllDetailedActiveMeasures() {
    try {
      appInsights.trackEvent({
        name: 'executing findAllDetailedActiveMeasures',
        properties: {},
      })

      const result =
        this.activeMeasureRepository.findAllDetailedActiveMeasures()

      appInsights.trackEvent({
        name: 'result of findAllDetailedActiveMeasures',
        properties: {
          result: {
            ...(await result),
          },
        },
      })

      return result
    } catch (error) {
      appInsights.trackEvent({
        name: 'error from findAllDetailedActiveMeasures',
        properties: { error },
      })

      return []
    }
  }

  async getMeasureByIdFromDb(
    measureId: string,
    organizationId: string
  ): Promise<SnowflakeMeasure | undefined> {
    const cacheKey = `${organizationId}-measure-${measureId}`
    const cachedData = await redisHelper.get(cacheKey)

    if (cachedData) {
      return JSON.parse(cachedData) as ADMMeasure
    }

    const measure = await this.measureRepo.findById(measureId)

    await redisHelper.set(
      cacheKey,
      JSON.stringify(measure),
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
    return measure
  }

  async getECMeasureResults(
    options: ECMeasureResultOptions
  ): Promise<ECAggregateResultsResponse | null> {
    const ecMeasureResultSummaryList = await tryCache(
      `ecMeasureResults.${JSON.stringify(options)}`,
      () =>
        this.measureRepo.calculateECMeasureResults(
          options.organizationId,
          options.isPartner,
          options.periodType,
          options.startDate,
          options.endDate,
          options.isSubmissionGroupLevel,
          options.submissionGroupId,
          options.measureIdentifier
        )
    )
    return {
      statusResponse: 'Success',
      ecMeasureResultSummaryList,
    }
  }

  async getMeasureResults(
    options: MeasureResultOptions
  ): Promise<AggregateResultsResponse | null> {
    const measureResultSummaryList = await tryCache(
      `measureResults.${JSON.stringify(options)}`,
      () =>
        this.measureRepo.calculateMeasureResults(
          options.organizationId,
          options.isPartner,
          options.periodType,
          options.startDate,
          options.endDate,
          options.isFacilityLevel || false,
          options.isCombinedGroup || false,
          options.subOrganizationId,
          options.measureIdentifier || undefined
        )
    )
    return {
      statusResponse: 'Success',
      measureResultSummaryList,
    }
  }

  async getPrimaryMeasureTypes(request: PrimaryMeasureTypesQuery) {
    const cachKey = `getPrimaryMeasureTypes-${JSON.stringify(request)}`
    return await tryCache(
      cachKey,
      () => {
        return getPrimaryMeasureTypesQuery(this.accessToken, request)
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
  }

  async getAmbulatoryMeasureResultDetails(
    uid: string,
    request: MeasureResultDetailsQuery
  ): Promise<AggregatedMeasureDetails[]> {
    console.log(
      'ECMeasureResultDetailsQueryHandler::Handle:: Started',
      new Date().toString()
    )

    return await getAmbulatoryMeasureResultDetails(
      uid,
      this,
      this.accessToken,
      request
    )
  }

  /**
   * Get entities with organization display names
   * @param options The entities options
   * @returns A promise that resolves to an array of entities
   */
  async getEntities(options: EntitiesOptions) {
    // Get organizations based on whether it's a partner or not
    let organizations: Organization[] = []

    if (options.isPartner) {
      // For partners, get all organizations from the entities table
      const sourceContainerIds =
        await this.entitiesRepository.findDistinctSourceContainerIdentifiers()

      // Get organization details for all source container IDs
      const citcOrganizationService = new CitCOrganizationService(
        this.accessToken
      )

      // Get organization details one by one since there's no bulk method
      for (const containerId of sourceContainerIds) {
        const org =
          await citcOrganizationService.getOrganizationDetailsAsync(containerId)
        if (org) {
          organizations.push(org)
        }
      }
    } else {
      // For a single organization, just get that organization's details
      const citcOrganizationService = new CitCOrganizationService(
        this.accessToken
      )
      const org = await citcOrganizationService.getOrganizationDetailsAsync(
        options.organizationId
      )
      if (org) {
        organizations = [org]
      }
    }

    const entities = await this.entitiesRepository.findAll()

    // Process entities to add display names
    return entities.map((entity) => {
      // Remove _EC and _EH suffixes from source container identifier
      const sourceId = entity.SourceContainerIdentifier.replace(
        '_EC',
        ''
      ).replace('_EH', '')

      // Find matching organization
      const matchingOrg = organizations.find(
        (org) => org.organizationId === sourceId
      )

      return {
        id: Number(entity.Id),
        code: entity.Code,
        entityName: entity.EntityName,
        entityDisplayName: matchingOrg
          ? `${entity.EntityName} (${matchingOrg.organizationName})`
          : entity.EntityName,
        entityTypeId: entity.EntityTypeId,
        entityTypeName: entity.EntityTypeName,
        organizationTypeId: entity.OrganizationTypeId,
        organizationTypeCode: entity.OrganizationTypeCode,
        sourceContainerIdentifier: entity.SourceContainerIdentifier,
      } as Entity
    })
  }
  async getMeasureResultsByHospital(request: MeasureResultsByHospitalsModel) {
    let subOrgs: string[] = request.entities

    const isPartner = request.selectionType === SelectionType.Partner

    let result: MeasureResultByHospital[] = []

    let organizations: Organization[] = []
    if (isPartner) {
      const partner = request.partners.find(
        (x) => x.id == request.organizationId
      )

      organizations = request.organizations
        ? partner?.organizations?.filter((x: Organization) =>
            request.organizations?.split(',').includes(x.organizationId!)
          )!
        : partner?.organizations!
    }

    if (
      request.primaryMeasureType ==
        PrimaryMeasureTypeConstants.AmbulatoryMeasures ||
      request.primaryMeasureType == PrimaryMeasureTypeConstants.RegistryMeasures
    ) {
      const mapleMeasuresService = new MapleMeasuresService(
        this.accessToken,
        this
      )

      const metadata = await mapleMeasureQuery(
        mapleMeasuresService,
        request.organizationId,
        [request.measureIdentifier]
      )

      const measureType =
        metadata[0]?.applicationName ===
        MeasureTypeByApplication.RegistryMeasures
          ? 'Q'
          : 'E'

      if (subOrgs.length === 0) {
        const submissiongroup =
          await this.getSubmissionGroupsByOrganizationByMeasureType({
            organizationId: request.organizationId,
            isPartner,
            measureType,
          })
        subOrgs = submissiongroup.map((x) => x.submissionGroupId)
      }

      if (request.providers && !request.providers.includes('*')) {
        const submissiongroup = await this.getSubmissionGroupsByProviders({
          organizationId: request.organizationId,
          isPartner,
          providers: request.providers,
        })

        const subOrgsUnmapped = subOrgs.includes('*')
          ? submissiongroup
          : submissiongroup.filter((x) => subOrgs.includes(x.submissionGroupId))

        subOrgs = subOrgsUnmapped.map((x) => x.submissionGroupId)
      }

      const query = await getECMeasureResultBySubmissionGroupQuery(
        this.accessToken,
        this,
        {
          measureIdentifier: request.measureIdentifier,
          organizationId: request.organizationId,
          scorecardView: request.selectedSpan,
          startDate: request.startDate.startOf('month'),
          endDate: request.endDate.endOf('month'),
          entites: subOrgs,
          isPartner: request.selectionType == SelectionType.Partner,
          measureType: measureType,
          userId: request.userId,
          hasLimitedAccess: hasLimitedAccessForOrganization(
            request.orgRoles,
            request.organizationId
          ),
          organizationList: organizations,
          expansionConfiguration: request.expansionConfiguration,
          partners: request.partners,
        }
      )

      result = query
    } else {
      let selectedSubOrganizations: SubOrganization[] = []
      if (request.useSpecialEntityStructure) {
        const entities = await getEntitiesQuery(this, {
          organizationId: request.organizationId,
          isPartner: isPartner,
        })
        const type3Entities = entities.filter(
          (x) =>
            x.organizationTypeCode ==
              EntityOrganizationTypeConstants.HospitalLevel &&
            x.entityTypeName == EntityTypeConstants.Facility
        )
        selectedSubOrganizations = type3Entities.map(
          (x) =>
            ({
              subOrganizationId: x.code,
              subOrganizationName: x.entityName,
            }) as SubOrganization
        )

        if (subOrgs.length > 0 && !subOrgs.includes('*')) {
          selectedSubOrganizations = selectedSubOrganizations.filter((x) =>
            subOrgs.includes(x.subOrganizationId!)
          )
        }
      } else {
        const citcOrganizationService = new CitCOrganizationService(
          this.accessToken
        )

        let subOrganizations = request.subOrganizations
        if (subOrganizations === null || subOrganizations.length == 0) {
          //Organization level access
          subOrganizations =
            await citcOrganizationService.getSubOrganizationsByOrganizationId(
              request.organizationId
            )
        }

        selectedSubOrganizations = subOrganizations

        if (subOrgs.length > 0 && !subOrgs.includes('*')) {
          const groups =
            await citcOrganizationService.getGroupsByOrganizationIdAsync({
              organizationId: request.organizationId,
            })
          if (groups.some((x) => subOrgs.includes(x.groupId))) {
            selectedSubOrganizations = groups.find(
              (x) => x.groupId == subOrgs[0]
            )?.subOrganizations!
          } else
            selectedSubOrganizations = subOrganizations.filter((x) =>
              subOrgs.includes(x.subOrganizationId!)
            )
        }
      }

      result = await getMeasureResultByHospitalQuery(this.accessToken, this, {
        measureIdentifier: request.measureIdentifier,
        organizationId: request.organizationId,
        scorecardView: request.selectedSpan,
        startDate: request.startDate.startOf('month'),
        endDate: request.endDate.endOf('month'),
        subOrganization: selectedSubOrganizations,
        isPartner,
        useSpecialEntityStructure: request.useSpecialEntityStructure,
        organizationList: organizations,
        expansionConfiguration: request.expansionConfiguration,
        partners: request.partners,
      })
    }

    if (request.hideEmptyIndicators) result = filterEmptyIndicators(result)

    // Sort results by hospital name
    result.sort((a, b) => {
      // First sort by hospital name
      if (a.hospital && b.hospital) {
        return a.hospital.localeCompare(b.hospital)
      }
      // If names are missing, use hospital ID as fallback
      return (a.hospitalId || '').localeCompare(b.hospitalId || '')
    })

    const messageAndProperties = {
      message: 'getMeasureResultsByHospital',
      properties: {
        request,
        result,
      },
    }

    console.log(messageAndProperties)
    appInsights.trackTrace(messageAndProperties)

    return result
  }

  async getProviderResults(
    organizationId: string,
    orgRoles: OrganizationRole[],
    selectionType: SelectionType,
    scorecardView: ScorecardView,
    measureIdentifier: string,
    startDate: Dayjs,
    endDate: Dayjs,
    entityId: string,
    hideEmptyIndicators: boolean,
    userId: string,
    providers: string[],
    partners: Partner[]
  ) {
    let results: ProviderResult[] = []

    const cacheKey = `get-provider-results-${providers || ''}-${organizationId || ''}-${scorecardView || ''}-${measureIdentifier || ''}-${entityId || ''}-${hideEmptyIndicators || ''}-${startDate.valueOf() || ''}-${endDate.valueOf() || ''}}`
    const cachedData = await redisHelper.get(cacheKey)

    if (cachedData && JSON.parse(cachedData).length > 0) {
      console.log(
        `Cache Hit for provider results: ${cachedData.substring(0, Math.min(100, cachedData.length))}`
      )
      return JSON.parse(cachedData)
    }

    if (
      !cachedData ||
      JSON.parse(cachedData).length == 0 ||
      hasLimitedAccessForOrganization(orgRoles, organizationId)
    ) {
      const partner = partners.find((x) =>
        x.organizations?.some((org) => org.organizationId === entityId)
      )

      //In case of entity is a rollup entity
      if (partner) {
        results = await getProvidersBySubmissionGroupQuery(
          this.accessToken,
          this,
          {
            organizationId: entityId,
            submissionGroupId: '*',
            startDate,
            endDate,
            measureIdentifier,
            scorecardView,
            isPartner: false,
            userId,
            hasLimitedAccess: hasLimitedAccessForOrganization(
              orgRoles,
              organizationId
            ),
          }
        )
      } else {
        results = await getProvidersBySubmissionGroupQuery(
          this.accessToken,
          this,
          {
            organizationId,
            submissionGroupId: entityId,
            startDate,
            endDate,
            measureIdentifier,
            scorecardView,
            isPartner: selectionType === SelectionType.Partner,
            userId,
            hasLimitedAccess: hasLimitedAccessForOrganization(
              orgRoles,
              organizationId
            ),
          }
        )
      }
    }

    if (providers && !providers.includes('*')) {
      const providerArr = providers
      results = results.filter((result) =>
        providerArr.some((provider) => provider === result.npi)
      )
    }

    if (hideEmptyIndicators) {
      results = filterEmptyIndicators(results)
    }

    // Sort results by providerName name
    results.sort((a, b) => {
      // First sort by providerName name
      if (a.providerName && b.providerName) {
        return a.providerName.localeCompare(b.providerName)
      }
      // If names are missing, use hospital ID as fallback
      return (a.friendlyName || '').localeCompare(b.friendlyName || '')
    })

    await redisHelper.set(
      cacheKey,
      JSON.stringify(results),
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
    return results
  }

  /**
   * Get performance rates by date and measure for hospital using SisenseMeasSummRepository
   * @param request The performance rates query
   * @returns A promise that resolves to an array of performance rate results
   */
  async getPerformanceRatesByDateAndMeasureForHospital(
    request: PerformanceRatesQuery
  ) {
    // Use the SisenseMeasSummRepository to get performance rates
    return this.sisenseMeasureSummaryRepository.findPerformanceRatesByDateAndMeasureForHospital(
      request.entityId,
      request.medisolvMeasureId,
      request.period,
      request.dateStart,
      request.dateEnd
    )
  }

  /**
   * Get entities for organization by ID and entity type ID using EntitiesRepository
   * @param request The entity by organization and entity type query
   * @returns A promise that resolves to an array of entities
   */
  async getEntitiesForOrganizationByIdAndEntityTypeId(
    request: EntityByOrganizationIAndEntityTypeQuery
  ) {
    return this.entitiesRepository.findByOrganizationIdAndEntityTypeId(
      request.organizationId,
      request.entityType
    )
  }

  /**
   * Get organization parameters
   * @param options The organization parameters options
   * @returns A promise that resolves to an object with checkSourceContainerIdentifier and ecOverallGroupId
   */
  async getOrganizationParameters(
    options: OrganizationParametersOptions
  ): Promise<{
    checkSourceContainerIdentifier: boolean
    ecOverallGroupId: string
  }> {
    // Import CitCParametersService
    const CitCParametersService = (await import('./citcParameters')).default
    const citcParameterService = new CitCParametersService(this.accessToken)

    let checkSourceContainerIdentifier = false
    let ecOverallGroupId = ''

    // Get parameters based on whether it's a partner or organization
    const parameters = options.isPartner
      ? await citcParameterService.getPartnerParametersAsync(
          options.organizationId
        )
      : await citcParameterService.getOrganizationParametersAsync(
          options.organizationId
        )

    // Check for SourceContainerIdentifier parameter
    if (
      parameters.some((x) => x.key === 'platform:SourceContainerIdentifier')
    ) {
      checkSourceContainerIdentifier = Boolean(
        parameters.find((x) => x.key === 'platform:SourceContainerIdentifier')
          ?.value
      )
    }

    // Check for EcOverallGroupId parameter
    if (!parameters.some((x) => x.key === 'platform:EcOverallGroupId')) {
      throw new Error(
        `No EcOverallGroupId parameter found for organization ${options.organizationId}`
      )
    }

    // Get EcOverallGroupId value
    ecOverallGroupId =
      parameters.find((x) => x.key === 'platform:EcOverallGroupId')?.value || ''

    return { checkSourceContainerIdentifier, ecOverallGroupId }
  }

  async getEHPatientDetails(
    organizationId: string,
    isPartner: boolean,
    startDate: Dayjs,
    endDate: Dayjs,
    measureIdentifier: string,
    submissionGroupIds: string[],
    subOrganizationIdsByOrganization: string[],
    groups: Group[],
    parameters: SimplifiedParameter[]
  ): Promise<MeasureResultDetails[]> {
    return await this.measureRepo.getEHPatientDetails(
      organizationId,
      isPartner,
      startDate,
      endDate,
      measureIdentifier,
      submissionGroupIds,
      parameters,
      subOrganizationIdsByOrganization,
      groups
    )
  }

  async getECPatientDetails(
    organizationId: string,
    isPartner: boolean,
    startDate: Dayjs,
    endDate: Dayjs,
    measureIdentifier: string,
    entities: Entity[],
    entityCodes: string[],
    activeSubmissionGroupIds: number[],
    parameters: SimplifiedParameter[]
  ): Promise<MeasureResultDetails[]> {
    return await this.measureRepo.getECPatientDetails(
      organizationId,
      isPartner,
      startDate,
      endDate,
      measureIdentifier,
      entities,
      entityCodes,
      activeSubmissionGroupIds,
      parameters
    )
  }

  async findAllOrganizationTypes(): Promise<OrganizationType[]> {
    return (await this.organizationTypeRepository.findAll()) || []
  }
  async findOrganizationTypesByEntityTypes() {
    return await this.organizationTypeRepository.findOrganizationTypesByEntityTypes()
  }
}

export default MeasureResultsService
