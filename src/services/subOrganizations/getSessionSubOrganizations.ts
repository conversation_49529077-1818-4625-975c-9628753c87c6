import { SelectionType } from '@/enums/selectionType'
import type { SubOrganization } from '@/types/subOrganization'
import { getPartnersByUserCommand } from '../partners/getPartnersByUserCommand'
import type { OrganizationRole } from '@/types/organizationRole'
import { getSubOrganizationsQuery } from './getSubOrganizationsQuery'

export const getSessionSubOrganizations = async (
  selectionType: SelectionType,
  accessToken: string,
  organizationId: string,
  organizationRoles: OrganizationRole[],
  uid: string
) => {
  const subOrganizations: SubOrganization[] = []

  if (selectionType === SelectionType.Organization) {
    const currentOrgRoles = organizationRoles?.find((c) =>
      c.organizationId?.includes(organizationId)
    )

    const claimSubOrgs = currentOrgRoles?.subOrganizationRoles?.map(
      (x) => x.subOrganizationId
    )
    const result = await getSubOrganizationsQuery(accessToken, organizationId)

    if (claimSubOrgs === null || claimSubOrgs?.length == 0) return null //Organization level access

    for (const subOrg in claimSubOrgs) {
      subOrganizations.push(result.find((x) => x.subOrganizationId === subOrg)!)
    }
  } else {
    const partnerList = await getPartnersByUserCommand(uid, accessToken)
    const partnersSubOrgs = partnerList
      ?.find((x) => x.id === organizationId)
      ?.organizations?.flatMap((org) => org.subOrganizations)

    if (partnersSubOrgs)
      subOrganizations.push(
        ...partnersSubOrgs.filter((org): org is SubOrganization => org != null)
      )
  }

  return subOrganizations
}
