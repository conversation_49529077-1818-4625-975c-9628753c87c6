import { env } from '@/env'
import {
  BlobServiceClient,
  type ContainerClient,
  type BlockBlobClient,
} from '@azure/storage-blob'
import * as fs from 'fs'

export class StorageService {
  private blobServiceClient: BlobServiceClient

  constructor() {
    this.blobServiceClient = BlobServiceClient.fromConnectionString(
      env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING
    )
  }

  getBlobClient(): BlobServiceClient {
    return this.blobServiceClient
  }

  async readBlobAsync(
    containerName: string,
    blobIdentifier: string
  ): Promise<Uint8Array> {
    // var container = cloudBlobClient.GetContainerReference(containerName);
    // await container.CreateIfNotExistsAsync();
    // var blobReference = container.GetBlockBlobReference(blobIdentifier);
    // using (var memoryStream = new MemoryStream())
    // {
    //     await blobReference.DownloadToStreamAsync(memoryStream);
    //     var blobContent = memoryStream.ToArray();
    //     return blobContent;
    // }

    const containerClient: ContainerClient =
      this.blobServiceClient.getContainerClient(containerName)
    await containerClient.createIfNotExists()

    const blobClient: BlockBlobClient =
      containerClient.getBlockBlobClient(blobIdentifier)
    const downloadBlockBlobResponse = await blobClient.download(0)
    const downloaded = await this.streamToBuffer(
      downloadBlockBlobResponse.readableStreamBody!
    )

    return downloaded
  }

  async writeBlobAsync(
    containerName: string,
    blobIdentifier: string,
    blobContent: Uint8Array
  ): Promise<boolean> {
    try {
      const containerClient =
        this.blobServiceClient.getContainerClient(containerName)
      await containerClient.createIfNotExists()

      const blockBlobClient = containerClient.getBlockBlobClient(blobIdentifier)
      await blockBlobClient.uploadData(blobContent)
      return true
    } catch (error) {
      console.error('Error writing blob: ', error)
      return false
    }
  }

  async uploadFileAsync(
    containerName: string,
    fileName: string,
    prefix: string
  ): Promise<boolean> {
    if (!fs.existsSync(fileName)) {
      return false
    }

    const containerClient =
      this.blobServiceClient.getContainerClient(containerName)
    await containerClient.createIfNotExists()

    const blockBlobClient = containerClient.getBlockBlobClient(
      `${prefix}/${fileName}`
    )
    const fileStream = fs.createReadStream(fileName)

    await blockBlobClient.uploadStream(fileStream)
    return true
  }

  async getBlobsList(containerName: string): Promise<string[]> {
    const containerClient =
      this.blobServiceClient.getContainerClient(containerName)
    const result: string[] = []

    for await (const blob of containerClient.listBlobsFlat()) {
      result.push(blob.name)
    }

    return result
  }

  async getFolderList(containerName: string): Promise<string[]> {
    const containerClient =
      this.blobServiceClient.getContainerClient(containerName)
    const result: string[] = []

    for await (const blob of containerClient.listBlobsByHierarchy('/')) {
      result.push(blob.name)
    }

    return result
  }

  async getContainerList(): Promise<string[]> {
    const result: string[] = []
    const containers = this.blobServiceClient.listContainers()

    for await (const container of containers) {
      result.push(container.name)
    }

    return result
  }

  async checkAndCreateContainerAsync(containerName: string): Promise<boolean> {
    const containerClient =
      this.blobServiceClient.getContainerClient(containerName)
    await containerClient.createIfNotExists()
    return true
  }

  // Helper function to convert a readable stream to buffer
  private async streamToBuffer(
    readableStream: NodeJS.ReadableStream
  ): Promise<Uint8Array> {
    const chunks: Uint8Array[] = []
    return new Promise((resolve, reject) => {
      readableStream.on('data', (data: Uint8Array) => {
        chunks.push(data)
      })
      readableStream.on('end', () => {
        resolve(Buffer.concat(chunks))
      })
      readableStream.on('error', reject)
    })
  }
}

export default StorageService
