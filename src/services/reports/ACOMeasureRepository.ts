import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'

/**
 * Interface representing a measure
 */
export interface Measure {
  MeasureName: string
  MeasureId: string
  MeasureIdentifier: string
}

/**
 * Repository for accessing measure data from Snowflake
 */
export class ACOMeasureRepository extends SnowflakeRepository<Measure> {
  constructor(connectionPool: any, tableName: string = 'MPRE.SisenseMeasures') {
    // Use a dummy identity field since we'll be using custom SQL queries
    super(connectionPool, tableName, 'Id')
  }

  /**
   * Fetches all available measures for ACO Performance Analysis
   *
   * @param codeNumeric - The organization type code
   * @param intervalType - The interval type (M: Monthly, Q: Quarterly, Y: Yearly)
   * @param startDate - The start date
   * @param endDate - The end date
   * @returns Promise resolving to an array of Measure objects or undefined
   */
  async findACOMeasures(
    codeNumeric: string,
    intervalType: string,
    startDate: Date,
    endDate: Date
  ): Promise<Measure[] | undefined> {
    // Build the SQL query to fetch measure names
    const sqlText = `
      SELECT DISTINCT
        "m"."Long Measure Name" AS "MeasureName",
	      "m"."Name" AS "MeasureId",
	      "m"."MedisolvMeasureId" AS "MeasureIdentifier"
      FROM "MPRE"."SisenseMeasures" AS "m"
      JOIN "MPRE"."SisenseMeasSumm" AS "sms"
          ON "sms"."Name" = "m"."Name"
      JOIN "MECA"."Entities" AS "e"
          ON "sms"."EntitiesId" = "e"."Id"
      JOIN "MECA"."OrganizationTypes" AS "ot"
          ON "e"."OrganizationTypeId" = "ot"."Id"
      WHERE "ot"."Code" = ?
        AND "sms"."SummaryDataRowCount" = 1
        AND "sms"."Period" = ?
        AND "sms"."StartDate" >= ?
        AND "sms"."StartDate" <= ?
      ORDER BY "m"."Long Measure Name"
    `

    // Create the binds array for the parameters
    const binds: any[] = [codeNumeric, intervalType, startDate, endDate] // TODO: update any

    // Execute the query
    return this.execute(sqlText, binds)
  }
}
