import {
  SisenseMeasureSummaryRepository,
  SisenseMeasureSummary,
} from '@/services/sisense/SisenseMeasureSummaryRepository'
import { ReportRequest } from '@/types/reports/medisolvReport'

/**
 * Service for handling ACO Performance Analysis data
 */
export class ACOPerformanceAnalysisService {
  private readonly repository: SisenseMeasureSummaryRepository

  /**
   * Constructor for ACOPerformanceAnalysisService
   *
   * @param repository - The repository for accessing Sisense measure summary data
   */
  constructor(repository: SisenseMeasureSummaryRepository) {
    this.repository = repository
  }

  /**
   * Get ACO Performance Analysis data
   *
   * @param intervalType - The period type (M: Monthly, Q: Quarterly, Y: Yearly)
   * @param startDate - The start date in YYYY-MM-DD format
   * @param measureNames - Array of measure names
   * @param codeNumeric - The organization type code
   * @param entityId - Optional entity ID filter
   * @param sourceContainer - Optional source container identifier filter
   * @returns Promise resolving to ACOPerformanceData
   */
  async getACOMeasurePerformanceByUniverse(reportRequest: ReportRequest) {
    // Execute both repository calls in parallel
    const results = await this.repository.findMeasurePerformanceByUniverse(
      reportRequest.intervalType,
      reportRequest.startDate,
      reportRequest.endDate,
      reportRequest.measureIdentifiers,
      reportRequest.universe!,
      reportRequest.sourceContainerIdentifier
    )

    return this.fillInMissingMeasures(results!, reportRequest)
  }

  async getACOMeasurePerformanceByOrgCode(reportRequest: ReportRequest) {
    const results = await this.repository.findMeasurePerformanceByOrgCode(
      reportRequest.intervalType,
      reportRequest.startDate,
      reportRequest.endDate,
      reportRequest.measureIdentifiers,
      reportRequest.sourceContainerIdentifier,
      reportRequest.code!
    )

    return this.fillInMissingMeasures(results!, reportRequest)
  }

  private fillInMissingMeasures(
    results: SisenseMeasureSummary[],
    reportRequest: ReportRequest
  ) {
    const entitiesMap = new Map<number, SisenseMeasureSummary>()
    const entityMeasuresMap = new Map<
      number,
      Map<string, SisenseMeasureSummary>
    >()
    const organizationName =
      results?.length! > 0 ? results![0]!.Organization : ''

    results!.forEach((row) => {
      entitiesMap.set(row.EntityId, row)
      const measuresMap = entityMeasuresMap.get(row.EntityId)
      if (measuresMap) {
        measuresMap.set(row.MeasureIdentifier, row)
      } else {
        const measuresMap = new Map<string, SisenseMeasureSummary>()
        measuresMap.set(row.MeasureIdentifier, row)
        entityMeasuresMap.set(row.EntityId, measuresMap)
      }
    })

    const completeResults: SisenseMeasureSummary[] = []
    for (const entityKey of entitiesMap.keys()) {
      const indexedEntity = entitiesMap.get(entityKey)
      const resultSetEntity = entityMeasuresMap.get(entityKey)
      for (const measureIdentifier of reportRequest.measureIdentifiers) {
        const record = resultSetEntity
          ? resultSetEntity.get(measureIdentifier)
          : null

        if (record) {
          completeResults.push(record)
        } else {
          completeResults.push({
            Period: reportRequest.intervalType,
            MeasureName: '-',
            EntityId: indexedEntity?.EntityId!,
            EntityDescription: indexedEntity?.EntityDescription!,
            //@ts-ignore
            Performance: null,
            //@ts-ignore
            Numerator: null,
            //@ts-ignore
            PerformanceDenominator: null,
            Organization: organizationName,
            PercentileSourceCode: '-',
            PTile: -1,
            SourceContainerIdentifier: '-',
            StartDate: '1970-01-01',
            SummaryDataRowCount: -1,
            MeasureIdentifier: measureIdentifier!,
          })
        }
      }
    }
    return completeResults
  }
}
