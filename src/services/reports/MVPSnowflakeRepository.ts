import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'
import {
  MVPSummary,
  QualityScoreByMVP,
  Score,
  QualityMeasuresScore,
  QualityMeasures,
  IAPIMeasure,
  MVP,
  ProviderDetail,
  ProviderPerformance,
} from '@/types/reports/mvpTypes'

/**
 * Repository for accessing mvp results from Snowflake
 */
export class MVPSNowflakeRepository extends SnowflakeRepository<MVPSummary> {
  constructor(connectionPool: any, tableName: string = 'MPRE.MVP_Summary') {
    // Use a dummy identity field since we'll be using custom SQL queries
    super(connectionPool, tableName, 'MVP_Id')
  }

  /**
   * Fetches MVP Summary data
   *
   * @param year - The year
   * @returns Promise resolving to an array of MVPSummary objects or undefined
   */
  async findMVPSummary(year: number): Promise<MVPSummary[] | undefined> {
    const sqlText = `
    WITH date_filter AS (
    SELECT "ReferenceDate"
    FROM "MPRE"."SisenseDimDate"
    WHERE "ReferenceDate" >= '?'::DATE
        AND "ReferenceDate" < '?'::DATE
    ),

    period_filter AS (
    SELECT "Period"
    FROM "MPRE"."SisensePeriod"
    WHERE "Period" = 'Y'
    ),

    base_summary AS (
    SELECT 
        ms."EntitiesId",
        ms."MVP_Id",
        ms."Score",
        ms."MeasureTypeId",
        ms."AdjustedWeight",
        m."MVPCategoryLink",
        e."Description"
    FROM "MPRE"."MVP_Summary" ms
    INNER JOIN "MECA"."MVP" m 
        ON ms."MVP_Id" = m."Id"
    INNER JOIN "MECA"."Entities" e 
        ON ms."EntitiesId" = e."Id"
    WHERE ms."Period" IN (SELECT "Period" FROM period_filter)
        AND ms."StartDate" IN (SELECT "ReferenceDate" FROM date_filter)
        AND ms."MeasureTypeId" IN (1, 2, 3)
    ),

    aggregated_scores AS (
    SELECT 
        "Description",
        "MVPCategoryLink",
        -- Type 1 measures (Quality)
        SUM(CASE WHEN "MeasureTypeId" = 1 THEN "Score" END) AS "sum__Score",
        COALESCE(SUM(CASE WHEN "MeasureTypeId" = 1 THEN "AdjustedWeight" END) * 100, 30) AS "max__Score",
        -- Type 3 measures (IA)
        SUM(CASE WHEN "MeasureTypeId" = 3 THEN "Score" END) AS "sum__Score_1",
        COALESCE(SUM(CASE WHEN "MeasureTypeId" = 3 THEN "AdjustedWeight" END) * 100, 15) AS "max__Score_1",
        -- Type 2 measures (PI)
        SUM(CASE WHEN "MeasureTypeId" = 2 THEN "Score" END) AS "sum__Score_2",
        COALESCE(SUM(CASE WHEN "MeasureTypeId" = 2 THEN "AdjustedWeight" END) * 100, 25) AS "max__Score_2",
        CAST(NULL AS FLOAT) AS "sum__Score_3"
    FROM base_summary
    GROUP BY "Description", "MVPCategoryLink"
    )

    -- Final result
    SELECT
    "Description" AS "Entity",
    "MVPCategoryLink" AS "MVP",
    COALESCE(SUM("sum__Score"), 0) AS "Quality",
    COALESCE(SUM("max__Score"), 30) AS "QualityMax",
    COALESCE(SUM("sum__Score_1"), 0) AS "IA",
    COALESCE(SUM("max__Score_1"), 15) AS "IAMax",
    COALESCE(SUM("sum__Score_2"), 0) AS "PIScore",
    COALESCE(SUM("max__Score_2"), 25) AS "PIScoreMax",
    0 AS "Cost",
    0 AS "CostMax",
    SUM("sum__Score_3") AS "Score"
    FROM aggregated_scores
    GROUP BY "Description", "MVPCategoryLink";
    `

    const binds: any[] = [new Date(year, 1, 1), new Date(year + 1, 1, 1)]

    // Execute the query
    return await this.execute(sqlText, binds)
  }

  /**
   * Fetches Quality Score over time (Summary)
   *
   * @param year - The year
   * @param periodType - The period type (M: Monthly, Q: Quarterly, Y: Yearly)
   * @returns Promise resolving to an array of Quality Scores
   */
  async findQualityScoreByMVP(
    year: number,
    periodType: string
  ): Promise<QualityScoreByMVP[] | undefined> {
    const sqlText = `
   WITH date_range AS (
    SELECT DISTINCT
        DATE_TRUNC('MONTH', "ReferenceDate")::DATE AS month_start_date
    FROM "MPRE"."SisenseDimDate"
    WHERE "ReferenceDate" >= '?'::DATE
        AND "ReferenceDate" < '?'::DATE
    ),

    measure_type_filter AS (
    SELECT "Id"
    FROM "MECA"."MVP_MeasureType"
    WHERE "MeasureType" = 'Quality'
    ),

    base_data AS (
    SELECT 
        DATE_TRUNC('MONTH', ms."StartDate")::DATE AS "StartDate",
        ms."MVP_SubTin",
        ms."Score",
        ms."Period",
        ms."MeasureTypeId"
    FROM "MPRE"."MVP_Summary" ms
    WHERE ms."Period" = '?'
        AND ms."MeasureTypeId" IN (SELECT "Id" FROM measure_type_filter)
    ),

    monthly_aggregation AS (
    SELECT
        d.month_start_date AS "datetime_months_DimDate_ReferenceDate",
        b."MVP_SubTin",
        SUM(b."Score") AS "Score"
    FROM base_data b
    INNER JOIN date_range d 
        ON b."StartDate" = d.month_start_date
    GROUP BY 
        d.month_start_date,
        b."MVP_SubTin"
    HAVING 
        SUM(b."Score") > 0
    )

    SELECT
    "datetime_months_DimDate_ReferenceDate" AS "Period",
    "MVP_SubTin" AS "MVP",
    "Score" AS "Score"
    FROM monthly_aggregation
    ORDER BY 
    "datetime_months_DimDate_ReferenceDate",
    "MVP_SubTin"
    LIMIT 50000;
    `

    const binds: any[] = [
      new Date(year, 1, 1),
      new Date(year + 1, 1, 1),
      periodType,
    ]

    // Execute the query
    return await this.execute(sqlText, binds)
  }

  /**
   * Fetches Score over time
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @param measureType - The measure type
   * @returns Promise resolving to the Score
   */
  async findScoreByMeasureType(
    year: number,
    mvp: string,
    entity: string,
    measureType: string
  ): Promise<Score | undefined> {
    let requiredMeasureType = ''
    let maxScoreFormula = ''

    switch (measureType) {
      case 'Quality':
        requiredMeasureType = 'Quality'
        maxScoreFormula = 'COALESCE(SUM("AdjustedWeight") * 100, 30) AS "MAX"'
        break
      case 'IA':
        requiredMeasureType = 'Improvement Activities (IA)'
        maxScoreFormula = 'COALESCE(SUM("AdjustedWeight") * 100, 15) AS "MAX"'
        break
      case 'PI':
        requiredMeasureType = 'Promoting Interoperability (PI)'
        maxScoreFormula = 'COALESCE(SUM("AdjustedWeight") * 100, 25) AS "MAX"'
        break
      case 'Cost':
        return {
          Score: 0,
          Min: 0,
          Max: 0,
        }
        break
      default:
        requiredMeasureType = ''
        maxScoreFormula = '0 AS "MAX"'
        break
    }

    const sqlText = `
  WITH quality_measure_types AS (
    SELECT "Id"
    FROM "MECA"."MVP_MeasureType"
    WHERE "MeasureType" = ?
    ),

    mvp_filter AS (
    SELECT "Id"
    FROM "MECA"."MVP"
    WHERE "MVPCategory" = ?
    ),

    entity_filter AS (
    SELECT "Id"
    FROM "MECA"."Entities"
    WHERE "Description" = ?
    ),

    base_summary AS (
    SELECT 
        "Score",
        "AdjustedWeight"
    FROM "MPRE"."MVP_Summary"
    WHERE "MeasureTypeId" IN (SELECT "Id" FROM quality_measure_types)
        AND "Period" = 'Y'
        AND "StartDate" >= ?
        AND "StartDate" < ?
        AND "MVP_Id" IN (SELECT "Id" FROM mvp_filter)
        AND "EntitiesId" IN (SELECT "Id" FROM entity_filter)
    )

    SELECT
    COALESCE(SUM("Score"), 0) AS "Score",
    0 AS "MIN",
    ${maxScoreFormula}
    FROM base_summary;
    `

    const binds: any[] = [
      requiredMeasureType,
      mvp,
      entity,
      new Date(year, 1, 1),
      new Date(year + 1, 1, 1),
    ]

    // Execute the query
    const result = await this.execute(sqlText, binds)

    return (
      (result?.map((x: any) => {
        return {
          Score: x.Score,
          Min: x.MIN,
          Max: x.MAX,
        }
      })[0] as Score) ?? undefined
    )
  }

  /**
   * Fetches Overall Weighted Score
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @returns Promise resolving to the Overall Weighted Score
   */
  async findOverallWeightedScore(
    year: number,
    mvp: string,
    entity: string
  ): Promise<Score | undefined> {
    const sqlText = `
    WITH measure_types AS (
    SELECT "Id"
    FROM "MECA"."MVP_MeasureType"
    WHERE "MeasureType" IN (
        'Improvement Activities (IA)',
        'Promoting Interoperability (PI)',
        'Quality'
        )
    )

    SELECT
    SUM("Score") AS "Score"
    FROM "MPRE"."MVP_Summary"
    WHERE "Period" = 'Y'
    AND "MeasureTypeId" IN (SELECT "Id" FROM measure_types)
    AND "StartDate" >= ?
    AND "StartDate" < ?
    AND "MVP_Id" IN (
        SELECT "Id"
        FROM "MECA"."MVP"
        WHERE "MVPCategory" = ?
    )
    AND "EntitiesId" IN (
        SELECT "Id"
        FROM "MECA"."Entities"
        WHERE "Description" = ?
    );
    `
    const binds: any[] = [
      new Date(year, 1, 1),
      new Date(year + 1, 1, 1),
      mvp,
      entity,
    ]

    // Execute the query
    const result = await this.execute(sqlText, binds)

    return (
      (result?.map((x: any) => {
        return {
          Score: x.Score,
        }
      })[0] as Score) ?? undefined
    )
  }

  /**
   * Fetches Quality Measures Score By Period
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @returns Promise resolving to the Overall Weighted Score
   */
  async findQualityMeasuresScore(
    year: number,
    mvp: string,
    entity: string,
    periodType: string
  ): Promise<QualityMeasuresScore[] | undefined> {
    const sqlText = `
   WITH measure_type_filter AS (
    SELECT "Id"
    FROM "MECA"."MVP_MeasureType"
    WHERE "MeasureType" IN (
        'Quality'
        )
    ),
    mvp_filter AS (
    SELECT "Id"
    FROM "MECA"."MVP"
    WHERE "MVPCategory" = ?
    ),
    entity_filter AS (
    SELECT "Id"
    FROM "MECA"."Entities"
    WHERE "Description" = ?
    ),
    base_data AS (
    SELECT
        md."StartDate",
        md."MeasureGUID",
        md."Points",
        md."Period",
        md."EntitiesId",
        md."MVP_Id",
        md."MeasureTypeId"
    FROM "MPRE"."MVP_Detail" md
    WHERE md."Period" = ?
        AND md."MeasureTypeId" IN (SELECT "Id" FROM measure_type_filter)
        AND md."MVP_Id" IN (SELECT "Id" FROM mvp_filter)
        AND md."EntitiesId" IN (SELECT "Id" FROM entity_filter)
    ),
    date_filtered AS (
    SELECT 
        bd.*,
        dd."ReferenceDate"
    FROM base_data bd
    INNER JOIN "MPRE"."SisenseDimDate" dd 
        ON bd."StartDate" = dd."ReferenceDate"
    WHERE dd."ReferenceDate" >= ?
        AND dd."ReferenceDate" < ?
    ),
    measure_names AS (
    SELECT 
        "MedisolvMeasureId",
        "Long Measure Name" AS "Long_Measure_Name"
    FROM "MPRE"."SisenseMeasures"
    )

    SELECT
    DATE_TRUNC('MONTH', df."ReferenceDate") AS "Period",
    mn."Long_Measure_Name" AS "MeasureName",
    SUM(df."Points") AS "Score"

    FROM date_filtered df
    INNER JOIN measure_names mn 
    ON df."MeasureGUID" = mn."MedisolvMeasureId"
    GROUP BY 
    DATE_TRUNC('MONTH', df."ReferenceDate"),
    mn."Long_Measure_Name"
    HAVING 
    SUM(df."Points") > 0
    ORDER BY 
    DATE_TRUNC('MONTH', df."ReferenceDate"),
    mn."Long_Measure_Name"
    LIMIT 50000;
    `
    const binds: any[] = [
      mvp,
      entity,
      periodType,
      new Date(year, 1, 1),
      new Date(year + 1, 1, 1),
    ]

    // Execute the query
    const result = await this.execute(sqlText, binds)

    return (
      (result?.map((x: any) => {
        return {
          MeasureName: x.MeasureName,
          Period: x.Period,
          Score: x.Score,
        }
      }) as QualityMeasuresScore[]) ?? undefined
    )
  }

  /**
   * Fetches Provider Performance By Period, Entity & MVP
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @returns Promise resolving to the Overall Weighted Score
   */
  async findProviderPerformanceByPeriodByEntityByMVP(
    year: number,
    mvp: string,
    entity: string
  ): Promise<ProviderPerformance[] | undefined> {
    const sqlText = `
      WITH date_range AS (
    SELECT "ReferenceDate"
    FROM "MPRE"."SisenseDimDate"
    WHERE "ReferenceDate" >= ?
        AND "ReferenceDate" < ?
),
mvp_filter AS (
    SELECT "Id"
    FROM "MECA"."MVP"
    WHERE "MVPCategory" = ?
),
entity_filter AS (
    SELECT "Id"
    FROM "MECA"."Entities"
    WHERE "Description" = ?
)

SELECT
    mdp."ProviderDescription" AS "ProviderName",
    COUNT(DISTINCT mdp."MeasureGUID") AS "MeasureCount",
    AVG(mdp."Points") AS "AvgPoints",
FROM "MPRE"."MVP_DetailProvider" mdp
INNER JOIN date_range dr 
    ON mdp."StartDate" = dr."ReferenceDate"
WHERE mdp."MVP_Id" IN (SELECT "Id" FROM mvp_filter)
    AND mdp."TinEntitiesId" IN (SELECT "Id" FROM entity_filter)
GROUP BY mdp."ProviderDescription"
ORDER BY AVG(mdp."Points") DESC;
        `
    const binds: any[] = [
      new Date(year, 1, 1),
      new Date(year + 1, 1, 1),
      mvp,
      entity,
    ]

    // Execute the query
    const result = await this.execute(sqlText, binds)

    return (
      (result?.map((x: any) => {
        return {
          ProviderName: x.ProviderName,
          MeasureCount: x.MeasureCount,
          Points: x.AvgPoints,
        }
      }) as ProviderPerformance[]) ?? undefined
    )
  }

  /**
   * Fetches Quality Measures By Period, Entity & MVP
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @returns Promise resolving to the Overall Weighted Score
   */
  async findQualityMeasures(
    year: number,
    mvp: string,
    entity: string
  ): Promise<QualityMeasures[] | undefined> {
    const sqlText = `
    WITH quality_measures AS (
    SELECT "Id"
    FROM "MECA"."MVP_MeasureType"
    WHERE "MeasureType" = 'Quality'
),
measure_data AS (
    SELECT 
        m."Long Measure Name" AS measure_name,
        m."MedisolvMeasureId" AS measure_guid
    FROM "MPRE"."SisenseMeasures" m
),
mvp_data AS (
    SELECT "Id"
    FROM "MECA"."MVP"
    WHERE "MVPCategory" = ?
),
entity_data AS (
    SELECT "Id"
    FROM "MECA"."Entities"
    WHERE "Description" = ?
),
ranked_measures AS (
    SELECT 
        md."MeasureGUID",
        md."Rank",
        md."Numerator",
        md."PerformanceDenominator",
        md."Performance",
        md."PTile",
        md."Points"
    FROM "MPRE"."MVP_Detail" md
    WHERE md."Rank" <= 4
        AND md."MeasureTypeId" IN (SELECT "Id" FROM quality_measures)
        AND md."Period" = 'Y'
        AND md."StartDate" >= ?
        AND md."StartDate" < ?
        AND md."MVP_Id" IN (SELECT "Id" FROM mvp_data)
        AND md."EntitiesId" IN (SELECT "Id" FROM entity_data)
)

SELECT
    m.measure_name AS "MeasureName",
    r."Rank" AS "Rank",
    r."Numerator" AS "Numerator",
    r."PerformanceDenominator" AS "Denominator",
    r."Performance" AS "Performance",
    r."PTile" AS "PTile",
    r."Points" AS "Points",
FROM measure_data m
INNER JOIN ranked_measures r 
    ON m.measure_guid = r."MeasureGUID"
GROUP BY 
    m.measure_name,
    r."Rank",
    r."Numerator",
    r."PerformanceDenominator",
    r."Performance",
    r."PTile",
    r."Points"
ORDER BY r."Rank";
             `
    const binds: any[] = [
      mvp,
      entity,
      new Date(year, 1, 1),
      new Date(year + 1, 1, 1),
    ]

    // Execute the query
    const result = await this.execute(sqlText, binds)

    return (
      (result?.map((x: any) => {
        return {
          MeasureName: x.MeasureName,
          Rank: x.Rank,
          Numerator: x.Numerator,
          Denominator: x.Denominator,
          Performance: x.Performance,
          PTile: x.PTile,
          Points: x.Points,
        }
      }) as QualityMeasures[]) ?? undefined
    )
  }

  /**
   * Fetches IA or PI Measures By Period, Entity & MVP
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @returns Promise resolving to the Overall Weighted Score
   */
  async findIaOrPiMeasures(
    year: number,
    mvp: string,
    entity: string,
    measureType: string
  ): Promise<IAPIMeasure[] | undefined> {
    let requiredMeasureType =
      measureType === 'IA'
        ? 'Improvement Activities (IA)'
        : 'Promoting Interoperability (PI)'

    const sqlText = `
WITH pi_measures AS (
    SELECT "Id"
    FROM "MECA"."MVP_MeasureType"
    WHERE "MeasureType" = ?
),
base_data AS (
    SELECT 
        mvp."MeasureGUID",
        mvp."Completed",
        mvp."Points"
    FROM "MPRE"."MVP_Detail" mvp
    WHERE mvp."MeasureTypeId" IN (SELECT "Id" FROM pi_measures)
        AND mvp."Period" = 'Y'
        AND mvp."StartDate" >= ?
        AND mvp."StartDate" < ?
        AND mvp."MVP_Id" IN (
            SELECT "Id" 
            FROM "MECA"."MVP" 
            WHERE "MVPCategory" = ?
        )
        AND mvp."EntitiesId" IN (
            SELECT "Id" 
            FROM "MECA"."Entities" 
            WHERE "Description" = ?
        )
)

SELECT
    m."Long Measure Name" AS "Long_Measure_Name_res",
    b."Completed" AS "Completed_res",
    SUM(b."Points") AS "sum__Points_res"
FROM base_data b
INNER JOIN "MPRE"."SisenseMeasures" m 
    ON b."MeasureGUID" = m."MedisolvMeasureId"
GROUP BY 
    m."Long Measure Name",
    b."Completed"
ORDER BY 
    m."Long Measure Name",
    b."Completed";
`
    const binds: any[] = [
      requiredMeasureType,
      new Date(year, 1, 1),
      new Date(year + 1, 1, 1),
      mvp,
      entity,
    ]

    // Execute the query
    const result = await this.execute(sqlText, binds)

    return (
      (result?.map((x: any) => {
        return {
          MeasureName: x.MeasureName,
          Completed: x.Completed,
          Points: x.Points,
        }
      }) as IAPIMeasure[]) ?? undefined
    )
  }

  async findMVPs(): Promise<MVP[]> {
    const sqlText = `
    SELECT "Id", "MVPCategory", "MVPCategoryLink", "MVPCategory_Short"
    FROM "MECA"."MVP"
    `
    const binds: any[] = []

    // Execute the query
    const result = await this.execute(sqlText, binds)

    return (
      (result?.map((x: any) => {
        return {
          Id: x.Id,
          MVPCategory: x.MVPCategory,
          MVPCategoryLink: x.MVPCategoryLink,
          MVPCategory_Short: x.MVPCategory_Short,
        }
      }) as MVP[]) ?? []
    )
  }

  async findProviderDetails(
    year: number,
    providerName: string
  ): Promise<ProviderDetail[] | undefined> {
    const sqlText = `
  WITH provider_entity AS (
  SELECT e."Id"
  FROM "MECA"."Entities" e
  JOIN "MPRE"."EntityOrganizationType" eot ON e."Id" = eot."EntityId"
  WHERE e."Description" = ?
    AND eot."Universe" = 'Medical Provider'
)

  SELECT
  m."Long Measure Name" AS "MeasureName",
  SUM(ms."Numerator") AS "Numerator",
  SUM(ms."PerformanceDenominator") AS "Denominator",
  SUM(ms."PTile") AS "PTile",
  SUM(ms."Points") AS "Points",
  MIN(ms."Performance") AS "performance"
  FROM
  "MPRE"."SisenseMeasSumm" ms
  JOIN "MPRE"."SisenseMeasures" m ON ms."MeasureGUID" = m."MedisolvMeasureId"
  JOIN provider_entity pe ON ms."EntitiesId" = pe."Id"  
  WHERE
  ms."SummaryDataRowCount" = 1
  AND ms."Period" = 'Y'
  AND ms."StartDate" BETWEEN ?::DATE AND ?::DATE
  GROUP BY
  m."Long Measure Name"
  ORDER BY
  m."Long Measure Name";
    `

    const binds: any[] = [
      providerName,
      new Date(year, 1, 1),
      new Date(year + 1, 1, 1),
    ]

    // Execute the query
    const result = await this.execute(sqlText, binds)

    return (
      (result?.map((x: any) => {
        return {
          MeasureName: x.MeasureName,
          Numerator: x.Numerator,
          Denominator: x.Denominator,
          Performance: x.Performance,
          PTile: x.PTile,
          Points: x.Points,
        }
      }) as ProviderDetail[]) ?? undefined
    )
  }
}
