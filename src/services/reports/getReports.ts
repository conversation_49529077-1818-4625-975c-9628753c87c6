import { MedisolvReport } from '@/types/reports/medisolvReport'
import StorageService from '../storeage'
import { tryCache } from '@/lib/redis'
import { env } from '@/env'

export const getReports = async () => {
  const storageService = new StorageService()
  const cachKeyy = 'medisolvReports'

  return await tryCache(
    cachKeyy,
    async () => {
      try {
        const jsonfileData = await storageService.readBlobAsync(
          'report-configuration',
          `ConfigurationsSettings/MedisolvReportOptions.json`
        )

        const fileResult = new TextDecoder('utf-8').decode(jsonfileData)

        const graphs: MedisolvReport[] = JSON.parse(fileResult)

        return graphs
      } catch (error) {
        console.error('Error reading blob:', error)
        return []
      }
    },
    Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
  )
}
