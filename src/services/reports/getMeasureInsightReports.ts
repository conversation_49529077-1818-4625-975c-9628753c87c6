import { StorageTables } from '@/enums/storageTables'
import { getReportPreferences } from './getReportPreferences'
import { getReports } from './getReports'
import { MedisolvReport } from '@/types/reports/medisolvReport'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { SelectionType } from '@/enums/selectionType'

export const getMeasureInsightReports = async (
  organizationId: string,
  selectionType: SelectionType
) => {
  try {
    // TODO: May remove
    const reports = await getReports()
    const reportPreferences = await getReportPreferences(
      new AzureTableStorageWrapper(StorageTables.ManageReports),
      organizationId,
      {
        organizationId,
        selectionType,
      }
    )
    const disabledReportIds = reportPreferences
      .filter((x) => x.isActive)
      .map((m) => m.reportId)

    let insightReports = reports
      .filter(
        (item: MedisolvReport) =>
          !disabledReportIds.includes(item.MedisolvReportId) &&
          item.ReportType === '2' &&
          item.ReportStatus === 'Active' &&
          item.MedisolvReportName !== 'SPC Alert Summary' &&
          item.MedisolvReportName !== 'Measure Stratification Report' &&
          item.MedisolvReportName !== 'ACO Performance Analysis Report' &&
          item.reportingContentType === 'report'
      )
      .map((item) => ({
        ReportId: btoa(item.MedisolvReportId),
        ReportName: item.MedisolvReportName,
      }))

    const insightDashboards = reports
      .filter(
        (item: MedisolvReport) =>
          (item.reportingContentType === 'dashboard' &&
            item.MedisolvReportName.includes(
              'Single Entity Multiple Measure'
            )) ||
          item.MedisolvReportName.includes('Single Measure Multiple Entity') ||
          item.MedisolvReportName.includes('Geo')
      )
      .map((item) => ({
        ReportId: btoa(item.MedisolvReportId),
        ReportName: item.MedisolvReportName,
      }))

    return insightReports
      .concat(insightDashboards)
      .sort((a, b) => a.ReportName.localeCompare(b.ReportName))
  } catch (error) {
    //logError
    console.log(error)
  }
}
