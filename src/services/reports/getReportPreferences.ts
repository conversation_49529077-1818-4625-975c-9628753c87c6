import { ManageReport } from '@/types/manageReport'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { MigrationConfig } from '@/types/migrationConfig'
import { migrateData } from '../migration/migrateData'
import { ADMPrismaClient } from '@/server/db'
import { upsertReportPreferences } from './upsertReportPreferences'

export const getReportPreferences = async (
  tableStorage: AzureTableStorageWrapper,
  organizationId: string,
  migrationConfig?: MigrationConfig
): Promise<ManageReport[]> => {
  // TODO: Enhancement* Add try/catch w/ app insights
  const existingReports = await tableStorage.queryEntities<ManageReport>(
    tableStorage.generateODataQuery({
      PartitionKey: organizationId,
    })
  )

  if (migrationConfig && existingReports.length === 0) {
    // TODO: Enhancement* Add try/catch w/ app insights
    await migrateData({
      storage: tableStorage,
      migrationConfig,
      prismaClientType: 'admClient',
      prismaQuery: (prismaClient: ADMPrismaClient) =>
        prismaClient.manageReports.findMany({
          where: {
            OrganizationId: organizationId,
          },
        }),
      upsertFunction: async (storage, data) =>
        // TODO: Enhancement* Add try/catch w/ app insights
        await Promise.all(
          data.map((report) =>
            upsertReportPreferences(storage, {
              partitionKey: organizationId,
              rowKey: report.Id.toString(),
              organizationId: report.OrganizationId,
              entityId: report.EntityId,
              reportId: report.ReportId,
              isActive: report.IsActive,
            })
          )
        ),
    })
  }

  return tableStorage.queryEntities<ManageReport>(
    tableStorage.generateODataQuery({
      PartitionKey: organizationId,
    })
  )
}
