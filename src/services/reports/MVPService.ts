import {
  MVPSummary,
  QualityScoreByMVP,
  Score,
  QualityMeasuresScore,
  ProviderPerformance,
  QualityMeasures,
  IAPIMeasure,
  MVP,
  ProviderDetail,
} from '@/types/reports/mvpTypes'
import { MVPSNowflakeRepository } from './MVPSnowflakeRepository'

/**
 * Service for handling MVP data
 */
export class MVPService {
  private readonly repository: MVPSNowflakeRepository

  /**
   * Constructor for MVPService
   *
   * @param repository - The repository for accessing MVP data
   */
  constructor(repository: MVPSNowflakeRepository) {
    this.repository = repository
  }

  /**
   * Get MVP summary for a specific year
   *
   * @param year - The year to get summary for
   * @returns Promise resolving to an array of MVPSummary objects or undefined
   */
  async getMVPSummary(year: number): Promise<MVPSummary[] | undefined> {
    try {
      return await this.repository.findMVPSummary(year)
    } catch (err) {
      console.error('Error fetching MVP summary:', err)
      return undefined
    }
  }

  /**
   * Get Quality Score over time
   *
   * @param year - The year
   * @param periodType - The period type (M: Monthly, Q: Quarterly, Y: Yearly)
   * @returns Promise resolving to an array of QualityScore objects or undefined
   */
  async getQualityScoreByMVP(
    year: number,
    periodType: string
  ): Promise<QualityScoreByMVP[] | undefined> {
    try {
      return await this.repository.findQualityScoreByMVP(year, periodType)
    } catch (err) {
      console.error('Error fetching quality score by period:', err)
      return undefined
    }
  }

  /**
   * Get Score by measure type
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @param measureType - The measure type
   * @returns Promise resolving to a Score object or undefined
   */
  async getScoreByMeasureType(
    year: number,
    mvp: string,
    entity: string,
    measureType: string
  ): Promise<Score | undefined> {
    try {
      return await this.repository.findScoreByMeasureType(
        year,
        mvp,
        entity,
        measureType
      )
    } catch (err) {
      console.error('Error fetching score by measure type:', err)
      return undefined
    }
  }

  /**
   * Get Overall Weighted Score
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @returns Promise resolving to a Score object or undefined
   */
  async getOverallWeightedScore(
    year: number,
    mvp: string,
    entity: string
  ): Promise<Score | undefined> {
    try {
      return await this.repository.findOverallWeightedScore(year, mvp, entity)
    } catch (err) {
      console.error('Error fetching overall weighted score:', err)
      return undefined
    }
  }

  /**
   * Get Quality Measures Score
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @param periodType - The period type
   * @returns Promise resolving to an array of QualityMeasuresScore objects or undefined
   */
  async getQualityMeasuresScore(
    year: number,
    mvp: string,
    entity: string,
    periodType: string
  ): Promise<QualityMeasuresScore[] | undefined> {
    try {
      return await this.repository.findQualityMeasuresScore(
        year,
        mvp,
        entity,
        periodType
      )
    } catch (err) {
      console.error('Error fetching quality measures score:', err)
      return undefined
    }
  }

  /**
   * Get Provider Performance
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @returns Promise resolving to an array of ProviderPeformance objects or undefined
   */
  async getProviderPerformance(
    year: number,
    mvp: string,
    entity: string
  ): Promise<ProviderPerformance[] | undefined> {
    try {
      return await this.repository.findProviderPerformanceByPeriodByEntityByMVP(
        year,
        mvp,
        entity
      )
    } catch (err) {
      console.error('Error fetching provider performance:', err)
      return undefined
    }
  }

  /**
   * Get Quality Measures
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @returns Promise resolving to an array of QualityMeasures objects or undefined
   */
  async getQualityMeasures(
    year: number,
    mvp: string,
    entity: string
  ): Promise<QualityMeasures[] | undefined> {
    try {
      return await this.repository.findQualityMeasures(year, mvp, entity)
    } catch (err) {
      console.error('Error fetching quality measures:', err)
      return undefined
    }
  }

  /**
   * Get IA PI Measures
   *
   * @param year - The year
   * @param mvp - The MVP
   * @param entity - The entity
   * @param measureType - The measure type
   * @returns Promise resolving to an array of IAPIMeasure objects or undefined
   */
  async getIAPIMeasures(
    year: number,
    mvp: string,
    entity: string,
    measureType: string
  ): Promise<IAPIMeasure[] | undefined> {
    try {
      return await this.repository.findIaOrPiMeasures(
        year,
        mvp,
        entity,
        measureType
      )
    } catch (err) {
      console.error('Error fetching IA PI measures:', err)
      return undefined
    }
  }

  /**
   * Get MVPs
   *
   * @returns Promise resolving to an array of MVP objects or undefined
   */
  async getMVPs(): Promise<MVP[] | undefined> {
    try {
      return await this.repository.findMVPs()
    } catch (err) {
      console.error('Error fetching MVPs:', err)
      return undefined
    }
  }

  async getProviderDetails(
    year: number,
    providerName: string
  ): Promise<ProviderDetail[] | undefined> {
    try {
      return await this.repository.findProviderDetails(year, providerName)
    } catch (err) {
      console.error('Error fetching provider details:', err)
      return undefined
    }
  }
}
