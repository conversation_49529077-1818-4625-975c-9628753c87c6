import snowflake, { type Connection, type Binds, RowStatement, FileAndStageBindStatement } from 'snowflake-sdk'
import { env } from "@/env"
import appInsights from "@/lib/applicationInsights"

export function executeAsAResultSet(
    connection: Connection,
    sqlText: string,
    binds: Binds = []
): Promise<{ [index: string]: any }[] | undefined> {
    return execute<{ [index: string]: any }>(connection, sqlText, binds)
}

export async function executeAsSingleValue<T>(
    connection: Connection,
    sqlText: string,
    binds: Binds = []
): Promise<T | undefined> {
    let result: T | undefined = undefined
    const results = await executeAsAResultSet(connection, sqlText, binds)
    if (results?.length! > 0) {
        const row = results![0]!
        const properties = Object.entries(row)
            .filter(([key]) => row[key] !== undefined)
        result = (properties.length > 0) ? properties[0]![1] as T : undefined
    }
    return result
}

export async function execute<T>(
    connection: Connection,
    sqlText: string,
    binds: Binds = []
): Promise<T[] | undefined> {
    const start = Date.now()
    let statement : RowStatement | FileAndStageBindStatement
    try {
        appInsights.trackEvent({
            name: `SnowflakeHelper.execute: Enter`,
            properties: {
                id: start,
                sqlText,
                binds,
            },
        })
        if (!connection.isUp()) {
            await connection.connectAsync(() => {})
        }
        const queryId = await new Promise<string>((resolve) => {
            connection.execute({
                sqlText,
                binds,
                asyncExec: true,
                complete: async (_err, stmt, _rows) => {
                    resolve(stmt.getQueryId())
                }
            })
        })
        statement = await connection.getResultsFromQueryId({
            queryId,
            sqlText
        })
    } catch (error) {
        appInsights.trackEvent({
            name: `SnowflakeHelper.execute: Exit`,
            properties: {
                id: start,
                success: false,
                error,
                duration: Date.now() - start,
            },
        })
        throw error
    }

    return new Promise((resolve, reject) => {
        const results: T[] = []
        try {
            var stream = statement.streamRows()
            stream.on('error', function (error) {
                appInsights.trackEvent({
                    name: `SnowflakeHelper.execute: Exit`,
                    properties: {
                        id: start,
                        success: false,
                        error,
                        duration: Date.now() - start,
                    },
                })
                reject(error)
            })
            stream.on('data', function (row: T) {
                results.push(row)
            })
            stream.on('end', function () {
                appInsights.trackEvent({
                    name: `SnowflakeHelper.execute: Exit`,
                    properties: {
                        id: start,
                        success: true,
                        duration: Date.now() - start,
                    },
                })  //if this was called on the client, just return the call back
                resolve(results)
            })
        } catch (error) {
            appInsights.trackEvent({
                name: `SnowflakeHelper.execute: Exit`,
                properties: {
                    id: start,
                    success: false,
                    error,
                    duration: Date.now() - start,
                },
            })
            reject(error)
        }
    })
}

export function setLoggingLevel(logLevel: snowflake.LogLevel) {
    snowflake.configure({
        logLevel
    })
}

export async function nextId(connection: Connection, tableName: string, id: string = "Id"): Promise<number> {
    if (isPotentialSQLInjection(tableName)) {
        throw `${tableName} has potential SQL injection value`
    }
    if (isPotentialSQLInjection(id)) {
        throw `${id} has potential SQL injection value`
    }

    return (await executeAsSingleValue<number>(
        connection,
        `Select MAX("${id}") + 1 as "NextId" FROM MECA."${tableName}"`
    ))!
}

export function createSnowflakeConfiguration() {
    return {
        account: env.SNOWFLAKE_ACCOUNT,
        username: env.SNOWFLAKE_USERNAME,
        password: env.SNOWFLAKE_PASSWORD,
        warehouse: env.SNOWFLAKE_WAREHOUSE,
        database: env.SNOWFLAKE_DATABASE,
    } as snowflake.ConnectionOptions
}

export function createConnection(options: snowflake.ConnectionOptions = createSnowflakeConfiguration()) {
    return snowflake.createConnection(options)
}

export function isPotentialSQLInjection(input: string): boolean {
    const sqlInjectionPattern = /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|UNION|EXEC|MERGE|CALL|TRUNCATE|REPLACE|HAVING|OR|AND)\b)|(--|;|\*|\/\*|\*\/|@@|CHAR\(|CAST\(|CONVERT\()/i
    return sqlInjectionPattern.test(input)
}

/**
 * Helper method to apply filter conditions to a SQL query
 *
 * @param sqlText - The current SQL query text
 * @param binds - The array of bind parameters
 * @param conditions - Array of filter conditions to apply
 * @returns Object containing the updated SQL text and binds array
 */
export function applyFilters(
    sqlText: string,
    binds: any[],
    conditions: {
        value: any
        column: string
        operator?: string }[]
): { sqlText: string, binds: any[] } {
    // Filter out conditions with undefined or null values
    const validConditions = conditions.filter(c => c.value !== undefined && c.value !== null)

    // Apply each valid condition to the SQL query
    validConditions.forEach(({ value, column, operator = '=' }) => {
        sqlText += `
          AND ${column} ${operator} ?
      `
        binds.push(value)
    })

    return { sqlText, binds }
}
