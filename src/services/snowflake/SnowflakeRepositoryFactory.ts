import { env } from '@/env'
import snowflake, { Pool, Connection } from 'snowflake-sdk'
import { MeasureRepository } from '@/services/measures/MeasureRepository'
import { FacilityRepository } from '@/services/facilities/FacilityRepository'
import { setLoggingLevel } from '@/services/snowflake/SnowflakeHelper'
import { SnowflakeActiveMeasureRepository } from '@/services/measures/ActiveMeasureRepository'
import { ProvidersRepository } from '@/services/providers/ProvidersRepository'
import { SubmissionGroupRepository } from '@/services/submissionGroups/SubmissionGroupRepository'
import { ProcedureStatusRepository } from '@/services/procedureStatus/ProcedureStatusRepository'
import { EntitiesRepository } from '@/services/entities/EntitiesRepository'
import { SisenseMeasureSummaryRepository } from '../sisense/SisenseMeasureSummaryRepository'
import { ACOMeasureRepository } from '../reports/ACOMeasureRepository'
import { EntityOrganizationTypeRepository } from '@/services/entityOrganizationType/EntityOrganizationTypeRepository'
import { OrganizationTypeRepository } from '../organizationType/OrganizationTypeRepository'
import { MeasureSummaryRepository } from '@/services/measureSummary/MeasureSummaryRepository'
import { MVPRepository } from '../reports/MVPRepository'
import { SnowflakeAvailableMeasuresRepository } from '../measures/AvailableMeasuresRepository'

setLoggingLevel((env.SNOWFLAKE_LOGGING_LEVEL ?? 'error') as snowflake.LogLevel)

export class Factory {
  connectionPool: Pool<Connection>

  constructor(connectionPool: Pool<Connection>) {
    this.connectionPool = connectionPool
  }

  createMeasureRepository = (tableName = 'MECA."Measures"') => {
    return new MeasureRepository(this.connectionPool, tableName)
  }

  createSisenseMeasureSummaryRepository = (
    tableName = 'MPRE."SisenseMeasSumm"'
  ) => {
    return new SisenseMeasureSummaryRepository(this.connectionPool, tableName)
  }

  createActiveMeasureRepository = (tableName = 'MECA."ActiveMeasures"') => {
    return new SnowflakeActiveMeasureRepository(this.connectionPool, tableName)
  }

  createFacilityRepository = (tableName = 'MECA."Facilities"') => {
    return new FacilityRepository(this.connectionPool, tableName)
  }

  createProvidersRepository = (tableName = 'CSRC."Providers"') => {
    return new ProvidersRepository(this.connectionPool, tableName)
  }

  createSubmissionGroupRepository = (tableName = 'MECA."Entities"') => {
    return new SubmissionGroupRepository(
      this.connectionPool,
      tableName
    )
  }

  createProcedureRepository = (tableName = 'AUD."LoadProcedureStatus"') => {
    return new ProcedureStatusRepository(this.connectionPool, tableName)
  }

  createEntitiesRepository = (tableName = 'MECA."Entities"') => {
    return new EntitiesRepository(this.connectionPool, tableName)
  }

  createACOMeasureRepository = (tableName = 'MPRE."SisenseMeasures"') => {
    return new ACOMeasureRepository(this.connectionPool, tableName)
  }

  createEntityOrganizationTypeRepository = (
    tableName = 'MPRE."EntityOrganizationType"'
  ) => {
    return new EntityOrganizationTypeRepository(this.connectionPool, tableName)
  }

  createMeasureSummaryRepository = (tableName = 'MECA."MeasureSummary"') => {
    return new MeasureSummaryRepository(this.connectionPool, tableName)
  }

  createMVPSnowflakeRepository = (tableName = 'MPRE."MVP_Summary"') => {
    return new MVPRepository(this.connectionPool, tableName)
  }

  createOrganizationTypeRepository = () => {
    return new OrganizationTypeRepository(
      this.connectionPool,
      'MECA."OrganizationType"'
    )
  }

  createAvailableMeasuresRepository = () => {
    return new SnowflakeAvailableMeasuresRepository(
      this.connectionPool,
      'MECA."ActiveMeasures"'
    )
  }
}

const cache = new Map<string, Factory>()

export async function closePool() {
  for (const key of cache.keys()) {
    await cache.get(key)?.connectionPool?.drain()
    cache.delete(key)
  }
}

function memoize(
  fn: (connectionOptions: snowflake.ConnectionOptions) => Factory
): (connectionOptions: snowflake.ConnectionOptions) => Factory {
  return function (connectionOptions: snowflake.ConnectionOptions) {
    const key = JSON.stringify(connectionOptions)
    if (cache.has(key)) {
      return cache.get(key)!
    }
    const result = fn(connectionOptions)
    cache.set(key, result)
    return result
  }
}

function createRepositoryFactory(
  connectionOptions: snowflake.ConnectionOptions
) {
  const connectionPool = snowflake.createPool(connectionOptions, {
    max: 10,
    min: 0,
  })
  return new Factory(connectionPool)
}

export const factory = memoize(createRepositoryFactory)
