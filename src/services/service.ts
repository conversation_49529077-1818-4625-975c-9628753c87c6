import appInsights from '@/lib/applicationInsights'
import { keysToCamelCase, type Transformable } from '@/lib/keysToCamelCase'
import { ADMPrismaClient, HUBPrismaClient } from "@/server/db";

export class Service {
  protected accessToken: string

  constructor(accessToken: string) {
    this.accessToken = accessToken
  }

  static fetchWithType = async <R>(
    accessToken: string,
    url: string,
    options?: RequestInit
  ): Promise<{ data?: R; error?: { message: string; response: string } }> => {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      ...options,
    })

    if (!response.ok) {
      const errorResponse = await response.text()

      if (response.status === 401) {
        // Return a custom error object instead of redirecting
        const err = new Error('Unauthorized', {
          cause: {
            statusCode: 401,
            message: `[Service Error] ${response.statusText}`,
            response: errorResponse,
            headers: getResponseHeaders(response),
          },
        })

        appInsights.trackException({
          exception: err,
          properties: {
            url: url,
            accessToken: accessToken,
            headers: getResponseHeaders(response),
            response: errorResponse,
          },
        })

        throw err
      }

      if (response.status >= 500) {
        const err = new Error(`Error fetching data`, {
          cause: {
            statusCode: 500,
            message: `[Service Error] ${response.statusText}`,
            response: errorResponse,
            headers: getResponseHeaders(response),
          },
        })

        appInsights.trackException({
          exception: err,
          properties: {
            url: url,
            accessToken: accessToken,
            headers: getResponseHeaders(response),
          },
        })
        throw err
      }

      console.error('Non-critical error:', {
        statusCode: response.status,
        message: response.statusText,
        response: errorResponse,
      })

      return {
        error: {
          message: response.statusText,
          response: errorResponse,
        },
      }
    }

    const jsonData: unknown = await response.json()

    // Add a runtime check to ensure jsonData is of type `Transformable`
    if (
      jsonData !== null &&
      (typeof jsonData === 'object' || Array.isArray(jsonData))
    ) {
      return { data: keysToCamelCase(jsonData as Transformable) as R }
    }

    throw new Error('Unexpected data format received from the API') // Handle unexpected data
  }

  protected fetchWithType = async <R>(
    url: string,
    options?: RequestInit
  ): Promise<{ data?: R; error?: { message: string; response: string } }> => {
    //added this because I want to be able to see the access token, then decode it to see whats inside and what are
    //getting back from citc
    appInsights.trackTrace({
      message: `fetchWithType - Fetching data from ${url}`,
      properties: {
        url: url,
        accessToken: this.accessToken,
      },
    })

    const response = await fetch(url, {
      // cache: 'force-cache',
      // next: { revalidate: 3600 },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.accessToken}`,
      },
      ...options,
    })

    if (!response.ok) {
      const errorResponse = await response.text()

      if (response.status === 401) {
        // Return a custom error object instead of redirecting
        const err = new Error('Unauthorized', {
          cause: {
            statusCode: 401,
            message: `[Service Error] ${response.statusText}`,
            response: errorResponse,
            headers: getResponseHeaders(response),
            payload: options,
          },
        })

        appInsights.trackException({
          exception: err,
          properties: {
            url: url,
            accessToken: this.accessToken,
            headers: getResponseHeaders(response),
            response: errorResponse,
          },
        })

        throw err
      }

      if (response.status >= 500) {
        const err = new Error(`Error fetching data`, {
          cause: {
            statusCode: 500,
            message: `[Service Error] ${response.statusText}`,
            response: errorResponse,
            headers: getResponseHeaders(response),
          },
        })

        appInsights.trackException({
          exception: err,
          properties: {
            url: url,
            accessToken: this.accessToken,
            headers: getResponseHeaders(response),
            payload: options,
          },
        })
        throw err
      }

      console.error('Non-critical error:', {
        statusCode: response.status,
        message: response.statusText,
        response: errorResponse,
      })

      return {
        error: {
          message: response.statusText,
          response: errorResponse,
        },
      }
    }

    let jsonData: unknown
    try {
      if (response.status === 204) {
        return { data: undefined }
      }
      jsonData = await response.json()
    } catch (err) {
      appInsights.trackException({
        exception: new Error(err?.toString() ?? 'Error parsing json data'),
        properties: {
          url: url,
          accessToken: this.accessToken,
          headers: getResponseHeaders(response),
          payload: response.text(),
        },
      })
      throw err
    }

    // Add a runtime check to ensure jsonData is of type `Transformable`
    if (
      jsonData !== null &&
      (typeof jsonData === 'object' || Array.isArray(jsonData))
    ) {
      return { data: keysToCamelCase(jsonData as Transformable) as R }
    }

    throw new Error('Unexpected data format received from the API') // Handle unexpected data
  }

  protected ADMPrismaClient = (organizationId: string): ADMPrismaClient =>
      prismaClients?.[organizationId]!.admClient!

  protected HUBPrismaClient = (organizationId: string): HUBPrismaClient =>
      prismaClients?.[organizationId]!.hubClient!
}

function getResponseHeaders(response: Response) {
  const result = []
  for (const header of response.headers) {
    result.push({ [header[0]]: header[1] })
  }
  return result
}
