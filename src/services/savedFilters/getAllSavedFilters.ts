import { tryCache } from '@/lib/redis'
import { SavedFilter } from '@/types/savedFilter'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { ChunkMetadata } from '@/types/filterQuery'
import { MigrationConfig } from '@/types/migrationConfig'
import { upsertSavedFilter } from './upsertSavedFilter'
import { PartnerStorageType } from '@/enums/partnerStorageType'
import { migrateData } from '../migration/migrateData'
import { HUBPrismaClient } from '@/server/db'

export const getFilterWithChunks = async (
  storage: AzureTableStorageWrapper,
  userId: string,
  mainEntityId: string
): Promise<string> => {
  const chunks = await storage.queryEntities<ChunkMetadata>(
    storage.generateODataQuery({
      PartitionKey: userId,
      mainEntityId,
      isChunk: true,
    })
  )

  if (chunks.length === 0) {
    return ''
  }

  // Sort chunks by index and concatenate
  return chunks
    .sort((a, b) => (a.chunkIndex ?? 0) - (b.chunkIndex ?? 0))
    .map((chunk) => chunk.chunkData)
    .join('')
}

export const getAllSavedFilters = async (
  storage: AzureTableStorageWrapper,
  userId: string,
  migrationConfig?: MigrationConfig
): Promise<SavedFilter[]> => {
  return tryCache(`savedFilters.${userId}`, async () => {
    // TODO: Enhancement* Add try/catch w/ app insights
    const existingFilters = await storage.queryEntities<SavedFilter>(
      storage.generateODataQuery({
        PartitionKey: userId,
      })
    )

    if (migrationConfig && existingFilters.length === 0) {
      // TODO: Enhancement* Add try/catch w/ app insights
      await migrateData({
        userId,
        storage,
        partnerStorageType: PartnerStorageType.Filters,
        migrationConfig,
        prismaClientType: 'hubClient',
        prismaQuery: (prismaClient: HUBPrismaClient) =>
          prismaClient.savedFilters.findMany({
            where: {
              UserId: userId,
            },
          }),
        upsertFunction: async (storage, data) =>
          // TODO: Enhancement* Add try/catch w/ app insights
          await Promise.all(
            data.map((filter) =>
              upsertSavedFilter(storage, {
                partitionKey: userId,
                rowKey: filter.Id.toString(),
                userId,
                filterName: filter.FilterName,
                filterMetadata: filter.FilterMetadata,
              })
            )
          ),
      })
    }

    // Get main filter records
    const mainFilters = await storage.queryEntities<SavedFilter>(
      storage.generateODataQuery({
        PartitionKey: userId,
        isChunk: false,
      })
    )

    // Reconstruct filters with their chunks
    const reconstructedFilters = await Promise.all(
      mainFilters.map(async (filter) => {
        const metadata = await getFilterWithChunks(
          storage,
          userId,
          filter.rowKey
        )
        return {
          ...filter,
          filterMetadata: metadata || filter.filterMetadata,
        }
      })
    )

    return reconstructedFilters
  })
}
