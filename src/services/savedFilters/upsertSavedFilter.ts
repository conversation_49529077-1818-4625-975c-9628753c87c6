import { SavedFilter } from '@/types/savedFilter'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { randomUUID } from 'crypto'
import { ChunkMetadata } from '@/types/filterQuery'
import { redisHelper } from "@/lib/redis";

const CHUNK_SIZE = 30000 // Azure Table Storage limit is 32KB, leaving some buffer

export const upsertSavedFilter = async (
  tableStorageWrapper: AzureTableStorageWrapper,
  filter: SavedFilter,
  chunkSize = CHUNK_SIZE
) => {
  const mainEntityId = filter.rowKey
  const metadata = filter.filterMetadata

  // If metadata is small enough, store directly
  if (metadata.length <= chunkSize) {
    await redisHelper.del(`savedFilters.${filter.userId}`)
    return tableStorageWrapper.upsertEntity({
      ...filter,
      isChunk: false,
    })
  }

  // Delete existing chunks if any
  const existingChunks = await tableStorageWrapper.queryEntities<ChunkMetadata>(
    tableStorageWrapper.generateODataQuery({
      PartitionKey: filter.partitionKey,
      mainEntityId,
      isChunk: true,
    })
  )

  for (const chunk of existingChunks) {
    await tableStorageWrapper.deleteEntity(chunk.partitionKey, chunk.rowKey)
  }

  // Store main filter record (with minimal required fields)
  await tableStorageWrapper.upsertEntity({
    partitionKey: filter.partitionKey,
    rowKey: mainEntityId,
    filterName: filter.filterName,
    userId: filter.userId,
    filterMetadata: '', // Empty since we're using chunks
    isChunk: false,
    mainEntityId,
  })

  // Split metadata into chunks
  const chunks = []
  for (let i = 0; i < metadata.length; i += chunkSize) {
    const chunkData = metadata.slice(i, i + chunkSize)
    chunks.push({
      partitionKey: filter.partitionKey,
      rowKey: randomUUID(),
      mainEntityId,
      isChunk: true,
      chunkIndex: Math.floor(i / chunkSize),
      chunkData,
    })
  }

  // Store all chunks
  await Promise.all(
    chunks.map((chunk) => tableStorageWrapper.upsertEntity(chunk))
  )
  await redisHelper.del(`savedFilters.${filter.userId}`)

  return filter
}
