import { ADMMeasure } from '@/types/admMeasure'
import { ECMeasureResultSummary } from '@/types/eCMeasureResultSummary'
import { MeasureResultSummary } from '@/types/measureResultSummary'
import { ScorecardDetails } from '@/types/scorecards/scorecardDetails'
import dayjs from 'dayjs'

export const getScorecardDetails = (
  measureResult: MeasureResultSummary | ECMeasureResultSummary,
  measuredetails: ADMMeasure,
  isVolumeMeasure?: boolean
) => {
  let details: ScorecardDetails = {}

  if (!measureResult) return details

  if (isVolumeMeasure) {
    details.numeratorCount = measureResult.numerator
    details.rate = measureResult.performance

    return details
  }

  details.ippCount = measureResult.ipp
  details.denominatorCount = measuredetails.NullDenominator
    ? null
    : measureResult.denominator
  details.excludedCount = measuredetails.NullDenExcl
    ? null
    : measureResult.denominatorExclusion
  details.numeratorCount = measuredetails.NullNumerator
    ? null
    : measureResult.numerator
  details.populationCount = measuredetails.NullIPP ? null : measureResult.ipp
  details.rate = measuredetails.NullRate ? null : measureResult.performance
  details.rejectedCount = measuredetails.NullNumExcl
    ? null
    : measureResult.numeratorExclusion
  details.startDate = dayjs(measureResult.startDate)
  details.endDate = dayjs(measureResult.endDate)
  details.denominatorException = measuredetails.NullException
    ? null
    : measureResult.denominatorException

  if (measureResult as MeasureResultSummary) {
    details.numeratorValue = measuredetails.NullNumerator
      ? null
      : (measureResult as MeasureResultSummary).numeratorValue
    details.denominatorValue = measuredetails.NullDenominator
      ? null
      : (measureResult as MeasureResultSummary).denominatorValue
  }
  return details
}
