import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { ScorecardView } from '@/enums/scorecardView'
import { getOrganizationPaidMeasuresQuery } from '@/prisma/getOrganizationPaidMeasuresQuery'
import { OrganizationPaidMeasure } from '@/types/organizationPaidMeasure'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import dayjs, { Dayjs } from 'dayjs'
import CitCParametersService from '@/services/citcParameters'
import MeasureResultsService from '../measureResults'
import { DetailsList } from '@/types/detailsList'
import { ScorecardDetails } from '@/types/scorecards/scorecardDetails'
import { getScorecardDetails } from './baseScorecardProvider'
import { Performance } from '@/enums/performance'
import TrendCSSHelper from '@/lib/trendCSSHelper'
import { ScorecardResult } from '@/types/scorecards/scorecards'
import { DateRange } from '@/lib/dateRange'
import { ECMeasureResultOptions } from '@/types/eCMeasureResultOptions'
import { INotation } from '@/enums/iNotation'
import appInsights from '@/lib/applicationInsights'
import { env } from '@/env'

export async function getECScorecardByMeasure(
  measureResultsService: MeasureResultsService,
  scorecardView: ScorecardView,
  organizationId: string,
  isPartner: boolean,
  startDate: Dayjs,
  endDate: Dayjs,
  submissionGroupIds: string[],
  primaryMeasureType: PrimaryMeasureTypeConstants,
  accessToken: string
): Promise<ScorecardResult[]> {
  try {
    let results: ScorecardResult[] = []
    const measureList: OrganizationPaidMeasure[] = []

    let parameters: SimplifiedParameter[] = []
    const citcParameterService = new CitCParametersService(accessToken)

    if (isPartner) {
      parameters =
        await citcParameterService.getPartnerParametersAsync(organizationId)
    } else {
      parameters =
        await citcParameterService.getOrganizationParametersAsync(
          organizationId
        )
    }

    const organizationPaidMeasures = await getOrganizationPaidMeasuresQuery(
      measureResultsService,
      startDate,
      endDate,
      primaryMeasureType,
      accessToken,
      parameters,
      organizationId
    )

    measureList.push(
      ...organizationPaidMeasures.sort((a, b) =>
        a.measureName.localeCompare(b.measureName)
      )
    )

    const dateRangeSpan = DateRange.getColumnIntervalsByCategory(
      scorecardView,
      startDate,
      endDate
    )

    const admMeasures =
      await measureResultsService.getAllMeasures(organizationId)

    const options: ECMeasureResultOptions = {
      organizationId,
      periodType: scorecardView,
      submissionGroupId: submissionGroupIds,
      startDate,
      endDate,
      isPartner: isPartner,
      isSubmissionGroupLevel: true,
      measureIdentifier: '',
    }

    const aggregateResultsResponse =
      await measureResultsService.getECMeasureResults(options)
    const measureRates = aggregateResultsResponse?.ecMeasureResultSummaryList

    const groupedMeasures = measureList.reduce(
      (acc, measure) => {
        const key = measure.measureIdentifier
        if (!acc[key]) acc[key] = []
        appInsights.trackEvent({
          name: 'getScorecardsByECMeasures: groupedMeasures',
          properties: {
            key,
            measure,
          },
        })
        acc[key].push(measure)
        return acc
      },
      {} as { [key: string]: OrganizationPaidMeasure[] }
    )

    for (const [key, group] of Object.entries(groupedMeasures)) {
      const admMeasureDetails = admMeasures.find(
        (x) => x.MedisolvMeasureId === key
      )
      if (!admMeasureDetails) {
        appInsights.trackEvent({
          name: 'getScorecardsByECMeasures: unable to find ADM Measures',
          properties: {
            key,
            admMeasures,
          },
        })
        continue
      }

      const isIAPIMeasure =
        admMeasureDetails?.SmallestInterval == 'Y' &&
        !admMeasureDetails?.DenominatorQualifyingType

      if (isIAPIMeasure) continue

      let currentMeasureRates = measureRates?.filter(
        (x) => x.measureGUID === key
      )
      currentMeasureRates = currentMeasureRates?.sort((a, b) =>
        dayjs(b.startDate).diff(dayjs(a.startDate))
      )
      const myObj: Partial<ScorecardResult> = {}

      myObj.measureIdentifier = key
      myObj.measureTitle = group[0]?.measureTitle!

      let isEmptyIndicator = true
      const scorecardDetails: ScorecardDetails[] = []

      for (const item of dateRangeSpan) {
        const span = item.replace('-', '_') as
          | `Q${number}_${number}`
          | `CY_${number}`
          | `${Capitalize<string>}_${number}`

        const period = DateRange.getDateFromDateRangeSpan(scorecardView, span)

        const rate = currentMeasureRates?.find(
          (x) =>
            dayjs(x.startDate).isSame(period.startDate, 'day') &&
            dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
        )

        if (rate) {
          myObj[span] =
            rate.performance === -1 || rate.performance === null
              ? '-'
              : rate.performance === 100
                ? rate.performance.toString()
                : rate.performance?.toFixed(2)

          if (rate.performance !== null) {
            isEmptyIndicator = false
          }

          let scorecard = getScorecardDetails(rate, admMeasureDetails!)
          scorecard.id = scorecardDetails.length
          scorecard.columnName = span
          scorecard.rateTitle =
            group[0]?.subTypeName == 'Rate' ? 'Percentage' : 'Median-Time'
          scorecardDetails.push(scorecard)
        } else {
          myObj[span] = '-'
          let scorecard: ScorecardDetails = {
            id: scorecardDetails.length,
            columnName: span,
            performance: Performance.NoData,
            goal: null,
          }
          scorecardDetails.push(scorecard)
        }
      }

      const trendName =
        INotation[group[0]?.iNotationName! as unknown as keyof typeof INotation]

      const detailList = scorecardDetails.map((x) => {
        return {
          performance: x.rate,
          denominator: x.denominatorCount,
        } as DetailsList
      })

      const trendCSS = TrendCSSHelper.getTrendSlopeCss(
        detailList,
        trendName,
        scorecardView
      )

      myObj.trendCss = trendCSS
      myObj.scorecardDetailsList = scorecardDetails
      myObj.scorecardView = scorecardView
      myObj.isEmptyIndicator = isEmptyIndicator
      myObj.smallestInterval = admMeasureDetails?.SmallestInterval ?? 'M'
      myObj.measureDescription = group[0]?.measureDescription.trim()!
      myObj.friendlyName = group[0]?.measureFriendlyName!
      myObj.subDomain = group[0]?.subDomainName!
      myObj.type = group[0]?.typeName!
      myObj.domain = group[0]?.domainName!
      myObj.cmsId = group[0]?.cMSId ?? '-'
      myObj.subType = group[0]?.subTypeName!
      myObj.application = group[0]?.applicationName!
      myObj.programName = group[0]?.programName ? group[0]?.programName : '-'
      myObj.inotation = trendName
      results.push(myObj)
    }

    return results
  } catch (error) {
    throw error
  }
}
