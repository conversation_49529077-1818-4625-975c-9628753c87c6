import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { ScorecardView } from '@/enums/scorecardView'
import { OrganizationPaidMeasure } from '@/types/organizationPaidMeasure'
import {
  HospitalSummary,
  ScorecardResultByHospital,
} from '@/types/scorecards/scorecards'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import dayjs, { Dayjs } from 'dayjs'
import CitCParametersService from '../citcParameters'
import { getOrganizationPaidMeasuresQuery } from '@/prisma/getOrganizationPaidMeasuresQuery'
import MeasureResultsService from '../measureResults'
import { ScorecardDetails } from '@/types/scorecards/scorecardDetails'
import { getScorecardDetails } from './baseScorecardProvider'
import { Performance } from '@/enums/performance'
import { DetailsList } from '@/types/detailsList'
import TrendCSSHelper from '@/lib/trendCSSHelper'
import { DateRange } from '@/lib/dateRange'
import { Organization } from '@/types/organization'
import { getSubmissionGroupsByOrganizationQuery } from '../submissionGroups/getSubmissionGroupsByOrganizationQuery'
import { ECMeasureResultOptions } from '@/types/eCMeasureResultOptions'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { ECMeasureResultSummary } from '@/types/eCMeasureResultSummary'
import { manageUserRolesQuery } from '../adm/users/manageUserRolesQuery'
import { ManageUserRole } from '@/types/manageUserRole'
import { INotation } from '@/enums/iNotation'
import appInsights from '@/lib/applicationInsights'

export async function getScorecardsBySubmissionGroups(
  measureResultsService: MeasureResultsService,
  scorecardView: ScorecardView,
  organizationId: string,
  isPartner: boolean,
  startDate: Dayjs,
  endDate: Dayjs,
  primaryMeasureType: PrimaryMeasureTypeConstants,
  scorecardDisplayType: string,
  submissionGroupIds: string[],
  measureIdentifiers: string[],
  selectedOrganizations: Organization[],
  partnerOrganizations: Organization[],
  accessToken: string,
  userId: string,
  hasLimitedAccess: boolean,
  measureIdentifier?: string
): Promise<HospitalSummary[] | ScorecardResultByHospital[]> {
  let results: HospitalSummary[] | ScorecardResultByHospital[] = []
  let type2Results: HospitalSummary[] | ScorecardResultByHospital[] = []

  let userRoles: ManageUserRole[] = []
  let canAccessibleRole: string[] = []

  if (hasLimitedAccess) {
    userRoles = await manageUserRolesQuery(measureResultsService, {
      organizationId: organizationId,
      userId: userId,
      isPartner: isPartner,
      organizationType: EntityOrganizationTypeConstants.SubmissionGroupLevel,
    })

    if (userRoles.length === 0 || !userRoles.some((x) => x.canAccess)) {
      results.push({
        note: 'You do not have access to view practice and provider information. Please contact an administrator to get access',
      })

      return results
    }

    canAccessibleRole = userRoles
      .filter((x) => x.canAccess)
      .map((x) => x.entitiesId)
      .filter((x): x is string => x != null && x !== undefined)
  }

  const measureList: OrganizationPaidMeasure[] = []

  let parameters: SimplifiedParameter[] = []
  const citcParameterService = new CitCParametersService(accessToken)

  if (isPartner) {
    parameters =
      await citcParameterService.getPartnerParametersAsync(organizationId)
  } else {
    parameters =
      await citcParameterService.getOrganizationParametersAsync(organizationId)
  }

  const organizationPaidMeasures = await getOrganizationPaidMeasuresQuery(
    measureResultsService,
    startDate,
    endDate,
    primaryMeasureType,
    accessToken,
    parameters,
    organizationId
  )

  measureList.push(
    ...organizationPaidMeasures.sort((a, b) =>
      a.measureName.localeCompare(b.measureName)
    )
  )

  appInsights.trackEvent({
    name: 'result of getScorecardsBySubmissionGroups.measureList',
    properties: {
      measureList,
    },
  })

  const periodList = DateRange.getColumnIntervalsByCategory(
    scorecardView,
    startDate,
    endDate
  )

  const admMeasures = await measureResultsService.getAllMeasures(organizationId)

  appInsights.trackEvent({
    name: 'result of getScorecardsBySubmissionGroups.admMeasures',
    properties: {
      admMeasures,
    },
  })

  if (isPartner) {
    const entities = await measureResultsService.getEntities({
      organizationId,
      isPartner,
    })

    const organizationIds = partnerOrganizations.map((x) => x.organizationId!)
    organizationIds.push('ALLORGANIZATION')

    const options: ECMeasureResultOptions = {
      organizationId,
      periodType: scorecardView,
      submissionGroupId: organizationIds,
      startDate,
      endDate,
      isPartner: isPartner,
      isSubmissionGroupLevel: true,
      measureIdentifier: measureIdentifier ?? '',
    }

    const aggregateResultsResponse =
      await measureResultsService.getECMeasureResults(options)
    let measureResultsForOrgs =
      aggregateResultsResponse?.ecMeasureResultSummaryList!

    if (scorecardDisplayType === 'Hospital') {
      const orgGroupResults = measureResultsForOrgs
        .filter((r) => r.performance !== null)
        .map((r) => {
          return {
            groupName: partnerOrganizations.find(
              (x) => x.organizationId === r.sourceContainerIdentifier
            )?.organizationName!,
            groupIdentifier: partnerOrganizations.find(
              (x) => x.organizationId === r.sourceContainerIdentifier
            )?.organizationId!,
            measureID: r.measureGUID!,
            entityName: r.entityName,
          }
        })
        .reduce(
          (acc, result) => {
            const key = result.groupIdentifier + '|' + result.groupName
            if (!acc[key]) acc[key] = []
            acc[key].push(result)
            return acc
          },
          {} as Record<
            string,
            {
              groupName: string
              groupIdentifier: string
              measureID: string
              entityName: string
            }[]
          >
        )

      for (const org of selectedOrganizations) {
        let combinedGroupName = entities.find(
          (e) =>
            e.entityName === EntityTypeConstants.RenderingProvider &&
            e.organizationTypeCode ===
              EntityOrganizationTypeConstants.CombinedGroupLevel &&
            e.sourceContainerIdentifier.startsWith(org.organizationId!)
        )?.entityName

        if (!combinedGroupName)
          combinedGroupName = entities.find(
            (e) =>
              e.entityName === EntityTypeConstants.RenderingProvider &&
              e.organizationTypeCode ===
                EntityOrganizationTypeConstants.SubmissionGroupLevel &&
              e.sourceContainerIdentifier.startsWith(org.organizationId!)
          )?.entityName

        if (!combinedGroupName) combinedGroupName = org.organizationName!

        const group =
          orgGroupResults[org.organizationId! + '|' + org.organizationName!]

        appInsights.trackEvent({
          name: 'result of getScorecardsBySubmissionGroups.group',
          properties: {
            group,
          },
        })

        let summaryCount = 0
        if (group) {
          if (measureIdentifiers.length > 0)
            summaryCount = [
              ...new Set(
                group
                  .filter((x) => measureIdentifiers.includes(x.measureID))
                  .map((m) => m.measureID)
              ),
            ].length
          else summaryCount = [...new Set(group.map((m) => m.measureID))].length
        }

        const myObj: Partial<HospitalSummary> = {}

        myObj.id = org.organizationId!
        myObj.name = combinedGroupName
        myObj.summary = `${summaryCount} Measures`
        myObj.scorecardView = scorecardView
        myObj.sortId = 1
        myObj.isEmptyIndicator = summaryCount === 0
        type2Results.push(myObj)
      }
    } else {
      const measure = organizationPaidMeasures.find(
        (x) => x.measureIdentifier === measureIdentifier
      )

      const admMeasureDetails = admMeasures.find(
        (x) => x.MedisolvMeasureId === measureIdentifier
      )

      const trendName =
        INotation[measure?.iNotationName! as unknown as keyof typeof INotation]

      const sourceContainerIdentifiers = [
        ...new Set(
          measureResultsForOrgs.map((x) =>
            x.sourceContainerIdentifier.replace('_EC', '')
          )
        ),
      ]
      const orgWithEmptyResults = selectedOrganizations
        .map((org) => org.organizationId!)
        .filter((x) => !sourceContainerIdentifiers.includes(x))

      if (orgWithEmptyResults) {
        orgWithEmptyResults.forEach((orgId) => {
          measureResultsForOrgs.push({
            startDate: dayjs(0),
            endDate: dayjs(0),
            denominator: null,
            performance: null,
            sourceContainerIdentifier: orgId,
          } as ECMeasureResultSummary)
        })
      }
      const groupedMeasureResultsForAllOrgs = measureResultsForOrgs.reduce(
        (groups, item) => {
          if (!groups[item.sourceContainerIdentifier]) {
            groups[item.sourceContainerIdentifier] = []
          }
          groups[item.sourceContainerIdentifier]?.push(item)
          return groups
        },
        {} as Record<string, ECMeasureResultSummary[]>
      )

      for (const [key, measureRates] of Object.entries(
        groupedMeasureResultsForAllOrgs
      )) {
        let combinedGroupName =
          entities.find(
            (x) =>
              x.entityTypeName === EntityTypeConstants.RenderingProvider &&
              x.organizationTypeCode ===
                EntityOrganizationTypeConstants.CombinedGroupLevel &&
              x.sourceContainerIdentifier.startsWith(key)
          )?.entityName ??
          entities.find(
            (x) =>
              x.entityTypeName === EntityTypeConstants.RenderingProvider &&
              x.organizationTypeCode ===
                EntityOrganizationTypeConstants.SubmissionGroupLevel &&
              x.sourceContainerIdentifier.startsWith(key)
          )?.entityName

        const organization = partnerOrganizations.find((x) =>
          key.startsWith(x.organizationId!)
        )

        if (!combinedGroupName) {
          combinedGroupName = organization?.organizationName ?? ''
        }

        let isEmptyIndicator = true

        const myObj: Partial<ScorecardResultByHospital> = {}

        myObj.id = organization?.organizationId!
        myObj.name = combinedGroupName

        const detailList: DetailsList[] = []
        const scorecardDetails: ScorecardDetails[] = []
        for (const item of periodList) {
          const span = item.replace('-', '_') as
            | `Q${number}_${number}`
            | `CY_${number}`
            | `${Capitalize<string>}_${number}`

          const period = DateRange.getDateFromDateRangeSpan(scorecardView, span)

          if (!admMeasureDetails || admMeasureDetails.NullRate) {
            myObj[span] = '-'
            detailList.push({ performance: null, denominator: null })
            continue
          }

          const measureResultItem = measureRates.find(
            (x) =>
              dayjs(x.startDate).isSame(period.startDate, 'day') &&
              dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
          )

          if (measureResultItem) {
            detailList.push({
              performance: measureResultItem.performance,
              denominator: admMeasureDetails?.NullDenominator
                ? null
                : measureResultItem.denominator,
            })

            myObj[span] =
              measureResultItem.performance === -1 ||
              measureResultItem.performance === null
                ? '-'
                : measureResultItem.performance === 100
                  ? measureResultItem.performance.toString()
                  : measureResultItem.performance?.toFixed(2)

            if (measureResultItem.performance !== null) {
              isEmptyIndicator = false
            }

            let scorecard = getScorecardDetails(
              measureResultItem,
              admMeasureDetails!
            )
            scorecard.id = scorecardDetails.length
            scorecard.columnName = span
            scorecard.rateTitle =
              measure?.subTypeName === 'Rate' ? 'Percentage' : 'Median-Time'
            scorecardDetails.push(scorecard)
          } else {
            myObj[span] = '-'
            let scorecard: ScorecardDetails = {
              id: scorecardDetails.length,
              columnName: span,
              performance: Performance.NoData,
              goal: null,
            }
            scorecardDetails.push(scorecard)
            detailList.push({ performance: null, denominator: null })
          }
        }

        const trendCSS = TrendCSSHelper.getTrendSlopeCss(
          detailList,
          trendName,
          scorecardView
        )

        myObj.trendCss = trendCSS
        myObj.measureDescription = measure?.measureDescription.trim()
        myObj.friendlyName = measure?.measureFriendlyName
        myObj.subDomain = measure?.subDomainName
        myObj.type = measure?.typeName
        myObj.domain = measure?.domainName
        myObj.cmsId = measure?.cMSId ?? '-'
        myObj.subType = measure?.subTypeName
        myObj.application = measure?.applicationName
        myObj.programName =
          measure?.programName === null ? '-' : measure?.programName
        myObj.measureIdentifier = measure?.measureIdentifier
        myObj.isEmptyIndicator = isEmptyIndicator
        myObj.inotation = trendName

        type2Results.push(myObj)
      }
    }
  }

  let subOrgIds: string[] = []

  if (isPartner) {
    subOrgIds = submissionGroupIds
      .map((x) => x.split('~')[0])
      .filter((x) => x !== undefined && x !== null)
  } else {
    subOrgIds.push(...submissionGroupIds)
  }

  let submissionGroups = await getSubmissionGroupsByOrganizationQuery(
    accessToken,
    measureResultsService,
    userId,
    {
      startDate: startDate,
      endDate: endDate,
      organizationId: organizationId,
      isPartner: isPartner,
      measureType: primaryMeasureType,
    }
  )

  const measure = organizationPaidMeasures.find(
    (x) => x.measureIdentifier === measureIdentifier
  )

  const measureType =
    scorecardDisplayType === 'Measure'
      ? measure?.applicationName === 'Registry Measures'
        ? 'Q'
        : 'E'
      : 'Q'

  if (measureType === 'Q') {
    let QSubmissionGroups =
      await measureResultsService.getSubmissionGroupsByOrganizationByMeasureType(
        {
          organizationId: organizationId,
          isPartner: isPartner,
          measureType: measureType,
        }
      )
    subOrgIds.push(...QSubmissionGroups.map((x) => x.submissionGroupId))
  }

  if (submissionGroupIds.length === 0 || submissionGroupIds.includes('*')) {
    subOrgIds = submissionGroups.map((x) => x.submissionGroupId)
  } else {
    submissionGroups = submissionGroups.filter((x) =>
      submissionGroupIds.includes(x.submissionGroupId)
    )

    subOrgIds = submissionGroups.map((x) => x.submissionGroupId)
  }

  if (hasLimitedAccess) {
    subOrgIds = subOrgIds.filter((x) => canAccessibleRole.includes(x))
    submissionGroups = submissionGroups.filter((x) =>
      canAccessibleRole.includes(x.submissionGroupId)
    )
  }

  const options: ECMeasureResultOptions = {
    organizationId: organizationId,
    periodType: scorecardView,
    submissionGroupId: subOrgIds,
    startDate: startDate,
    endDate: endDate,
    isPartner: isPartner,
    isSubmissionGroupLevel: true,
    measureIdentifier: measureIdentifier ?? '',
  }

  const aggregateResultsResponse =
    await measureResultsService.getECMeasureResults(options)
  const measureResults =
    aggregateResultsResponse?.ecMeasureResultSummaryList ?? []

  if (measureResults.length === 0) return results

  if (scorecardDisplayType === 'Hospital') {
    const measureResultsGroups = measureResults
      .filter((r) => r.performance !== null)
      .map((r) => {
        return {
          groupName: r.entityName,
          groupIdentifier: r.entityCode,
          measureID: r.measureGUID!,
        }
      })
      .reduce(
        (acc, result) => {
          const key = result.groupIdentifier + '|' + result.groupName
          if (!acc[key]) acc[key] = []
          acc[key].push(result)
          return acc
        },
        {} as Record<
          string,
          {
            groupName: string
            groupIdentifier: string
            measureID: string
          }[]
        >
      )

    submissionGroups.forEach((submissionGroup) => {
      if (
        !hasLimitedAccess ||
        canAccessibleRole.includes(submissionGroup.submissionGroupId)
      ) {
        const group =
          measureResultsGroups[
            submissionGroup.submissionGroupId! +
              '|' +
              submissionGroup.submissionGroupName!
          ]

        let summaryCount = 0
        if (group) {
          if (
            measureIdentifiers.length > 0 &&
            !measureIdentifiers.includes('*')
          )
            summaryCount = [
              ...new Set(
                group
                  .filter((x) => measureIdentifiers.includes(x.measureID))
                  .map((m) => m.measureID)
              ),
            ].length
          else summaryCount = [...new Set(group.map((m) => m.measureID))].length
        }

        const myObj: Partial<HospitalSummary> = {}

        myObj.id = submissionGroup.submissionGroupId
        myObj.name = submissionGroup.submissionGroupName
        myObj.summary = `${summaryCount} Measures`
        myObj.scorecardView = scorecardView
        myObj.sortId = 1
        myObj.isEmptyIndicator = summaryCount === 0
        results.push(myObj)
      }
    })
  } else {
    const emptySubmissionGroups = subOrgIds.filter(
      (id) => !measureResults.some((result) => result.entityCode === id)
    )

    if (emptySubmissionGroups.length !== 0) {
      measureResults.push(
        ...emptySubmissionGroups.map((submissionGroupId) => {
          const submissionGroup = submissionGroups.find(
            (x) => x.submissionGroupId === submissionGroupId
          )

          return {
            entityCode: submissionGroupId,
            entityName: submissionGroup?.submissionGroupName ?? '',
            startDate: dayjs(0),
            endDate: dayjs(0),
            denominator: null,
            performance: null,
          } as ECMeasureResultSummary
        })
      )
    }

    const measure = organizationPaidMeasures.find(
      (x) => x.measureIdentifier === measureIdentifier
    )

    const admMeasureDetails = admMeasures.find(
      (x) => x.MedisolvMeasureId === measureIdentifier
    )

    const trendName =
      INotation[measure?.iNotationName! as unknown as keyof typeof INotation]

    for (const [entityCode, measureRates] of Object.entries(
      measureResults.reduce<Record<string, ECMeasureResultSummary[]>>(
        (groups, item) => {
          if (!groups[item.entityCode]) {
            groups[item.entityCode] = []
          }
          groups[item.entityCode]?.push(item)
          return groups
        },
        {}
      )
    )) {
      const submissionGroup = submissionGroups.find(
        (x) => x.submissionGroupId === entityCode
      )

      if (!submissionGroup) continue

      let isEmptyIndicator = true

      const myObj: Partial<ScorecardResultByHospital> = {}

      myObj.id = submissionGroup.submissionGroupId
      myObj.name = submissionGroup.submissionGroupName

      const detailList: DetailsList[] = []
      const scorecardDetails: ScorecardDetails[] = []
      for (const item of periodList) {
        const span = item.replace('-', '_') as
          | `Q${number}_${number}`
          | `CY_${number}`
          | `${Capitalize<string>}_${number}`

        const period = DateRange.getDateFromDateRangeSpan(scorecardView, span)

        if (!admMeasureDetails || admMeasureDetails.NullRate) {
          myObj[span] = '-'
          detailList.push({ performance: null, denominator: null })
          continue
        }

        const measureResultItem = measureRates.find(
          (x) =>
            dayjs(x.startDate).isSame(period.startDate, 'day') &&
            dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
        )

        if (measureResultItem) {
          detailList.push({
            performance: measureResultItem.performance,
            denominator: admMeasureDetails?.NullDenominator
              ? null
              : measureResultItem.denominator,
          })

          myObj[span] =
            measureResultItem.performance === -1 ||
            measureResultItem.performance === null
              ? '-'
              : measureResultItem.performance === 100
                ? measureResultItem.performance.toString()
                : measureResultItem.performance?.toFixed(2)

          if (measureResultItem.performance !== null) {
            isEmptyIndicator = false
          }

          let scorecard = getScorecardDetails(
            measureResultItem,
            admMeasureDetails!
          )
          scorecard.id = scorecardDetails.length
          scorecard.columnName = span
          scorecard.rateTitle =
            measure?.subTypeName === 'Rate' ? 'Percentage' : 'Median-Time'
          scorecardDetails.push(scorecard)
        } else {
          myObj[span] = '-'
          let scorecard: ScorecardDetails = {
            id: scorecardDetails.length,
            columnName: span,
            performance: Performance.NoData,
            goal: null,
          }
          scorecardDetails.push(scorecard)
          detailList.push({ performance: null, denominator: null })
        }
      }

      const trendCSS = TrendCSSHelper.getTrendSlopeCss(
        detailList,
        trendName,
        scorecardView
      )

      myObj.scorecardDetailsList = scorecardDetails
      myObj.trendCss = trendCSS
      myObj.measureDescription = measure?.measureDescription.trim()
      myObj.friendlyName = measure?.measureFriendlyName
      myObj.subDomain = measure?.subDomainName
      myObj.type = measure?.typeName
      myObj.domain = measure?.domainName
      myObj.cmsId = measure?.cMSId ?? '-'
      myObj.subType = measure?.subTypeName
      myObj.application = measure?.applicationName
      myObj.programName =
        measure?.programName === null ? '-' : measure?.programName
      myObj.measureIdentifier = measure?.measureIdentifier
      myObj.isEmptyIndicator = isEmptyIndicator

      results.push(myObj)
    }
  }

  type2Results.sort((a, b) => a.name!.localeCompare(b.name!))
  results.sort((a, b) => a.name!.localeCompare(b.name!))
  results.unshift(...type2Results)

  return results
}
