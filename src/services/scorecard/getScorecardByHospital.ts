import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { ScorecardView } from '@/enums/scorecardView'
import { OrganizationPaidMeasure } from '@/types/organizationPaidMeasure'
import {
  HospitalSummary,
  ScorecardResultByHospital,
} from '@/types/scorecards/scorecards'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { SubOrganization } from '@/types/subOrganization'
import dayjs, { Dayjs } from 'dayjs'
import CitCParametersService from '../citc/citcParameters'
import { getOrganizationPaidMeasuresQuery } from '@/prisma/getOrganizationPaidMeasuresQuery'
import MeasureResultsService from '../measureResults'
import { MeasureResultOptions } from '@/types/measureResultOptions'
import { config } from '@/config'
import { MeasureResultSummary } from '@/types/measureResultSummary'
import { ScorecardDetails } from '@/types/scorecards/scorecardDetails'
import { getScorecardDetails } from './baseScorecardProvider'
import { Performance } from '@/enums/performance'
import { DetailsList } from '@/types/detailsList'
import TrendCSSHelper from '@/lib/trendCSSHelper'
import { DateRange } from '@/lib/dateRange'
import { INotation } from '@/enums/iNotation'
import CitCOrganizationService from '@/services/citc/citCOrganizations'
import appInsights from '@/lib/applicationInsights'

export async function getScorecardByHospital(
  measureResultsService: MeasureResultsService,
  scorecardView: ScorecardView,
  organizationId: string,
  isPartner: boolean,
  startDate: Dayjs,
  endDate: Dayjs,
  primaryMeasureType: PrimaryMeasureTypeConstants,
  scorecardDisplayType: string,
  subOrgnizations: SubOrganization[],
  measureIdentifiers: string[],
  accessToken: string,
  measureIdentifier?: string
): Promise<unknown[]> {
  const measureList: OrganizationPaidMeasure[] = []

  let parameters: SimplifiedParameter[] = []
  const citcParameterService = new CitCParametersService(accessToken)
  const citcOrganizationService = new CitCOrganizationService(accessToken)
  let isCombinedGroup = false
  if (isPartner) {
    parameters =
      await citcParameterService.getPartnerParametersAsync(organizationId)
  } else {
    parameters =
      await citcParameterService.getOrganizationParametersAsync(organizationId)
    //Get the sub-organizations (irrespective of the current user's access),
    const subOrgs =
      await citcOrganizationService.getSubOrganizationsByOrganizationId(
        organizationId
      )
    isCombinedGroup = subOrgs.length > 1
  }

  // Get organization paid measures using MeasureResultsService
  const organizationPaidMeasures = await getOrganizationPaidMeasuresQuery(
    measureResultsService,
    startDate,
    endDate,
    primaryMeasureType,
    accessToken,
    parameters,
    organizationId
  )

  appInsights.trackEvent({
    name: 'result of getScorecardByHospital.organizationPaidMeasures',
    properties: {
      organizationPaidMeasures,
    },
  })

  measureList.push(
    ...organizationPaidMeasures.sort((a, b) =>
      a.measureName.localeCompare(b.measureName)
    )
  )

  appInsights.trackEvent({
    name: 'result of getScorecardByHospital.measureList',
    properties: {
      measureList,
    },
  })

  const dateRangeSpan = DateRange.getColumnIntervalsByCategory(
    scorecardView,
    startDate,
    endDate
  )

  const subOrganizationIds = subOrgnizations.map((x) => x.subOrganizationId!)

  const admMeasures = await measureResultsService.getAllMeasures(organizationId)

  appInsights.trackEvent({
    name: 'result of getScorecardByHospital.admMeasures',
    properties: {
      admMeasures,
    },
  })

  const options: MeasureResultOptions = {
    organizationId,
    periodType: scorecardView,
    subOrganizationId: subOrganizationIds,
    startDate,
    endDate,
    isPartner: isPartner,
    isCombinedGroup,
    measureIdentifier: measureIdentifier,
  }

  const aggregateResultsResponse =
    await measureResultsService.getMeasureResults(options)
  const measureRates = aggregateResultsResponse?.measureResultSummaryList?.map(
    (rate) => ({
      ...rate,
      measureGUID: rate.measureGUID.toLocaleLowerCase(),
    })
  )

  appInsights.trackEvent({
    name: 'result of getScorecardByHospital.measureRates',
    properties: {
      measureRates,
    },
  })

  if (scorecardDisplayType == 'Hospital') {
    let results: HospitalSummary[] = []

    const hospitalResults = measureRates
      ?.filter(
        (mr) =>
          mr.performance !== null &&
          dayjs(mr.startDate) >= startDate &&
          dayjs(mr.endDate) <= endDate.add(1, 'day')
      )
      .map((mr) => {
        const orgMeasure = organizationPaidMeasures.find(
          (org) =>
            org.measureIdentifier.toLowerCase() === mr.measureGUID.toLowerCase()
        )
        const subOrg = subOrgnizations.find(
          (sub) => sub.subOrganizationId === mr.entityCode
        )

        return subOrg && orgMeasure
          ? {
              hospitalName: subOrg.subOrganizationName!,
              hospitalId: subOrg.subOrganizationId!,
              measureID: mr.measureGUID!,
            }
          : null
      })
      .filter((x) => x !== null)
      .reduce(
        (acc, curr) => {
          const key = curr.hospitalId!
          if (!acc[key]) {
            acc[key] = []
          }
          acc[key].push(curr)
          return acc
        },
        {} as Record<
          string,
          { hospitalName: string; hospitalId: string; measureID: string }[]
        >
      )

    appInsights.trackEvent({
      name: 'result of getScorecardByHospital.hospitalResults',
      properties: {
        hospitalResults,
      },
    })

    let i = 0

    const ccns = subOrgnizations
      .filter((x) => x.subOrganizationName?.includes('(CCN)'))
      .sort((a, b) =>
        a.subOrganizationName!.localeCompare(b.subOrganizationName!)
      )
      .map((x) => {
        return {
          subOrgnization: x,
          index: ++i,
        }
      })

    const nonCCNs = subOrgnizations
      .filter((x) => !x.subOrganizationName?.includes('(CCN)'))
      .sort((a, b) =>
        a.subOrganizationName!.localeCompare(b.subOrganizationName!)
      )
      .map((x) => {
        return {
          subOrgnization: x,
          index: ++i,
        }
      })

    const orderedSubOrgs = ccns.concat(nonCCNs)

    orderedSubOrgs.forEach((x) => {
      try {
        const hospitalEntry =
          hospitalResults![x.subOrgnization.subOrganizationId!]
        let summaryCount = 0

        if (
          hospitalEntry &&
          measureIdentifiers.length > 0 &&
          !measureIdentifiers?.includes('*')
        ) {
          const measureIds = organizationPaidMeasures
            .filter((x) => measureIdentifiers.includes(x.measureIdentifier))
            .map((x) => x.measureIdentifier)

          summaryCount = [
            ...new Set(
              hospitalEntry
                .filter((x) => measureIds.includes(x.measureID))
                .map((x) => x.measureID)
            ),
          ].length
        }
        if (hospitalEntry && measureIdentifiers.includes('*')) {
          summaryCount = [...new Set(hospitalEntry.map((m) => m.measureID))]
            .length
        }

        const myObj: Partial<HospitalSummary> = {}

        myObj.id = x.subOrgnization.subOrganizationId!
        myObj.name = x.subOrgnization.subOrganizationName!
        myObj.summary = `${summaryCount} Measures`
        myObj.scorecardView = scorecardView
        myObj.sortId = x.index

        results.push(myObj)
      } catch (error) {
        throw error
      }
    })

    return results
  }

  let results: ScorecardResultByHospital[] = []
  const measure = organizationPaidMeasures.find(
    (x) => x.measureIdentifier === measureIdentifier
  )

  const isRatioMeasure = config.customMeasures.ratioMeasures.some(
    (x) => x.toLowerCase() === measure?.measureIdentifier.toLowerCase()
  )

  const isVolumeMeasure = config.customMeasures.volumeMeasures.some(
    (x) => x.toLowerCase() === measure?.measureIdentifier.toLowerCase()
  )

  const entityGroups = measureRates?.reduce(
    (acc, rate) => {
      const key = rate.entityCode!
      if (!acc[key]) acc[key] = []
      acc[key].push(rate)
      return acc
    },
    {} as { [key: string]: MeasureResultSummary[] }
  )

  const hospitalgrpsKeys = Object.entries(entityGroups!).map(
    ([key, group]) => key
  )

  const emptyResultHospitals = subOrganizationIds.filter(
    (x) => !hospitalgrpsKeys.includes(x)
  )

  if (emptyResultHospitals.length > 0) {
    emptyResultHospitals.forEach((x) => {
      measureRates?.push({
        ...measureRates[0]!,
        startDate: new Date(),
        endDate: new Date(),
        denominator: null,
        performance: null,
        sourceContainerIdentifier: x,
        measureGUID: measure?.measureIdentifier!,
        entityCode: x,
      })
    })
  }

  const hospitalGroups = measureRates?.reduce(
    (acc, rate) => {
      const key = rate.entityCode!
      if (!acc[key]) acc[key] = []
      acc[key].push(rate)
      return acc
    },
    {} as { [key: string]: MeasureResultSummary[] }
  )

  for (const [key, group] of Object.entries(hospitalGroups!)) {
    const admMeasureDetails = admMeasures.find(
      (x) => (x.MedisolvMeasureId = key)
    )

    const myobj: Partial<ScorecardResultByHospital> = {}

    myobj.name = subOrgnizations.find(
      (x) => x.subOrganizationId == key
    )?.subOrganizationName!
    myobj.id = key
    myobj.scorecardView = scorecardView

    let isEmptyIndicator = true
    const scorecardDetails: ScorecardDetails[] = []
    for (const item of dateRangeSpan) {
      const span = item.replace('-', '_') as
        | `Q${number}_${number}`
        | `CY_${number}`
        | `${Capitalize<string>}_${number}`

      const period = DateRange.getDateFromDateRangeSpan(scorecardView, span)

      const rate = measureRates?.find(
        (x) =>
          dayjs(x.startDate).isSame(period.startDate, 'day') &&
          dayjs(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
      )

      if (rate) {
        if (isRatioMeasure) {
          myobj[span] =
            rate.performance === -1 || rate.performance === null
              ? '-'
              : rate.performance === 100
                ? `${rate.numeratorValue}/${rate.denominatorValue}</br>${rate.performance}`
                : `${rate.numeratorValue}/${rate.denominatorValue}</br>${rate.performance?.toFixed(2)}`
        } else {
          myobj[span] =
            rate.performance === -1 || rate.performance === null
              ? '-'
              : rate.performance === 100
                ? rate.performance.toString()
                : rate.performance?.toFixed(2)
        }

        if (rate.performance !== null) {
          isEmptyIndicator = false
        }

        let scorecard = getScorecardDetails(
          rate,
          admMeasureDetails!,
          isVolumeMeasure
        )
        scorecard.id = scorecardDetails.length
        scorecard.columnName = span
        scorecard.rateTitle = isRatioMeasure
          ? 'Ratio'
          : measure?.subTypeName == 'Rate'
            ? 'Percentage'
            : 'Median-Time'
        scorecardDetails.push(scorecard)
      } else {
        myobj[span] = '-'
        let scorecard: ScorecardDetails = {
          id: scorecardDetails.length,
          columnName: span,
          performance: Performance.NoData,
          goal: null,
        }
        scorecardDetails.push(scorecard)
      }
    }

    const trendName =
      INotation[measure?.iNotationName! as unknown as keyof typeof INotation]

    const detailList = scorecardDetails.map((x) => {
      return {
        performance: x.rate,
        denominator: x.denominatorCount,
      } as DetailsList
    })

    const trendCSS = TrendCSSHelper.getTrendSlopeCss(
      detailList,
      trendName,
      scorecardView
    )

    myobj.trendCss = trendCSS
    myobj.scorecardDetailsList = scorecardDetails
    myobj.scorecardView = scorecardView
    myobj.isEmptyIndicator = isEmptyIndicator
    myobj.smallestInterval = admMeasureDetails?.SmallestInterval ?? 'M'
    myobj.measureIdentifier = measure?.measureIdentifier
    myobj.measureDescription = measure?.measureDescription.trim()!
    myobj.friendlyName = measure?.measureFriendlyName!
    myobj.subDomain = measure?.subDomainName!
    myobj.type = measure?.typeName!
    myobj.domain = measure?.domainName!
    myobj.cmsId = measure?.cMSId ?? '-'
    myobj.subType = measure?.subTypeName!
    myobj.application = measure?.applicationName!
    myobj.programName = measure?.programName ? measure?.programName : '-'
    myobj.inotation = trendName

    results.push(myobj)
  }

  return results
}
