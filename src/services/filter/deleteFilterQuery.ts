import { StatusResponse } from '@/types/statusResponse'
import { deleteSavedFilter } from '../savedFilters/deleteSavedFilter'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'

type DeleteFilterQuery = {
  filterId: string
  organizationId: string
  isPartner: boolean
  userId: string
}

export const deleteFilterQuery = async (
  request: DeleteFilterQuery
): Promise<StatusResponse> => {
  const savedFilterTableStorage = new AzureTableStorageWrapper(
    StorageTables.SavedFilters
  )

  await deleteSavedFilter(
    savedFilterTableStorage,
    request.userId,
    request.filterId
  )

  return {
    message: 'Filter deleted successfully!!',
    success: true,
  }
}
