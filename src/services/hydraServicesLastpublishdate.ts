

import { Service } from './service'
import { env } from '@/env'
import { tryCache } from '@/lib/redis'
import { MSPApiResult } from '@/types/mspApiResult'

class HydraServices extends Service {
    constructor(accessToken: string) {
        super(accessToken)
    }

    async getLastpublishdate(): Promise<MSPApiResult[]> {
        return (
            (
                await this.fetchWithType<MSPApiResult[]>(`${env.NEXT_PUBLIC_HYDRA_SERVER_URL}/api/reporting/lastpublishdate`)
            ).data ?? []
        )
    }

}

export default HydraServices