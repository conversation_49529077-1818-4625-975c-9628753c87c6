import { ExpansionConfiguration } from '@/types/expansionConfiguration'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'

export const getAllExpansionConfigs = async (
  tableStorage: AzureTableStorageWrapper,
  organizationId: string
) => {
  // TODO: need migration?
  return tableStorage.queryEntities<ExpansionConfiguration>(
    tableStorage.generateODataQuery({
      PartitionKey: organizationId,
    })
  )
}
