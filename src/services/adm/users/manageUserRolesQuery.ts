import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import type { ManageUserRolesQuery } from '@/enums/manageUserRolesQuery'
import { StorageTables } from '@/enums/storageTables'
import { env } from '@/env'
import { tryCache } from '@/lib/redis'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { getManageUserRoles } from '@/services/manageUserRoles/getManageUserRoles'
import { getProvidersBySubmissionGroupAsync } from '@/services/providers/getProvidersBySubmissionGroupAsync'
import { ManageUserRole } from '@/types/manageUserRole'
import MeasureResultsService from '@/services/measureResults'

export const manageUserRolesQuery = async (
  measureResultsService: MeasureResultsService,
  request: ManageUserRolesQuery
) => {
  const result: ManageUserRole[] = []

  const cacheKey = `${request.organizationId}-manageUserRolesQuery-${JSON.stringify(request)}`

  return await tryCache(
    cacheKey,
    async () => {
      try {
        let entities = await measureResultsService.getEntities({
          organizationId: request.organizationId,
          isPartner: request.isPartner,
        })

        // Find entities matching the conditions
        entities = entities.filter(
          (x) =>
            x.sourceContainerIdentifier === request.organizationId &&
            x.organizationTypeCode === request.organizationType &&
            x.entityTypeName === EntityTypeConstants.RenderingProvider
        )

        if (!entities) return result

        const entitiesId = entities.map((entity) => entity.id.toString())

        if (
          request.organizationType ===
          EntityOrganizationTypeConstants.ProviderLevel
        ) {
          const providerResponse = await getProvidersBySubmissionGroupAsync(
            measureResultsService,
            request.organizationId,
            request.isPartner,
            request.submissionGroupId
          )
          const npis = providerResponse.map((x) => x.npi)
          const providerEntities = entities.filter((x) => npis.includes(x.code))

          entitiesId.push(
            ...providerEntities.map((entity) => entity.id.toString())
          )
        }

        let dbData: ManageUserRole[] = []

        dbData = await getManageUserRoles(
          new AzureTableStorageWrapper(StorageTables.ManageUserRoles),
          request.organizationId,
          request.userId,
          entitiesId
        )

        entities.forEach((entity) => {
          const existingRole = dbData.find(
            (role) => role.entitiesId === entity.id.toString()
          )

          result.push({
            organizationId: request.organizationId,
            userId: request.userId,
            entitiesId: entity.id.toString(),
            entityName: entity.entityDisplayName || '',
            entityCode: entity.code,
            canAccess: existingRole?.canAccess || false,
            canDrillDown: existingRole?.canDrillDown || false,
            organizationTypeCode: request.organizationType,
            partitionKey: existingRole?.organizationId!,
            rowKey: existingRole?.userId!,
          })
        })

        return result
      } catch (e) {
        // TODO: Log it
        return result
      }
    },
    Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
  )
}
