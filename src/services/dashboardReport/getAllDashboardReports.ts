import { DashboardReport } from '@/types/dashboardReport'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { MigrationConfig } from '@/types/migrationConfig'
import { migrateData } from '../migration/migrateData'
import { HUBPrismaClient } from '@/server/db'
import { upsertDashboardReport } from './upsertDashboardReport'

export const getAllDashboardReports = async (
  tableStorage: AzureTableStorageWrapper,
  organizationId: string,
  migrationConfig?: MigrationConfig
) => {
  // TODO: Enhancement* Add try/catch w/ app insights
  const existingReports = await tableStorage.queryEntities<DashboardReport>(
    tableStorage.generateODataQuery({
      PartitionKey: organizationId,
    })
  )

  if (migrationConfig && existingReports.length === 0) {
    // TODO: Enhancement* Add try/catch w/ app insights
    await migrateData({
      storage: tableStorage,
      migrationConfig,
      prismaClientType: 'hubClient',
      prismaQuery: (prismaClient: HUBPrismaClient) =>
        prismaClient.dashboardReport.findMany(),
      upsertFunction: async (storage, data) => {
        // TODO: Enhancement* Add try/catch w/ app insights
        // Use Promise.all to ensure all reports are migrated
        await Promise.all(
          data.map((report) =>
            upsertDashboardReport(storage, {
              partitionKey: organizationId,
              rowKey: report.Id.toString(),
              reportName: report.ReportName,
              sisenseDashboardId: report.SisenseDashboardId,
              reportType: report.ReportType,
              filterSettings: report.FilterSettings,
              status: report.Status ?? false,
              lastUpdatedBy: report.LastUpdatedBy,
              lastUpdatedDate: report.LastUpdatedDate,
            })
          )
        )
      },
    })
  }

  return tableStorage.queryEntities<DashboardReport>(
    tableStorage.generateODataQuery({
      PartitionKey: organizationId,
    })
  )
}
