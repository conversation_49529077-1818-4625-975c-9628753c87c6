import { staticpagesData } from './../../data/pages'
import { Pages } from '@/types/pages'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { GuidGenerator } from '@/lib/utils'
import { getAllUserPreferences } from '../userPreferences/getAllUserPreferences'
import { StorageTables } from '@/enums/storageTables'
import { migrateData } from '../migration/migrateData'
import { MigrationConfig } from '@/types/migrationConfig'
import { HUBPrismaClient } from '@/server/db'
import { upsertPlatformPages } from './upsertPlatformPages'
import appInsights from '@/lib/applicationInsights'

export const getPlatformPages = async (
  pagesTableStorage: AzureTableStorageWrapper,
  organizationId: string,
  hasDashboardAccess: boolean,
  userId: string,
  migrationConfig?: MigrationConfig
): Promise<Pages[]> => {
  let pages: Pages[] = []
  try {
    // TODO: Enhancement* Add try/catch w/ app insights
    const existingPages = await pagesTableStorage.queryEntities<Pages>(
      pagesTableStorage.generateODataQuery({
        PartitionKey: organizationId,
      })
    )

    if (migrationConfig && existingPages.length === 0) {
      // TODO: Enhancement* Add try/catch w/ app insights
      await migrateData({
        storage: pagesTableStorage,
        migrationConfig,
        prismaClientType: 'hubClient',
        prismaQuery: (prismaClient: HUBPrismaClient) =>
          prismaClient.pages.findMany({
            orderBy: {
              SortOrder: 'asc',
            },
          }),
        upsertFunction: async (storage, data) =>
          // TODO: Enhancement* Add try/catch w/ app insights
          await Promise.all(
            data.map((page) =>
              upsertPlatformPages(storage, {
                partitionKey: organizationId,
                rowKey: page.PageId,
                name: page.Name,
                ownerId: page.OwnerId,
                isPrimary: page.IsPrimary,
                routeOrExternalUrl: page.RouteOrExternalUrl,
                isExternal: page.IsExternal,
                sortOrder: page.SortOrder,
                isVisible: page.IsVisible,
                message: page.Message,
              })
            )
          ),
      })
    }

    pages = await pagesTableStorage.queryEntities<Pages>(
      pagesTableStorage.generateODataQuery({
        PartitionKey: organizationId,
      })
    )

    if (!pages.length) {
      //need to create records in azuretable
      pages = await Promise.all(
        staticpagesData.map(async (page) => {
          const pagedata: Pages = {
            partitionKey: `${organizationId}`,
            rowKey: GuidGenerator.standard(),
            name: page.name,
            isPrimary: page.isPrimary,
            isExternal: page.isExternal,
            isVisible:
              page.name === 'Measures' || page.name === 'Scorecards'
                ? true
                : false,
            message: page.message,
            ownerId: page.ownerId,
            routeOrExternalUrl: page.routeOrExternalUrl,
            sortOrder: page.sortOrder,
          }
          await pagesTableStorage.insertEntity(pagedata)
          return pagedata
        })
      )
    }

    const userPreferencesTableStorageProvider = new AzureTableStorageWrapper(
      StorageTables.UserPreferences
    )

    const [userPreference] = await getAllUserPreferences(
      userPreferencesTableStorageProvider,
      userId
    )

    if (userPreference != null) {
      pages = pages.map((page) => {
        // update visibility for dashboards
        if (
          (userPreference.dashboardEnabledOverride || hasDashboardAccess) &&
          page.name === 'dashboards'
        ) {
          return { ...page, isvisible: true }
        }

        // set the primary page based on user preference
        if (page.name === userPreference.defaultModuleId) {
          return { ...page, isprimary: true }
        }

        return page
      })
    } else {
      pages = pages.map((page) => {
        if (page.name === 'dashboards') {
          return { ...page, isvisible: hasDashboardAccess }
        }
        return page
      })
    }
  } catch (error) {
    appInsights.trackException({
      exception:
        error instanceof Error ? error : new Error('Error in getPlatformPages'),
      properties: {
        function: 'getPlatformPages',
        organizationId,
      },
    })

    // Return empty array as fallback
    return []
  }

  return pages
}
