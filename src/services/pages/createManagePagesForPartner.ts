import StorageService from '../storeage'
import { Pages } from '@/types/pages'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'

/** @deprecated - no longer in use? */
export const createManagePagesForPartner = async (
  partnerId: string,
  storageService: StorageService
): Promise<Pages[]> => {
  let pages: Pages[] = []

  const tableStorageProvider = new AzureTableStorageWrapper(
    StorageTables.ManagePages
  )

  let query = tableStorageProvider.generateODataQuery({
    PartitionKey: `${partnerId}`,
  })

  pages = await tableStorageProvider.queryEntities<Pages>(query)

  for (const x of pages) {
    x.isVisible = x.name === 'Measure' || x.name === 'Scorecard'
  }

  await storageService.writeBlobAsync(
    `report-configuration`,
    `ConfigurationsSettings/ManagePages/${partnerId}.json`,
    new TextEncoder().encode(JSON.stringify(pages))
  )

  return pages
}
