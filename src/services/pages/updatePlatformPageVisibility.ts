import { Pages } from '@/types/pages'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { env } from '@/env'
import { redisHelper } from '@/lib/redis'
import { StorageTables } from '@/enums/storageTables'

export const updatePlatformPageVisibility = async (
  organizationId: string,
  updatedpageconfig: any
): Promise<Pages[]> => {
  let pages: Pages[] = []
  try {
    const tableStorageProvider = new AzureTableStorageWrapper(
      StorageTables.ManagePages
    )

    let query = tableStorageProvider.generateODataQuery({
      PartitionKey: `${organizationId}`,
    })

    pages = await tableStorageProvider.queryEntities<Pages>(query)

    updatedpageconfig?.forEach(async (item: any) => {
      let data = pages.find((x) => x.rowKey == item.rowKey)
      if (data && item.IsVisible != data.isVisible) {
        data.isVisible = item.IsVisible
        tableStorageProvider.updateEntity(data)
      }
    })
  } catch {}

  // //removing all cached pages
  redisHelper.set(
    `${organizationId}-all-pagesfromtablestorage`,
    '',
    Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
  )
  return pages
}
