import { MigrationConfig } from '@/types/migrationConfig'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { ManageUserRole } from '@/types/manageUserRole'
import { migrateData } from '../migration/migrateData'
import { ADMPrismaClient } from '@/server/db'
import { upsertManageUserRole } from './upsertManageUserRole'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

const CHUNK_SIZE = 100 // Reduced chunk size to avoid OData query complexity limits

const chunkArray = (array: (bigint | string)[], chunkSize: number) => {
  const results = []
  for (let i = 0; i < array.length; i += chunkSize) {
    results.push(array.slice(i, i + chunkSize))
  }
  return results
}

export const getManageUserRoles = async (
  tableStorage: AzureTableStorageWrapper,
  organizationId: string,
  userId: string,
  entitiesIds: (bigint | string)[],
  migrationConfig?: MigrationConfig
) => {
  const chunks = chunkArray(entitiesIds, CHUNK_SIZE)

  // Check if migration already happened for this organization/user
  const existingRoles = await tableStorage.queryEntities<ManageUserRole>(
    tableStorage.generateODataQuery({
      PartitionKey: organizationId,
      userId,
      isChunk: false,
    })
  )

  // Only migrate if no roles exist yet
  if (migrationConfig && existingRoles.length === 0) {
    await migrateData({
      storage: tableStorage,
      migrationConfig,
      prismaClientType: 'admClient',
      prismaQuery: async (prismaClient: ADMPrismaClient) => {
        const results = await Promise.all(
          chunks.map((chunk) =>
            prismaClient.manageUserRoles.findMany({
              where: {
                OrganizationId: migrationConfig.organizationId,
                UserId: userId,
                EntitiesId: {
                  in: chunk.map((id) =>
                    typeof id === 'string' ? BigInt(id) : id
                  ),
                },
              },
            })
          )
        )

        return results.flat()
      },
      upsertFunction: async (storage, data) =>
        await Promise.all(
          data.map((role) =>
            upsertManageUserRole(storage, {
              partitionKey: role.OrganizationId,
              rowKey: role.Id.toString(),
              userId: role.UserId,
              entitiesId: role.EntitiesId,
              organizationId: role.OrganizationId,
              canAccess: role.CanAccess ?? false,
              canDrillDown: role.CanDrillDown ?? false,
            })
          )
        ),
    })
  }

  const queryPromises = chunks.map((chunk) =>
    tableStorage.queryEntities<ManageUserRole>(
      tableStorage.generateODataQuery({
        PartitionKey: organizationId,
        userId,
        entitiesId: {
          value: chunk,
        },
      })
    )
  )

  const results = await Promise.all(queryPromises)

  return results.flat()
}
