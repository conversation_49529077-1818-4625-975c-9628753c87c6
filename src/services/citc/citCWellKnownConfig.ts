import { env } from '@/env'

interface WellKnownConfig {
  revocation_endpoint: string
  end_session_endpoint: string
  token_endpoint: string
  userinfo_endpoint: string
  // Add other endpoints as needed
}

class CitCWellKnownConfig {
  private static instance: CitCWellKnownConfig
  private config: WellKnownConfig | null = null

  private constructor() {}

  public static getInstance(): CitCWellKnownConfig {
    if (!CitCWellKnownConfig.instance) {
      CitCWellKnownConfig.instance = new CitCWellKnownConfig()
    }
    return CitCWellKnownConfig.instance
  }

  private async fetchConfig(): Promise<WellKnownConfig> {
    const response = await fetch(env.NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG!, {
      cache: 'no-store', // Ensure we always get the latest config
    })
    if (!response.ok) {
      throw new Error(`Failed to fetch well-known config: ${response.status}`)
    }
    return await response.json()
  }

  public async getConfig(): Promise<WellKnownConfig> {
    if (!this.config) {
      this.config = await this.fetchConfig()
    }
    return this.config
  }

  private async getEndpoint(key: keyof WellKnownConfig): Promise<string> {
    const config = await this.getConfig()
    if (!config[key]) {
      throw new Error(`No ${key} in well-known config`)
    }
    return config[key] as string
  }

  // Endpoint getter methods
  public async getRevocationEndpoint(): Promise<string> {
    return this.getEndpoint('revocation_endpoint')
  }

  public async getEndSessionEndpoint(): Promise<string> {
    return this.getEndpoint('end_session_endpoint')
  }

  public async getTokenEndpoint(): Promise<string> {
    return this.getEndpoint('token_endpoint')
  }

  // API call methods
  public async revokeToken(token: string, tokenTypeHint: 'refresh_token' | 'access_token'): Promise<void> {
    const endpoint = await this.getRevocationEndpoint()
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        token,
        token_type_hint: tokenTypeHint,
        client_id: env.NEXT_PUBLIC_OIDC_CLIENT_ID,
        client_secret: env.NEXT_PUBLIC_OIDC_CLIENT_SECRET,
      }),
    })
    if (!response.ok) {
      throw new Error(`Failed to revoke token: ${response.status}`)
    }
  }

  public async getEndSessionUrl(idToken: string | null | undefined): Promise<string> {
    const endpoint = await this.getEndSessionEndpoint()
    const url = new URL(endpoint)
    if (idToken) {
      url.searchParams.append('id_token_hint', idToken)
    }
    url.searchParams.append('client_id', env.NEXT_PUBLIC_OIDC_CLIENT_ID)
    return url.toString()
  }

  public async tokenEndpoint(token: string,  grant_type: string): Promise<any> {
    const endpoint = await this.getTokenEndpoint()
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: env.NEXT_PUBLIC_OIDC_CLIENT_ID!,
        client_secret: env.NEXT_PUBLIC_OIDC_CLIENT_SECRET!,
        grant_type,
        refresh_token: token,
      }),
    })
    if (!response.ok) {
      throw new Error(`Failed to refresh token: ${response.status}`)
    }

    const tokenResponse = await response.json()
    return tokenResponse
  }
}

export default CitCWellKnownConfig