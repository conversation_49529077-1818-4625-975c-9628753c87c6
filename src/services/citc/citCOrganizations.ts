import { Service } from '../service'
import { env } from '@/env'
import appInsights from '@/lib/applicationInsights'
import { tryCache } from '@/lib/redis'
import type { App } from '@/types/app'
import { Group } from '@/types/group'
import type { Organization } from '@/types/organization'
import { SubOrganization } from '@/types/subOrganization'

interface citcSubOrganizationListOptions {
  organizationId: string
  includeAll: boolean
  includeDeleted: boolean
}

type CitcGroupsByOrganizationOptions = {
  organizationId: string
}

class CitCOrganizationService extends Service {
  constructor(accessToken: string) {
    super(accessToken)
  }

  async getOrganizationsByUser(userId: string) {
    const cacheKey = `${userId}-getOrganizationsByUser`
    return await tryCache(
      cacheKey,
      async () => {
        return (
          (
            await this.fetchWithType<Organization[]>(
              `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/organizations/GetByUser`,
              {
                cache: 'force-cache',
                next: { revalidate: 3600 },
              }
            )
          ).data ?? []
        )
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
  }

  async getOrganizationsApplications(organizationId: string) {
    const cacheKey = `${organizationId}-getOrganizationsApplications`
    return await tryCache(
      cacheKey,
      async () => {
        return (
          (
            await this.fetchWithType<App[]>(
              `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/organizations/${organizationId}/applications`
            )
          ).data ?? []
        )
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
  }

  async getOrganizationDetailsAsync(
    organizationId: string
  ) {
    const cacheKey = `${organizationId}-getOrganizationDetailsAsync`

    appInsights.trackEvent({
      name: 'executing getOrganizationDetailsAsync',
      properties: {
        organizationId,
      },
    })

    const result = await tryCache(
      cacheKey,
      async () => {
        return (
          (
            await this.fetchWithType<Organization>(
              `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/organizations/${organizationId}`
            )
          ).data ?? null
        )
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )

    appInsights.trackEvent({
      name: 'result of getOrganizationDetailsAsync',
      properties: {
        organizationId,
        result,
      },
    })

    return result
  }

  async getGroupsByOrganizationIdAsync(
    options: CitcGroupsByOrganizationOptions
  ) {
    return (
      (
        await this.fetchWithType<Group[]>(
          `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/groups/organization/${options.organizationId}`
        )
      ).data ?? []
    )
  }

  async getSubOrganizationsByOrganizationId(
    organizationId: string,
    includeAll = false
  ) {
    return await tryCache(
      `SubOrganizationsByOrganization.${organizationId}.${includeAll}`,
      () =>
        this.getSubOrganizationsByOrganizationIdWithOptions({
          organizationId: organizationId,
          includeAll: includeAll,
          includeDeleted: false,
        })
    )
  }
  async getSubOrganizationsByOrganizationIdWithOptions(
    options: citcSubOrganizationListOptions
  ) {
    const cacheKey = `${options.organizationId}-getSubOrganizationsByOrganizationIdWithOptions-${options.includeAll}-${options.includeDeleted}`
    return await tryCache(
      cacheKey,
      async () => {
        const url = options.includeAll
          ? options.includeDeleted
            ? `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/Organizations/${options.organizationId}/sub-organizations/all/with/removed`
            : `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/Organizations/${options.organizationId}/sub-organizations/all`
          : options.includeDeleted
            ? `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/Organizations/${options.organizationId}/sub-organizations/with/removed`
            : `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/Organizations/${options.organizationId}/sub-organizations`

        return (await this.fetchWithType<SubOrganization[]>(url)).data ?? []
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
  }
}

export default CitCOrganizationService
