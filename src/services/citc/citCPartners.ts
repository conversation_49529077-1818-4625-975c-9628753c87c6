import { Service } from '../service'
import { env } from '@/env'
import { tryCache } from '@/lib/redis'
import { type Partner } from '@/types/partner'

class CitCPartnersService extends Service {
  constructor(accessToken: string) {
    super(accessToken)
  }

  async getPartnersByUserAsync(userId: string) {
    const cacheKey = `${userId}-getPartnersByUserAsync`
    return await tryCache(
      cacheKey,
      async () => {
        return (
          (
            await this.fetchWithType<Partner[]>(
              `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/partners/userinfo/${userId}`
            )
          ).data ?? []
        )
      },
      Number(env.REDIS_CACHE_TIME_DURATION_SECONDS)
    )
  }
}

export default CitCPartnersService
