import type { CitcCreateSecretOptions } from '../../types/citcCreateSecretOptions'
//import { CitcSecretListOptions } from '../../types/citcSecretListOptions'
import type { CitcSecretOptions } from '../../types/citcSecretOptions'
import type { SecretDetail } from '../../types/secretDetail'
import type { SecretResponse } from '../../types/secretResponse'
import { Service } from '../service'
import { env } from '@/env'

class CitcVaultService extends Service {
  constructor(accessToken: string) {
    super(accessToken)
  }

  async getSecretValueAsync(
    options: CitcSecretOptions
  ): Promise<SecretResponse> {
    const url = `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/vault/secret/${options.secretIdentifier}`
    return (await this.fetchWithType<SecretResponse>(url)).data!
  }

  async getAllSecretsAsync(): Promise<SecretDetail[]> {
    const url = `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/vault/secret/generate`
    return (await this.fetchWithType<SecretDetail[]>(url)).data!
  }

  async setSecretValueAsync(
    options: CitcCreateSecretOptions
  ): Promise<SecretResponse> {
    const url = `${env.NEXT_PUBLIC_OIDC_AUTHORITY}/api/vault/secret/generate`
    return (await this.fetchWithType<SecretResponse>(url)).data!
  }
}

export default CitcVaultService
