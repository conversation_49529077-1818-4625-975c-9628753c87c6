import { SelectionType } from '@/enums/selectionType'
import { PartnerStorageType } from '@/enums/partnerStorageType'
import StorageService from '../storeage'
import { MigrationConfig } from '@/types/migrationConfig'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import CitCParametersService from '../citc/citcParameters'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import appInsights from '@/lib/applicationInsights'

type MigrationOptions<T> = {
  migrationConfig: MigrationConfig
  userId?: string
  storage?: AzureTableStorageWrapper
  partnerStorageType?: PartnerStorageType
  citCParamsQuery?: (
    citCParametersService: CitCParametersService
  ) => Promise<SimplifiedParameter[]>
  prismaClientType?: 'hubClient' | 'admClient'
  prismaQuery?: (prismaClient: any) => Promise<T[]>
  upsertFunction: (storage: AzureTableStorageWrapper, data: T[]) => Promise<any>
}

/**
 * Migrates data between different storage systems (Prisma DB, Azure Blob Storage, Azure Table Storage)
 * based on the provided configuration and transformation rules.
 *
 * The function handles two main migration paths:
 * 1. Partner-level migrations: Reads from blob storage using a partner-specific path
 * 2. Organization-level migrations: Reads from Prisma DB and optionally transforms/uploads to Azure Table Storage
 *
 * @template T - Source data type (e.g., database record type)
 *
 * @param {MigrationOptions<T>} options - Migration configuration options
 * @param {MigrationConfig} options.migrationConfig - Contains organizationId and selectionType
 * @param {string} [options.userId] - Optional user ID for partner-specific migrations
 * @param {AzureTableStorageWrapper} [options.storage] - Azure Table Storage client for data upload
 * @param {PartnerStorageType} [options.partnerStorageType] - Storage type for partner-level migrations
 * @param {'hubClient' | 'admClient'} options.prismaClientType - Which Prisma client instance to use
 * @param {Function} options.prismaQuery - Function that returns the Prisma query to execute
 * @param {Function} [options.citCParamsQuery] - Optional function to fetch data from CitC Parameters service
 * @param {Function} options.upsertFunction - Function to handle upserting data to storage
 *
 * @returns {Promise<T[]>} Array of migrated data in its original form
 *
 * @example
 * // Migrate user data from Prisma to Azure Table Storage
 * const result = await migrateData({
 *   migrationConfig: { organizationId: 'org1', selectionType: SelectionType.Organization },
 *   prismaClientType: 'hubClient',
 *   prismaQuery: (client) => client.users.findMany(),
 *   storage: tableStorageClient,
 *   upsertFunction: async (storage, data) =>
 *     await Promise.all(data.map(user =>
 *       upsertUser(storage, {
 *         partitionKey: 'users',
 *         rowKey: user.id,
 *         ...user
 *       })
 *     ))
 * });
 */
export const migrateData = async <T, U>({
  migrationConfig,
  userId,
  storage,
  partnerStorageType,
  citCParamsQuery,
  prismaClientType,
  prismaQuery,
  upsertFunction,
}: MigrationOptions<T>): Promise<T[]> => {
  const { organizationId, selectionType } = migrationConfig

  let prevData: T[] = []

  try {
    // Get data from appropriate source based on configuration
    if (!!citCParamsQuery) {
      // CitC Parameters query takes precedence regardless of selection type
      const citCParametersService = new CitCParametersService(
        migrationConfig.accessToken!
      )
      prevData = (await citCParamsQuery(
        citCParametersService
      )) as unknown as T[]
    } else if (selectionType === SelectionType.Partner && partnerStorageType) {
      // Partner-level migrations using blob storage
      const storageService = new StorageService()
      const fileName = userId
        ? `${organizationId}/${userId}.json`
        : `${organizationId}.json`

      const blobContent = await storageService.readBlobAsync(
        `partner-${partnerStorageType.toLowerCase()}`,
        fileName
      )
      prevData = JSON.parse(blobContent.toString())
    } else if (!!prismaQuery) {
      // Organization-level migrations using Prisma
      const prismaClient =
        globalThis.prismaClients?.[organizationId]?.[prismaClientType!]

      if (prismaClient) {
        prevData = await prismaQuery(prismaClient)
      }
    }

    // If we have data and storage is provided, upsert the data
    if (prevData.length > 0 && !!storage) {
      await upsertFunction(storage, prevData)
    }

    return prevData
  } catch (error) {
    appInsights.trackException({
      properties: {
        function: 'migrateData',
      },
      exception: error as Error,
    })

    return []
  }
}
