import { PartnerStorageType } from '@/enums/partnerStorageType'
import { AzureTableStorageWrapper } from '../azure/tableStorageWrapper'
import { upsertSavedView } from './upsertSavedView'
import { HUBPrismaClient } from '@/server/db'
import { migrateData } from '../migration/migrateData'
import { MigrationConfig } from '@/types/migrationConfig'
import { SavedView } from '@/types/savedView'

export const getAllSavedViews = async (
  tableStorage: AzureTableStorageWrapper,
  userId: string,
  organizationId: string,
  migrationConfig?: MigrationConfig
) => {
  // TODO: Enhancement* Add try/catch w/ app insights
  // Check if migration already happened for this user
  const existingViews = await tableStorage.queryEntities<SavedView>(
    tableStorage.generateODataQuery({
      PartitionKey: userId,
      organizationId: organizationId,
    })
  )

  // Only migrate if no views exist yet
  if (migrationConfig && existingViews.length === 0) {
    // TODO: Enhancement* Add try/catch w/ app insights
    await migrateData({
      userId,
      storage: tableStorage,
      partnerStorageType: PartnerStorageType.Views,
      migrationConfig,
      prismaClientType: 'hubClient',
      prismaQuery: (prismaClient: HUBPrismaClient) =>
        prismaClient.savedViews.findMany({
          where: {
            UserId: userId,
          },
        }),
      upsertFunction: async (storage, data) => {
        // TODO: Enhancement* Add try/catch w/ app insights
        return await Promise.all(
          data.map((view) =>
            upsertSavedView(storage, {
              partitionKey: userId,
              rowKey: view.Id.toString(),
              organizationId,
              userId,
              viewName: view.ViewName,
              viewMetadata: view.ViewMetadata,
              isDefault: view.IsDefault ?? false,
              isShared: view.IsShared ?? false,
            })
          )
        )
      },
    })
  }

  return tableStorage.queryEntities<SavedView>(
    tableStorage.generateODataQuery({
      PartitionKey: userId,
      organizationId: organizationId,
    })
  )
}
