import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'
import { type Connection, type Pool, type Bind } from 'snowflake-sdk'
import { EntityOrganizationTypeConstants } from '@/enums/entityOrganizationTypeConstants'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { SubmissionGroup } from '@/types/submissionGroup'
import { isPotentialSQLInjection } from '../snowflake/SnowflakeHelper'

export class SubmissionGroupRepository extends SnowflakeRepository<SubmissionGroup> {
  constructor(connectionPool: Pool<Connection>, tableName: string) {
    super(connectionPool, tableName, 'SubmissionGroupId')
  }

  findSubmissionGroupsByOrganization = async (
    organizationId: string,
    isPartner: boolean
  ): Promise<SubmissionGroup[]> => {
    return this.findSubmissionGroups(organizationId, isPartner)
  }

  findSubmissionGroupsByOrganizationAndMeasureType = async (
    organizationId: string,
    isPartner: boolean,
    measureIdentifiers: string[]
  ): Promise<SubmissionGroup[]> => {
    return this.findSubmissionGroups(
      organizationId,
      isPartner,
      measureIdentifiers
    )
  }

  findSubmissionGroupsByOrganizationAndProviders = async (
    organizationId: string,
    isPartner: boolean,
    NPIs: string[]
  ): Promise<SubmissionGroup[]> => {
    if (NPIs.some(isPotentialSQLInjection)) {
      throw new Error('Potential SQL injection detected in provider list')
    }

    let organizationIdCondition = ''
    if (!isPartner) {
      organizationIdCondition = `AND REPLACE(entities."SourceContainerIdentifier", '_EC', '') = ?`
    }

    const sql = `
        WITH AllAvailableEntities AS (
          SELECT
            entities."Id" AS "EntityId",
            entities."Description" AS "SubmissionGroupName",
            entities."Code" AS "SubmissionGroupId",
            orgTypes."Code" AS "OrgTypeCode",
            REPLACE(entities."SourceContainerIdentifier", '_EC', '') AS "SourceContainerIdentifier"
          FROM MECA."Entities" entities
          JOIN MECA."OrganizationTypes" orgTypes ON entities."OrganizationTypeId" = orgTypes."Id"
          JOIN MECA."EntityTypes" entityType ON entities."EntityTypeId" = entityType."Id"
          WHERE
            entityType."Code" = '${EntityTypeConstants.RenderingProvider}'
            ${organizationIdCondition}
        )
        SELECT DISTINCT
          E."SubmissionGroupId",
          E."SubmissionGroupName"
        FROM MECA."EntityProviders" EP
        JOIN CSRC."Providers" P ON EP."ProvidersId" = P."Id"
        JOIN AllAvailableEntities E ON EP."EntitiesId" = E."EntityId"
        JOIN AllAvailableEntities E2 ON P."ProviderNPI" = E2."SubmissionGroupId"
        WHERE
          P."ProviderNPI" IN (${NPIs.map(() => '?').join(', ')})
          AND E."OrgTypeCode" = '${EntityOrganizationTypeConstants.SubmissionGroupLevel}'

      `
    const params: Bind[] = []
    if (!isPartner) {
      params.push(organizationId)
    }
    params.push(...NPIs)

    const results = await this.execute(sql, params)

    return (
      results?.map(
        (row: any) =>
          ({
            submissionGroupId: row.SubmissionGroupId,
            submissionGroupName: row.SubmissionGroupName,
          }) as SubmissionGroup
      ) || []
    )
  }

  private findSubmissionGroups = async (
    organizationId: string,
    isPartner: boolean,
    measureIdentifiers?: string[]
  ): Promise<SubmissionGroup[]> => {
    let organizationIdCondition = ''
    if (!isPartner) {
      organizationIdCondition = `AND REPLACE(entities."SourceContainerIdentifier", '_EC', '') = ?`
    }
    const caseInsensitiveMeasureIdentifers = measureIdentifiers?.map( (measure) => measure.toLowerCase()) || []
    let measureIdentifiersCondition = ''
    if (caseInsensitiveMeasureIdentifers && caseInsensitiveMeasureIdentifers.length > 0) {
      measureIdentifiersCondition = `WHERE LOWER(am."MeasureGUID") IN (${caseInsensitiveMeasureIdentifers.map(() => '?').join(', ')})`
    }
    const sql = `
            WITH AllType3Entities AS (
                SELECT
                    entities."Id" AS "EntityId",
                    entities."Code" AS "EntityCode",
                    entities."Code" AS "SubmissionGroupId",
                    entities."Description" AS "SubmissionGroupName",
                    entities."SourceContainerIdentifier" AS "SourceContainerIdentifier"
                FROM MECA."Entities" entities
                JOIN MECA."OrganizationTypes" orgTypes ON entities."OrganizationTypeId" = orgTypes."Id"
                JOIN MECA."EntityTypes" entityType ON entities."EntityTypeId" = entityType."Id"
                WHERE
                    orgTypes."Code" = '${EntityOrganizationTypeConstants.SubmissionGroupLevel}'
                    AND entityType."Code" = '${EntityTypeConstants.RenderingProvider}'
                    ${organizationIdCondition}
            ),
            FilteredMeasures AS (
                SELECT
                    am."EntitiesId",
                    am."MeasureGUID",
                    am."ProcessingStartDate"
                FROM MECA."ActiveMeasures" am
                ${measureIdentifiersCondition}
            ),
            ActiveMeasures AS (
                SELECT
                    fm."EntitiesId",
                    MIN(fm."ProcessingStartDate") AS "ProcessingStartDate",
                    MAX(fm."ProcessingStartDate") AS "ProcessingEndDate"
                FROM FilteredMeasures fm
                GROUP BY fm."EntitiesId"
            )
            SELECT
                ate."SubmissionGroupId",
                ate."SubmissionGroupName",
                TO_CHAR(am."ProcessingStartDate", 'YYYY-MM-DD"T"HH24:MI:SS') as "ProcessingStartDate",
                TO_CHAR(am."ProcessingEndDate", 'YYYY-MM-DD"T"HH24:MI:SS') as "ProcessingEndDate",
            FROM AllType3Entities ate
            JOIN ActiveMeasures am ON ate."EntityId" = am."EntitiesId"
            WHERE EXISTS (
                SELECT 1
                FROM FilteredMeasures fm
                WHERE fm."EntitiesId" = ate."EntityId"
            )
        `

    const params: Bind[] = []
    if (!isPartner) {
      params.push(organizationId)
    }

    if (caseInsensitiveMeasureIdentifers && caseInsensitiveMeasureIdentifers.length > 0)
      params.push(...caseInsensitiveMeasureIdentifers)

    const results = await this.execute(sql, params)

    return (
      results?.map(
        (row: any) =>
          ({
            submissionGroupId: row.SubmissionGroupId,
            submissionGroupName: row.SubmissionGroupName,
            processingStartDate: row.ProcessingStartDate,
            processingEndDate: row.ProcessingEndDate,
          }) as SubmissionGroup
      ) || []
    )
  }
}
