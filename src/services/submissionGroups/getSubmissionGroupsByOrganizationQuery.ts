import type { SubmissionGroup } from '@/types/submissionGroup'
import type { Dayjs } from 'dayjs'
import MeasureResultsService from '../measureResults'
import CitCParametersService from '../citcParameters'
import { ScorecardView } from '@/enums/scorecardView'
import { getPartnersByUserCommand } from '../partners/getPartnersByUserCommand'
import dayjs from 'dayjs'
import { Constants } from '@/enums/constants'

type GetSubmissionGroupsByOrganizationQuery = {
  organizationId: string
  startDate: Dayjs
  endDate: Dayjs
  isPartner: boolean
  measureType?: string
}

export const getSubmissionGroupsByOrganizationQuery = async (
  accessToken: string,
  measureResultsService: MeasureResultsService,
  userId: string,
  request: GetSubmissionGroupsByOrganizationQuery
) => {
  let submissionGroups: SubmissionGroup[] = []

  const citcParameterService = new CitCParametersService(accessToken)

  // Fetch submission groups by organization
  submissionGroups =
    await measureResultsService.getSubmissionGroupsByOrganization({
      organizationId: request.organizationId,
      isPartner: request.isPartner,
    })

  // Fetch submission groups by measure type (Q)
  const qSubmissionGroups =
    await measureResultsService.getSubmissionGroupsByOrganizationByMeasureType({
      organizationId: request.organizationId,
      isPartner: request.isPartner,
      measureType: ScorecardView.Quarterly,
    })
  submissionGroups.push(...qSubmissionGroups)

  let activeSubmissionGroups: SubmissionGroup[] = []

  if (request.isPartner) {
    const partners = await getPartnersByUserCommand(userId, accessToken)

    const partner = partners?.find((p) => p.id === request.organizationId)
    if (partner) {
      for (const organization of partner.organizations ?? []) {
        const organizationParameters =
          await citcParameterService.getOrganizationParametersAsync(
            organization.organizationId!
          )

        const ecOverallGroupId = organizationParameters.find(
          (param) => param.key === Constants.EcOverallGroupId
        )?.value

        if (!ecOverallGroupId) {
          // throw new Error("No parameter available: 'platform:EcOverallGroupId'") // not implemented at the moment
          continue
        }

        activeSubmissionGroups.push(
          ...submissionGroups.filter((group) =>
            group.submissionGroupId.startsWith(ecOverallGroupId)
          )
        )
      }
    }
  } else {
    activeSubmissionGroups = submissionGroups
  }

  // Filter submission groups by the date range
  const filteredSubmissionGroups = activeSubmissionGroups.filter((group) => {
    const startYear = dayjs.utc(group.processingStartDate).year()
    const endYear = dayjs.utc(group.processingEndDate).year()
    const requestStartYear = request.startDate.year()
    const requestEndYear = request.endDate.year()

    return (
      (startYear >= requestStartYear && startYear <= requestEndYear) ||
      (endYear >= requestStartYear && endYear <= requestEndYear)
    )
  })

  if (filteredSubmissionGroups.length > 1) {
    filteredSubmissionGroups = [...filteredSubmissionGroups].sort((a, b) =>
      (a.submissionGroupName || '').localeCompare(b.submissionGroupName || '')
    );
  }
  return filteredSubmissionGroups
}
