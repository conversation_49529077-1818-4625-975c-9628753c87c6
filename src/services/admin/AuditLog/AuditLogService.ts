import { env } from "@/env"
import { Service } from "@/services/service"
import { ApiAuditLogData, } from "@/types/auditLog"


class AuditLogService extends Service {
    constructor(accesstoken: string) {
        super(accesstoken)
    }

    async getAuditLogs(
        organizationId: string,
        applicationId: string,
        startTimeMMDDYYYY: string,
        endTimeMMDDYYYY: string
    ): Promise<ApiAuditLogData[]> {
        try {
            let baseUrl = env.NEXT_PUBLIC_AUDIT_LOG_SERVER_URL;
            if (baseUrl && !baseUrl.endsWith('/')) {
                baseUrl += '/';
            }

            const url = `${baseUrl}api/audit-logs/organization/${encodeURIComponent(organizationId)}/application/${encodeURIComponent(applicationId)}/start-time/${encodeURIComponent(startTimeMMDDYYYY)}/end-time/${encodeURIComponent(endTimeMMDDYYYY)}`

            const response = await this.fetchWithType<ApiAuditLogData[]>(url)
            return response?.data ?? [];
        }
        catch (error) {
            console.log("Error fetching audit logs.", error);
            return [];
        }

    }

    async postAuditLogs(auditLogData: {
        url: string
        userAgent: string
        pageTitle: string
        actionType: string
        productId: string
        userId: string
        userEmail: string
        applicationId: string
        currentRoles: string
        organizationId: string
    }): Promise<boolean> {
        try {
            let baseUrl = env.NEXT_PUBLIC_AUDIT_LOG_SERVER_URL;
            if (baseUrl && !baseUrl.endsWith('/')) {
                baseUrl += '/';
            }

            const url = `${baseUrl}api/audit-logs/add`;
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${this.accessToken}`,
                },
                body: JSON.stringify(auditLogData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Failed to post audit log: ${response.status} ${response.statusText} - ${errorText}`);
            }

            return response.ok;
        } catch (error) {
            console.error("Error posting audit logs:", error);
            return false;
        }
    }
}
export default AuditLogService
