import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { ExtensionLevels } from '@/enums/extensionLevels'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { getAllExpansionConfigs } from '@/services/expansionConfigs/getAllExpansionConfigs'
import MeasureResultsService from '@/services/measureResults'
import {
  EntityLevelConstants,
  ExpansionConfiguration,
} from '@/types/expansionConfiguration'

export const getDropDownValues = async (
  tableStorage: AzureTableStorageWrapper,
  MeasureResultsService: MeasureResultsService,
  isPartner: boolean,
  organizationId: string,
  priamryMeasureTypes: PrimaryMeasureTypeConstants[]
) => {
  const expansionConfigurations: ExpansionConfiguration[] = []

  const storedValues = await getAllExpansionConfigs(
    tableStorage,
    organizationId
  )

  const organizationTypesByEntityType =
    await MeasureResultsService.findOrganizationTypesByEntityTypes()

  const facilityEntry = organizationTypesByEntityType.find(
    (x) => x.EntityTypeCode === EntityTypeConstants.Facility
  )

  const getselectedLevel = (
    measureType: PrimaryMeasureTypeConstants,
    level: ExtensionLevels
  ) => {
    if (storedValues) {
      return (
        storedValues.find(
          (x) => x.rowKey === `${measureType}.${level}`,
        )?.selectedLevel ?? EntityLevelConstants.None
      )
    }

    return EntityLevelConstants.None
  }

  if (
    priamryMeasureTypes.includes(
      PrimaryMeasureTypeConstants.HospitalMeasures
    ) &&
    facilityEntry
  ) {
    const organizationTypeCodes = organizationTypesByEntityType
      .filter((x) => x.EntityTypeCode === EntityTypeConstants.Facility)
      .map((x) => x.OrganizationTypeCode)

    organizationTypeCodes.forEach((code) => {
      switch (code) {
        case '1':
          expansionConfigurations.push({
            partitionKey: organizationId,
            rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level1}`,
            level: ExtensionLevels.level1,
            label: 'Level 1 Entities',
            measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
            entries: JSON.stringify(
              isPartner
                ? [EntityLevelConstants.TopLevel]
                : [
                    EntityLevelConstants.TopLevel,
                    EntityLevelConstants.DoNotDisplay,
                  ]
            ),
            selectedLevel: getselectedLevel(
              PrimaryMeasureTypeConstants.HospitalMeasures,
              ExtensionLevels.level1
            ),
          })
          break
        case '2':
          if (!isPartner && organizationTypeCodes.includes('1')) {
            expansionConfigurations.push({
              partitionKey: organizationId,
              rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level2}`,
              level: ExtensionLevels.level2,
              label: 'Level 2 Entities',
              measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
              entries: JSON.stringify([
                EntityLevelConstants.TopLevel,
                EntityLevelConstants.SecondLevel,
              ]),
              selectedLevel: getselectedLevel(
                PrimaryMeasureTypeConstants.HospitalMeasures,
                ExtensionLevels.level2
              ),
            })
          }
          if (!isPartner && !organizationTypeCodes.includes('1')) {
            expansionConfigurations.push({
              partitionKey: organizationId,
              rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level1}`,
              level: ExtensionLevels.level1,
              label: 'Level 1 Entities',
              measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
              entries: JSON.stringify([EntityLevelConstants.DoNotDisplay]),
              selectedLevel: getselectedLevel(
                PrimaryMeasureTypeConstants.HospitalMeasures,
                ExtensionLevels.level1
              ),
            })

            expansionConfigurations.push({
              partitionKey: organizationId,
              rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level2}`,
              level: ExtensionLevels.level2,
              label: 'Level 2 Entities',
              measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
              entries: JSON.stringify([EntityLevelConstants.TopLevel]),
              selectedLevel: getselectedLevel(
                PrimaryMeasureTypeConstants.HospitalMeasures,
                ExtensionLevels.level2
              ),
            })
          }
          if (isPartner) {
            expansionConfigurations.push({
              partitionKey: organizationId,
              rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level2}`,
              level: ExtensionLevels.level2,
              label: 'Level 2 Entities',
              measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
              entries: JSON.stringify([EntityLevelConstants.SecondLevel]),
              selectedLevel: getselectedLevel(
                PrimaryMeasureTypeConstants.HospitalMeasures,
                ExtensionLevels.level2
              ),
            })
          }

          break
        case '3':
          expansionConfigurations.push({
            partitionKey: organizationId,
            rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level3}`,
            level: ExtensionLevels.level3,
            label: 'Level 3 Entities',
            measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
            entries: JSON.stringify([EntityLevelConstants.SecondLevel]),
            selectedLevel: getselectedLevel(
              PrimaryMeasureTypeConstants.HospitalMeasures,
              ExtensionLevels.level3
            ),
          })
          break
        case '4':
          expansionConfigurations.push({
            partitionKey: organizationId,
            rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level4}`,
            level: ExtensionLevels.level4,
            label: 'Level 4 Entities',
            measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
            entries: JSON.stringify([
              EntityLevelConstants.Thirdlevel,
              EntityLevelConstants.DoNotDisplay,
            ]),
            selectedLevel: getselectedLevel(
              PrimaryMeasureTypeConstants.HospitalMeasures,
              ExtensionLevels.level4
            ),
          })
          break
        default:
          break
      }
    })
  }

  const renderingProviderEntry = organizationTypesByEntityType.find(
    (x) => x.EntityTypeCode === EntityTypeConstants.RenderingProvider
  )
  if (
    priamryMeasureTypes.includes(
      PrimaryMeasureTypeConstants.AmbulatoryMeasures
    ) &&
    renderingProviderEntry
  ) {
    const organizationTypeCodes = organizationTypesByEntityType
      .filter((x) => x.EntityTypeCode === EntityTypeConstants.RenderingProvider)
      .map((x) => x.OrganizationTypeCode)

    organizationTypeCodes.forEach((code) => {
      switch (code) {
        case '1':
          expansionConfigurations.push({
            partitionKey: organizationId,
            rowKey: `${PrimaryMeasureTypeConstants.AmbulatoryMeasures}.${ExtensionLevels.level1}`,
            level: ExtensionLevels.level1,
            label: 'Level 1 Entities',
            measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
            entries: JSON.stringify(
              isPartner
                ? [EntityLevelConstants.TopLevel]
                : [
                    EntityLevelConstants.TopLevel,
                    EntityLevelConstants.DoNotDisplay,
                  ]
            ),
            selectedLevel: getselectedLevel(
              PrimaryMeasureTypeConstants.AmbulatoryMeasures,
              ExtensionLevels.level1
            ),
          })
          break
        case '2':
          if (!isPartner && organizationTypeCodes.includes('1')) {
            expansionConfigurations.push({
              partitionKey: organizationId,
              rowKey: `${PrimaryMeasureTypeConstants.AmbulatoryMeasures}.${ExtensionLevels.level2}`,
              level: ExtensionLevels.level2,
              label: 'Level 2 Entities',
              measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
              entries: JSON.stringify([
                EntityLevelConstants.TopLevel,
                EntityLevelConstants.SecondLevel,
              ]),
              selectedLevel: getselectedLevel(
                PrimaryMeasureTypeConstants.AmbulatoryMeasures,
                ExtensionLevels.level2
              ),
            })
          }
          if (!isPartner && !organizationTypeCodes.includes('1')) {
            expansionConfigurations.push({
              partitionKey: organizationId,
              rowKey: `${PrimaryMeasureTypeConstants.AmbulatoryMeasures}.${ExtensionLevels.level2}`,
              level: ExtensionLevels.level2,
              label: 'Level 2 Entities',
              measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
              entries: JSON.stringify([EntityLevelConstants.TopLevel]),
              selectedLevel: getselectedLevel(
                PrimaryMeasureTypeConstants.AmbulatoryMeasures,
                ExtensionLevels.level2
              ),
            })

            expansionConfigurations.push({
              partitionKey: organizationId,
              rowKey: `${PrimaryMeasureTypeConstants.AmbulatoryMeasures}.${ExtensionLevels.level1}`,
              level: ExtensionLevels.level1,
              label: 'Level 1 Entities',
              measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
              entries: JSON.stringify([EntityLevelConstants.DoNotDisplay]),
              selectedLevel: getselectedLevel(
                PrimaryMeasureTypeConstants.AmbulatoryMeasures,
                ExtensionLevels.level1
              ),
            })
          }
          if (isPartner) {
            expansionConfigurations.push({
              partitionKey: organizationId,
              rowKey: `${PrimaryMeasureTypeConstants.AmbulatoryMeasures}.${ExtensionLevels.level2}`,
              level: ExtensionLevels.level2,
              label: 'Level 2 Entities',
              measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
              entries: JSON.stringify([EntityLevelConstants.SecondLevel]),
              selectedLevel: getselectedLevel(
                PrimaryMeasureTypeConstants.AmbulatoryMeasures,
                ExtensionLevels.level2
              ),
            })
          }

          break
        case '3':
          expansionConfigurations.push({
            partitionKey: organizationId,
            rowKey: `${PrimaryMeasureTypeConstants.AmbulatoryMeasures}.${ExtensionLevels.level3}`,
            level: ExtensionLevels.level3,
            label: 'Level 3 Entities',
            measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
            entries: JSON.stringify([EntityLevelConstants.SecondLevel]),
            selectedLevel: getselectedLevel(
              PrimaryMeasureTypeConstants.AmbulatoryMeasures,
              ExtensionLevels.level3
            ),
          })
          break
        case '4':
          expansionConfigurations.push({
            partitionKey: organizationId,
            rowKey: `${PrimaryMeasureTypeConstants.AmbulatoryMeasures}.${ExtensionLevels.level4}`,
            level: ExtensionLevels.level4,
            label: 'Level 4 Entities',
            measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
            entries: JSON.stringify([
              EntityLevelConstants.Thirdlevel,
              EntityLevelConstants.DoNotDisplay,
            ]),
            selectedLevel: getselectedLevel(
              PrimaryMeasureTypeConstants.AmbulatoryMeasures,
              ExtensionLevels.level4
            ),
          })
          break
        default:
          break
      }
    })
  }

  return expansionConfigurations
}
