import { type Instrumentation } from 'next'
import { env } from './env'
import { type TelemetryClient } from 'applicationinsights'
//import appInsights from './lib/applicationInsights'
let client: null | TelemetryClient = null

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs' && typeof window === 'undefined') {
    setTimeout(async () => {
      let appInsights = await import('applicationinsights')
      appInsights
          ?.setup(env.NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING)
          .setAutoDependencyCorrelation(true)
          .setAutoCollectRequests(true)
          .setAutoCollectPerformance(true, true)
          .setAutoCollectExceptions(true)
          .setAutoCollectDependencies(true)
          .setAutoCollectConsole(true)
          .setUseDiskRetryCaching(true)
          .setSendLiveMetrics(false)
          .setDistributedTracingMode(appInsights.DistributedTracingModes.AI)
          .start()

      client = appInsights?.defaultClient
      client.trackTrace({ message: 'App insights instrumentation initialized' })
    }, 5000)
  }
}

export const onRequestError: Instrumentation.onRequestError = async (
  err,
  request,
  context
) => {
  //const error = err as Error
  if (err instanceof Error && typeof window === 'undefined') {
    client?.trackException({ exception: err })
  }

  //   await fetch('https://.../report-error', {
  //     method: 'POST',
  //     body: JSON.stringify({
  //       message: err.message,
  //       request,
  //       context,
  //     }),
  //     headers: {
  //       'Content-Type': 'application/json',
  //     },
  //   })
}
