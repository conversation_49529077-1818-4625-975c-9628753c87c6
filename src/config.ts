const unnormalizedConfig = {
  isCurrentYearDataAvailable: true,
  customMeasures: {
    ratioMeasures: [
      'CBA03BF5-FEFA-4B3F-924B-D99E4C819C58',
    ] as <PERSON><PERSON><PERSON><PERSON><PERSON>y<string>,
    volumeMeasures: [
      '4EB10D78-29AC-EA11-A8FE-4CEDFB610C38',
      '4FB10D78-29AC-EA11-A8FE-4CEDFB610C38',
      'C0FED34B-15EE-ED11-874B-501AC5E8862E',
    ] as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><string>,
    excludedMeasuresList: [
      'C4FED34B-15EE-ED11-874B-501AC5E8862E',
      'D3BCBAB4-967C-ED11-AC20-2818784A59EB',
    ] as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><string>,
  },
  measureList: [
    '7A7CFBE3-A011-ED11-BD6E-2818784AB177',
    '7B7CFBE3-A011-ED11-BD6E-2818784AB177',
    '847CFBE3-A011-ED11-BD6E-2818784AB177',
    '72D93B2F-A111-ED11-BD6E-2818784AB177',
    '73D93B2F-A111-ED11-BD6E-2818784AB177',
    '74D93B2F-A111-ED11-BD6E-2818784AB177',
    '75D93B2F-A111-ED11-BD6E-2818784AB177',
    '76D93B2F-A111-ED11-BD6E-2818784AB177',
    '77D93B2F-A111-ED11-BD6E-2818784AB177',
    '78D93B2F-A111-ED11-BD6E-2818784AB177',
    '79D93B2F-A111-ED11-BD6E-2818784AB177',
    '7AD93B2F-A111-ED11-BD6E-2818784AB177',
    '7BD93B2F-A111-ED11-BD6E-2818784AB177',
    '857CFBE3-A011-ED11-BD6E-2818784AB177',
    '7CD93B2F-A111-ED11-BD6E-2818784AB177',
    '7DD93B2F-A111-ED11-BD6E-2818784AB177',
    '7FD93B2F-A111-ED11-BD6E-2818784AB177',
    '80D93B2F-A111-ED11-BD6E-2818784AB177',
    '81D93B2F-A111-ED11-BD6E-2818784AB177',
    '82D93B2F-A111-ED11-BD6E-2818784AB177',
    '83D93B2F-A111-ED11-BD6E-2818784AB177',
    '84D93B2F-A111-ED11-BD6E-2818784AB177',
    '85D93B2F-A111-ED11-BD6E-2818784AB177',
    '86D93B2F-A111-ED11-BD6E-2818784AB177',
    '867CFBE3-A011-ED11-BD6E-2818784AB177',
    '87D93B2F-A111-ED11-BD6E-2818784AB177',
    '88D93B2F-A111-ED11-BD6E-2818784AB177',
    '89D93B2F-A111-ED11-BD6E-2818784AB177',
    '8AD93B2F-A111-ED11-BD6E-2818784AB177',
    '8BD93B2F-A111-ED11-BD6E-2818784AB177',
    '8CD93B2F-A111-ED11-BD6E-2818784AB177',
    '8ED93B2F-A111-ED11-BD6E-2818784AB177',
    '8FD93B2F-A111-ED11-BD6E-2818784AB177',
    '90D93B2F-A111-ED11-BD6E-2818784AB177',
    '91D93B2F-A111-ED11-BD6E-2818784AB177',
    '877CFBE3-A011-ED11-BD6E-2818784AB177',
    '92D93B2F-A111-ED11-BD6E-2818784AB177',
    '93D93B2F-A111-ED11-BD6E-2818784AB177',
    '94D93B2F-A111-ED11-BD6E-2818784AB177',
    '95D93B2F-A111-ED11-BD6E-2818784AB177',
    '96D93B2F-A111-ED11-BD6E-2818784AB177',
    '97D93B2F-A111-ED11-BD6E-2818784AB177',
    '98D93B2F-A111-ED11-BD6E-2818784AB177',
    '99D93B2F-A111-ED11-BD6E-2818784AB177',
    '9AD93B2F-A111-ED11-BD6E-2818784AB177',
    '9BD93B2F-A111-ED11-BD6E-2818784AB177',
    '897CFBE3-A011-ED11-BD6E-2818784AB177',
    '809B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '819B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '829B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '839B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '849B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '859B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '869B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '879B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '889B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '899B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '8A7CFBE3-A011-ED11-BD6E-2818784AB177',
    '8A9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '8B9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '8C9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '8D9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '8E9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '8F9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '909B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '919B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '929B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '939B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '8B7CFBE3-A011-ED11-BD6E-2818784AB177',
    '949B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '959B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '969B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '979B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '989B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '999B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '9A9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '9B9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '9C9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '9D9B9E1A-DA2E-ED11-AE83-501AC5E8A7F5',
    '8C7CFBE3-A011-ED11-BD6E-2818784AB177',
    '17B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '04B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '2FB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '0EB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '18B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '1BB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '31B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '0FB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '32B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '30B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '8D7CFBE3-A011-ED11-BD6E-2818784AB177',
    '61E9C0E0-73D9-47B9-8A7E-90A10A9BE0BC',
    'D43A428E-B3D5-49C1-8360-B865EE81A5A2',
    'D53A428E-B3D5-49C1-8360-B865EE81A5A2',
    'D63A428E-B3D5-49C1-8360-B865EE81A5A2',
    '01B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '3218E96E-8100-47C0-AFAA-3F01B80AEED8',
    'D23A428E-B3D5-49C1-8360-B865EE81A5A2',
    'D33A428E-B3D5-49C1-8360-B865EE81A5A2',
    '05B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '1AB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '8E7CFBE3-A011-ED11-BD6E-2818784AB177',
    '2DB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '2EB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '7C7CFBE3-A011-ED11-BD6E-2818784AB177',
    '8F7CFBE3-A011-ED11-BD6E-2818784AB177',
    '907CFBE3-A011-ED11-BD6E-2818784AB177',
    '917CFBE3-A011-ED11-BD6E-2818784AB177',
    '927CFBE3-A011-ED11-BD6E-2818784AB177',
    '937CFBE3-A011-ED11-BD6E-2818784AB177',
    '947CFBE3-A011-ED11-BD6E-2818784AB177',
    '957CFBE3-A011-ED11-BD6E-2818784AB177',
    '967CFBE3-A011-ED11-BD6E-2818784AB177',
    '987CFBE3-A011-ED11-BD6E-2818784AB177',
    '997CFBE3-A011-ED11-BD6E-2818784AB177',
    '7D7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9A7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9B7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9C7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9D7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9E7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9F7CFBE3-A011-ED11-BD6E-2818784AB177',
    'A07CFBE3-A011-ED11-BD6E-2818784AB177',
    'A17CFBE3-A011-ED11-BD6E-2818784AB177',
    'A27CFBE3-A011-ED11-BD6E-2818784AB177',
    'A37CFBE3-A011-ED11-BD6E-2818784AB177',
    '7E7CFBE3-A011-ED11-BD6E-2818784AB177',
    'A47CFBE3-A011-ED11-BD6E-2818784AB177',
    'A57CFBE3-A011-ED11-BD6E-2818784AB177',
    'A77CFBE3-A011-ED11-BD6E-2818784AB177',
    'A87CFBE3-A011-ED11-BD6E-2818784AB177',
    'A97CFBE3-A011-ED11-BD6E-2818784AB177',
    'AA7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AB7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AC7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AD7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AE7CFBE3-A011-ED11-BD6E-2818784AB177',
    '7F7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AF7CFBE3-A011-ED11-BD6E-2818784AB177',
    'C0119C26-A111-ED11-BD6E-2818784AB177',
    'C1119C26-A111-ED11-BD6E-2818784AB177',
    'C2119C26-A111-ED11-BD6E-2818784AB177',
    'C3119C26-A111-ED11-BD6E-2818784AB177',
    'C4119C26-A111-ED11-BD6E-2818784AB177',
    'C6119C26-A111-ED11-BD6E-2818784AB177',
    'C7119C26-A111-ED11-BD6E-2818784AB177',
    'C8119C26-A111-ED11-BD6E-2818784AB177',
    'C9119C26-A111-ED11-BD6E-2818784AB177',
    '807CFBE3-A011-ED11-BD6E-2818784AB177',
    'CA119C26-A111-ED11-BD6E-2818784AB177',
    'CB119C26-A111-ED11-BD6E-2818784AB177',
    'CC119C26-A111-ED11-BD6E-2818784AB177',
    'CD119C26-A111-ED11-BD6E-2818784AB177',
    'CE119C26-A111-ED11-BD6E-2818784AB177',
    'CF119C26-A111-ED11-BD6E-2818784AB177',
    'D0119C26-A111-ED11-BD6E-2818784AB177',
    'D1119C26-A111-ED11-BD6E-2818784AB177',
    'D2119C26-A111-ED11-BD6E-2818784AB177',
    'D3119C26-A111-ED11-BD6E-2818784AB177',
    '817CFBE3-A011-ED11-BD6E-2818784AB177',
    'D5119C26-A111-ED11-BD6E-2818784AB177',
    'D6119C26-A111-ED11-BD6E-2818784AB177',
    'D7119C26-A111-ED11-BD6E-2818784AB177',
    'D8119C26-A111-ED11-BD6E-2818784AB177',
    'D9119C26-A111-ED11-BD6E-2818784AB177',
    'DA119C26-A111-ED11-BD6E-2818784AB177',
    'DB119C26-A111-ED11-BD6E-2818784AB177',
    'DC119C26-A111-ED11-BD6E-2818784AB177',
    'DD119C26-A111-ED11-BD6E-2818784AB177',
    'DE119C26-A111-ED11-BD6E-2818784AB177',
    '827CFBE3-A011-ED11-BD6E-2818784AB177',
    'DF119C26-A111-ED11-BD6E-2818784AB177',
    'E0119C26-A111-ED11-BD6E-2818784AB177',
    'E1119C26-A111-ED11-BD6E-2818784AB177',
    'E2119C26-A111-ED11-BD6E-2818784AB177',
    'E4119C26-A111-ED11-BD6E-2818784AB177',
    'E5119C26-A111-ED11-BD6E-2818784AB177',
    'E6119C26-A111-ED11-BD6E-2818784AB177',
    'E7119C26-A111-ED11-BD6E-2818784AB177',
    'E8119C26-A111-ED11-BD6E-2818784AB177',
    'E9119C26-A111-ED11-BD6E-2818784AB177',
    '837CFBE3-A011-ED11-BD6E-2818784AB177',
    'EA119C26-A111-ED11-BD6E-2818784AB177',
    'EB119C26-A111-ED11-BD6E-2818784AB177',
    'EC119C26-A111-ED11-BD6E-2818784AB177',
    'ED119C26-A111-ED11-BD6E-2818784AB177',
    'EE119C26-A111-ED11-BD6E-2818784AB177',
    'EF119C26-A111-ED11-BD6E-2818784AB177',
    'F0119C26-A111-ED11-BD6E-2818784AB177',
    '6ED93B2F-A111-ED11-BD6E-2818784AB177',
    '70D93B2F-A111-ED11-BD6E-2818784AB177',
    '71D93B2F-A111-ED11-BD6E-2818784AB177',
    '61E9C0E0-73D9-47B9-8A7E-90A10A9BE0BC',
    'D43A428E-B3D5-49C1-8360-B865EE81A5A2',
    'D53A428E-B3D5-49C1-8360-B865EE81A5A2',
    'D63A428E-B3D5-49C1-8360-B865EE81A5A2',
    '0FB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '0EB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '31B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '2FB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '17B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '3218E96E-8100-47C0-AFAA-3F01B80AEED8',
    'D23A428E-B3D5-49C1-8360-B865EE81A5A2',
    'D33A428E-B3D5-49C1-8360-B865EE81A5A2',
    '1BB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '1AB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '30B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '05B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '977CFBE3-A011-ED11-BD6E-2818784AB177',
    '987CFBE3-A011-ED11-BD6E-2818784AB177',
    '997CFBE3-A011-ED11-BD6E-2818784AB177',
    '9A7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9B7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9C7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9D7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9E7CFBE3-A011-ED11-BD6E-2818784AB177',
    '9F7CFBE3-A011-ED11-BD6E-2818784AB177',
    'A07CFBE3-A011-ED11-BD6E-2818784AB177',
    'A17CFBE3-A011-ED11-BD6E-2818784AB177',
    'A27CFBE3-A011-ED11-BD6E-2818784AB177',
    'A37CFBE3-A011-ED11-BD6E-2818784AB177',
    'A47CFBE3-A011-ED11-BD6E-2818784AB177',
    'A57CFBE3-A011-ED11-BD6E-2818784AB177',
    'A67CFBE3-A011-ED11-BD6E-2818784AB177',
    'A77CFBE3-A011-ED11-BD6E-2818784AB177',
    'A87CFBE3-A011-ED11-BD6E-2818784AB177',
    'A97CFBE3-A011-ED11-BD6E-2818784AB177',
    'AA7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AB7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AC7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AD7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AE7CFBE3-A011-ED11-BD6E-2818784AB177',
    'AF7CFBE3-A011-ED11-BD6E-2818784AB177',
    'C0119C26-A111-ED11-BD6E-2818784AB177',
    'C1119C26-A111-ED11-BD6E-2818784AB177',
    'C2119C26-A111-ED11-BD6E-2818784AB177',
    'C3119C26-A111-ED11-BD6E-2818784AB177',
    'C4119C26-A111-ED11-BD6E-2818784AB177',
    'D4119C26-A111-ED11-BD6E-2818784AB177',
    'D5119C26-A111-ED11-BD6E-2818784AB177',
    'D6119C26-A111-ED11-BD6E-2818784AB177',
    'D7119C26-A111-ED11-BD6E-2818784AB177',
    'D8119C26-A111-ED11-BD6E-2818784AB177',
    'D9119C26-A111-ED11-BD6E-2818784AB177',
    'DA119C26-A111-ED11-BD6E-2818784AB177',
    'DB119C26-A111-ED11-BD6E-2818784AB177',
    'DC119C26-A111-ED11-BD6E-2818784AB177',
    'DD119C26-A111-ED11-BD6E-2818784AB177',
    'DE119C26-A111-ED11-BD6E-2818784AB177',
    'DF119C26-A111-ED11-BD6E-2818784AB177',
    'E0119C26-A111-ED11-BD6E-2818784AB177',
    'E1119C26-A111-ED11-BD6E-2818784AB177',
    'E2119C26-A111-ED11-BD6E-2818784AB177',
    '6FD93B2F-A111-ED11-BD6E-2818784AB177',
    '70D93B2F-A111-ED11-BD6E-2818784AB177',
    '71D93B2F-A111-ED11-BD6E-2818784AB177',
    '72D93B2F-A111-ED11-BD6E-2818784AB177',
    '73D93B2F-A111-ED11-BD6E-2818784AB177',
    '74D93B2F-A111-ED11-BD6E-2818784AB177',
    '75D93B2F-A111-ED11-BD6E-2818784AB177',
    '76D93B2F-A111-ED11-BD6E-2818784AB177',
    '77D93B2F-A111-ED11-BD6E-2818784AB177',
    '78D93B2F-A111-ED11-BD6E-2818784AB177',
    '79D93B2F-A111-ED11-BD6E-2818784AB177',
    '7AD93B2F-A111-ED11-BD6E-2818784AB177',
    '7BD93B2F-A111-ED11-BD6E-2818784AB177',
    '7CD93B2F-A111-ED11-BD6E-2818784AB177',
    '7DD93B2F-A111-ED11-BD6E-2818784AB177',
    '887CFBE3-A011-ED11-BD6E-2818784AB177',
    '897CFBE3-A011-ED11-BD6E-2818784AB177',
    '8A7CFBE3-A011-ED11-BD6E-2818784AB177',
    '8B7CFBE3-A011-ED11-BD6E-2818784AB177',
    '8C7CFBE3-A011-ED11-BD6E-2818784AB177',
    '8D7CFBE3-A011-ED11-BD6E-2818784AB177',
    '8E7CFBE3-A011-ED11-BD6E-2818784AB177',
    '8F7CFBE3-A011-ED11-BD6E-2818784AB177',
    '907CFBE3-A011-ED11-BD6E-2818784AB177',
    '917CFBE3-A011-ED11-BD6E-2818784AB177',
    '927CFBE3-A011-ED11-BD6E-2818784AB177',
    '937CFBE3-A011-ED11-BD6E-2818784AB177',
    '947CFBE3-A011-ED11-BD6E-2818784AB177',
    '957CFBE3-A011-ED11-BD6E-2818784AB177',
    '967CFBE3-A011-ED11-BD6E-2818784AB177',
    'F6B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    '859DC28F-A989-4E6F-B9D1-5F6869347C64',
    '5382A93F-B6B5-4937-BD97-93AECB0DA3D6',
    'E97CF5F3-3C2A-47A1-93FA-37CE9E59CAD5',
    'C028A85A-2DCF-E911-A8EF-4CEDFB610C38',
    '74E05D66-E280-44FA-B84B-ED1D43407597',
    '599610BA-9BAD-4D94-8A09-651E9A6B4589',
    '98DD6DA9-35EF-4970-AB62-BEF9C9402C99',
    '99FEB4AF-0F74-4D0F-8C72-053C41012F01',
    '35512B00-B3E7-42B9-B083-258614A0DBD9',
    'CF28A85A-2DCF-E911-A8EF-4CEDFB610C38',
    '7E5978B4-A0E4-4629-A80C-80B616D36F0B',
    'F2B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    '2D06AD8D-D4B9-4E0E-9A76-4678C70CE5F2',
    'D309A766-9164-470D-846C-DB79472E9737',
    'CC28A85A-2DCF-E911-A8EF-4CEDFB610C38',
    'F8B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    '1A3AC234-75BE-442E-844D-7F23BCF0B3E9',
    '50B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '9DF4A94D-91D1-441E-B93E-731E6E67180A',
    '4EB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    'AC389388-2F54-4C58-9468-EFE112F179D3',
    'D128A85A-2DCF-E911-A8EF-4CEDFB610C38',
    '4FB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    'CA28A85A-2DCF-E911-A8EF-4CEDFB610C38',
    'DB0DE145-551D-461A-9652-AF0480672061',
    'CD28A85A-2DCF-E911-A8EF-4CEDFB610C38',
    '4CB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    'F4B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    '15079D60-636B-4E69-A6D3-3765B2EA493F',
    '5B369D98-F659-4B01-BDAE-4981053414EF',
    'CB28A85A-2DCF-E911-A8EF-4CEDFB610C38',
    'F7B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    '65CDAB77-A285-4C12-8EA0-5E03E565BDFD',
    '8DFD79D8-C2E8-451B-9A4F-0F3E38F577BE',
    'F5B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    '980BE6B2-2B70-4DB7-A319-C4F04C965DE2',
    'F3B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    '9F7EF540-D931-4C52-BD0F-07995E0F8EF1',
    '9CCDED26-656F-4952-9F12-05D20D920AF3',
    '46C2ADA0-FA8B-4DAB-9234-72F0AA86BFBA',
    '4DB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    'CE28A85A-2DCF-E911-A8EF-4CEDFB610C38',
    'FBB00D78-29AC-EA11-A8FE-4CEDFB610C38',
    '4BB10D78-29AC-EA11-A8FE-4CEDFB610C38',
    '03B55FDD-F872-4103-A79B-A54F66E18D7A',
    'BF4EDD24-60A3-4CC5-ABB4-95766F1BFD05',
    '06C4BCAC-6567-4DBB-81E0-5B6ABE90F243',
    'AC87C98D-DA30-4DCB-852C-E7BDF5D3313A',
    'C7454A99-ECE9-43FE-8FF4-A8A2EFE886A7',
    'C828A85A-2DCF-E911-A8EF-4CEDFB610C38',
    'F0A5C888-3093-4CF2-B6D1-49D2CD9D6D20',
    'E815E427-6E08-4EBC-A27A-146A746C81F2',
    'CD5C674A-7348-47FB-9F70-9E060E934504',
    '12C44B59-971C-408B-AAC5-F915A2682DDA',
    '1AE8ED3D-D639-416A-808E-22564C907871',
    '51B10D78-29AC-EA11-A8FE-4CEDFB610C38',
    'D028A85A-2DCF-E911-A8EF-4CEDFB610C38',
    '205E921F-633B-4A3B-92D5-F9FBA4870D7B',
    '1888DC30-BBCE-4A67-A2F6-B3EE053097CB',
    '36773ADE-B093-4510-B096-3F14E7E3A852',
    'C0CDAE4C-6A83-4D1E-BC6F-5A8C93FF2BA2',
    'EBA5D904-E622-4993-9780-6B0D4E6F73E9',
    'B64E5049-B424-4C12-8942-6C6F8DAC398B',
    'EFB00D78-29AC-EA11-A8FE-4CEDFB610C38',
    'EFB00D78-29AC-EA11-A8FE-4CEDFB610C38',
    'EFB00D78-29AC-EA11-A8FE-4CEDFB610C38',
    'F0B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    'F0B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    'F0B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    'F1B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    'F1B00D78-29AC-EA11-A8FE-4CEDFB610C38',
    'F1B00D78-29AC-EA11-A8FE-4CEDFB610C38',
  ] as ReadonlyArray<string>,
}

// Function to recursively lowercase only string arrays
const lowercaseConfigValues = <T extends Record<string, any>>(config: T): T => {
  return Object.fromEntries(
    Object.entries(config).map(([key, value]) => [
      key,
      Array.isArray(value) && typeof value[0] === 'string' // Check if it's a string array
        ? value.map((v) => v.toLowerCase()) // Convert to lowercase
        : typeof value === 'object' && value !== null // If it's an object, recurse
          ? lowercaseConfigValues(value)
          : value, // Leave other values unchanged
    ])
  ) as T
}

export const config = lowercaseConfigValues(unnormalizedConfig)
