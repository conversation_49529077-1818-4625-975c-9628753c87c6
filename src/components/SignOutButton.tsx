'use client'
import { But<PERSON> } from '@/components/ui/button'
import logout from '@/actions/logout'
import { cn } from '@/lib/utils'

type Props = { className?: string }

const SignOutButton = ({ className }: Props) => {
  return (
    <form action={logout}>
      <Button
        className={cn(
          'h-5 p-4 border border-[#cd9557] font-normal text-xs',
          className
        )}
        id="btnSignIn"
        type="submit"
      >
        Sign Out
      </Button>
    </form>
  )
}

export default SignOutButton
