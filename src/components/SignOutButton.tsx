'use client'

import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { env } from '@/env'
import { api } from '@/trpc/react'

interface SignOutButtonProps {
  className?: string
}

export function SignOutButton({ className }: SignOutButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const logoutMutation = api.auth.logout.useMutation({
    trpc: {
      context: {
        skipBatch: true,
      },
    }
  })
  
  useEffect(() => {
    return () => {
      setIsLoading(false)
    }
  }, [])

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      const result = await logoutMutation.mutateAsync()
      if (result && typeof result === 'object' && 'redirectUrl' in result) {
        router.push(result.redirectUrl)
      } else {
        console.error('Unexpected result structure:', result)
        router.push(env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT)
      }
    } catch (error) {
      console.error('Sign out failed:', error)
      router.push(env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT)
    }
  }

  return (
    <button className={className} onClick={handleSignOut} disabled={isLoading}>
      {isLoading ? 'Signing out ...' : 'Sign Out'}
    </button>
  )
}