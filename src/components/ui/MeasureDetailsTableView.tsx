'use client'

import { api } from '@/trpc/react'
import { useUIStore } from '@/stores/ui'
import {
  ColumnDef,
  ColumnFiltersState,
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table'
import { X } from 'lucide-react'
import React, { useEffect, useMemo, useState } from 'react'
import Loader from './Loader'
import { ScorecardView } from '@/enums/scorecardView'
import { useUserSessionStore } from '@/stores/userSession'
import dayjs from 'dayjs'
import { cn } from '@/lib/utils'
import { Pagination } from '../measures/pagination'
import { getSortingIcon } from '../ui/sortIcon'
import { customStringFilter } from '@/lib/customStringFilter'
import * as Dialog from '@radix-ui/react-dialog'
import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'

interface DataRow {
  benchmark: string
  date: string
  denominator: string
  denominatorExclusion: string
  endDate?: string | null
  exception: string
  fullDate: string
  goal: string
  inDenominatorOnly: string
  isPatientLevelAccessAvailable: boolean
  noDrill: boolean
  numerator: string
  population: string
  rate: string
  sourceContainerIdentifier?: string | null
}

interface PatientDataRow {
  patientName: string
  age: number
  gender: string
  result: string
  dischargeDate: string
}

const columnHelper = createColumnHelper<DataRow>()

// Helper function to safely convert string to number
const toNumber = (value: string): number => {
  const num = Number(value.replace(/[^0-9.-]+/g, ''))
  return isNaN(num) ? 0 : num
}

// Helper function for date comparison
const compareDates = (a: string, b: string): number => {
  // First try to parse as quarter (e.g., "Q1-2024")
  const quarterRegex = /Q(\d)-(\d{4})/
  const aMatch = a.match(quarterRegex)
  const bMatch = b.match(quarterRegex)

  if (aMatch && bMatch) {
    const aYear = parseInt(aMatch[2]!)
    const bYear = parseInt(bMatch[2]!)
    if (aYear !== bYear) return aYear - bYear
    return parseInt(aMatch[1]!) - parseInt(bMatch[1]!)
  }

  // If not quarters, try regular date parsing
  const aDate = new Date(a)
  const bDate = new Date(b)
  return aDate.getTime() - bDate.getTime()
}

const categoryMapper: { [key: string]: string } = {
  exception: 'Denominator Exception (only)',
  denominator: '*',
  numerator: 'Numerator',
  denominatorExclusion: 'Excluded (only)',
  inDenominatorOnly: 'Denominator (only)',
  population: '*',
}

function PatientDetailDialog({
  isOpen,
  onClose,
  span,
  category,
  categoryHeader,
}: {
  isOpen: boolean
  onClose: () => void
  span: string
  category: string
  categoryHeader: string
}) {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])

  const {
    currentMeasureId,
    entityId,
    entityType,
    aggregationType,
    sourceContainerIdentifier,
  } = useUIStore()

  const { patientDetailAccess } = useUserSessionStore()

  let { primaryMeasureType } = useUserSessionStore()
  const { data: patientDetailsData, isLoading: ispatientDetailDataLoading } =
    api.patients.getPatientDetails.useQuery({
      measureId: currentMeasureId!,
      periodSpan: span,
      entityDetailType: entityType!,
      entityId: entityId!,
      sourceContainerIdentifier: sourceContainerIdentifier!,
      isPatientLevelAccessAvailable: patientDetailAccess.IsOrgLevelAccess,
      scorecardView: aggregationType!,
      primaryMeasureType: primaryMeasureType!,
      category: categoryMapper[category] || '*',
    })

  const patientDetails = useMemo(() => patientDetailsData, [patientDetailsData])

  const columnHelper = createColumnHelper<PatientDataRow>()
  const filterFunction = customStringFilter<PatientDataRow>()

  const columns = [
    columnHelper.accessor('patientName', {
      header: 'Patient Name',
      cell: (info) => info.getValue(),
      filterFn: filterFunction,
    }),
    columnHelper.accessor('age', {
      header: 'Age',
      cell: (info) => info.getValue(),
      sortingFn: (a, b) => a.original.age - b.original.age,
      filterFn: filterFunction, // Apply customStringFilter to age column
    }),
    columnHelper.accessor('gender', {
      header: 'Gender',
      cell: (info) =>
        info.getValue() === 'M'
          ? 'Male'
          : info.getValue() === 'F'
            ? 'Female'
            : 'Other',
      filterFn: filterFunction,
    }),
    columnHelper.accessor('dischargeDate', {
      header: 'Discharge Date',
      cell: (info) => info.getValue(),
      filterFn: filterFunction,
    }),
  ]

  const memoColumns = useMemo(() => columns, [])
  const table = useReactTable({
    data: patientDetails || [],
    columns: memoColumns,
    state: {
      columnFilters,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnFiltersChange: setColumnFilters,
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  })

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/20 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed top-[169px] bottom-0 right-0  z-[100] w-full max-w-[55%] bg-white shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right duration-300 overflow-auto">
          <div className="flex justify-end items-center p-5">
            <Dialog.Title className="text-lg font-medium"></Dialog.Title>
            <Dialog.Close asChild>
              <button className="rounded-full p-1 hover:bg-gray-100">
                <X className="h-5 w-5" />
                <span className="sr-only">Close</span>
              </button>
            </Dialog.Close>
          </div>
          <div className="flex justify-center items-center font-semibold text-[18px] font-open-sans">
            Population Data For {span}: {categoryHeader}
          </div>
          <div className="p-4">
            <div className="h-[200px]">
              {ispatientDetailDataLoading ? (
                <div className="relative top-4">
                  <Loader className="" />
                </div>
              ) : (
                <div
                  className={cn(
                    'overflow-y-auto',
                    'border-x-[#DDDCDF]',
                    'border-x-[1px]',
                    'border-t-[#DDDCDF]',
                    'border-t-[1px]',
                    'mac-scrollbar'
                  )}
                  style={{ maxHeight: `${440}px` }}
                >
                  <table className="w-full border-collapse font-open-sans text-xs">
                    <thead>
                      {table.getHeaderGroups().map((headerGroup) => (
                        <tr key={headerGroup.id}>
                          {headerGroup.headers.map((header) => (
                            <th
                              key={header.id}
                              className="bg-[#45536D] text-white font-semibold text-xs uppercase p-0"
                              style={{
                                position: 'sticky',
                                top: 0,
                                zIndex: 10,
                                minWidth:
                                  header.id === 'group' ? '200px' : '120px',
                              }}
                            >
                              <div className="flex items-center px-4 py-3 justify-start">
                                {flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                                {header.column.getCanFilter() && (
                                  <div className="flex ml-4">
                                    <DataTableColumnFilter
                                      column={header.column}
                                      setColumnFilters={setColumnFilters}
                                    />
                                  </div>
                                )}
                              </div>
                            </th>
                          ))}
                        </tr>
                      ))}
                    </thead>
                    <tbody>
                      {table.getRowModel().rows.map((row, index) => (
                        <tr
                          key={index}
                          className="bg-[#F9F8F9] border-b-4 border-white"
                        >
                          {row.getVisibleCells().map((cell) => (
                            <td key={cell.id} className="p-0 text-left">
                              <div className="px-4 py-3">
                                {flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext()
                                )}
                              </div>
                            </td>
                          ))}
                        </tr>
                      ))}
                      {table.getRowModel().rows.length === 0 && (
                        <tr>
                          <td colSpan={columns.length} className="p-0">
                            <div className="px-4 py-6 text-center">
                              No data available
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                  {table.getPageCount() > 1 && <Pagination table={table} />}
                </div>
              )}
            </div>
          </div>
          <Dialog.Description />
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

function PatientDetailCell({
  value,
  span,
  category,
  categoryHeader,
}: {
  value: string
  span: string
  category: string
  categoryHeader: string
}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      {value === '-' ? (
        <span className="text-[#205782]">{value}</span>
      ) : (
        <button
          className="flex items-center gap-2 cursor-pointer"
          onClick={() => setIsDialogOpen(true)}
        >
          <span className="text-[#205782] text-xs underline">{value}</span>
        </button>
      )}
      {isDialogOpen && (
        <PatientDetailDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          span={span}
          category={category}
          categoryHeader={categoryHeader}
        />
      )}
    </>
  )
}

const columns = (measureName?: string) => [
  columnHelper.accessor('date', {
    header: 'Date',
    cell: ({ row }) => <div>{row.getValue('date')}</div>,
    sortingFn: (a, b) => compareDates(a.original.date, b.original.date),
  }),
  columnHelper.accessor('rate', {
    header: measureName?.toLowerCase().includes('median')
      ? 'Median Time'
      : 'Rate',
    cell: ({ getValue }) => (
      <div className="text-center">
        {String(getValue()).indexOf('<br>') >= 0 ? (
          <>
            <span>{String(getValue()).split('<br>')[0]}</span> <br></br>
            <span>{String(getValue()).split('<br>')[1]}</span>
          </>
        ) : (
          String(getValue())
        )}
      </div>
    ),
    sortingFn: (a, b) => toNumber(a.original.rate) - toNumber(b.original.rate),
  }),
  columnHelper.accessor('population', {
    header: 'Initial Population',
    cell: ({ row }) => (
      <PatientDetailCell
        value={String(row.getValue('population'))}
        span={row.original.date}
        category="population"
        categoryHeader="Initial Population"
      />
    ),
    sortingFn: (a, b) =>
      toNumber(a.original.population) - toNumber(b.original.population),
  }),
  columnHelper.accessor('inDenominatorOnly', {
    header: 'Denominator Only',
    cell: ({ row }) => (
      <PatientDetailCell
        value={String(row.getValue('inDenominatorOnly'))}
        span={row.original.date}
        category="inDenominatorOnly"
        categoryHeader="Denominator Only"
      />
    ),
    sortingFn: (a, b) =>
      toNumber(a.original.inDenominatorOnly) -
      toNumber(b.original.inDenominatorOnly),
  }),
  columnHelper.accessor('denominatorExclusion', {
    header: 'Den. Exclusion',
    cell: ({ row }) => (
      <PatientDetailCell
        value={String(row.getValue('denominatorExclusion'))}
        span={row.original.date}
        category="denominatorExclusion"
        categoryHeader="Den. Exclusion"
      />
    ),
    sortingFn: (a, b) =>
      toNumber(a.original.denominatorExclusion) -
      toNumber(b.original.denominatorExclusion),
  }),
  columnHelper.accessor('numerator', {
    header: 'Numerator',
    cell: ({ row }) => (
      <PatientDetailCell
        value={String(row.getValue('numerator'))}
        span={row.original.date}
        category="numerator"
        categoryHeader="Numerator"
      />
    ),
    sortingFn: (a, b) =>
      toNumber(a.original.numerator) - toNumber(b.original.numerator),
  }),
  columnHelper.accessor('denominator', {
    header: 'Denominator',
    cell: ({ row }) => (
      <PatientDetailCell
        value={String(row.getValue('denominator'))}
        span={row.original.date}
        category="denominator"
        categoryHeader="Denominator"
      />
    ),
    sortingFn: (a, b) =>
      toNumber(a.original.denominator) - toNumber(b.original.denominator),
  }),
  columnHelper.accessor('exception', {
    header: 'Exception',
    cell: ({ row }) => (
      <PatientDetailCell
        value={String(row.getValue('exception'))}
        span={row.original.date}
        category="exception"
        categoryHeader="Exception"
      />
    ),
    sortingFn: (a, b) =>
      toNumber(a.original.exception) - toNumber(b.original.exception),
  }),
]

type Props = {
  className?: string
}
export const MeasureDetailsTableView = ({ className }: Props) => {
  const [isDataReady, setIsDataReady] = useState(false)
  const [sorting, setSorting] = useState<SortingState>([])
  const {
    currentMeasureId,
    currentMeasureName,
    entityId,
    entityType,
    aggregationType,
    currentMeasureDetailTab,
    isIAPIMeasure,
    currentStartDate,
    currentEndDate,
  } = useUIStore()

  let { primaryMeasureType } = useUserSessionStore()
  const iaPIMeasureDetails = api.measures.getIAPIMeasureDetails.useQuery(
    {
      measureIdentifier: currentMeasureId!,
      startDate: dayjs(currentStartDate)?.utc().toDate()!,
      endDate: dayjs(currentEndDate)?.utc().toDate()!,
      entityId: entityId!,
    },
    { enabled: isIAPIMeasure && isDataReady }
  )

  const measureMetaData = api.measures.getMeasureResultDetails.useQuery(
    {
      measureId: currentMeasureId!,
      startDate: dayjs(currentStartDate)?.utc().toDate(),
      endDate: dayjs(currentEndDate)?.utc().toDate(),
      entityId: entityId!,
      entityDetailType: entityType!,
      aggregationType: aggregationType,
      measureType: primaryMeasureType,
    },
    {
      enabled: isDataReady && !isIAPIMeasure,
    }
  )

  !isIAPIMeasure &&
    api.measures.getMeasureResultDetails.usePrefetchQuery({
      measureId: currentMeasureId!,
      startDate: dayjs(currentStartDate)?.utc().toDate()!,
      endDate: dayjs(currentEndDate)?.utc().toDate()!,
      entityId: entityId!,
      entityDetailType: entityType!,
      aggregationType: ScorecardView.Yearly,
      measureType: primaryMeasureType,
    })

  !isIAPIMeasure &&
    api.measures.getMeasureResultDetails.usePrefetchQuery({
      measureId: currentMeasureId!,
      startDate: dayjs(currentStartDate).utc()?.toDate()!,
      endDate: dayjs(currentEndDate)?.utc().toDate()!,
      entityId: entityId!,
      entityDetailType: entityType!,
      aggregationType: ScorecardView.Monthly,
      measureType: primaryMeasureType,
    })

  !isIAPIMeasure &&
    api.measures.getMeasureResultDetails.usePrefetchQuery({
      measureId: currentMeasureId!,
      startDate: dayjs(currentStartDate)?.utc().toDate()!,
      endDate: dayjs(currentEndDate)?.utc().toDate()!,
      entityId: entityId!,
      entityDetailType: entityType!,
      aggregationType: ScorecardView.Quarterly,
      measureType: primaryMeasureType,
    })

  api.measures.getMeasureMetaData.usePrefetchQuery({
    measureId: currentMeasureId!,
  })

  useEffect(() => {
    if (currentStartDate && currentEndDate) {
      setIsDataReady(true)
    }
  }, [
    currentStartDate ? dayjs(currentStartDate).toISOString() : '',
    currentEndDate ? dayjs(currentEndDate).toISOString() : '',
  ])

  const table = useReactTable({
    data: measureMetaData.data ?? [],
    columns: columns(currentMeasureName),
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    sortDescFirst: false, // This ensures ascending sort on first click
  })

  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
    lineHeight: '1.5',
    minHeight: '45px',
  } as React.CSSProperties

  if (isIAPIMeasure) {
    const getFormatedTitle = (col: string) => {
      const titleMap: { [key: string]: string } = {
        implementationstartdate: 'Implementation/nStart Date',
        implementationenddate: 'Implementation/nEnd Date',
        NumberofDays: 'Number Of Days',
        ActivityID: 'Activity ID',
        Groupname: 'Group Name',
      }

      return titleMap[col] || col.replaceAll(/([A-Z])/g, ' $1')
    }

    const iapiResultTable = useReactTable({
      data: iaPIMeasureDetails.data ?? [],
      columns: React.useMemo(() => {
        if (iaPIMeasureDetails.data?.length === 0) return []

        if (!iaPIMeasureDetails.data) return []

        const excludedColumns = [
          'ActivityID',
          'Groupname',
          'ActivityName',
          'MeasureID',
        ]
        return Object.keys(iaPIMeasureDetails.data[0])
          .filter((x) => !excludedColumns.includes(x))
          .map(
            (key) =>
              ({
                accessorKey: key,
                header: () =>
                  getFormatedTitle(key).includes('/n') ? (
                    <div>
                      <span>
                        {getFormatedTitle(key).substring(
                          0,
                          getFormatedTitle(key).indexOf('/n')
                        )}
                      </span>
                      <br />
                      <span>
                        {getFormatedTitle(key).substring(
                          getFormatedTitle(key).indexOf('/n') + 2
                        )}
                      </span>
                    </div>
                  ) : (
                    <span>{getFormatedTitle(key)}</span>
                  ),
                cell: (info: any) => (
                  <div className="text-center">{String(info.getValue())}</div>
                ),
              }) as ColumnDef<any>
          )
      }, [iaPIMeasureDetails.data]),
      state: {
        sorting,
      },
      onSortingChange: setSorting,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      sortDescFirst: false,
    })

    return (
      <>
        {iaPIMeasureDetails.isPending || !currentStartDate ? (
          <Loader />
        ) : (
          <table className="w-full border-collapse">
            <thead>
              {iapiResultTable.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      colSpan={header.colSpan}
                      style={headerStyle}
                      className="border-b border-l cursor-pointer select-none pt-['18px'] pb-['18px']"
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      {header.isPlaceholder ? null : (
                        <div className="flex justify-center items-center">
                          <span className="flex-grow text-center">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                          </span>
                          {getSortingIcon(
                            header.column.getIsSorted() as boolean,
                            header.column.getIsSorted() === 'desc'
                          )}
                        </div>
                      )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {iapiResultTable.getRowModel().rows.map((row, i) => (
                <React.Fragment key={row.id}>
                  <tr
                    key={row.id}
                    className={`${
                      row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                    }`}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={`${row.id}-${cell.id}`}
                        className={`p-2 font-normal text-center border-[#DDDCDF] border-b border-l align-middle font-open-sans  pl-['10px'] pr-['10px'] pb-['24px'] pt-['24px'] text-['#000000de'] `}
                        style={{ fontSize: '14px', padding: '10px 24px' }}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                </React.Fragment>
              ))}
              {iapiResultTable.getRowModel().rows.length == 0 && (
                <tr>No Data available</tr>
              )}
            </tbody>
          </table>
        )}
      </>
    )
  } else {
    return (
      <div className="flex flex-col w-full font-open-sans text-sm">
        <div
          className={cn(
            'max-h-[500px]',
            'overflow-y-auto',
            'border-x-[#DDDCDF]',
            'border-x-[1px]',
            'border-t-[#DDDCDF]',
            'border-t-[1px]',
            'mac-scrollbar',
            className
          )}
        >
          {measureMetaData.isPending || !currentStartDate ? (
            <Loader />
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <tr key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <th
                            key={header.id}
                            colSpan={header.colSpan}
                            style={headerStyle}
                            className="border-b border-l cursor-pointer select-none pt-['18px'] pb-['18px']"
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            {header.isPlaceholder ? null : (
                              <div className="flex justify-center items-center">
                                <span className="flex-grow text-center">
                                  {flexRender(
                                    header.column.columnDef.header,
                                    header.getContext()
                                  )}
                                </span>
                                {getSortingIcon(
                                  header.column.getIsSorted() as boolean,
                                  header.column.getIsSorted() === 'desc'
                                )}
                              </div>
                            )}
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                  <tbody>
                    {table.getRowModel().rows.map((row, i) => {
                      return (
                        <React.Fragment key={row.id}>
                          <tr
                            key={row.id}
                            className={`${
                              row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                            }`}
                          >
                            {row.getVisibleCells().map((cell) => (
                              <td
                                key={`${row.id}-${cell.id}`}
                                className={`p-2 font-normal text-center border-[#DDDCDF] border-b  border-r border-l align-middle font-open-sans  pl-['10px'] pr-['10px'] pb-['24px'] pt-['24px'] text-['#000000de'] `}
                                style={{
                                  fontSize: '14px',
                                  padding: '10px 24px',
                                }}
                              >
                                <span
                                  className={`inline-flex ${cell.column.id === 'date' && 'relative -left-3 '}`}
                                  onMouseEnter={() => {
                                    if (cell.column.id === 'date') {
                                      console.log(
                                        'prefetching',
                                        row.original.date
                                      )
                                    }
                                  }}
                                >
                                  {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                  )}
                                </span>
                              </td>
                            ))}
                          </tr>
                        </React.Fragment>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </div>

        {currentMeasureDetailTab === 'TABLE' && <Pagination table={table} />}
      </div>
    )
  }
}
