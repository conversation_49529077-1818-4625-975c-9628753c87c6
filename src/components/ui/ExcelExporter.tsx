'use client'

import React, { useRef, useState, useEffect } from 'react'
import Image from 'next/image'
import * as XLSX from 'xlsx'
import { Button } from './button'
import DownloadIconSvg from '../../../public/images/download.svg'
import checkedSVG from '../../../public/images/checked-radio.svg'

interface ExcelExporterProps {
  data: any[]
  columns: { header: string; accessorKey: string }[]
  buttonText?: string
  filePrefix?: string
  disabled?: boolean
}

const ExcelExporter: React.FC<ExcelExporterProps> = ({
  data,
  columns,
  buttonText = 'EXPORT TO EXCEL',
  filePrefix = 'export',
  disabled = false,
}) => {
  const [isExporting, setIsExporting] = useState(false)
  const csvExportIMGref = useRef<HTMLImageElement>(null)
  const [hasData, setHasData] = useState(false)

  // Check if data is available
  useEffect(() => {
    console.log("ExcelExporter received data:", {
      dataLength: data?.length || 0,
      columnsLength: columns?.length || 0
    })

    setHasData(Array.isArray(data) && data.length > 0 && Array.isArray(columns) && columns.length > 0)
  }, [data, columns])

  const handleExcelExport = () => {
    if (!data || data.length === 0 || !columns || columns.length === 0) {
      console.error("No data available for export")
      return
    }

    setIsExporting(true)

    try {
      // Export logic...
      const headers = columns.map(col => col.header)

      const exportData = [
        headers,
        ...data.map(row => {
          return headers.map(header => {
            const col = columns.find(c => c.header === header)
            const key = col?.accessorKey
            return key && row[key] !== undefined ? row[key] : ''
          })
        })
      ]

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.aoa_to_sheet(exportData)

      const colWidths = headers.map(() => ({ wch: 20 }))
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, filePrefix)

      const date = new Date().toISOString().slice(0, 10)
      XLSX.writeFile(wb, `${filePrefix}_${date}.xlsx`)

      if (csvExportIMGref.current) {
        csvExportIMGref.current.src = checkedSVG.src

        setTimeout(() => {
          if (csvExportIMGref.current) {
            csvExportIMGref.current.src = DownloadIconSvg.src
          }
        }, 3000)
      }
    } catch (error) {
      console.error('Error exporting to Excel:', error)
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <Button
      onClick={handleExcelExport}
      disabled={disabled || isExporting || !hasData}
      className="bg-white-300 hover:bg-white-400 text-[#1a5276] font-bold py-2 px-4 rounded inline-flex items-center"
    >
      <Image
        ref={csvExportIMGref}
        src={DownloadIconSvg}
        alt="Download icon"
        width={18}
        height={18}
        style={{
          width: '18px',
          height: 'auto',
        }}
        data-testid="download-icon"
      />
      <span className="ml-2">{buttonText}</span>
    </Button>
  )
}

export default ExcelExporter


