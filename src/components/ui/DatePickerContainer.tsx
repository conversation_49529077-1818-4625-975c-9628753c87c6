import React, { useEffect, useMemo, useRef, useState } from 'react'
import Image from 'next/image'
import { CalendarDays, Plus, Minus } from 'lucide-react'

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import dayjs, { Dayjs } from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import AdvancedDatePicker from './AdvancedDatePicker'
import { calculateQuarterDates, useDateStore } from '@/stores/dates'
import checked from '../../../public/images/checked-radio.svg'
import empty from '../../../public/images/empty-radio.svg'
import { ScorecardView } from '@/enums/scorecardView'
import { type DatePickerMode } from '@/types/datePickerMode'
import { useClickOutside } from '@/hooks/useClickOutside'
import { useUIStore } from '@/stores/ui'
import PickerButton from './PickerButton'
import { useViewStore } from '@/stores/viewStore'

// Extend dayjs with quarter plugin
dayjs.extend(quarterOfYear)

const DatePickerContainer = () => {
  const ref = useRef<HTMLDivElement>(null)
  const originalRangeRef = useRef<[Dayjs | null, Dayjs | null] | null>(null)
  const popoverRef = useRef<HTMLDivElement>(null)

  const [isMainExpanded, setIsMainExpanded] = useState(false)
  const [isIntervalOpen, setIsIntervalOpen] = useState(true)
  const [isMonthOpen, setIsMonthOpen] = useState(true)
  const [openAdvancedPicker, setOpenAdvancedPicker] = useState(false)
  const { mode, selectedRange, setMode, setSelectedRange, setSelectedPeriod } =
    useDateStore()
  const { setAggregationType } = useUIStore()
  const { currentView } = useViewStore()

  const [localSelectedRange, setLocalSelectedRange] = useState<
    [Dayjs | null, Dayjs | null] | null
  >(null)
  const [localMode, setLocalMode] = useState<
    'Monthly' | 'Quarterly' | 'Yearly'
  >('Quarterly')

  const memoizedSelectedRange = useMemo(() => selectedRange, [selectedRange])

  useEffect(() => {
    const startDate = memoizedSelectedRange[0]
    const endDate = memoizedSelectedRange[1]

    if (startDate && endDate) {
      setLocalSelectedRange([dayjs(startDate).utc(), dayjs(endDate).utc()])
    }
  }, [memoizedSelectedRange])

  useEffect(() => {
    if (currentView?.from && currentView?.to) {
      originalRangeRef.current = [
        dayjs(currentView.from),
        dayjs(currentView.to),
      ]
    }
  }, [currentView])

  useEffect(() => {
    setLocalMode(mode as 'Monthly' | 'Quarterly' | 'Yearly')

    if (localSelectedRange && localSelectedRange[0] && localSelectedRange[1]) {
      let startDate = dayjs(localSelectedRange[0])
      let endDate = dayjs(localSelectedRange[1])

      switch (mode) {
        case 'Quarterly':
          // Existing Quarterly logic
          let startofQuarters = [0, 3, 6, 9]
          let endOfQuarters = [2, 5, 8, 11]
          if (
            !(
              startofQuarters.includes(startDate.utc().month()) &&
              endOfQuarters.includes(endDate.utc().month())
            )
          ) {
            let startDateMonth = Number(startDate.utc().month())
            let quarterStarts = 0
            switch (startDateMonth) {
              case 0:
              case 1:
              case 2:
                quarterStarts = 0
                break
              case 3:
              case 4:
              case 5:
                quarterStarts = 3
                break
              case 6:
              case 7:
              case 8:
                quarterStarts = 6
                break
              case 9:
              case 10:
              case 11:
                quarterStarts = 9
                break
            }
            startDate = startDate.utc().month(quarterStarts).startOf('month')
            const endDateMonth = Number(endDate.utc().month())
            let quaterEndmonth = 0

            switch (endDateMonth) {
              case 0:
              case 1:
              case 2:
                quaterEndmonth = 0
                break
              case 3:
              case 4:
              case 5:
                quaterEndmonth = 3
                break
              case 6:
              case 7:
              case 8:
                quaterEndmonth = 6
                break
              case 9:
              case 10:
              case 11:
                quaterEndmonth = 9
                break
            }
            endDate = endDate.utc().month(quaterEndmonth).endOf('month')
          }
          break

        case 'Yearly':
          // Yearly logic - adjust to full year
          startDate = startDate.utc().month(0).startOf('month')
          endDate = endDate.utc().month(11).endOf('month')
          break

        case 'Monthly':
          // Monthly logic - adjust to full month
          startDate = startDate.utc().startOf('month')
          endDate = endDate.utc().endOf('month')
          break
      }

      setLocalSelectedRange([dayjs(startDate), dayjs(endDate)])
    }
  }, [mode])

  const handleChange = (value: Dayjs | null | [Dayjs | null, Dayjs | null]) => {
    if (Array.isArray(value)) {
      setLocalSelectedRange(value)
    } else {
      setLocalSelectedRange([value, value])
    }
  }

  const handleClearAll = () => {
    // revert to original store’s viewType
    const defaultMode = (currentView?.viewType as DatePickerMode) ?? 'Quarterly'
    setMode(defaultMode)

    // revert to the store’s original date range
    if (originalRangeRef.current) {
      setLocalSelectedRange(originalRangeRef.current)
    } else {
      // fallback if originalRangeRef was never set
      const { startDate, endDate } = calculateQuarterDates()

      setLocalSelectedRange([dayjs(startDate), dayjs(endDate)])
    }
  }

  const handleApply = () => {
    if (localSelectedRange) {
      setAggregationType(ScorecardView[mode as keyof typeof ScorecardView])
      handlePeriodChange(mode as keyof typeof ScorecardView)
      setSelectedRange(localSelectedRange)
    }
  }

  const handlePeriodChange = (period: keyof typeof ScorecardView) => {
    const selectedPeriod = Object.entries(ScorecardView).find((x) => {
      return x?.[0] === period
    })?.[0] as keyof typeof ScorecardView

    setSelectedPeriod(selectedPeriod)

    setIsMainExpanded(false)
  }

  useEffect(() => {
    if (currentView) {
      setAggregationType(
        mode in ScorecardView
          ? ScorecardView[mode as keyof typeof ScorecardView]
          : ScorecardView.Quarterly
      )
    }
  }, [currentView])

  const getSecondSectionLabel = () => {
    switch (mode) {
      case 'Yearly':
        return 'Yearly'
      case 'Quarterly':
        return 'Quarterly'
      default:
        return 'Monthly'
    }
  }

  useClickOutside(ref, (event: any) => {
    if (event.target.closest('.date-picker-popup')) {
      return // Ignore clicks inside the popover content
    }

    setIsMainExpanded(false)
  })

  useEffect(() => {
    // Watch for changes that indicate a clear operation
    const viewType = currentView?.viewType
    const defaultMode = (viewType as DatePickerMode) ?? 'Quarterly'
    const [startDate, endDate] = memoizedSelectedRange
  }, [mode, memoizedSelectedRange, currentView?.viewType])

  useEffect(() => {
    // This effect specifically watches for changes to currentView
    if (currentView) {
      // Reset local state based on the current view
      setLocalMode(
        (currentView.viewType as 'Monthly' | 'Quarterly' | 'Yearly') ||
          'Quarterly'
      )

      // If the view has dates, use them
      if (currentView.from && currentView.to) {
        const fromDate = dayjs(currentView.from)
        const toDate = dayjs(currentView.to)
        setLocalSelectedRange([fromDate, toDate])

        // Also update the original reference for "Clear All" functionality
        originalRangeRef.current = [fromDate, toDate]
      }
    }
  }, [currentView?.from, currentView?.to, currentView?.viewType])

  return (
    <div ref={ref} className="relative w-[225px]">
      {/* Picker Button */}
      {/*  update this only when apply is clicked */}
      <PickerButton text={formatLocalAbbreviatedMonths(selectedRange, mode)}>
        <div className="p-4 space-y-4">
          {/* Interval Section */}
          <div className="bg-white border-ui-dark-gray rounded border-[0.5px] p-4 ">
            <button
              onClick={() => setIsIntervalOpen(!isIntervalOpen)}
              className="w-full flex justify-between items-center text-blue-4 font-semibold mb-2 focus:outline-none font-open-sans text-[10px]"
            >
              Interval
              {isIntervalOpen ? (
                <Minus className="w-5 h-5" />
              ) : (
                <Plus className="w-5 h-5" />
              )}
            </button>

            {isIntervalOpen && (
              <div className="space-y-2 font-open-sans relative -left-5">
                {Object.entries(ScorecardView).map(([interval]) => (
                  <label
                    key={interval}
                    className="flex items-center space-x-2 font-open-sans text-black-400 text-[10px] "
                  >
                    <input
                      type="radio"
                      name="interval"
                      value={interval}
                      checked={mode === interval}
                      onChange={(e) =>
                        setMode(e.target.value as DatePickerMode)
                      }
                      className="appearance-none w-4 h-4 border-gray-300 focus:ring-blue-800 font-open-sans rounded-full focus:outline-none"
                    />
                    <div>
                      {mode.toLowerCase() === interval.toLowerCase() ? (
                        <Image
                          className=""
                          src={checked}
                          alt="check"
                          width={14}
                          height={14}
                        />
                      ) : (
                        <Image src={empty} alt="check" width={14} height={14} />
                      )}
                    </div>
                    <span className="text-[10px] font-open-sans font-normal">
                      {interval}
                    </span>
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Dynamic Second Section */}
          <div className="bg-white border-ui-dark-gray border-[0.5px] p-4 rounded">
            <button
              onClick={() => setIsMonthOpen(!isMonthOpen)}
              className="w-full flex justify-between items-center text-blue-4 font-open-sans font-semibold mb-2 text-[12px]"
            >
              {getSecondSectionLabel()}
              {isMonthOpen ? (
                <Minus className="w-5 h-5" />
              ) : (
                <Plus className="w-5 h-5" />
              )}
            </button>

            {isMonthOpen && (
              <div className="relative font-open-sans">
                <Popover
                  open={openAdvancedPicker}
                  onOpenChange={setOpenAdvancedPicker}
                >
                  <PopoverTrigger asChild>
                    <div className="flex items-center border border-gray-300 rounded p-1 cursor-pointer">
                      <CalendarDays className="w-5 h-5 text-[#1B4A70] mr-2" />
                      <input
                        readOnly
                        value={formatLocalAbbreviatedMonths(
                          localSelectedRange,
                          localMode
                        )}
                        className="w-full outline-none text-gray-1 text-[10px] cursor-pointer font-normal font-open-sans"
                        placeholder={`Choose date`}
                      />
                    </div>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-auto p-0 border-0 rounded-lg shadow-lg font-open-sans"
                    align="start"
                  >
                    <div ref={popoverRef}>
                      <AdvancedDatePicker
                        selectedRange={localSelectedRange}
                        setSelectedRange={setLocalSelectedRange}
                        selectionType={localMode}
                        onChange={handleChange}
                        isOpen={true}
                        toggleIsOpen={() => setIsMainExpanded(false)}
                        toggleAdvancedPickerOpen={() =>
                          setOpenAdvancedPicker(!openAdvancedPicker)
                        }
                      />
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            )}
          </div>

          {/* Bottom Buttons */}
          <div className="flex justify-between items-center mt-6">
            <button
              onClick={() => {
                handleClearAll()
              }}
              className="text-[12px] text-blue-4 hover:text-blue-1 font-normal underline font-open-sans"
            >
              Clear All
            </button>
            <button
              className="text-[13px] text-white px-6 py-2 font-semibold rounded bg-ui-dark-gray border border-ui-dark-gray hover:bg-transparent hover:text-ui-dark-gray font-open-sans"
              onClick={() => {
                setIsMainExpanded(false)

                handleApply()
              }}
            >
              APPLY
            </button>
          </div>
        </div>
      </PickerButton>
    </div>
  )
}

export default DatePickerContainer

/**
 * A local utility that replicates your store’s getAbbreviatedMonthsLabels logic,
 * but for local range + local mode.
 */
export const formatLocalAbbreviatedMonths = (
  localRange: [Dayjs | null, Dayjs | null] | null,
  localMode: 'Yearly' | 'Quarterly' | 'Monthly' | string
): string => {
  // If absolutely nothing, just return empty
  if (!localRange || !localRange[0]) {
    return ''
  }

  let [startDate, endDate] = localRange

  if (!startDate) {
    return ''
  }

  // If you want to tweak start/end for certain modes:
  switch (localMode) {
    case 'Yearly':
      // Force range from Jan–Dec
      startDate = startDate.utc().month(0).startOf('month') // January
      if (endDate) endDate = endDate.utc().month(11).endOf('month') // December
      break

    case 'Quarterly':
      let startofQuarters = [0, 3, 6, 9]
      let endOfQuarters = [2, 5, 8, 11]
      if (!endDate) endDate = startDate
      if (
        !(
          startofQuarters.includes(startDate.utc().month()) &&
          endOfQuarters.includes(endDate.utc().month())
        )
      ) {
        let startDateMonth = Number(startDate.utc().month())
        let quarterStarts = 0
        switch (startDateMonth) {
          case 0:
          case 1:
          case 2:
            quarterStarts = 0
            break
          case 3:
          case 4:
          case 5:
            quarterStarts = 3
            break
          case 6:
          case 7:
          case 8:
            quarterStarts = 6
            break
          case 9:
          case 10:
          case 11:
            quarterStarts = 9
            break
        }
        startDate = startDate.utc().month(quarterStarts).startOf('month')
        const endDateMonth = Number(endDate.utc().month())
        let quaterEndmonth = 0

        switch (endDateMonth) {
          case 0:
          case 1:
          case 2:
            quaterEndmonth = 0
            break
          case 3:
          case 4:
          case 5:
            quaterEndmonth = 3
            break
          case 6:
          case 7:
          case 8:
            quaterEndmonth = 6
            break
          case 9:
          case 10:
          case 11:
            quaterEndmonth = 9
            break
        }
        endDate = endDate.utc().month(quaterEndmonth).endOf('month')
      }
    //setSelectedRange([startDate, endDate])

    case 'Monthly':
    default:
      // Add your own logic or leave as-is
      break
  }

  // If no end date, return a single value
  if (!endDate) {
    return startDate.utc().format('MMM YYYY').toUpperCase()
  }

  return `${startDate.utc().format('MMM YYYY').toUpperCase()} - ${endDate.utc().format('MMM YYYY').toUpperCase()}`
}
