import React, { useRef, useState } from 'react'
import { api } from '@/trpc/react'
import PickerButton from './PickerButton'
import { useClickOutside } from '@/hooks/useClickOutside'

export const MeasureDetailsReportPicker = () => {
  const [isExpanded, setIsExpanded] = useState(false)
  const reports = api.report.getMeasureInsightReports.useQuery()

  const ref = useRef<HTMLDivElement>(null)
  useClickOutside(ref, () => setIsExpanded(false))

  return (
    <div ref={ref} className="w-[135px]">
      <PickerButton text="REPORTS">
        <div className="p-4">
          {reports.data?.map((report) => (
            <div
              key={report.ReportId}
              className="flex items-center pt-[10px] pl-[8px] pr-[8px] pb-[10px] border-[1px] mb-2 hover:bg-white border-[#97A4BA]"
              onClick={() => {
                alert(`${report.ReportName} in progress`)
              }}
            >
              <span className="text-[#1B4A70] text-[12px]  font-open-sans text-left font-semibold">
                {report.ReportName.toUpperCase()}
              </span>
            </div>
          ))}
        </div>
      </PickerButton>
    </div>
  )
}

export default MeasureDetailsReportPicker
