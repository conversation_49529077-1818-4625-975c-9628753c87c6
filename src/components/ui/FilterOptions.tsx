import { Button } from './button'

interface FilterOptionsProps {
  onSelectOption: (option: string) => void
  onClose: () => void
  selectedOption: string;
}

const filterOptions = [
  'Is equal to',
  'Is not equal to',
  'Starts with',
  'Contains',
  'Does not contain',
  'Ends with',
  'Is null',
  'Is not null',
  'Is empty',
  'Is not empty',
  'Has no value',
]

const FilterOptions: React.FC<FilterOptionsProps> = ({
  onSelectOption,
  onClose,
  selectedOption,
}) => {

  const handleSelect = (option: string) => {
    onSelectOption(option);
    onClose();
  };

  return (
    <div className="p-2">
      {filterOptions.map((option) => (
        <Button
          key={option}
          variant="ghost"
          className={`w-full justify-start font-bold ${option === selectedOption ? 'text-blue-500' : ''}`}
          onClick={() => handleSelect(option)}
        >
          {option}
        </Button>
      ))}
    </div>
  )
}

export default FilterOptions
