import React, { useState, useRef } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import * as XLSX from 'xlsx'
import chevronDown from '../../../public/images/chevronDown.svg'
import download from '../../../public/images/download.svg'

// Define a generic type for the data row that allows string indexing
type DataRecord = {
  [key: string]: string | number | boolean | null | undefined
}

type ExportButtonProps = {
  headers: string[]
  resultSet: DataRecord[] // Keeping the prop name for backward compatibility
  text?: string
  className?: string
  enabled?: boolean
  filePrefix?: string
}

const ExportButton = ({
  headers,
  resultSet: dataset, // Rename internally for clarity
  text = 'Export',
  className,
  enabled = true,
  filePrefix,
}: ExportButtonProps) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Function to convert data to CSV format
  const convertToCSV = (headers: string[], data: DataRecord[]) => {
    // Create header row
    const headerRow = headers.join(',')

    // Create data rows
    const dataRows = data
      .map((row) => {
        return headers
          .map((header) => {
            // Handle values that might contain commas or quotes
            const value = row[header] !== undefined ? row[header] : ''
            const valueStr = String(value)

            // Escape quotes and wrap in quotes if needed
            if (
              valueStr.includes(',') ||
              valueStr.includes('"') ||
              valueStr.includes('\n')
            ) {
              return `"${valueStr.replace(/"/g, '""')}"`
            }
            return valueStr
          })
          .join(',')
      })
      .join('\n')

    // Combine header and data
    return `${headerRow}\n${dataRows}`
  }

  // Function to handle CSV export
  const handleCSVExport = () => {
    const csvContent = convertToCSV(headers, dataset)

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')

    // Set up download link
    link.setAttribute('href', url)
    link.setAttribute(
      'download',
      `${filePrefix || 'export'}_${new Date().toISOString().slice(0, 10)}.csv`
    )
    link.style.visibility = 'hidden'

    // Add to document, trigger download, and clean up
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // Close dropdown
    setIsExpanded(false)
  }

  // Function to handle Excel export using the xlsx library with formatting
  const handleExcelExport = () => {
    // Convert data to worksheet format
    const worksheet = XLSX.utils.json_to_sheet(
      dataset.map((row) => {
        // Create a new object with only the headers we want
        const newRow: Record<string, any> = {}
        headers.forEach((header) => {
          newRow[header] = row[header] !== undefined ? row[header] : ''
        })
        return newRow
      })
    )

    // Set column widths based on content
    const columnWidths = [
      { wch: 40 }, // ENTITY (ORGANIZATION)
      { wch: 30 }, // ORGANIZATION
      { wch: 50 }, // LONG MEASURE NAME
      { wch: 15 }, // PERFORMANCE
      { wch: 12 }, // NUMERATOR
      { wch: 25 }, // DENOMINATOR
    ]
    worksheet['!cols'] = columnWidths

    // Apply styles to the worksheet
    // First, get the range of the worksheet
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')

    // Create styles for header row
    const headerStyle = {
      fill: { fgColor: { rgb: 'D9D9D9' } }, // Light gray background
      font: { bold: true, sz: 11, color: { rgb: '000000' } }, // Bold black text
      alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } },
      },
    }

    // Create styles for data rows (alternating colors)
    const evenRowStyle = {
      fill: { fgColor: { rgb: 'F2F2F2' } }, // Very light gray
      font: { sz: 11, color: { rgb: '000000' } },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } },
      },
    }

    const oddRowStyle = {
      fill: { fgColor: { rgb: 'FFFFFF' } }, // White
      font: { sz: 11, color: { rgb: '000000' } },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } },
      },
    }

    // Apply styles to cells
    if (!worksheet['!rows']) worksheet['!rows'] = []

    // Initialize cell styles if not present
    if (!worksheet['!cells']) worksheet['!cells'] = {}

    // Apply header style to the first row
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: C })
      if (!worksheet[cellRef]) continue

      // Apply header style
      worksheet[cellRef].s = headerStyle
    }

    // Apply alternating row styles to data rows
    for (let R = 1; R <= range.e.r; ++R) {
      const rowStyle = R % 2 === 1 ? oddRowStyle : evenRowStyle

      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellRef = XLSX.utils.encode_cell({ r: R, c: C })
        if (!worksheet[cellRef]) continue

        // Apply row style with conditional right alignment for numeric columns (NUMERATOR and DENOMINATOR)
        worksheet[cellRef].s =
          C === 4 || C === 5
            ? { ...rowStyle, alignment: { horizontal: 'right' } }
            : rowStyle

        // Format percentage cells (PERFORMANCE column, typically the 4th column)
        if (
          C === 3 &&
          typeof worksheet[cellRef].v === 'string' &&
          worksheet[cellRef].v.includes('%')
        ) {
          // Extract numeric value from percentage string
          const numValue =
            parseFloat(worksheet[cellRef].v.replace('%', '')) / 100
          worksheet[cellRef].v = numValue
          worksheet[cellRef].t = 'n' // Set cell type to number
          worksheet[cellRef].z = '0.00%' // Apply percentage format
        }
      }
    }

    // Create a workbook with the worksheet
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

    // Generate Excel file as an array buffer
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
      cellStyles: true, // Enable cell styles
    })

    // Convert to Blob
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })

    // Create download link
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute(
      'download',
      `${filePrefix || 'export'}_${new Date().toISOString().slice(0, 10)}.xlsx`
    )
    link.style.visibility = 'hidden'

    // Add to document, trigger download, and clean up
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // Close dropdown
    setIsExpanded(false)
  }

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsExpanded(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div
      className={cn('ml-7 rounded-xl relative', className)}
      ref={dropdownRef}
    >
      <button
        onClick={() => enabled && setIsExpanded(!isExpanded)}
        disabled={!enabled}
        className={`rounded-[5px] flex items-center space-x-2 px-[15px] py-2 font-semibold focus:outline-none focus:ring-0 
                border bg-ui-pale-blue text-ui-dark-gray border-ui-pale-blue 
                ${!isExpanded && 'hover:bg-white hover:border-ui-dark-gray hover:rounded-[5px]'}`}
      >
        <Image
          src={download}
          alt="download"
          height={14}
          width={14}
          className={cn(!enabled && 'opacity-50')}
        />
        <span
          className={`font-open-sans font-weight-600 text-[13px] ${enabled ? 'text-ui-dark-gray' : 'text-gray-400'}`}
        >
          {text}
        </span>
        <Image
          src={chevronDown}
          alt="chevronDown"
          height={14}
          width={14}
          className={cn(isExpanded && 'rotate-180', !enabled && 'opacity-50')}
        />
      </button>

      {isExpanded && (
        <div className="absolute p-[15px] top-full right-0 z-20 bg-ui-pale-blue shadow-lg rounded w-[285px] font-sans mt-1">
          <div className="flex flex-col gap-[15px]">
            <button
              onClick={handleCSVExport}
              className="w-full text-[#1B4A70] font-semibold border-ui-dark-gray border-[0.5px] rounded p-2 flex justify-between items-center text-start text-[12px] outline-none hover:bg-white"
              role="menuitem"
            >
              Export as CSV
            </button>
            <button
              onClick={handleExcelExport}
              className="w-full text-[#1B4A70] font-semibold border-ui-dark-gray border-[0.5px] rounded p-2 flex justify-between items-center text-start text-[12px] outline-none hover:bg-white"
              role="menuitem"
            >
              Export as Excel
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default ExportButton
