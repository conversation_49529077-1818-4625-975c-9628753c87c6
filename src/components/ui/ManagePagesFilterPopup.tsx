import { useState } from 'react'
import { Input } from './input'
import { Button } from './button'
import { cn } from '@/lib/utils'

interface ManagePagesFilterPopupProps {
  onFilterChange: (filterValue: string) => void
  onClose: () => void
}

const filterOptions = [
  'Ends with',
  'Is null',
  'Is not null',
  'Is empty',
  'Is not empty',
  'Has no value',
]

const ManagePagesFilterPopup: React.FC<ManagePagesFilterPopupProps> = ({
  onFilterChange,
  onClose,
}) => {
  const [filterText, setFilterText] = useState('')
  const [selectedOption, setSelectedOption] = useState('')

  const handleApplyFilter = () => {
    onFilterChange(filterText)
    onClose()
  }

  return (
    <div className="p-4">
      <Input
        type="text"
        placeholder="Filter..."
        value={filterText}
        onChange={(e) => setFilterText(e.target.value)}
        className="mb-2 w-full"
      />
      <select
        value={selectedOption}
        onChange={(e) => setSelectedOption(e.target.value)}
        className="mb-2 w-full rounded border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
      >
        <option value="">Select an option</option>
        {filterOptions.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>
      <div className="flex justify-end">
        <Button variant="ghost" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={handleApplyFilter} className="ml-2">
          Apply
        </Button>
      </div>
    </div>
  )
}

export default ManagePagesFilterPopup
