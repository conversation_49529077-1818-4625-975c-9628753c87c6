'use client'

import ActionsMenu from '@/components/ui/ActionsMenu'
import DatePickerContainer from '@/components/ui/DatePickerContainer'
import { useDateStore } from '@/stores/dates'
import React, { useEffect } from 'react'
import { HiOutlineSwitchHorizontal } from 'react-icons/hi'

import Filter from '../filter'
import EmptyIndicatorPicker from './EmptyIndicatorPicker'
import { useFilterStore } from '@/stores/filter'
import { useUIStore } from '@/stores/ui'
import MeasureTypePicker from './MeasureTypePicker'
import { Button } from './button'

type Props = {
  showMeasureTypeToggle?: boolean
}

const FilterAndActionBar = ({ showMeasureTypeToggle }: Props) => {
  const { applyPresetRange, mode, selectedRange } = useDateStore()
  const { clearAll, applyFilters } = useFilterStore()
  const { scorecardViewType, setScorecardViewType } = useUIStore()

  useEffect(() => {
    if (mode === 'Quarterly') {
      if (!selectedRange?.[0]) {
        applyPresetRange()
      }
    }
  }, [mode, selectedRange])

  const handleClearAll = () => {
    // Get the DatePickerContainer's handleClearAll function
    const datePickerClearAll = useDateStore.getState().clearAll

    // Call DatePickerContainer's clear function if available
    if (datePickerClearAll) {
      datePickerClearAll()
    }

    // Continue with the existing clear functionality
    clearAll()
    applyFilters()
  }

  function toggleMeasureView(
    event: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ): void {
    event.preventDefault()
    setScorecardViewType(
      scorecardViewType === 'Hospital' ? 'Measure' : 'Hospital'
    )
  }

  return (
    <div className="relative flex w-full justify-between items-center h-auto py-[14px] text-[#566582]">
      <div className="flex items-center">
        <Filter />
        <div className="ml-[40px]">
          <DatePickerContainer />
        </div>
        {/* <PeriodPicker /> */}
        <MeasureTypePicker />
      </div>

      <div className="flex items-center justify-end">
        <EmptyIndicatorPicker />

        {showMeasureTypeToggle && (
          <div className='mr-4'>
            <Button
              onClick={toggleMeasureView}
              className="text-[#566582] rounded-[5px] flex items-center space-x-2 px-[15px] py-2 ml-[15px] font-semibold bg-ui-pale-blue text-ui-dark-gray focus:outline-none focus:ring-0 border border-ui-pale-blue hover:bg-white hover:border-ui-dark-gray hover:rounded-[5px] "
            >
              <span className="font-['Open_Sans'] font-weight-600 text-[13px] text-[#566582]">
                <HiOutlineSwitchHorizontal className="inline-flex text-[#566582]" />{' '}
                {scorecardViewType === 'Hospital' ? 'MEASURE' : 'HOSPITAL'} VIEW
              </span>
            </Button>
          </div>
        )}

        <Button
          onClick={handleClearAll}
          className="text-[#566582] rounded-[5px] flex items-center space-x-2 px-[15px] py-3 mr-4 font-semibold bg-ui-pale-blue hover:bg-ui-pale-blue text-ui-dark-gray focus:outline-none focus:ring-0 border border-ui-pale-blue hover:bg-white hover:border-ui-dark-gray"
        >
          <span className="font-open-sans font-weight-600 text-[13px] text-ui-dark-gray">
            CLEAR ALL
          </span>
        </Button>

        <ActionsMenu />
      </div>
    </div>
  )
}

export default FilterAndActionBar
