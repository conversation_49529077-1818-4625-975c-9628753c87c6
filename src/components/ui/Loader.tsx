'use client'

import { cn } from '@/lib/utils'
import React, {useEffect, useState} from 'react'

// Only import and register the animation at the module level if running in the browser
// This ensures the animation is registered as soon as this component is imported
// However, it can cause hydration warnings.  So we add "suppressHydrationWarning" to the
// l-cardio element
if (typeof process !== 'undefined' && process.browser) {
    import('ldrs').then(ldrs => {
        ldrs.cardio.register()
    }).catch(err => {
        console.log('Error loading ldrs library:', err)
    })
}

const Loader = ({ className, text, delay = 100 }: { className?: string; text?: string, delay?: number }) => {
    const [show, setShow] = useState(false)

    useEffect(() => {
        const timer = setTimeout(() => {
            setShow(true)
        }, delay)

        return () => clearTimeout(timer)
    }, [delay])

    return (
        show
            ? <div className={cn(`flex flex-col justify-center items-center p-4`, className)} >
                {/*@ts-ignore*/}
                <l-cardio size="40" stroke="5" speed="0.9" color="#2D7CBA" suppressHydrationWarning={true}></l-cardio>
                <h2 className="text-[1.3rem] p-4">{text ?? 'Loading...'}</h2>
            </div>
            : <></>
    )
}
export default Loader
