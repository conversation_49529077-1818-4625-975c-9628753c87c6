'use client'

import React, { ReactNode, useEffect, useState } from 'react'
import Image from 'next/image'

import monthlyIcon from '../../../public/images/monthly-icon.svg'
import quarterlyIcon from '../../../public/images/quarterly-icon.svg'
import yearlyIcon from '../../../public/images/yearly-icon.svg'
import barGraphIcon from '../../../public/images/yearly-icon.svg'
import stackedBarGraphIcon from '../../../public/images/monthly-icon.svg'
import columnGraphIcon from '../../../public/images/yearly-icon.svg'
import lineGraphIcon from '../../../public/images/line-graph-icon.svg'
import spcChartIcon from '../../../public/images/spc-chart-icon.svg'
import { useUIStore } from '@/stores/ui'
import { ScorecardView } from '@/enums/scorecardView'

export type MenuItem = {
  id: string | ScorecardView
  label: string
  icon: ReactNode
}

const tableMenuItems: MenuItem[] = [
  {
    id: ScorecardView.Monthly,
    label: 'MONTHLY',
    icon: <Image src={monthlyIcon} alt="check" width={18} height={18} />,
  },
  {
    id: ScorecardView.Quarterly,
    label: 'QUARTERLY',
    icon: <Image src={quarterlyIcon} alt="check" width={18} height={18} />,
  },
  {
    id: ScorecardView.Yearly,
    label: 'YEARLY',
    icon: <Image src={yearlyIcon} alt="check" width={18} height={18} />,
  },
]

const graphMenuItems: MenuItem[] = [
  {
    id: 'BAR_GRAPH',
    label: 'BAR GRAPH',
    icon: <Image src={barGraphIcon} alt="Bar Graph" width={18} height={18} />,
  },
  {
    id: 'STACKEDBAR_GRAPH',
    label: 'STACKED BAR GRAPH',
    icon: (
      <Image
        src={stackedBarGraphIcon}
        alt="Stacked Bar Graph"
        width={18}
        height={18}
      />
    ),
  },
  {
    id: 'COLUMN_GRAPH',
    label: 'COLUMN GRAPH',
    icon: (
      <Image src={columnGraphIcon} alt="Column Graph" width={18} height={18} />
    ),
  },
  {
    id: 'LINE_GRAPH',
    label: 'LINE GRAPH',
    icon: <Image src={lineGraphIcon} alt="Line Graph" width={18} height={18} />,
  },
  {
    id: 'SPC_GRAPH',
    label: 'SPC CHART',
    icon: <Image src={spcChartIcon} alt="SPC Chart" width={18} height={18} />,
  },
]

export const MeasureDetailsLeftMenu = () => {
  const {
    currentMeasureDetailTab,
    setAggregationType,
    aggregationType,
    graphType,
    setGraphType,
  } = useUIStore()

  const [selectedView, setSelectedView] = useState(
    currentMeasureDetailTab === 'TABLE' ? aggregationType : graphType
  )

  useEffect(() => {
    if (currentMeasureDetailTab === 'TABLE') {
      if (aggregationType === ScorecardView.Monthly) {
        setSelectedView(ScorecardView.Monthly)
      }
      if (aggregationType === ScorecardView.Yearly) {
        setSelectedView(ScorecardView.Yearly)
      }
      if (aggregationType === ScorecardView.Quarterly) {
        setSelectedView(ScorecardView.Quarterly)
      }
    }
  }, [selectedView, aggregationType])

  if (currentMeasureDetailTab === 'GRAPH') {
    return (
      <ul className="w-48 relative top-0 px-3">
        {graphMenuItems.map((item) => (
          <li
            key={item.id}
            onClick={() => {
              setGraphType(item.id)
            }}
            className={`w-full px-3 py-3 text-left flex items-center space-x-2 transition-colors font-semibold text-[14px] hover:cursor-pointer
              ${
                graphType === item.id
                  ? 'bg-ui-pale-blue text-[#074880] border-r-[#2970A7] border-r-[4px]'
                  : 'text-ui-dark-gray hover:bg-gray-50'
              }`}
          >
            <span className="text-right flex justify-end w-full font-open-sans text-[13px] font-semibold ]">
              <div className="mr-2">{item.icon}</div>
              <div className="nowrap">{item.label}</div>
            </span>
          </li>
        ))}
      </ul>
    )
  }

  if (currentMeasureDetailTab === 'TABLE') {
    return (
      <ul className="w-48 relative top-0 px-3">
        {tableMenuItems.map((item) => (
          <li
            key={item.id}
            onClick={() => {
              setSelectedView(item.id)
              setAggregationType(item.id as ScorecardView)
            }}
            className={`w-full px-3 py-3 text-left flex items-center space-x-2 transition-colors font-semibold text-[14px]
              ${
                selectedView === item.id
                  ? 'bg-ui-pale-blue text-[#074880] border-r-[#2970A7] border-r-[4px]'
                  : 'text-ui-dark-gray hover:bg-gray-50'
              }`}
          >
            <span className="text-right flex justify-end w-full font-open-sans text-[13px] font-semibold ]">
              <div className="mr-2">{item.icon}</div>
              <div className="nowrap">{item.label}</div>
            </span>
          </li>
        ))}
      </ul>
    )
  }
}
