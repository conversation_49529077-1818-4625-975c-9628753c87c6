import React, { useEffect, useRef, useState } from 'react'
import Image from 'next/image'
import { ChevronDown, ChevronUp } from 'lucide-react'
import checked from '../../../public/images/checked-radio.svg'
import empty from '../../../public/images/empty-radio.svg'
import { useDateStore } from '@/stores/dates'
import { ScorecardView } from '@/enums/scorecardView'
import { api } from '@/trpc/react'
import { useClickOutside } from '@/hooks/useClickOutside'

export const PeriodPicker = () => {
  const ref = useRef<HTMLDivElement>(null)
  const [isExpanded, setIsExpanded] = useState(false)

  useClickOutside(ref, () => setIsExpanded(false))

  const { selectedPeriod, setSelectedPeriod } = useDateStore()

  const apiUtils = api.useUtils()

  const handlePeriodChange = (period: ScorecardView) => {
    setSelectedPeriod(
      Object.entries(ScorecardView).find(
        (x) => x[1] === period
      )?.[0]! as keyof typeof ScorecardView
    )

    setIsExpanded(false)
  }

  return (
    <div ref={ref} className="relative w-[165px]">
      <div className="ml-[40px] rounded-xl">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="rounded-[5px] flex justify-between items-center px-4 py-2 font-semibold bg-ui-pale-blue text-ui-dark-gray w-full"
        >
          <span className="font-open-sans font-weight-600 text-[13px] text-[#13497C] w-full">
            {selectedPeriod.toUpperCase()}
          </span>
          {isExpanded ? (
            <ChevronUp className="w-5 h-5" strokeWidth={2} />
          ) : (
            <ChevronDown className="w-5 h-5" strokeWidth={2} />
          )}
        </button>
      </div>
      {isExpanded && (
        <div className="absolute w-full p-4 top-full z-20 bg-ui-pale-blue shadow-lg rounded">
          {Object.entries(ScorecardView).map(([key, value], idx) => (
            <div
              key={idx}
              className={`flex items-center justify-between p-2 border rounded mb-2  ${
                selectedPeriod === key ? 'bg-white' : ''
              }`}
              onClick={() => handlePeriodChange(value)}
            >
              <span className="text-[#1B4A70]-600 text-[12px] font-open-sans">
                {key}
              </span>
              <Image
                src={selectedPeriod === key ? checked : empty}
                alt="check"
                width={14}
                height={14}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default PeriodPicker
