import chevronDown from '../../../public/images/chevronDown.svg'
import React from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import { Popover, PopoverContent, PopoverTrigger } from './popover'

type Props = {
  text: string
  children?: React.ReactNode
}
const PickerButton = ({ text, children }: Props) => {
  return (
    <Popover>
      <PopoverTrigger asChild className="ml-[15px] group">
        <button
          className={`text-[#566582] rounded-[5px] flex items-center space-x-2 px-[15px] py-2 font-semibold bg-ui-pale-blue text-ui-dark-gray focus:outline-none focus:ring-0 border border-ui-pale-blue hover:bg-white hover:border-ui-dark-gray`}
        >
          <span className="font-open-sans font-weight-600 text-[13px] text-ui-dark-gray">
            {text}
          </span>

          <Image
            src={chevronDown}
            alt="chevronDown"
            height={14}
            width={14}
            className={cn(
              'transition-transform',
              'group-data-[state=open]:rotate-180'
            )}
          />
        </button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 w-[275px] bg-ui-pale-blue shadow-lg rounded border-none -mt-1"
        align="end"
      >
        {children}
      </PopoverContent>
    </Popover>
  )
}

export default PickerButton
