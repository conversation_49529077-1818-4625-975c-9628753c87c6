'use client'

import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { useEffect, useState } from 'react'
import { Plus, Trash2 } from 'lucide-react'

interface FilterCondition {
  operator: string
  value: string
}

interface FilterConjunction {
  type: 'and' | 'or'
}

interface NewMeasureDetailsPatientFilterPopupProps {
  onFilterChange: (newFilterValues: {
    conditions: Array<FilterCondition>
    conjunctions: Array<FilterConjunction['type']>
  }) => void
  initialValues?: {
    conditions: Array<FilterCondition>
    conjunctions: Array<FilterConjunction['type']>
  }
}

export default function NewMeasureDetailsPatientFilterPopup({
  onFilterChange,
  initialValues,
}: NewMeasureDetailsPatientFilterPopupProps) {
  const [conditions, setConditions] = useState<FilterCondition[]>([
    { operator: 'eq', value: '' }
  ])
  const [conjunctions, setConjunctions] = useState<FilterConjunction['type'][]>(['and'])

  // Initialize with initial values if provided
  useEffect(() => {
    if (initialValues) {
      if (initialValues.conditions && initialValues.conditions.length > 0) {
        setConditions(initialValues.conditions);
      }
      if (initialValues.conjunctions && initialValues.conjunctions.length > 0) {
        setConjunctions(initialValues.conjunctions);
      }
    }
  }, [initialValues]);

  const operators = [
    { value: 'eq', label: 'equal' },
    { value: 'neq', label: 'are not equal' },
    // { value: 'startswith', label: 'starts with' },
    { value: 'contains', label: 'contains' },
    // { value: 'doesnotcontain', label: 'does not contain' },
    // { value: 'endswith', label: 'ends with' },
    // { value: 'isnull', label: 'is null' },
    // { value: 'isnotnull', label: 'is not null' },
    // { value: 'isempty', label: 'is empty' },
    // { value: 'isnotempty', label: 'is not empty' },
    // { value: 'isnullorempty', label: 'has no value' },
    // { value: 'isnotnullorempty', label: 'has value' },
    { value: 'gt', label: 'are greater than' },
    { value: 'lt', label: 'are less than' },
  ]

  const handleOperatorChange = (index: number, value: string) => {
    const newConditions = [...conditions]
    newConditions[index].operator = value
    setConditions(newConditions)
  }

  const handleValueChange = (index: number, value: string) => {
    const newConditions = [...conditions]
    newConditions[index].value = value
    setConditions(newConditions)
  }

  const handleAddCondition = () => {
    if (conditions.length < 3) {
      setConditions([...conditions, { operator: 'eq', value: '' }])

      // Make sure we have enough conjunctions
      if (conditions.length >= conjunctions.length) {
        setConjunctions([...conjunctions, 'and'])
      }
    }
  }

  const handleRemoveCondition = (index: number) => {
    if (conditions.length <= 1) {
      // Don't remove the last condition, just reset it
      setConditions([{ operator: 'eq', value: '' }])
      setConjunctions(['and'])
      return
    }

    const newConditions = [...conditions]
    newConditions.splice(index, 1)
    setConditions(newConditions)

    // Also remove the conjunction if needed
    if (index > 0) {
      const newConjunctions = [...conjunctions]
      newConjunctions.splice(index - 1, 1)
      setConjunctions(newConjunctions)
    }
  }

  const handleConjunctionChange = (index: number, type: FilterConjunction['type']) => {
    const newConjunctions = [...conjunctions]
    newConjunctions[index] = type
    setConjunctions(newConjunctions)
  }

  const handleClear = () => {
    setConditions([{ operator: 'eq', value: '' }])
    setConjunctions(['and'])

    // Clear the filter
    onFilterChange({
      conditions: [],
      conjunctions: []
    })
  }

  const handleApply = () => {
    // Filter out conditions without values (except for operators that don't need values)
    const validConditions = conditions.filter(condition =>
      condition.value.trim() !== '' ||
      ['isnull', 'isnotnull', 'isempty', 'isnotempty', 'isnullorempty', 'isnotnullorempty'].includes(condition.operator)
    );

    if (validConditions.length > 0) {
      // Only keep the necessary conjunctions
      const neededConjunctions = conjunctions.slice(0, Math.max(0, validConditions.length - 1));

      // Apply the filter
      onFilterChange({
        conditions: validConditions,
        conjunctions: neededConjunctions
      });
    } else {
      // If no valid conditions, clear the filter
      onFilterChange({
        conditions: [],
        conjunctions: []
      });
    }
  }

  // Special handling for operators that don't need a value
  const doesOperatorNeedValue = (operator: string) => {
    return !['isnull', 'isnotnull', 'isempty', 'isnotempty', 'isnullorempty', 'isnotnullorempty'].includes(operator);
  }

  return (
    <div className="w-[330px] p-[15px] bg-[#F8FAFC] border border-[rgb(226,232,240)] rounded-sm shadow-sm font-open-sans text-xs">
      <div className="border border-[#566582] rounded-[2px] bg-white p-5">
        <div className="text-[#1B4A70] font-open-sans font-semibold text-[12px] mb-5">Show Items That:</div>

        {conditions.map((condition, index) => (
          <div key={index} className="mb-2">
            <div className="mb-2">
              <Select value={condition.operator} onValueChange={(value) => handleOperatorChange(index, value)}>
                <SelectTrigger className="w-56 h-[22px] rounded-none border-[#97A4BA] focus:outline-none focus:ring-0 focus:border-[#97A4BA] bg-white text-[#000000]">
                  <SelectValue placeholder="Select operator" />

                </SelectTrigger>
                <SelectContent className="bg-white z-[201]">
                  {operators.map((op) => (
                    <SelectItem key={op.value} value={op.value} className="font-semibold">
                      {op.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="mb-2">
              <div className="flex items-center ">
                <Input
                  value={condition.value}
                  onChange={(e) => handleValueChange(index, e.target.value)}
                  className="w-56 h-[22px] rounded-none border-[#97A4BA]  focus:outline-none focus:ring-0 focus:border-[#97A4BA]"
                  disabled={!doesOperatorNeedValue(condition.operator)}
                />
                <button
                  className="p-1  text-gray-400 hover:text-[#2D7CBA] flex items-center justify-center"
                  onClick={() => handleRemoveCondition(index)}
                  disabled={conditions.length <= 1}
                >

                  <Trash2 className={`h-5 w-5 text-gray-300 ${conditions.length <= 1 ? 'hover:text-gray-300' : 'hover:text-[#205782]'}`} />
                </button>
              </div>
            </div>

            {index < conditions.length - 1 && (
              <div className="flex w-56">
                <button
                  className={`flex-1 py-1 text-center border border-[#2D7CBA]  ${conjunctions[index] === 'and'
                    ? 'bg-[#EBEFFD] text-[#205782] font-bold text-[12px]'
                    : 'bg-white text-[#566582] text-[12px]'
                    } rounded-l-sm`}
                  onClick={() => handleConjunctionChange(index, 'and')}
                >
                  AND
                </button>
                <button
                  className={`flex-1 py-1 text-center border border-[#2D7CBA] ${conjunctions[index] === 'or'
                    ? 'bg-[#EBEFFD] text-[#205782] font-bold text-[12px]'
                    : 'bg-white text-[#566582] text-[12px]'
                    } rounded-r-sm`}
                  onClick={() => handleConjunctionChange(index, 'or')}
                >
                  OR
                </button>
              </div>
            )}
          </div>
        ))}
        <div className="border-t border-[#DDDCDF] "></div>
        {conditions.length < 3 && (
          <div className="justify-center ">
            <Button
              variant="ghost"
              className="flex items-center justify-center hover:bg-transparent p-0"
              onClick={handleAddCondition}
            >
              <div
                className="flex items-center font-open-sans font-semibold text-[10.59px] leading-[100%] tracking-normal text-center uppercase text-[#566582] hover:text-[#1B4A70]"
                style={{
                  width: '105.56px',
                  height: '17.93px',
                  gap: '8.15px'
                }}
              >
                <Plus className="h-4 w-4" /> ADD CONDITION
              </div>
            </Button>
          </div>
        )}

        <div className="flex justify-between items-center">
          <Button
            variant="link"
            className="text-[#1B4A70] font-normal text-sm underline p-0 hover:no-underline"
            onClick={handleClear}
          >
            Clear All
          </Button>
          <Button
            className="bg-[#566582] text-white w-[70px] h-[27px] rounded-[5px] text-[13px] font-open-sans font-semibold hover:bg-[#F5F7FF] hover:text-[#566582] hover:border hover:border-[#566582]"
            onClick={handleApply}
          >
            APPLY
          </Button>
        </div>
      </div>
    </div>
  )
}



















