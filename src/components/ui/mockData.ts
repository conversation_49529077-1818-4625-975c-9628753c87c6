export const patientData = [
  {
    patientName: '<PERSON>',
    age: 26,
    gender: 'F',
    result: 'Numerator',
    dischargeDate: '03/31/2024 23:59:00',
  },
  {
    patientName: '<PERSON>',
    age: 69,
    gender: 'F',
    result: 'Denominator (only)',
    dischargeDate: '03/31/2024 23:59:00',
  },
  {
    patientName: '<PERSON>',
    age: 88,
    gender: 'M',
    result: 'Numerator',
    dischargeDate: '03/30/2024 23:59:00',
  },
  {
    patientName: '<PERSON>',
    age: 5,
    gender: 'M',
    result: 'Numerator',
    dischargeDate: '03/30/2024 23:59:00',
  },
  {
    patientName: '<PERSON>',
    age: 54,
    gender: 'M',
    result: 'Numerator',
    dischargeDate: '03/29/2024 23:59:00',
  },
  {
    patientName: '<PERSON> MacHeath',
    age: 11,
    gender: 'F',
    result: 'Excluded (only)',
    dischargeDate: '03/28/2024 23:59:00',
  },
  {
    patientName: '<PERSON>',
    age: 19,
    gender: 'M',
    result: 'Excluded (only)',
    dischargeDate: '03/28/2024 23:59:00',
  },
  {
    patientName: '<PERSON>',
    age: 8,
    gender: '<PERSON>',
    result: 'Numerator',
    dischargeDate: '03/27/2024 23:59:00',
  },
  {
    patientName: 'Susie Kay',
    age: 43,
    gender: 'F',
    result: 'Numerator',
    dischargeDate: '03/27/2024 23:59:00',
  },
  {
    patientName: 'Sally Ardrey',
    age: 81,
    gender: 'F',
    result: 'Excluded (only)',
    dischargeDate: '03/26/2024 23:59:00',
  },
  {
    patientName: 'John Doe',
    age: 30,
    gender: 'M',
    result: 'Numerator',
    dischargeDate: '03/25/2024 23:59:00',
  },
  {
    patientName: 'Jane Smith',
    age: 45,
    gender: 'F',
    result: 'Denominator (only)',
    dischargeDate: '03/24/2024 23:59:00',
  },
  {
    patientName: 'David Lee',
    age: 22,
    gender: 'M',
    result: 'Excluded (only)',
    dischargeDate: '03/23/2024 23:59:00',
  },
  {
    patientName: 'Sarah Jones',
    age: 50,
    gender: 'F',
    result: 'Numerator',
    dischargeDate: '03/22/2024 23:59:00',
  },
  {
    patientName: 'Michael Brown',
    age: 35,
    gender: 'M',
    result: 'Numerator',
    dischargeDate: '03/21/2024 23:59:00',
  },
  {
    patientName: 'Emily Davis',
    age: 28,
    gender: 'F',
    result: 'Denominator (only)',
    dischargeDate: '03/20/2024 23:59:00',
  },
  {
    patientName: 'Christopher Wilson',
    age: 60,
    gender: 'M',
    result: 'Excluded (only)',
    dischargeDate: '03/19/2024 23:59:00',
  },
  {
    patientName: 'Jessica Garcia',
    age: 40,
    gender: 'F',
    result: 'Numerator',
    dischargeDate: '03/18/2024 23:59:00',
  },
  {
    patientName: 'Matthew Rodriguez',
    age: 32,
    gender: 'M',
    result: 'Numerator',
    dischargeDate: '03/17/2024 23:59:00',
  },
  {
    patientName: 'Ashley Martinez',
    age: 25,
    gender: 'F',
    result: 'Denominator (only)',
    dischargeDate: '03/16/2024 23:59:00',
  },
]

for (let i = 0; i < 1000; i++) {
  const randomAge = Math.floor(Math.random() * (80 - 18 + 1)) + 18
  const randomGender = Math.random() < 0.5 ? 'M' : 'F'
  const randomResult = ['Numerator', 'Denominator (only)', 'Excluded (only)'][
    Math.floor(Math.random() * 3)
  ]
  const randomDate = new Date(
    2024,
    2,
    Math.floor(Math.random() * 31) + 1
  ).toLocaleDateString('en-US')

  patientData.push({
    patientName: `Patient ${i + 21}`,
    age: randomAge,
    gender: randomGender,
    result: randomResult!,
    dischargeDate: `${randomDate} 23:59:00`, // Ensure randomDate is always a string
  })
}
