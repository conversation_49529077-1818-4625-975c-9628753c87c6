'use client'

import React, { useRef, useState } from 'react'
import { useClickOutside } from '@/hooks/useClickOutside'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import chevronDown from '../../../public/images/chevronDown.svg'

interface ActionMenuPickerButtonProps {
  text: string
  children: React.ReactNode
  className?: string
}

const ActionMenuPickerButton: React.FC<ActionMenuPickerButtonProps> = ({
  text,
  children,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Handle click outside to close dropdown
  useClickOutside(dropdownRef, (event) => {
    // Check if click is on scrollbar by comparing clientX with window width
    const windowWidth = document.documentElement.clientWidth
    const clickX = event.clientX
    const isClickOnScrollbar = clickX >= windowWidth
    
    // Only close if not clicking on scrollbar and not clicking on the button
    if (!isClickOnScrollbar && !buttonRef.current?.contains(event.target as Node)) {
      setIsOpen(false)
    }
  })

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'rounded-[5px] flex items-center space-x-2 px-[15px] py-2 font-semibold focus:outline-none focus:ring-0 border bg-ui-pale-blue text-ui-dark-gray border-ui-pale-blue',
          !isOpen && 'hover:bg-white hover:border-ui-dark-gray hover:rounded-[5px]',
          className
        )}
      >
        <span className="font-open-sans font-weight-600 text-[13px] text-ui-dark-gray">
          {text}
        </span>
        <Image
          src={chevronDown}
          alt="chevronDown"
          height={14}
          width={14}
          className={cn(isOpen && 'rotate-180')}
        />
      </button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 top-full right-0 mt-1 bg-[#F5F7FF] rounded-lg shadow-lg min-w-[250px] actions-menu-dropdown"
        >
          {children}
        </div>
      )}
    </div>
  )
}

export default ActionMenuPickerButton

