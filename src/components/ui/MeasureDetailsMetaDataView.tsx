'use client'

import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { api } from '@/trpc/react'
import { useUIStore } from '@/stores/ui'
import Loader from './Loader'
import { cn } from '@/lib/utils'
import { useEffect, useState } from 'react'
import { Measure } from '@/types/measure'

// Default values shown

const columnHelper = createColumnHelper<Record<string, any>>()

const columns = [
  columnHelper.accessor('versionNumber', {
    header: 'Version Number',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('startPeriod', {
    header: 'Start Date',
    cell: (info) => info.getValue().split(' ')[0],
  }),
  columnHelper.accessor('endPeriod', {
    header: 'End Date',
    cell: (info) => info.getValue().split(' ')[0],
  }),
  columnHelper.accessor('specificationURL_HTML', {
    header: 'Specifications',
    cell: (info) => {
      const htmlUrl = info.getValue()
      const pdfUrl = info.row.original.specificationURL_PDF

      if (!htmlUrl && !pdfUrl) {
        return '-'
      } else if (htmlUrl) {
        return (
          <a
            href={`/api/docs/get-specification-doc?path=${htmlUrl}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-[#13497C] hover:underline"
          >
            View Specification
          </a>
        )
      } else {
        return (
          <a
            href={`/api/docs/get-specification-doc?path=${pdfUrl}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-[#13497C] hover:underline"
          >
            View Specification
          </a>
        )
      }
    },
  }),
]

type Props = {
  className?: string
}
export const MeasureDetailsMetaDataView = ({ className }: Props) => {
  const { currentMeasureId } = useUIStore()
  const [filteredData, setFilteredData] = useState<Measure[]>([])

  const measureMetaData = api.measures.getMeasureMetaData.useQuery({
    measureId: currentMeasureId!,
  })

  const currentYear = new Date().getFullYear()

  //this is the fix, in react any time state is touched it causes a render
  //this fix ensures that once we get the data, we only mutate/touch it one time. One first load

  useEffect(() => {
    const filteredData = (measureMetaData.data || [])
      .filter((item) => {
        const startPeriodYear = new Date(item.startPeriod).getFullYear()
        return startPeriodYear <= currentYear
      })
      .sort((a, b) => {
        const dateA = new Date(a.startPeriod)
        const dateB = new Date(b.startPeriod)
        return dateB.getTime() - dateA.getTime()
      })

    setFilteredData(filteredData)
  }, [measureMetaData.isPending])

  const table = useReactTable({
    data: filteredData,
    columns: columns as any,
    getCoreRowModel: getCoreRowModel(),
  })

  // if(measureMetaData.data?.length === 0) {
  //   return (
  //     <div className="mx-auto text-center max-w-[750px]">
  //       <h4 className="text-[1.2rem] font-[300] font-open-sans">
  //         No specification documents found
  //       </h4>
  //     </div>
  //   )
  // }

  return (
    <div className={cn('mx-auto text-center max-w-[750px]', className)}>
      {measureMetaData.isPending ? (
        <Loader />
      ) : (
        <>
          <h4 className="text-[1.2rem] font-[300] font-open-sans">
            Specification Documents
          </h4>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <th
                        key={header.id}
                        colSpan={header.colSpan}
                        className="bg-ui-dark-gray text-ghostwhite text-center font-open-sans text-[14px] font-[600] border-b border-l px-[18px] py-[11px]"
                      >
                        {header.isPlaceholder ? null : (
                          <div className="flex justify-center items-center">
                            <span className="flex-grow text-center">
                              {flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                            </span>
                          </div>
                        )}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody>
                {table.getRowModel().rows.map((row) => (
                  <tr
                    key={row.id}
                    className={`${
                      row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                    }`}
                  >
                    {row.getVisibleCells().map((cell, index, allCells) => (
                      <td
                        key={cell.id}
                        className={`p-2 text-center border-[#DDDCDF] border-b border-l align-middle ${
                          index === allCells.length - 1 ? 'border-r' : ''
                        }`}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  )
}
