import React, { useMemo } from 'react'
import { useMeasureResultsStore } from '@/stores/measuresResultsStore'
import Exporter, { ExportDataset } from './Exporter'

const MeasurePerformanceExporter: React.FC = () => {
    const { aggregatedMeasureDetails } = useMeasureResultsStore()

    // Transform data into ExportDataset format
    const exportDataset: ExportDataset | undefined = useMemo(() => {
        // Check if we have measure performance data
        if (aggregatedMeasureDetails && aggregatedMeasureDetails.length > 0) {
            // Use measure performance data
            const headers = ['date', 'performance', 'numerator', 'denominator']

            const dataset = aggregatedMeasureDetails.map(item => {
                return {
                    date: item.fullDate,
                    performance: item.rate,
                    numerator: item.numerator,
                    denominator: item.denominator
                }
            })

            return { headers, dataset }
        }
        // Otherwise, check if we have measure results data

        // No data available
        return undefined
    }, [aggregatedMeasureDetails])

    return <Exporter exportDataset={exportDataset} buttonText="Export Data" filePrefix="measure-performance" />
}

export default MeasurePerformanceExporter
