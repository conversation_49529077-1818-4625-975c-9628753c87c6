'use client'

import { useState, useEffect, useRef } from 'react'
import { ChevronLeft, ChevronRight, ChevronDown } from 'lucide-react'
import {
  format,
  addDays,
  subDays,
  startOfMonth,
  endOfMonth,
  isSameMonth,
  isSameDay,
  addMonths,
  isAfter,
  isBefore,
} from 'date-fns'

interface DateRangeFilterProps {
  startDate?: Date | null
  endDate?: Date | null
  onDateRangeChange?: (startDate: Date | null, endDate: Date | null) => void
}

export default function DateRangeFilter({
  startDate: initialStartDate = null,
  endDate: initialEndDate = null,
  onDateRangeChange,
}: DateRangeFilterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [startDate, setStartDate] = useState<Date | null>(initialStartDate)
  const [endDate, setEndDate] = useState<Date | null>(initialEndDate)
  const [currentLeftMonth, setCurrentLeftMonth] = useState(new Date())
  const [currentRightMonth, setCurrentRightMonth] = useState(
    addMonths(new Date(), 1)
  )
  const [showCustomRange, setShowCustomRange] = useState(true)
  const [activeButton, setActiveButton] = useState('custom')
  const [isFirstLoad, setIsFirstLoad] = useState(true)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Check if it's first load when component mounts
  useEffect(() => {
    const hasVisited = sessionStorage.getItem('dateRangeFilterVisited')
    if (!hasVisited) {
      setIsFirstLoad(true)
      sessionStorage.setItem('dateRangeFilterVisited', 'true')
    } else {
      setIsFirstLoad(false)
    }
  }, [])

  // Now you can use isFirstLoad in your button styling
  const getButtonClassName = (buttonType: string) => `
    px-3 py-1.5 
    rounded-md 
    font-open-sans 
    font-semibold 
    text-[12px] 
    text-ui-dark-gray 
    whitespace-nowrap 
    ${isFirstLoad && buttonType === 'custom' ? 'bg-white border-2 border-[#566582]' : ''}
    ${!isFirstLoad && activeButton === buttonType ? 'bg-white border-2 border-[#566582]' : 'border border-transparent'}
  `

  // Update internal state if props change
  useEffect(() => {
    if (initialStartDate !== startDate) {
      setStartDate(initialStartDate)
    }
    if (initialEndDate !== endDate) {
      setEndDate(initialEndDate)
    }
  }, [initialStartDate, initialEndDate])

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Update right month when left month changes
  useEffect(() => {
    setCurrentRightMonth(addMonths(currentLeftMonth, 1))
  }, [currentLeftMonth])

  // Notify parent component when date range changes
  useEffect(() => {
    if (
      onDateRangeChange &&
      (startDate !== initialStartDate || endDate !== initialEndDate)
    ) {
      onDateRangeChange(startDate, endDate)
    }
  }, [startDate, endDate, onDateRangeChange, initialStartDate, initialEndDate])

  const handleDateClick = (date: Date) => {
    if (!startDate || (startDate && endDate) || isBefore(date, startDate)) {
      setStartDate(date)
      setEndDate(null)
    } else {
      setEndDate(date)
      // Close popup when both dates are selected
      setIsOpen(false)
    }
  }

  const handlePreviousMonth = () => {
    setCurrentLeftMonth((prevMonth) => addMonths(prevMonth, -1))
  }

  const handleNextMonth = () => {
    setCurrentLeftMonth((prevMonth) => addMonths(prevMonth, 1))
  }

  const handleLast30Days = () => {
    const end = new Date()
    const start = subDays(end, 29) // 30 days including today
    setStartDate(start)
    setEndDate(end)
    setShowCustomRange(false)
    setActiveButton('last30')
    setIsOpen(false) // Close popup after selection
  }

  const handleLast60Days = () => {
    const end = new Date()
    const start = subDays(end, 59) // 60 days including today
    setStartDate(start)
    setEndDate(end)
    setShowCustomRange(false)
    setActiveButton('last60')
    setIsOpen(false) // Close popup after selection
  }

  const handleLast90Days = () => {
    const end = new Date()
    const start = subDays(end, 89) // 90 days including today
    setStartDate(start)
    setEndDate(end)
    setShowCustomRange(false)
    setActiveButton('last90')
    setIsOpen(false) // Close popup after selection
  }

  const handleCustomRange = () => {
    setShowCustomRange(true)
    setActiveButton('custom')
    if (!startDate) {
      setStartDate(new Date())
    }
  }

  const renderCalendarDays = (month: Date) => {
    const monthStart = startOfMonth(month)
    const monthEnd = endOfMonth(month)

    // Get all days in month
    const daysInMonth = []
    let currentDay = monthStart

    // Add days from previous month to start on Sunday
    const startDay = monthStart.getDay()
    for (let i = 0; i < startDay; i++) {
      daysInMonth.push(null)
    }

    // Add days of current month
    while (currentDay <= monthEnd) {
      daysInMonth.push(new Date(currentDay))
      currentDay = addDays(currentDay, 1)
    }

    return (
      <div className="calendar-month">
        <div className="grid grid-cols-7 gap-1 mb-1">
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
            <div
              key={index}
              className="text-center text-xs font-medium text-gray-500"
            >
              {day}
            </div>
          ))}
        </div>
        <div className="grid grid-cols-7 gap-1">
          {daysInMonth.map((day, index) => {
            if (!day) {
              return <div key={`empty-${index}`} className="h-8 w-8" />
            }

            const isSelected =
              startDate && endDate
                ? isSameDay(day, startDate) ||
                  isSameDay(day, endDate) ||
                  (isAfter(day, startDate) && isBefore(day, endDate))
                : startDate
                  ? isSameDay(day, startDate)
                  : false

            const isStartDate = startDate ? isSameDay(day, startDate) : false
            const isEndDate = endDate ? isSameDay(day, endDate) : false

            // Determine if the day is in the current month
            const isCurrentMonth = isSameMonth(day, month)

            return (
              <button
                data-testid={day.toDateString()}
                key={day.toString()}
                onClick={() => handleDateClick(day)}
                className={`
                  h-8 w-8 flex items-center justify-center text-sm
                  ${isSelected ? 'bg-[#566582] text-white' : 'hover:bg-gray-100'}
                  ${!isCurrentMonth ? 'text-gray-300' : 'text-gray-700'}
                  ${isStartDate || isEndDate ? 'bg-[#566582] text-white' : ''}
                  ${isSelected && !isStartDate && !isEndDate ? 'bg-[#E6EBF8] text-[#566582]' : ''}
                `}
              >
                {format(day, 'd')}
              </button>
            )
          })}
        </div>
      </div>
    )
  }

  const formatDateRange = () => {
    if (startDate && endDate) {
      return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`
    }
    if (startDate) {
      return format(startDate, 'MMM d, yyyy')
    }
    return 'DATE RANGE'
  }

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center px-4 py-2 border bg-ui-pale-blue border-ui-pale-blue hover:bg-white hover:border-ui-dark-gray rounded-md  font-open-sans font-semibold text-[13px] text-ui-dark-gray focus:outline-none"
      >
        {startDate && endDate ? formatDateRange() : 'DATE RANGE'}{' '}
        <ChevronDown className="ml-2 h-3.5 w-3.5 text-ui-dark-gray" />
      </button>

      {isOpen && (
        <div className="absolute mt-1 z-50 bg-white border border-gray-200 rounded-md shadow-lg w-[500px]">
          <div className="p-4">
            <div className="flex justify-between mb-4">
              <button onClick={handlePreviousMonth} className="text-gray-600">
                <ChevronLeft className="h-5 w-5" />
              </button>
              <div className="flex space-x-24">
                <div className="text-center font-bold text-gray-700">
                  {format(currentLeftMonth, 'MMMM yyyy')}
                </div>
                <div className="text-center font-bold text-gray-700">
                  {format(currentRightMonth, 'MMMM yyyy')}
                </div>
              </div>
              <button onClick={handleNextMonth} className="text-gray-600">
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>

            <div className="flex justify-between space-x-4">
              <div className="flex-1">
                {renderCalendarDays(currentLeftMonth)}
              </div>
              <div className="flex-1">
                {renderCalendarDays(currentRightMonth)}
              </div>
            </div>

            <div className="border-t mt-4 pt-4 flex space-x-4">
              <button
                onClick={handleCustomRange}
                className={getButtonClassName('custom')}
              >
                Custom Range
              </button>
              <button
                onClick={handleLast30Days}
                className={getButtonClassName('last30')}
              >
                Last 30 Days
              </button>
              <button
                onClick={handleLast60Days}
                className={getButtonClassName('last60')}
              >
                Last 60 Days
              </button>
              <button
                onClick={handleLast90Days}
                className={getButtonClassName('last90')}
              >
                Last 90 Days
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
