'use client'
'use client'
'use client'
import React, { useState } from 'react';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { Calendar } from 'lucide-react';

interface SingleDatePickerProps {
    onSelect: (date: Date | undefined) => void;
    selectedDay?: Date;
}

export const SingleDatePicker = ({ onSelect, selectedDay }: SingleDatePickerProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedDate, setSelectedDate] = useState<Date | undefined>(selectedDay);

    const formattedDate = selectedDate ? selectedDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    }).toUpperCase() : '';

    const handleDateSelect = (date: Date | undefined) => {
        setSelectedDate(date);
        onSelect(date);
        setIsOpen(false);
    };

    return (
        <div className="relative">
            <div className="flex items-center border border-[#1B4A70] rounded py-2 px-3 min-w-[200px] bg-white">
                <Calendar className="w-5 h-5 text-[#1B4A70] mr-2" />
                <input
                    type="text"
                    className="w-full outline-none text-[#1B4A70] font-semibold"
                    value={formattedDate}
                    readOnly
                    onClick={() => setIsOpen(!isOpen)}
                    placeholder="Select Date"
                />
            </div>
            {isOpen && (
                <div className="absolute top-full left-0 mt-1 bg-white border rounded-md shadow-lg z-50">
                    <DayPicker
                        mode="single"
                        selected={selectedDate}
                        onSelect={handleDateSelect}
                        className="p-2"
                        styles={{
                            head_cell: { color: '#1B4A70' },
                            caption: { color: '#1B4A70' },
                            table: { fontSize: '0.875rem' }
                        }}
                    />
                </div>
            )}
        </div>
    );
};

export default SingleDatePicker;
