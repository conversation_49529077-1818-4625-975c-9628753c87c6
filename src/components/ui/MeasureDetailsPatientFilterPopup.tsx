'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { useState } from 'react'

interface MeasureDetailsPatientFilterPopupProps {
  onFilterChange: (newFilterValues: {
    firstOperator: string
    secondOperator: string
    conjunction: string
    firstValue: string
    secondValue: string
  }) => void
}

export default function MeasureDetailsPatientFilterPopup({
  onFilterChange,
}: MeasureDetailsPatientFilterPopupProps) {
  const [firstOperator, setFirstOperator] = useState('eq')
  const [secondOperator, setSecondOperator] = useState('eq')
  const [conjunction, setConjunction] = useState('and')
  const [firstValue, setFirstValue] = useState('')
  const [secondValue, setSecondValue] = useState('')

  const handleClear = () => {
    setFirstOperator('eq')
    setSecondOperator('eq')
    setConjunction('and')
    setFirstValue('')
    setSecondValue('')

    // Trigger filter change with default values to reset the table
    onFilterChange({
      firstOperator: 'eq',
      secondOperator: 'eq',
      conjunction: 'and',
      firstValue: '',
      secondValue: '',
    })
  }

  const inputStyle = 'h-[calc(1.125em+16px)] bg-white rounded-none'

  const operators = [
    { value: 'eq', label: 'Is equal to' },
    { value: 'neq', label: 'Is not equal to' },
    { value: 'startswith', label: 'Starts with' },
    { value: 'contains', label: 'Contains' },
    { value: 'doesnotcontain', label: 'Does not contain' },
    { value: 'endswith', label: 'Ends with' },
    { value: 'isnull', label: 'Is null' },
    { value: 'isnotnull', label: 'Is not null' },
    { value: 'isempty', label: 'Is empty' },
    { value: 'isnotempty', label: 'Is not empty' },
    { value: 'isnullorempty', label: 'Has no value' },
    { value: 'isnotnullorempty', label: 'Has value' },
  ]

  const handleFilter = () => {
    onFilterChange({
      firstOperator,
      secondOperator,
      conjunction,
      firstValue,
      secondValue,
    })
  }

  return (
    <div className="w-[400px] p-4 bg-ui-pale-blue border border-[#E2E8F0] rounded-sm shadow-sm font-open-sans text-xs shadow-[0px_0px_5px_0px_#00000026]">
      <div className="text-sm mb-4">Show items with value that:</div>

      <div className="space-y-3">
        <div className="space-y-2">
          <Select value={firstOperator} onValueChange={setFirstOperator}>
            <SelectTrigger
              className={`w-full ${inputStyle}  focus:outline-none border-ui-dark-gray`}
            >
              <SelectValue placeholder="Select operator" />
            </SelectTrigger>
            <SelectContent autoFocus={false} className="bg-white  z-[201]">
              {operators.map((op) => (
                <SelectItem
                  className="font-semibold"
                  key={op.value}
                  value={op.value}
                >
                  {op.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Input
            value={firstValue}
            onChange={(e) => setFirstValue(e.target.value)}
            className={`w-full ${inputStyle} border-ui-dark-gray`}
          />
        </div>

        <Select value={conjunction} onValueChange={setConjunction}>
          <SelectTrigger
            className={`w-[100px] ${inputStyle} border-ui-dark-gray`}
          >
            <SelectValue placeholder="And/Or" />
          </SelectTrigger>
          <SelectContent className="bg-white z-[201]">
            <SelectItem value="and">And</SelectItem>
            <SelectItem value="or">Or</SelectItem>
          </SelectContent>
        </Select>

        <div className="space-y-2">
          <Select value={secondOperator} onValueChange={setSecondOperator}>
            <SelectTrigger
              className={`w-full ${inputStyle} focus:outline-none border-ui-dark-gray`}
            >
              <SelectValue placeholder="Select operator" />
            </SelectTrigger>
            <SelectContent autoFocus={false} className="bg-white z-[201]">
              {operators.map((op) => (
                <SelectItem
                  className="font-semibold"
                  key={op.value}
                  value={op.value}
                >
                  {op.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Input
            value={secondValue}
            onChange={(e) => setSecondValue(e.target.value)}
            className={`w-full ${inputStyle} border-ui-dark-gray`}
          />
        </div>

        <div className="flex items-center pt-2">
          <Button
            variant="link"
            className="text-[#1B4A70] text-xs h-auto ltr pe-8 underline font-normal"
            onClick={handleClear}
          >
            Clear All
          </Button>
          <Button
            className="bg-ui-dark-gray hover:bg-ui-dark-gray/90 w-32 text-white rounded"
            onClick={handleFilter}
          >
            Filter
          </Button>
        </div>
      </div>
    </div>
  )
}
