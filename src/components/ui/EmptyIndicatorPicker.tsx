import React from 'react'
import { useFilterStore } from '@/stores/filter'
import { Checkbox } from './checkbox'
import { CheckedState } from '@radix-ui/react-checkbox'

export const EmptyIndicatorPicker = () => {
  const { hideEmptyIndicators, setHideEmptyIndicators } = useFilterStore()

  const handleEmptyIndicatorChange = (checked: CheckedState) => {
    if (typeof checked === 'boolean') {
      setHideEmptyIndicators(checked as boolean)
    }
  }

  return (
    <div>
      <div className="ml-[15px] rounded-xl space-x-2 mr-6 flex items-center">
        <Checkbox
          id="terms"
          onCheckedChange={(checked) => handleEmptyIndicatorChange(checked)}
          checked={hideEmptyIndicators}
        />
        <label
          htmlFor="terms"
          className="text-ui-dark-gray font-semibold font-sans uppercase text-[13px] peer-disabled:cursor-not-allowed peer-disabled:opacity-70 hover:cursor-pointer"
        >
          Hide Empty Indicators
        </label>
      </div>
    </div>
  )
}

export default EmptyIndicatorPicker
