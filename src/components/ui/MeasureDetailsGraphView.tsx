'use client'

import { AgCharts } from 'ag-charts-react'
import type { AgCartesianChartOptions } from 'ag-charts-community'
import { cn } from '@/lib/utils'
import { useUIStore } from '@/stores/ui'
import { api } from '@/trpc/react'
import { TimePeriod } from '@/types/TimePeriod'
import { format } from 'date-fns'
import Loader from '@/components/ui/Loader'
import dynamic from 'next/dynamic'
import { useEffect, useMemo } from 'react'
import { calculateSPCLimits, checkSPCRules } from '@/lib/measureDetailsSPCCalculations'
import type { RawSPCData } from '@/types/measureDetailsSPCData'
import { useDateStore } from '@/stores/dates'
import type { ChartType } from '@/types/ChartTypes'
import { useMeasureResultsStore } from '@/stores/measuresResultsStore'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { useUserSessionStore } from "@/stores/userSession";
import { ScorecardView } from "@/enums/scorecardView";

dayjs.extend(utc)

// Dynamically import SPCChart to avoid SSR issues with Plotly
const SPCChart = dynamic(
  () => import('@/components/ui/measureDetailsSPCChart'),
  { ssr: false }
)

interface MeasureDetailsGraphViewProps {
  className?: string
}

// Maximum number of Y-axis labels to display
const MAX_NUMBER_Y_LABELS = 12

export const MeasureDetailsGraphView = ({
  className,
}: MeasureDetailsGraphViewProps) => {
  const {
    currentMeasureId,
    graphType,
    currentMeasureName,
    currentStartDate,
    currentEndDate,
    entityId,
    entityType,
  } = useUIStore()
  const { selectedPeriod } = useDateStore()
  const { setAggregateMeasureDetails } = useMeasureResultsStore()
  let { primaryMeasureType } = useUserSessionStore()

  const performanceRateTitle = currentMeasureName?.toLowerCase().includes('median')
      ? 'Performance Median'
      : 'Performance Rate'

  const timePeriod = (selectedPeriod as TimePeriod) || TimePeriod.Monthly

  const startDate = dayjs(currentStartDate)
    ? dayjs(currentStartDate).utc().toDate()
    : new Date()

  const endDate = dayjs(currentEndDate)
    ? dayjs(currentEndDate).utc().toDate()
    : new Date()

  const measureResultsDetailsQuery = api.measures.getMeasureResultDetails.useQuery(
    {
      measureId: currentMeasureId!,
      startDate: dayjs(currentStartDate)?.utc().toDate(),
      endDate: dayjs(currentEndDate)?.utc().toDate(),
      entityId: entityId!,
      entityDetailType: entityType!,
      aggregationType: timePeriod.charAt(0) as ScorecardView,
      measureType: primaryMeasureType,
    },
    {
      enabled: !!currentMeasureId,
    }
  )

  useEffect(() => {
    if (
      measureResultsDetailsQuery?.isSuccess &&
      measureResultsDetailsQuery.data?.length > 0
    ) {
      setAggregateMeasureDetails(measureResultsDetailsQuery.data)
    }
  }, [measureResultsDetailsQuery?.isSuccess, measureResultsDetailsQuery?.data])

  // Percentage formatter for axes
  const percentageFormatter = (params: { value: number }) => `${params.value}%`

  // Transform data for charts
  const getTransformedData = () => {
    if (!measureResultsDetailsQuery?.data) return []

    return measureResultsDetailsQuery.data.map((row) => {
      const performance = (typeof(row.rate) === 'string' && row.rate.search(/<br[\/]*>/) > -1)
          ? Number(row.rate.split(/<br[\/]*>/)[1]) * 100
          : Number(row.rate)
      return {
        unit: dayjs.utc(row.fullDate).format('MM/YYYY'),
        performance: performance,
        numerator: isNaN(+row.numerator) ? 0 : +row.numerator,
        inDenominatorOnly: isNaN(+row.inDenominatorOnly) ? 0 : +row.inDenominatorOnly,
      }
    })
  }

  // Function to limit X-axis labels to a maximum of MAX_NUMBER_Y_LABELS, evenly distributed
  const getLimitedXAxisLabels = () => {
    const data = getTransformedData()
    if (data.length <= MAX_NUMBER_Y_LABELS) return data.map(item => item.unit)

    // Calculate indices for evenly distributed labels
    const step = (data.length - 1) / (MAX_NUMBER_Y_LABELS - 1)
    const indices = Array.from({ length: MAX_NUMBER_Y_LABELS }, (_, i) => Math.round(i * step))

    // Return the evenly distributed labels
    return indices.map(index => data[index]?.unit || '')
  }

  // Transform data for SPC chart
  const spcData = useMemo(() => {
    if (!measureResultsDetailsQuery?.data) return null

    // Convert performance rates to raw SPC data format
    const rawSPCData: RawSPCData = {
      dates: measureResultsDetailsQuery.data.map((row) =>
        dayjs(row.fullDate).format('MM/YYYY')
      ),
      performance: measureResultsDetailsQuery.data.map(
        (row) => Number(row.rate) / 100
      ), // Convert to decimal for SPC calculations
      newPhases: measureResultsDetailsQuery.data.map(
        (_, i) => i === Math.floor(measureResultsDetailsQuery.data.length / 2)
      ), // Example: add a phase change in the middle
      excludedPoints: measureResultsDetailsQuery.data.map(() => false),
      measureScoring: 'Rate',
      chartType: 'p-chart',
      denominators: measureResultsDetailsQuery.data.map((row) => Number(row.denominator)),
    }

    // Calculate SPC limits and then apply rule checks
    const calculatedData = calculateSPCLimits(rawSPCData, timePeriod)
    return checkSPCRules(calculatedData)
  }, [measureResultsDetailsQuery?.data, currentMeasureId, timePeriod])

  // Common chart options
  const createChartOptions = (config: {
    chartType: ChartType
    data: any[]
    series: any[]
    axes: any[]
    background?: { fill: string }
  }): AgCartesianChartOptions => {
    return {
      data: config.data,
      background: config.background,
      title: {
        text: '',
        enabled: false,
      },
      subtitle: {
        text: '',
        enabled: false,
      },
      height: 500,
      series: config.series,
      axes: config.axes,
      legend: {
        position: 'bottom',
      },
    }
  }

  // Bar graph options
  const barOptions: AgCartesianChartOptions = createChartOptions({
    chartType: 'bar',
    data: getTransformedData(),
    series: [
      {
        type: 'bar',
        direction: 'horizontal',
        xKey: 'unit',
        yKey: 'performance',
        yName: currentMeasureName,
        fill: '#1e4976',
        strokeWidth: 0,
      },
    ],
    axes: [
      {
        type: 'category',
        position: 'left',
        title: {
          enabled: false,
        },
        label: {
          formatter: (params: { value: string }) => {
            const limitedLabels = getLimitedXAxisLabels()
            return limitedLabels.includes(params.value) ? params.value : ''
          },
        },
      },
      {
        type: 'number',
        position: 'bottom',
        title: {
          text: performanceRateTitle,
        },
        min: 0,
        max: 100,
        label: {
          formatter: percentageFormatter,
        },
      },
    ],
  })

  // Stacked bar graph options
  const stackedBarOptions: AgCartesianChartOptions = createChartOptions({
    chartType: 'stackedBar',
    data: getTransformedData(),
    series: [
      {
        type: 'bar',
        xKey: 'unit',
        yKey: 'numerator',
        yName: 'Numerator',
        stacked: true,
        fill: '#059669', // Green color
        strokeWidth: 0,
      },
      {
        type: 'bar',
        xKey: 'unit',
        yKey: 'inDenominatorOnly',
        yName: 'Denominator Only',
        stacked: true,
        fill: '#dc2626', // Red color
        strokeWidth: 0,
      },
      {
        type: 'line',
        xKey: 'unit',
        yKey: 'performance',
        yName: 'Performance',
        stroke: '#3b82f6', // Blue color
        strokeWidth: 2,
        marker: {
          enabled: true,
          fill: '#3b82f6',
        },
      },
    ],
    axes: [
      {
        type: 'category',
        position: 'bottom',
        title: {
          enabled: false,
        },
        label: {
          formatter: (params: { value: string }) => {
            const limitedLabels = getLimitedXAxisLabels()
            return limitedLabels.includes(params.value) ? params.value : ''
          },
        },
      },
      {
        type: 'number',
        position: 'left',
        title: {
          text: '# of Cases',
        },
        keys: ['numerator', 'inDenominatorOnly'],
      },
      {
        type: 'number',
        position: 'right',
        title: {
          text: performanceRateTitle,
        },
        min: 0,
        max: 100,
        label: {
          formatter: percentageFormatter,
        },
        keys: ['performance'],
      },
    ],
  })

  // Common category axis for column and line charts
  const bottomCategoryAxis = {
    type: 'category',
    position: 'bottom',
    title: {
      enabled: false,
    },
    label: {
      fontSize: 12,
      formatter: (params: { value: string }) => {
        const limitedLabels = getLimitedXAxisLabels()
        return limitedLabels.includes(params.value) ? params.value : ''
      },
    },
  }

  // Common performance axis for column and line charts
  const leftPerformanceAxis = {
    type: 'number',
    position: 'left',
    title: {
      text: performanceRateTitle,
    },
    min: 0,
    max: 100,
    label: {
      formatter: percentageFormatter,
    },
  }

  // Column graph options
  const columnOptions: AgCartesianChartOptions = createChartOptions({
    chartType: 'column',
    data: getTransformedData(),
    series: [
      {
        type: 'bar',
        xKey: 'unit',
        yKey: 'performance',
        yName: currentMeasureName,
        fill: '#1e4976', // Dark blue color
        strokeWidth: 0,
      },
    ],
    axes: [bottomCategoryAxis, leftPerformanceAxis],
  })

  // Line graph options
  const lineOptions: AgCartesianChartOptions = createChartOptions({
    chartType: 'line',
    data: getTransformedData(),
    series: [
      {
        type: 'line',
        xKey: 'unit',
        yKey: 'performance',
        yName: currentMeasureName,
        stroke: '#1e4976', // Dark blue line color
        strokeWidth: 2,
        marker: {
          enabled: true,
          fill: '#1e4976',
          stroke: '#1e4976',
        },
      },
    ],
    axes: [bottomCategoryAxis, leftPerformanceAxis],
  })

  // Get the appropriate chart options or component based on graph type
  const renderChart = () => {
    switch (graphType) {
      case 'BAR_GRAPH':
        return <AgCharts options={barOptions} />
      case 'STACKEDBAR_GRAPH':
        return <AgCharts options={stackedBarOptions} />
      case 'COLUMN_GRAPH':
        return <AgCharts options={columnOptions} />
      case 'LINE_GRAPH':
        return <AgCharts options={lineOptions} />
      case 'SPC_GRAPH':
        // Render our SPC chart when SPC_GRAPH is selected
        return spcData ? (
          <div className="h-[400px]">
            <SPCChart
              data={spcData}
              period={
                timePeriod === TimePeriod.Monthly
                  ? 'Monthly'
                  : timePeriod === TimePeriod.Quarterly
                    ? 'Quarterly'
                    : timePeriod === TimePeriod.Yearly
                      ? 'Yearly'
                      : 'Monthly'
              }
            />
          </div>
        ) : null
      default:
        return <AgCharts options={barOptions} />
    }
  }

  if (!currentMeasureId) {
    return <Loader />
  }

  return (
    <div
      className={cn('overflow-x-auto', className)}
      style={{ fontFamily: 'Open Sans', fontSize: '13px', color: '#5B6372' }}
    >
      {measureResultsDetailsQuery.isLoading ? (
        <Loader />
      ) : (
        <>
          <div className="text-left mb-4 ms-4 font-bold text-base">
            Period: {format(startDate, 'MM/dd/yyyy')} -{' '}
            {format(endDate, 'MM/dd/yyyy')}
          </div>
          {renderChart()}
        </>
      )}

      {/* Display rule violations if SPC chart is selected and has violations */}
      {graphType === 'SPC_GRAPH' &&
        spcData &&
        spcData.ruleViolations.length > 0 && (
          <div className="mt-6 border-t pt-4">
            <h2 className="text-lg font-medium mb-2">
              Rule Violations & Limitations
            </h2>
            <div className="space-y-2">
              {spcData.ruleViolations.map((violation, index) => (
                <div key={index} className="flex gap-4">
                  <div className="w-24 text-sm">{violation.date}</div>
                  <div className="text-sm">{violation.description}</div>
                </div>
              ))}
            </div>
          </div>
        )}
    </div>
  )
}
