'use client'
import React, { useState, useEffect, useRef } from 'react'
import { X } from 'lucide-react'

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  iframeSrc: string
  title?: string
  width?: string
  height?: string
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  iframeSrc,
  title = 'Modal Title',
  width = 'max-w-4xl',
  height = 'h-[600px]',
}) => {
  const [_isAnimating, setIsAnimating] = useState<boolean>(false)
  const modalRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true)
      document.body.style.overflow = 'hidden'
    }
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent): void => {
      if (e.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onClose])

  const handleAnimationEnd = (): void => {
    if (!isOpen) {
      setIsAnimating(false)
    }
  }

  return (
    <div className="relative" ref={modalRef}>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black/50 z-50 backdrop-blur-sm transition-opacity duration-300
          ${isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'}
        `}
        onClick={onClose}
        onTransitionEnd={handleAnimationEnd}
      />

      {/* Modal */}
      <div
        className={`fixed left-1/2 top-1/2 -translate-x-1/2 z-50 w-full ${width}
          transition-all duration-300 ease-out
          ${
            isOpen
              ? '-translate-y-1/2 opacity-100 scale-100 visible'
              : '-translate-y-1/3 opacity-0 scale-95 invisible'
          }
        `}
        onTransitionEnd={handleAnimationEnd}
      >
        {/* Modal content */}
        <div className="bg-white rounded-lg shadow-xl overflow-hidden">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
              aria-label="Close modal"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* Body with iframe */}
          <div className={`relative w-full ${height} bg-white`}>
            <iframe
              src={iframeSrc}
              title={`${title} content`}
              className="w-full h-full"
              style={{
                visibility: isOpen ? 'visible' : 'hidden',
                position: isOpen ? 'relative' : 'absolute',
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
