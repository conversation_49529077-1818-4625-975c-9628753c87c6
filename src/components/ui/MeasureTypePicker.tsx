import React from 'react'
import Image from 'next/image'
import checked from '../../../public/images/checked-radio.svg'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import PickerButton from './PickerButton'
import { useUserSessionStore } from '@/stores/userSession'
import { useViewStore } from '@/stores/viewStore'
import { api } from '@/trpc/react'
import { useFilterStore } from '@/stores/filter'
import { usePathname } from 'next/navigation'

export const MeasureTypePicker = () => {
  const {
    setCurrentView,
    setMeasureTypeSwitchProcessing,
    measureTypeSwitchProcessing,
  } = useViewStore()

  const { primaryMeasureType, setPrimaryMeasureType } = useUserSessionStore()
  const { removeAllSavedFilters } = useFilterStore()

  const pathname = usePathname()
  const currentPage = pathname.substring(1)

  const primaryMeasureTypesList = api.measures.getPrimaryMeasureTypes.useQuery()

  const savedViews = api.savedViews.getSavedViews.useQuery({
    currentPage: currentPage,
  })
  const handleMeasureChange = (measure: PrimaryMeasureTypeConstants) => {
    // Clear current view first

    setPrimaryMeasureType(measure)
    setCurrentView(undefined)
    removeAllSavedFilters()
    setMeasureTypeSwitchProcessing(!measureTypeSwitchProcessing)
    // console.log('measure type changed to', measure)
  }

  return (
    <PickerButton text={primaryMeasureType?.toUpperCase()}>
      <div className="p-4">
        {primaryMeasureTypesList.data?.map((measureType, idx) => (
          <div
            style={{
              padding: '10px 8px 10px 8px',
            }}
            key={idx}
            className={`flex items-center justify-between gap-10 border-[0.75px] border-[#97A4BA] rounded-[2px] mb-2 bg-ghostwhite hover:bg-white ${
              primaryMeasureType === measureType ? 'bg-white' : ''
            }`}
            onClick={() => handleMeasureChange(measureType)}
          >
            <span className="text-[#1B4A70] text-[12px] font-open-sans font-semibold leading-[16.34px]">
              {measureType
                .split(' ')
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ')}
            </span>
            {primaryMeasureType === measureType && (
              <Image src={checked} alt="check" width={14} height={14} />
            )}
          </div>
        ))}
      </div>
    </PickerButton>
  )
}

export default MeasureTypePicker
