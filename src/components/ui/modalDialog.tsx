import React, { useEffect, useRef, useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { cn } from '@/lib/utils'
import { useClickOutside } from '@/hooks/useClickOutside'

type Props = {
  children: React.ReactNode
  title?: string
  className?: string
  triggerElement: React.ReactNode
}
export const ModalDialog = ({
  children,
  title,
  className,
  triggerElement,
}: Props) => {
  const ref = useRef(null)
  const [isOpen, setIsOpen] = useState(false)

  useClickOutside(ref, () => setIsOpen(false))

  return (
    <div ref={ref}>
      <Dialog open={isOpen}>
        <DialogTrigger
          asChild
          onClick={(e) => {
            e.preventDefault()
            setIsOpen(true)
          }}
        >
          {triggerElement}
        </DialogTrigger>
        <DialogContent className={cn(``, className)}>
          <DialogHeader>
            (title && <DialogTitle>{title}</DialogTitle>)
            <DialogDescription>{children}</DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </div>
  )
}
