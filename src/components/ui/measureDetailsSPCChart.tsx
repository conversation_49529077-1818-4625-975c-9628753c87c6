"use client"

import { useEffect, useState } from "react"
import dynamic from "next/dynamic"
import type { ProcessedSPCData } from "@/types/measureDetailsSPCData"
import type { PlotData, Layout, Config } from "plotly.js"

// Dynamically import Plotly to avoid SSR issues
const Plot = dynamic(() => import("react-plotly.js"), { ssr: false })

export interface SPCChartProps {
  data: ProcessedSPCData
  period: "Monthly" | "Quarterly" | "Yearly"
}

export default function SPCChart({ data, period }: SPCChartProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return <div className="h-full w-full flex items-center justify-center">Loading chart...</div>

  // Performance line
  const performanceLine: Partial<PlotData> = {
    x: data.dates,
    y: data.performance,
    type: "scatter",
    mode: "lines+markers",
    name: "Performance",
    line: {
      color: "#124c87",
      width: 3,
      shape: "linear",
    },
    marker: {
      size: 8,
      color: data.ruleViolationPoints.map((isViolation, i) =>
        isViolation ? "red" : data.excludedPoints[i] ? "orange" : "blue",
      ),
      line: {
        width: data.ruleViolationPoints.map((isViolation) => (isViolation ? 2 : 1)),
        color: data.ruleViolationPoints.map((isViolation) => (isViolation ? "darkred" : "#124c87")),
      },
    },
  }

  // Center line - using steps
  const centerLine: Partial<PlotData> = {
    x: data.dates,
    y: data.centerLine,
    type: "scatter",
    mode: "lines",
    name: "Center Line",
    line: {
      color: "#808080",
      width: 2,
      shape: "hv", // This creates the step effect (horizontal then vertical)
    },
  }

  // Upper control limit - using steps
  const upperControlLimit: Partial<PlotData> = {
    x: data.dates,
    y: data.upperLimit,
    type: "scatter",
    mode: "lines",
    name: "Upper Control Limit (3σ)",
    line: {
      color: "#fc7570",
      width: 2,
      shape: "hv", // Step effect
      dash: "solid",
    },
    visible: true,
  }

  // Lower control limit - using steps
  const lowerControlLimit: Partial<PlotData> = {
    x: data.dates,
    y: data.lowerLimit,
    type: "scatter",
    mode: "lines",
    name: "Lower Control Limit (3σ)",
    line: {
      color: "#fc7570",
      width: 2,
      shape: "hv", // Step effect
      dash: "solid",
    },
    visible: true,
  }

  // Upper warning limit - using steps
  const upperWarningLimit: Partial<PlotData> = {
    x: data.dates,
    y: data.upperWarning,
    type: "scatter",
    mode: "lines",
    name: "Upper Warning (2σ)",
    line: {
      color: "#ffcd84",
      width: 2,
      shape: "hv", // Step effect
      dash: "dot",
    },
    visible: true,
  }

  // Lower warning limit - using steps
  const lowerWarningLimit: Partial<PlotData> = {
    x: data.dates,
    y: data.lowerWarning,
    type: "scatter",
    mode: "lines",
    name: "Lower Warning (2σ)",
    line: {
      color: "#ffcd84",
      width: 2,
      shape: "hv", // Step effect
      dash: "dot",
    },
    visible: true,
  }

  // Phase change lines
  const phaseChangeLines = data.phaseChanges.map((index) => ({
    type: "line" as const,
    x0: data.dates[index],
    y0: Math.min(...data.lowerLimit.filter((v) => v !== null)) * 0.95,
    x1: data.dates[index],
    y1: Math.max(...data.upperLimit.filter((v) => v !== null)) * 1.05,
    line: {
      color: "rgb(128, 128, 128)",
      width: 3,
    },
  }))

  // Calculate y-axis range
  const allValues = [
    ...data.performance,
    ...data.upperLimit.filter((v) => v !== null),
    ...data.lowerLimit.filter((v) => v !== null),
  ]
  const minY = Math.min(...allValues) * 0.95
  const maxY = Math.max(...allValues) * 1.05

  const layout: Partial<Layout> = {
    // Remove all titles and text labels
    title: {
      text: "",
      font: {
        size: 0,
      },
    },
    xaxis: {
      title: {
        text: "",
      },
      tickangle: 0,
      gridcolor: "#e1e5ed",
      zeroline: false,
    },
    yaxis: {
      title: {
        text: "",
      },
      tickformat: data.measureScoring === "Rate" || data.measureScoring === "Percentage" ? ".0%" : "",
      gridcolor: "#e1e5ed",
      zeroline: false,
      range: [minY, maxY],
    },
    shapes: phaseChangeLines,
    legend: {
      orientation: "h",
      y: -0.2,
    },
    hovermode: "closest",
    margin: { t: 10, r: 10, b: 50, l: 50 },
    plot_bgcolor: "#ffffff",
    paper_bgcolor: "#ffffff",
  }

  const config: Partial<Config> = {
    responsive: true,
    displayModeBar: true,
    modeBarButtonsToRemove: ["lasso2d", "select2d", "autoScale2d"] as any,
  }

  return (
    <Plot
      data={[
        lowerControlLimit as PlotData,
        upperControlLimit as PlotData,
        lowerWarningLimit as PlotData,
        upperWarningLimit as PlotData,
        centerLine as PlotData,
        performanceLine as PlotData,
      ]}
      layout={layout}
      config={config}
      style={{ width: "100%", height: "100%" }}
    />
  )
}
