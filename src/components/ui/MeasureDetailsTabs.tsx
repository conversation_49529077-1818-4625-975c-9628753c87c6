import { useUIStore } from '@/stores/ui'
import React from 'react'
export const MeasureDetailsTabs = () => {
  const {
    setCurrentMeasureDetailTab,
    currentMeasureDetailTab,
    setGraphType,
    isIAPIMeasure,
  } = useUIStore()

  const handleGraphTabClick = () => {
    setCurrentMeasureDetailTab('GRAPH')
    setGraphType('BAR_GRAPH')
  }
  // Base styles for all buttons
  const baseStyles = 'text-[13px] py-1 px-8 border-y border-r border-gray-300 transition-all duration-200'
  // First button needs left border
  const firstButtonStyles = 'border-l border-gray-300 rounded-l-md'
  // Last button needs rounded right corners
  const lastButtonStyles = 'rounded-r-md'
  // Styles for unselected buttons
  const unselectedStyles = `${baseStyles} bg-[#FFFFFF] text-[#566582] font-normal w-[177px] h-[27px] rounded-tr-[5px] rounded-br-[5px] border-t border-r border-b pr-[30px] pl-[30px] gap-[10px] hover:bg-[#F5F7FF] hover:text-[#97A4BA] hover:w-[177px] hover:h-[27px] hover:border-t hover:border-r hover:border-b hover:pr-[30px] hover:pl-[30px] hover:gap-[10px] hover:font-normal`
  // Styles for selected button
  const selectedStyles = `${baseStyles} bg-[#EBEFFD] text-[#205782] font-['Open_Sans'] font-bold text-[13px] leading-[100%] tracking-[0%] text-center align-middle uppercase w-[181px] h-[27px] rounded-tl-[5px] rounded-bl-[5px] border border-[#566582] pr-[30px] pl-[30px] gap-[10px] hover:bg-[#F5F7FF] hover:text-[#97A4BA] hover:w-[177px] hover:h-[27px] hover:border-t hover:border-r hover:border-b hover:pr-[30px] hover:pl-[30px] hover:gap-[10px] hover:font-normal`

  return (
    <div className="p-4">
      <ul className="flex font-sans shadow-sm max-w-fit mx-auto rounded-md overflow-hidden">
        {!isIAPIMeasure && (
          <li>
            <button
              className={`${currentMeasureDetailTab === 'GRAPH'
                ? selectedStyles
                : unselectedStyles
                } ${firstButtonStyles}`

              }

              onClick={() => handleGraphTabClick()}
            >
              GRAPH
            </button>
          </li>
        )}
        <li>
          <button
            className={`${currentMeasureDetailTab === 'TABLE'
              ? selectedStyles
              : unselectedStyles
              } ${isIAPIMeasure ? firstButtonStyles : ''}`}
            onClick={() => setCurrentMeasureDetailTab('TABLE')}
          >
            TABLE
          </button>
        </li>
        <li>
          <button
            className={`${currentMeasureDetailTab === 'METADATA'
              ? selectedStyles
              : unselectedStyles
              } ${lastButtonStyles}`}
            onClick={() => setCurrentMeasureDetailTab('METADATA')}
          >
            METADATA
          </button>
        </li>
      </ul>
    </div>
  )
}
