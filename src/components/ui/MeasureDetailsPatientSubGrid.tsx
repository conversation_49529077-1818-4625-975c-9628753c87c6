'use client'

import React, { useEffect, useState } from 'react'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  SortingState,
  useReactTable,
  ColumnFiltersState,
} from '@tanstack/react-table'
import { IoCaretForward } from 'react-icons/io5'
import { IoMdSkipForward } from 'react-icons/io'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'
import { EntityDetailType } from '@/enums/entityDetailType'
import { ScorecardView } from '@/enums/scorecardView'
import { api } from '@/trpc/react'
import { useUserSessionStore } from '@/stores/userSession'
import useStore from '@/stores/useStore'
import Loader from './Loader'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'
import { customStringFilter } from '@/lib/customStringFilter'
import { getSortingIcon } from './sortIcon'

interface PatientDataRow {
  patientName: string
  age: number
  gender: string
  result: string
  dischargeDate: string
}

const columnHelper = createColumnHelper<PatientDataRow>()
const filterFunction = customStringFilter<PatientDataRow>()

const columns = [
  columnHelper.accessor('patientName', {
    header: 'Patient Name',
    cell: (info) => info.getValue(),
    filterFn: filterFunction,
  }),
  columnHelper.accessor('age', {
    header: 'Age',
    cell: (info) => info.getValue(),
    sortingFn: (a, b) => a.original.age - b.original.age,
    filterFn: filterFunction, // Apply customStringFilter to age column
  }),
  columnHelper.accessor('gender', {
    header: 'Gender',
    cell: (info) => info.getValue(),
    filterFn: filterFunction,
  }),
  columnHelper.accessor('result', {
    header: 'Result',
    cell: (info) => info.getValue(),
    filterFn: filterFunction,
  }),
  columnHelper.accessor('dischargeDate', {
    header: 'Discharge Date',
    cell: (info) => info.getValue(),
    filterFn: filterFunction,
  }),
]

type Props = {
  measureId: string
  period: string
  scorecardView: ScorecardView
  entityId: string
  entityType: EntityDetailType
  sourceContainerIdentifier?: string
  primaryMeasureType: PrimaryMeasureTypeConstants
}

export const MeasureDetailsPatientSubGrid = ({
  measureId,
  period,
  scorecardView,
  entityId,
  entityType,
  sourceContainerIdentifier,
  primaryMeasureType,
}: Props) => {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const pageSize = 10 // Default page size
  const [isDataReady, setIsDataReady] = useState(false)

  const session = useStore(useUserSessionStore, (state) => state)

  const patientDetailsData = api.patients.getPatientDetails.useQuery(
    {
      measureId,
      periodSpan: period,
      entityDetailType: entityType,
      entityId,
      sourceContainerIdentifier,
      isPatientLevelAccessAvailable: true,
      scorecardView,
      primaryMeasureType,
    },
    {
      enabled: isDataReady,
    }
  )

  useEffect(() => {
    if (session?.organizationId) {
      setIsDataReady(true)
    }
  }, [session?.organizationId])

  const table = useReactTable<PatientDataRow>({
    data: patientDetailsData.data ?? [],
    columns,
    state: {
      sorting,
      columnFilters,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    sortDescFirst: false,
    initialState: {
      pagination: {
        pageSize: pageSize,
      },
    },
  })

  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
    lineHeight: '1.5',
    minHeight: '45px',
  } as React.CSSProperties

  if (patientDetailsData.isPending) {
    return (
      <div className="overflow-x-auto relative mt-10">
        <Loader />
      </div>
    )
  }

  return (
    <>
      <div className="overflow-x-auto relative">
        <table className="w-full border-collapse">
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={headerStyle}
                    className="border-b border-l cursor-pointer select-none pt-['18px'] pb-['18px'] relative"
                  >
                    {header.isPlaceholder ? null : (
                      <div className="flex justify-center items-center">
                        <span
                          className="flex m-3 w-full text-center justify-center"
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {getSortingIcon(
                            header.column.getIsSorted() as boolean,
                            header.column.getIsSorted() === 'desc'
                          )}
                        </span>
                        <div className="flex">
                          <DataTableColumnFilter
                            column={header.column}
                            setColumnFilters={setColumnFilters}
                          />
                        </div>
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <tr
                key={row.id}
                className={`${
                  row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                }`}
              >
                {row.getVisibleCells().map((cell, i) => (
                  <td
                    key={cell.id}
                    className={`p-2 text-center border-[#DDDCDF] border-r border-b border-l align-middle font-open-sans`}
                    style={{ fontSize: '14px', padding: '10px 24px' }}
                  >
                    <span
                      className={`inline-flex ${i === 0 ? 'relative -left-3' : ''}`}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </span>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="px-4 border-t border-[#DDDCDF] flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <IoMdSkipForward size={16} className="rotate-180" />
            </button>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <IoCaretForward size={16} className="rotate-180" />
            </button>
            <ul
              className="flex"
              style={{
                paddingInlineStart: '40px',
                marginBlockStart: '1em',
                marginBlockEnd: '1em',
                marginInlineStart: '0px',
                marginInlineEnd: '0px',
                lineHeight: '2',
                position: 'relative',
                alignItems: 'center',
                padding: '6px 6px',
                alignSelf: 'stretch',
                alignContent: 'stretch',
              }}
            >
              <GeneratePageNumbers table={table} />
            </ul>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <IoCaretForward size={16} className="" />
            </button>
            <button
              className="px-2 py-1  text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <IoMdSkipForward size={16} className="" />
            </button>
          </div>
          <select
            value={table.getState().pagination.pageSize}
            onChange={(e) => {
              table.setPageSize(Number(e.target.value))
            }}
            className="pl-8 p-1 text-[14px] font-open-sans text-black"
          >
            {[10, 25, 50, 100].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </select>
          <span
            className="text-[14px] font-open-sans text-black"
            style={{ padding: '10px 8px', lineHeight: '2' }}
          >
            Items per page
          </span>
        </div>
      </div>
    </>
  )
}
