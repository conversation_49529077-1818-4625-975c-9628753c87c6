'use client'

import React, { useEffect } from 'react'
import { useDateStore } from '@/stores/dates'
import { MeasureDetailsTabs } from './MeasureDetailsTabs'
import MeasureDetailsReportPicker from './MeasureDetailsReportPicker'
import { useUIStore } from '@/stores/ui'
import PeriodPicker from "@/components/ui/PeriodPicker";
import MeasurePerformanceExporter from "@/components/ui/MeasurePerformanceExporter";

const MeasureDetailsActionBar = () => {
  const { applyPresetRange, mode, selectedRange } = useDateStore()
  const { currentMeasureDetailTab, isIAPIMeasure } = useUIStore()

  useEffect(() => {
    if (mode === 'Quarterly') {
      if (!selectedRange?.[0]) {
        applyPresetRange()
      }
    }
  }, [mode, selectedRange])

  return (
    <div className="h-[85px] w-full relative">
      <ul className="flex justify-center items-center text-center w-full ">
        {currentMeasureDetailTab == 'GRAPH' && (
            <li className="absolute left-0">
              <PeriodPicker />
            </li>
        )}

        <li className="">
          <MeasureDetailsTabs />
        </li>

        {currentMeasureDetailTab == 'GRAPH' && (
            <li className="absolute right-[125px]">
              <MeasurePerformanceExporter />
            </li>
        )}

        {!isIAPIMeasure && (
          <li className="absolute right-0">
            <MeasureDetailsReportPicker />
          </li>
        )}
      </ul>
    </div>
  )
}

export default MeasureDetailsActionBar
