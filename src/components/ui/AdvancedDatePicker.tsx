'use client'

import React, { useEffect, useState } from 'react'
import { ChevronLeft, ChevronRight, ChevronDown, ChevronUp } from 'lucide-react'
import { useDateStore } from '@/stores/dates'
import { type DatePickerMode } from '@/types/datePickerMode'
import dayjs, { Dayjs } from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'

dayjs.extend(isBetween)

// const DAYS = ['S', 'M', 'T', 'W', 'T', 'F', 'S']
const MONTHS = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
]
const BUSINESS_QUARTERS = [0, 3, 6, 9] // Jan, Apr, Jul, Oct

type AdvancedDatePickerProps = {
  selectedRange: [Dayjs | null, Dayjs | null] | null
  setSelectedRange: React.Dispatch<
    React.SetStateAction<[Dayjs | null, Dayjs | null] | null>
  >
  selectionType?: DatePickerMode
  onChange: (value: Dayjs | null | [Dayjs | null, Dayjs | null]) => void
  isOpen: boolean
  toggleIsOpen: () => void
  toggleAdvancedPickerOpen: () => void
  showLabel?: boolean
}

export default function AdvancedDatePicker({
  selectedRange,
  setSelectedRange,
  selectionType = 'Quarterly',
  onChange,
  isOpen,
  toggleIsOpen,
  toggleAdvancedPickerOpen,
  showLabel = false,
}: AdvancedDatePickerProps) {
  const { mode: globalMode, viewDate: globalViewDate } = useDateStore()

  // Local state
  const [mode, setLocalMode] = useState(globalMode)
  const [viewDate, setLocalViewDate] = useState(globalViewDate)

  useEffect(() => {
    setLocalMode(selectionType)
  }, [selectionType])

  useEffect(() => {
    if (isOpen && selectedRange?.[0]) {
      setLocalViewDate(dayjs().year(selectedRange[0].year()).startOf('month'))
    }
  }, [isOpen, selectedRange])

  useEffect(() => {
    if (onChange) {
      onChange(selectedRange)
    }
  }, [selectedRange, mode, onChange])

  const formatHeaderDate = () => {
    if (
      mode === 'Monthly' ||
      mode === 'Yearly' ||
      mode === 'Range' ||
      mode === 'Quarterly'
    ) {
      if (selectedRange?.[0] && selectedRange?.[1]) {
        const start = selectedRange?.[0]
        const end = selectedRange?.[1]
        if (start.year() === end.year() && start.month() === end.month()) {
          return `${MONTHS[start.month()]} ${start.year()}`
        } else {
          return `${MONTHS[start.month()]} ${start.year()} - ${MONTHS[end.month()]} ${end.year()}`
        }
      } else {
        return 'Select a range'
      }
    }
  }

  const getDecadeRange = (year: number) => {
    const start = Math.floor(dayjs().year(year).year() / 10) * 10 + 1
    const end = start + 9
    return { start, end }
  }
  const renderMonthPicker = () => {
    const currentYear = viewDate.year()
    const nextYear = currentYear + 1
    const [hoveredDate, setHoveredDate] = useState<Dayjs | null>(null)

    const renderYear = (year: number) => (
      <div
        key={year}
        className="grid grid-cols-4 bg-white p-4 rounded-lg focus:outline-none"
      >
        {MONTHS.map((month, index) => {
          const date = dayjs().utc().year(year).month(index).startOf('month')

          // Ensure the selected range is properly determined
          const rangeStart = selectedRange?.[0]?.utc().startOf('month')
          const rangeEnd = selectedRange?.[1]?.utc().endOf('month')

          // Check if this month is the exact start or end
          const isRangeStart = rangeStart && date.isSame(rangeStart, 'month')
          const isRangeEnd = rangeEnd && date.isSame(rangeEnd, 'month')

          // Check if the month is within the range (excluding the start and end)
          const isInRange =
            rangeStart &&
            rangeEnd &&
            date.isBetween(rangeStart, rangeEnd, 'month', '()')

          const isInHoverRange =
            selectedRange?.[0] &&
            !selectedRange?.[1] &&
            hoveredDate &&
            date.isBetween(selectedRange?.[0], hoveredDate, 'month', '[]')

          let currentRoundedEdge = ''

          if (isRangeStart && isRangeEnd) {
            currentRoundedEdge = 'rounded-l rounded-r'
          } else if (isRangeStart) {
            currentRoundedEdge = 'rounded-l'
          } else if (isRangeEnd) {
            currentRoundedEdge = 'rounded-r'
          }

          return (
            <button
              key={`${year}-${month}`}
              className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans w-[36px] h-[30px] focus:outline-none 
        ${
          isRangeStart || isRangeEnd
            ? `bg-ui-dark-gray text-white ${currentRoundedEdge} hover:text-blue-1`
            : isInRange
              ? 'bg-[#EBEFFD] text-black'
              : isInHoverRange
                ? 'bg-[#F0F4FF] text-[#D1D5DB]'
                : 'text-black'
        }
        hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
              onClick={() => {
                if (
                  !selectedRange?.[0] ||
                  (selectedRange?.[0] && selectedRange?.[1])
                ) {
                  setSelectedRange([date, null]) // Start a new range
                } else {
                  const newRange = [selectedRange[0], date].sort(
                    (a, b) => a.valueOf() - b.valueOf()
                  ) as [Dayjs, Dayjs]

                  // Update range to full months
                  const updatedRange: [Dayjs, Dayjs] = [
                    newRange[0].startOf('month'),
                    newRange[1].endOf('month'),
                  ]
                  setSelectedRange(updatedRange)
                  toggleAdvancedPickerOpen()
                }
              }}
              onMouseEnter={() => {
                if (selectedRange?.[0] && !selectedRange?.[1]) {
                  setHoveredDate(date)
                }
              }}
            >
              {month}
            </button>
          )
        })}
      </div>
    )

    return (
      <div
        onMouseLeave={() => {
          setHoveredDate(null)
        }}
        className="flex"
      >
        {renderYear(currentYear)}
        {renderYear(nextYear)}
      </div>
    )
  }

  const renderYearPicker = () => {
    const decadeRange = getDecadeRange(viewDate.year())
    const currentYear = decadeRange.end
    const startYear = decadeRange.start
    const [hoveredDate, setHoveredDate] = useState<Dayjs | null>(null)

    const years = []

    for (let i = startYear; i <= currentYear; i++) {
      const year = i
      const yearStart = dayjs().utc().year(year).month(0).startOf('month')
      const yearEnd = dayjs().utc().year(year).month(11).endOf('month')

      const rangeStart = selectedRange?.[0]?.startOf('year')
      const rangeEnd = selectedRange?.[1]?.endOf('year')

      // Check if this year is the exact start or end
      const isRangeStart = rangeStart && yearStart.isSame(rangeStart, 'year')
      const isRangeEnd = rangeEnd && yearEnd.isSame(rangeEnd, 'year')

      // Check if the year is within the range (excluding the start and end)
      const isInRange =
        rangeStart &&
        rangeEnd &&
        yearStart.isBetween(rangeStart, rangeEnd, 'year', '()')

      // Calculate if this month is in the hover range (between start date and hovered date)
      const isInHoverRange =
        selectedRange?.[0] &&
        !selectedRange?.[1] &&
        hoveredDate &&
        yearStart.isBetween(selectedRange?.[0], hoveredDate, 'month', '[]')

      let currentRoundedEdge = ''

      if (isRangeStart && isRangeEnd) {
        currentRoundedEdge = 'rounded-l rounded-r'
      } else if (isRangeStart) {
        currentRoundedEdge = 'rounded-l'
      } else if (isRangeEnd) {
        currentRoundedEdge = 'rounded-r'
      }

      years.push(
        <button
          key={year}
          className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans h-[30px] focus:outline-none 
            ${
              isRangeStart || isRangeEnd
                ? `bg-ui-dark-gray text-white ${currentRoundedEdge} hover:text-blue-1`
                : isInRange
                  ? 'bg-[#EBEFFD] text-black'
                  : isInHoverRange
                    ? 'bg-[#F0F4FF] text-[#D1D5DB]'
                    : 'text-black'
            }
            hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
          onClick={() => {
            if (
              !selectedRange?.[0] ||
              (selectedRange?.[0] && selectedRange?.[1])
            ) {
              // If first selection, always start at January
              setSelectedRange([yearStart, null])
            } else {
              const newRange = [selectedRange?.[0], yearEnd].sort(
                (a, b) => a.valueOf() - b.valueOf()
              ) as [Dayjs, Dayjs]

              // Ensure the second selection always ends at December
              setSelectedRange([
                newRange[0].startOf('year'),
                newRange[1].endOf('year'),
              ])
              toggleAdvancedPickerOpen()
            }
          }}
          onMouseEnter={() => {
            if (selectedRange?.[0] && !selectedRange?.[1]) {
              setHoveredDate(yearStart)
            }
          }}
        >
          {year}
        </button>
      )
    }

    return (
      <div
        onMouseLeave={() => {
          setHoveredDate(null)
        }}
        className="grid grid-cols-4 bg-white p-4 rounded-lg"
      >
        {years}
      </div>
    )
  }

  const renderQuarterlyPicker = () => {
    const currentYear = viewDate.year()
    const nextYear = currentYear + 1

    const [hoveredDate, setHoveredDate] = useState<Dayjs | null>(null)

    const renderYear = (year: number) => {
      return (
        <div key={year} className="grid grid-cols-4 bg-white p-4 rounded-lg">
          {MONTHS.map((month, index) => {
            const date = dayjs().utc().year(year).month(index).startOf('month')
            const isValidQuarter =
              BUSINESS_QUARTERS.includes(index) ||
              BUSINESS_QUARTERS.map((q) => q + 2).includes(index)

            const rangeStart = selectedRange?.[0]?.utc().startOf('month')
            const rangeEnd = selectedRange?.[1]?.utc().endOf('month')

            // Check if this month is within the selected range (excluding the start and end)
            const isInRange =
              rangeStart &&
              rangeEnd &&
              date.isBetween(rangeStart, rangeEnd, 'month', '()')

            // Check if this month is the exact start or end
            const isRangeStart = rangeStart && date.isSame(rangeStart, 'month')
            const isRangeEnd = rangeEnd && date.isSame(rangeEnd, 'month')

            // Calculate if this month is in the hover range (between start date and hovered date)
            const isInHoverRange =
              selectedRange?.[0] &&
              !selectedRange?.[1] &&
              hoveredDate &&
              date.isBetween(selectedRange?.[0], hoveredDate, 'month', '[]')

            let isDisabled = false // used to disable the button, user can not click on month
            let isVisuallyDisabled = false // used to grey out the text but user can still select month where isvisuallyDisabled = true

            if (
              // if month is not a valid quarter or if end date is not selected, disable months before start date and months with start of the quarters
              !isValidQuarter ||
              (selectedRange?.[0] &&
                !selectedRange?.[1] &&
                (date.isBefore(selectedRange?.[0]) ||
                  BUSINESS_QUARTERS.includes(index)))
            ) {
              isDisabled = true
              isVisuallyDisabled = true
            } else if (selectedRange?.[1]) {
              //when both start and end are selected

              //disable months which are quarter ends
              if (BUSINESS_QUARTERS.map((q) => q + 2).includes(index))
                isDisabled = true
              // enable months which are quarter starts
              else isDisabled = false

              // everything else is visually disabled
              isVisuallyDisabled = true
            }
            let currentRoundedEdge = ''

            if (isRangeStart && isRangeEnd) {
              currentRoundedEdge = 'rounded-l rounded-r'
            } else if (isRangeStart) {
              currentRoundedEdge = 'rounded-l'
            } else if (isRangeEnd) {
              currentRoundedEdge = 'rounded-r'
            }

            return (
              <button
                key={`${year}-${month}`}
                className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans w-[36px] h-[30px] focus:outline-none 
                ${
                  isRangeStart || isRangeEnd
                    ? `bg-ui-dark-gray text-white ${currentRoundedEdge}`
                    : isInRange
                      ? `bg-[#EBEFFD] ${isVisuallyDisabled ? 'text-[#D1D5DB]' : 'text-black'}`
                      : isInHoverRange
                        ? 'bg-[#F0F4FF] text-[#D1D5DB]'
                        : isVisuallyDisabled
                          ? 'text-[#D1D5DB]'
                          : 'text-black'
                }
                ${isDisabled ? 'cursor-not-allowed' : ''}
                hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
                onClick={() => {
                  if (
                    !selectedRange?.[0] ||
                    (selectedRange?.[0] && selectedRange?.[1])
                  ) {
                    setSelectedRange([date, null])
                  } else if (date.valueOf() === selectedRange?.[0].valueOf()) {
                    // Deselect if clicking the same date
                    setSelectedRange([null, null])
                  } else {
                    const newRange = [selectedRange?.[0], date].sort(
                      (a, b) => a.valueOf() - b.valueOf()
                    ) as [Dayjs, Dayjs]

                    // Update range to full months
                    setSelectedRange([
                      newRange[0].startOf('month'),
                      newRange[1].endOf('month'),
                    ])
                    toggleAdvancedPickerOpen()
                  }
                }}
                onMouseEnter={() => {
                  if (selectedRange?.[0] && !selectedRange?.[1]) {
                    setHoveredDate(date)
                  }
                }}
                disabled={!!isDisabled}
              >
                {month}
              </button>
            )
          })}
        </div>
      )
    }

    return (
      <div
        className="flex"
        onMouseLeave={() => {
          // Clear hover state when mouse leaves the entire calendar area
          setHoveredDate(null)
        }}
      >
        {renderYear(currentYear)}
        {renderYear(nextYear)}
      </div>
    )
  }

  const navigateYear = (direction: 'prev' | 'next') => {
    if (mode === 'Yearly') {
      setLocalViewDate(
        dayjs()
          .utc()
          .set('year', viewDate.year() + (direction === 'next' ? 10 : -10))
          .month(viewDate.month())
          .startOf('month')
      )
    } else
      setLocalViewDate(
        dayjs()
          .utc()
          .set('year', viewDate.year() + (direction === 'next' ? 1 : -1))
          .month(viewDate.month())
          .startOf('month')
      )
  }

  return (
    <div className="font-open-sans">
      {showLabel && (
        <span
          className="color-ui-dark-gray bg-ui-pale-blue mt-18 ml-10 text-[13px] font-semibold p-2 inline-flex uppercase rounded-[5px]"
          onClick={toggleIsOpen}
        >
          {formatHeaderDate()}
          {!isOpen ? (
            <ChevronDown className="h-5 w-5 ml-2" />
          ) : (
            <ChevronUp className="h-5 w-5 ml-2" />
          )}
        </span>
      )}
      {isOpen && (
        <div className="relative z-10 w-auto">
          <div className="p-[10px]  bg-ui-pale-blue shadow-lg w-fit rounded-lg date-picker-popup shadow-[0px_0px_4px_0px_#00000040]">
            <div className="pt-4 bg-white">
              <div
                className={`flex justify-between items-center  ${mode === 'Yearly' ? 'ml-1 mr-1 pr-2 pl-2' : 'ml-8 mr-8'} `}
              >
                <div className="flex items-center text-ui-dark-gray font-bold">
                  <button
                    onClick={() => navigateYear('prev')}
                    className="focus:outline-none"
                  >
                    <ChevronLeft className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                  </button>
                  <span className="text-ui-dark-gray font-semibold ml-6">
                    {mode === 'Yearly'
                      ? `${getDecadeRange(viewDate.year()).start} - ${getDecadeRange(viewDate.year()).end}`
                      : viewDate.year()}
                  </span>
                </div>
                <div className="flex items-center">
                  {mode !== 'Yearly' && (
                    <span className="text-ui-dark-gray font-semibold mr-6">
                      {viewDate.year() + 1}
                    </span>
                  )}
                  <button
                    onClick={() => navigateYear('next')}
                    className="focus:outline-none"
                  >
                    <ChevronRight className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                  </button>
                </div>
              </div>
            </div>

            {/* {mode === 'Day' && renderDayPicker()} */}
            {mode === 'Monthly' && renderMonthPicker()}
            {mode === 'Yearly' && renderYearPicker()}
            {/* {mode === 'Range' && renderRangePicker()} */}
            {mode === 'Quarterly' && renderQuarterlyPicker()}
          </div>
        </div>
      )}
    </div>
  )
}
