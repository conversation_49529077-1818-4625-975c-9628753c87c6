'use client'

import React, { useEffect, useState } from 'react'
import { ChevronLeft, ChevronRight, ChevronDown, ChevronUp } from 'lucide-react'
import { useDateStore } from '@/stores/dates'
import { type DatePickerMode } from '@/types/datePickerMode'
import dayjs, { Dayjs } from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'

dayjs.extend(isBetween)

// const DAYS = ['S', 'M', 'T', 'W', 'T', 'F', 'S']
const MONTHS = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
]
const BUSINESS_QUARTERS = [0, 3, 6, 9] // Jan, Apr, Jul, Oct

type AdvancedDatePickerProps = {
  selectedRange: [Dayjs | null, Dayjs | null] | null
  setSelectedRange: React.Dispatch<
    React.SetStateAction<[Dayjs | null, Dayjs | null] | null>
  >
  selectionType?: DatePickerMode
  onChange: (value: Dayjs | null | [Dayjs | null, Dayjs | null]) => void
  isOpen: boolean
  toggleIsOpen: () => void
  showLabel?: boolean
}

export default function AdvancedDatePicker({
  selectedRange,
  setSelectedRange,
  selectionType = 'Quarterly',
  onChange,
  isOpen,
  toggleIsOpen,
  showLabel = false,
}: AdvancedDatePickerProps) {
  const {
    selectedDate: globalSelectedDate,
    mode: globalMode,
    viewDate: globalViewDate,
    activePreset: globalActivePreset,
  } = useDateStore()

  // Local state
  const [selectedDate, setLocalSelectedDate] = useState(globalSelectedDate)
  const [mode, setLocalMode] = useState(globalMode)
  const [viewDate, setLocalViewDate] = useState(globalViewDate)
  const [activePreset, setLocalActivePreset] = useState(globalActivePreset)

  useEffect(() => {
    setLocalMode(selectionType)
  }, [selectionType])

  useEffect(() => {
    if (isOpen && selectedRange?.[0]) {
      setLocalViewDate(dayjs().year(selectedRange[0].year()).startOf('month'))
    }
  }, [isOpen, selectedRange])

  useEffect(() => {
    if (selectedRange?.[0] && selectedRange?.[1]) {
      const daysDiff = Math.round(
        (selectedRange?.[1].valueOf() - selectedRange?.[0].valueOf()) /
          (1000 * 60 * 60 * 24)
      )
      if (daysDiff === 59) setLocalActivePreset('60')
      else if (daysDiff === 89) setLocalActivePreset('90')
      else if (daysDiff === 179) setLocalActivePreset('180')
      else setLocalActivePreset(null)
    } else {
      setLocalActivePreset(null)
    }
  }, [selectedRange])

  useEffect(() => {
    if (onChange) {
      if (mode === 'Day') {
        onChange(selectedDate)
      } else {
        onChange(selectedRange)
      }
    }
  }, [selectedDate, selectedRange, mode, onChange])

  const formatHeaderDate = () => {
    if (mode === 'Day') {
      return selectedDate.format('MMM D, YYYY').toUpperCase()
    } else if (
      mode === 'Monthly' ||
      mode === 'Yearly' ||
      mode === 'Range' ||
      mode === 'Quarterly'
    ) {
      if (selectedRange?.[0] && selectedRange?.[1]) {
        const start = selectedRange?.[0]
        const end = selectedRange?.[1]
        if (start.year() === end.year() && start.month() === end.month()) {
          return `${MONTHS[start.month()]} ${start.year()}`
        } else {
          return `${MONTHS[start.month()]} ${start.year()} - ${MONTHS[end.month()]} ${end.year()}`
        }
      } else {
        return 'Select a range'
      }
    }
  }

  // const renderDayPicker = () => {
  //   const firstDayOfMonth = new Date(viewDate.year(), viewDate.month(), 1)
  //   const lastDayOfMonth = new Date(viewDate.year(), viewDate.month() + 1, 0)
  //   const daysInMonth = lastDayOfMonth.getDate()
  //   const startingDay = firstDayOfMonth.getDay()

  //   const days = []
  //   for (let i = 0; i < startingDay; i++) {
  //     days.push(<div key={`empty-${i}`} className="w-8 h-8" />)
  //   }
  //   for (let i = 1; i <= daysInMonth; i++) {
  //     const date = dayjs()
  //       .utc()
  //       .set('year', viewDate.year())
  //       .set('month', viewDate.month())
  //       .date(i)
  //     const isSelected =
  //       date.format('ddd MMM DD YYYY') ===
  //       selectedDate.format('ddd MMM DD YYYY')
  //     days.push(
  //       <button
  //         key={i}
  //         // variant="ghost" // shadCN cannot handle focus
  //         className={`w-8 h-8 p-0 text-sm focus:outline-none ${
  //           isSelected
  //             ? 'bg-blue-600 text-white hover:bg-blue-700'
  //             : 'hover:bg-gray-100'
  //         }`}
  //         onClick={() => setLocalSelectedDate(date)}
  //       >
  //         {i}
  //       </button>
  //     )
  //   }

  //   return (
  //     <div className="grid grid-cols-7 gap-1">
  //       {DAYS.map((day) => (
  //         <div
  //           key={day}
  //           className="text-center text-xs font-medium text-gray-500 focus:outline-none"
  //         >
  //           {day}
  //         </div>
  //       ))}
  //       {days}
  //     </div>
  //   )
  // }

  const renderMonthPicker = () => {
    const currentYear = viewDate.year()
    const nextYear = currentYear + 1

    const renderYear = (year: number) => (
      <div
        key={year}
        className="grid grid-cols-4 bg-white p-4 rounded-lg focus:outline-none"
      >
        {MONTHS.map((month, index) => {
          const date = dayjs().utc().year(year).month(index).startOf('month')

          // Ensure the selected range is properly determined
          const rangeStart = selectedRange?.[0]?.utc().startOf('month')
          const rangeEnd = selectedRange?.[1]?.utc().endOf('month')

          // Check if this month is the exact start or end
          const isRangeStart = rangeStart && date.isSame(rangeStart, 'month')
          const isRangeEnd = rangeEnd && date.isSame(rangeEnd, 'month')

          // Check if the month is within the range (excluding the start and end)
          const isInRange =
            rangeStart &&
            rangeEnd &&
            date.isBetween(rangeStart, rangeEnd, 'month', '()')

          let currentRoundedEdge = ''

          if (isRangeStart && isRangeEnd) {
            currentRoundedEdge = 'rounded-l rounded-r'
          } else if (isRangeStart) {
            currentRoundedEdge = 'rounded-l'
          } else if (isRangeEnd) {
            currentRoundedEdge = 'rounded-r'
          }

          return (
            <button
              key={`${year}-${month}`}
              className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans w-[36px] h-[30px] focus:outline-none 
        ${
          isRangeStart || isRangeEnd
            ? `bg-ui-dark-gray text-white ${currentRoundedEdge} hover:text-blue-1`
            : isInRange
              ? 'bg-[#EBEFFD] text-black'
              : 'text-black'
        }
        hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
              onClick={() => {
                if (
                  !selectedRange?.[0] ||
                  (selectedRange?.[0] && selectedRange?.[1])
                ) {
                  setSelectedRange([date, null]) // Start a new range
                } else {
                  const newRange = [selectedRange[0], date].sort(
                    (a, b) => a.valueOf() - b.valueOf()
                  ) as [Dayjs, Dayjs]

                  // Update range to full months
                  const updatedRange: [Dayjs, Dayjs] = [
                    newRange[0].startOf('month'),
                    newRange[1].endOf('month'),
                  ]

                  setSelectedRange(updatedRange)
                }
              }}
            >
              {month}
            </button>
          )
        })}
      </div>
    )

    return (
      <div className="flex">
        {renderYear(currentYear)}
        {renderYear(nextYear)}
      </div>
    )
  }

  const renderYearPicker = () => {
    const currentYear = viewDate.year()
    const startYear = currentYear - 5
    const years = []

    for (let i = 0; i < 12; i++) {
      const year = startYear + i
      const yearStart = dayjs().utc().year(year).month(0).startOf('month')
      const yearEnd = dayjs().utc().year(year).month(11).endOf('month')

      const rangeStart = selectedRange?.[0]?.startOf('year')
      const rangeEnd = selectedRange?.[1]?.endOf('year')

      // Check if this year is the exact start or end
      const isRangeStart = rangeStart && yearStart.isSame(rangeStart, 'year')
      const isRangeEnd = rangeEnd && yearEnd.isSame(rangeEnd, 'year')

      // Check if the year is within the range (excluding the start and end)
      const isInRange =
        rangeStart &&
        rangeEnd &&
        yearStart.isBetween(rangeStart, rangeEnd, 'year', '()')

      let currentRoundedEdge = ''

      if (isRangeStart && isRangeEnd) {
        currentRoundedEdge = 'rounded-l rounded-r'
      } else if (isRangeStart) {
        currentRoundedEdge = 'rounded-l'
      } else if (isRangeEnd) {
        currentRoundedEdge = 'rounded-r'
      }

      years.push(
        <button
          key={year}
          className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans h-[30px] focus:outline-none 
            ${
              isRangeStart || isRangeEnd
                ? `bg-ui-dark-gray text-white ${currentRoundedEdge} hover:text-blue-1`
                : isInRange
                  ? 'bg-[#EBEFFD] text-black'
                  : 'text-black'
            }
            hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
          onClick={() => {
            if (
              !selectedRange?.[0] ||
              (selectedRange?.[0] && selectedRange?.[1])
            ) {
              // If first selection, always start at January
              setSelectedRange([yearStart, null])
            } else {
              const newRange = [selectedRange?.[0], yearEnd].sort(
                (a, b) => a.valueOf() - b.valueOf()
              ) as [Dayjs, Dayjs]

              // Ensure the second selection always ends at December
              setSelectedRange([
                newRange[0].startOf('year'),
                newRange[1].endOf('year'),
              ])
            }
          }}
        >
          {year}
        </button>
      )
    }

    return (
      <div className="grid grid-cols-4 bg-white p-4 rounded-lg">{years}</div>
    )
  }

  // const renderRangePicker = () => {
  //   const firstMonth = dayjs()
  //     .utc()
  //     .year(viewDate.year())
  //     .month(viewDate.month())
  //     .startOf('month')
  //   const secondMonth = dayjs()
  //     .utc()
  //     .year(viewDate.year())
  //     .month(viewDate.month() + 1)
  //     .startOf('month')

  //   const renderMonth = (date: Dayjs) => {
  //     const daysInMonth = dayjs()
  //       .utc()
  //       .year(date.year())
  //       .month(date.month() + 1)
  //       .endOf('month')
  //       .date()
  //     const startingDay = dayjs()
  //       .utc()
  //       .year(date.year())
  //       .month(date.month())
  //       .startOf('month')
  //       .day()

  //     const days = []
  //     for (let i = 0; i < startingDay; i++) {
  //       days.push(<div key={`empty-${i}`} className="w-6 h-6" />)
  //     }
  //     for (let i = 1; i <= daysInMonth; i++) {
  //       const currentDate = dayjs()
  //         .utc()
  //         .set('year', date.year())
  //         .set('month', date.month())
  //         .date(i)

  //       const isInRange =
  //         selectedRange?.[0] &&
  //         selectedRange?.[1] &&
  //         currentDate >= selectedRange?.[0] &&
  //         currentDate <= selectedRange?.[1]
  //       const isRangeStart =
  //         selectedRange?.[0] &&
  //         currentDate.format('ddd MMM DD YYYY') ===
  //           selectedRange?.[0].format('ddd MMM DD YYYY')
  //       const isRangeEnd =
  //         selectedRange?.[1] &&
  //         currentDate.format('ddd MMM DD YYYY') ===
  //           selectedRange?.[1].format('ddd MMM DD YYYY')

  //       days.push(
  //         <Button
  //           key={i}
  //           variant="ghost"
  //           className={`rounded-none w-6 h-6 p-0 text-sm focus:outline-none ${
  //             isInRange ? 'bg-blue-100 hover:bg-blue-200' : 'hover:bg-gray-100'
  //           } ${
  //             isRangeStart || isRangeEnd
  //               ? 'bg-blue-600 text-white hover:bg-blue-700  hover:text-blue-1'
  //               : ''
  //           }`}
  //           onClick={() => {
  //             if (
  //               !selectedRange?.[0] ||
  //               (selectedRange?.[0] && selectedRange?.[1])
  //             ) {
  //               setSelectedRange([currentDate, null])
  //             } else {
  //               setSelectedRange(
  //                 [selectedRange?.[0], currentDate].sort(
  //                   (a, b) => a.valueOf() - b.valueOf()
  //                 ) as [Dayjs, Dayjs]
  //               )
  //             }
  //           }}
  //         >
  //           {i}
  //         </Button>
  //       )
  //     }

  //     return (
  //       <div className="flex flex-col items-center">
  //         <div className="font-medium mb-2 text-sm text-gray-500">
  //           {MONTHS[date.month()]} {date.year()}
  //         </div>
  //         <div className="grid grid-cols-7 gap-1">
  //           {DAYS.map((day) => (
  //             <div
  //               key={day}
  //               className="text-center text-xs font-medium text-gray-500"
  //             >
  //               {day}
  //             </div>
  //           ))}
  //           {days}
  //         </div>
  //       </div>
  //     )
  //   }

  //   return (
  //     <div className="flex">
  //       {renderMonth(firstMonth)}
  //       {renderMonth(secondMonth)}
  //     </div>
  //   )
  // }

  const renderQuarterlyPicker = () => {
    const currentYear = viewDate.year()
    const nextYear = currentYear + 1

    const renderYear = (year: number, isFirstYear: boolean) => (
      <div key={year} className="grid grid-cols-4 bg-white p-4 rounded-lg">
        {MONTHS.map((month, index) => {
          const date = dayjs().utc().year(year).month(index).startOf('month')
          const isValidQuarter = isFirstYear
            ? BUSINESS_QUARTERS.includes(index)
            : BUSINESS_QUARTERS.map((q) => q + 2).includes(index)

          const rangeStart = selectedRange?.[0]?.utc().startOf('month')
          const rangeEnd = selectedRange?.[1]?.utc().endOf('month')

          // Check if this month is within the selected range (excluding the start and end)
          const isInRange =
            rangeStart &&
            rangeEnd &&
            date.isBetween(rangeStart, rangeEnd, 'month', '()')

          // Check if this month is the exact start or end
          const isRangeStart = rangeStart && date.isSame(rangeStart, 'month')
          const isRangeEnd = rangeEnd && date.isSame(rangeEnd, 'month')

          const isDisabled =
            !isValidQuarter ||
            (selectedRange?.[0] &&
              !selectedRange?.[1] &&
              index < selectedRange?.[0].month()) ||
            (selectedRange?.[1] && index > selectedRange?.[1].month())

          let currentRoundedEdge = ''

          if (isRangeStart && isRangeEnd) {
            currentRoundedEdge = 'rounded-l rounded-r'
          } else if (isRangeStart) {
            currentRoundedEdge = 'rounded-l'
          } else if (isRangeEnd) {
            currentRoundedEdge = 'rounded-r'
          }

          return (
            <button
              key={`${year}-${month}`}
              className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans w-[36px] h-[30px] focus:outline-none 
            ${
              isRangeStart || isRangeEnd
                ? `bg-ui-dark-gray text-white ${currentRoundedEdge}`
                : isInRange
                  ? `bg-[#EBEFFD] ${isDisabled ? 'text-[#D1D5DB] cursor-not-allowed' : 'text-black'}`
                  : isDisabled
                    ? 'text-[#D1D5DB] cursor-not-allowed'
                    : 'text-black'
            }
            hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
              onClick={() => {
                if (!isDisabled) {
                  if (
                    !selectedRange?.[0] ||
                    (selectedRange?.[0] && selectedRange?.[1])
                  ) {
                    setSelectedRange([date, null])
                  } else if (date.valueOf() === selectedRange?.[0].valueOf()) {
                    // Deselect if clicking the same date
                    setSelectedRange([null, null])
                  } else {
                    const newRange = [selectedRange?.[0], date].sort(
                      (a, b) => a.valueOf() - b.valueOf()
                    ) as [Dayjs, Dayjs]

                    // Update range to full months
                    setSelectedRange([
                      newRange[0].startOf('month'),
                      newRange[1].endOf('month'),
                    ])
                  }
                }
              }}
              disabled={!!isDisabled}
            >
              {month}
            </button>
          )
        })}
      </div>
    )

    return (
      <div className="flex ">
        {renderYear(currentYear, true)}
        {renderYear(nextYear, false)}
      </div>
    )
  }

  const navigateYear = (direction: 'prev' | 'next') => {
    setLocalViewDate(
      dayjs()
        .utc()
        .set('year', viewDate.year() + (direction === 'next' ? 1 : -1))
        .month(viewDate.month())
        .startOf('month')
    )
  }

  return (
    <div className="font-open-sans">
      {showLabel && (
        <span
          className="color-ui-dark-gray bg-ui-pale-blue mt-18 ml-10 text-[13px] font-semibold p-2 inline-flex uppercase rounded-[5px]"
          onClick={toggleIsOpen}
        >
          {formatHeaderDate()}
          {!isOpen ? (
            <ChevronDown className="h-5 w-5 ml-2" />
          ) : (
            <ChevronUp className="h-5 w-5 ml-2" />
          )}
        </span>
      )}
      {isOpen && (
        <div className="relative z-10 w-auto">
          <div className="p-[10px]  bg-ui-pale-blue shadow-lg w-fit rounded-lg date-picker-popup shadow-[0px_0px_4px_0px_#00000040]">
            <div className="pt-4 bg-white">
              <div
                className={`flex justify-between items-center  ${mode === 'Yearly' ? 'ml-1 mr-1 pr-2 pl-2' : 'ml-8 mr-8'} `}
              >
                <div className="flex items-center text-ui-dark-gray font-bold">
                  <button
                    onClick={() => navigateYear('prev')}
                    className="focus:outline-none"
                  >
                    <ChevronLeft className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                  </button>
                  <span className="text-ui-dark-gray font-semibold ml-6">
                    {viewDate.year()}
                  </span>
                </div>
                <div className="flex items-center">
                  <span className="text-ui-dark-gray font-semibold mr-6">
                    {viewDate.year() + 1}
                  </span>
                  <button
                    onClick={() => navigateYear('next')}
                    className="focus:outline-none"
                  >
                    <ChevronRight className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                  </button>
                </div>
              </div>
            </div>

            {/* {mode === 'Day' && renderDayPicker()} */}
            {mode === 'Monthly' && renderMonthPicker()}
            {mode === 'Yearly' && renderYearPicker()}
            {/* {mode === 'Range' && renderRangePicker()} */}
            {mode === 'Quarterly' && renderQuarterlyPicker()}
          </div>
        </div>
      )}
    </div>
  )
}
