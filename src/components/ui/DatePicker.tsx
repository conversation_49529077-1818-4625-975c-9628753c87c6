'use client'
import React from 'react'
import { DateRangePicker } from 'rsuite'
//use the global stylesheet below of other styles fail
//import 'rsuite/dist/rsuite-no-reset.min.css'
import 'rsuite/DateRangePicker/styles/index.css'
import dayjs from 'dayjs'

const DatePicker = () => {
  return (
    <div>
      <DateRangePicker
        appearance="default"
        character=" - "
        style={{ width: 300 }}
        className="pl-10 top-0 absolute "
        showHeader={false}
        placeholder="Select Date"
        renderValue={([start, end]) => {
          return (
            dayjs(start).format('MMM YYYY') +
            ' - ' +
            dayjs(end).format('MMM YYYY')
          )
        }}
      />
    </div>
  )
}

export default DatePicker
