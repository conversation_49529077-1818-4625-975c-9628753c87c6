'use client'

import React, { useEffect, useRef, useState } from 'react'
import {
  ChevronDown,
  ChevronUp,
  Download,
  Loader2,
  Minus,
  Plus,
} from 'lucide-react'
import { useActionsStore } from '@/stores/actions'
import { useViewStore } from '@/stores/viewStore'
import { useDateStore } from '@/stores/dates'
import useStore from '@/stores/useStore'
import { useFilterStore } from '@/stores/filter'
import { useUserSessionStore } from '@/stores/userSession'
import { api } from '@/trpc/react'
import { useClickOutside } from '@/hooks/useClickOutside'
import { cn } from '@/lib/utils'
import { usePathname } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'
import { ChipSection } from '../filter/sections/chips/chipSection'
import { User } from '@/types/user'
import checked from '../../../public/images/checked-radio.svg'
import Image from 'next/image'
import PickerButton from './PickerButton'
import { ScorecardView } from '@/enums/scorecardView'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import dayjs from 'dayjs'
import { ViewSettingsModel } from '@/types/savedViewModel'
import { useUIStore } from '@/stores/ui'
import appInsights from '@/lib/applicationInsights'

export const ActionsMenu = () => {
  const { toast } = useToast()
  const pathname = usePathname()
  const ref = useRef<HTMLDivElement>(null)
  const [isExpanded, setIsExpanded] = useState(false)

  useClickOutside(ref, () => setIsExpanded(false))

  const [expandedAction, setExpandingAction] = useState('')
  const [isUsersExpanded, setIsUsersExpanded] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<
    { id: string; label: string }[]
  >([])
  const [exportOption, setExportOption] = useState('true')
  const [userSearchTerm, setUserSearchTerm] = useState('')

  const { scorecardViewType } = useUIStore()
  const handleExportOptionChange = (event: any) => {
    setExportOption(event.target.value)
  }
  const {
    options,
    selectedOptions,
    toggleOption,
    selectAllOptions,
    SetShowProgress,
    deselectAllOptions,
  } = useActionsStore()

  const { tableState, currentView, setCurrentView } = useViewStore()
  const { hideEmptyIndicators, appliedFilters } = useFilterStore()
  const dateStore = useStore(useDateStore, (state) => state)
  const { primaryMeasureType, organizationId, organizationName } =
    useUserSessionStore()

  useClickOutside(ref, () => setIsExpanded(false))

  const users = api.users.getOrganizationUsers.useQuery()

  const [viewName, setViewName] = useState('')
  const [triggerSave, setTriggerSave] = useState(false)

  const trpcUtils = api.useUtils()

  const handleSelectAll = () => {
    if (selectedOptions.includes('Select All')) {
      deselectAllOptions()
    } else {
      selectAllOptions()
    }
  }

  const settings: ViewSettingsModel = {
    columnFilters: tableState?.columnFilters,
    columnOrder: tableState?.columnOrder,
    pageSize: tableState?.pagination.pageSize,
    visibleColumns: selectedOptions,
    version: 'nextGen',
    measureType: primaryMeasureType,
    hideEmptyIndicators: hideEmptyIndicators,
    measures: appliedFilters.measures,
    hospitals: appliedFilters.subOrganizations,
    submissionGroups: appliedFilters.submissionGroups,
    providers: appliedFilters.providers,
    groups: [],
    facilities: [],
  }

  const saveViewMutation = api.savedViews.saveView.useMutation({})
  const deleteViewMutation = api.savedViews.deleteView.useMutation({})
  const shareViewMutation = api.savedViews.shareView.useMutation({})
  const markAsDefaultMutation = api.savedViews.markAsDefault.useMutation({})
  const exportMutation = api.measures.getExportResults.useMutation({})

  const saveView = (isCurrentView: boolean) => {
    if (!viewName) {
      toast({
        title: 'Please enter a view name',
        variant: 'destructive',
      })
      return
    }
    SetShowProgress(true)
    saveViewMutation.mutate(
      {
        viewName: viewName,
        startDate: dayjs(dateStore?.selectedRange[0]).utc().toISOString()!,
        endDate: dayjs(dateStore?.selectedRange[1]).utc().toISOString()!,
        periodType: dateStore?.selectedPeriod!,
        settings: JSON.stringify(settings),
        page: pathname.replace('/', ''),
        isCurrentView: isCurrentView,
      },
      {
        onSuccess: (data) => {
          SetShowProgress(false)
          setViewName('')

          trpcUtils.savedViews.getSavedViews.invalidate()

          if (data.success) {
            setCurrentView(data.view)
          }

          toast({
            className: cn(
              'text-white',
              data.success
                ? 'bg-green-600  border-green-600'
                : 'bg-red-600  border-red-600'
            ),
            title: data.message,
            variant: 'default',
          })
        },
      }
    )
  }

  const handleDeleteView = () => {
    SetShowProgress(true)
    setIsExpanded(false)
    setExpandingAction('')
    deleteViewMutation.mutateAsync(
      {
        page: pathname.replace('/', ''),
        viewId: currentView?.id!,
      },
      {
        onSuccess: (data) => {
          SetShowProgress(false)

          setCurrentView(undefined)
          trpcUtils.savedViews.getSavedViews.invalidate()
          toast({
            className: cn('bg-green-600 text-white border-green-600'),
            title: data.message,
            variant: 'default',
          })
        },
        onError: (error) => {
          appInsights.trackException({
            exception: new Error(error.message),
            properties: {
              viewId: currentView?.id,
            },
          })

          SetShowProgress(false)
          toast({
            className: cn('bg-red-600 text-white border-red-600'),
            title: 'Error deleting view',
            variant: 'default',
          })
        },
      }
    )
  }

  const handleShareView = () => {
    if (selectedUsers.length == 0) {
      toast({
        title: 'Select at least 1 user',
        variant: 'default',
      })
      return
    }

    SetShowProgress(true)
    setIsExpanded(false)
    setExpandingAction('')
    shareViewMutation.mutate(
      {
        userIds: selectedUsers.map((x) => x.id),
        viewId: currentView?.id!,
      },
      {
        onSuccess: (data) => {
          SetShowProgress(false)

          setSelectedUsers([])
          toast({
            className: cn('bg-green-600 text-white border-green-600'),
            title: data.message,
            variant: 'default',
          })
        },
        onError: (error) => {
          appInsights.trackException({
            exception: new Error(error.message),
            properties: {
              viewId: currentView?.id,
              userIds: selectedUsers.map((x) => x.id),
            },
          })

          SetShowProgress(false)
          toast({
            className: cn('bg-red-600 text-white border-red-600'),
            title: 'Error sharing view',
            variant: 'default',
          })
        },
      }
    )
  }

  useEffect(() => {
    if (triggerSave && viewName) {
      saveView(true)
      setTriggerSave(false) // Reset the flag after saving
    }
  }, [viewName, triggerSave])

  const handleSaveCurrentView = async () => {
    setViewName(currentView?.viewName!)
    setTriggerSave(true) // This ensures saveView only runs when triggered from "Save Current View"
  }

  const removeUser = (Id: string) => {
    setSelectedUsers(selectedUsers.filter((x) => x.id != Id))
  }

  const handleUserSelect = (user: User) => {
    if (selectedUsers.find((e) => e.id === user.userId)) {
      setIsUsersExpanded(false)
      return
    }

    setSelectedUsers([
      ...selectedUsers,
      { id: user.userId!, label: user.displayName! },
    ])
    setIsUsersExpanded(false)
  }

  const handleMarkAsDefaultView = () => {
    SetShowProgress(true)

    markAsDefaultMutation.mutate(
      {
        viewId: currentView?.id!,
        page: pathname.replace('/', ''),
        measureType: primaryMeasureType,
      },
      {
        onSuccess: (data) => {
          SetShowProgress(false)

          if (!data.success) {
            toast({
              className: cn('bg-red-600 text-white border-red-600'),
              title: data.message,
              variant: 'default',
            })
            return
          }

          // // update all views to reflect the new default status
          // const updatedviews = views?.map(view => ({
          //   ...view,
          //   isdefault: view.id === currentview?.id
          // }));
          // setviews(updatedviews);

          // Update current view
          if (currentView) {
            setCurrentView({
              ...currentView,
              isDefault: true,
            })
          }

          // Invalidate queries to refresh data from server
          trpcUtils.savedViews.getSavedViews.invalidate()

          toast({
            className: cn('bg-green-600 text-white border-green-600'),
            title: data.message,
            variant: 'default',
          })
        },
        onError: (error) => {
          appInsights.trackException({
            exception: new Error(error.message),
            properties: {
              viewId: currentView?.id,
            },
          })

          SetShowProgress(false)
          toast({
            className: cn('bg-red-600 text-white border-red-600'),
            title: 'Error marking view as default',
            variant: 'default',
          })
        },
      }
    )
  }

  const handleExport = () => {
    exportMutation.mutate(
      {
        startDate: dayjs(dateStore?.selectedRange[0])?.utc().toISOString()!,
        endDate: dayjs(dateStore?.selectedRange[1])?.utc().toISOString()!,
        scorecardView: Object.entries(ScorecardView).find(
          (entry) => entry[0] === dateStore?.selectedPeriod
        )?.[1]!,
        includeLevel4:
          primaryMeasureType ==
            PrimaryMeasureTypeConstants.AmbulatoryMeasures &&
          exportOption == 'true'
            ? true
            : false,
        measureIdentifiers: appliedFilters.measures,
        primaryMeasureType: primaryMeasureType,
      },
      {
        onSuccess: (response: any) => {
          const result = []

          if (response) {
            const keys = Object.keys(response[0])
            result.push(keys.join(','))
            response.forEach((obj: any) => {
              const row = keys.map(function (key) {
                return obj[key] == null
                  ? '-'
                  : obj[key].toString().replace(',', '')
              })
              result.push(row.join(','))
            })
          }
          const blob = new Blob([result.join('\n')], { type: 'text/csv' })
          const link = document.createElement('a')
          link.href = URL.createObjectURL(blob)
          link.download = `${organizationName ?? organizationId}.csv`
          link.click()
        },
      }
    )
  }
  return (
    <div ref={ref} className="relative">
      <PickerButton text="ACTIONS">
        <div className="p-4">
          <div
            className={cn(
              'mb-2',
              pathname.includes('scorecards') &&
                scorecardViewType === 'Hospital' &&
                'hidden'
            )}
          >
            <div
              className={cn(
                expandedAction == 'Column' ? 'bg-white' : '',
                'border-ui-dark-gray border-[0.5px] rounded px-2 py-2 hover:bg-white'
              )}
            >
              <button
                onClick={() =>
                  setExpandingAction(expandedAction == 'Column' ? '' : 'Column')
                }
                className="w-full flex justify-between items-center text-[#1B4A70] font-semibold text-[12px] outline-none"
              >
                Columns
                {expandedAction == 'Column' ? (
                  <Minus className="w-5 h-5" />
                ) : (
                  <Plus className="w-5 h-5" />
                )}
              </button>
              {expandedAction == 'Column' && (
                <div className="space-y-2 h-[240px] overflow-x-clip overflow-y-auto scrollbar-thin scrollbar-thumb-rounded-full scrollbar-thumb-ui-dark-gray scrollbar-track-[#F5F7FF] mt-2">
                  {options.map((option) => (
                    <label
                      key={option}
                      className="flex items-center space-x-2 text-black-400 text-[12px] font-open-sans font-normal leading-[16.34px]"
                    >
                      <input
                        type="checkbox"
                        role="checkbox"
                        checked={selectedOptions.includes(option)}
                        onChange={() => {
                          if (option === 'Select All') {
                            handleSelectAll()
                          } else {
                            toggleOption(option)
                          }
                        }}
                        className=" w-[13px] h-[16.34px] mr-2 accent-[#1B4A70]"
                      />
                      <span className="text-gray-700">{option}</span>
                    </label>
                  ))}
                </div>
              )}
            </div>
          </div>
          <div className="mb-2 font-sans">
            <div
              className={cn(
                expandedAction == 'SaveAs' ? 'bg-white' : '',
                'border-ui-dark-gray border-[0.5px] rounded px-2 py-2 outline-none hover:bg-white'
              )}
            >
              <button
                onClick={() =>
                  setExpandingAction(expandedAction == 'SaveAs' ? '' : 'SaveAs')
                }
                className="w-full flex justify-between items-center text-[#1B4A70] font-semibold text-[12px]"
              >
                Save As
                {expandedAction == 'SaveAs' ? (
                  <Minus className="w-5 h-5" />
                ) : (
                  <Plus className="w-5 h-5" />
                )}
              </button>
              {expandedAction == 'SaveAs' && (
                <div className="flex items-center rounded p-1 mt-2">
                  <input
                    disabled={saveViewMutation.isPending}
                    type="text"
                    placeholder="Name"
                    value={viewName}
                    onChange={(e) => setViewName(e.target.value)}
                    className="border-[0.5px] w-[202px] p-1 text-[12px] outline-none"
                  />
                  <button
                    disabled={saveViewMutation.isPending}
                    className="bg-[#1B4A70] text-[10px] text-white px-[12px] py-[6.2px]"
                    onClick={() => saveView(false)}
                  >
                    Save
                  </button>
                </div>
              )}
            </div>
          </div>
          {currentView?.id !== '0' && (
            <button
              className="w-full text-[#1B4A70] font-semibold border-ui-dark-gray border-[0.5px] rounded p-2 mb-2 flex justify-between items-center text-start text-[12px] outline-none hover:bg-white"
              onClick={() => handleSaveCurrentView()}
            >
              Save
            </button>
          )}
          <button
            className="w-full text-[#1B4A70] font-semibold border-ui-dark-gray border-[0.5px] rounded p-2 mb-2 flex justify-between items-center text-start text-[12px] hover:bg-white"
            onClick={handleMarkAsDefaultView}
            disabled={markAsDefaultMutation.isPending}
          >
            Mark View As Default
            {(currentView?.isDefault || markAsDefaultMutation.isSuccess) && (
              <Image src={checked} alt="check" width={14} height={14} />
            )}
          </button>
          {currentView?.id !== '0' && (
            <div className="mb-2 font-sans">
              <div
                className={cn(
                  expandedAction == 'Share' ? 'bg-white' : '',
                  'border-ui-dark-gray border-[0.5px] rounded p-2 outline-none hover:bg-white'
                )}
              >
                <button
                  className="w-full text-[#1B4A70] font-semibold flex justify-between items-center text-[12px]"
                  onClick={() =>
                    setExpandingAction(expandedAction == 'Share' ? '' : 'Share')
                  }
                >
                  Share
                  {expandedAction == 'Share' ? (
                    <Minus className="w-5 h-5" />
                  ) : (
                    <Plus className="w-5 h-5" />
                  )}
                </button>
                {expandedAction == 'Share' && (
                  <div className="flex flex-col pt-2">
                    <span className="px-1 mb-2 font-open-sans font-weight-400 text-[10px]">
                      User*
                    </span>
                    <div className="w-full">
                      <div
                        onClick={() => setIsUsersExpanded(!isUsersExpanded)}
                        className="w-full border-[0.5px] border-[solid] border-[#97A4BA] rounded-[4px] flex justify-between items-center px-2 py-1 text-ui-dark-gray"
                      >
                        <input
                          className="flex font-open-sans font-weight-400 text-[10px] text-[#787878] bg-transparent outline-none w-full"
                          placeholder="Select User"
                          value={userSearchTerm}
                          onChange={(e) => setUserSearchTerm(e.target.value)}
                          onClick={(e) => {
                            e.stopPropagation()
                            setIsUsersExpanded(true)
                          }}
                        />
                        {isUsersExpanded ? (
                          <ChevronUp
                            className="w-5 h-5 font-semibold"
                            strokeWidth={2}
                          />
                        ) : (
                          <ChevronDown
                            className="w-5 h-5 font-semibold"
                            strokeWidth={2}
                          />
                        )}
                      </div>
                    </div>
                    {isUsersExpanded && (
                      <div className="w-full flex flex-col px-2 pt-2 h-[150px] overflow-y-auto">
                        {users.data
                          ?.filter((user) =>
                            user.displayName
                              ?.toLowerCase()
                              .includes(userSearchTerm.toLowerCase())
                          )
                          ?.map((user: User) => (
                            <span
                              onClick={() => {
                                handleUserSelect(user)
                                setUserSearchTerm('')
                              }}
                              key={user.userId}
                              className="font-weight-400 text-[10px] py-1 hover:cursor-pointer"
                            >
                              {user.displayName}
                            </span>
                          ))}
                      </div>
                    )}
                    <span className="px-1 mb-2 mt-2 font-open-sans font-weight-400 text-[10px]">
                      Message*
                    </span>
                    <textarea className="w-full p-2 h-[50px] font-weight-400 text-[10px] border-[0.5px] border-[solid] border-[#97A4BA] rounded-[4px] outline-none"></textarea>
                    <button
                      disabled={shareViewMutation.isPending}
                      className="w-[77px] font-semibold text-[10px] text-ui-dark-gray py-[4px] mt-4 border-[0.8px] border-[solid] border-ui-dark-gray rounded-[4px]"
                      onClick={() => {
                        handleShareView()
                      }}
                    >
                      {shareViewMutation.isPending ? (
                        <Loader2 size={13} className="animate-spin" />
                      ) : (
                        'SEND'
                      )}
                    </button>
                    <span className="px-1 mt-2 font-open-sans font-weight-400 text-[10px] text-[#13497C]">
                      Sharing With:
                    </span>
                    <div className="w-full mb-2">
                      <ChipSection
                        title=""
                        allSelected={false}
                        items={selectedUsers}
                        onRemove={removeUser}
                        noSelectionMessage="No Users Selected"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          {currentView?.id !== '0' && (
            <div className="mb-2 font-sans">
              <div
                className={cn(
                  expandedAction == 'Delete' ? 'bg-white' : '',
                  'border-ui-dark-gray border-[0.5px] rounded p-2 hover:bg-white'
                )}
              >
                <button
                  className="w-full text-[#1B4A70] font-semibold text-start text-[12px] outline-none"
                  onClick={() =>
                    setExpandingAction(
                      expandedAction == 'Delete' ? '' : 'Delete'
                    )
                  }
                >
                  Delete View
                </button>
                {expandedAction == 'Delete' && (
                  <div className="flex flex-col rounded p-1">
                    <p className="py-[10px] font-weight-400 text-[10px]">
                      {currentView?.isDefault &&
                        'This is your default view. If you delete this view, the original settings will be set as default view.'}
                      {currentView?.isDefault && <br />}
                      Are you sure you want to continue?
                    </p>
                    <div className="flex justify-start items-center">
                      <button
                        disabled={deleteViewMutation.isPending}
                        className="font-semibold text-[10px] text-ui-dark-gray py-1 px-7 border-[0.8px] border-[solid] border-ui-dark-gray rounded-[4px] outline-none"
                        onClick={() => {
                          handleDeleteView()
                        }}
                      >
                        {deleteViewMutation.isPending ? (
                          <Loader2 className="animate-spin" />
                        ) : (
                          'DELETE VIEW'
                        )}
                      </button>
                      <button
                        className="ml-4 text-ui-dark-gray text-[10px] font-semibold underline"
                        onClick={() => setExpandingAction('')}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          {primaryMeasureType ==
            PrimaryMeasureTypeConstants.HospitalMeasures && (
            <button
              className="w-full text-[#1B4A70] font-semibold border-ui-dark-gray border-[0.5px] rounded p-2 flex justify-between items-center text-start text-[12px] outline-none hover:bg-white"
              onClick={handleExport}
            >
              Export To Excel
              {exportMutation.isPending ? (
                <Loader2 className="animate-spin" />
              ) : (
                <Download className="w-5 h-5" />
              )}
            </button>
          )}
          {primaryMeasureType ==
            PrimaryMeasureTypeConstants.AmbulatoryMeasures && (
            <div className="mb-2 font-sans">
              <div
                className={cn(
                  expandedAction == 'Export' ? 'bg-white' : '',
                  'border-ui-dark-gray border-[0.5px] rounded px-2 py-2 hover:bg-white'
                )}
              >
                <button
                  className="w-full text-[#1B4A70] font-semibold flex justify-between items-center text-start text-[12px]"
                  onClick={() =>
                    setExpandingAction(
                      expandedAction == 'Export' ? '' : 'Export'
                    )
                  }
                >
                  Export To Excel
                  <Download className="w-5 h-5" />
                </button>

                {expandedAction == 'Export' && (
                  <div className="flex flex-col items-start rounded p-1 mt-2">
                    <div className="flex text-[12px] font-normal mb-2">
                      <input
                        type="radio"
                        id="exportOption1"
                        name="export-options"
                        value="true"
                        checked={exportOption === 'true'}
                        onChange={handleExportOptionChange}
                      />
                      <label className="ml-2" htmlFor="exportOption1">
                        Include all providers
                      </label>
                    </div>
                    <div className="flex text-[12px] font-normal mb-2">
                      <input
                        type="radio"
                        id="exportOption2"
                        name="export-options"
                        value="false"
                        checked={exportOption === 'false'}
                        onChange={handleExportOptionChange}
                      />
                      <label className="ml-2" htmlFor="exportOption2">
                        Do not include all providers
                      </label>
                    </div>
                    <button
                      className="flex items-center border-[1px] border-solid boder-ui-dark-gray rounded-sm bg-[#fff] text-[12px] font-semibold py-1 px-6 mt-2 text-ui-dark-gray hover:text-[#fff] hover:bg-ui-dark-gray"
                      onClick={handleExport}
                    >
                      EXPORT
                      {exportMutation.isPending && (
                        <Loader2 className="animate-spin ml-1" />
                      )}
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </PickerButton>
    </div>
  )
}

export default ActionsMenu
