'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import InputWithButton from './inputWithButton'
import { useEffect, useState } from 'react'
import { toast } from '@/hooks/use-toast'

type Props = {
  useFullWidth?: boolean
}

const EncountersSearch = ({ useFullWidth }: Props) => {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    if (
      searchParams.get('search') &&
      searchParams.get('type') === 'encounters'
    ) {
      setSearchTerm(searchParams.get('search')!)
    }
  }, [searchParams.get('search'), searchParams.get('type')])

  return (
    <InputWithButton
      className={useFullWidth ? 'w-full' : 'max-w-[500px]'}
      query={searchTerm}
      setQuery={setSearchTerm}
      onSearch={() => {
        if (!searchTerm) {
          toast({
            variant: 'destructive',
            title: 'Please enter valid search text to proceed.',
          })
          return
        }

        const params = new URLSearchParams(searchParams)
        params.set('search', searchTerm)
        params.set('type', 'encounters')

        router.push(`/explorer/encounters/results?${params.toString()}`)
      }}
    />
  )
}

export default EncountersSearch
