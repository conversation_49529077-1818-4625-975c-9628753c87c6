'use client'

import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { IoMdSkipForward } from 'react-icons/io'
import { IoCaretForward } from 'react-icons/io5'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'
import { cn } from '@/lib/utils'
import { useSearchParams } from 'next/navigation'
import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'
import React from 'react'
import { api } from '@/trpc/react'
import { PatientExplorerResult } from '@/types/patientExplorerResult'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { EncounterSearchResult } from '@/types/encounterSearchResult'
import { customStringFilter } from '@/lib/customStringFilter'
import Link from 'next/link'
import Loader from '../ui/Loader'

dayjs.extend(utc)

type Props = {
  searchType: 'patients' | 'encounters'
}

const ResultsTable = ({ searchType }: Props) => {
  const searchParams = useSearchParams()
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])

  const query = searchParams.get('search') ?? ''

  const patientsSearch = api.patients.search.useQuery(
    {
      query,
    },
    {
      enabled: !!query && searchType === 'patients',
    }
  )

  const encountersSearch = api.encounters.search.useQuery(
    {
      query,
    },
    {
      enabled: !!query && searchType === 'encounters',
    }
  )

  const formatDate = (value: unknown) =>
    value ? dayjs.utc(value as string).format('MM/DD/YYYY') : '-'

  const filterFunction = customStringFilter<
    PatientExplorerResult | EncounterSearchResult
  >()

  const generateColumns = (): ColumnDef<
    PatientExplorerResult | EncounterSearchResult
  >[] => {
    const commonColumns: ColumnDef<
      PatientExplorerResult | EncounterSearchResult
    >[] = [
      {
        accessorKey: 'firstName',
        header: 'First Name',
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        cell: ({ getValue }) => getValue(),
      },
      {
        accessorKey: 'lastName',
        header: 'Last Name',
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        cell: ({ getValue }) => getValue(),
      },
      {
        accessorKey: 'gender',
        header: searchType === 'patients' ? 'Gender' : 'Patient Gender',
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        cell: ({ getValue }) => getValue(),
      },
    ]

    if (searchType === 'patients') {
      return [
        {
          accessorKey: 'displayPatientId',
          header: 'Patient Identifier',
          filterFn: filterFunction,
          sortingFn: 'alphanumeric',
          cell: ({ row, getValue }) => (
            <Link
              className="text-blue-3 underline"
              href={`/explorer/explorer-details?pid=${row.original.patientId}&type=${searchType}`}
            >
              {getValue() as string}
            </Link>
          ),
        },
        ...commonColumns,
        {
          accessorKey: 'dob',
          header: 'Date of Birth',
          enableColumnFilter: true,
          filterFn: filterFunction,
          sortingFn: 'alphanumeric',
          cell: ({ getValue }) => formatDate(getValue()),
          meta: {
            filterPosition: 'right', // This will be used by DataTableColumnFilter
          },
        },
      ]
    }

    return [
      {
        accessorKey: 'accountNumber',
        header: 'Case Identifier',
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        cell: ({ row, getValue }) => (
          <Link
            className="text-blue-3 underline"
            href={`/explorer/explorer-details?pid=${row.original.patientId}&eid=${(row.original as EncounterSearchResult).encounterId}&type=${searchType}`}
          >
            {getValue() as string}
          </Link>
        ),
      },
      ...commonColumns,
      {
        accessorKey: 'encounterType',
        header: 'Encounter Type',
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        cell: ({ getValue }) => getValue(),
      },
      {
        accessorKey: 'startDate',
        header: 'Start Date',
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        cell: ({ getValue }) => formatDate(getValue()),
      },
      {
        accessorKey: 'dischargeDate',
        header: 'Discharge Date',
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        cell: ({ getValue }) => formatDate(getValue()),
        meta: {
          filterPosition: 'right', // This will be used by DataTableColumnFilter
        },
      },
    ]
  }

  const data = useMemo(() => {
    if (searchType === 'patients' && !patientsSearch.isPending) {
      return patientsSearch.data && patientsSearch.data.length > 0
        ? patientsSearch.data
        : []
    }

    if (searchType === 'encounters' && !encountersSearch.isPending) {
      return encountersSearch.data && encountersSearch.data.length > 0
        ? encountersSearch.data
        : []
    }

    return []
  }, [
    patientsSearch.data,
    encountersSearch.data,
    patientsSearch.isPending,
    encountersSearch.isPending,
    searchType,
  ])

  const columns = useMemo(() => generateColumns(), [])

  const table = useReactTable({
    data,
    columns,
    state: {
      columnFilters,
    },
    onColumnFiltersChange: setColumnFilters,
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getCoreRowModel: getCoreRowModel(),
    // onStateChange: () => setTableState(table.getState()),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
  } as React.CSSProperties

  if (
    (searchType === 'patients' && patientsSearch.isPending) ||
    (searchType === 'encounters' && encountersSearch.isPending)
  ) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <Loader />
      </div>
    )
  }

  return (
    <div className="flex flex-col w-full font-open-sans text-sm">
      <div
        className={cn(
          'max-h-[500px]',
          'overflow-y-auto',
          'border-x-[#DDDCDF]',
          'border-x-[1px]',
          'border-t-[#DDDCDF]',
          'border-t-[1px]',
          'mac-scrollbar'
        )}
      >
        {/* Grid */}
        <table className="w-full border-collapse">
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{
                      ...headerStyle,
                      ...{
                        minWidth: `${header.getSize()}px`,
                      },
                      position: 'sticky',
                      top: 0,
                      zIndex: 10,
                    }}
                    className={cn('border-b', 'border-l')}
                  >
                    {header.isPlaceholder ? null : (
                      <div className="flex justify-center items-center">
                        <span
                          className="flex m-3 w-full text-center justify-center cursor-pointer"
                          onClick={
                            !header.id?.toString().startsWith('Q')
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {/* {!header.id?.toString().startsWith('Q') &&
                            getSortingIcon(
                              header.column.getIsSorted() as boolean,
                              header.column.getIsSorted() === 'desc'
                            )} */}
                        </span>
                        {header.column.getCanFilter() && (
                          <div className="flex">
                            <DataTableColumnFilter
                              column={header.column}
                              setColumnFilters={setColumnFilters}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <React.Fragment key={row.id}>
                <tr
                  key={row.id}
                  className={`${
                    row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                  } relative border-b border-[#DDDCDF]`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      style={{
                        fontSize: '14px',
                        padding: '10px 24px',
                        width: `${cell.column.getSize()}px`,
                      }}
                      className={cn(
                        `p-2 align-middle border-r border-[#DDDCDF] text-center`,
                        row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                      )}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
      <div className="px-4 border-t border-[#DDDCDF] flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <IoMdSkipForward size={16} className="rotate-180" />
            </button>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <IoCaretForward size={16} className="rotate-180" />
            </button>
            <ul
              className="flex"
              style={{
                paddingInlineStart: '40px',
                marginBlockStart: '1em',
                marginBlockEnd: '1em',
                marginInlineStart: '0px',
                marginInlineEnd: '0px',
                lineHeight: '2',
                position: 'relative',
                alignItems: 'center',
                padding: '6px 6px',
                alignSelf: 'stretch',
                alignContent: 'stretch',
              }}
            >
              <GeneratePageNumbers table={table} />
            </ul>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <IoCaretForward size={16} className="" />
            </button>
            <button
              className="px-2 py-1  text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <IoMdSkipForward size={16} className="" />
            </button>

            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value))
              }}
              className="pl-8 p-1 text-[14px] font-open-sans text-black"
            >
              {[10, 25, 50, 100].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
            <span
              className="text-[14px] font-open-sans text-black"
              style={{ padding: '10px 8px', lineHeight: '2' }}
            >
              Items per page
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ResultsTable
