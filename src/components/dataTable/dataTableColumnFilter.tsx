import {
  ColumnMetaType,
  DataTableColumnFilterProps,
} from '@/types/dataTableColumnFilterProps'
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover'
import { Button } from '../ui/button'
import { ListFilter } from 'lucide-react'
import NewMeasureDetailsPatientFilterPopup from '../ui/NewMeasureDetailsPatientFilterPopup'

export const DataTableColumnFilter = <TData, TValue>({
  column,
  table,
}: DataTableColumnFilterProps<TData, TValue>) => {
  const position =
    (column.columnDef.meta as ColumnMetaType)?.filterPosition || 'left'

  // Get current filter value for this column
  const currentFilter = column.getFilterValue() as any;

  const handleFilterChange = (newFilterValues: {
    conditions: Array<{ operator: string; value: string }>
    conjunctions: Array<'and' | 'or'>
  }) => {
    // Check if we have valid conditions
    const hasValidConditions = newFilterValues.conditions.some(condition =>
      condition.value.trim() !== ''
    );

    if (hasValidConditions) {
      // Apply the filter
      column.setFilterValue(newFilterValues);
    } else {
      // Clear the filter
      column.setFilterValue(undefined);
    }
  }

  return (
    <Popover modal>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 w-8 data-[state=open]:bg-[#4c5972] data-[state=open]:rounded-full"
          data-state={column.getIsFiltered() ? "open" : undefined}
        >
          <ListFilter className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        avoidCollisions
        className="border-none shadow-md rounded-sm p-0"
        align={position === 'left' ? 'start' : 'end'}
      >
        <NewMeasureDetailsPatientFilterPopup
          onFilterChange={handleFilterChange}
          initialValues={currentFilter}
        />
      </PopoverContent>
    </Popover>
  )
}
