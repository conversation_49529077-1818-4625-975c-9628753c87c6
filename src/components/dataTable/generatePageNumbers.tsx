import type { Table } from '@tanstack/react-table'

type Props<T> = {
  table: Table<T>
}

export const GeneratePageNumbers = <T,>({ table }: Props<T>) => {
  const pageCount = table.getPageCount()
  const currentPage = table.getState().pagination.pageIndex + 1
  const maxVisiblePages = 10 // Maximum visible page numbers

  const pageNumbers: React.ReactNode[] = []

  // Calculate start and end page numbers
  let startPage =
    Math.floor((currentPage - 1) / maxVisiblePages) * maxVisiblePages + 1
  let endPage = Math.min(pageCount, startPage + maxVisiblePages - 1)

  // Add "..." button for previous pages
  if (startPage > 1) {
    pageNumbers.push(
      <li
        key="previous-pages"
        className="inline-flex align-items-center text-center justify-center p-2 text-black hover:underline"
        onClick={() => table.setPageIndex(startPage - maxVisiblePages - 1)}
      >
        <a href="/#" title="Previous pages" onClick={(e) => e.preventDefault()}>
          ...
        </a>
      </li>
    )
  }

  // Add page numbers
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(
      <li
        key={i}
        style={{
          boxSizing: 'border-box',
          fontSize: '13px',
          height: '18px',
          width: '18px',
          alignItems: 'center',
        }}
        className={`flex align-items-center text-center justify-center  hover:underline p-3 font-open-sans ${
          i === currentPage
            ? 'bg-[#97A4BA] text-black rounded-full'
            : 'text-black'
        } ${typeof i === 'string' ? 'cursor-not-allowed' : ''}`}
        onClick={() => typeof i === 'number' && table.setPageIndex(i - 1)}
      >
        {i}
      </li>
    )
  }

  // Add "..." button for next pages
  if (endPage < pageCount) {
    pageNumbers.push(
      <li
        key="next-pages"
        className="inline-flex align-items-center text-center justify-center p-2 text-black hover:underline"
        onClick={() => table.setPageIndex(startPage + maxVisiblePages - 1)}
      >
        <a href="/#" title="Next pages" onClick={(e) => e.preventDefault()}>
          ...
        </a>
      </li>
    )
  }

  return pageNumbers
}
