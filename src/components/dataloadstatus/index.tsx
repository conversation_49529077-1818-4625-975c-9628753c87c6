'use client'
import {
    ColumnDef,
    ColumnFiltersState,
    flexRender,
    getCoreRowModel,
    getPaginationRowModel,
    useReactTable,
    getFilteredRowModel,
    SortingState,
    getSortedRowModel,
} from '@tanstack/react-table'

import { api } from '@/trpc/react'
import Loader from '@/components/ui/Loader'
import { cn } from '@/lib/utils'

import React, { useMemo, useState, useEffect, useRef } from 'react'

import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'
import { customStringFilter } from '@/lib/customStringFilter'
import { IoMdSkipForward } from 'react-icons/io'
import { IoCaretForward } from 'react-icons/io5'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'


import DownloadIconSvg from '../../../public/images/download.svg'

import { LoadStatus } from '@/types/loadStatus'
import { useContext } from 'react'
import { SingleDateContext } from '@/app/(admin)/singleDateContext'
import { DataLoadStatusContext } from '@/app/(admin)/dataLoadStatusContext'

export const DataLoadStatus = () => {
    const [sorting, setSorting] = useState<SortingState>([])
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [tableState, setTableState] = useState({})
    const { singleStartDate } = useContext(SingleDateContext)
    const { setDataLoadStatus } = useContext(DataLoadStatusContext)




    const { data: result, isLoading, isFetching } = api.admin.getAllDataLoadStatus.useQuery({
        loaddate: singleStartDate?.toLocaleDateString() || ''
    })

    const filterFunction = customStringFilter<LoadStatus>()

    const pinnedColumns: { [key: string]: boolean } = {
        organizationName: true,
        entityOrHospital: true,
    }

    const generateColumns = (
        data: typeof result
    ): ColumnDef<LoadStatus, any>[] => {
        const dynamicColumns: ColumnDef<LoadStatus, any>[] = [
        ]
        dynamicColumns.push({
            accessorKey: 'organizationName' as keyof LoadStatus,
            enablePinning: true,
            filterFn: filterFunction,
            size: 300,
            header: 'Organization Name',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'processingType' as keyof LoadStatus,
            header: 'Processing Type',
            filterFn: filterFunction,
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'hospitalName' as keyof LoadStatus,
            header: 'Hospital Name',
            filterFn: filterFunction,
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'cdmLastLoadDateDisplay' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'CDM Last Load Date',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'mspLastPublishDateDisplay' as keyof LoadStatus,
            header: 'MSP Last Publish Date',
            filterFn: filterFunction,
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'admLoadStartDate' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'Load Start Date',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'admLoadEndDate' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'Load End Date',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'ehCalculationsCompletionDateDisplay' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'ENCOR-e EH Last Updated Date',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'ecCalculationsCompletionDateDisplay' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'ENCOR-e EC Last Updated Date',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'startDateTime' as keyof LoadStatus,
            header: 'Oak Push Start Date',
            filterFn: filterFunction,
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'endDateTime' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'Oak Push End Date',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'duration' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'Duration',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'categoryAssignmentCount' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'Category  Assignment Count',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'status' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'Oak Push  Status',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })
        dynamicColumns.push({
            accessorKey: 'count' as keyof LoadStatus,
            filterFn: filterFunction,
            header: 'No. of Tries',
            enableColumnFilter: true,
            cell: (info: any) => (
                <div className="text-center">{String(info.getValue())}</div>
            ),
        })

        return dynamicColumns
    }
    const columns = useMemo(() => {
        return generateColumns(result)
    }, [result])

    // Add a ref to track previous data
    const prevDataRef = useRef<{ data: any[] | null, columns: any[] | null }>({ data: null, columns: null });

    // Memoize the formatted columns
    const formattedColumns = useMemo(() => {
        if (!columns.length) return [];
        return columns.map(col => ({
            header: typeof col.header === 'string' ? col.header : 'Unknown',
            accessorKey: 'accessorKey' in col ? col.accessorKey : 'unknown'
        }));
    }, [columns]);

    // Emit data for export
    useEffect(() => {
        if (result && result.length > 0 && formattedColumns.length > 0) {
            // Check if data has actually changed
            const prevData = prevDataRef.current.data;
            const prevColumns = prevDataRef.current.columns;

            if (
                prevData !== result ||
                prevColumns !== formattedColumns
            ) {
                console.log("Setting data load status data", {
                    dataLength: result.length,
                    columnsLength: formattedColumns.length
                });

                // Update the ref
                prevDataRef.current = { data: result, columns: formattedColumns };

                // Update parent state
                setDataLoadStatus(result, formattedColumns);
            }
        }
    }, [result, formattedColumns, setDataLoadStatus]);



    const table = useReactTable({
        data: result ?? [],
        columns,
        state: {
            sorting,
            columnPinning: {
                left: ['organizationName'],
            },
            columnFilters,
        },
        onSortingChange: setSorting,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        onColumnFiltersChange: setColumnFilters,
        sortDescFirst: false,
        initialState: {
            pagination: {
                pageSize: 20,
            },
        },
        onStateChange: () => setTableState(table.getState()),
    });

    return (
        <div className="w-full bg-white rounded-lg shadow-sm px-4"> {/* Added px-4 for padding */}
            <div className='p-2'>
                <div className="font-open-sans font-bold text-[28px] text-center text-black">Data Load Status</div>
            </div>
            <div className="pt-0"> {/* Removed p-6 to extend width, kept pt-0 */}
                {isLoading || isFetching ? (
                    <div className="overflow-x-auto relative mt-10">
                        <Loader />
                    </div>
                ) : (
                    <div className="flex flex-col w-full font-open-sans text-sm">
                        <div
                            className={cn(
                                'max-h-[500px]',
                                'overflow-y-auto',
                                'mac-scrollbar'
                            )}
                        >
                            <table
                                className="w-full"
                                style={{
                                    borderCollapse: 'collapse',
                                    border: 'none',
                                }}
                            >
                                <thead>
                                    {table.getHeaderGroups().map((headerGroup) => (
                                        <tr key={headerGroup.id} style={{ border: 'none' }}>
                                            {headerGroup.headers.map((header) => (
                                                <th
                                                    key={header.id}
                                                    colSpan={header.colSpan}
                                                    style={{
                                                        backgroundColor: '#566582',
                                                        verticalAlign: 'middle',
                                                        color: '#F5F7FE',
                                                        height: '0px',
                                                        fontSize: '11px',
                                                        padding: '0px 18px',
                                                        textAlign: 'center',
                                                        fontFamily: 'Open Sans',
                                                        borderTop: 'none',
                                                        minWidth: `${header.getSize()}px`,
                                                        position: 'sticky',
                                                        top: 0,
                                                        zIndex: 10,
                                                        border: 'none',
                                                        ...(pinnedColumns[header.column.id] && {
                                                            left: 0,
                                                            zIndex: 11,
                                                        }),
                                                    }}
                                                >
                                                    {header.isPlaceholder ? null : (
                                                        <div className="flex justify-center items-center">
                                                            <span
                                                                className="flex m-3 w-full text-center justify-center cursor-pointer"
                                                                onClick={
                                                                    !header.id?.toString().startsWith('Q')
                                                                        ? header.column.getToggleSortingHandler()
                                                                        : undefined
                                                                }
                                                            >
                                                                {flexRender(
                                                                    header.column.columnDef.header,
                                                                    header.getContext()
                                                                )}

                                                            </span>
                                                            {header.column.getCanFilter() && (
                                                                <div className="flex">
                                                                    <DataTableColumnFilter
                                                                        column={header.column}
                                                                        setColumnFilters={setColumnFilters}
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                </th>
                                            ))}
                                        </tr>
                                    ))}
                                </thead>
                                <tbody>
                                    {table.getRowModel().rows.length === 0 ? (
                                        <tr style={{ border: 'none' }}>
                                            <td
                                                className="bg-[#e2e4e7] p-[1.5rem] text-left"
                                                colSpan={columns.length || 1}
                                                style={{ border: 'none' }}
                                            >
                                                No data available
                                            </td>
                                        </tr>
                                    ) : (
                                        table.getRowModel().rows.map((row) => (
                                            <tr
                                                key={row.id}
                                                className={`${row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'}`}
                                                style={{ border: 'none' }}
                                            >
                                                {row.getVisibleCells().map((cell) => (
                                                    <td
                                                        key={cell.id}
                                                        className={`p-2 align-middle ${row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'}`}
                                                        style={{
                                                            fontSize: '12px',
                                                            padding: '10px 24px',
                                                            width: `${cell.column.getSize()}px`,
                                                            border: 'none',
                                                            ...(cell.column.getIsPinned() === 'left' && {
                                                                position: 'sticky',
                                                                left: 1,
                                                                zIndex: 1,
                                                            }),
                                                        }}
                                                    >
                                                        {flexRender(
                                                            cell.column.columnDef.cell,
                                                            cell.getContext()
                                                        )}
                                                    </td>
                                                ))}
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>
                        <div className="px-4 flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <div className="flex items-center space-x-1">
                                    <button
                                        className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                                        onClick={() => table.setPageIndex(0)}
                                        disabled={!table.getCanPreviousPage()}
                                    >
                                        <IoMdSkipForward size={16} className="rotate-180" />
                                    </button>
                                    <button
                                        className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                                        onClick={() => table.previousPage()}
                                        disabled={!table.getCanPreviousPage()}
                                    >
                                        <IoCaretForward size={16} className="rotate-180" />
                                    </button>
                                    <ul
                                        className="flex"
                                        style={{
                                            paddingInlineStart: '40px',
                                            marginBlockStart: '1em',
                                            marginBlockEnd: '1em',
                                            marginInlineStart: '0px',
                                            marginInlineEnd: '0px',
                                            lineHeight: '2',
                                            position: 'relative',
                                            alignItems: 'center',
                                            padding: '6px 6px',
                                            alignSelf: 'stretch',
                                            alignContent: 'stretch',
                                        }}
                                    >
                                        <GeneratePageNumbers table={table} />
                                    </ul>
                                    <button
                                        className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                                        onClick={() => table.nextPage()}
                                        disabled={!table.getCanNextPage()}
                                    >
                                        <IoCaretForward size={16} className="" />
                                    </button>
                                    <button
                                        className="px-2 py-1  text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                                        onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                                        disabled={!table.getCanNextPage()}
                                    >
                                        <IoMdSkipForward size={16} className="" />
                                    </button>

                                    <select
                                        value={table.getState().pagination.pageSize}
                                        onChange={(e) => {
                                            table.setPageSize(Number(e.target.value))
                                        }}
                                        className="pl-8 p-1 text-[14px] font-open-sans text-black  focus:outline-none"
                                    >
                                        {[10, 25, 50, 100].map((pageSize) => (
                                            <option key={pageSize} value={pageSize}>
                                                {pageSize}
                                            </option>
                                        ))}
                                    </select>
                                    <span
                                        className="text-[14px] font-open-sans text-black"
                                        style={{ padding: '10px 8px', lineHeight: '2' }}
                                    >
                                        Items per page
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
