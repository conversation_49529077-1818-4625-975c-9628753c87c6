'use client'
import React, { useEffect, useState } from 'react'
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
} from '@tanstack/react-table'
import { useToast } from '@/hooks/use-toast'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { api } from '@/trpc/react'
import Loader from '@/components/ui/Loader'
import { Loader2 } from 'lucide-react'
import BasicDataTable from '@/components/dataTable/basicDataTable'
import { Report } from '@/types/reports/medisolvReport'
import { customStringFilter } from '@/lib/customStringFilter'

export const ManageReports = () => {
  const [reports, setReports] = useState<Report[]>([])

  const { toast } = useToast()
  const reportsQuery = api.admin.getStandardReports.useQuery()
  const reportStatusMutation = api.report.saveReportsStatus.useMutation()

  useEffect(() => {
    if (reportsQuery.data) setReports(reportsQuery.data)
  }, [reportsQuery.data])

  const [loadingStates, setLoadingStates] = useState(new Map<string, boolean>())

  const handleCheckboxChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const id = event.target.value
    const status = event.target.checked
    const reportType = reports.find((x) => x.rowKey === id)?.reportType!

    setLoadingStates((prev) => new Map(prev).set(id, true))

    try {
      await reportStatusMutation.mutateAsync({
        id: id,
        reportType: reportType,
        status: status,
      })
      reportsQuery.refetch()
      toast({
        title: 'Success',
        description: 'Report status updated successfully',
      })
      setLoadingStates((prev) => {
        const newMap = new Map(prev)
        newMap.delete(id)
        return newMap
      })

      let reportToUpdate = reports.find((x) => x.rowKey === id)
      reportToUpdate!.isActive = status

      setReports((prevReports) =>
        prevReports.map((report) =>
          report.rowKey === reportToUpdate?.rowKey
            ? { ...report, ...reportToUpdate }
            : report
        )
      )
    } catch (error) {
      console.error('Error updating report status:', error)
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update report status',
      })
      setLoadingStates((prev) => {
        const newMap = new Map(prev)
        newMap.delete(id)
        return newMap
      })
    }
  }

  const filterFunction = customStringFilter<Report>()

  const columns: ColumnDef<any>[] = React.useMemo(
    () => [
      {
        accessorKey: 'name',
        header: 'Report Name',
        filterFn: filterFunction,
        cell: (info: any) => (
          <div className="text-center">{String(info.getValue())}</div>
        ),
      },
      {
        accessorKey: 'isActive',
        header: 'Is Active',
        enableColumnFilter: false,
        cell: (info: any) => (
          <div className="text-center">
            {loadingStates.get(info.row.original?.rowKey) ? (
              <Loader2 className="ml-2 h-4 w-4 animate-spin inline-block" />
            ) : (
              <input
                type="checkbox"
                checked={info.getValue() === true}
                onChange={handleCheckboxChange}
                value={info.row.original?.rowKey}
                disabled={loadingStates.get(info.row.original?.rowKey) || false}
              />
            )}
          </div>
        ),
      },
    ],
    [loadingStates, reports]
  )

  const table = useReactTable({
    data: reports,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 20,
      },
    },
  })

  return (
    <Card className="border">
      <CardHeader>
        <CardTitle>Manage Reports</CardTitle>
        <CardDescription>Configure status of reports</CardDescription>
      </CardHeader>
      <CardContent>
        {reportsQuery.isPending ? (
          <div className="overflow-x-auto relative mt-10">
            <Loader />
          </div>
        ) : (
          <div>
            <BasicDataTable table={table} />
          </div>
        )}
      </CardContent>
    </Card>
  )
}
