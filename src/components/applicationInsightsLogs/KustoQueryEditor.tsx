'use client'

import React, { useRef, useEffect } from 'react'
import Editor from '@monaco-editor/react'
import * as monaco from 'monaco-editor'

interface KustoQueryEditorProps {
  value: string
  onChange: (value: string) => void
  height?: string
  placeholder?: string
}

// Kusto/KQL language configuration
const kustoLanguageConfig = {
  // Language tokens
  tokenizer: {
    root: [
      // Comments
      [/\/\/.*$/, 'comment'],
      [/\/\*/, 'comment', '@comment'],

      // Strings
      [/"([^"\\]|\\.)*$/, 'string.invalid'],
      [/"/, 'string', '@string'],
      [/'([^'\\]|\\.)*$/, 'string.invalid'],
      [/'/, 'string', '@string_single'],

      // Numbers
      [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
      [/\d+/, 'number'],

      // Operators
      [/[=!<>]=?/, 'operator'],
      [/[+\-*/%]/, 'operator'],
      [/[&|^~]/, 'operator'],

      // Delimiters
      [/[{}()\[\]]/, '@brackets'],
      [/[;,.]/, 'delimiter'],

      // Keywords
      [
        /\b(let|datatable|print|evaluate|invoke|execute|render|sort|order|top|take|limit|skip|count|summarize|extend|project|where|join|union|distinct|sample|reduce|parse|split|extract|bag_unpack|mv-expand|mv-apply|make-series|range|facet|search|find|lookup|as|by|on|kind|hint|materialize|cache|fork|partition|serialize|toscalar|tostring|todatetime|totimespan|todouble|toint|tolong|tobool|isnull|isempty|isnan|isinf|case|iff|coalesce|max|min|sum|avg|count|countif|dcount|dcountif|stdev|stdevp|variance|variancep|percentile|percentiles|arg_max|arg_min|any|take_any|make_list|make_set|make_bag|buildschema|pack|pack_all|pack_array|unpack|bag_keys|bag_remove_keys|treepath|zip|array_length|array_slice|array_split|array_concat|array_reverse|array_sort_asc|array_sort_desc|set_difference|set_intersect|set_union|set_has_element|jaccard_index|binary_and|binary_or|binary_xor|binary_not|binary_shift_left|binary_shift_right|hash|hash_sha256|hash_md5|base64_encode_tostring|base64_decode_tostring|url_encode|url_decode|extract_all|extractjson|parsejson|todynamic|parse_json|parse_xml|parse_csv|parse_ipv4|parse_ipv6|format_datetime|format_timespan|startofday|startofweek|startofmonth|startofyear|endofday|endofweek|endofmonth|endofyear|dayofweek|dayofmonth|dayofyear|weekofyear|monthofyear|getyear|getmonth|hourofday|minuteofhour|secondofminute|millisecond|microsecond|nanosecond|now|ago|datetime_add|datetime_diff|timespan|totimespan|bin|floor|ceiling|round|abs|sign|rand|log|log10|log2|exp|exp10|exp2|pow|sqrt|isfinite|degrees|radians|sin|cos|tan|asin|acos|atan|atan2|sinh|cosh|tanh|gamma|beta|welch_test|series_fir|series_iir|series_fit_line|series_fit_2lines|series_outliers|series_periods_detect|series_periods_validate|series_seasonal|series_decompose|series_decompose_anomalies|series_decompose_forecast|series_fill_backward|series_fill_const|series_fill_forward|series_fill_linear|series_stats|series_stats_dynamic|hourofday|geo_distance_2points|geo_distance_point_to_line|geo_point_in_circle|geo_point_in_polygon|geo_polygon_area|geo_polygon_centroid|geo_line_centroid|geo_point_to_s2cell|geo_s2cell_to_central_point|geo_polygon_to_s2cells|geo_line_buffer|geo_polygon_buffer|strcat|strcat_delim|split|substring|strlen|indexof|indexof_regex|replace|replace_regex|trim|trim_start|trim_end|toupper|tolower|reverse|has|has_cs|!has|!has_cs|hasprefix|hasprefix_cs|!hasprefix|!hasprefix_cs|hassuffix|hassuffix_cs|!hassuffix|!hassuffix_cs|contains|contains_cs|!contains|!contains_cs|startswith|startswith_cs|!startswith|!startswith_cs|endswith|endswith_cs|!endswith|!endswith_cs|matches|matches_regex|in|!in|in~|!in~|between|!between)\b/,
        'keyword',
      ],

      // Table names and identifiers
      [/[a-zA-Z_]\w*/, 'identifier'],
    ],

    comment: [
      [/[^\/*]+/, 'comment'],
      [/\*\//, 'comment', '@pop'],
      [/[\/*]/, 'comment'],
    ],

    string: [
      [/[^\\"]+/, 'string'],
      [/\\./, 'string.escape'],
      [/"/, 'string', '@pop'],
    ],

    string_single: [
      [/[^\\']+/, 'string'],
      [/\\./, 'string.escape'],
      [/'/, 'string', '@pop'],
    ],
  },
}

// Kusto language definition
const kustoLanguage = {
  defaultToken: '',
  tokenPostfix: '.kusto',
  ignoreCase: true,

  brackets: [
    { open: '{', close: '}', token: 'delimiter.curly' },
    { open: '[', close: ']', token: 'delimiter.square' },
    { open: '(', close: ')', token: 'delimiter.parenthesis' },
  ],

  keywords: [
    'let',
    'datatable',
    'print',
    'evaluate',
    'invoke',
    'execute',
    'render',
    'sort',
    'order',
    'top',
    'take',
    'limit',
    'skip',
    'count',
    'summarize',
    'extend',
    'project',
    'where',
    'join',
    'union',
    'distinct',
    'sample',
    'reduce',
    'parse',
    'split',
    'extract',
    'bag_unpack',
    'mv-expand',
    'mv-apply',
    'make-series',
    'range',
    'facet',
    'search',
    'find',
    'lookup',
    'as',
    'by',
    'on',
    'kind',
    'hint',
    'materialize',
    'cache',
    'fork',
    'partition',
    'serialize',
    'toscalar',
    'tostring',
    'todatetime',
    'totimespan',
    'todouble',
    'toint',
    'tolong',
    'tobool',
    'isnull',
    'isempty',
    'isnan',
    'isinf',
    'case',
    'iff',
    'coalesce',
    'max',
    'min',
    'sum',
    'avg',
    'count',
    'countif',
    'dcount',
    'dcountif',
    'stdev',
    'stdevp',
    'variance',
    'variancep',
    'percentile',
    'percentiles',
    'arg_max',
    'arg_min',
    'any',
    'take_any',
    'make_list',
    'make_set',
    'make_bag',
    'buildschema',
    'pack',
    'pack_all',
    'pack_array',
    'unpack',
    'ago',
    'now',
    'datetime',
    'timespan',
    'bin',
    'floor',
    'ceiling',
    'round',
    'abs',
    'sign',
    'rand',
    'log',
    'log10',
    'log2',
    'exp',
    'exp10',
    'exp2',
    'pow',
    'sqrt',
    'isfinite',
    'contains',
    'startswith',
    'endswith',
    'has',
    'hasprefix',
    'hassuffix',
    'matches',
    'in',
    'between',
    'and',
    'or',
    'not',
    'true',
    'false',
    'null',
    'dynamic',
    'string',
    'int',
    'long',
    'real',
    'bool',
    'guid',
    'decimal',
  ],

  operators: [
    '=',
    '!=',
    '<>',
    '<',
    '<=',
    '>',
    '>=',
    '+',
    '-',
    '*',
    '/',
    '%',
    '==',
    '=~',
    '!~',
    'contains',
    'contains_cs',
    '!contains',
    '!contains_cs',
    'startswith',
    'startswith_cs',
    '!startswith',
    '!startswith_cs',
    'endswith',
    'endswith_cs',
    '!endswith',
    '!endswith_cs',
    'has',
    'has_cs',
    '!has',
    '!has_cs',
    'hasprefix',
    'hasprefix_cs',
    '!hasprefix',
    '!hasprefix_cs',
    'hassuffix',
    'hassuffix_cs',
    '!hassuffix',
    '!hassuffix_cs',
    'matches',
    'matches_regex',
    'in',
    '!in',
    'in~',
    '!in~',
    'between',
    '!between',
  ],

  // Common Application Insights tables for autocomplete
  tables: [
    'requests',
    'dependencies',
    'exceptions',
    'traces',
    'pageViews',
    'customEvents',
    'customMetrics',
    'performanceCounters',
    'availabilityResults',
    'browserTimings',
    'customDimensions',
    'customMeasurements',
    'operation_Name',
    'operation_Id',
    'operation_ParentId',
    'user_Id',
    'user_AuthenticatedId',
    'session_Id',
    'client_Type',
    'client_Model',
    'client_OS',
    'client_City',
    'client_StateOrProvince',
    'client_CountryOrRegion',
    'cloud_RoleName',
    'cloud_RoleInstance',
    'appId',
    'appName',
    'iKey',
    'sdkVersion',
    'itemId',
    'itemType',
    'itemCount',
    'timestamp',
    'name',
    'url',
    'duration',
    'resultCode',
    'success',
    'type',
    'target',
    'data',
    'id',
    'message',
    'severityLevel',
    'problemId',
    'handledAt',
    'assembly',
    'method',
    'outerMessage',
    'outerType',
    'outerAssembly',
    'outerMethod',
    'innermostMessage',
    'innermostType',
    'innermostAssembly',
    'innermostMethod',
    'details',
  ],

  ...kustoLanguageConfig,
}

export const KustoQueryEditor: React.FC<KustoQueryEditorProps> = ({
  value,
  onChange,
  height = '200px',
  placeholder = 'Enter your Kusto query here...',
}) => {
  const editorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null)

  const handleEditorDidMount = (
    editor: monaco.editor.IStandaloneCodeEditor,
    monaco: typeof import('monaco-editor')
  ) => {
    editorRef.current = editor

    // Register Kusto language
    monaco.languages.register({ id: 'kusto' })

    // Set language configuration
    monaco.languages.setLanguageConfiguration('kusto', {
      comments: {
        lineComment: '//',
        blockComment: ['/*', '*/'],
      },
      brackets: [
        ['{', '}'],
        ['[', ']'],
        ['(', ')'],
      ],
      autoClosingPairs: [
        { open: '{', close: '}' },
        { open: '[', close: ']' },
        { open: '(', close: ')' },
        { open: '"', close: '"' },
        { open: "'", close: "'" },
      ],
      surroundingPairs: [
        { open: '{', close: '}' },
        { open: '[', close: ']' },
        { open: '(', close: ')' },
        { open: '"', close: '"' },
        { open: "'", close: "'" },
      ],
    })

    // Set tokenizer
    monaco.languages.setMonarchTokensProvider('kusto', kustoLanguage)

    // Register completion provider for autocomplete
    monaco.languages.registerCompletionItemProvider('kusto', {
      provideCompletionItems: (model, position) => {
        const word = model.getWordUntilPosition(position)
        const range = {
          startLineNumber: position.lineNumber,
          endLineNumber: position.lineNumber,
          startColumn: word.startColumn,
          endColumn: word.endColumn,
        }

        const suggestions: monaco.languages.CompletionItem[] = []

        // Add keyword suggestions
        kustoLanguage.keywords.forEach((keyword) => {
          suggestions.push({
            label: keyword,
            kind: monaco.languages.CompletionItemKind.Keyword,
            insertText: keyword,
            range: range,
            documentation: `Kusto keyword: ${keyword}`,
          })
        })

        // Add table suggestions
        kustoLanguage.tables.forEach((table) => {
          suggestions.push({
            label: table,
            kind: monaco.languages.CompletionItemKind.Class,
            insertText: table,
            range: range,
            documentation: `Application Insights table: ${table}`,
          })
        })

        // Add operator suggestions
        kustoLanguage.operators.forEach((operator) => {
          suggestions.push({
            label: operator,
            kind: monaco.languages.CompletionItemKind.Operator,
            insertText: operator,
            range: range,
            documentation: `Kusto operator: ${operator}`,
          })
        })

        // Add common query patterns and snippets
        const queryPatterns = [
          {
            label: 'Basic query template',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'customEvents',
              '| where timestamp > ago(24h)',
              '| project timestamp, name, customDimensions',
              '| order by timestamp desc',
              '| limit 100',
            ].join('\n'),
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Basic query template for custom events',
            range: range,
          },
          {
            label: 'Error analysis template',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'exceptions',
              '| where timestamp > ago(7d)',
              '| summarize count() by type, bin(timestamp, 1h)',
              '| order by timestamp desc',
            ].join('\n'),
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Template for analyzing exceptions over time',
            range: range,
          },
          {
            label: 'Performance analysis template',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'requests',
              '| where timestamp > ago(24h)',
              '| summarize avg(duration), count() by name',
              '| order by avg_duration desc',
            ].join('\n'),
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Template for analyzing request performance',
            range: range,
          },
          {
            label: 'User behavior analysis',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'pageViews',
              '| where timestamp > ago(7d)',
              '| extend userId = tostring(customDimensions.userId)',
              '| summarize pageCount = count(), uniquePages = dcount(name) by userId',
              '| order by pageCount desc',
            ].join('\n'),
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Template for analyzing user behavior patterns',
            range: range,
          },
          {
            label: 'Dependency failure analysis',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'dependencies',
              '| where timestamp > ago(24h) and success == false',
              '| summarize failureCount = count(), avgDuration = avg(duration) by name, resultCode',
              '| order by failureCount desc',
            ].join('\n'),
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Template for analyzing dependency failures',
            range: range,
          },
          {
            label: 'Custom dimensions exploration',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'customEvents',
              '| where timestamp > ago(24h)',
              '| extend userId = tostring(customDimensions.userId)',
              '| extend feature = tostring(customDimensions.feature)',
              '| summarize eventCount = count() by userId, feature',
              '| order by eventCount desc',
            ].join('\n'),
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Template for exploring custom dimensions data',
            range: range,
          },
        ]

        suggestions.push(...queryPatterns)

        return { suggestions }
      },
    })

    // Set theme
    monaco.editor.defineTheme('kusto-theme', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'keyword', foreground: '0000FF', fontStyle: 'bold' },
        { token: 'string', foreground: 'A31515' },
        { token: 'comment', foreground: '008000', fontStyle: 'italic' },
        { token: 'number', foreground: '098658' },
        { token: 'operator', foreground: '000000', fontStyle: 'bold' },
        { token: 'identifier', foreground: '000000' },
      ],
      colors: {
        'editor.background': '#FFFFFF',
        'editor.foreground': '#000000',
        'editorLineNumber.foreground': '#237893',
        'editor.selectionBackground': '#ADD6FF',
        'editor.inactiveSelectionBackground': '#E5EBF1',
      },
    })

    monaco.editor.setTheme('kusto-theme')

    // Add placeholder text
    if (!value && placeholder) {
      editor.setValue(placeholder)
      editor.setSelection(new monaco.Selection(1, 1, 1, placeholder.length))
    }

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      // Trigger a custom event for running the query
      const event = new CustomEvent('runQuery', {
        detail: { query: editor.getValue() },
      })
      window.dispatchEvent(event)
    })

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyK, () => {
      // Trigger command palette
      editor.trigger('keyboard', 'editor.action.quickCommand', {})
    })

    // Focus the editor
    editor.focus()
  }

  const handleEditorChange = (newValue: string | undefined) => {
    if (newValue !== undefined) {
      onChange(newValue)
    }
  }

  return (
    <div className="border border-gray-300 rounded-md overflow-hidden">
      <Editor
        height={height}
        language="kusto"
        value={value}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        options={{
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          fontSize: 14,
          lineNumbers: 'on',
          roundedSelection: false,
          scrollbar: {
            vertical: 'auto',
            horizontal: 'auto',
          },
          automaticLayout: true,
          wordWrap: 'on',
          wrappingIndent: 'indent',
          folding: true,
          foldingStrategy: 'indentation',
          showFoldingControls: 'always',
          matchBrackets: 'always',
          autoIndent: 'full',
          formatOnPaste: true,
          formatOnType: true,
          suggestOnTriggerCharacters: true,
          acceptSuggestionOnEnter: 'on',
          tabCompletion: 'on',
          quickSuggestions: {
            other: true,
            comments: false,
            strings: false,
          },
          parameterHints: {
            enabled: true,
          },
          hover: {
            enabled: true,
          },
        }}
      />
    </div>
  )
}

export default KustoQueryEditor
