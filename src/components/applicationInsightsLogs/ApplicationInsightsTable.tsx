'use client'
import React, { useMemo } from 'react'
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  flexRender,
  getExpandedRowModel,
  ExpandedState,
} from '@tanstack/react-table'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ApplicationInsightsQueryResult } from '@/types/applicationInsights'
import { cn } from '@/lib/utils'
import {
  ChevronLeft,
  ChevronRight,
  Download,
  Table,
  ChevronDown,
  ChevronUp,
} from 'lucide-react'
import { getSortingIcon } from '@/components/ui/sortIcon'

interface ApplicationInsightsTableProps {
  queryResults: ApplicationInsightsQueryResult
}

export const ApplicationInsightsTable: React.FC<
  ApplicationInsightsTableProps
> = ({ queryResults }) => {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [expanded, setExpanded] = React.useState<ExpandedState>({})

  // Helper function to check if a value is a complex object
  const isComplexObject = (value: any): boolean => {
    return (
      value !== null &&
      typeof value === 'object' &&
      !Array.isArray(value) &&
      !(value instanceof Date) &&
      Object.keys(value).length > 0
    )
  }

  // Helper function to check if a string contains JSON
  const isJsonString = (str: string): boolean => {
    if (typeof str !== 'string') return false
    const trimmed = str.trim()
    return (
      (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
      (trimmed.startsWith('[') && trimmed.endsWith(']'))
    )
  }

  // Helper function to safely parse and prettify JSON
  const tryParseAndPrettifyJson = (
    str: string
  ): { isJson: boolean; formatted: string; parsed?: any } => {
    try {
      const parsed = JSON.parse(str)
      let formatted = JSON.stringify(parsed, null, 2)
      // Convert escaped newlines to actual newlines for better readability
      formatted = formatted.replace(/\\n/g, '\n')
      return { isJson: true, formatted, parsed }
    } catch {
      return { isJson: false, formatted: str }
    }
  }

  // Helper function to render expandable content
  const renderExpandableContent = (value: any) => {
    if (isComplexObject(value)) {
      return (
        <div className="bg-gray-50 p-3 rounded border mt-2">
          <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
            {JSON.stringify(value, null, 2)}
          </pre>
        </div>
      )
    }
    return null
  }

  // Transform Application Insights results into table data
  const { columns, data } = useMemo(() => {
    if (!queryResults.tables || queryResults.tables.length === 0) {
      return { columns: [], data: [] }
    }

    const table = queryResults.tables[0]

    // Create columns from Application Insights column definitions
    const cols: ColumnDef<any>[] = [
      // Expander column
      {
        id: 'expander',
        header: '',
        cell: ({ row }) => {
          const hasExpandableData = table.columns.some((col) => {
            const value = row.original[col.name]
            const stringValue = String(value)
            return (
              isComplexObject(value) ||
              (typeof value === 'string' && value.length > 100) ||
              (typeof value === 'string' && isJsonString(stringValue))
            )
          })

          if (!hasExpandableData) return null

          return (
            <button
              onClick={() => row.toggleExpanded()}
              className="p-1 hover:bg-gray-100 rounded"
            >
              {row.getIsExpanded() ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </button>
          )
        },
        size: 40,
      },
      // Data columns
      ...table.columns.map((col, index) => ({
        accessorKey: col.name,
        header: ({ column }) => (
          <div
            className="flex items-center space-x-2 cursor-pointer select-none"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            <span className="font-semibold">{col.name}</span>
            {getSortingIcon(column.getIsSorted())}
          </div>
        ),
        cell: ({ getValue, row }) => {
          const value = getValue()

          // Handle different data types
          if (value === null || value === undefined) {
            return <span className="text-gray-400 italic">null</span>
          }

          if (isComplexObject(value)) {
            return (
              <div>
                <span
                  className="font-mono text-xs bg-blue-100 px-2 py-1 rounded cursor-pointer"
                  onClick={() => row.toggleExpanded()}
                >
                  {`{${Object.keys(value).length} properties}`}
                </span>
              </div>
            )
          }

          // Format timestamps
          if (col.type === 'datetime' && typeof value === 'string') {
            try {
              const date = new Date(value)
              return (
                <span className="font-mono text-xs">
                  {date.toLocaleString()}
                </span>
              )
            } catch {
              return <span className="font-mono text-xs">{String(value)}</span>
            }
          }

          const stringValue = String(value)

          // Check if the string contains JSON
          if (isJsonString(stringValue)) {
            const jsonResult = tryParseAndPrettifyJson(stringValue)
            if (jsonResult.isJson) {
              return (
                <div>
                  <span
                    className="font-mono text-xs bg-green-100 px-2 py-1 rounded cursor-pointer"
                    onClick={() => row.toggleExpanded()}
                    title="Click to expand JSON"
                  >
                    📄 JSON ({Object.keys(jsonResult.parsed || {}).length} keys)
                  </span>
                </div>
              )
            }
          }

          // Format long strings
          if (stringValue.length > 100) {
            return (
              <div>
                <span
                  className="font-mono text-xs cursor-pointer"
                  onClick={() => row.toggleExpanded()}
                  title="Click to expand"
                >
                  {stringValue.substring(0, 100)}...
                </span>
              </div>
            )
          }

          return <span className="font-mono text-xs">{stringValue}</span>
        },
        size: 150,
      })),
    ]

    // Transform rows
    const rows = table.rows.map((row, rowIndex) => {
      const rowData: any = {}
      table.columns.forEach((col, colIndex) => {
        rowData[col.name] = row[colIndex]
      })
      return rowData
    })

    return { columns: cols, data: rows }
  }, [queryResults])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    onSortingChange: setSorting,
    onExpandedChange: setExpanded,
    state: {
      sorting,
      expanded,
    },
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  // Export to CSV
  const exportToCsv = () => {
    if (!queryResults.tables || queryResults.tables.length === 0) return

    const table = queryResults.tables[0]
    const headers = table.columns.map((col) => col.name).join(',')
    const rows = table.rows
      .map((row) =>
        row
          .map((cell) => {
            const value =
              cell === null || cell === undefined ? '' : String(cell)
            // Escape quotes and wrap in quotes if contains comma
            return value.includes(',') || value.includes('"')
              ? `"${value.replace(/"/g, '""')}"`
              : value
          })
          .join(',')
      )
      .join('\n')

    const csv = `${headers}\n${rows}`
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `app-insights-query-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (!queryResults.tables || queryResults.tables.length === 0) {
    return (
      <Card className="border">
        <CardContent className="p-6">
          <div className="text-center text-gray-500">No results to display</div>
        </CardContent>
      </Card>
    )
  }

  const resultTable = queryResults.tables[0]

  return (
    <Card className="border">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Table className="w-5 h-5" />
              Query Results
            </CardTitle>
            <CardDescription>
              {resultTable.rows.length} rows × {resultTable.columns.length}{' '}
              columns
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={exportToCsv}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Export CSV
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Table */}
          <div className="border rounded-lg overflow-hidden">
            <div className="overflow-x-auto max-h-[600px]">
              <table className="w-full border-collapse">
                <thead className="bg-gray-50 sticky top-0">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b border-gray-200"
                          style={{ minWidth: `${header.getSize()}px` }}
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody>
                  {table.getRowModel().rows.map((row, index) => (
                    <React.Fragment key={row.id}>
                      <tr
                        className={cn(
                          'border-b border-gray-100',
                          index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                        )}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <td
                            key={cell.id}
                            className="px-4 py-3 text-sm text-gray-900 max-w-xs"
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </td>
                        ))}
                      </tr>
                      {row.getIsExpanded() && (
                        <tr>
                          <td
                            colSpan={columns.length}
                            className="px-4 py-3 bg-gray-50"
                          >
                            <div className="space-y-3">
                              <h4 className="font-semibold text-sm text-gray-700">
                                Expanded Data:
                              </h4>
                              {resultTable.columns.map((col) => {
                                const value = row.original[col.name]
                                const stringValue = String(value)

                                if (isComplexObject(value)) {
                                  return (
                                    <div key={col.name} className="mb-3">
                                      <div className="font-medium text-sm text-gray-600 mb-1">
                                        {col.name}:{' '}
                                        <span className="text-blue-600">
                                          (Object)
                                        </span>
                                      </div>
                                      <div className="bg-gray-50 p-4 rounded border border-gray-200 max-h-96 overflow-auto">
                                        <pre className="text-xs font-mono whitespace-pre-wrap text-gray-800 leading-relaxed">
                                          {JSON.stringify(value, null, 2)}
                                        </pre>
                                      </div>
                                    </div>
                                  )
                                } else if (
                                  typeof value === 'string' &&
                                  isJsonString(stringValue)
                                ) {
                                  const jsonResult =
                                    tryParseAndPrettifyJson(stringValue)
                                  if (jsonResult.isJson) {
                                    return (
                                      <div key={col.name} className="mb-3">
                                        <div className="font-medium text-sm text-gray-600 mb-1">
                                          {col.name}:{' '}
                                          <span className="text-green-600">
                                            (JSON)
                                          </span>
                                        </div>
                                        <div className="bg-gray-50 p-4 rounded border border-gray-200 max-h-96 overflow-auto">
                                          <pre className="text-xs font-mono whitespace-pre-wrap text-gray-800 leading-relaxed">
                                            {jsonResult.formatted}
                                          </pre>
                                        </div>
                                      </div>
                                    )
                                  }
                                } else if (
                                  typeof value === 'string' &&
                                  value.length > 100
                                ) {
                                  return (
                                    <div key={col.name} className="mb-3">
                                      <div className="font-medium text-sm text-gray-600 mb-1">
                                        {col.name}:{' '}
                                        <span className="text-orange-600">
                                          (Long Text)
                                        </span>
                                      </div>
                                      <div className="bg-gray-50 p-4 rounded border border-gray-200 max-h-96 overflow-auto">
                                        <div className="text-xs font-mono whitespace-pre-wrap text-gray-800 leading-relaxed">
                                          {value}
                                        </div>
                                      </div>
                                    </div>
                                  )
                                }
                                return null
                              })}
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing{' '}
              {table.getState().pagination.pageIndex *
                table.getState().pagination.pageSize +
                1}{' '}
              to{' '}
              {Math.min(
                (table.getState().pagination.pageIndex + 1) *
                  table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length
              )}{' '}
              of {table.getFilteredRowModel().rows.length} results
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>
              <span className="text-sm text-gray-700">
                Page {table.getState().pagination.pageIndex + 1} of{' '}
                {table.getPageCount()}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                Next
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
