'use client'
import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { api } from '@/trpc/react'
import Loader from '@/components/ui/Loader'
import { Loader2, Play, Database, Clock, Save } from 'lucide-react'
import { ApplicationInsightsTable } from './ApplicationInsightsTable'
import { ApplicationInsightsQueryResult } from '@/types/applicationInsights'

// Common Application Insights tables
const APP_INSIGHTS_TABLES = [
  { value: 'requests', label: 'requests - HTTP requests' },
  { value: 'dependencies', label: 'dependencies - External calls' },
  { value: 'exceptions', label: 'exceptions - Application exceptions' },
  { value: 'traces', label: 'traces - Application traces' },
  { value: 'pageViews', label: 'pageViews - Page view telemetry' },
  { value: 'customEvents', label: 'customEvents - Custom events' },
  { value: 'customMetrics', label: 'customMetrics - Custom metrics' },
  {
    value: 'performanceCounters',
    label: 'performanceCounters - Performance data',
  },
  {
    value: 'availabilityResults',
    label: 'availabilityResults - Availability tests',
  },
  { value: 'browserTimings', label: 'browserTimings - Browser performance' },
]

// Sample queries for different tables
const SAMPLE_QUERIES = {
  requests: `requests
| where timestamp > ago(24h)
| summarize count() by resultCode
| order by count_ desc`,

  dependencies: `dependencies
| where timestamp > ago(24h)
| where success == false
| project timestamp, name, type, resultCode, duration
| order by timestamp desc`,

  exceptions: `exceptions
| where timestamp > ago(24h)
| project timestamp, type, outerMessage, method, assembly
| order by timestamp desc`,

  traces: `traces
| where timestamp > ago(24h)
| where severityLevel >= 2
| project timestamp, message, severityLevel
| order by timestamp desc`,

  pageViews: `pageViews
| where timestamp > ago(24h)
| summarize count() by name
| order by count_ desc`,

  customEvents: `customEvents
| where timestamp > ago(24h)
| project timestamp, name, customDimensions
| order by timestamp desc`,
}

// Time range options
const TIME_RANGES = [
  { value: '1h', label: 'Last 1 hour' },
  { value: '24h', label: 'Last 24 hours' },
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
]

export const ApplicationInsightsLogs = () => {
  const [selectedTable, setSelectedTable] = useState<string>('')
  const [query, setQuery] = useState<string>('')
  const [timeRange, setTimeRange] = useState<string>('24h')
  const [queryResults, setQueryResults] =
    useState<ApplicationInsightsQueryResult | null>(null)
  const [isExecuting, setIsExecuting] = useState(false)

  const { toast } = useToast()

  // TRPC mutation for executing queries
  const executeQueryMutation = api.admin.executeAppInsightsQuery.useMutation({
    onSuccess: (data) => {
      setQueryResults(data)
      setIsExecuting(false)

      // Check if this is mock data by looking for "Sample data" in the results
      const isMockData = data.tables?.[0]?.rows?.some(row =>
        row.some(cell => typeof cell === 'string' && cell.includes('Sample data'))
      )

      toast({
        title: isMockData ? 'Query executed (Mock Data)' : 'Query executed successfully',
        description: `Retrieved ${data.tables[0]?.rows?.length || 0} rows${isMockData ? ' (mock data - set API key for real data)' : ' from Application Insights'}`,
        variant: isMockData ? 'default' : 'default',
      })
    },
    onError: (error) => {
      setIsExecuting(false)
      toast({
        title: 'Query execution failed',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  // Handle table selection
  const handleTableSelect = (table: string) => {
    setSelectedTable(table)
    if (SAMPLE_QUERIES[table as keyof typeof SAMPLE_QUERIES]) {
      setQuery(SAMPLE_QUERIES[table as keyof typeof SAMPLE_QUERIES])
    } else {
      setQuery(`${table}\n| limit 100`)
    }
  }

  // Execute query
  const handleExecuteQuery = () => {
    if (!query.trim()) {
      toast({
        title: 'Query required',
        description: 'Please enter a KQL query to execute',
        variant: 'destructive',
      })
      return
    }

    setIsExecuting(true)
    executeQueryMutation.mutate({
      query: query.trim(),
      timeRange,
    })
  }

  // Load sample query
  const loadSampleQuery = () => {
    if (
      selectedTable &&
      SAMPLE_QUERIES[selectedTable as keyof typeof SAMPLE_QUERIES]
    ) {
      setQuery(SAMPLE_QUERIES[selectedTable as keyof typeof SAMPLE_QUERIES])
    }
  }

  return (
    <div className="space-y-6">
      <Card className="border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Application Insights Logs
          </CardTitle>
          <CardDescription>
            Query Application Insights telemetry data using KQL (Kusto Query
            Language). This is currently showing mock data for demonstration
            purposes.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Table and Time Range Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Table</label>
              <Select value={selectedTable} onValueChange={handleTableSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a table" />
                </SelectTrigger>
                <SelectContent>
                  {APP_INSIGHTS_TABLES.map((table) => (
                    <SelectItem key={table.value} value={table.value}>
                      {table.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Time Range</label>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TIME_RANGES.map((range) => (
                    <SelectItem key={range.value} value={range.value}>
                      {range.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Query Editor */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">KQL Query</label>
              <Button
                variant="outline"
                size="sm"
                onClick={loadSampleQuery}
                disabled={
                  !selectedTable ||
                  !SAMPLE_QUERIES[selectedTable as keyof typeof SAMPLE_QUERIES]
                }
              >
                Load Sample Query
              </Button>
            </div>
            <Textarea
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Enter your KQL query here..."
              className="min-h-[200px] font-mono text-sm"
            />
          </div>

          {/* Execute Button */}
          <div className="flex justify-start">
            <Button
              onClick={handleExecuteQuery}
              disabled={isExecuting || !query.trim()}
              className="flex items-center gap-2"
            >
              {isExecuting ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              {isExecuting ? 'Executing...' : 'Run Query'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card className="border border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800 text-lg">Sample Queries</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-blue-800 mb-2">Common Queries:</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• <code>requests | limit 100</code> - Recent HTTP requests</li>
                <li>• <code>exceptions | limit 50</code> - Application exceptions</li>
                <li>• <code>traces | where severityLevel >= 2</code> - Warning+ traces</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-800 mb-2">Advanced Examples:</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• <code>requests | summarize count() by resultCode</code></li>
                <li>• <code>dependencies | where success == false</code></li>
                <li>• <code>pageViews | summarize count() by name</code></li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {queryResults && (
        <>
          {/* Data Source Banner */}
          {(() => {
            const isMockData = queryResults.tables?.[0]?.rows?.some(row =>
              row.some(cell => typeof cell === 'string' && cell.includes('Sample data'))
            )

            if (isMockData) {
              return (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Using Mock Data
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          You're seeing sample data. To get real Application Insights data:
                        </p>
                        <ol className="list-decimal list-inside mt-1 space-y-1">
                          <li>Go to Azure Portal → Application Insights → API Access</li>
                          <li>Create API Key with "Read telemetry" permission</li>
                          <li>Add it to your .env file as APPINSIGHTS_API_KEY="your-key-here"</li>
                          <li>Restart the development server</li>
                        </ol>
                      </div>
                    </div>
                  </div>
                </div>
              )
            } else {
              return (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">
                        Connected to Application Insights
                      </h3>
                      <p className="mt-1 text-sm text-green-700">
                        Showing real telemetry data from your Application Insights resource.
                      </p>
                    </div>
                  </div>
                </div>
              )
            }
          })()}

          <ApplicationInsightsTable queryResults={queryResults} />
        </>
      )}
    </div>
  )
}
