'use client'

import { api } from '@/trpc/react'
import { Alert, AlertDescription } from './ui/alert'
import { CircleX, TriangleAlert } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useEffect, useState } from 'react'

/** If this is limited to only measures page, add pathname check logic */

export const NoticeContainer = () => {
  const noticeCheckQuery = api.notices.check.useQuery()

  const [showNotice, setShowNotice] = useState(false)

  useEffect(() => {
    if (noticeCheckQuery.data?.isNoticeDisplay) {
      setShowNotice(noticeCheckQuery.data?.isNoticeDisplay)
    }
  }, [noticeCheckQuery.data?.isNoticeDisplay])

  return (
    <Alert
      variant="destructive"
      className={cn(
        'bg-[#FCEBE9] text-[#F23620] font-open-sans text-[13px] border-[#FCEBE9] pl-[75px] pr-[75px]',
        showNotice ? '' : 'hidden'
      )}
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2 text-[13px] ">
          <TriangleAlert className="h-4 w-4" />
          <AlertDescription className=" font-normal font-open-sans ">
            {noticeCheckQuery.data?.noticeContent}
          </AlertDescription>
        </div>

        <CircleX className="h-4 w-4" onClick={() => setShowNotice(false)} />
      </div>
    </Alert>
  )
}
