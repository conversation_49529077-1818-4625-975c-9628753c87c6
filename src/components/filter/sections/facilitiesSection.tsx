import { useMemo, useState } from 'react'
import { Section } from './section'
import { useFilterStore } from '@/stores/filter'
import { List, AutoSizer, CellMeasurerCache } from 'react-virtualized'
import { Search } from 'lucide-react'
import { Facility } from '@/types/facility'

export const FacilitiesSection = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const {
    facilities,
    checkedFacilities,
    expandedSections,
    setExpandedSections,
    removeFacility,
    addFacility,
    removeAllFacilities,
    addAllFacilities,
  } = useFilterStore()

  // Assuming you have a similar structure for Hospital CCN Groups as measures
  const filteredFacilities = useMemo(() => {
    return facilities.filter((facility) =>
      facility.facilityName!.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [searchTerm, facilities])

  const toggleFacility = (facility: Facility) => {
    if (
      !!checkedFacilities.find(
        (checkedFacility) => checkedFacility.id === facility.facilityCode!
      )
    ) {
      removeFacility(facility.facilityCode!)
    } else {
      addFacility(facility)
    }
  }

  const toggleSelectAll = () => {
    if (filteredFacilities) {
      if (checkedFacilities.length === filteredFacilities.length) {
        removeAllFacilities()
      } else {
        addAllFacilities()
      }
    }
  }

  const rowRenderer = ({ index, key, style }: any) => {
    const facility = filteredFacilities?.[index]
    if (!facility) return null
    return (
      <div key={key} style={style}>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id={facility.facilityCode!}
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              !!checkedFacilities.find(
                (checkedFacility) =>
                  checkedFacility.id === facility.facilityCode!
              )
            }
            onChange={() => toggleFacility(facility)}
          />
          <label
            htmlFor={facility.facilityCode!}
            className="text-[12px] font-open-sans font-normal leading-[16.34px]"
          >
            {facility.facilityName!}
          </label>
        </div>
      </div>
    )
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const cache = new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 30,
  })

  return (
    <Section
      title={`FACILITIES (${checkedFacilities.length})`}
      expanded={expandedSections.includes('FACILITIES')}
      onToggle={() => setExpandedSections('FACILITIES')}
    >
      <div className="relative mb-4">
        <input
          key="hccSeach"
          autoFocus={true}
          type="text"
          placeholder="Search"
          value={searchTerm}
          onChange={(e) => handleChange(e)}
          className="pl-4 pr-8 py-2 border-b border-blue-3 text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
          style={{
            borderTop: 'none',
            borderLeft: 'none',
            borderRight: 'none',
          }}
        />
        <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-blue-3" />
      </div>

      {searchTerm.length === 0 && (
        <div className="flex items-center space-x-2 mb-2">
          <input
            type="checkbox"
            id="selectAllHospitalCCNGroups"
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              filteredFacilities &&
              checkedFacilities.length === filteredFacilities.length
            }
            onChange={toggleSelectAll}
          />
          <label
            htmlFor="selectAllHospitalCCNGroups"
            className="text-[12px] font-open-sans font-normal"
          >
            Select All
          </label>
        </div>
      )}

      {filteredFacilities && (
        <AutoSizer>
          {({ width, height }) => {
            // Calculate height based on the number of items
            const rowHeight = cache.defaultHeight || 30 // Default row height
            const totalHeight = filteredFacilities.length * rowHeight
            const maxItems = 10

            const constrainedHeight = Math.min(
              totalHeight,
              maxItems * rowHeight
            )

            return (
              <List
                width={width}
                height={constrainedHeight}
                rowHeight={cache.rowHeight}
                rowRenderer={rowRenderer}
                rowCount={filteredFacilities.length}
                overscanRowCount={3}
              />
            )
          }}
        </AutoSizer>
      )}
    </Section>
  )
}
