import { useMemo, useState } from 'react'
import { Section } from './section'
import { useFilterStore } from '@/stores/filter'
import {
  CellMeasurer,
  CellMeasurerCache,
  List,
  AutoSizer,
} from 'react-virtualized'
import { Search } from 'lucide-react'
import { MeasureFilterModel } from '@/types/measureFilterModel'

export const MeasuresSection = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const { expandedSections, setExpandedSections } = useFilterStore()
  const [isAllChecked, setIsAllChecked] = useState(false)

  const {
    measures,
    checkedMeasures,
    addMeasure,
    removeMeasure,
    removeAllMeasures,
    addAllMeasures,
    addMeasures,
    removeMeasures,
  } = useFilterStore()

  const filteredMeasures = useMemo(() => {
    return measures.filter((measure) =>
      measure.measureName.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [searchTerm, measures])

  const toggleMeasure = (measure: MeasureFilterModel) => {
    if (
      !!checkedMeasures.find(
        (checkedMeasures) => checkedMeasures.id === measure.measureId
      )
    ) {
      removeMeasure(measure.measureId)
    } else {
      addMeasure(measure)
    }
  }

  const toggleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    //leaving this here for now as we might come back and use this in the future
    if (filteredMeasures) {
      //first check to see if the user is searching for a specific measure

      // const isSearching = searchTerm.length > 0
      // if (isSearching) {
      //   if (e.target.checked) {
      //     addMeasures(filteredMeasures)
      //     setIsAllChecked(true)
      //   } else {
      //     removeMeasures(filteredMeasures)
      //     setIsAllChecked(false)
      //   }

      //   return
      // }

      if (checkedMeasures.length === filteredMeasures.length) {
        removeAllMeasures()
      } else {
        addAllMeasures()
      }
    }
  }

  const cache = new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 30,
  })
  const rowRenderer = ({ index, key, style, parent }: any) => {
    const measure = filteredMeasures?.[index]
    if (!measure) return null
    return (
      <CellMeasurer
        key={key}
        cache={cache}
        parent={parent}
        columnIndex={0}
        rowIndex={index}
      >
        {({ registerChild }) => (
          <div style={style} className="row pb-1" ref={registerChild}>
            <span className="flex space-x-2">
              <input
                type="checkbox"
                id={measure.measureId}
                className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px] "
                checked={
                  !!checkedMeasures.find(
                    (checkedMeasure) => checkedMeasure.id === measure.measureId
                  )
                }
                onChange={() => toggleMeasure(measure)}
              />
              <label
                htmlFor={measure.measureId}
                className="text-[12px] font-open-sans font-normal "
              >
                {measure.measureName}
              </label>
            </span>
          </div>
        )}
      </CellMeasurer>
    )
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const getHeight = () => {
    const rowHeight = cache.defaultHeight || 30 // Default row height
    const totalHeight = filteredMeasures.length * rowHeight
    const maxItems = 10

    return Math.min(totalHeight, maxItems * rowHeight)
  }

  return (
    <Section
      title={`MEASURES (${checkedMeasures.length})`}
      expanded={expandedSections.includes('MEASURES')}
      onToggle={() => setExpandedSections('MEASURES')}
      customHeight={getHeight() + 91}
      className="focus:outline-none"
    >
      <div className=" mb-4 relative">
        <input
          key="measureSearch"
          autoFocus={true}
          type="text"
          placeholder="Search"
          value={searchTerm}
          onChange={(e) => handleChange(e)}
          className="pl-4 pr-8 py-2 border-b border-blue-3 text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
          style={{
            borderTop: 'none',
            borderLeft: 'none',
            borderRight: 'none',
          }}
        />
        <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-blue-3" />
      </div>

      {searchTerm.length === 0 && (
        <div className="flex items-center space-x-2 mb-2">
          <input
            type="checkbox"
            id="selectAllMeasures"
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              filteredMeasures &&
              checkedMeasures.length === filteredMeasures.length
            }
            onChange={(e) => toggleSelectAll(e)}
          />
          <label
            htmlFor="selectAllMeasures"
            className="text-[12px] font-open-sans font-normal"
          >
            Select All
          </label>
        </div>
      )}
      {filteredMeasures && (
        <AutoSizer>
          {({ width, height }) => {
            // Calculate height based on the number of items
            const rowHeight = cache.defaultHeight || 30 // Default row height
            const totalHeight = filteredMeasures.length * rowHeight
            const maxItems = 10

            const constrainedHeight = Math.min(
              totalHeight,
              maxItems * rowHeight
            )

            return (
              <List
                width={width}
                height={getHeight()}
                rowHeight={cache.rowHeight}
                rowRenderer={rowRenderer}
                rowCount={filteredMeasures.length}
                overscanRowCount={3}
                className="mac-scrollbar focus:outline-none"
              />
            )
          }}
        </AutoSizer>
      )}
    </Section>
  )
}
