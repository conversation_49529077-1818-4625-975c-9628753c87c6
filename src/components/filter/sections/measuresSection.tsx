import { useMemo, useState } from 'react'
import { Section } from './section'
import { useFilterStore } from '@/stores/filter'
import {
  CellMeasurer,
  CellMeasurerCache,
  List,
  AutoSizer,
} from 'react-virtualized'
import { Search } from 'lucide-react'
import { MeasureFilterModel } from '@/types/measureFilterModel'
import { MeasureTypeByApplication } from '@/enums/measureTypeByApplication'
import { SourceType } from '@/enums/measureSource'
import MeasureSourcePills from './chips/MeasureSourcePills'

export const MeasuresSection = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const { expandedSections, setExpandedSections } = useFilterStore()
  const [isAllChecked, setIsAllChecked] = useState(false)

  const {
    measures,
    checkedMeasures,
    addMeasure,
    removeMeasure,
    removeAllMeasures,
    addAllMeasures,
    addMeasures,
    removeMeasures,
  } = useFilterStore()

  const [measureSource, setMeasureSource] = useState([
    {
      label: SourceType.CQM,
      isSelected: false,
      isVisible: false,
      applications: [MeasureTypeByApplication.RegistryMeasures],
    },
    {
      label: SourceType.ABSTR,
      isSelected: false,
      isVisible: false,
      applications: [MeasureTypeByApplication.AbstractedMeasures],
    },
    {
      label: SourceType.ECQM,
      isSelected: false,
      isVisible: false,
      applications: [
        MeasureTypeByApplication.HospitalMeasures,
        MeasureTypeByApplication.AmbulatoryMeasures,
      ],
    },
  ])

  const selectedMeasuresSource: string[] = useMemo(
    () =>
      measureSource
        .filter((source) => source.isSelected)
        .flatMap((source) => source.applications),
    [measureSource]
  )

  // Extract measure names from the API response - memoize to prevent unnecessary recalculations
  const measuresList = useMemo(() => {
    const isSourceVisible = {
      [SourceType.CQM]: false,
      [SourceType.ABSTR]: false,
      [SourceType.ECQM]: false,
    }

    const list =
      measures?.map((measure) => {
        if (
          !isSourceVisible[SourceType.CQM] &&
          measure.applicationName === MeasureTypeByApplication.RegistryMeasures
        ) {
          isSourceVisible[SourceType.CQM] = true
        } else if (
          !isSourceVisible[SourceType.ABSTR] &&
          measure.applicationName ===
            MeasureTypeByApplication.AbstractedMeasures
        ) {
          isSourceVisible[SourceType.ABSTR] = true
        } else if (
          !isSourceVisible[SourceType.ECQM] &&
          (measure.applicationName ===
            MeasureTypeByApplication.HospitalMeasures ||
            measure.applicationName ===
              MeasureTypeByApplication.AmbulatoryMeasures)
        ) {
          isSourceVisible[SourceType.ECQM] = true
        }

        return measure
      }) || []

    setMeasureSource((prev) =>
      prev.map((measure) => ({
        ...measure,
        isVisible: isSourceVisible[measure.label],
      }))
    )
    return list
  }, [measures])

  const filteredMeasures = useMemo(() => {
    // Extract all measure
    let filtered = measures

    // Filter by selected source if any are selected
    const selectedSources: string[] = measureSource
      .filter((source) => source.isSelected)
      .flatMap((source) => source.applications)

    if (selectedSources.length > 0) {
      filtered = filtered.filter((measure) =>
        selectedSources.includes(measure.applicationName)
      )
    }

    // Apply the search term filter
    return filtered.filter((measure) =>
      measure.measureName.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [searchTerm, measures, measureSource])

  const toggleMeasure = (measure: MeasureFilterModel) => {
    if (
      !!checkedMeasures.find(
        (checkedMeasures) => checkedMeasures.id === measure.measureId
      )
    ) {
      removeMeasure(measure.measureId)
    } else {
      addMeasure(measure)
    }
  }

  const toggleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    //leaving this here for now as we might come back and use this in the future
    if (filteredMeasures) {
      //first check to see if the user is searching for a specific measure

      // const isSearching = searchTerm.length > 0
      // if (isSearching) {
      //   if (e.target.checked) {
      //     addMeasures(filteredMeasures)
      //     setIsAllChecked(true)
      //   } else {
      //     removeMeasures(filteredMeasures)
      //     setIsAllChecked(false)
      //   }

      //   return
      // }

      // Check if measure source filter is applied of not
      if (selectedMeasuresSource.length === 0) {
        if (checkedMeasures.length === measuresList.length) {
          removeAllMeasures()
        } else {
          addAllMeasures()
        }
      } else {
        const existingIds = new Set(checkedMeasures.map((cm) => cm.id))
        const isAllSelected = filteredMeasures.every((fm) =>
          existingIds.has(fm.measureId)
        )

        if (isAllSelected) {
          // Deselct previously selected measures
          removeMeasures(filteredMeasures)
        } else {
          // Extract filtered but not checked measures
          const toAdd = filteredMeasures.filter(
            (fm) => !existingIds.has(fm.measureId)
          )
          addMeasures(toAdd)
        }
      }
    }
  }

  const cache = new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 30,
  })
  const rowRenderer = ({ index, key, style, parent }: any) => {
    const measure = filteredMeasures?.[index]
    if (!measure) return null
    return (
      <CellMeasurer
        key={key}
        cache={cache}
        parent={parent}
        columnIndex={0}
        rowIndex={index}
      >
        {({ registerChild }) => (
          <div style={style} className="row pb-1" ref={registerChild}>
            <span className="flex space-x-2">
              <input
                type="checkbox"
                id={measure.measureId}
                className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px] "
                checked={
                  !!checkedMeasures.find(
                    (checkedMeasure) => checkedMeasure.id === measure.measureId
                  )
                }
                onChange={() => toggleMeasure(measure)}
              />
              <label
                htmlFor={measure.measureId}
                className="text-[12px] font-open-sans font-normal "
              >
                {measure.measureName}
              </label>
            </span>
          </div>
        )}
      </CellMeasurer>
    )
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const getHeight = () => {
    const rowHeight = cache.defaultHeight || 30 // Default row height
    const totalHeight = filteredMeasures.length * rowHeight
    const maxItems = 10

    return Math.min(totalHeight, maxItems * rowHeight)
  }

  // Handle measure source pill selection
  const handleMeasurePillSelection = (label: string, isSelected: boolean) => {
    const updatedSource = measureSource.map((source) =>
      source.label === label ? { ...source, isSelected } : source
    )
    setMeasureSource(updatedSource)
  }

  return (
    <Section
      title={`MEASURES (${checkedMeasures.length})`}
      expanded={expandedSections.includes('MEASURES')}
      onToggle={() => setExpandedSections('MEASURES')}
      customHeight={getHeight() + 131}
      className="focus:outline-none"
    >
      <div className="relative mb-4">
        <input
          key="measureSearch"
          autoFocus={true}
          type="text"
          placeholder="Search"
          value={searchTerm}
          onChange={(e) => handleChange(e)}
          className="pl-4 pr-8 py-2 border-b border-blue-3 text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
          style={{
            borderTop: 'none',
            borderLeft: 'none',
            borderRight: 'none',
          }}
        />
        <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-blue-3" />

        <MeasureSourcePills
          measureSource={measureSource}
          handleMeasurePillSelection={handleMeasurePillSelection}
        />
      </div>

      {searchTerm.length === 0 && (
        <div className="flex items-center space-x-2 mb-2">
          <input
            type="checkbox"
            id="selectAllMeasures"
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              checkedMeasures.length === measuresList.length ||
              checkedMeasures.filter((cm) =>
                selectedMeasuresSource.includes(cm.applicationName!)
              ).length === filteredMeasures.length
            }
            onChange={(e) => toggleSelectAll(e)}
          />
          <label
            htmlFor="selectAllMeasures"
            className="text-[12px] font-open-sans font-normal"
          >
            Select All
          </label>
        </div>
      )}
      {filteredMeasures && (
        <AutoSizer>
          {({ width, height }) => {
            // Calculate height based on the number of items
            const rowHeight = cache.defaultHeight || 30 // Default row height
            const totalHeight = filteredMeasures.length * rowHeight
            const maxItems = 10

            const constrainedHeight = Math.min(
              totalHeight,
              maxItems * rowHeight
            )

            return (
              <List
                width={width}
                height={getHeight()}
                rowHeight={cache.rowHeight}
                rowRenderer={rowRenderer}
                rowCount={filteredMeasures.length}
                overscanRowCount={3}
                className="mac-scrollbar focus:outline-none"
              />
            )
          }}
        </AutoSizer>
      )}
    </Section>
  )
}
