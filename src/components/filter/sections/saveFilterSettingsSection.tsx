import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'
import { useFilterStore } from '@/stores/filter'
import { api } from '@/trpc/react'
import { Loader2 } from 'lucide-react'
import { usePathname } from 'next/navigation'
import { useState } from 'react'

import { Section } from './section'
import { useUserSessionStore } from '@/stores/userSession'

export const SaveFilterSettingsSection = () => {
  const pathname = usePathname()
  const { toast } = useToast()
  const [filterName, setFilterName] = useState('')

  const {
    expandedSections,
    setExpandedSections,
    checkedSubOrganizations,
    checkedSubmissionGroups,
    checkedMeasures,
    checkedProviders,
  } = useFilterStore()

  const { primaryMeasureType } = useUserSessionStore()

  const saveFilterMutation = api.filters.saveFilters.useMutation()

  const trpcUtils = api.useUtils()

  const saveFilters = () =>
    saveFilterMutation.mutate(
      {
        filterName,
        primaryMeasureType,
        page: pathname.replace('/', ''),
        hospitals:
          primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures
            ? checkedSubOrganizations.map(
              (checkedSubOrganization) => checkedSubOrganization.id
            )
            : checkedSubmissionGroups.map(
              (checkedSubmissionGroup) => checkedSubmissionGroup.id
            ),
        measures: checkedMeasures.map((checkedMeasure) => checkedMeasure.id),
        groups: [],
        providers: checkedProviders.map(
          (checkedProvider) => checkedProvider.id
        ),
        organizations: [],
        facilities: [],
      },
      {
        onSuccess: (data) => {
          trpcUtils.filters.getSavedFilters.invalidate().catch(console.error)

          setFilterName('')

          toast({
            className: cn('bg-green-600 text-white border-green-600'),
            title: data.message,
            variant: 'default',
          })
        },
      }
    )

  return (
    <Section
      title="SAVE FILTER SETTINGS"
      expanded={expandedSections.includes('SAVE FILTER SETTINGS')}
      onToggle={() => setExpandedSections('SAVE FILTER SETTINGS')}
      className="!mb-6"
    >
      <div className="flex items-center">
        <input
          disabled={saveFilterMutation.isPending}
          type="text"
          placeholder="Name"
          value={filterName}
          onChange={(e) => setFilterName(e.target.value)}
          className="flex-grow px-3 py-1 text-xs border border-gray-300 text-[#34495e] focus:outline-none"
        />
        <button
          onClick={saveFilters}
          className="bg-[#1B4A70] text-white h-[27px] px-4 py-1 text-3xs border-blue-4 font-medium focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={saveFilterMutation.isPending}
        >
          {saveFilterMutation.isPending ? (
            <Loader2 className="animate-spin" />
          ) : (
            'Save'
          )}
        </button>
      </div>
    </Section>
  )
}
