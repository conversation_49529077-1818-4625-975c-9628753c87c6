import { useMemo, useState } from 'react'
import { Section } from './section'
import { List, AutoSizer, CellMeasurerCache } from 'react-virtualized'
import { useFilterStore } from '@/stores/filter'
import { Provider } from '@/types/provider'
import { Search } from 'lucide-react'

export const ProvidersSection = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const {
    providers,
    checkedProviders,
    removeAllProviders,
    addAllProviders,
    expandedSections,
    setExpandedSections,
    removeProvider,
    addProvider,
  } = useFilterStore()

  const filteredProviders = useMemo(() => {
    return providers.filter((provider) =>
      provider.providerName.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [searchTerm, providers])

  const toggleProvider = (provider: Provider) => {
    if (
      checkedProviders.find(
        (checkedProvider) => checkedProvider.id === provider.npi
      )
    ) {
      removeProvider(provider.npi)
    } else {
      addProvider(provider)
    }
  }

  const rowRenderer = ({ index, key, style }: any) => {
    const provider = filteredProviders?.[index]
    if (!provider) return null
    return (
      <div key={key} style={style}>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id={provider.npi}
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              !!checkedProviders.find(
                (checkedProvider) => checkedProvider.id === provider.npi
              )
            }
            onChange={() => toggleProvider(provider)}
          />
          <label
            htmlFor={provider.npi}
            className="text-[12px] font-open-sans font-normal leading-[16.34px]"
          >
            {provider.providerName}
          </label>
        </div>
      </div>
    )
  }

  const toggleSelectAll = () => {
    if (filteredProviders) {
      if (checkedProviders.length === filteredProviders.length) {
        removeAllProviders()
      } else {
        addAllProviders()
      }
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const cache = new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 30,
  })

  return (
    <Section
      title={`PROVIDERS (${checkedProviders.length})`}
      expanded={expandedSections.includes('PROVIDERS')}
      onToggle={() => setExpandedSections('PROVIDERS')}
    >
      <div className="relative mb-4">
        <input
          key="providerSeach"
          autoFocus={true}
          type="text"
          placeholder="Search"
          value={searchTerm}
          onChange={(e) => handleChange(e)}
          className="pl-4 pr-8 py-2 border-b border-blue-3 text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
          style={{
            borderTop: 'none',
            borderLeft: 'none',
            borderRight: 'none',
          }}
        />
        <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-blue-3" />
      </div>

      {searchTerm.length === 0 && (
        <div className="flex items-center space-x-2 mb-2">
          <input
            type="checkbox"
            id="selectAllProviders"
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              filteredProviders &&
              checkedProviders.length === filteredProviders.length
            }
            onChange={toggleSelectAll}
          />
          <label
            htmlFor="selectAllProviders"
            className="text-[12px] font-open-sans font-normal"
          >
            Select All
          </label>
        </div>
      )}

      {filteredProviders && (
        <AutoSizer>
          {({ width, height }) => {
            // Calculate height based on the number of items
            const rowHeight = cache.defaultHeight || 30 // Default row height
            const totalHeight = filteredProviders.length * rowHeight
            const maxItems = 10

            const constrainedHeight = Math.min(
              totalHeight,
              maxItems * rowHeight
            )

            return (
              <List
                width={width}
                height={constrainedHeight}
                rowHeight={cache.rowHeight}
                rowRenderer={rowRenderer}
                rowCount={filteredProviders.length}
                overscanRowCount={3}
              />
            )
          }}
        </AutoSizer>
      )}
    </Section>
  )
}
