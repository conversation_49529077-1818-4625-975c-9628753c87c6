import { useMemo, useState } from 'react'
import { Section } from './section'
import { useFilterStore } from '@/stores/filter'
import { List, AutoSizer, CellMeasurerCache } from 'react-virtualized'
import { Search } from 'lucide-react'
import { GroupsnSubOrgs } from '@/types/groupsnSubOrgs'

export const HospitalCCNGroupsSection = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const {
    subOrganizations,
    checkedSubOrganizations,
    expandedSections,
    setExpandedSections,
    removeSubOrganization,
    addSubOrganization,
    removeAllSubOrganizations,
    addAllSubOrganizations,
  } = useFilterStore()

  // Assuming you have a similar structure for Hospital CCN Groups as measures
  const filteredHospitalGroups = useMemo(() => {
    return subOrganizations.filter((subOrganization) =>
      subOrganization.subOrganizationName
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
    )
  }, [searchTerm, subOrganizations])

  const toggleHostpitalCCNGroup = (subOrganization: GroupsnSubOrgs) => {
    if (
      !!checkedSubOrganizations.find(
        (checkedSubOrganization) =>
          checkedSubOrganization.id === subOrganization.subOrganizationId
      )
    ) {
      removeSubOrganization(subOrganization.subOrganizationId)
    } else {
      addSubOrganization(subOrganization)
    }
  }

  const toggleSelectAll = () => {
    if (filteredHospitalGroups) {
      if (checkedSubOrganizations.length === filteredHospitalGroups.length) {
        removeAllSubOrganizations()
      } else {
        addAllSubOrganizations()
      }
    }
  }

  const rowRenderer = ({ index, key, style }: any) => {
    const group = filteredHospitalGroups?.[index]
    if (!group) return null
    return (
      <div key={key} style={style}>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id={group.subOrganizationId}
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              !!checkedSubOrganizations.find(
                (checkedSubOrganization) =>
                  checkedSubOrganization.id === group.subOrganizationId
              )
            }
            onChange={() => toggleHostpitalCCNGroup(group)}
          />
          <label
            htmlFor={group.subOrganizationId}
            className="text-[12px] font-open-sans font-normal leading-[16.34px]"
          >
            {group.subOrganizationName}
          </label>
        </div>
      </div>
    )
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const cache = new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 30,
  })

  return (
    <Section
      title={`HOSPITAL CCN GROUPS (${checkedSubOrganizations.length})`}
      expanded={expandedSections.includes('HOSPITAL CCN GROUPS')}
      onToggle={() => setExpandedSections('HOSPITAL CCN GROUPS')}
    >
      <div className="relative mb-4">
        <input
          key="hccSeach"
          autoFocus={true}
          type="text"
          placeholder="Search"
          value={searchTerm}
          onChange={(e) => handleChange(e)}
          className="pl-4 pr-8 py-2 border-b border-blue-3 text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
          style={{
            borderTop: 'none',
            borderLeft: 'none',
            borderRight: 'none',
          }}
        />
        <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-blue-3" />
      </div>

      {searchTerm.length === 0 && (
        <div className="flex items-center space-x-2 mb-2">
          <input
            type="checkbox"
            id="selectAllHospitalCCNGroups"
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              filteredHospitalGroups &&
              checkedSubOrganizations.length === filteredHospitalGroups.length
            }
            onChange={toggleSelectAll}
          />
          <label
            htmlFor="selectAllHospitalCCNGroups"
            className="text-[12px] font-open-sans font-normal"
          >
            Select All
          </label>
        </div>
      )}

      {filteredHospitalGroups && (
        <AutoSizer>
          {({ width, height }) => {
            // Calculate height based on the number of items
            const rowHeight = cache.defaultHeight || 30 // Default row height
            const totalHeight = filteredHospitalGroups.length * rowHeight
            const maxItems = 10

            const constrainedHeight = Math.min(
              totalHeight,
              maxItems * rowHeight
            )

            return (
              <List
                width={width}
                height={constrainedHeight}
                rowHeight={cache.rowHeight}
                rowRenderer={rowRenderer}
                rowCount={filteredHospitalGroups.length}
                overscanRowCount={3}
              />
            )
          }}
        </AutoSizer>
      )}
    </Section>
  )
}
