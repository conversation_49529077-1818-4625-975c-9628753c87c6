import { useMemo, useState } from 'react'
import { Section } from './section'
import { useFilterStore } from '@/stores/filter'
import { List, AutoSizer, CellMeasurerCache } from 'react-virtualized'
import { SubmissionGroup } from '@/types/submissionGroup'
import { Search } from 'lucide-react'

export const SubmissionGroupsSection = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const {
    submissionGroups,
    expandedSections,
    setExpandedSections,
    checkedSubmissionGroups,
    removeSubmissionGroup,
    addSubmissionGroup,
    removeAllSubmissionGroups,
    addAllSubmissionGroups,
  } = useFilterStore()

  const filteredSubmissionGroups = useMemo(() => {
    return submissionGroups.filter((submissionGroup) =>
      submissionGroup.submissionGroupName
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
    )
  }, [searchTerm, submissionGroups])

  const toggleSubmissionGroup = (submissionGroup: SubmissionGroup) => {
    if (
      !!checkedSubmissionGroups.find(
        (checkedSubmissionGroup) =>
          checkedSubmissionGroup.id === submissionGroup.submissionGroupId
      )
    ) {
      removeSubmissionGroup(submissionGroup.submissionGroupId)
    } else {
      addSubmissionGroup(submissionGroup)
    }
  }

  const rowRenderer = ({ index, key, style }: any) => {
    const group = filteredSubmissionGroups?.[index]
    if (!group) return null
    return (
      <div key={key} style={style}>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id={group.submissionGroupId}
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              !!checkedSubmissionGroups.find(
                (checkedSubmissionGroup) =>
                  checkedSubmissionGroup.id === group.submissionGroupId
              )
            }
            onChange={() => toggleSubmissionGroup(group)}
          />
          <label
            htmlFor={group.submissionGroupId}
            className="text-[12px] font-open-sans font-normal leading-[16.34px]"
          >
            {group.submissionGroupName}
          </label>
        </div>
      </div>
    )
  }

  const toggleSelectAll = () => {
    if (filteredSubmissionGroups) {
      if (checkedSubmissionGroups.length === filteredSubmissionGroups.length) {
        removeAllSubmissionGroups()
      } else {
        addAllSubmissionGroups()
      }
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const cache = new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 30,
  })

  return (
    <Section
      title={`SUBMISSION GROUPS (${checkedSubmissionGroups.length})`}
      expanded={expandedSections.includes('SUBMISSION GROUPS')}
      onToggle={() => setExpandedSections('SUBMISSION GROUPS')}
    >
      <div className="relative mb-4">
        <input
          key="submissionGroupSeach"
          autoFocus={true}
          type="text"
          placeholder="Search"
          value={searchTerm}
          onChange={(e) => handleChange(e)}
          className="pl-4 pr-8 py-2 border-b border-blue-3 text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
          style={{
            borderTop: 'none',
            borderLeft: 'none',
            borderRight: 'none',
          }}
        />
        <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-blue-3" />
      </div>

      {searchTerm.length === 0 && (
        <div className="flex items-center space-x-2 mb-2">
          <input
            type="checkbox"
            id="selectAllSubmissionGroups"
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              filteredSubmissionGroups &&
              checkedSubmissionGroups.length === filteredSubmissionGroups.length
            }
            onChange={toggleSelectAll}
          />
          <label
            htmlFor="selectAllSubmissionGroups"
            className="text-[12px] font-open-sans font-normal"
          >
            Select All
          </label>
        </div>
      )}

      {filteredSubmissionGroups && (
        <AutoSizer>
          {({ width, height }) => {
            // Calculate height based on the number of items
            const rowHeight = cache.defaultHeight || 30 // Default row height
            const totalHeight = filteredSubmissionGroups.length * rowHeight
            const maxItems = 10

            const constrainedHeight = Math.min(
              totalHeight,
              maxItems * rowHeight
            )

            return (
              <List
                width={width}
                height={constrainedHeight}
                rowHeight={cache.rowHeight}
                rowRenderer={rowRenderer}
                rowCount={filteredSubmissionGroups.length}
                overscanRowCount={3}
              />
            )
          }}
        </AutoSizer>
      )}
    </Section>
  )
}
