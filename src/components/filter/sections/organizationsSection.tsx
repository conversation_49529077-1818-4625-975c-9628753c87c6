import { useMemo, useState } from 'react'
import { Section } from './section'
import { useFilterStore } from '@/stores/filter'
import { List, AutoSizer, CellMeasurerCache } from 'react-virtualized'
import { Search } from 'lucide-react'
import { Organization } from '@/types/organization'

export const OrganizationsSection = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const {
    organizations,
    expandedSections,
    setExpandedSections,
    checkedOrganizations,
    removeOrganization,
    addOrganization,
    removeAllOrganizations,
    addAllOrganizations,
  } = useFilterStore()

  const filteredOrganizations = useMemo(() => {
    return organizations.filter((organization) =>
      organization
        .organizationName!.toLowerCase()
        .includes(searchTerm.toLowerCase())
    )
  }, [searchTerm, organizations])

  const toggleOrganization = (organization: Organization) => {
    if (
      !!checkedOrganizations.find(
        (checkedOrganization) =>
          checkedOrganization.id === organization.organizationId!
      )
    ) {
      removeOrganization(organization.organizationId!)
    } else {
      addOrganization(organization)
    }
  }

  const rowRenderer = ({ index, key, style }: any) => {
    const org = filteredOrganizations?.[index]
    if (!org) return null
    return (
      <div key={key} style={style}>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id={org.organizationId!}
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={
              !!checkedOrganizations.find(
                (checkedOrganization) =>
                  checkedOrganization.id === org.organizationId!
              )
            }
            onChange={() => toggleOrganization(org)}
          />
          <label
            htmlFor={org.organizationId!}
            className="text-[12px] font-open-sans font-normal leading-[16.34px]"
          >
            {org.organizationName!}
          </label>
        </div>
      </div>
    )
  }

  // Calculate if all filtered organizations are selected
  const allFilteredSelected = useMemo(() => {
    if (filteredOrganizations.length === 0) return false

    return filteredOrganizations.every((org) =>
      checkedOrganizations.some((checked) => checked.id === org.organizationId!)
    )
  }, [filteredOrganizations, checkedOrganizations])

  const toggleSelectAll = () => {
    if (filteredOrganizations.length > 0) {
      if (allFilteredSelected) {
        // Remove only the filtered organizations
        filteredOrganizations.forEach((org) => {
          removeOrganization(org.organizationId!)
        })
      } else {
        // Add only the filtered organizations
        filteredOrganizations.forEach((org) => {
          // Only add if not already selected
          if (
            !checkedOrganizations.some(
              (checked) => checked.id === org.organizationId!
            )
          ) {
            addOrganization(org)
          }
        })
      }
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const cache = new CellMeasurerCache({
    fixedWidth: true,
    defaultHeight: 30,
  })

  return (
    <Section
      title={`ORGANIZATIONS (${checkedOrganizations.length})`}
      expanded={expandedSections.includes('ORGANIZATIONS')}
      onToggle={() => setExpandedSections('ORGANIZATIONS')}
    >
      <div className="relative mb-4">
        <input
          key="organizationSeach"
          autoFocus={true}
          type="text"
          placeholder="Search"
          value={searchTerm}
          onChange={(e) => handleChange(e)}
          className="pl-4 pr-8 py-2 border-b border-blue-3 text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
          style={{
            borderTop: 'none',
            borderLeft: 'none',
            borderRight: 'none',
          }}
        />
        <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-blue-3" />
      </div>

      {searchTerm.length === 0 && (
        <div className="flex items-center space-x-2 mb-2">
          <input
            type="checkbox"
            id="selectAllOrganizations"
            className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] h-[16.34px]"
            checked={allFilteredSelected}
            onChange={toggleSelectAll}
          />
          <label
            htmlFor="selectAllSubmissionGroups"
            className="text-[12px] font-open-sans font-normal"
          >
            Select All
          </label>
        </div>
      )}

      {filteredOrganizations && (
        <AutoSizer>
          {({ width, height }) => {
            // Calculate height based on the number of items
            const rowHeight = cache.defaultHeight || 30 // Default row height
            const totalHeight = filteredOrganizations.length * rowHeight
            const maxItems = 10

            const constrainedHeight = Math.min(
              totalHeight,
              maxItems * rowHeight
            )

            return (
              <List
                width={width}
                height={constrainedHeight}
                rowHeight={cache.rowHeight}
                rowRenderer={rowRenderer}
                rowCount={filteredOrganizations.length}
                overscanRowCount={3}
              />
            )
          }}
        </AutoSizer>
      )}
    </Section>
  )
}
