import { cn } from '@/lib/utils'
import { Minus, Plus } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'

export const Section = ({
  className,
  title,
  children,
  expanded,
  onToggle,
  customHeight,
}: {
  title: string
  children?: React.ReactNode
  expanded: boolean
  className?: string
  onToggle: () => void
  customHeight?: number
}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const [height, setHeight] = useState<number | undefined>(
    expanded ? undefined : 0
  )

  useEffect(() => {
    if (expanded) {
      const contentEl = contentRef.current
      if (contentEl) {
        const height = contentEl.scrollHeight
        setHeight(height)
      }
    } else {
      setHeight(0)
    }
  }, [expanded, children])

  return (
    <div
      className={cn(
        className,
        'bg-white border border-[#97a5b9] rounded-[3px] overflow-hidden  flex flex-col',
        !expanded && 'border-[#97A4BA]'
      )}
    >
      <div
        className={cn(
          'flex justify-between items-center cursor-pointer px-4 py-2 font-semibold text-blue-3 w-full rounded-md bg-ui-pale-blue hover:bg-white',
          expanded && 'bg-white'
        )}
        onClick={onToggle}
      >
        <span className="font-open-sans font-weight-600 text-[12px] w-full leading-[16.34px]">
          {title}
        </span>
        {expanded ? (
          <Minus className="w-5 h-5" />
        ) : (
          <Plus className="w-5 h-5" />
        )}
      </div>
      <div
        ref={contentRef}
        style={{
          height: expanded && customHeight ? customHeight : height,
          transition: 'height 0.3s ease-in-out',
          overflow: 'hidden',
        }}
      >
        {children && <div className=" p-3 border-[#E2E8F0]">{children}</div>}
      </div>
    </div>
  )
}
