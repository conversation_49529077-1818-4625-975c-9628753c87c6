import { useFilterStore } from '@/stores/filter'
import { ChipSection } from './chipSection'

export const OrganizationsChipSection = () => {
  const { removeOrganization, checkedOrganizations, organizations } =
    useFilterStore()

  return (
    <ChipSection
      title="Organizations"
      allSelected={organizations.length === checkedOrganizations.length}
      items={checkedOrganizations}
      onRemove={removeOrganization}
    />
  )
}
