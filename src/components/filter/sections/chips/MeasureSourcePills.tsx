import React from 'react'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { SourceType } from '@/enums/measureSource'

// Types for props
export interface MeasureSourceItem {
  label: string
  isSelected: boolean
  isVisible: boolean
  applications: string[]
}

// Allowed props
interface MeasureSourcePillsProps {
  measureSource: MeasureSourceItem[]
  handleMeasurePillSelection: (label: string, select: boolean) => void
}

const MeasureSourcePills: React.FC<MeasureSourcePillsProps> = ({
  measureSource,
  handleMeasurePillSelection,
}) => {
  const visibleSources = measureSource.filter((source) => source.isVisible)

  if (visibleSources.length === 0) return null

  return (
    <div className="flex flex-wrap gap-2 mt-4">
      {measureSource.map(
        (item, index) =>
          item.isVisible && (
            <div
              key={index}
              className={cn(
                'flex items-center px-3 py-1 text-ui-dark-gray border border-ui-dark-gray rounded-full text-xs font-bold shadow-sm cursor-pointer transition',
                {
                  'bg-[#DEEEDE] text-[#496049] border-[#DEEEDE]':
                    item.isSelected && item.label === SourceType.ABSTR,
                  'bg-[#D8E4EF] text-[#205782] border-[#D8E4EF]':
                    item.isSelected && item.label === SourceType.ECQM,
                  'bg-[#F8AF64] text-[#683F16] border-[#F8AF64]':
                    item.isSelected && item.label === SourceType.CQM,
                },
                {
                  'hover:bg-[#cfe1cf] hover:border-[#cfe1cf]':
                    item.label === SourceType.ABSTR,
                  'hover:bg-[#cbd8e4] hover:border-[#cbd8e4]':
                    item.label === SourceType.ECQM,
                  'hover:bg-[#e7a75e] hover:border-[#e7a75e]':
                    item.label === SourceType.CQM,
                }
              )}
            >
              <span
                className="text-[10px]"
                onClick={() => handleMeasurePillSelection(item.label, true)}
              >
                {item.label}
              </span>
              {item.isSelected && (
                <X
                  data-testid="remove-chip-button"
                  className="h-[14px] w-[14px] ml-1 cursor-pointer"
                  onClick={() => handleMeasurePillSelection(item.label, false)}
                />
              )}
            </div>
          )
      )}
    </div>
  )
}

export default MeasureSourcePills
