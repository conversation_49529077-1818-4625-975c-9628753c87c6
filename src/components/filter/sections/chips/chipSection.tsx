import { cn } from '@/lib/utils'
import { X } from 'lucide-react'
import { useState } from 'react'

const INITIAL_DISPLAY_COUNT = 20

export const ChipSection = ({
  className,
  title,
  allSelected,
  items,
  noSelectionMessage,
  onRemove,
}: {
  className?: string
  title: string
  allSelected: boolean
  items: { id: string; label: string }[]
  noSelectionMessage?: string
  onRemove: (id: string) => void
}) => {
  const [showAll, setShowAll] = useState(false)
  const displayedItems = showAll ? items : items.slice(0, INITIAL_DISPLAY_COUNT)
  const remainingCount = items.length - INITIAL_DISPLAY_COUNT

  return (
    <div className={cn(className, 'space-y-2')}>
      <h3 className="font-semibold text-[12px] text-blue-1 font-open-sans leading-[16.34px]">
        {title.toUpperCase()}
      </h3>
      <div className="flex flex-wrap gap-2">
        {allSelected && (
          <span className="bg-[#2970A7] text-white text-[10px] px-3 py-1 rounded-[18px] flex items-center gap-2 font-open-sans font-normal leading-[13.62px]">
            {`All ${title} Selected`}
          </span>
        )}

        {!allSelected &&
          (items.length > 0 ? (
            <>
              {displayedItems.map((item, idx) => (
                <span
                  key={idx}
                  className="bg-[#2970A7] text-white text-[10px] px-3 py-1 rounded-[18px] flex items-center gap-2 font-open-sans font-normal leading-[13.62px]"
                >
                  {item.label}
                  <X
                    className="ml-1 h-3 w-3 cursor-pointer text-white"
                    onClick={() => onRemove(item.id)}
                  />
                </span>
              ))}
              {!showAll && items.length > INITIAL_DISPLAY_COUNT && (
                <button
                  onClick={() => setShowAll(true)}
                  className="bg-[#2970A7] text-white text-[10px] px-3 py-1 rounded-[18px] flex items-center gap-2 font-open-sans font-normal leading-[13.62px]"
                >
                  +{remainingCount} more...
                </button>
              )}
            </>
          ) : (
            <span className="bg-[#2970A7] text-white text-[10px] px-3 py-1 rounded-[18px] flex items-center gap-2 font-open-sans font-normal leading-[13.62px]">
              {noSelectionMessage ? noSelectionMessage : 'No Active Filter'}
            </span>
          ))}
      </div>
    </div>
  )
}
