import { useFilterStore } from '@/stores/filter'
import { ChipSection } from './chipSection'

export const ProvidersChipSection = () => {
  const { removeProvider, checkedProviders, providers } = useFilterStore()

  return (
    <ChipSection
      title="Providers"
      allSelected={providers.length === checkedProviders.length}
      items={checkedProviders}
      onRemove={removeProvider}
    />
  )
}
