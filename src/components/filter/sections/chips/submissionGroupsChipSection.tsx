import { useFilterStore } from '@/stores/filter'
import { ChipSection } from './chipSection'
import { check } from 'prettier'

export const SubmissionGroupsChipSection = () => {
  const { removeSubmissionGroup, checkedSubmissionGroups, submissionGroups } =
    useFilterStore()

  return (
    <ChipSection
      title="Submission Groups"
      allSelected={submissionGroups.length === checkedSubmissionGroups.length}
      items={checkedSubmissionGroups}
      onRemove={removeSubmissionGroup}
    />
  )
}
