import { ChipSection } from './chipSection'
import { useFilterStore } from '@/stores/filter'

export const FacilitiesChipSection = () => {
  const { removeFacility, checkedFacilities, facilities } = useFilterStore()

  return (
    <ChipSection
      title="Facilities"
      allSelected={facilities.length === checkedFacilities.length}
      items={checkedFacilities}
      onRemove={removeFacility}
    />
  )
}
