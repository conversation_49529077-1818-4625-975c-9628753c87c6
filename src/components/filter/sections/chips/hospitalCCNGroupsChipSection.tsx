import { useFilterStore } from '@/stores/filter'
import { ChipSection } from './chipSection'

export const HospitalCCNGroupsChipSection = () => {
  const { removeSubOrganization, checkedSubOrganizations, subOrganizations } =
    useFilterStore()

  return (
    <ChipSection
      title="Hospital CCN Groups"
      allSelected={subOrganizations.length === checkedSubOrganizations.length}
      items={checkedSubOrganizations}
      onRemove={removeSubOrganization}
    />
  )
}
