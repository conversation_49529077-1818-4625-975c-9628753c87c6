import { useFilterStore } from '@/stores/filter'
import { ChipSection } from './chipSection'

// This is to display checked saved filters
export const ActiveFiltersChipSection = () => {
  const { checkedSavedFilters, removeSavedFilter, savedFilters } =
    useFilterStore()

  return (
    <ChipSection
      title="Active Filters"
      // allSelected={
      //   savedFilters.length !== 0 &&
      //   savedFilters.length === checkedSavedFilters.length
      // }
      allSelected={false}
      items={checkedSavedFilters}
      onRemove={removeSavedFilter}
    />
  )
}
