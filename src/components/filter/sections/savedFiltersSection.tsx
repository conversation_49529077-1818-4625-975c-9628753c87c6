import { Section } from './section'
import { useFilterStore } from '@/stores/filter'
import { Checkbox } from '@/components/ui/checkbox'
import { Loader2, Trash2 } from 'lucide-react'
import { SavedFilterModel } from '@/types/savedFilterModel'
import { CheckedState } from '@radix-ui/react-checkbox'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { Button } from '@/components/ui/button'
import { api } from '@/trpc/react'
import { useToast } from '@/hooks/use-toast'
import { useState } from 'react'
import { cn } from '@/lib/utils'
import { useUserSessionStore } from '@/stores/userSession'
import appInsights from '@/lib/applicationInsights'

export const SavedFiltersSection = () => {
  const { toast } = useToast()
  const {
    savedFilters,
    expandedSections,
    checkedSavedFilters,
    setExpandedSections,
    addAllMeasures,
    addAllSubOrganizations,
    addAllProviders,
    addAllSubmissionGroups,
    addSavedFilter,
    removeSavedFilter,
  } = useFilterStore()

  const { primaryMeasureType } = useUserSessionStore()

  const [activeFilterId, setActiveFilterId] = useState<string | null>(null)

  const handleEnableSavedFilter = (
    checkedState: CheckedState,
    savedFilter: SavedFilterModel
  ) => {
    setActiveFilterId(checkedState === true ? savedFilter.id! : null) // Set to null when unchecking

    addSavedFilter(savedFilter)

    addAllMeasures(checkedState === true ? savedFilter.measures : undefined)

    if (primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures) {
      addAllSubOrganizations(
        checkedState === true ? savedFilter.hospitals : undefined
      )
    } else {
      addAllSubmissionGroups(
        checkedState === true ? savedFilter.hospitals : undefined
      )
      addAllProviders(checkedState === true ? savedFilter.providers : undefined)
    }
  }

  const deleteFilterMutation = api.filters.deleteFilter.useMutation()

  const trpcUtils = api.useUtils()

  const [deletingId, setDeletingId] = useState<string | null>(null)

  const deleteFilter = async (filterId: string) => {
    setDeletingId(filterId)

    deleteFilterMutation.mutate(
      {
        filterId,
      },
      {
        onSuccess: (data) => {
          removeSavedFilter(filterId)

          trpcUtils.filters.getSavedFilters.invalidate()

          toast({
            className: cn('bg-green-600 text-white border-green-600'),
            title: data.message,
            variant: 'default',
          })

          setDeletingId(null)
        },
        onError: (error) => {
          appInsights.trackException({
            exception: new Error(error.message),
            properties: {
              filterId,
            },
          })
          // reset on error too
          setDeletingId(null)
        },
      }
    )
  }

  return (
    <Section
      title={`SAVED FILTERS`}
      expanded={expandedSections.includes('SAVED FILTERS')}
      onToggle={() => setExpandedSections('SAVED FILTERS')}
    >
      {savedFilters.map((savedFilter, idx) => (
        <div key={idx} className="flex items-center justify-between ">
          <div className="flex items-center space-x-2 pl-[2px]">
            <Checkbox
              id={savedFilter.id!}
              className="rounded border-ui-dark-gray accent-[#1B4A70] w-[13px] "
              checked={activeFilterId === savedFilter.id}
              onCheckedChange={(checked) =>
                handleEnableSavedFilter(checked, savedFilter)
              }
            />
            <label
              htmlFor={savedFilter.id!}
              className="text-[.8rem] font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {savedFilter.filterName}
            </label>
          </div>

          <Button
            className="pr-1"
            onClick={() => deleteFilter(savedFilter.id!)}
            // Disable only if this specific row is being deleted
            disabled={
              deleteFilterMutation.isPending || deletingId === savedFilter.id
            }
            variant="ghost"
          >
            {deletingId === savedFilter.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
          </Button>
        </div>
      ))}
    </Section>
  )
}
