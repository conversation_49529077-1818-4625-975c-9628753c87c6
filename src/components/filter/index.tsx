import { useState, useRef, useEffect } from 'react'
import { useFilterStore } from '@/stores/filter'
import { api } from '@/trpc/react'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { Header } from './header'
import { SavedFiltersSection } from './sections/savedFiltersSection'
import { MeasuresSection } from './sections/measuresSection'
import { SubmissionGroupsSection } from './sections/submissionGroupsSection'
import { ProvidersSection } from './sections/providersSection'
import { SaveFilterSettingsSection } from './sections/saveFilterSettingsSection'
import { Footer } from './footer'
import { ActiveFiltersChipSection } from './sections/chips/activeFiltersChipSection'
import { MeasuresChipSection } from './sections/chips/measuresChipSection'
import { SubmissionGroupsChipSection } from './sections/chips/submissionGroupsChipSection'
import { ProvidersChipSection } from './sections/chips/providersChipSection'
import { useClickOutside } from '@/hooks/useClickOutside'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { HospitalCCNGroupsChipSection } from './sections/chips/hospitalCCNGroupsChipSection'
import { HospitalCCNGroupsSection } from './sections/hospitalCCNGroupsSection'
import { useViewStore } from '@/stores/viewStore'
import { useUserSessionStore } from '@/stores/userSession'
import { SelectionType } from '@/enums/selectionType'
import { OrganizationsSection } from './sections/organizationsSection'
import { OrganizationsChipSection } from './sections/chips/organizationsChipSection'
import { useStore } from 'zustand'
import { useDateStore } from '@/stores/dates'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { FacilitiesSection } from './sections/facilitiesSection'
import { FacilitiesChipSection } from './sections/chips/facilitiesChipSection'

dayjs.extend(utc)

const Filter = () => {
  const { expandedSections, setExpandedSections, setInFlux } = useFilterStore()
  const pathname = usePathname()
  const ref = useRef<HTMLDivElement>(null)
  const scrollContainer = useRef<HTMLDivElement>(null)

  const [isFilterOpen, setIsFilterOpen] = useState(false)

  const { currentView } = useViewStore()

  useClickOutside(ref, () => setIsFilterOpen(false))

  const { primaryMeasureType, selectionType } = useUserSessionStore()
  const dateStore = useStore(useDateStore, (state) => state)
  const [
    measuresQuery,
    savedFiltersQuery,
    subOrganizationsQuery,
    providersQuery,
    submissionGroupQuery,
    organizationsQuery,
    facilitiesQuery,
  ] = api.useQueries((t) => [
    t.measures.get(
      {
        primaryMeasureType,
        periodStartDate: dayjs.utc(dateStore.selectedRange[0]).toDate(),
        periodEndDate: dayjs.utc(dateStore.selectedRange[1]).toDate(),
      },
      {
        enabled:
          !!primaryMeasureType &&
          !!dateStore?.selectedRange?.[0] &&
          !!dateStore?.selectedRange?.[1],
      }
    ),
    t.filters.getSavedFilters({
      path: pathname,
      primaryMeasureType,
    }),
    t.organizations.getSubOrganizations(),
    t.providers.get({
      primaryMeasureType: primaryMeasureType,
      enabled: ['/measures'].includes(pathname),
      startDate: dayjs.utc(dateStore.selectedRange[0]).toDate(),
      endDate: dayjs.utc(dateStore.selectedRange[1]).toDate(),
    }, {
      enabled: 
          !!dateStore?.selectedRange?.[0] &&
          !!dateStore?.selectedRange?.[1] &&
          ['/measures'].includes(pathname),
    }),
     t.submissionGroups.get({
      startDate: dayjs.utc(dateStore.selectedRange[0]).toDate(),
      endDate: dayjs.utc(dateStore.selectedRange[1]).toDate(),
     }, {
      enabled: 
          !!dateStore?.selectedRange?.[0] &&
          !!dateStore?.selectedRange?.[1],
    }),
    t.organizations.get({
      primaryMeasureType: primaryMeasureType,
    }),
    t.facilities.get({
      primaryMeasureType,
    }),
  ])

  const {
    setMeasures,
    setSavedFilters,
    addAllMeasures,
    addAllSubmissionGroups,
    addAllProviders,
    addAllSubOrganizations,
    setSubmissionGroups,
    setProviders,
    setSubOrganizations,
    setOrganizations,
    addAllOrganizations,
    setFacilities,
    addAllFacilities,
    applyFilters,
    appliedFilters
  } = useFilterStore()

  useEffect(() => {
    setInFlux(true)
  }, [])

  useEffect(() => {
    if (measuresQuery.data) {
      setMeasures(measuresQuery.data)
      addAllMeasures(currentView?.settings?.measures ?? ['*'])
    }

    if (savedFiltersQuery.data) {
      setSavedFilters(savedFiltersQuery.data)
    }

    if (primaryMeasureType === PrimaryMeasureTypeConstants.AmbulatoryMeasures) {
      if (submissionGroupQuery.data) {
        setSubmissionGroups(submissionGroupQuery.data)
        addAllSubmissionGroups(currentView?.settings?.submissionGroups ?? ['*'])
      }

      if (providersQuery.data) {
        setProviders(providersQuery.data)
        addAllProviders(currentView?.settings?.providers ?? ['*'])
      }

      if (organizationsQuery.data) {
        setOrganizations(organizationsQuery.data)
        addAllOrganizations(currentView?.settings?.organizations ?? ['*'])
      }
    } else if (
      primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures
    ) {
      if (subOrganizationsQuery.data) {
        setSubOrganizations(subOrganizationsQuery.data)
        addAllSubOrganizations(currentView?.settings?.hospitals ?? ['*'])
      }
      if (facilitiesQuery.data) {
        setFacilities(facilitiesQuery.data.facilities)
        addAllFacilities(currentView?.settings?.facilities ?? ['*'])
      }
    }

    setInFlux(savedFiltersQuery.isPending || organizationsQuery.isPending)
    applyFilters()
  }, [
    measuresQuery.data,
    savedFiltersQuery.data,
    providersQuery.data,
    submissionGroupQuery.data,
    subOrganizationsQuery.data,
    organizationsQuery.data,
    facilitiesQuery.data,
    primaryMeasureType,
    currentView?.id,
    dateStore?.selectedRange?.[0],
    dateStore?.selectedRange?.[1],
  ])

  const toggleFilter = () => {
    if (!isFilterOpen) setExpandedSections('')
    setIsFilterOpen(!isFilterOpen)
  }

  useEffect(() => {
    if (isFilterOpen && scrollContainer.current) {
      scrollContainer.current?.scrollTo(0, 0)
    }
  }, [isFilterOpen])
  return (
    <>
      <div
        ref={ref}
        className={`relative ${isFilterOpen ? '' : 'rounded-[5px] text-[#566582] hover:bg-white border border-white hover:border-ui-dark-gray px-4 py-1'}`}
      >
        <Image
          className="object-cover cursor-pointer"
          alt="logo"
          src="/images/filter-action-icon.svg"
          width={85}
          height={28}
          onClick={toggleFilter}
        />

        {isFilterOpen && (
          <div className="absolute overflow-hidden top-0 z-20 w-[360px] font-sans text-[#2c3e50] bg-ghostwhite !shadow-[0px_0px_7px_5px_rgba(0,_0,_0,_0.1)] rounded flex flex-col h-[calc(100vh-320px)]">
            <div
              className="flex-1 overflow-y-auto mac-scrollbar"
              ref={scrollContainer}
            >
              <Header setIsFilterOpen={setIsFilterOpen} />

              <div className="space-y-5 px-4">
                <SavedFiltersSection />

                <MeasuresSection />

                {PrimaryMeasureTypeConstants.AmbulatoryMeasures ===
                  primaryMeasureType && (
                  <>
                    <SubmissionGroupsSection />

                    {selectionType === SelectionType.Partner && (
                      <OrganizationsSection />
                    )}
                  </>
                )}
                {PrimaryMeasureTypeConstants.AmbulatoryMeasures ===
                  primaryMeasureType &&
                  ['/measures'].includes(pathname) && <ProvidersSection />}

                {PrimaryMeasureTypeConstants.HospitalMeasures ===
                  primaryMeasureType && (
                  <>
                    <HospitalCCNGroupsSection />

                    {facilitiesQuery.data?.useSpecialEntityStructure && (
                      <FacilitiesSection />
                    )}
                  </>
                )}

                <ActiveFiltersChipSection />

                <MeasuresChipSection />

                {primaryMeasureType ===
                  PrimaryMeasureTypeConstants.AmbulatoryMeasures && (
                  <>
                    {selectionType === SelectionType.Partner && (
                      <OrganizationsChipSection />
                    )}
                    <SubmissionGroupsChipSection />
                  </>
                )}

                {PrimaryMeasureTypeConstants.AmbulatoryMeasures ===
                  primaryMeasureType &&
                  ['measures'].includes(pathname) && <ProvidersChipSection />}

                {primaryMeasureType ===
                  PrimaryMeasureTypeConstants.HospitalMeasures && (
                  <>
                    <HospitalCCNGroupsChipSection />
                    {facilitiesQuery.data?.useSpecialEntityStructure && (
                      <FacilitiesChipSection />
                    )}
                  </>
                )}

                <SaveFilterSettingsSection />
              </div>
            </div>

            <Footer
              useSpecialEntityStructure={
                facilitiesQuery.data?.useSpecialEntityStructure
              }
              setIsFilterOpen={setIsFilterOpen}
            />
          </div>
        )}
      </div>
    </>
  )
}

export default Filter
