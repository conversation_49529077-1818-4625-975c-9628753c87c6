import { ListFilter, X } from 'lucide-react'

type Props = {
  setIsFilterOpen: (value: boolean) => void
}

export const Header = ({ setIsFilterOpen }: Props) => (
  <div className="flex justify-between items-center mb-4 p-4">
    <h2 className="text-[13px] font-semibold flex items-center text-[#393939] font-open-sans leading-[17.7px]">
      <ListFilter className="w-5 h-5 mr-2" />
      FILTER
    </h2>
    <X
      className="cursor-pointer w-5 h-5"
      onClick={() => setIsFilterOpen(false)}
    />
  </div>
)
