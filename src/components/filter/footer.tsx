import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { ScorecardView } from '@/enums/scorecardView'
import { SelectionType } from '@/enums/selectionType'
import { useToast } from '@/hooks/use-toast'
import { useDateStore } from '@/stores/dates'
import { useFilterStore } from '@/stores/filter'
import { useMeasureResultsStore } from '@/stores/measuresResultsStore'
import { useUserSessionStore } from '@/stores/userSession'
import useStore from '@/stores/useStore'
import { useViewStore } from '@/stores/viewStore'
import { api } from '@/trpc/react'
import dayjs from 'dayjs'
import { usePathname } from 'next/navigation'
import { useMemo } from 'react'

type FooterProps = {
  useSpecialEntityStructure?: boolean
  setIsFilterOpen?: (value: boolean) => void
}

export const Footer = ({
  setIsFilterOpen,
  useSpecialEntityStructure,
}: FooterProps) => {
  const { toast } = useToast()
  const {
    clearFilters,
    applyFilters,
    checkedMeasures,
    checkedProviders,
    checkedSubOrganizations,
    checkedSubmissionGroups,
    checkedOrganizations,
    checkedFacilities,
    hideEmptyIndicators,
    appliedFilters,
  } = useFilterStore()

  const { currentView } = useViewStore()
  const pathname = usePathname()
  const { primaryMeasureType, selectionType } = useUserSessionStore()

  const clearAllFilters = () => {
    clearFilters(currentView?.settings ?? undefined)
  }

  const { setMeasureResults } = useMeasureResultsStore()

  const dateStore = useStore(useDateStore, (state) => state)

  const memoizedFilters = useMemo(
    () =>
      Object.entries(appliedFilters).reduce(
        (acc, [key, value]) => {
          acc[key as keyof typeof acc] = value
          return acc
        },
        {} as Record<
          'measures' | 'subOrganizations' | 'submissionGroups' | 'providers',
          string[]
        >
      ),
    [appliedFilters]
  )

  const calculatedMeasuresMutation = api.measures.calculateResults.useMutation()

  const apply = async () => {
    const validationRules = [
      {
        condition: checkedMeasures.length === 0,
        message: 'Select at least 1 measure',
      },
      {
        condition:
          primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures &&
          checkedSubOrganizations.length === 0,
        message: 'Select at least 1 hospital',
      },
      {
        condition:
          primaryMeasureType ===
            PrimaryMeasureTypeConstants.AmbulatoryMeasures &&
          checkedSubmissionGroups.length === 0,
        message: 'Select at least 1 submission group',
      },
      {
        condition:
          primaryMeasureType ===
            PrimaryMeasureTypeConstants.AmbulatoryMeasures &&
          checkedProviders.length === 0 &&
          ['measures'].includes(pathname),
        message: 'Select at least 1 provider',
      },
      {
        condition:
          primaryMeasureType ===
            PrimaryMeasureTypeConstants.AmbulatoryMeasures &&
          checkedProviders.length === 0 &&
          selectionType === SelectionType.Partner,
        message: 'Select at least 1 organization',
      },
      {
        condition:
          useSpecialEntityStructure &&
          primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures &&
          checkedFacilities.length === 0,
        message: 'Select at least 1 facility',
      },
    ]

    // Check all validation rules
    for (const rule of validationRules) {
      if (rule.condition) {
        toast({
          title: rule.message,
          variant: 'destructive',
        })
        return // Exit early if any rule fails
      }
    }

    setIsFilterOpen?.(false)

    applyFilters()

    const measureResults = await calculatedMeasuresMutation.mutateAsync({
      primaryMeasureType,
      startDate: dayjs(dateStore?.selectedRange[0])?.utc().toISOString()!,
      endDate: dayjs(dateStore?.selectedRange[1])?.utc().toISOString()!,
      hideEmptyIndicators,
      aggregationType: Object.entries(ScorecardView).find(
        (entry) => entry[0] === dateStore?.selectedPeriod
      )?.[1]!,
      filters: {
        measures: checkedMeasures.map((measure) => measure.id),
        subOrganizations: checkedSubOrganizations.map(
          (subOrganization) => subOrganization.id
        ),
        organizations: checkedOrganizations.map((org) => org.id),
      },
    })

    setMeasureResults(measureResults)
  }

  return (
    <div className="flex justify-between items-center p-4 bg-[#dee2f0] overflow-hidden shadow-[0px_0px_5px_5px_rgba(0,0,0,0.2)]">
      <button
        onClick={clearAllFilters}
        className="text-[#1B4A70] text-[12px] font-normal underline"
      >
        Clear All
      </button>
      <button
        onClick={apply}
        className="bg-blue-3 text-white w-[248px] h-[31px] rounded-[5px] font-open-sans font-semibold text-[12px] leading-[16.34px]"
      >
        APPLY CHANGES
      </button>
    </div>
  )
}
