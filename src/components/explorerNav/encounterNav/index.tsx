'use client'

import { ExplorerTabValue } from '@/types/explorerTabValue'
import { Button } from '../../ui/button'
import { Dispatch, SetStateAction } from 'react'
import Image from 'next/image'

type Props = {
  activeTab: ExplorerTabValue | undefined
  setActiveTab: Dispatch<SetStateAction<ExplorerTabValue | undefined>>
}

export const EncounterNav = ({ activeTab, setActiveTab }: Props) => {
  const tabs = [
    {
      icon: '/images/list.svg',
      label: 'Encounter Summary',
      value: 'encounterSummary',
    },
    {
      icon: '/images/topic.svg',
      label: 'Encounter Measures',
      value: 'encounterMeasures',
    },
    {
      icon: '/images/prescriptions.svg',
      label: 'Diagnoses',
      value: 'diagnoses',
    },
    {
      icon: '/images/procedure.svg',
      label: 'Procedures',
      value: 'procedures',
    },
    {
      icon: '/images/checkbook.svg',
      label: 'Insurance',
      value: 'insurance',
    },
  ] as const

  return (
    <>
      <h1 className="font-open-sans font-bold text-[18.09px] leading-[24.64px] text-end text-blue-3 mt-[45px] mb-[25px] px-4">
        Encounter
      </h1>

      <div className="flex flex-col">
        {tabs.map((tab) => (
          <Button
            key={tab.value}
            variant={'ghost'}
            className={`justify-end ${activeTab === tab.value ? 'bg-ui-pale-blue text-[#074880] border-r-[#2970A7] border-r-[4px] rounded-none' : ''}`}
            onClick={() => setActiveTab(tab.value)}
          >
            <Image src={tab.icon} alt="All Measures" width={19} height={19} />
            {tab.label}
          </Button>
        ))}
      </div>
    </>
  )
}
