'use client'

import { MoveLeft } from 'lucide-react'
import Link from 'next/link'
import { EncounterNav } from './encounterNav'
import { PatientNav } from './patientNav'
import { useRouter, useSearchParams } from 'next/navigation'
import { Dispatch, SetStateAction } from 'react'
import { ExplorerTabValue } from '@/types/explorerTabValue'

type Props = {
  activeTab?: ExplorerTabValue
  setActiveTab: Dispatch<SetStateAction<ExplorerTabValue | undefined>>
}

export const ExplorerNav = ({ activeTab, setActiveTab }: Props) => {
  const searchParams = useSearchParams()
  const router = useRouter()

  const searchType = searchParams.get('type')

  return (
    <div className="min-w-[200px]">
      <div className="pr-4">
        <Link
          onClick={router.back}
          href="#"
          className="flex space-x-2 items-center text-ui-dark-gray nowrap justify-between"
        >
          <MoveLeft size={24} />
          <span className="font-open-sans text-[13px] font-semibold leading-[17.7px] text-center nowrap">
            {`${searchType!.slice(0, -1)} Search`.toUpperCase()}
          </span>
        </Link>
      </div>

      <PatientNav activeTab={activeTab} setActiveTab={setActiveTab} />

      {searchType === 'encounters' && (
        <EncounterNav activeTab={activeTab} setActiveTab={setActiveTab} />
      )}
    </div>
  )
}
