'use client'

import React, { useEffect } from 'react'

import Filter from '../filter'
import { useFilterStore } from '@/stores/filter'
import { useUIStore } from '@/stores/ui'
import DashboardActionsMenu from "@/components/dashboard/DashboardActionsMenu";

type Props = {
  showMeasureTypeToggle?: boolean
}

const DashboardActionBar = ({ showMeasureTypeToggle }: Props) => {
  const { clearAll, applyFilters } = useFilterStore()
  const { scorecardViewType, setScorecardViewType } = useUIStore()

  const handleClearAll = () => {
    clearAll()
    applyFilters()
  }

  function toggleMeasureView(
    event: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ): void {
    event.preventDefault()
    setScorecardViewType(
      scorecardViewType === 'Hospital' ? 'Measure' : 'Hospital'
    )
  }

  return (
    <div className="relative flex w-full justify-between h-[36px] pb-[1rem]">
      <div className="flex items-center">
        <Filter />

      </div>

      <div className="flex items-center justify-end">
        <DashboardActionsMenu />
      </div>
    </div>
  )
}

export default DashboardActionBar
