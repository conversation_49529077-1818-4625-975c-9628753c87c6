'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Search, Star, ChevronDown, ChevronUp, Plus } from 'lucide-react'
import { api } from '@/trpc/react'
import { cn } from '@/lib/utils'
import { useDashboardStore } from "@/stores/dashboardStore";

type DashboardSelectorProps = {
    className?: string
}

const DashboardSelector = ({ className }: DashboardSelectorProps) => {
    const [isOpen, setIsOpen] = useState(false)
    const [searchQuery, setSearchQuery] = useState('')
    const { showDashboardConfiguration, setShowDashboardConfiguration } = useDashboardStore()
    const dropdownRef = useRef<HTMLDivElement>(null)

    // Fetch dashboards data
    const { data: dashboards = [], isLoading } = api.admin.getAllDashboards.useQuery()

    // Define dashboard type based on the API response
    type Dashboard = {
        rowKey?: string;
        name?: string;
        isDefault?: boolean;
    }

    // Filter dashboards based on search query
    const filteredDashboards = dashboards.filter((dashboard: Dashboard) =>
        dashboard.name?.toLowerCase().includes(searchQuery.toLowerCase())
    )

    // Handle click outside to close dropdown
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false)
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [])

    // Reset dashboard configuration on unmount
    useEffect(() => {
        return () => {
            setShowDashboardConfiguration(false)
        }
    }, [setShowDashboardConfiguration])

    // Toggle dropdown
    const toggleDropdown = () => {
        setIsOpen(!isOpen)
        if (showDashboardConfiguration) {
            setShowDashboardConfiguration(false)
        }
    }

    // Handle "See All Dashboards" click
    const handleSeeAllDashboards = () => {
        setShowDashboardConfiguration(true)
        setIsOpen(false)
    }

    return (
        <div className="flex flex-row border-t border-[#fdfdfd] text-white mt-4 ">
            <div className="flex justify-center align-middle items-center content-center min-w-full">
                <div className="mx-auto  relative">
                    <div className={cn("relative", className)} ref={dropdownRef}>
                        {/* Collapsed state */}
                        <button
                            onClick={toggleDropdown}
                            className="flex items-center justify-between w-full px-4 py-2 text-white bg-[#13497C] hover:bg-[#0e3a63] transition-colors"
                        >
                            <span className="font-semibold">DEFAULT SPOTLIGHT DASHBOARD</span>
                            {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                        </button>

                        {/* Expanded state */}
                        {isOpen && (
                            <div className="absolute z-50 w-full bg-[#13497C] text-white shadow-lg">
                                {/* Search bar */}
                                <div className="p-3 border-b border-[#2a5d8f]">
                                    <div className="relative">
                                        <input
                                            type="text"
                                            placeholder="Search Favorite Dashboards"
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                            className="w-full p-2 pl-3 pr-10 text-sm bg-[#0e3a63] border border-[#2a5d8f] rounded focus:outline-none focus:ring-1 focus:ring-[#4a7ca8] text-white placeholder-gray-300"
                                        />
                                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                            <Search size={16} className="text-gray-300" />
                                        </div>
                                    </div>
                                </div>

                                {/* Dashboard list */}
                                <div className="max-h-60 overflow-y-auto">
                                    {isLoading ? (
                                        <div className="p-3 text-center">Loading dashboards...</div>
                                    ) : (
                                        <ul>
                                            {filteredDashboards.map((dashboard: Dashboard) => (
                                                <li
                                                    key={dashboard.rowKey}
                                                    className="flex items-center px-3 py-2 hover:bg-[#0e3a63] cursor-pointer"
                                                >
                                                    <Star size={16} className="mr-2 text-yellow-400" />
                                                    <span className="flex-1">{dashboard.name}</span>
                                                    {dashboard.isDefault && (
                                                        <span className="px-2 py-0.5 text-xs bg-gray-600 rounded">DEFAULT</span>
                                                    )}
                                                </li>
                                            ))}
                                        </ul>
                                    )}
                                </div>

                                {/* Footer options */}
                                <div className="border-t border-[#2a5d8f]">
                                    <button
                                        onClick={handleSeeAllDashboards}
                                        className="flex items-center w-full px-3 py-2 text-left hover:bg-[#0e3a63]"
                                    >
                                        <Star size={16} className="mr-2 text-white" />
                                        <span>See All Dashboards</span>
                                    </button>
                                    <button
                                        className="flex items-center w-full px-3 py-2 text-left hover:bg-[#0e3a63]"
                                    >
                                        <Plus size={16} className="mr-2" />
                                        <span>Add New Dashboard</span>
                                    </button>
                                </div>
                            </div>
                        )}

                    </div>
                </div>
            </div>
        </div>
    )
}

export default DashboardSelector
