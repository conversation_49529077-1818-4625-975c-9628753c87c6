'use client'

import React, {useEffect} from 'react'
import { useDashboardStore } from "@/stores/dashboardStore";
import {api} from "@/trpc/react";

const DashboardReport = () => {
    const { setCurrentDashboardId } = useDashboardStore()
    const allDashboards = api.admin.getAllDashboards.useQuery()

    useEffect(() => {
        if (!allDashboards.isSuccess || allDashboards.data.length === 0) return
        const firstDashboard = allDashboards.data[0]!
        setCurrentDashboardId(firstDashboard.rowKey)
    }, [allDashboards.data]);

    return (
        <div className="flex flex-col w-full font-open-sans text-sm">
            <h1 className="text-3xl font-bold text-center my-6">Lorem ipsum dolor sit amet, consectetur adipiscing elit</h1>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                <div className="p-6 bg-white border border-[#dedede] rounded-sm shadow-md">
                    <h3 className="text-center font-medium mb-4">Measures Selected</h3>
                    <p className="text-[40px] text-[#2D4A86] text-center font-bold">4</p>
                </div>

                <div className="p-6 bg-white border border-[#dedede] rounded-sm shadow-md">
                    <h3 className="text-center font-medium mb-4">Entities Selected</h3>
                    <p className="text-[40px] text-[#2D4A86] text-center font-bold">11</p>
                </div>

                <div className="p-6 bg-white border border-[#dedede] rounded-sm shadow-md">
                    <h3 className="text-center font-medium mb-4">Missed Goals</h3>
                    <p className="text-[40px] text-[#2D4A86] text-center font-bold">0</p>
                </div>

                <div className="p-6 bg-white border border-[#dedede] rounded-sm shadow-md">
                    <h3 className="text-center font-medium mb-4">SPC Alerts</h3>
                    <p className="text-[40px] text-[#2D4A86] text-center font-bold">0</p>
                </div>

                <div className="bg-white border border-[#dedede] rounded-sm shadow-md p-0">
                    <div className="bg-[#2d7cba] h-[8px] w-full"></div>
                    <div className="p-6 mt-[-8px]">
                        <h3 className="text-center font-medium mb-4">Insight Reports Available</h3>
                        <p className="text-[40px] text-[#2D4A86] text-center font-bold">1</p>
                    </div>
                </div>
            </div>

            {/* Data Table */}
            <div className="bg-white border border-[#dedede] rounded-sm shadow-md p-4 overflow-x-auto ">
                <table className="w-full border-collapse">
                    <thead>
                        <tr className="bg-[#F9F8F9]">
                            <th className="border-[#DDDCDF] center border-t border-b border-r border-l px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">
                                Entity (<b>11</b>)
                            </th>
                            <th className="border-[#DDDCDF] border-t border-b border-r border-l px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">
                                Discharged on Antithrombotic
                            </th>
                            <th className="border-[#DDDCDF] border-t border-b border-r border-l px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">
                                Safe Use of Opioids
                            </th>
                            <th className="border-[#DDDCDF] border-t border-b border-r border-l px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">
                                Venous Thromboembolism
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr className="bg-white">
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                Memorial Hermann Cypress Hospital
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-[#e37878]" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                24
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-600 text-white" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                9
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-100" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                65
                            </td>
                        </tr>
                        <tr className="bg-[#F9F8F9]">
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                Memorial Hermann Greater Heights Hospital
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-200" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                68
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-600 text-white" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                8
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-100" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                70
                            </td>
                        </tr>
                        <tr className="bg-white">
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                Memorial Hermann Memorial City Medical Center
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-red-200" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                34
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-600 text-white" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                5
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-100" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                54
                            </td>
                        </tr>
                        <tr className="bg-[#F9F8F9]">
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                Memorial Hermann Northeast Hospital
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-[#e37878]" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                25
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-600 text-white" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                8
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-100" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                59
                            </td>
                        </tr>
                        <tr className="bg-white">
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                Memorial Hermann Orthopedic & Spine Hospital
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans" style={{ fontSize: '14px', padding: '10px 24px' }}>
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-600 text-white" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                43
                            </td>
                            <td className="p-2 font-normal text-center border-[#DDDCDF] border-b border-r border-l align-middle font-open-sans bg-green-800 text-white" style={{ fontSize: '14px', padding: '10px 24px' }}>
                                98
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    )
}

export default DashboardReport
