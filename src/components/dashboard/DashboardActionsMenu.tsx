'use client'

import React, { useRef, useState } from 'react'
import { ChevronDown, ChevronUp, Loader2, Minus, Plus } from 'lucide-react'
import { api } from '@/trpc/react'
import { useClickOutside } from '@/hooks/useClickOutside'
import { cn } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'
import { ChipSection } from '../filter/sections/chips/chipSection'
import { User } from '@/types/user'
import PickerButton from '@/components/ui/PickerButton'
import { useDashboardStore } from '@/stores/dashboardStore'

export const DashboardActionsMenu = () => {
  const { toast } = useToast()
  const [isExpanded, setIsExpanded] = useState(false)
  const [expandedAction, setExpandingAction] = useState('')
  const [isUsersExpanded, setIsUsersExpanded] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<
    { id: string; label: string }[]
  >([])
  const { currentDashboardId } = useDashboardStore()

  const ref = useRef<HTMLDivElement>(null)
  useClickOutside(ref, () => setIsExpanded(false))

  const usersApi = api.users.getOrganizationUsers.useQuery()

  const shareDashboardAPI = api.admin.shareDashboard.useMutation()

  const handleShareDashboard = async () => {
    if (selectedUsers.length == 0) {
      toast({
        title: 'Select at least 1 user',
        variant: 'default',
      })
    }
    selectedUsers.filter(async (user) => {
      await shareDashboardAPI.mutateAsync({
        dashboardId: currentDashboardId,
        userId: user.id,
      })
    })
    setIsExpanded(false)
    return
  }

  const removeUser = (Id: string) => {
    setSelectedUsers(selectedUsers.filter((x) => x.id != Id))
  }

  const handleUserSelect = (user: User) => {
    if (selectedUsers.find((e) => e.id === user.userId)) {
      setIsUsersExpanded(false)
      return
    }

    setSelectedUsers([
      ...selectedUsers,
      { id: user.userId!, label: user.displayName! },
    ])
    setIsUsersExpanded(false)
  }

  return (
    <div ref={ref} className="relative">
      <PickerButton text="ACTIONS">
        <div className="p-4">
          <div className="mb-2">
            <div
              className={cn(
                expandedAction == 'Share' ? 'bg-white' : '',
                'border-ui-dark-gray border-[0.5px] rounded p-2 outline-none hover:bg-white'
              )}
            >
              <div
                className="w-full text-[#1B4A70] font-semibold flex justify-between items-center text-[12px]"
                onClick={() => setExpandingAction('Share')}
              >
                Share
                {expandedAction == 'Share' ? (
                  <Minus className="w-5 h-5" />
                ) : (
                  <Plus className="w-5 h-5" />
                )}
                {expandedAction == 'Share' && (
                  <div className="flex flex-col pt-2">
                    <span className="px-1 mb-2 font-open-sans font-weight-400 text-[10px]">
                      User*
                    </span>
                    <div className="w-full">
                      <button
                        onClick={() => setIsUsersExpanded(!isUsersExpanded)}
                        className="w-full border-[0.5px] border-[solid] border-[#97A4BA] rounded-[4px] flex justify-between items-center px-2 py-1 text-ui-dark-gray"
                      >
                        <span className="flex font-open-sans font-weight-400 text-[10px] text-[#787878]">
                          Select User
                        </span>
                        {isUsersExpanded ? (
                          <ChevronUp
                            className="w-5 h-5 font-semibold"
                            strokeWidth={2}
                          />
                        ) : (
                          <ChevronDown
                            className="w-5 h-5 font-semibold"
                            strokeWidth={2}
                          />
                        )}
                      </button>
                    </div>
                    {isUsersExpanded && (
                      <div className="w-full flex flex-col px-2 pt-2 h-[150px] overflow-y-auto">
                        {usersApi?.data?.map((user: User) => (
                          <span
                            onClick={() => handleUserSelect(user)}
                            key={user.userId}
                            className="font-weight-400 text-[10px] py-1 hover:cursor-pointer"
                          >
                            {user.displayName}
                          </span>
                        ))}
                      </div>
                    )}
                    <button
                      disabled={shareDashboardAPI.isPending}
                      className="w-[77px] font-semibold text-[10px] text-ui-dark-gray py-[4px] mt-4 border-[0.8px] border-[solid] border-ui-dark-gray rounded-[4px]"
                      onClick={handleShareDashboard}
                    >
                      {shareDashboardAPI.isPending ? (
                        <Loader2 size={13} className="animate-spin" />
                      ) : (
                        'SEND'
                      )}
                    </button>
                    <span className="px-1 mt-2 font-open-sans font-weight-400 text-[10px] text-[#13497C]">
                      Sharing With:
                    </span>
                    <div className="w-full mb-2">
                      <ChipSection
                        title=""
                        allSelected={false}
                        items={selectedUsers}
                        onRemove={removeUser}
                        noSelectionMessage="No Users Selected"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </PickerButton>
    </div>
  )
}

export default DashboardActionsMenu
