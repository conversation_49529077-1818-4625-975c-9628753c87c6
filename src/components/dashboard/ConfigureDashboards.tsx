'use client'

import React, { useState } from 'react'
import { X, Plus, Minus } from 'lucide-react'
import { api } from '@/trpc/react'
import { cn } from '@/lib/utils'
import { useDashboardStore } from "@/stores/dashboardStore";

export const ConfigureDashboards = () => {
    const [expandedDashboard, setExpandedDashboard] = useState<string | null>(null)
    const [showAddForm, setShowAddForm] = useState(false)
    const [newDashboardName, setNewDashboardName] = useState('')
    const [newDashboardDescription, setNewDashboardDescription] = useState('')
    const { showDashboardConfiguration, setShowDashboardConfiguration } = useDashboardStore()


    // Fetch all dashboards
    const { data: dashboards = [], isLoading, refetch } = api.admin.getAllDashboards.useQuery()

    // Add dashboard mutation
    const addDashboardMutation = api.admin.addDashboard.useMutation({
        onSuccess: () => {
            // Reset form and refetch dashboards
            setNewDashboardName('')
            setNewDashboardDescription('')
            setShowAddForm(false)
            refetch()
        },
    })

    const handleAddDashboard = () => {
        if (!newDashboardName.trim()) return

        addDashboardMutation.mutate({
            name: newDashboardName,
            description: newDashboardDescription,
            configuration: JSON.stringify({}) // Default empty configuration
        })
    }

    const closeConfigureDashboard = async () => {
        await setShowDashboardConfiguration(false)
    }

    const toggleDashboard = (id: string) => {
        if (expandedDashboard === id) {
            setExpandedDashboard(null)
        } else {
            setExpandedDashboard(id)
        }
    }

    return showDashboardConfiguration ? (
        <div className="absolute inset-0 z-50 flex items-start justify-start bg-black bg-opacity-50 font-open-sans">
            <div className="flex flex-col w-full max-w-md bg-[#F5F7FE] border border-gray-200 rounded-md shadow-md mt-[20px] ml-[90px] ">
                {/* Header */}
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-800">CONFIGURE DASHBOARDS</h2>
                    <button
                        onClick={closeConfigureDashboard}
                        className="text-gray-500 hover:text-gray-700"
                        aria-label="Close"
                    >
                        <X size={20} />
                    </button>
                </div>

                {/* Search */}
                <div className="p-4 border-b border-gray-200">
                    <div className="relative">
                        <input
                            type="text"
                            placeholder="Search All Dashboards"
                            className="w-full p-2 pl-4 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-y-auto">
                    {/* My Dashboards Section */}
                    <div className="p-4">
                        <div className="flex justify-between items-center mb-2">
                            <h3 className="text-md font-semibold text-[#2D4A86]">MY DASHBOARDS</h3>
                            <button
                                onClick={() => setShowAddForm(!showAddForm)}
                                className="text-[#2D4A86] hover:text-blue-700"
                                aria-label={showAddForm ? "Hide add dashboard form" : "Show add dashboard form"}
                            >
                                {showAddForm ? <Minus size={20} /> : <Plus size={20} />}
                            </button>
                        </div>

                        {/* Add Dashboard Form */}
                        {showAddForm && (
                            <div className="mb-4 p-3 bg-white border border-gray-200 rounded-md">
                                <div className="mb-3">
                                    <input
                                        type="text"
                                        placeholder="Dashboard Name"
                                        value={newDashboardName}
                                        onChange={(e) => setNewDashboardName(e.target.value)}
                                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                    />
                                </div>
                                <div className="mb-3">
                                    <textarea
                                        placeholder="Description"
                                        value={newDashboardDescription}
                                        onChange={(e) => setNewDashboardDescription(e.target.value)}
                                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                        rows={2}
                                    />
                                </div>
                                <button
                                    onClick={handleAddDashboard}
                                    disabled={addDashboardMutation.isPending}
                                    className={cn(
                                        "w-full p-2 text-white bg-[#2D4A86] rounded-md hover:bg-blue-700 focus:outline-none",
                                        addDashboardMutation.isPending && "opacity-50 cursor-not-allowed"
                                    )}
                                >
                                    {addDashboardMutation.isPending ? "Adding..." : "Add"}
                                </button>
                            </div>
                        )}

                        {/* Dashboard List */}
                        {isLoading ? (
                            <div className="text-center py-4">Loading dashboards...</div>
                        ) : (
                            <div className="space-y-2">
                                {dashboards.map((dashboard) => (
                                    <div key={dashboard.rowKey} className="border border-gray-200 rounded-md overflow-hidden">
                                        <div
                                            className="flex justify-between items-center p-3 bg-white cursor-pointer"
                                            onClick={() => toggleDashboard(dashboard.rowKey as string)}
                                        >
                                            <span className="font-medium text-[#2D4A86]">{dashboard.name}</span>
                                            <button className="text-[#2D4A86]">
                                                {expandedDashboard === dashboard.rowKey ? <Minus size={20} /> : <Plus size={20} />}
                                            </button>
                                        </div>

                                        {expandedDashboard === dashboard.rowKey && (
                                            <div className="p-3 bg-white border-t border-gray-200">
                                                {dashboard.description && (
                                                    <div className="text-sm text-gray-600 mb-2">{dashboard.description}</div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Favorites Section */}
                    <div className="p-4 border-t border-gray-200">
                        <h3 className="text-md font-semibold text-[#2D4A86] mb-2">FAVORITES</h3>
                        <div className="border border-gray-200 rounded-md overflow-hidden">
                            <div className="flex justify-between items-center p-3 bg-white">
                                <span className="font-medium text-[#2D4A86]">MY FAVORITE DASHBOARDS</span>
                                <button className="text-[#2D4A86]">
                                    <Plus size={20} />
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Shared Section */}
                    <div className="p-4 border-t border-gray-200">
                        <h3 className="text-md font-semibold text-[#2D4A86] mb-2">SHARED</h3>
                        <div className="border border-gray-200 rounded-md overflow-hidden">
                            <div className="flex justify-between items-center p-3 bg-white">
                                <span className="font-medium text-[#2D4A86]">MY SHARED DASHBOARDS</span>
                                <button className="text-[#2D4A86]">
                                    <Minus size={20} />
                                </button>
                            </div>
                            <div className="p-3 bg-white border-t border-gray-200">
                                <div className="text-sm text-gray-600">Lorem ipsum dolor sit amet lorem</div>
                                <div className="text-sm text-gray-600">Lorem ipsum dolor sit amet lorem</div>
                                <div className="text-sm text-gray-600">Lorem ipsum dolor sit amet lorem</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ) : <></>
}

export default ConfigureDashboards
