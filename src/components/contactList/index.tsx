'use client'

import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
  TableState,
} from '@tanstack/react-table'
import { ContactListType } from '@/types/contacts'
import { Button } from '@/components/ui/button'
import Image from 'next/image'
import { useToast } from '@/hooks/use-toast'
import { useActionsStore } from '@/stores/actions'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { api } from '@/trpc/react'
import Loader from '@/components/ui/Loader'
import { cn } from '@/lib/utils'
import React, { useMemo, useState, useEffect, useRef, useCallback } from 'react'
import { useViewStore } from '@/stores/viewStore'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'
import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'
import { customStringFilter } from '@/lib/customStringFilter'
import { IoCaretForward } from 'react-icons/io5'
import { IoMdSkipForward } from 'react-icons/io'
import exportToCsv, { getCsvBlob } from 'tanstack-table-export-to-csv'
import checkedSVG from '../../../public/images/checked-radio.svg'
import { NoDataAvailable } from '../dataTable/noDataAvailable'
import DownloadIconSvg from '../../../public/images/download.svg'
import isEqual from 'lodash/isEqual'
import { getSortingIcon } from '../ui/sortIcon'

export const ContactList = () => {

  const [IsDownloadIconSvg, SetIsDownloadIconSvg] = useState<boolean>(true)
  const csvExportIMGref = useRef(DownloadIconSvg)

  //columns function
  const { selectedOptions, setSelectedOptions, setOptions } = useActionsStore()
  const { setTableState, tableState, visibleColumnsForView, currentView } =
    useViewStore()
  const [filterValues, setFilterValues] = useState<{ [key: string]: string }>(
    {}
  )
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const filterFunction = useCallback(customStringFilter<ContactListType>(), []);

  const setFilter = (columnAccessorKey: string, value: string) => {
    setFilterValues((prev) => ({
      ...prev,
      [columnAccessorKey]: value,
    }))
  }

  //getting data from hydra api
  const [contacts, query] =
    api.contacts.getOrgContactList.useSuspenseQuery<ContactListType[]>()

  //using hook to convert into Contact List entity
  const [allContacts, setAllContacts] = useState<ContactListType[]>([])
  useEffect(() => {
    if (contacts && !isEqual(contacts, allContacts)) {
      setAllContacts(contacts);
    }
  }, [contacts])
  useEffect(() => {
   // console.log("set all icons useEffect");
    if (!IsDownloadIconSvg && csvExportIMGref.current) {
      const myTimeout = setTimeout(() => {
        if (csvExportIMGref.current) {  // Additional check inside timeout
          setExportDownloadIconSvg();
          SetIsDownloadIconSvg(true);
        }
        // else
        // {
        //   console.log("csvExportIMGref.current is null");
        // }
      }, 5000);
      
      return () => clearTimeout(myTimeout); // Clean up timeout
    }
  }, [IsDownloadIconSvg]);

  const setExportDownloadIconSvg = () => {
   // console.log("set all setExportDownloadIconSvg");
    if (csvExportIMGref.current) {
      csvExportIMGref.current.src = DownloadIconSvg.src;
    }
    // else
    // {
    //   console.log("csvExportIMGref.current is null");
    // }
  }

  const contactListData = useMemo(() => allContacts, [allContacts])

  const columnsDef = [
    {
      accessorKey: 'organizationName',
      header: 'ORGANIZATION',
      filterFn: filterFunction,
      enableColumnFilter: true, // Added
      cell: (info: any) => (
        <div className="text-left">{String(info.getValue())}</div>
      ),
    },
    {
      accessorKey: 'fullName',
      header: 'NAME',
      filterFn: filterFunction,
      enableColumnFilter: true, // Added
      cell: (info: any) => (
        <div className="text-left">{String(info.getValue())}</div>
      ),
    },
    {
      accessorKey: 'contactEmail',
      header: 'EMAIL',
      filterFn: filterFunction,
      enableColumnFilter: true, // Added
      cell: (info: any) => (
        <div className="text-left">{String(info.getValue())}</div>
      ),
    },
    {
      accessorKey: 'active',
      header: 'ACTIVE',
      filterFn: filterFunction,
      enableColumnFilter: true, // Added
      cell: (info: any) => (
        <div className="text-left">
          {info.getValue() === true || info.getValue() === "true" ? "Yes" : "No"}
        </div>
      ),
    },
  ] as ColumnDef<ContactListType>[]

  const contactListColumns = useMemo(() => {
    if (allContacts.length === 0) return []

    if (!allContacts) return []
    return columnsDef
  }, [allContacts])

  const removeDuplicates = (arr: any) => {
    return [...new Set(arr)]
  }

  const tableConfig = useMemo(() => ({
    data: contactListData,
    columns: contactListColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    sortDescFirst: false,
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
    state: {
      sorting,
      columnFilters,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onStateChange: (newState: TableState) => {
      setTableState(typeof newState === 'function' 
        ? (newState as (prev: TableState) => TableState)(tableState ?? {
            pagination: { pageIndex: 0, pageSize: 10 },
            sorting: [],
            columnFilters: [],
            columnVisibility: {},
            columnOrder: [],
            columnPinning: { left: [], right: [] },
            rowPinning: { top: [], bottom: [] },
            expanded: {},
            grouping: [],
            columnSizing: {},
            columnSizingInfo: {
              startOffset: 0,
              startSize: 0,
              deltaOffset: 0,
              deltaPercentage: 0,
              isResizingColumn: false,
              columnSizingStart: [],
            },
            globalFilter: '',
            rowSelection: {}
          })
        : newState
      )
    },
  }), [contactListData, contactListColumns, sorting, columnFilters, tableState]);

  const table = useReactTable<ContactListType>({
    ...tableConfig,
    onStateChange: (updater) => {
      const newState = typeof updater === 'function' ? updater(tableState ?? {
        pagination: { pageIndex: 0, pageSize: 10 },
        sorting: [],
        columnFilters: [],
        columnVisibility: {},
        columnOrder: [],
        columnPinning: { left: [], right: [] },
        rowPinning: { top: [], bottom: [] },
        expanded: {},
        grouping: [],
        columnSizing: {},
        columnSizingInfo: {
          startOffset: 0,
          startSize: 0,
          deltaOffset: 0,
          deltaPercentage: 0,
          isResizingColumn: false,
          columnSizingStart: [],
        },
        globalFilter: '',
        rowSelection: {}
      }) : updater;
      setTableState(newState);
    }
  });

  const handleExportToCsv = (): void => {
    const headers = table
      .getHeaderGroups()
      .map((x) => x.headers)
      .flat();

    const rows = table.getFilteredRowModel().rows;
    exportToCsv('Contacts_data', headers, rows);

    if (csvExportIMGref.current) {
      csvExportIMGref.current.src = checkedSVG.src;
      SetIsDownloadIconSvg(false);
    }
  };

  // Effect to handle icon reset
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    
    if (!IsDownloadIconSvg && csvExportIMGref.current) {
      timeoutId = setTimeout(() => {
        if (csvExportIMGref.current) {
          setExportDownloadIconSvg();
          SetIsDownloadIconSvg(true);
        }
      }, 5000);
    }
    
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [IsDownloadIconSvg]);

  return (
    <div className="relative">
      <div className="flex justify-end mt-[-35px] relative">
        <Button
          onClick={handleExportToCsv}
          disabled={table.getFilteredRowModel().rows.length === 0}
          className="bg-white-300 hover:bg-white-400 text-[#1a5276] font-bold py-2 px-4 rounded inline-flex items-center"
        >
          <Image 
            ref={csvExportIMGref} 
            src={DownloadIconSvg} 
            alt="Download icon"
            width={18}
            height={18}
            style={{
              width: '18px',
              height: 'auto',
            }}
            data-testid="download-icon"
          />
          <span>EXPORT TO EXCEL</span>
        </Button>
      </div>
      <Card className="rounded-lg bg-card text-card-foreground shadow-sm border-0">
        <div className="flex justify-center items-center">
          <CardHeader className='p-2'>
            <CardTitle className="font-open-sans font-bold text-[28px] text-black">Contacts</CardTitle>
            {/* <CardDescription>Organization Contacts List</CardDescription> */}
          </CardHeader>
        </div>
        <CardContent className="p-0 px-0 pb-6">
          {query.isPending || query.isRefetching ? (
            <div className="overflow-x-auto relative mt-10">
              <Loader data-testid="loader" />
            </div>
          ) : contactListData.length > 0 ? (
            <div className="flex flex-col w-full font-open-sans text-sm">
              <table className="w-full border-collapse mx-0">
                <thead>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => {
                        return (
                          <th
                            key={header.id}
                            colSpan={header.colSpan}
                            className={cn(
                              // Base styles
                              "bg-[#566582] text-[#F5F7FE]",
                              "h-[45px] text-sm font-semibold",
                              "p-[18px] text-left",
                              "font-['Open_Sans']",
                              "border-[#dddcdf]",
                              "border-b",  // Removed border-r
                              "sticky top-0 z-10",
                              // Sorting styles
                              getSortingIcon(header.column.getIsSorted() as boolean,
                               header.column.getIsSorted() === 'desc'),
                            )}
                            style={{
                              minWidth: `${header.getSize()}px`, // Keep this in style prop as it's dynamic
                              paddingLeft: '24px', // Match the cell padding
                            }}
                          >
                            {header.isPlaceholder ? null : (
                              <div className="flex justify-start items-center">
                                <span
                                  className="flex w-full text-left justify-start"
                                  onClick={header.column.getToggleSortingHandler()}
                                >
                                  {flexRender(
                                    header.column.columnDef.header,
                                    header.getContext()
                                  )}
                                </span>
                                {header.column.getCanFilter() && (
                                  <div className="flex">
                                    <DataTableColumnFilter
                                      column={header.column}
                                      setColumnFilters={setColumnFilters}
                                    />
                                  </div>
                                )}
                              </div>
                            )}
                          </th>
                        )
                      })}
                    </tr>
                  ))}
                </thead>
                <tbody className="bg-[#F9F8F9]">
                  {table.getRowModel().rows.map((row) => (
                    <tr 
                      key={row.id}
                      className="border-b-[7px] border-white"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <td
                          key={cell.id}
                          className="px-6 py-4 border-[#DDDCDF] text-left"
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
                {table.getFilteredRowModel().rows.length > 0 && (
                  <tfoot>
                    <tr>
                      <td colSpan={4}>
                        <div className="px-4 border-t border-[#DDDCDF] flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-1">
                              <button
                                className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                                onClick={() => table.setPageIndex(0)}
                                disabled={!table.getCanPreviousPage()}
                              >
                                <IoMdSkipForward
                                  size={16}
                                  className="rotate-180"
                                />
                              </button>
                              <button
                                className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                                onClick={() => table.previousPage()}
                                disabled={!table.getCanPreviousPage()}
                              >
                                <IoCaretForward
                                  size={16}
                                  className="rotate-180"
                                />
                              </button>
                              <ul
                                className="flex items-center py-1.5 px-1.5 self-stretch content-stretch"
                              >
                                <GeneratePageNumbers table={table} />
                              </ul>
                              <button
                                className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                                onClick={() => table.nextPage()}
                                disabled={!table.getCanNextPage()}
                              >
                                <IoCaretForward size={16} className="" />
                              </button>
                              <button
                                className="px-2 py-1  text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                                onClick={() =>
                                  table.setPageIndex(table.getPageCount() - 1)
                                }
                                disabled={!table.getCanNextPage()}
                              >
                                <IoMdSkipForward size={16} className="" />
                              </button>

                              <div className="flex items-center">
                                <select
                                  value={table.getState().pagination.pageSize}
                                  onChange={(e) => {
                                    table.setPageSize(Number(e.target.value))
                                  }}
                                  className="pl-8 p-1 text-[14px] font-open-sans text-black"
                                  aria-label="Select number of items per page"
                                >
                                  {[10, 25, 50, 100].map((pageSize) => (
                                    <option key={pageSize} value={pageSize}>
                                      {pageSize}
                                    </option>
                                  ))}
                                </select>
                                <span className="text-[14px] font-open-sans text-black" style={{ padding: '10px 8px', lineHeight: '2' }}>
                                  Items per page
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tfoot>
                )}
              </table>
            </div>
          ) : (
            <NoDataAvailable 
              content="No data available for Contacts" 
              data-testid="no-data" 
            />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
