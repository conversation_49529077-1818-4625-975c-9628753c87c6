"use client"

import refresh from '@/actions/refresh'
import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useFormStatus } from 'react-dom'

type Props = {
  className?: string
}

const SubmitButton = ({ className }: Props) => {
  const { pending } = useFormStatus()

  return (
    <Button 
      type="submit" 
      className={cn('bg-ui-dark-gray', 'transition-opacity', {
        'opacity-50 cursor-not-allowed': pending
      }, className)}
      disabled={pending}
    >
      {pending ? 'Renewing...' : 'Renew Session'}
    </Button>
  )
}

const RefreshSessionButton = ({ className }: Props) => {
  return (
    <form action={refresh} method="post">
      <SubmitButton className={className} />
    </form>
  )
}

export default RefreshSessionButton