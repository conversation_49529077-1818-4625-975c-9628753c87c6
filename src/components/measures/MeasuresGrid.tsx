'use client'

import React, {
  useMemo,
  useState,
  useEffect,
  useRef,
  useLayoutEffect,
} from 'react'
import { ArrowRight, ChevronDown, ChevronRight } from 'lucide-react'
import { FaInfoCircle } from 'react-icons/fa'
import { IoCaretForward } from 'react-icons/io5'
import { IoMdSkipForward } from 'react-icons/io'
import Link from 'next/link'
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
  ExpandedState,
  Updater,
} from '@tanstack/react-table'
import { api } from '@/trpc/react'
import { ScorecardView } from '@/enums/scorecardView'
import { CalculatedMeasure } from '@/types/calculatedMeasure'
import dayjs from 'dayjs'
import { cn } from '@/lib/utils'
import { useDateStore } from '@/stores/dates'
import { useFilterStore } from '@/stores/filter'
import useStore from '@/stores/useStore'
import { useRouter } from 'next/navigation'
import Loader from '../ui/Loader'
import { useUIStore } from '@/stores/ui'
import { MeasuresResultsByHospitalRow } from './MeasuresResultsByHospitalRow'
import { EntityDetailType } from '@/enums/entityDetailType'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'
import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'
import { customStringFilter } from '@/lib/customStringFilter'
import { useUserSessionStore } from '@/stores/userSession'
import { useActionsStore } from '@/stores/actions'
import { useViewStore } from '@/stores/viewStore'
import { getSortingIcon } from '../ui/sortIcon'
import { useMeasureResultsStore } from '@/stores/measuresResultsStore'
import { EntityLevelConstants } from '@/types/expansionConfiguration'
import { SelectionType } from '@/enums/selectionType'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { revalidateMeasureDetails } from '@/actions/revalidateMeasureDetails'
import { ExtensionLevels } from '@/enums/extensionLevels'
import { useOrgSwitcherStore } from '@/stores/orgSwitcher'

export const MeasuresGrid = () => {
  const router = useRouter()

  const pinnedColumns: { [key: string]: boolean } = {
    measureTitle: true,
    entityOrHospital: true,
  }
  // localStorage.removeItem(Constants.CurrentSelectedData)

  const {
    organizationId,
    selectionType,
    expansionConfiguration,
    primaryMeasureType,
  } = useUserSessionStore()

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])

  const {
    setCurrentMeasureId,
    setCurrentMeasureName,
    setEntityId,
    setEntityType,
    setIsIAPIMeasure,
    setCurrentHospitalId,
    setCurrentHospitalName,
    setcurrentStartDate,
    setcurrentEndDate,
  } = useUIStore()
  const {
    hideEmptyIndicators,
    appliedFilters,
    inFlux: filterInFlux,
  } = useFilterStore()
  const { inFlux: orgSwitcherInFlux } = useOrgSwitcherStore()
  const dateStore = useStore(useDateStore, (state) => state)
  const { selectedOptions, setSelectedOptions, setOptions, ShowProgress } =
    useActionsStore()
  const { setTableState, tableState, visibleColumnsForView, currentView } =
    useViewStore()
  const [sorting, setSorting] = useState<SortingState>([])
  const { measureResults, setMeasureResults } = useMeasureResultsStore()
  const [expanded, setExpanded] = useState<ExpandedState>({})
  const [currentOrganizationId, setCurrentOrganizationId] = useState<string>('')

  const memoizedFilters = useMemo(
    () =>
      Object.entries(appliedFilters).reduce(
        (acc, [key, value]) => {
          acc[key as keyof typeof acc] = value
          return acc
        },
        {} as Record<
          'measures' | 'subOrganizations' | 'submissionGroups' | 'providers',
          string[]
        >
      ),
    [JSON.stringify(appliedFilters)]
  )

  const abortControllerRef = useRef<AbortController | null>(null)

  const requestPayload = useMemo(() => {
    return {
      currentView,
      primaryMeasureType,
      aggregationType: dateStore?.selectedPeriod
        ? ScorecardView[dateStore.selectedPeriod as keyof typeof ScorecardView]
        : undefined,
      startDate: dateStore?.selectedRange?.[0],
      endDate: dateStore?.selectedRange?.[1],
      hideEmptyIndicators,
      filters: memoizedFilters,
    }
    // Only re-create this object if any relevant value truly changes:
  }, [
    currentView,
    primaryMeasureType,
    dateStore?.selectedRange?.[0]?.toISOString(),
    dateStore?.selectedRange?.[1]?.toISOString(),
    dateStore?.selectedPeriod,
    hideEmptyIndicators,
    memoizedFilters,
  ])

  const calculatedMeasuresMutation = api.measures.calculateResults.useMutation()

  useEffect(() => {
    const loadInitialData = async () => {
      if (
        !organizationId ||
        !requestPayload.currentView ||
        !requestPayload.startDate ||
        !requestPayload.endDate ||
        !requestPayload.aggregationType ||
        filterInFlux ||
        orgSwitcherInFlux ||
        requestPayload.primaryMeasureType === PrimaryMeasureTypeConstants.None
      ) {
        return
      }

      // Cancel the previous request if it exists
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController
      const abortController = new AbortController()
      abortControllerRef.current = abortController
      const signal = abortController.signal

      try {
        // Pass the signal to the request
        const data = await calculatedMeasuresMutation.mutateAsync({
          primaryMeasureType: requestPayload.primaryMeasureType,
          hideEmptyIndicators: requestPayload.hideEmptyIndicators,
          startDate: dayjs.utc(requestPayload.startDate).toISOString(),
          endDate: dayjs.utc(requestPayload.endDate).toISOString(),
          aggregationType: requestPayload.aggregationType,
          filters: requestPayload.filters,
        })
        if (signal.aborted) {
          console.log('Request was aborted')
          return
        }

        setMeasureResults(data)
        setCurrentOrganizationId(organizationId!)
      } catch (error) {
        if (signal.aborted) {
          console.log('Request aborted by user')
        } else {
          console.error('Failed to load measures:', error)
        }
      }
    }

    loadInitialData()

    // Cleanup function to abort any ongoing request on component unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [requestPayload, organizationId, filterInFlux, orgSwitcherInFlux])

  const filterFunction = customStringFilter<CalculatedMeasure>()

  const disableType1Link =
    selectionType == SelectionType.Organization &&
    expansionConfiguration?.find((x) => x.level === ExtensionLevels.level1)
      ?.selectedLevel === EntityLevelConstants.TopLevel

  const generateColumns = (
    data: CalculatedMeasure[]
  ): ColumnDef<CalculatedMeasure>[] => {
    const dynamicColumns: ColumnDef<CalculatedMeasure>[] = [
      {
        accessorKey: 'measureTitle',
        enablePinning: true,
        size: 500,
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        header: ({ table }) => `Measure Title (${table.getRowCount()})`,
        cell: ({ row, getValue }) => {
          return (
            <div
              style={{
                paddingLeft: `${row.depth * 2}rem`,
              }}
            >
              <div className="flex justify-between">
                <div className="flex items-center">
                  <button
                    className="focus:outline-none"
                    onClick={() => {
                      const isCurrentlyExpanded = row.getIsExpanded()
                      if (!isCurrentlyExpanded) {
                        // If expanding, collapse all other rows and only expand this one
                        setExpanded({ [row.id]: true })
                      } else {
                        // If collapsing, just collapse this row
                        setExpanded((prev) => ({
                          ...((prev || {}) as Record<string, boolean>),
                          [row.id]: false,
                        }))
                      }
                    }}
                  >
                    {row.getIsExpanded() ? (
                      <span className="relative -left-1 top-0.5">
                        <ChevronDown size={16} className="mr-2 opacity-30" />
                      </span>
                    ) : (
                      <span className="relative -left-1 top-0.5">
                        <ChevronRight size={16} className="mr-2 opacity-30" />
                      </span>
                    )}
                  </button>

                  {disableType1Link ? (
                    <div className="text-start"> {getValue() as string}</div>
                  ) : (
                    <Link
                      className="cursor-pointer"
                      href={
                        row.original.isIAPIMeasure
                          ? `#!`
                          : `/measures/measure-details`
                      }
                      prefetch={true}
                      legacyBehavior
                    >
                      <a
                        className={cn('text-start underline', {
                          'text-gray-500 pointer-events-none ':
                            row.original.isIAPIMeasure,
                        })}
                        onClick={async (event) => {
                          event.preventDefault()

                          setCurrentMeasureId(row.original.measureIdentifier!)
                          setCurrentMeasureName(row.original.measureTitle!)
                          setcurrentStartDate(dayjs(requestPayload.startDate!))
                          setcurrentEndDate(dayjs(requestPayload.endDate!))

                          setEntityId(row.original.measureIdentifier!)
                          setEntityType(EntityDetailType.Measure)
                          setIsIAPIMeasure(row.original.isIAPIMeasure ?? false)

                          setCurrentHospitalId()
                          setCurrentHospitalName()

                          await revalidateMeasureDetails()

                          router.push(`/measures/measure-details`)
                        }}
                      >
                        {getValue() as string}
                      </a>
                    </Link>
                  )}
                </div>

                {row.original.smallestInterval === 'Y' && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <FaInfoCircle fill="#566582" className="text-white" />
                      </TooltipTrigger>

                      <TooltipContent
                        side="left"
                        className="bg-white border border-[#dddcdf]"
                      >
                        <p className="text-sm">Data only exists annually</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>
          )
        },
      },
    ]

    if (selectedOptions.includes('Trend')) {
      dynamicColumns.push({
        accessorKey: 'trendCss',
        enableColumnFilter: false,
        header: () => `Trend`,
        cell: ({ getValue }) => {
          const trend: string = getValue() as string // color-angle
          const [color, angleStr] = trend.split('-')

          const isNegative = angleStr?.startsWith('n')
          const angle = isNegative ? angleStr?.slice(1) : angleStr

          return (
            <div className="text-center">
              <ArrowRight
                size={21}
                className={cn('transform inline', `text-${color}-500`)}
                color={color === 'yellow' ? '#ddbb0b' : color}
                style={{
                  transform: isNegative
                    ? `rotate(${angle}deg)`
                    : `rotate(-${angle}deg)`,
                }}
              />
            </div>
          )
        },
      })
    }

    // Identify and sort dynamic yearly columns (e.g., CY_2023, CY_2024)
    dynamicColumns.push(
      ...Object.keys(data[0] || {})
        .filter((key) => /^CY_[0-9]{4}$/.test(key)) // Matches "CY_2023", "CY_2024", etc.
        .sort((a, b) => {
          // Sort keys by year
          const yearA = parseInt(a.split('_')[1]!, 10)
          const yearB = parseInt(b.split('_')[1]!, 10)
          return yearA - yearB
        })
        .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
        .map(
          (key) =>
            ({
              accessorKey: key,
              filterFn: filterFunction,
              header: () => key.replace(/_/g, '-').toUpperCase(),
              cell: ({ getValue }) => (
                <div className="text-center">
                  {String(getValue()).indexOf('</br>') >= 0 ? (
                    <>
                      <span>{String(getValue()).split('</br>')[0]}</span>{' '}
                      <br></br>
                      <span>{String(getValue()).split('</br>')[1]}</span>
                    </>
                  ) : (
                    String(getValue())
                  )}
                </div>
              ),
            }) as ColumnDef<CalculatedMeasure>
        )
    )

    // Identify and sort dynamic monthly columns (e.g., Jan_2024, Feb_2024)
    dynamicColumns.push(
      ...Object.keys(data[0] || {})
        .filter((key) => /^[A-Za-z]{3}_[0-9]{4}$/.test(key)) // Matches "Jan_2024", "Feb_2024", etc.
        .sort((a, b) => {
          // Sort keys by month and year
          const dateA = dayjs(a, 'MMM_YYYY')
          const dateB = dayjs(b, 'MMM_YYYY')
          return dateA.isBefore(dateB) ? -1 : 1
        })
        .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
        .map(
          (key) =>
            ({
              accessorKey: key,
              minSize: 150,
              filterFn: filterFunction,
              header: () => key.replace(/_/g, '-'),
              cell: ({ getValue }) => (
                <div className="text-center">
                  {String(getValue()).indexOf('</br>') >= 0 ? (
                    <>
                      <span>{String(getValue()).split('</br>')[0]}</span>{' '}
                      <br></br>
                      <span>{String(getValue()).split('</br>')[1]}</span>
                    </>
                  ) : (
                    String(getValue())
                  )}
                </div>
              ),
            }) as ColumnDef<CalculatedMeasure>
        )
    )

    // Identify dynamic quarterly columns (e.g., Q1_2023, Q2_2024, etc.)
    dynamicColumns.push(
      ...Object.keys(data[0] || {})
        .filter((key) => /^Q\d{1}_[0-9]{4}$/.test(key)) // Matches "Q1_2023", "Q2_2024", etc.
        .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
        .map(
          (key) =>
            ({
              accessorKey: key,
              filterFn: filterFunction,
              header: () => key.replace(/_/g, '-').toUpperCase(),
              cell: ({ getValue }) => (
                <div className="text-center">
                  {String(getValue()).indexOf('</br>') >= 0 ? (
                    <>
                      <span>{String(getValue()).split('</br>')[0]}</span>{' '}
                      <br></br>
                      <span>{String(getValue()).split('</br>')[1]}</span>
                    </>
                  ) : (
                    String(getValue())
                  )}
                </div>
              ),
            }) as ColumnDef<CalculatedMeasure>
        )
    )

    if (selectedOptions.includes('CMS ID')) {
      dynamicColumns.push({
        accessorKey: 'cmsId',
        filterFn: filterFunction,
        header: () => `CMS ID`,
        cell: ({ getValue }) => {
          const cmsId = String(getValue())

          return <div className="text-center">{cmsId}</div>
        },
      })
    }

    if (selectedOptions.includes('Measure Description')) {
      dynamicColumns.push({
        accessorKey: 'measureDescription',
        header: () => 'Measure Description',
        cell: ({ getValue }) => (
          <div className="text-center w-[200px]">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Friendly Name')) {
      dynamicColumns.push({
        accessorKey: 'friendlyName',
        header: () => 'Friendly Name',
        cell: ({ getValue }) => (
          <div className="text-center w-[200px]">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Sub Domain')) {
      dynamicColumns.push({
        accessorKey: 'subDomain',
        header: () => 'Sub Domain',
        cell: ({ getValue }) => (
          <div className="text-center w-[200px]">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Type')) {
      dynamicColumns.push({
        accessorKey: 'type',
        header: () => 'Type',
        cell: ({ getValue }) => (
          <div className="text-center w-[200px]">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Sub Type')) {
      dynamicColumns.push({
        accessorKey: 'subType',
        header: () => 'Sub Type',
        cell: ({ getValue }) => (
          <div className="text-center w-[200px]">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Application')) {
      dynamicColumns.push({
        accessorKey: 'application',
        header: () => 'Application',
        cell: ({ getValue }) => (
          <div className="text-center w-[200px]">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Program Name')) {
      dynamicColumns.push({
        accessorKey: 'programName',
        header: () => 'Program Name',
        cell: ({ getValue }) => (
          <div className="text-center w-[200px]">{String(getValue())}</div>
        ),
      })
    }

    // Return combined static and dynamic columns
    return dynamicColumns
  }

  const columns = useMemo(
    () => generateColumns(measureResults),
    [measureResults, selectedOptions]
  )

  const table = useReactTable({
    data: measureResults,
    columns,
    state: {
      sorting,
      columnPinning: {
        left: ['measureTitle'],
      },
      columnFilters,
      expanded,
    },
    onExpandedChange: (updatedExpanded: Updater<ExpandedState>) => {
      // Handle both function and direct value updates
      const newExpanded =
        typeof updatedExpanded === 'function'
          ? updatedExpanded(expanded)
          : updatedExpanded

      // Get the keys of the newly expanded state
      const expandedRows = Object.keys(newExpanded)

      // If the row is already expanded, allow it to collapse
      if (expandedRows.length === 0) {
        setExpanded({})
        return
      }

      // Get the newly expanded row
      const newlyExpandedRow = expandedRows.find(
        (row) => !expanded[row as keyof typeof expanded]
      )

      if (newlyExpandedRow) {
        // If there's a newly expanded row, only show that one
        setExpanded({ [newlyExpandedRow]: true } as ExpandedState)
      } else {
        // If we're collapsing a row, allow it
        setExpanded(newExpanded)
      }
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onStateChange: () => setTableState(table.getState()),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  const gridContainerRef = useRef<HTMLDivElement>(null)
  const [gridHeight, setGridHeight] = useState<number>(500) // Default height

  // Function to calculate and adjust grid height based on available space
  const adjustGridHeight = () => {
    if (!gridContainerRef.current) return

    const gridTop = gridContainerRef.current.getBoundingClientRect().top
    const footerElement = document.querySelector('footer')

    if (footerElement) {
      const footerTop = footerElement.getBoundingClientRect().top
      const availableSpace = footerTop - gridTop - 80 // Buffer for pagination controls

      // Only increase height if we have more space available
      if (availableSpace > 500) {
        setGridHeight(availableSpace)
      } else {
        setGridHeight(500) // Default height
      }
    }
  }

  // Adjust height when page size changes
  useEffect(() => {
    // Wait for the DOM to update after page size change
    setTimeout(adjustGridHeight, 100)
  }, [table.getState().pagination.pageSize])

  // Also adjust on window resize
  useEffect(() => {
    window.addEventListener('resize', adjustGridHeight)
    return () => window.removeEventListener('resize', adjustGridHeight)
  }, [])

  // Initial height adjustment
  useLayoutEffect(() => {
    adjustGridHeight()
  }, [measureResults])

  useEffect(() => {
    let options: string[] = ['Select All', 'Measure Title', 'Trend']
    let visibleColumns = ['Measure Title', 'Trend', 'CMS ID']
    if (measureResults && measureResults.length > 0) {
      Object.keys(measureResults[0]!).map((x) => {
        if (
          /^Q\d{1}_[0-9]{4}$/.test(x) ||
          /^[A-Za-z]{3}_[0-9]{4}$/.test(x) ||
          /^CY_[0-9]{4}$/.test(x)
        ) {
          options.push(x.replace('_', '-'))
          visibleColumns.push(x.replace('_', '-'))
        }
      })

      options.push(
        ...[
          'Measure Description',
          'Friendly Name',
          'Sub Domain',
          'Type',
          'CMS ID',
          'Sub Type',
          'Application',
          'Program Name',
        ]
      )

      setOptions(options)

      if (visibleColumnsForView && visibleColumnsForView.length > 0)
        visibleColumns = options.filter((x) =>
          visibleColumnsForView.includes(x)
        )

      setSelectedOptions(visibleColumns)

      if (tableState) {
        table.setColumnFilters(tableState.columnFilters)
        table.setColumnOrder(tableState.columnOrder)
        table.setPageSize(tableState.pagination.pageSize)
      }
    }
  }, [measureResults, visibleColumnsForView])

  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
  } as React.CSSProperties

  const isLoading = useMemo(
    () =>
      !currentView ||
      calculatedMeasuresMutation.isIdle ||
      calculatedMeasuresMutation.isPending ||
      currentOrganizationId === '' ||
      organizationId !== currentOrganizationId ||
      ShowProgress,
    [
      currentView,
      organizationId,
      currentOrganizationId,
      calculatedMeasuresMutation.isIdle,
      calculatedMeasuresMutation.isPending,
      ShowProgress,
    ]
  )

  if (isLoading) {
    return (
      <div className="relative top-32">
        <Loader className="" />
      </div>
    )
  }

  return (
    <div
      className="flex flex-col w-full font-open-sans text-sm"
      ref={gridContainerRef}
    >
      <div
        className={cn(
          'overflow-y-auto',
          'border-x-[#DDDCDF]',
          'border-x-[1px]',
          'border-t-[#DDDCDF]',
          'border-t-[1px]',
          'mac-scrollbar'
        )}
        style={{ maxHeight: `${gridHeight}px` }}
      >
        <table className="w-full border-collapse">
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{
                      ...headerStyle,
                      ...{
                        minWidth: `${header.getSize()}px`,
                      },
                      position: 'sticky',
                      top: 0,
                      zIndex: 10,
                      ...(pinnedColumns[header.column.id] && {
                        boxShadow: '-4px 0px 4px -4px gray inset',
                        left: 0,
                        zIndex: 11,
                      }),
                    }}
                    className={cn('border-b', 'border-l')}
                  >
                    {header.isPlaceholder ? null : (
                      <div className="flex justify-center items-center">
                        {/* <span className="flex w-full text-center justify-center">
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                        </span> */}
                        <span
                          className="flex m-3 w-full text-center justify-center cursor-pointer"
                          onClick={
                            !header.id?.toString().startsWith('Q')
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {!header.id?.toString().startsWith('Q') &&
                            getSortingIcon(
                              header.column.getIsSorted() as boolean,
                              header.column.getIsSorted() === 'desc'
                            )}
                        </span>
                        {header.column.getCanFilter() && (
                          <div className="flex">
                            <DataTableColumnFilter
                              column={header.column}
                              setColumnFilters={setColumnFilters}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <React.Fragment key={row.id}>
                <tr
                  key={row.id}
                  className={`${
                    row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                  } relative border-b border-[#DDDCDF]`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      className={cn(
                        `p-2 align-middle border-r border-[#DDDCDF]`,
                        row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                      )}
                      style={{
                        fontSize: '14px',
                        padding: '10px 24px',
                        width: `${cell.column.getSize()}px`,
                        ...(cell.column.getIsPinned() === 'left' && {
                          boxShadow: '-4px 0px 4px -4px gray inset',
                          position: 'sticky',
                          left: 1,
                          zIndex: 1,
                        }),
                      }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
                {row.getIsExpanded() && (
                  <tr className="relative">
                    <td
                      className="overflow-visible border-b border-[#DDDCDF]"
                      colSpan={row.getVisibleCells().length}
                    >
                      <MeasuresResultsByHospitalRow
                        key={row.id}
                        measureIdentifier={row.original.measureIdentifier!}
                        measureTitle={row.original.measureTitle!}
                      />
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
      <div className="px-4 border-t border-[#DDDCDF] flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <IoMdSkipForward size={16} className="rotate-180" />
            </button>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <IoCaretForward size={16} className="rotate-180" />
            </button>
            <ul
              className="flex"
              style={{
                paddingInlineStart: '40px',
                marginBlockStart: '1em',
                marginBlockEnd: '1em',
                marginInlineStart: '0px',
                marginInlineEnd: '0px',
                lineHeight: '2',
                position: 'relative',
                alignItems: 'center',
                padding: '6px 6px',
                alignSelf: 'stretch',
                alignContent: 'stretch',
              }}
            >
              <GeneratePageNumbers table={table} />
            </ul>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <IoCaretForward size={16} className="" />
            </button>
            <button
              className="px-2 py-1  text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <IoMdSkipForward size={16} className="" />
            </button>

            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value))
              }}
              className="pl-8 p-1 text-[14px] font-open-sans text-black  focus:outline-none"
            >
              {[10, 25, 50, 100].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
            <span
              className="text-[14px] font-open-sans text-black"
              style={{ padding: '10px 8px', lineHeight: '2' }}
            >
              Items per page
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
