'use client'

import { api } from '@/trpc/react'
import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    getPaginationRowModel,
    useReactTable,
} from '@tanstack/react-table'
import { Fragment, useEffect, useMemo, useState } from 'react'
import dayjs from 'dayjs'
import { FacilityResultByCCN, ProviderResult } from '@/types/calculatedMeasure'
import { ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useDateStore } from '@/stores/dates'
import { useFilterStore } from '@/stores/filter'
import useStore from '@/stores/useStore'
import { ScorecardView } from '@/enums/scorecardView'
import Loader from '../ui/Loader'
import { useActionsStore } from '@/stores/actions'
import { Pagination } from './pagination'
import { useUIStore } from '@/stores/ui'
import { EntityDetailType } from '@/enums/entityDetailType'
import { revalidateMeasureDetails } from '@/actions/revalidateMeasureDetails'

type Props = {
    measureIdentifier: string
    measureTitle: string
    entityId: string
}

export const MeasuresFacilityResultsRow = ({
    measureIdentifier,
    measureTitle,
    entityId,
}: Props) => {
    const router = useRouter()
    const [isDataReady, setIsDataReady] = useState(false)
    const { hideEmptyIndicators } = useFilterStore()
    const { selectedOptions } = useActionsStore()
    const dateStore = useStore(useDateStore, (state) => state)

    const {
        setCurrentMeasureId,
        setCurrentMeasureName,
        setEntityId,
        setEntityType,
        setCurrentHospitalName,
        setIsIAPIMeasure,
        setCurrentHospitalId,
        setcurrentStartDate,
        setcurrentEndDate,
        isIAPIMeasure
    } = useUIStore()

    const facilityResults = api.facilities.getFacilitiesByCCN.useQuery(
        {
            hospitalId: entityId,
            scorecardView: Object.entries(ScorecardView).find(
                (entry) => entry[0] === dateStore?.selectedPeriod
            )?.[1]!,
            startDate: dayjs.utc(dateStore?.selectedRange[0])?.toISOString()!,
            endDate: dayjs.utc(dateStore?.selectedRange[1])?.toISOString()!,
            measureIdentifier,
            isIAPIMeasure: isIAPIMeasure,
        },
        {
            enabled: isDataReady,
        }
    )


    useEffect(() => {
        if (
            hideEmptyIndicators !== undefined &&
            hideEmptyIndicators !== null &&
            dateStore?.selectedRange[0] &&
            dateStore?.selectedRange[1] &&
            dateStore?.selectedPeriod
        ) {
            setIsDataReady(true)
        }
    }, [dateStore, hideEmptyIndicators])

    const pageSize = 20 // Default page size
    const data = facilityResults.data ?? []

    const generateColumns = (
        data: FacilityResultByCCN[]
    ): ColumnDef<ProviderResult>[] => {
        const dynamicColumns: ColumnDef<FacilityResultByCCN>[] = [
            {
                id: 'facilityName',
                size: 500,
                cell: ({ row }) => {
                    const value = row.original.facilityName

                    return (
                        <div
                            style={{
                                paddingLeft: `${row.depth + 2 * 2}rem`,
                            }}
                        >
                            <div className="flex items-center">
                                <Link
                                    href={`/measures/measure-details`}
                                    legacyBehavior
                                    className="text-start cursor-pointer underline hover:underline"
                                >
                                    <a
                                        className="text-start  underline"
                                        onMouseDown={async (e) => {
                                            const { measureIdentifier } = row.original
                                            // Set the measure info that’s shared across hospital/ambulatory
                                            setCurrentMeasureId(measureIdentifier!)
                                            setCurrentMeasureName(measureTitle)

                                            setcurrentStartDate(dayjs(dateStore?.selectedRange[0]!))
                                            setcurrentEndDate(dayjs(dateStore?.selectedRange[1]!))

                                            const { facilityId, facilityName } = row.original

                                            setEntityId(facilityId!)
                                            setCurrentHospitalName(facilityName!)
                                            setCurrentHospitalId(facilityId!)
                                            setEntityType(EntityDetailType.Provider)
                                            setIsIAPIMeasure(row.original.isIAPIMeasure ?? false)

                                            await revalidateMeasureDetails()
                                        }}
                                        onClick={async (event) => {
                                            event.preventDefault()

                                            // Set the measure info that’s shared across hospital/ambulatory
                                            setCurrentMeasureId(measureIdentifier!)
                                            setCurrentMeasureName(measureTitle)

                                            setEntityId(row.original.facilityId!)
                                            setCurrentHospitalName(row.original.facilityName!)
                                            setEntityType(EntityDetailType.Provider)
                                            setIsIAPIMeasure(row.original.isIAPIMeasure ?? false)

                                            setcurrentStartDate(dayjs(dateStore?.selectedRange[0]!))
                                            setcurrentEndDate(dayjs(dateStore?.selectedRange[1]!))

                                            await revalidateMeasureDetails()
                                            router.push('/measures/measure-details')
                                        }}
                                    >
                                        {value ? value : "n/a"}
                                    </a>
                                </Link>
                            </div>
                        </div>
                    )
                },
            },
        ]

        if (selectedOptions.includes('Trend')) {
            dynamicColumns.push({
                accessorKey: 'trendCss',
                cell: ({ getValue }) => {
                    const trend: string = getValue() as string // color-angle
                    const [color, angleStr] = trend.split('-')

                    const isNegative = angleStr?.startsWith('n')
                    const angle = isNegative ? angleStr?.slice(1) : angleStr

                    return (
                        <div className="flex w-full justify-center">
                            <ArrowRight
                                size={16}
                                className={cn('transform inline', `text-${color}-500`)}
                                color={color === 'yellow' ? '#ddbb0b' : color}
                                style={{
                                    transform: isNegative
                                        ? `rotate(${angle}deg)`
                                        : `rotate(-${angle}deg)`,
                                }}
                            />
                        </div>
                    )
                },
            })
        }
        // Identify and sort dynamic yearly columns (e.g., CY_2023, CY_2024)
        dynamicColumns.push(
            ...Object.keys(data[0] ?? {})
                .filter((key) => /^CY_[0-9]{4}$/.test(key)) // Matches "CY_2023", "CY_2024", etc.
                .sort((a, b) => {
                    // Sort keys by year
                    const yearA = parseInt(a.split('_')[1]!, 10)
                    const yearB = parseInt(b.split('_')[1]!, 10)
                    return yearA - yearB
                })
                .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
                .map(
                    (key) =>
                        ({
                            accessorKey: key,
                            cell: ({ getValue }) => (
                                <div className="flex w-full justify-center">
                                    {String(getValue())}
                                </div>
                            ),
                        }) as ColumnDef<ProviderResult>
                )
        )

        // Identify and sort dynamic monthly columns (e.g., Jan_2024, Feb_2024)
        dynamicColumns.push(
            ...Object.keys(data[0] ?? {})
                .filter((key) => /^[A-Za-z]{3}_[0-9]{4}$/.test(key)) // Matches "Jan_2024", "Feb_2024", etc.
                .sort((a, b) => {
                    // Sort keys by month and year
                    const dateA = dayjs(a, 'MMM_YYYY')
                    const dateB = dayjs(b, 'MMM_YYYY')
                    return dateA.isBefore(dateB) ? -1 : 1
                })
                .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
                .map(
                    (key) =>
                        ({
                            accessorKey: key,
                            minSize: 150,
                            cell: ({ getValue }) => (
                                <div className="flex w-full justify-center">
                                    {String(getValue())}
                                </div>
                            ),
                        }) as ColumnDef<ProviderResult>
                )
        )
        // Identify dynamic quarterly columns (e.g., Q1_2023, Q2_2024, etc.)
        dynamicColumns.push(
            ...Object.keys(data[0] ?? {})
                .filter((key) => /^Q\d{1}_[0-9]{4}$/.test(key)) // Matches "Q1_2023", "Q2_2024", etc.
                .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
                .map(
                    (key) =>
                        ({
                            accessorKey: key,
                            cell: ({ getValue }) => (
                                <div className="flex w-full justify-center">
                                    {String(getValue())}
                                </div>
                            ),
                        }) as ColumnDef<ProviderResult>
                )
        )
        if (selectedOptions.includes('CMS ID')) {
            dynamicColumns.push({
                accessorKey: 'cmsId',
                cell: ({ getValue }) => (
                    <div className="flex w-full justify-center">{String(getValue())}</div>
                ),
            })
        }

        if (selectedOptions.includes('Measure Description')) {
            dynamicColumns.push({
                accessorKey: 'measureDescription',
                cell: ({ getValue }) => (
                    <div className="text-center">{String(getValue())}</div>
                ),
            })
        }

        if (selectedOptions.includes('Friendly Name')) {
            dynamicColumns.push({
                accessorKey: 'friendlyName',
                cell: ({ getValue }) => (
                    <div className="text-center">{String(getValue())}</div>
                ),
            })
        }

        if (selectedOptions.includes('Sub Domain')) {
            dynamicColumns.push({
                accessorKey: 'subDomain',
                cell: ({ getValue }) => (
                    <div className="text-center">{String(getValue())}</div>
                ),
            })
        }

        if (selectedOptions.includes('Type')) {
            dynamicColumns.push({
                accessorKey: 'type',
                cell: ({ getValue }) => (
                    <div className="text-center">{String(getValue())}</div>
                ),
            })
        }

        if (selectedOptions.includes('Sub Type')) {
            dynamicColumns.push({
                accessorKey: 'subType',
                cell: ({ getValue }) => (
                    <div className="text-center">{String(getValue())}</div>
                ),
            })
        }

        if (selectedOptions.includes('Application')) {
            dynamicColumns.push({
                accessorKey: 'application',
                cell: ({ getValue }) => (
                    <div className="text-center">{String(getValue())}</div>
                ),
            })
        }

        if (selectedOptions.includes('Program Name')) {
            dynamicColumns.push({
                accessorKey: 'programName',
                cell: ({ getValue }) => (
                    <div className="text-center">{String(getValue())}</div>
                ),
            })
        }

        // Return combined static and dynamic columns
        return dynamicColumns
    }

    const columns = useMemo(() => generateColumns(data), [data])

    const table = useReactTable({
        data,
        columns,
        state: {
            columnPinning: {
                left: ['providerName'],
            },
        },
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        sortDescFirst: false,
        initialState: {
            pagination: {
                pageSize: pageSize,
            },
        },
    })

    if (facilityResults.isPending) {
        return (
            <div className="overflow-x-auto relative mt-10">
                <Loader />
            </div>
        )
    }

    return (
        <div className="block relative border-1 border-[#DDDCDF]">
            <table className="w-full border-collapse">
                <tbody>
                    {table.getRowModel().rows.length === 0 ? (
                        <tr>
                            <td
                                className="bg-[#e2e4e7] p-[1.5rem] text-center border-r border-[#DDDCDF]"
                                colSpan={columns.length || 1}
                            >
                                No data available
                            </td>
                        </tr>
                    ) : (
                        table.getRowModel().rows.map((row) => (
                            <Fragment key={row.id}>
                                <tr
                                    className={`${row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'} relative border-b border-[#DDDCDF]`}
                                >
                                    {row.getVisibleCells().map((cell, i) => (
                                        <td
                                            key={cell.id}
                                            className={cn(
                                                `px-[24px] py-[10px] text-center align-middle border-r border-[#DDDCDF]`,
                                                row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                                            )}
                                            style={{
                                                fontSize: '14px',
                                                width: `${cell.column.getSize()}px`,
                                                ...(cell.column.getIsPinned() === 'left' && {
                                                    boxShadow: '-4px 0px 4px -4px gray inset',
                                                    position: 'sticky',
                                                    left: '3px',
                                                    zIndex: 2,
                                                }),
                                            }}
                                        >
                                            <span className={`w-full inline-flex ${i === 0 ? 'relative' : ''}`}>
                                                {(() => {
                                                    const renderedCell = flexRender(cell.column.columnDef.cell, cell.getContext());
                                                    return typeof renderedCell === 'string'
                                                        ? renderedCell
                                                            .replace(/&/g, "&amp;")
                                                            .replace(/</g, "&lt;")
                                                            .replace(/>/g, "&gt;")
                                                            .replace(/"/g, "&quot;")
                                                            .replace(/'/g, "&#039;")
                                                        : renderedCell;
                                                })()}
                                            </span>
                                        </td>
                                    ))}
                                </tr>
                            </Fragment>
                        ))
                    )}
                </tbody>
            </table>
            {table.getPageCount() > 1 && <Pagination table={table} />}
        </div>
    )
}
