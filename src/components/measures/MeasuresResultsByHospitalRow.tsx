'use client'

import { api } from '@/trpc/react'
import {
  ColumnDef,
  ExpandedState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getPaginationRowModel,
  Updater,
  useReactTable,
} from '@tanstack/react-table'
import { Fragment, useEffect, useMemo, useState } from 'react'
import dayjs from 'dayjs'
import {
  CalculatedMeasure,
  CalculatedMeasureEntity,
  CalculatedMeasureHospital,
} from '@/types/calculatedMeasure'
import { ArrowRight, ChevronDown, ChevronRight } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { MeasuresProvidersResultsRow } from './MeasuresProvidersResultsRow'
import { useFilterStore } from '@/stores/filter'
import useStore from '@/stores/useStore'
import { useDateStore } from '@/stores/dates'
import { ScorecardView } from '@/enums/scorecardView'
import Loader from '../ui/Loader'
import { useActionsStore } from '@/stores/actions'
import { Pagination } from './pagination'
import { EntityDetailType } from '@/enums/entityDetailType'
import { useUIStore } from '@/stores/ui'
import { useUserSessionStore } from '@/stores/userSession'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { SelectionType } from '@/enums/selectionType'
import { EntityLevelConstants } from '@/types/expansionConfiguration'

import { revalidateMeasureDetails } from '@/actions/revalidateMeasureDetails'
import { ExtensionLevels } from '@/enums/extensionLevels'
import { MeasuresFacilityResultsRow } from './MeasuresFacilityResults'
import { MeasureTypeByApplication } from '@/enums/measureTypeByApplication'
import { getMeasureSource } from '@/lib/getMeasureSource'
type Props = {
  measureIdentifier: string
  measureTitle: string
}

export const MeasuresResultsByHospitalRow = ({
  measureIdentifier,
  measureTitle,
}: Props) => {
  const router = useRouter()
  const [isDataReady, setIsDataReady] = useState(false)
  const { hideEmptyIndicators, appliedFilters } = useFilterStore()
  const { selectedOptions } = useActionsStore()
  const { primaryMeasureType, expansionConfiguration, selectionType } =
    useUserSessionStore()

  const dateStore = useStore(useDateStore, (state) => state)
  const {
    setCurrentMeasureId,
    setCurrentMeasureName,
    setEntityId,
    setEntityType,
    setCurrentHospitalId,
    setCurrentHospitalName,
    setIsIAPIMeasure,
    setcurrentStartDate,
    setcurrentEndDate,
  } = useUIStore()

  const hospitalMeasuresByHospital =
    api.measures.getMeasureResultsByHospital.useQuery(
      {
        primaryMeasureType,
        startDate: dayjs.utc(dateStore?.selectedRange[0])?.toISOString()!,
        endDate: dayjs.utc(dateStore?.selectedRange[1])?.toISOString()!,
        hideEmptyIndicators,
        measureIdentifier,
        scorecardView: Object.entries(ScorecardView).find(
          (entry) => entry[0] === dateStore?.selectedPeriod
        )?.[1]!,
        filters: {
          subOrgs: appliedFilters.subOrganizations,
          organizations: appliedFilters.organizations,
          providers: appliedFilters.providers,
          submissionGroups: appliedFilters.submissionGroups
        },
      },
      {
        enabled: isDataReady,
      }
    )

  useEffect(() => {
    if (
      primaryMeasureType &&
      dateStore?.selectedRange[0] &&
      dateStore?.selectedRange[1] &&
      dateStore?.selectedPeriod
    ) {
      setIsDataReady(true)
    }
  }, [dateStore, primaryMeasureType])

  const pageSize = 10 // Default page size
  const data = useMemo(() => {
    if (
      !hospitalMeasuresByHospital.data ||
      hospitalMeasuresByHospital.data.length === 0
    ) {
      return []
    }
    return hospitalMeasuresByHospital.data
  }, [hospitalMeasuresByHospital.data])

  const disableDrillDownToType4 =
    selectionType == SelectionType.Organization &&
    expansionConfiguration.find(
      (x) =>
        x.level === ExtensionLevels.level4 &&
        x.measureType === primaryMeasureType
    )?.selectedLevel == EntityLevelConstants.DoNotDisplay

  const generateColumns = (
    data: (CalculatedMeasureEntity | CalculatedMeasureHospital)[]
  ): ColumnDef<CalculatedMeasure>[] => {
    const dynamicColumns: ColumnDef<
      CalculatedMeasureEntity | CalculatedMeasureHospital
    >[] = [
      {
        id: 'entityOrHospital',
        enablePinning: true,
        size: 500,
        cell: ({ row }) => {
          // Determine if 'hospital' or 'entity' should be shown
          const value =
            (row.original as CalculatedMeasureHospital).hospital ??
            (row.original as CalculatedMeasureEntity).entity

          return (
            <div
              style={{
                paddingLeft: `${row.depth + 1 * 2}rem`,
              }}
            >
              <div className="flex items-center">
                {!disableDrillDownToType4 &&
                  (row.original.isExpandable ?? true) && (
                    <button
                      className="focus:outline-none"
                      onClick={() => {
                        const isCurrentlyExpanded = row.getIsExpanded()
                        if (!isCurrentlyExpanded) {
                          // If expanding, collapse all other rows and only expand this one
                          setExpanded({ [row.id]: true })
                        } else {
                          // If collapsing, just collapse this row
                          setExpanded({})
                        }
                      }}
                    >
                      {row.getIsExpanded() ? (
                        <span className="relative -left-1 top-0.5">
                          <ChevronDown size={16} className="mr-2 opacity-30" />
                        </span>
                      ) : (
                        <span className="relative -left-1 top-0.5">
                          <ChevronRight size={16} className="mr-2 opacity-30" />
                        </span>
                      )}
                    </button>
                  )}

                <Link href={`/measures/measure-details`} legacyBehavior>
                  <a
                    className="text-start underline "
                    onMouseDown={async (e) => {
                      const { measureIdentifier } = row.original
                      // Set the measure info that’s shared across hospital/ambulatory
                      setCurrentMeasureId(measureIdentifier!)
                      setCurrentMeasureName(measureTitle!)
                      setcurrentStartDate(dayjs(dateStore?.selectedRange[0]!))
                      setcurrentEndDate(dayjs(dateStore?.selectedRange[1]!))

                      setIsIAPIMeasure(row.original.isIAPIMeasure ?? false)

                      if (
                        primaryMeasureType ===
                        PrimaryMeasureTypeConstants.HospitalMeasures
                      ) {
                        const { hospitalId, hospital } =
                          row.original as CalculatedMeasureHospital

                        setCurrentHospitalId(hospitalId)
                        setEntityId(hospitalId!)
                        setCurrentHospitalName(hospital!)
                        setEntityType(EntityDetailType.Hospital)
                      } else if (
                        primaryMeasureType ===
                        PrimaryMeasureTypeConstants.AmbulatoryMeasures
                      ) {
                        const { entityCode, entityNameForDetails } =
                          row.original as CalculatedMeasureEntity

                        setEntityId(entityCode!)
                        setCurrentHospitalName(entityNameForDetails!)
                        setEntityType(EntityDetailType.SubmissionGroup)
                      }

                      await revalidateMeasureDetails()
                    }}
                    onClick={async (event) => {
                      event.preventDefault()

                      const { measureIdentifier } = row.original

                      // Set the measure info that’s shared across hospital/ambulatory
                      setCurrentMeasureId(measureIdentifier!)
                      setCurrentMeasureName(measureTitle!)

                      setcurrentStartDate(dayjs(dateStore?.selectedRange[0]!))
                      setcurrentEndDate(dayjs(dateStore?.selectedRange[1]!))

                      // Then narrow down the type-specific fields
                      if (
                        primaryMeasureType ===
                        PrimaryMeasureTypeConstants.HospitalMeasures
                      ) {
                        const { hospitalId, hospital } =
                          row.original as CalculatedMeasureHospital

                        setCurrentHospitalId(hospitalId)
                        setCurrentHospitalName(hospital!)
                        setEntityType(EntityDetailType.Hospital)
                      } else if (
                        primaryMeasureType ===
                        PrimaryMeasureTypeConstants.AmbulatoryMeasures
                      ) {
                        const { entityCode, entityNameForDetails } =
                          row.original as CalculatedMeasureEntity

                        setEntityId(entityCode!)
                        setCurrentHospitalName(entityNameForDetails!)
                        setEntityType(EntityDetailType.SubmissionGroup)
                      }
                      await revalidateMeasureDetails()
                      router.push('/measures/measure-details')
                    }}
                  >
                    {value ? value : 'n/a'}
                  </a>
                </Link>
              </div>
            </div>
          )
        },
      },
      {
        accessorKey: 'application',
        enableColumnFilter: false,
        header: () => `Source`,
        cell: ({ row, getValue }) => {
          if (!row.original.application) return <span>-</span>

          const isRegistryMeasures =
            row.original.application ===
            MeasureTypeByApplication.RegistryMeasures

          const isAbstractedMeasures =
            row.original.application ===
            MeasureTypeByApplication.AbstractedMeasures

          const isHospitalOrAmbulatoryEMeasures =
            row.original.application ===
              MeasureTypeByApplication.HospitalMeasures ||
            row.original.application ===
              MeasureTypeByApplication.AmbulatoryMeasures

          return (
            <div className="text-center">
              <span
                className={cn(
                  'px-3 py-1 rounded-full text-xs font-bold w-[66px]',
                  {
                    'bg-[#DEEEDE] text-[#496049]': isAbstractedMeasures,
                    'bg-[#D8E4EF] text-[#205782]':
                      isHospitalOrAmbulatoryEMeasures,
                    'bg-[#F8AF64] text-[#683F16]': isRegistryMeasures,
                  }
                )}
              >
                {getMeasureSource(getValue() as string)}
              </span>
            </div>
          )
        },
      },
    ]

    if (selectedOptions.includes('Trend')) {
      dynamicColumns.push({
        accessorKey: 'trendCss',
        cell: ({ getValue }) => {
          const trend: string = getValue() as string // color-angle
          const [color, angleStr] = trend.split('-')

          const isNegative = angleStr?.startsWith('n')
          const angle = isNegative ? angleStr?.slice(1) : angleStr

          return (
            <div className="flex w-full justify-center">
              <ArrowRight
                size={16}
                className={cn('transform inline', `text-${color}-500`)}
                color={color === 'yellow' ? '#ddbb0b' : color}
                style={{
                  transform: isNegative
                    ? `rotate(${angle}deg)`
                    : `rotate(-${angle}deg)`,
                }}
              />
            </div>
          )
        },
      })
    }
    // Identify and sort dynamic yearly columns (e.g., CY_2023, CY_2024)
    dynamicColumns.push(
      ...Object.keys(data[0] ?? {})
        .filter((key) => /^CY_[0-9]{4}$/.test(key)) // Matches "CY_2023", "CY_2024", etc.
        .sort((a, b) => {
          // Sort keys by year
          const yearA = parseInt(a.split('_')[1]!, 10)
          const yearB = parseInt(b.split('_')[1]!, 10)
          return yearA - yearB
        })
        .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
        .map(
          (key) =>
            ({
              accessorKey: key,
              cell: ({ getValue }) => {
                const value = String(getValue())
                const brRegex = /<\/?br>/i // matches <br>, </br>, <BR>, </BR>

                return (
                  <div className="text-center">
                    {brRegex.test(value) ? (
                      <>
                        <span>{value.split(brRegex)[0]}</span>
                        <br></br>
                        <span>{value.split(brRegex)[1]}</span>
                      </>
                    ) : (
                      value
                    )}
                  </div>
                )
              },
            }) as ColumnDef<CalculatedMeasure>
        )
    )

    // Identify and sort dynamic monthly columns (e.g., Jan_2024, Feb_2024)
    dynamicColumns.push(
      ...Object.keys(data[0] ?? {})
        .filter((key) => /^[A-Za-z]{3}_[0-9]{4}$/.test(key)) // Matches "Jan_2024", "Feb_2024", etc.
        .sort((a, b) => {
          // Sort keys by month and year
          const dateA = dayjs(a, 'MMM_YYYY')
          const dateB = dayjs(b, 'MMM_YYYY')
          return dateA.isBefore(dateB) ? -1 : 1
        })
        .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
        .map(
          (key) =>
            ({
              accessorKey: key,
              minSize: 150,
              cell: ({ getValue }) => {
                const value = String(getValue())
                const brRegex = /<\/?br>/i // matches <br>, </br>, <BR>, </BR>

                return (
                  <div className="text-center">
                    {brRegex.test(value) ? (
                      <>
                        <span>{value.split(brRegex)[0]}</span>
                        <br></br>
                        <span>{value.split(brRegex)[1]}</span>
                      </>
                    ) : (
                      value
                    )}
                  </div>
                )
              },
            }) as ColumnDef<CalculatedMeasure>
        )
    )
    // Identify dynamic quarterly columns (e.g., Q1_2023, Q2_2024, etc.)
    dynamicColumns.push(
      ...Object.keys(data[0] ?? {})
        .filter((key) => /^Q\d{1}_[0-9]{4}$/.test(key)) // Matches "Q1_2023", "Q2_2024", etc.
        .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
        .map(
          (key) =>
            ({
              accessorKey: key,
              cell: ({ getValue }) => {
                const value = String(getValue())
                const brRegex = /<\/?br>/i // matches <br>, </br>, <BR>, </BR>

                return (
                  <div className="text-center">
                    {brRegex.test(value) ? (
                      <>
                        <span>{value.split(brRegex)[0]}</span>
                        <br></br>
                        <span>{value.split(brRegex)[1]}</span>
                      </>
                    ) : (
                      value
                    )}
                  </div>
                )
              },
            }) as ColumnDef<CalculatedMeasure>
        )
    )
    if (selectedOptions.includes('CMS ID')) {
      dynamicColumns.push({
        accessorKey: 'cmsId',
        cell: ({ getValue }) => (
          <div className="flex w-full justify-center">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Measure Description')) {
      dynamicColumns.push({
        accessorKey: 'measureDescription',
        cell: ({ getValue }) => (
          <div className="text-center">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Friendly Name')) {
      dynamicColumns.push({
        accessorKey: 'friendlyName',
        cell: ({ getValue }) => (
          <div className="text-center">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Sub Domain')) {
      dynamicColumns.push({
        accessorKey: 'subDomain',
        cell: ({ getValue }) => (
          <div className="text-center">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Type')) {
      dynamicColumns.push({
        accessorKey: 'type',
        cell: ({ getValue }) => (
          <div className="text-center">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Sub Type')) {
      dynamicColumns.push({
        accessorKey: 'subType',
        cell: ({ getValue }) => (
          <div className="text-center">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Application')) {
      dynamicColumns.push({
        accessorKey: 'application',
        cell: ({ getValue }) => (
          <div className="text-center">{String(getValue())}</div>
        ),
      })
    }

    if (selectedOptions.includes('Program Name')) {
      dynamicColumns.push({
        accessorKey: 'programName',
        cell: ({ getValue }) => (
          <div className="text-center">{String(getValue())}</div>
        ),
      })
    }

    // Return combined static and dynamic columns
    return dynamicColumns
  }

  const columns = useMemo(() => {
    if (!hospitalMeasuresByHospital.isPending && data.length > 0) {
      return generateColumns(data)
    }

    return []
  }, [data, hospitalMeasuresByHospital.isPending])

  const [expanded, setExpanded] = useState<Record<string, boolean>>({})

  const table = useReactTable({
    data,
    columns,
    state: {
      columnPinning: {
        left: ['entityOrHospital'],
      },
      expanded,
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    sortDescFirst: false,
    initialState: {
      pagination: {
        pageSize: pageSize,
      },
    },
    onExpandedChange: (updatedExpanded: Updater<ExpandedState>) => {
      // Handle both function and direct value updates
      const newExpanded =
        typeof updatedExpanded === 'function'
          ? updatedExpanded(expanded)
          : updatedExpanded

      // Get the keys of the newly expanded state
      const expandedRows = Object.keys(newExpanded)

      // If the row is already expanded, allow it to collapse
      if (expandedRows.length === 0) {
        setExpanded({})
        return
      }

      // Get the newly expanded row
      const newlyExpandedRow = expandedRows.find(
        (row) => !expanded[row as keyof typeof expanded]
      )

      if (newlyExpandedRow) {
        // If there's a newly expanded row, only show that one
        setExpanded({ [newlyExpandedRow]: true })
      } else {
        // If we're collapsing a row, allow it
        setExpanded(newExpanded as Record<string, boolean>)
      }
    },
    getExpandedRowModel: getExpandedRowModel(),
  })

  if (hospitalMeasuresByHospital.isPending) {
    return (
      <div className="overflow-x-auto relative mt-10">
        <Loader />
      </div>
    )
  }

  return (
    <div className="block relative border-1 border-[#DDDCDF]">
      <table className="w-full border-collapse">
        <tbody>
          {table.getRowModel().rows.length === 0 ? (
            <tr>
              <td
                className="bg-[#e2e4e7] p-[1.5rem] text-center border-r border-[#DDDCDF]"
                colSpan={columns.length || 1}
              >
                No data available
              </td>
            </tr>
          ) : (
            table.getRowModel().rows.map((row) => (
              <Fragment key={row.id}>
                <tr
                  key={row.id}
                  className={`${
                    row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                  } relative border-b border-[#DDDCDF]`}
                >
                  {row.getVisibleCells().map((cell, i) => (
                    <td
                      key={cell.id}
                      className={cn(
                        `p-2 text-center align-middle border-r border-[#DDDCDF]`,
                        row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                      )}
                      style={{
                        fontSize: '14px',
                        padding: '10px 24px',
                        width: `${cell.column.getSize()}px`,
                        ...(cell.column.getIsPinned() === 'left' && {
                          boxShadow: '-4px 0px 4px -4px gray inset',
                          position: 'sticky',
                          left: 2,
                          zIndex: 2, // Ensure it remains on top
                        }),
                      }}
                    >
                      {/* <div
                        className={`w-full inline-flex ${i === 0 ? 'relative ' : ''}`}
                      > */}
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                      {/* </div> */}
                    </td>
                  ))}
                </tr>

                {row.getIsExpanded() && (
                  <tr className="relative">
                    <td
                      className="overflow-visible border-r border-[#DDDCDF]"
                      colSpan={row.getVisibleCells().length}
                    >
                      {primaryMeasureType ===
                      PrimaryMeasureTypeConstants.AmbulatoryMeasures ? (
                        <MeasuresProvidersResultsRow
                          measureIdentifier={row.original.measureIdentifier!}
                          measureTitle={measureTitle}
                          entityId={
                            (row.original as CalculatedMeasureEntity)
                              .entityCode!
                          }
                        />
                      ) : (
                        <MeasuresFacilityResultsRow
                          measureIdentifier={row.original.measureIdentifier!}
                          measureTitle={measureTitle}
                          entityId={
                            (row.original as CalculatedMeasureHospital)
                              .hospitalId!
                          }
                        />
                      )}
                    </td>
                  </tr>
                )}
              </Fragment>
            ))
          )}
        </tbody>
      </table>
      {table.getPageCount() > 1 && <Pagination table={table} />}
    </div>
  )
}
