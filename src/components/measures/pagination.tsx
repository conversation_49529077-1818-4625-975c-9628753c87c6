import { Table } from '@tanstack/react-table'
import { IoMdSkipForward } from 'react-icons/io'
import { IoCaretForward } from 'react-icons/io5'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'

type Props<T> = {
  table: Table<T>
}

export const Pagination = <T,>({ table }: Props<T>) => {
  // Calculate pagination info
  const { pageIndex, pageSize } = table.getState().pagination

  const totalItems = table.getFilteredRowModel().rows.length
  const from = pageIndex * pageSize + 1
  const to = Math.min((pageIndex + 1) * pageSize, totalItems)

  return (
    <div className="px-4 border-t border-[#DDDCDF] flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1">
          <button
            className="px-2 py-1 rounded-full text-black hover:bg-[#97A4BA] disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
          >
            <IoMdSkipForward size={16} className="rotate-180" />
          </button>
          <button
            className="px-2 py-1 rounded-full text-black hover:bg-[#97A4BA] disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <IoCaretForward size={16} className="rotate-180" />
          </button>
          <ul
            className="flex"
            style={{
              paddingInlineStart: '40px',
              marginBlockStart: '1em',
              marginBlockEnd: '1em',
              marginInlineStart: '0px',
              marginInlineEnd: '0px',
              lineHeight: '2',
              position: 'relative',
              alignItems: 'center',
              padding: '6px 6px',
              alignSelf: 'stretch',
              alignContent: 'stretch',
            }}
          >
            <GeneratePageNumbers table={table} />
          </ul>
          <button
            className="px-2 py-1 rounded-full text-black hover:bg-[#97A4BA] disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <IoCaretForward size={16} className="" />
          </button>
          <button
            className="px-2 py-1 text-black hover:bg-[#97A4BA] disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
          >
            <IoMdSkipForward size={16} className="" />
          </button>
        </div>
        <select
          value={table.getState().pagination.pageSize}
          onChange={(e) => {
            table.setPageSize(Number(e.target.value))
          }}
          className="pl-8 p-1 text-[14px] font-open-sans text-black"
        >
          {[10, 25, 50, 100].map((pageSize) => (
            <option key={pageSize} value={pageSize}>
              {pageSize}
            </option>
          ))}
        </select>
        <span
          className="text-[14px] font-open-sans text-black"
          style={{ padding: '10px 8px', lineHeight: '2' }}
        >
          Items per page
        </span>
      </div>

      {/* Added section */}
      <div className="text-[14px] text-[#0000008a] font-open-sans text-black">
        {from}-{to} of {totalItems} items
      </div>
    </div>
  )
}
