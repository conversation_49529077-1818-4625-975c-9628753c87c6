'use client'

import React, { useMemo, useState, useEffect, useRef } from 'react'
import { ArrowRight, ChevronDown, ChevronRight } from 'lucide-react'
import { IoCaretForward } from 'react-icons/io5'
import { IoMdSkipForward } from 'react-icons/io'
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getPaginationRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
  ExpandedState,
  Updater,
  Cell,
  Row,
} from '@tanstack/react-table'
import { api } from '@/trpc/react'
import { ScorecardView } from '@/enums/scorecardView'
import { cn } from '@/lib/utils'
import { useDateStore } from '@/stores/dates'
import { useFilterStore } from '@/stores/filter'
import useStore from '@/stores/useStore'
import Loader from '../ui/Loader'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'
import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'
import { useUserSessionStore } from '@/stores/userSession'
import { useActionsStore } from '@/stores/actions'
import { useViewStore } from '@/stores/viewStore'
import { getSortingIcon } from '../ui/sortIcon'
import {
  HospitalSummary,
  ScorecardResultByHospital,
} from '@/types/scorecards/scorecards'
import { ScorecardByMeasuresGrid } from './ScorecardByMeasuresGrid'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import dayjs from 'dayjs'
import { Performance } from '@/enums/performance'
import { useOrgSwitcherStore } from '@/stores/orgSwitcher'
import { getColor } from '@/lib/getColor'

type Props = {
  scorecardviewType: string
  measureIdentifier?: string
}

export const ScorecardByHospitalsGrid = ({
  scorecardviewType,
  measureIdentifier,
}: Props) => {
  const { organizationId, primaryMeasureType } = useUserSessionStore()
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [expanded, setExpanded] = useState<ExpandedState>({})
  const [scorecardResults, setScorecardResults] = useState<unknown[]>([])
  const [currentOrganizationId, setCurrentOrganizationId] = useState<string>('')

  const {
    hideEmptyIndicators,
    appliedFilters,
    inFlux: filterInFlux,
  } = useFilterStore()
  const { inFlux: orgSwitcherInFlux } = useOrgSwitcherStore()
  const dateStore = useStore(useDateStore, (state) => state)
  const { selectedOptions, setSelectedOptions, setOptions, ShowProgress } =
    useActionsStore()
  const { setTableState, tableState, visibleColumnsForView, currentView } =
    useViewStore()
  const [sorting, setSorting] = useState<SortingState>([])

  const memoizedFilters = useMemo(
    () =>
      Object.entries(appliedFilters).reduce(
        (acc, [key, value]) => {
          acc[key as keyof typeof acc] = value
          return acc
        },
        {} as Record<
          | 'measures'
          | 'subOrganizations'
          | 'submissionGroups'
          | 'providers'
          | 'organizations'
          | 'facilities',
          string[]
        >
      ),
    [JSON.stringify(appliedFilters)]
  )

  const requestPayload = useMemo(() => {
    return {
      currentView,
      primaryMeasureType,
      startDate: dateStore?.selectedRange[0]!,
      endDate: dateStore?.selectedRange[1]!,
      hideEmptyIndicators,
      scorecardView: Object.entries(ScorecardView).find(
        (entry) => entry[0] === dateStore?.selectedPeriod
      )?.[1]!,
      scorecardDisplayType: scorecardviewType,
      facilities: memoizedFilters.facilities,
      measureIdentifiers: memoizedFilters.measures,
      subOrganizationIds: memoizedFilters.subOrganizations,
      sumissionGroupIds: memoizedFilters.submissionGroups,
      organizations: memoizedFilters.organizations,
    }
  }, [
    currentView,
    primaryMeasureType,
    dateStore?.selectedRange?.[0]?.toISOString(),
    dateStore?.selectedRange?.[1]?.toISOString(),
    dateStore?.selectedPeriod,
    hideEmptyIndicators,
    memoizedFilters,
  ])

  const scorecardResultsMutation =
    api.scorecards.getScorecardByHospitalResults.useMutation()
  const abortControllerRef = useRef<AbortController | null>(null)

  useEffect(() => {
    const loadInitialData = async () => {
      if (
        !organizationId ||
        !requestPayload.currentView ||
        !requestPayload.startDate ||
        !requestPayload.endDate ||
        !requestPayload.scorecardView ||
        filterInFlux ||
        orgSwitcherInFlux ||
        requestPayload.primaryMeasureType === PrimaryMeasureTypeConstants.None
      ) {
        return
      }

      // Cancel the previous request if it exists
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController
      const abortController = new AbortController()
      abortControllerRef.current = abortController
      const signal = abortController.signal

      try {
        // Pass the signal to the request
        const data = await scorecardResultsMutation.mutateAsync({
          measureIdentifier: measureIdentifier,
          primaryMeasureType: requestPayload.primaryMeasureType,
          startDate: dayjs(requestPayload.startDate).utc().toISOString()!,
          endDate: dayjs(requestPayload.endDate).utc().toISOString()!,
          hideEmptyIndicators: requestPayload.hideEmptyIndicators,
          scorecardView: requestPayload.scorecardView,
          scorecardDisplayType: requestPayload.scorecardDisplayType,
          facilities: requestPayload.facilities,
          measureIdentifiers: requestPayload.measureIdentifiers,
          subOrganizationIds: requestPayload.subOrganizationIds,
          submissionGroupIds: requestPayload.sumissionGroupIds,
          organizations: requestPayload.organizations,
        })

        if (signal.aborted) {
          console.log('Request was aborted')
          return
        }

        setScorecardResults(data)
        setCurrentOrganizationId(organizationId!)
      } catch (error) {
        if (signal.aborted) {
          console.log('Request aborted by user')
        } else {
          console.error('Failed to load scorecard results:', error)
        }
      }
    }

    loadInitialData()

    // Cleanup function to abort any ongoing request on component unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [
    requestPayload,
    measureIdentifier,
    organizationId,
    filterInFlux,
    orgSwitcherInFlux,
  ])

  const generateColumns = (
    data: HospitalSummary[] | ScorecardResultByHospital[]
  ): ColumnDef<HospitalSummary | ScorecardResultByHospital>[] => {
    const dynamicColumns: ColumnDef<
      HospitalSummary | ScorecardResultByHospital
    >[] = []
    if (!measureIdentifier) {
      dynamicColumns.push({
        accessorKey: 'name',
        //enablePinning: true,
        size: 300,
        sortingFn: 'alphanumeric',
        header: () => (
          <div
            className="text-[#F5F7FE] font-['Open_Sans'] text-[11px] font-semibold uppercase"
            style={{ paddingTop: '3px' }}
          >
            Name
          </div>
        ),
        cell: ({ row, getValue }) => {
          return (
            <div
              style={{
                paddingLeft: `${row.depth * 2}rem`,
              }}
            >
              <div className="flex justify-between">
                <div className="flex items-center">
                  <button
                    onClick={() => row.toggleExpanded()}
                    style={{
                      display: 'flex',
                      width: '24px',
                      height: '24px',
                      padding: '4px 3.5px 3.5px 4px',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    {row.getIsExpanded() ? (
                      <ChevronDown size={24} className="text-[#566582]" />
                    ) : (
                      <ChevronRight size={24} className="text-[#566582]" />
                    )}
                  </button>
                  <div className="text-left text-black font-['Open_Sans'] text-[12px] font-normal">
                    {String(getValue())}
                  </div>
                </div>
              </div>
            </div>
          )
        },
      })

      dynamicColumns.push({
        accessorKey: 'summary',
        header: () => (
          <div className="text-[#F5F7FE] font-['Open_Sans'] text-[11px] font-semibold uppercase font-weight: 600">
            Summary
          </div>
        ),
        size: 300,
        cell: ({ getValue }) => (
          <div className="text-left text-black font-['Open_Sans'] text-[12px] font-normal">
            {String(getValue())}
          </div>
        ),
      })
    } else {
      dynamicColumns.push({
        id: 'name',
        accessorKey: 'name',
        enablePinning: true,
        size: 300,
        header: () => (
          <div className="text-[#F5F7FE] font-['Open_Sans'] text-[11px] font-semibold uppercase font-weight: 600 padding-top: 3px">
            Name
          </div>
        ),
        cell: ({ getValue }) => (
          <div className="text-left pl-6 text-black font-['Open_Sans'] text-[12px] font-normal">
            {String(getValue())}
          </div>
        ),
      })

      if (selectedOptions.includes('Trend')) {
        dynamicColumns.push({
          accessorKey: 'trendCss',
          cell: ({ getValue }) => {
            const trend: string = getValue() as string // color-angle
            const [color, angleStr] = trend.split('-')

            const isNegative = angleStr?.startsWith('n')
            const angle = isNegative ? angleStr?.slice(1) : angleStr

            return (
              <div className="flex w-full justify-center">
                <ArrowRight
                  size={16}
                  className={cn('transform inline', `text-${color}-500`)}
                  color={color === 'yellow' ? '#ddbb0b' : color}
                  style={{
                    transform: isNegative
                      ? `rotate(${angle}deg)`
                      : `rotate(-${angle}deg)`,
                  }}
                />
              </div>
            )
          },
        })
      }
      // Identify and sort dynamic yearly columns (e.g., CY_2023, CY_2024)
      dynamicColumns.push(
        ...Object.keys(data[0] ?? {})
          .filter((key) => /^CY_[0-9]{4}$/.test(key)) // Matches "CY_2023", "CY_2024", etc.
          .sort((a, b) => {
            // Sort keys by year
            const yearA = parseInt(a.split('_')[1]!, 10)
            const yearB = parseInt(b.split('_')[1]!, 10)
            return yearA - yearB
          })
          .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
          .map(
            (key) =>
              ({
                accessorKey: key,
                cell: ({ getValue }) => (
                  <div className="flex w-full justify-center">
                    {String(getValue())}
                  </div>
                ),
              }) as ColumnDef<ScorecardResultByHospital>
          )
      )

      // Identify and sort dynamic monthly columns (e.g., Jan_2024, Feb_2024)
      dynamicColumns.push(
        ...Object.keys(data[0] ?? {})
          .filter((key) => /^[A-Za-z]{3}_[0-9]{4}$/.test(key)) // Matches "Jan_2024", "Feb_2024", etc.
          .sort((a, b) => {
            // Sort keys by month and year
            const dateA = dayjs(a, 'MMM_YYYY')
            const dateB = dayjs(b, 'MMM_YYYY')
            return dateA.isBefore(dateB) ? -1 : 1
          })
          .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
          .map(
            (key) =>
              ({
                accessorKey: key,
                minSize: 150,
                cell: ({ getValue }) => (
                  <div className="flex w-full justify-center">
                    {String(getValue())}
                  </div>
                ),
              }) as ColumnDef<ScorecardResultByHospital>
          )
      )
      // Identify dynamic quarterly columns (e.g., Q1_2023, Q2_2024, etc.)
      dynamicColumns.push(
        ...Object.keys(data[0] ?? {})
          .filter((key) => /^Q\d{1}_[0-9]{4}$/.test(key)) // Matches "Q1_2023", "Q2_2024", etc.
          .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
          .map(
            (key) =>
              ({
                accessorKey: key,
                cell: ({ getValue }) => (
                  <div className="flex w-full justify-center">
                    {String(getValue())}
                  </div>
                ),
              }) as ColumnDef<ScorecardResultByHospital>
          )
      )

      if (selectedOptions.includes('CMS ID')) {
        dynamicColumns.push({
          accessorKey: 'cmsId',
          cell: ({ getValue }) => (
            <div className="flex w-full justify-center">
              {String(getValue())}
            </div>
          ),
        })
      }

      if (selectedOptions.includes('Measure Description')) {
        dynamicColumns.push({
          accessorKey: 'measureDescription',
          cell: ({ getValue }) => (
            <div className="text-center">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Friendly Name')) {
        dynamicColumns.push({
          accessorKey: 'friendlyName',
          cell: ({ getValue }) => (
            <div className="text-center">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Sub Domain')) {
        dynamicColumns.push({
          accessorKey: 'subDomain',
          cell: ({ getValue }) => (
            <div className="text-center">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Type')) {
        dynamicColumns.push({
          accessorKey: 'type',
          cell: ({ getValue }) => (
            <div className="text-center">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Sub Type')) {
        dynamicColumns.push({
          accessorKey: 'subType',
          cell: ({ getValue }) => (
            <div className="text-center">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Application')) {
        dynamicColumns.push({
          accessorKey: 'application',
          cell: ({ getValue }) => (
            <div className="text-center">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Program Name')) {
        dynamicColumns.push({
          accessorKey: 'programName',
          cell: ({ getValue }) => (
            <div className="text-center">{String(getValue())}</div>
          ),
        })
      }
    }

    return dynamicColumns
  }

  const columns = useMemo(
    () =>
      generateColumns(
        measureIdentifier
          ? (scorecardResults as ScorecardResultByHospital[])
          : (scorecardResults as HospitalSummary[]) || []
      ),
    [scorecardResults, selectedOptions]
  )

  const table = useReactTable({
    data: measureIdentifier
      ? (scorecardResults as ScorecardResultByHospital[])
      : (scorecardResults as HospitalSummary[]) || [],
    columns: columns,
    state: {
      sorting,
      columnPinning: {
        left: ['name'],
      },
      columnFilters,
      expanded,
    },
    onExpandedChange: (updatedExpanded: Updater<ExpandedState>) => {
      // Handle both function and direct value updates
      const newExpanded =
        typeof updatedExpanded === 'function'
          ? updatedExpanded(expanded)
          : updatedExpanded

      // Get the keys of the newly expanded state
      const expandedRows = Object.keys(newExpanded)

      // If the row is already expanded, allow it to collapse
      if (expandedRows.length === 0) {
        setExpanded({})
        return
      }

      // Get the newly expanded row
      const newlyExpandedRow = expandedRows.find(
        (row) => !expanded[row as keyof typeof expanded]
      )

      if (newlyExpandedRow) {
        // If there's a newly expanded row, only show that one
        setExpanded({ [newlyExpandedRow]: true } as ExpandedState)
      } else {
        // If we're collapsing a row, allow it
        setExpanded(newExpanded)
      }
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getPaginationRowModel: getPaginationRowModel(),
    //    getFilteredRowModel: getFilteredRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onStateChange: () => setTableState(table.getState()),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  })

  // useEffect(() => {
  //     let options: string[] = ['Name', 'Summary']

  //     let visibleColumns = ['Name', 'Summary']
  //     if (
  //         calculatedMeasuresQuery.data &&
  //         calculatedMeasuresQuery.data.length > 0
  //     ) {
  //         Object.keys(calculatedMeasuresQuery.data[0]!).map((x) => {
  //             if (
  //                 /^Q\d{1}_[0-9]{4}$/.test(x) ||
  //                 /^[A-Za-z]{3}_[0-9]{4}$/.test(x) ||
  //                 /^CY_[0-9]{4}$/.test(x)
  //             ) {
  //                 options.push(x.replace('_', '-'))
  //                 visibleColumns.push(x.replace('_', '-'))
  //             }
  //         })
  //         options.sort()
  //         options.unshift('Select All')
  //         setOptions(options)

  //         if (visibleColumnsForView && visibleColumnsForView.length > 0)
  //             visibleColumns = options.filter((x) =>
  //                 visibleColumnsForView.includes(x)
  //             )

  //         setSelectedOptions(visibleColumns)
  //         console.log('table state', tableState)

  //         if (tableState) {
  //             console.log('setting new state', tableState)
  //             table.setColumnFilters(tableState.columnFilters)
  //             table.setColumnOrder(tableState.columnOrder)
  //             table.setPageSize(tableState.pagination.pageSize)
  //         }
  //     }
  // }, [calculatedMeasuresQuery.data, visibleColumnsForView])

  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    textTransform: 'uppercase',
  } as React.CSSProperties

  const getTooltip = (
    cell: Cell<ScorecardResultByHospital, unknown>,
    row: Row<ScorecardResultByHospital>
  ) => {
    const content = row.original.scorecardDetailsList?.find(
      (x) => x.columnName == cell.column.id
    )

    const isMedianMeasure = !row.original.measureTitle
      ? row.original.measureDescription?.includes('Median')
      : row.original.measureTitle.includes('Median')

    return (
      <>
        <div className="text-center">
          <TooltipProvider>
            <Tooltip delayDuration={100}>
              <TooltipTrigger className="pointer">
                {String(cell.getValue())}
              </TooltipTrigger>

              <TooltipContent
                side="bottom"
                className="bg-white border border-[#dddcdf] font-[system-ui]"
              >
                <div className="mx-[-12px] mt-[-6px] bg-[#d3d3d3] py-1 px-9">
                  <span className="font-medium text-[14px]">
                    Indicator Analysis
                  </span>
                </div>
                <div className="flex flex-col items-center">
                  <span className="font-bold mb-5 mt-4">
                    {content?.columnName?.replace('_', '-')}
                  </span>
                </div>
                <div className="flex justify-center pb-5 px-7 font-normal">
                  <table>
                    <tbody>
                      <tr>
                        <td className="text-right">Goal :</td>
                        <td>{content?.goal ?? '-'}</td>
                      </tr>
                      {!isMedianMeasure && (
                        <tr>
                          <td className="text-right">Numerator : </td>
                          <td>{content?.numeratorCount ?? '-'}</td>
                        </tr>
                      )}
                      <tr>
                        <td className="text-right">Denominator : </td>
                        <td>{content?.denominatorCount ?? '-'}</td>
                      </tr>
                      <tr>
                        <td className="text-right">
                          {content?.rateTitle?.replace('-', ' ')} :
                        </td>
                        <td>{content?.rate ?? '-'}</td>
                      </tr>
                      {content?.rateTitle == 'Ratio' && (
                        <>
                          <tr>
                            <td className="text-right">Num. Days : </td>
                            <td>{content.numeratorValue ?? '-'}</td>
                          </tr>
                          <tr>
                            <td className="text-right">Denom. Days : </td>
                            <td>{content.denominatorValue ?? '-'}</td>
                          </tr>
                        </>
                      )}
                      <tr>
                        <td className="text-right">Performance : </td>
                        <td>
                          {!content?.performance ||
                            (content?.performance as Performance) ==
                            Performance.NoData
                            ? '-'
                            : Performance[
                            content?.performance as unknown as keyof typeof Performance
                            ]}
                          {/* <div className="col-span-1 no-pad analysis-' + data.Performance.toLowerCase() + '"></div> */}
                        </td>
                      </tr>
                      <tr>
                        <td className="text-right">Population : </td>
                        <td>{content?.populationCount ?? '-'}</td>
                      </tr>
                      <tr>
                        <td className="text-right">Exclusion :</td>
                        <td>{content?.excludedCount ?? '-'}</td>
                      </tr>
                      <tr>
                        <td className="text-right">Exception :</td>
                        <td>{content?.denominatorException ?? '-'}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </>
    )
  }
  const isLoading = useMemo(
    () =>
      !currentView ||
      currentOrganizationId === '' ||
      organizationId !== currentOrganizationId ||
      scorecardResultsMutation.isIdle ||
      scorecardResultsMutation.isPending ||
      ShowProgress,
    [
      currentView,
      organizationId,
      currentOrganizationId,
      scorecardResultsMutation.isIdle,
      scorecardResultsMutation.isPending,
      ShowProgress,
    ]
  )

  if (isLoading) {
    return (
      <div
        className={cn('relative', measureIdentifier ? '"top-4"' : '"top-32"')}
      >
        <Loader className="" />
      </div>
    )
  }

  return (
    <div className="flex flex-col w-full font-['Open_Sans'] text-sm">
      <div
        className={cn(
          'max-h-[800px]',
          'overflow-y-auto',
          measureIdentifier ? 'border-t-0 border-l-0' : 'border-t-[1px]'
        )}
      >
        <table className="w-full border-collapse border-spacing-0">
          {!measureIdentifier && (
            <thead>
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} >
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      colSpan={header.colSpan}
                      style={{
                        ...headerStyle,
                        ...{
                          minWidth: `${header.getSize()}px`,
                        },
                        position: 'sticky',
                        top: 0,
                        zIndex: 10,
                        borderBottom: 'none',
                      }}
                    // className={cn('border-0', 'border-r border-[#DDDCDF]')}
                    >
                      {header.isPlaceholder ? null : (
                        <div className="flex justify-center items-center">
                          <span
                            className="flex m-3 w-full text-left justify-start cursor-pointer"
                            onClick={
                              !header.id?.toString().startsWith('summary')
                                ? header.column.getToggleSortingHandler()
                                : undefined
                            }
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {!header.id?.toString().startsWith('summary') &&
                              getSortingIcon(
                                header.column.getIsSorted() as boolean,
                                header.column.getIsSorted() === 'desc'
                              )}
                          </span>
                          {header.column.getCanFilter() && (
                            <div className="flex">
                              <DataTableColumnFilter
                                column={header.column}
                                setColumnFilters={setColumnFilters}
                              />
                            </div>
                          )}
                        </div>
                      )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
          )}
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <React.Fragment key={row.id}>
                <tr
                  key={row.id}
                  className={`${row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'} `}
                  style={{ borderBottom: 'none' }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      style={{
                        fontSize: '12px',
                        fontFamily: '"Open Sans"',
                        fontWeight: 400,
                        color: '#000',
                        lineHeight: 'normal',
                        padding: '10px 24px',
                        width: `${cell.column.getSize()}px`,

                      }}
                      className={cn(
                        `p-2 align-middle ${cell.column.columnDef.id}`,
                        `${getColor<ScorecardResultByHospital>(cell, row, scorecardviewType)}`,
                        'border-0'
                      )}
                    >
                      {/^CY_[0-9]{4}$/.test(cell.column.id) ||
                        /^[A-Za-z]{3}_[0-9]{4}$/.test(cell.column.id) ||
                        (/^Q\d{1}_[0-9]{4}$/.test(cell.column.id) &&
                          String(cell.getValue() != '-')) ? (
                        <>{getTooltip(cell, row)}</>
                      ) : (
                        flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )
                      )}
                    </td>
                  ))}
                </tr>
                {row.getIsExpanded() && (
                  <tr
                    className="relative border-0"
                    style={{ borderBottom: 'none', borderTop: 'none' }}
                  >
                    <td
                      className="overflow-visible border-0"
                      style={{ borderBottom: 'none', borderTop: 'none' }}
                      colSpan={row.getVisibleCells().length}
                    >
                      <ScorecardByMeasuresGrid
                        key={row.id}
                        hospitalId={row.original.id!}
                      />
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
      {!measureIdentifier && (
        <div className="px-4 border-t border-[#DDDCDF] flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <button
                className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <IoMdSkipForward size={16} className="rotate-180" />
              </button>
              <button
                className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <IoCaretForward size={16} className="rotate-180" />
              </button>
              <ul
                className="flex"
                style={{
                  paddingInlineStart: '40px',
                  marginBlockStart: '1em',
                  marginBlockEnd: '1em',
                  marginInlineStart: '0px',
                  marginInlineEnd: '0px',
                  lineHeight: '2',
                  position: 'relative',
                  alignItems: 'center',
                  padding: '6px 6px',
                  alignSelf: 'stretch',
                  alignContent: 'stretch',
                }}
              >
                <GeneratePageNumbers table={table} />
              </ul>
              <button
                className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <IoCaretForward size={16} className="" />
              </button>
              <button
                className="px-2 py-1  text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <IoMdSkipForward size={16} className="" />
              </button>

              <select
                value={table.getState().pagination.pageSize}
                onChange={(e) => {
                  table.setPageSize(Number(e.target.value))
                }}
                className="pl-8 p-1 text-[14px] font-['Open_Sans'] text-black"
              >
                {[10, 25, 50, 100].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
              <span
                className="text-[14px] font-['Open_Sans'] text-black"
                style={{ padding: '10px 8px', lineHeight: '2' }}
              >
                Items per page
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
