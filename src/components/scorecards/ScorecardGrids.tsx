'use client'

import { useUIStore } from "@/stores/ui"
import { ScorecardByHospitalsGrid } from "./ScorecardByHospitalsGrid"
import { ScorecardByMeasuresGrid } from "./ScorecardByMeasuresGrid"

export const ScorecardGrids = () => {
    const { scorecardViewType } = useUIStore()

    return scorecardViewType === 'Hospital'
        ?
            <div>
                <ScorecardByHospitalsGrid scorecardviewType='Hospital' />
            </div>
        :
            <div>
                <ScorecardByMeasuresGrid scorecardviewType='Measure' />
            </div>
}
