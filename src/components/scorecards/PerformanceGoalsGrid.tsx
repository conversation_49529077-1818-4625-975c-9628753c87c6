'use-client'

import { api } from '@/trpc/react'
import { PerformanceGoal } from '@/types/scorecards/performanceGoal'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useEffect, useMemo, useState } from 'react'
import Loader from '../ui/Loader'
import { cn } from '@/lib/utils'
import React from 'react'
import { IoMdSkipForward } from 'react-icons/io'
import { IoCaretForward } from 'react-icons/io5'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'
import dayjs from 'dayjs'
import { Checkbox } from '../ui/checkbox'
import { toast } from '@/hooks/use-toast'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog'
import { Button } from '../ui/button'
import { PerformanceGoalMeasure } from '@/types/scorecards/performanceGoalMeasure'
import { getDateString } from '@/lib/getDateString'
import appInsights from '@/lib/applicationInsights'

type Props = {
  performanceGoal: PerformanceGoalMeasure
}
export const PerformanceGoalsGrid = ({ performanceGoal }: Props) => {
  const [goals, setGoals] = useState<PerformanceGoal[]>([])
  const [goalsToEdit, setGoalsToEdit] = useState<string[]>([])
  const [goalsToDelete, setGoalsToDelete] = useState<string[]>([])

  const goalsQuery = api.scorecards.getPerformanceGoals.useQuery({
    entityId: performanceGoal.entityId.toString(),
    measureIdentifier: performanceGoal.measureIdentifier,
  })

  const deleteGoalsMutation =
    api.scorecards.deletePerformanceGoals.useMutation()

  const updateGoalsMutation =
    api.scorecards.updatePerformanceGoals.useMutation()

  useEffect(() => {
    if (goalsQuery.data) {
      setGoals(goalsQuery.data)
      setGoalsToEdit([])
      setGoalsToDelete([])
    }
  }, [goalsQuery.data])

  const handleDeleteGoals = () => {
    deleteGoalsMutation.mutate(
      {
        entityId: performanceGoal.entityId.toString(),
        measureIdentifier: performanceGoal.measureIdentifier,
        rowKeys: goalsToDelete.includes('*')
          ? goals.map((x) => x.rowKey)
          : goalsToDelete,
      },
      {
        onSuccess: (data) => {
          setGoals(
            goalsToDelete.includes('*')
              ? []
              : goals.filter((goal) => !goalsToDelete.includes(goal.rowKey))
          )

          toast({
            className: cn('bg-green-600 text-white border-green-600'),
            title: data.message,
            variant: 'default',
          })
        },
        onError: (error) => {
          appInsights.trackException({
            exception: new Error(error.message),
            properties: {
              entityId: performanceGoal.entityId.toString(),
              measureIdentifier: performanceGoal.measureIdentifier,
              rowKeys: goalsToDelete.includes('*')
                ? goals.map((x) => x.rowKey)
                : goalsToDelete,
            },
          })

          toast({
            title: 'Server Error',
            variant: 'default',
          })
        },
      }
    )
  }

  const editGoalsDialog = () => {
    const [errorMessage, setErrorMessage] = useState<null | string>(null)
    const [openDialog, setOpenDialog] = useState(false)
    const [newGoal, setNewGoal] = useState({
      goal: '',
      yellowZone: '',
      exceptionalPerformance: '',
    })

    const handleEditGoals = () => {
      const goalsToUpdate = goalsToEdit.includes('*')
        ? goals
        : goals.filter((goal) => goalsToEdit.includes(goal.rowKey))

      goalsToUpdate.forEach((goal) => {
        goal.goalLower = Number.parseFloat(newGoal.goal) ?? goal.goalLower
        goal.yellowZone =
          Number.parseFloat(newGoal.yellowZone) ?? goal.yellowZone
        goal.exceptionalPerformance =
          Number.parseFloat(newGoal.yellowZone) ?? goal.yellowZone
      })

      setOpenDialog(false)
      updateGoalsMutation.mutate(
        {
          performanceGoals: goalsToUpdate,
        },
        {
          onSuccess: (data) => {
            goalsQuery.refetch()
            toast({
              className: cn('bg-green-600 text-white border-green-600'),
              title: data.message,
              variant: 'default',
            })
          },
          onError: (error) => {
            appInsights.trackException({
              exception: new Error(error.message),
              properties: {
                entityId: performanceGoal.entityId.toString(),
                measureIdentifier: performanceGoal.measureIdentifier,
                goalsToUpdate,
              },
            })
          },
        }
      )
    }

    return (
      <>
        <Dialog open={openDialog} onOpenChange={setOpenDialog}>
          <DialogTrigger asChild>
            <div
              className="space-x-2 rounded-[5px] items-center px-4 py-1 font-semibold bg-ui-dark-gray text-[#fff] hover:cursor-pointer"
              onClick={() => setOpenDialog(true)}
            >
              <span>EDIT GOAL</span>
            </div>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="font-open-sans text-[14px] font-bold leading-[19.07px] text-center">
                Edit Goals
              </DialogTitle>
            </DialogHeader>
            <div className=" flex flex-col font-open-sans text-[12px] leading-[16.34px]">
              <div className="my-2">
                <span className="font-semibold text-sm">Goal : </span>
                <input
                  type="number"
                  value={newGoal.goal}
                  onChange={(e) => {
                    setNewGoal({ ...newGoal, goal: e.target.value })
                    if (e.target.value) setErrorMessage(null)
                  }}
                  placeholder="Enter Lower Goal"
                  className="ml-56 px-1 border-b-[1px] focus:outline-none"
                />
              </div>

              <div className="my-2">
                <span className="font-semibold text-sm">Yello Zone : </span>
                <input
                  type="number"
                  placeholder="Enter Yellow Zone"
                  className="ml-[186px] px-1 border-b-[1px] focus:outline-none"
                  onChange={(e) =>
                    setNewGoal({ ...newGoal, yellowZone: e.target.value })
                  }
                />
              </div>

              <div className="my-2">
                <span className="font-semibold text-sm">
                  Exceptional Performance :
                </span>
                <input
                  type="number"
                  placeholder="Enter Exceptional Performance"
                  className="ml-24 px-1 border-b-[1px] focus:outline-none"
                  onChange={(e) =>
                    setNewGoal({
                      ...newGoal,
                      exceptionalPerformance: e.target.value,
                    })
                  }
                />
              </div>
              {errorMessage && (
                <div className="my-2">
                  <p className="text-red-500">{errorMessage}</p>
                </div>
              )}
              <div className="flex justify-end mt-[30px]">
                <Button
                  id="cancelAddGoal"
                  onClick={() => setOpenDialog(false)}
                  className="bg-white text-[#566582] rounded-sm border-[#566582] h-[26px] hover:bg-white hover:text-[#566582]"
                >
                  Cancel
                </Button>
                <Button
                  id="AddGoal"
                  className="ml-[15px] bg-[#566582] text-[#fff] rounded-sm h-[27px] hover:bg-[#566582] hover:text-[#fff]"
                  onClick={handleEditGoals}
                >
                  Update
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </>
    )
  }

  const generateColumns = (): ColumnDef<PerformanceGoal>[] => {
    const dynamicColumns: ColumnDef<PerformanceGoal>[] = [
      {
        accessorKey: 'startDate',
        header: () => `Start Date`,
        cell: ({ getValue }) => {
          return (
            <div className="text-center">
              {dayjs(getDateString(getValue() as Date)).format('MM/YYYY')}
            </div>
          )
        },
      },
      {
        accessorKey: 'endDate',
        header: () => `End Date`,
        cell: ({ row, getValue }) => {
          return (
            <div className="text-center">
              {dayjs(getDateString(new Date(row.original.startDate))).format(
                'MM/YYYY'
              )}
            </div>
          )
        },
      },
      {
        accessorKey: 'goalLower',
        header: () => `Goal`,
        cell: ({ getValue }) => {
          return <div className="text-center">{getValue() as string}</div>
        },
      },
      {
        accessorKey: 'yellowZone',
        header: () => `Yellow Zone`,
        cell: ({ getValue }) => {
          return <div className="text-center">{getValue() as string}</div>
        },
      },
      {
        accessorKey: 'exceptionalPerformance',
        header: () => `Exceptional Per...`,
        cell: ({ getValue }) => {
          return <div className="text-center">{getValue() as string}</div>
        },
      },
      {
        accessorKey: 'editAll',
        header: () => {
          return (
            <div className="flex items-center font-semibold text-[#566582] text-[13px]">
              <Checkbox
                className="border-[#838383] mr-2"
                id="editall"
                checked={goalsToEdit.includes('*')}
                onCheckedChange={(checked: boolean) => {
                  checked
                    ? setGoalsToEdit([...goalsToEdit, '*'])
                    : setGoalsToEdit([])
                }}
              />
              EDIT ALL
            </div>
          )
        },
        footer: () => {
          return (
            <div className="flex justify-center">
              {goalsToEdit.length > 0 && editGoalsDialog()}
            </div>
          )
        },
        cell: ({ row }) => {
          let key = row.original.rowKey
          return (
            <div className="justify-center flex items-center text-[#566582] font-semibold">
              <Checkbox
                id={'edit_' + key}
                className="mr-2"
                checked={goalsToEdit.includes(key) || goalsToEdit.includes('*')}
                onCheckedChange={(checked: boolean) => {
                  checked
                    ? setGoalsToEdit([...goalsToEdit, key])
                    : setGoalsToEdit(
                        goalsToEdit.filter(
                          (goal) => goal !== key && goal !== '*'
                        )
                      )
                }}
              />
              EDIT
            </div>
          )
        },
      },
      {
        accessorKey: 'deleteAll',
        header: () => {
          return (
            <div className="flex items-center font-semibold text-[#566582] text-[13px]">
              <Checkbox
                className="border-[#838383] mr-2"
                id="deleteall"
                checked={goalsToDelete.includes('*')}
                onCheckedChange={(checked: boolean) => {
                  checked
                    ? setGoalsToDelete([...goalsToDelete, '*'])
                    : setGoalsToDelete([])
                }}
              />
              DELETE ALL
            </div>
          )
        },
        footer: () => {
          return (
            <>
              {goalsToDelete.length > 0 && (
                <div className="flex justify-center">
                  <button
                    className="space-x-2 rounded-[5px] flex justify-between items-center px-4 py-1 font-semibold bg-ui-dark-gray text-[#fff]"
                    onClick={handleDeleteGoals}
                  >
                    <span>DELETE GOAL</span>
                  </button>
                </div>
              )}
            </>
          )
        },
        cell: ({ row }) => {
          var key = row.original.rowKey
          return (
            <div className="justify-center flex items-center text-[#566582] font-semibold">
              <Checkbox
                id={'delete_' + key}
                className="mr-2"
                checked={
                  goalsToDelete.includes(key) || goalsToDelete.includes('*')
                }
                onCheckedChange={(checked: boolean) => {
                  checked
                    ? setGoalsToDelete([...goalsToDelete, key])
                    : setGoalsToDelete(
                        goalsToDelete.filter(
                          (goal) => goal !== key && goal !== '*'
                        )
                      )
                }}
              />
              DELETE
            </div>
          )
        },
      },
    ]
    return dynamicColumns
  }

  const columns = useMemo(
    () => generateColumns(),
    [goals, goalsToEdit, goalsToDelete]
  )
  const table = useReactTable({
    data: goals,
    columns,
    state: {
      columnPinning: {
        left: ['measureName'],
      },
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  const headerStyle = {
    backgroundColor: '#F9F8F9',
    verticalAlign: 'middle',
    color: '#000000',
    height: '45px',
    fontSize: '14px',
    fontWeight: 400,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#DDDCDF',
  } as React.CSSProperties

  if (
    goalsQuery.isPending ||
    deleteGoalsMutation.isPending ||
    updateGoalsMutation.isPending
  ) {
    return (
      <div className="relative top-4">
        <Loader className="" />
      </div>
    )
  }

  return (
    <div className="flex flex-col w-full font-open-sans text-sm">
      <div
        className={cn(
          'max-h-[600px]',
          'overflow-y-auto',
          'border-x-[#DDDCDF]',
          'border-x-[1px]',
          'border-t-[#DDDCDF]',
          'border-t-[1px]',
          'mac-scrollbar'
        )}
      >
        <table className="w-full border-collapse">
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{
                      ...headerStyle,
                      ...{
                        minWidth: `${header.getSize()}px`,
                      },
                      position: 'sticky',
                      top: 0,
                      zIndex: 9,
                    }}
                    className={cn('border-b', 'border-l')}
                  >
                    {header.isPlaceholder ? null : (
                      <div className="flex justify-center items-center">
                        <span
                          className="flex m-3 w-full text-center justify-center cursor-pointer"
                          onClick={
                            !header.id?.toString().startsWith('Q')
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                        </span>
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <React.Fragment key={row.id}>
                <tr
                  key={row.id}
                  className={`${
                    row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                  } relative border-b border-[#DDDCDF]`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      style={{
                        fontSize: '14px',
                        padding: '10px 24px',
                        width: `${cell.column.getSize()}px`,
                        ...(cell.column.getIsPinned() === 'left' && {
                          boxShadow: '-4px 0px 4px -4px gray inset',
                          position: 'sticky',
                          left: 1,
                          zIndex: 1,
                        }),
                      }}
                      className={cn(
                        `p-2 align-middle border-r border-[#DDDCDF]`,
                        row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                      )}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              </React.Fragment>
            ))}
          </tbody>
          <tfoot>
            {table.getFooterGroups().map((footerGrouop) => (
              <tr key={footerGrouop.id} className="bg-[#F5F7FF]">
                {footerGrouop.headers.map((footer) =>
                  footer.id === 'editAll' || footer.id === 'deleteAll' ? (
                    <td key={footer.id} className="py-2">
                      {flexRender(
                        footer.column.columnDef.footer,
                        footer.getContext()
                      )}
                    </td>
                  ) : (
                    <td key={footer.id}>
                      {flexRender(
                        footer.column.columnDef.footer,
                        footer.getContext()
                      )}
                    </td>
                  )
                )}
              </tr>
            ))}
          </tfoot>
        </table>
      </div>
      <div className="px-4 border-t border-[#DDDCDF] flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <IoMdSkipForward size={16} className="rotate-180" />
            </button>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <IoCaretForward size={16} className="rotate-180" />
            </button>
            <ul
              className="flex"
              style={{
                paddingInlineStart: '40px',
                marginBlockStart: '1em',
                marginBlockEnd: '1em',
                marginInlineStart: '0px',
                marginInlineEnd: '0px',
                lineHeight: '2',
                position: 'relative',
                alignItems: 'center',
                padding: '6px 6px',
                alignSelf: 'stretch',
                alignContent: 'stretch',
              }}
            >
              <GeneratePageNumbers table={table} />
            </ul>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <IoCaretForward size={16} className="" />
            </button>
            <button
              className="px-2 py-1  text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <IoMdSkipForward size={16} className="" />
            </button>

            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value))
              }}
              className="pl-8 p-1 text-[14px] font-open-sans text-black"
            >
              {[10, 25, 50, 100].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
            <span
              className="text-[14px] font-open-sans text-black"
              style={{ padding: '10px 8px', lineHeight: '2' }}
            >
              Items per page
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
