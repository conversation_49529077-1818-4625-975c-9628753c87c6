'use client'

import { ScorecardView } from '@/enums/scorecardView'
import { customStringFilter } from '@/lib/customStringFilter'
import { cn } from '@/lib/utils'
import { useActionsStore } from '@/stores/actions'
import { useDateStore } from '@/stores/dates'
import { useFilterStore } from '@/stores/filter'
import { useUserSessionStore } from '@/stores/userSession'
import { useViewStore } from '@/stores/viewStore'
import { api } from '@/trpc/react'
import { ScorecardResult } from '@/types/scorecards/scorecards'
import {
  Cell,
  ColumnDef,
  ColumnFiltersState,
  ExpandedState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  Updater,
  useReactTable,
} from '@tanstack/react-table'
import { ArrowRight, ChevronDown, ChevronRight } from 'lucide-react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useStore } from 'zustand'
import { getSortingIcon } from '../ui/sortIcon'
import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'
import React from 'react'
import { IoMdSkipForward } from 'react-icons/io'
import { IoCaretForward } from 'react-icons/io5'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'
import dayjs from 'dayjs'
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
} from '../ui/tooltip'
import { Performance } from '@/enums/performance'
import Loader from '../ui/Loader'
import { FaInfoCircle } from 'react-icons/fa'
import { ScorecardByHospitalsGrid } from './ScorecardByHospitalsGrid'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { useOrgSwitcherStore } from '@/stores/orgSwitcher'
import { MeasureTypeByApplication } from '@/enums/measureTypeByApplication'
import { getMeasureSource } from '@/lib/getMeasureSource'
import { getColor } from '@/lib/getColor'

type Props = {
  scorecardviewType?: string
  hospitalId?: string
}

export const ScorecardByMeasuresGrid = ({
  scorecardviewType,
  hospitalId,
}: Props) => {
  const pinnedColumns: { [key: string]: boolean } = {
    measureTitle: true,
  }

  const { organizationId, primaryMeasureType } = useUserSessionStore()
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [currentOrganizationId, setCurrentOrganizationId] = useState<string>('')

  const {
    hideEmptyIndicators,
    appliedFilters,
    inFlux: filterInFlux,
  } = useFilterStore()
  const { inFlux: orgSwitcherInFlux } = useOrgSwitcherStore()
  const dateStore = useStore(useDateStore, (state) => state)
  const { selectedOptions, setSelectedOptions, setOptions, ShowProgress } =
    useActionsStore()
  const { setTableState, visibleColumnsForView, currentView } = useViewStore()
  const [sorting, setSorting] = useState<SortingState>([])
  const [scorecardResults, setScorecardResults] = useState<ScorecardResult[]>(
    []
  )
  const [expanded, setExpanded] = useState<ExpandedState>({})

  const memoizedFilters = useMemo(
    () =>
      Object.entries(appliedFilters).reduce(
        (acc, [key, value]) => {
          acc[key as keyof typeof acc] = value
          return acc
        },
        {} as Record<
          'measures' | 'subOrganizations' | 'submissionGroups' | 'providers',
          string[]
        >
      ),
    [JSON.stringify(appliedFilters)]
  )

  const requestPayload = useMemo(() => {
    return {
      currentView,
      primaryMeasureType,
      aggregationType: dateStore?.selectedPeriod
        ? ScorecardView[dateStore.selectedPeriod as keyof typeof ScorecardView]
        : undefined,
      startDate: dateStore?.selectedRange?.[0],
      endDate: dateStore?.selectedRange?.[1],
      hideEmptyIndicators,
      filters: memoizedFilters,
    }
    // Only re-create this object if any relevant value truly changes:
  }, [
    currentView,
    primaryMeasureType,
    dateStore?.selectedRange?.[0], // Ensure correct dependency tracking
    dateStore?.selectedRange?.[1], // Ensure correct dependency tracking
    dateStore?.selectedPeriod, // Track aggregation type correctly
    hideEmptyIndicators,
    memoizedFilters,
  ])

  const abortControllerRef = useRef<AbortController | null>(null)

  const scorecardResultsMutation =
    api.scorecards.getScorecardByMeasureResults.useMutation()

  useEffect(() => {
    const loadInitialData = async () => {
      if (
        !organizationId ||
        !requestPayload.currentView ||
        !requestPayload.startDate ||
        !requestPayload.endDate ||
        !requestPayload.aggregationType ||
        filterInFlux ||
        orgSwitcherInFlux ||
        requestPayload.primaryMeasureType === PrimaryMeasureTypeConstants.None
      ) {
        return
      }

      // Cancel the previous request if it exists
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController
      const abortController = new AbortController()
      abortControllerRef.current = abortController
      const signal = abortController.signal

      try {
        // Pass the signal to the request
        const data = await scorecardResultsMutation.mutateAsync({
          primaryMeasureType: requestPayload.primaryMeasureType,
          hideEmptyIndicators: requestPayload.hideEmptyIndicators,
          startDate: dayjs(requestPayload.startDate).utc().toISOString(),
          endDate: dayjs(requestPayload.endDate).utc().toISOString(),
          scorecardView: requestPayload.aggregationType,
          measureIdentifiers: requestPayload.filters.measures,
          hospitalId: hospitalId,
        })

        if (signal.aborted) {
          console.log('Request was aborted')
          return
        }

        setScorecardResults(data)
        setCurrentOrganizationId(organizationId!)
      } catch (error) {
        if (signal.aborted) {
          console.log('Request aborted by user')
        } else {
          console.error('Failed to load measures:', error)
        }
      }
    }

    loadInitialData()

    // Cleanup function to abort any ongoing request on component unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [
    requestPayload,
    hospitalId,
    organizationId,
    filterInFlux,
    orgSwitcherInFlux,
  ])

  const filterFunction = customStringFilter<ScorecardResult>()

  const generateColumns = (
    data: ScorecardResult[]
  ): ColumnDef<ScorecardResult>[] => {
    // Create a reusable header style object
    const headerStyle = {
      color: '#F5F7FE',
      fontFamily: '"Open Sans"',
      fontSize: '11px',
      fontStyle: 'normal',
      fontWeight: 600,
      lineHeight: 'normal',
      textTransform: 'uppercase',
      paddingTop: '3px',
    } as React.CSSProperties

    const dynamicColumns: ColumnDef<ScorecardResult>[] = [
      {
        accessorKey: 'measureTitle',
        enablePinning: true,
        size: 500,
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        header: ({ table }) => (
          <div style={headerStyle}>Measure Name </div>
        ),
        cell: ({ row, getValue }) => {
          return hospitalId ? (
            <div className="text-left pl-6">{String(getValue())}</div>
          ) : (
            <div
              style={{
                paddingLeft: `${row.depth * 2}rem`,
              }}
            >
              <div className="flex justify-between">
                <div className="flex items-center">
                  <button
                    className="focus:outline-none"
                    onClick={() => {
                      const isCurrentlyExpanded = row.getIsExpanded()
                      if (!isCurrentlyExpanded) {
                        // If expanding, collapse all other rows and only expand this one
                        setExpanded({ [row.id]: true })
                      } else {
                        // If collapsing, just collapse this row
                        setExpanded({})
                      }
                    }}
                  >
                    {row.getIsExpanded() ? (
                      <span className="relative -left-1 top-0.5">
                        <ChevronDown size={16} className="mr-2 opacity-30" />
                      </span>
                    ) : (
                      <span className="relative -left-1 top-0.5">
                        <ChevronRight size={16} className="mr-2 opacity-30" />
                      </span>
                    )}
                  </button>
                  <div className="text-left">{String(getValue())}</div>
                </div>

                {row.original.smallestInterval === 'Y' && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <FaInfoCircle fill="#566582" className="text-white" />
                      </TooltipTrigger>

                      <TooltipContent
                        side="left"
                        className="bg-white border border-[#dddcdf]"
                      >
                        <p className="text-sm">Data only exists annually</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>
          )
        },
      },
      {
        accessorKey: 'application',
        enableColumnFilter: false,
        header: () => <div style={headerStyle}>Source</div>,
        cell: ({ row, getValue }) => {
          if (!row.original.application) return <span>-</span>

          const isRegistryMeasures =
            row.original.application ===
            MeasureTypeByApplication.RegistryMeasures

          const isAbstractedMeasures =
            row.original.application ===
            MeasureTypeByApplication.AbstractedMeasures

          const isHospitalOrAmbulatoryEMeasures =
            row.original.application ===
            MeasureTypeByApplication.HospitalMeasures ||
            row.original.application ===
            MeasureTypeByApplication.AmbulatoryMeasures

          return (
            <div className="text-center">
              <span
                className={cn(
                  'px-3 py-1 rounded-full text-xs font-bold w-[66px]',
                  {
                    'bg-[#DEEEDE] text-[#496049]': isAbstractedMeasures,
                    'bg-[#D8E4EF] text-[#205782]':
                      isHospitalOrAmbulatoryEMeasures,
                    'bg-[#F8AF64] text-[#683F16]': isRegistryMeasures,
                  }
                )}
              >
                {getMeasureSource(getValue() as string)}
              </span>
            </div>
          )
        },
      },
    ]

    if (selectedOptions.includes('Trend')) {
      dynamicColumns.push({
        accessorKey: 'trendCss',
        enableColumnFilter: false,
        enableSorting: false,


        header: () => <div style={headerStyle}>Trend</div>,
        cell: ({ getValue }) => {
          const trend: string = getValue() as string // color-angle
          const [color, angleStr] = trend.split('-')

          const isNegative = angleStr?.startsWith('n')
          const angle = isNegative ? angleStr?.slice(1) : angleStr

          return (
            <div className="text-center">
              <ArrowRight
                size={21}
                className={cn('transform inline', `text-${color}-500`)}
                color={color === 'yellow' ? '#ddbb0b' : color}
                style={{
                  width: '24px',
                  height: '24px',
                  transform: isNegative
                    ? `rotate(${angle}deg)`
                    : `rotate(-${angle}deg)`,
                }}
              />
            </div>
          )
        },
      })
    }

    // Identify and sort dynamic yearly columns (e.g., CY_2023, CY_2024)
    dynamicColumns.push(
      ...Object.keys(data[0] || {})
        .filter((key) => /^CY_[0-9]{4}$/.test(key)) // Matches "CY_2023", "CY_2024", etc.
        .sort((a, b) => {
          // Sort keys by year
          const yearA = parseInt(a.split('_')[1]!, 10)
          const yearB = parseInt(b.split('_')[1]!, 10)
          return yearA - yearB
        })
        .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
        .map(
          (key) =>
            ({
              accessorKey: key,
              filterFn: filterFunction,
              header: () => (
                <div style={headerStyle}>
                  {key.replace(/_/g, '-').toUpperCase()}
                </div>
              ),
              cell: ({ getValue }) => (
                <div className="text-center">{String(getValue())}</div>
              ),
            }) as ColumnDef<ScorecardResult>
        )
    )

    // Identify and sort dynamic monthly columns (e.g., Jan_2024, Feb_2024)
    dynamicColumns.push(
      ...Object.keys(data[0] || {})
        .filter((key) => /^[A-Za-z]{3}_[0-9]{4}$/.test(key)) // Matches "Jan_2024", "Feb_2024", etc.
        .sort((a, b) => {
          // Sort keys by month and year
          const dateA = dayjs(a, 'MMM_YYYY')
          const dateB = dayjs(b, 'MMM_YYYY')
          return dateA.isBefore(dateB) ? -1 : 1
        })
        .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
        .map(
          (key) =>
            ({
              accessorKey: key,
              minSize: 150,
              filterFn: filterFunction,
              header: () => (
                <div style={headerStyle}>
                  {key.replace(/_/g, '-').toUpperCase()}
                </div>
              ),
              cell: ({ getValue }) => (
                <div className="text-center">{String(getValue())}</div>
              ),
            }) as ColumnDef<ScorecardResult>
        )
    )

    // Identify dynamic quarterly columns (e.g., Q1_2023, Q2_2024, etc.)
    dynamicColumns.push(
      ...Object.keys(data[0] || {})
        .filter((key) => /^Q\d{1}_[0-9]{4}$/.test(key)) // Matches "Q1_2023", "Q2_2024", etc.
        .filter((key) => selectedOptions.includes(key.replace(/_/g, '-')))
        .map(
          (key) =>
            ({
              accessorKey: key,
              filterFn: filterFunction,
              header: () => (
                <div style={headerStyle}>
                  {key.replace(/_/g, '-').toUpperCase()}
                </div>
              ),
              cell: ({ getValue }) => (
                <div className="text-center">{String(getValue())}</div>
              ),
            }) as ColumnDef<ScorecardResult>
        )
    )

    if (scorecardviewType == 'Measure') {
      if (selectedOptions.includes('CMS ID')) {
        dynamicColumns.push({
          accessorKey: 'cmsId',
          filterFn: filterFunction,
          header: () => `CMS ID`,
          cell: ({ getValue }) => (
            <div className="text-center">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Measure Description')) {
        dynamicColumns.push({
          accessorKey: 'measureDescription',
          header: () => 'Measure Description',
          cell: ({ getValue }) => (
            <div className="text-center w-[200px]">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Friendly Name')) {
        dynamicColumns.push({
          accessorKey: 'friendlyName',
          header: () => 'Friendly Name',
          cell: ({ getValue }) => (
            <div className="text-center w-[200px]">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Sub Domain')) {
        dynamicColumns.push({
          accessorKey: 'subDomain',
          header: () => 'Sub Domain',
          cell: ({ getValue }) => (
            <div className="text-center w-[200px]">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Type')) {
        dynamicColumns.push({
          accessorKey: 'type',
          header: () => 'Type',
          cell: ({ getValue }) => (
            <div className="text-center w-[200px]">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Sub Type')) {
        dynamicColumns.push({
          accessorKey: 'subType',
          header: () => 'Sub Type',
          cell: ({ getValue }) => (
            <div className="text-center w-[200px]">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Application')) {
        dynamicColumns.push({
          accessorKey: 'application',
          header: () => 'Application',
          cell: ({ getValue }) => (
            <div className="text-center w-[200px]">{String(getValue())}</div>
          ),
        })
      }

      if (selectedOptions.includes('Program Name')) {
        dynamicColumns.push({
          accessorKey: 'programName',
          header: () => 'Program Name',
          cell: ({ getValue }) => (
            <div className="text-center w-[200px]">{String(getValue())}</div>
          ),
        })
      }
    }
    return dynamicColumns
  }

  const columns = useMemo(
    () => generateColumns(scorecardResults || []),
    [scorecardResults, selectedOptions]
  )

  const table = useReactTable({
    data: scorecardResults || [],
    columns,
    state: {
      expanded,
      sorting,
      columnPinning: {
        left: ['measureTitle'],
      },
      columnFilters,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onStateChange: () => setTableState(table.getState()),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
    onExpandedChange: (updatedExpanded: Updater<ExpandedState>) => {
      // Handle both function and direct value updates
      const newExpanded =
        typeof updatedExpanded === 'function'
          ? updatedExpanded(expanded)
          : updatedExpanded

      // Get the keys of the newly expanded state
      const expandedRows = Object.keys(newExpanded)

      // If the row is already expanded, allow it to collapse
      if (expandedRows.length === 0) {
        setExpanded({})
        return
      }

      // Get the newly expanded row
      const newlyExpandedRow = expandedRows.find(
        (row) => !expanded[row as keyof typeof expanded]
      )

      if (newlyExpandedRow) {
        // If there's a newly expanded row, only show that one
        setExpanded({ [newlyExpandedRow]: true } as ExpandedState)
      } else {
        // If we're collapsing a row, allow it
        setExpanded(newExpanded)
      }
    },
  })

  useEffect(() => {
    let options: string[] = [
      'Measure Title',
      'Trend',
      'CMS ID',
      'Measure Description',
      'Friendly Name',
      'Sub Domain',
      'Type',
      'Sub Type',
      'Application',
      'Program Name',
    ]
    let visibleColumns = ['Measure Title', 'Trend']
    if (scorecardResults && scorecardResults.length > 0) {
      Object.keys(scorecardResults[0]!).map((x) => {
        if (
          /^Q\d{1}_[0-9]{4}$/.test(x) ||
          /^[A-Za-z]{3}_[0-9]{4}$/.test(x) ||
          /^CY_[0-9]{4}$/.test(x)
        ) {
          options.push(x.replace('_', '-'))
          visibleColumns.push(x.replace('_', '-'))
        }
      })
      options.sort()
      options.unshift('Select All')
      setOptions(options)

      if (visibleColumnsForView && visibleColumnsForView.length > 0)
        visibleColumns = options.filter((x) =>
          visibleColumnsForView.includes(x)
        )

      setSelectedOptions(visibleColumns)
    }
  }, [scorecardResults, visibleColumnsForView])

  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    textTransform: 'uppercase',
  } as React.CSSProperties

  const getTooltip = (
    cell: Cell<ScorecardResult, unknown>,
    row: Row<ScorecardResult>
  ) => {
    const content = row.original.scorecardDetailsList?.find(
      (x) => x.columnName == cell.column.id
    )

    const isMedianMeasure = !row.original.measureTitle
      ? row.original.measureDescription?.includes('Median')
      : row.original.measureTitle.includes('Median')

    return (
      <>
        <div className="text-center">
          <TooltipProvider>
            <Tooltip delayDuration={100}>
              <TooltipTrigger className="pointer">
                {String(cell.getValue()).indexOf('</br>') >= 0 ? (
                  <>
                    <span>{String(cell.getValue()).split('</br>')[0]}</span>{' '}
                    <br></br>
                    <span>{String(cell.getValue()).split('</br>')[1]}</span>
                  </>
                ) : (
                  String(cell.getValue())
                )}
              </TooltipTrigger>

              <TooltipContent
                side="bottom"
                className="bg-white border border-[#dddcdf] font-[system-ui]"
              >
                <div className="mx-[-12px] mt-[-6px] bg-[#d3d3d3] py-1 px-9">
                  <span className="font-medium text-[14px]">
                    Indicator Analysis
                  </span>
                </div>
                <div className="flex flex-col items-center">
                  <span className="font-bold mb-5 mt-4">
                    {content?.columnName?.replace('_', '-')}
                  </span>
                </div>
                <div className="flex justify-center pb-5 px-7 font-normal">
                  <table>
                    <tbody>
                      <tr>
                        <td className="text-right">Goal :</td>
                        <td>{content?.goal ?? '-'}</td>
                      </tr>
                      {!isMedianMeasure && (
                        <tr>
                          <td className="text-right">Numerator : </td>
                          <td>{content?.numeratorCount ?? '-'}</td>
                        </tr>
                      )}
                      <tr>
                        <td className="text-right">Denominator : </td>
                        <td>{content?.denominatorCount ?? '-'}</td>
                      </tr>
                      <tr>
                        <td className="text-right">
                          {content?.rateTitle?.replace('-', ' ')} :
                        </td>
                        <td>{content?.rate ?? '-'}</td>
                      </tr>
                      {content?.rateTitle == 'Ratio' && (
                        <>
                          <tr>
                            <td className="text-right">Num. Days : </td>
                            <td>{content.numeratorValue ?? '-'}</td>
                          </tr>
                          <tr>
                            <td className="text-right">Denom. Days : </td>
                            <td>{content.denominatorValue ?? '-'}</td>
                          </tr>
                        </>
                      )}
                      <tr>
                        <td className="text-right">Performance : </td>
                        <td>
                          {!content?.performance ||
                            (content?.performance as Performance) ==
                            Performance.NoData
                            ? '-'
                            : Performance[
                            content?.performance as unknown as keyof typeof Performance
                            ]}
                          {/* <div className="col-span-1 no-pad analysis-' + data.Performance.toLowerCase() + '"></div> */}
                        </td>
                      </tr>
                      <tr>
                        <td className="text-right">Population : </td>
                        <td>{content?.populationCount ?? '-'}</td>
                      </tr>
                      <tr>
                        <td className="text-right">Exclusion :</td>
                        <td>{content?.excludedCount ?? '-'}</td>
                      </tr>
                      <tr>
                        <td className="text-right">Exception :</td>
                        <td>{content?.denominatorException ?? '-'}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </>
    )
  }

  const isLoading = useMemo(
    () =>
      !currentView ||
      currentOrganizationId === '' ||
      organizationId !== currentOrganizationId ||
      scorecardResultsMutation.isIdle ||
      scorecardResultsMutation.isPending ||
      ShowProgress,

    [
      currentView,
      organizationId,
      currentOrganizationId,
      scorecardResultsMutation.isIdle,
      scorecardResultsMutation.isPending,
      ShowProgress,
    ]
  )

  if (isLoading) {
    return (
      <div className="relative">
        <Loader className="" />
      </div>
    )
  }

  return (
    <div className="flex flex-col w-full font-['Open_Sans'] text-sm">
      <div
        className={cn(
          'max-h-fit',
          'overflow-y-auto',
          hospitalId ? '-mt-[2px]' : '',
          hospitalId ? '-ml-[2px]' : ''
        )}
      >
        <table className="w-full border-collapse">
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id} className="border-b-0">
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{
                      ...headerStyle,
                      ...{
                        minWidth: `${header.getSize()}px`,
                      },
                      position: 'sticky',
                      top: 0,
                      zIndex: 10,
                      ...(pinnedColumns[header.column.id] && {
                        boxShadow: '-4px 0px 4px -4px gray inset',
                        left: 0,
                        zIndex: 11,
                      }),
                      borderBottom: 'none', // Remove bottom border
                    }}

                  >
                    {header.isPlaceholder ? null : (
                      <div className="flex justify-center items-center">
                        <span
                          className="flex m-3 w-full text-center justify-center cursor-pointer"
                          onClick={
                            !header.id?.toString().startsWith('Q')
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {!header.id?.toString().startsWith('Q') &&
                            getSortingIcon(
                              header.column.getIsSorted() as boolean,
                              header.column.getIsSorted() === 'desc'
                            )}
                        </span>
                        {header.column.getCanFilter() && (
                          <div className="flex">
                            <DataTableColumnFilter
                              column={header.column}
                              setColumnFilters={setColumnFilters}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <React.Fragment key={row.id}>
                <tr
                  key={row.id}
                  className={`${row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'} relative border-b-0`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      style={{
                        color: '#000',
                        fontFamily: '"Open Sans"',
                        fontSize: '12px',
                        fontStyle: 'normal',
                        fontWeight: 400,
                        lineHeight: 'normal',
                        padding: '10px 24px',
                        width: `${cell.column.getSize()}px`,
                        borderBottom: 'none', // Remove bottom border
                        ...(cell.column.getIsPinned() === 'left' && {
                          boxShadow: '-4px 0px 4px -4px gray inset',
                          position: 'sticky',
                          left: 1,
                          zIndex: 1,
                        }),
                      }}
                      className={cn(
                        `p-2 align-middle ${cell.column.columnDef.id}`,
                        `${getColor<ScorecardResult>(cell, row)}`,
                        'border-b-0' // Remove bottom border class
                      )}
                    >
                      {/^CY_[0-9]{4}$/.test(cell.column.id) ||
                        /^[A-Za-z]{3}_[0-9]{4}$/.test(cell.column.id) ||
                        (/^Q\d{1}_[0-9]{4}$/.test(cell.column.id) &&
                          String(cell.getValue() != '-')) ? (
                        <>{getTooltip(cell, row)}</>
                      ) : (
                        flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )
                      )}
                    </td>
                  ))}
                </tr>
                {row.getIsExpanded() && (
                  <tr className="relative border-b-0">
                    <td
                      className="overflow-visible border-b-0"
                      colSpan={row.getVisibleCells().length}
                    >
                      <ScorecardByHospitalsGrid
                        key={row.id}
                        scorecardviewType="Measure"
                        measureIdentifier={row.original.measureIdentifier!}
                      />
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
      <div className="px-4 border-t border-[#DDDCDF] flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <IoMdSkipForward size={16} className="rotate-180" />
            </button>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <IoCaretForward size={16} className="rotate-180" />
            </button>
            <ul
              className="flex"
              style={{
                paddingInlineStart: '40px',
                marginBlockStart: '1em',
                marginBlockEnd: '1em',
                marginInlineStart: '0px',
                marginInlineEnd: '0px',
                lineHeight: '2',
                position: 'relative',
                alignItems: 'center',
                padding: '6px 6px',
                alignSelf: 'stretch',
                alignContent: 'stretch',
              }}
            >
              <GeneratePageNumbers table={table} />
            </ul>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <IoCaretForward size={16} className="" />
            </button>
            <button
              className="px-2 py-1  text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <IoMdSkipForward size={16} className="" />
            </button>

            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value))
              }}
              className="pl-8 p-1 text-[14px] font-['Open_Sans'] text-black"
            >
              {[10, 25, 50, 100].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
            <span
              className="text-[14px] font-['Open_Sans'] text-black"
              style={{ padding: '10px 8px', lineHeight: '2' }}
            >
              Items per page
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
