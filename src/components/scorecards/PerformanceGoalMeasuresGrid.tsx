'use client'
import { customStringFilter } from '@/lib/customStringFilter'
import { cn } from '@/lib/utils'
import { api } from '@/trpc/react'
import { PerformanceGoalMeasure } from '@/types/scorecards/performanceGoalMeasure'
import {
  ColumnDef,
  ColumnFiltersState,
  ExpandedState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  Updater,
  useReactTable,
} from '@tanstack/react-table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { ArrowRight, ChevronDown, ChevronRight } from 'lucide-react'
import { useEffect, useMemo, useState } from 'react'
import Loader from '../ui/Loader'
import { getSortingIcon } from '../ui/sortIcon'
import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'
import React from 'react'
import { IoMdSkipForward } from 'react-icons/io'
import { IoCaretForward } from 'react-icons/io5'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'
import { PerformanceGoalsGrid } from './PerformanceGoalsGrid'
import { INotation } from '@/enums/iNotation'
import { Checkbox } from '../ui/checkbox'
import { Button } from '../ui/button'
import dayjs, { Dayjs } from 'dayjs'
import { Input } from '../ui/input'
import { toast } from '@/hooks/use-toast'
import appInsights from '@/lib/applicationInsights'
import { MeasureTypeByApplication } from '@/enums/measureTypeByApplication'
import { getMeasureSource } from '@/lib/getMeasureSource'

export const PerformanceGoalMeasureGrid = () => {
  const [measures, setMeasures] = useState<PerformanceGoalMeasure[]>([])
  const [sorting, setSorting] = useState<SortingState>([])
  const [expanded, setExpanded] = useState<ExpandedState>({})
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])

  const result = api.scorecards.getMeasuresForPerformanceGoals.useQuery()
  useEffect(() => {
    if (result.data) {
      setMeasures(result.data)
    }
  }, [result.data])

  const filterFunction = customStringFilter<PerformanceGoalMeasure>()
  const upsertGoalsMutation =
    api.scorecards.upsertPerformanceGoals.useMutation()

  const addGoalDialog = (
    measurename: string,
    measureIdentifier: string,
    entityId: string,
    trend: INotation
  ) => {
    const [openDialog, setOpenDialog] = useState(false)
    const [errorMessage, setErrorMessage] = useState<null | string>(null)
    const [newGoal, setNewGoal] = useState({
      startDate: dayjs.utc(),
      endDate: dayjs.utc().add(1, 'month'),
      goal: '',
      yellowZone: '',
      exceptionalPerformance: '',
      IsYellowZonePercent: false,
      IsExceptionalZonePercent: false,
    })

    const trpcUtils = api.useUtils()

    const addGoal = () => {
      if (!newGoal.goal) {
        setErrorMessage('Enter the valid goal value')
        return
      }

      setErrorMessage(null)
      setOpenDialog(false)

      console.log(newGoal)
      upsertGoalsMutation.mutate(
        {
          isEditProcess: false,
          measureIdentifier: measureIdentifier,
          entityId: entityId.toString(),
          startDate: newGoal.startDate.utc().toDate(),
          endDate: newGoal.endDate.utc().toDate(),
          isExceptionalPerformanceNumber: !newGoal.IsExceptionalZonePercent,
          isYellowZoneFixedNumber: !newGoal.IsYellowZonePercent,
          yellowZone: newGoal.yellowZone
            ? Number.parseFloat(newGoal.yellowZone)
            : undefined,
          exceptionalPerformance: newGoal.exceptionalPerformance
            ? Number.parseFloat(newGoal.exceptionalPerformance)
            : undefined,
          goalLower: Number.parseFloat(newGoal.goal),
        },
        {
          onSuccess: (data) => {
            toast({
              className: cn('bg-green-600 text-white border-green-600'),
              title: data.message,
              variant: 'default',
            })
            trpcUtils.scorecards.getPerformanceGoals.invalidate();
            const measure = measures.find((x) => x.measureIdentifier === measureIdentifier);
            if (measure) {
              measure.goalExists = true;
            }
          },
          onError: (error) => {
            appInsights.trackException({
              exception: new Error(error.message),
              properties: {
                measureIdentifier,
                entityId,
              },
            })
          },
        }
      )
    }

    return (
      <>
        <Dialog open={openDialog} onOpenChange={setOpenDialog}>
          <DialogTrigger asChild>
            <div
              onClick={() => setOpenDialog(true)}
              className="w-24 rounded-[5px] text-sm py-1 border-[1px] border-ui-dark-gray font-semibold bg-ui-pale-blue text-ui-dark-gray hover:cursor-pointer"
            >
              ADD GOAL
            </div>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="font-open-sans text-[14px] font-bold leading-[19.07px] text-center">
                Set Measure Goal
              </DialogTitle>
            </DialogHeader>
            <div className=" flex flex-col font-open-sans text-[12px] leading-[16.34px]">
              <div className="flex my-2">
                <span className="font-semibold text-sm">Measure Name : </span>
                <p className="ml-1">{measurename}</p>
              </div>
              <div className="my-2">
                <span className="font-semibold text-sm">Trend : </span>
                <ArrowRight
                  strokeWidth={3}
                  size={21}
                  className={cn(
                    'transform inline mb-1',
                    `text-${trend === INotation.Higher ? 'green' : 'black'}-500`
                  )}
                  color={trend === INotation.Higher ? 'green' : 'black'}
                  style={{
                    transform:
                      trend === INotation.Higher
                        ? `rotate(-90deg)`
                        : `rotate(90deg)`,
                  }}
                />
                ({' ' + INotation[trend] + ' '})
              </div>
              <div className="flex items-center mb-2">
                <div className="font-semibold text-sm">Start Date : </div>
                <Input
                  className="w-[200px] px-6 py-[4px] border-[0.5px] border-[#e7e7e7] rounded-sm mx-5"
                  type="month"
                  value={newGoal.startDate.format('YYYY-MM')}
                  onChange={(e) =>
                    setNewGoal({
                      ...newGoal,
                      startDate: dayjs.utc(e.target.value),
                    })
                  }
                />
                {/* <div  x
                  className="w-[200px] px-8 py-[6px] border-[0.5px] border-[#e7e7e7] rounded-sm"
                  onClick={() => {
                    setCurrentYear(dayjs(new Date()))
                    toggleOpen(open ? false : true)
                  }}
                >
                  {startDate?.format('MMMM YYYY')}
                </div>
                {openDatePicker && renderYear(currentYear.year(), true)} */}
                <span className="font-semibold text-sm">End Date : </span>

                {/* <span
                  className=' className="px-8 py-[6px] boder-[0.5px] boder-[#e7e7e7] rounded-sm'
                  onClick={() => {
                    setCurrentYear(dayjs(new Date()))
                    toggleOpen(!open)
                  }}
                >
                  {selectedRange[1]!?.format('MMM YYYY')}
                </span> */}

                <Input
                  className="w-[200px] px-6 py-[4px] border-[0.5px] border-[#e7e7e7] rounded-sm mx-5"
                  type="month"
                  value={newGoal.endDate.format('YYYY-MM')}
                  onChange={(e) =>
                    setNewGoal({
                      ...newGoal,
                      endDate: dayjs.utc(e.target.value),
                    })
                  }
                />
              </div>
              <div className="my-2">
                <span className="font-semibold text-sm">Goal : </span>
                <input
                  type="number"
                  value={newGoal.goal}
                  onChange={(e) => {
                    setNewGoal({ ...newGoal, goal: e.target.value })
                    if (e.target.value) setErrorMessage(null)
                  }}
                  placeholder="Enter Lower Goal"
                  className="ml-56 px-1 border-b-[1px] focus:outline-none"
                />
              </div>
              <div className="my-2">
                <span className="font-semibold text-sm">
                  Is Yellow Zone a Percent Value? :
                </span>
                <Checkbox
                  id="IsYellowZonePercent"
                  className="ml-16"
                  checked={newGoal.IsYellowZonePercent}
                  onClick={(e) =>
                    setNewGoal({
                      ...newGoal,
                      IsYellowZonePercent: !newGoal.IsYellowZonePercent,
                    })
                  }
                />
              </div>
              <div className="my-2">
                <span className="font-semibold text-sm">Yello Zone : </span>
                <input
                  type="number"
                  placeholder="Enter Yellow Zone"
                  className="ml-[186px] px-1 border-b-[1px] focus:outline-none"
                  onChange={(e) =>
                    setNewGoal({ ...newGoal, yellowZone: e.target.value })
                  }
                />
              </div>
              <div className="my-2">
                <span className="font-semibold text-sm">
                  Is Yellow Zone a Percent Value? :
                </span>
                <Checkbox
                  id="IsExceptionalZonePercent"
                  className="ml-16"
                  checked={newGoal.IsExceptionalZonePercent}
                  onClick={(e) =>
                    setNewGoal({
                      ...newGoal,
                      IsExceptionalZonePercent:
                        !newGoal.IsExceptionalZonePercent,
                    })
                  }
                />
              </div>
              <div className="my-2">
                <span className="font-semibold text-sm">
                  Exceptional Performance :
                </span>
                <input
                  type="number"
                  placeholder="Enter Exceptional Performance"
                  className="ml-24 px-1 border-b-[1px] focus:outline-none"
                  onChange={(e) =>
                    setNewGoal({
                      ...newGoal,
                      exceptionalPerformance: e.target.value,
                    })
                  }
                />
              </div>
              {errorMessage && (
                <div className="my-2">
                  <p className="text-red-500">{errorMessage}</p>
                </div>
              )}
              <div className="flex justify-end mt-[30px]">
                <Button
                  id="cancelAddGoal"
                  onClick={() => setOpenDialog(false)}
                  className="bg-white text-[#566582] rounded-sm border-[#566582] h-[26px] hover:bg-white hover:text-[#566582]"
                >
                  Cancel
                </Button>
                <Button
                  id="AddGoal"
                  className="ml-[15px] bg-[#566582] text-[#fff] rounded-sm h-[27px] hover:bg-[#566582] hover:text-[#fff]"
                  onClick={addGoal}
                >
                  Submit
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </>
    )
  }

  const generateColumns = (): ColumnDef<PerformanceGoalMeasure>[] => {
    const dynamicColumns: ColumnDef<PerformanceGoalMeasure>[] = [
      {
        accessorKey: 'measureName',
        enablePinning: true,
        size: 500,
        filterFn: filterFunction,
        sortingFn: 'alphanumeric',
        header: ({ table }) => (
          <div
            className="text-[#F5F7FE] font-['Open_Sans'] text-[11px] font-semibold uppercase"
            style={{
              color: '#F5F7FE',
              fontFamily: '"Open Sans"',
              fontSize: '11px',
              fontStyle: 'normal',
              fontWeight: 600,
              lineHeight: 'normal',
              textTransform: 'uppercase',
              paddingTop: '3px',
            }}
          >
            Measure Title ({table.getRowCount()})
          </div>
        ),
        cell: ({ row, getValue }) => {
          return (
            <div
              style={{
                paddingLeft: `${row.depth * 2}rem`,
              }}
            >
              <div className="flex justify-between">
                <div
                  className={cn(
                    'flex items-center',
                    row.original.goalExists ? 'font-semibold' : ''
                  )}
                >
                  <button
                    className="focus:outline-none"
                    onClick={() => row.toggleExpanded()}
                  >
                    {row.getIsExpanded() ? (
                      <span className="relative -left-1 top-0.5">
                        <ChevronDown size={16} className="mr-2 opacity-30" />
                      </span>
                    ) : (
                      <span className="relative -left-1 top-0.5">
                        <ChevronRight size={16} className="mr-2 opacity-30" />
                      </span>
                    )}
                  </button>
                  {getValue() as string}
                </div>
              </div>
            </div>
          )
        },
      },
      {
        accessorKey: 'applicationName',
        enableColumnFilter: false,
        header: () => `Source`,
        cell: ({ row, getValue }) => {
          if (!row.original.applicationName) return <span>-</span>

          const isRegistryMeasures =
            row.original.applicationName ===
            MeasureTypeByApplication.RegistryMeasures

          const isAbstractedMeasures =
            row.original.applicationName ===
            MeasureTypeByApplication.AbstractedMeasures

          const isHospitalOrAmbulatoryEMeasures =
            row.original.applicationName ===
              MeasureTypeByApplication.HospitalMeasures ||
            row.original.applicationName ===
              MeasureTypeByApplication.AmbulatoryMeasures

          return (
            <div className="text-center">
              <span
                className={cn(
                  'px-3 py-1 rounded-full text-xs font-bold w-[66px]',
                  {
                    'bg-[#DEEEDE] text-[#496049]': isAbstractedMeasures,
                    'bg-[#D8E4EF] text-[#205782]':
                      isHospitalOrAmbulatoryEMeasures,
                    'bg-[#F8AF64] text-[#683F16]': isRegistryMeasures,
                  }
                )}
              >
                {getMeasureSource(getValue() as string)}
              </span>
            </div>
          )
        },
      },
      {
        accessorKey: 'measureFriendlyName',
        filterFn: filterFunction,
        header: () => `Friendly Name`,
        cell: ({ getValue }) => {
          return <div className="text-center">{getValue() as string}</div>
        },
      },
      {
        accessorKey: 'iNotationName',
        filterFn: filterFunction,
        header: () => `Trend`,
        cell: ({ getValue }) => {
          return <div className="text-center">{getValue() as string}</div>
        },
      },
      {
        accessorKey: 'domainName',
        filterFn: filterFunction,
        header: () => `Domain`,
        cell: ({ getValue }) => {
          return <div className="text-center">{getValue() as string}</div>
        },
      },
      {
        accessorKey: 'actions',
        header: () => `Actions`,
        cell: ({ row }) => {
          return (
            <div className="text-center flex justify-center">
              {addGoalDialog(
                row.original.measureName,
                row.original.measureIdentifier,
                row.original.entityId,
                row.original.iNotationName
              )}
            </div>
          )
        },
      },
    ]
    return dynamicColumns
  }

  const columns = useMemo(() => generateColumns(), [measures])

  const table = useReactTable({
    data: measures,
    columns,
    state: {
      sorting,
      columnPinning: {
        left: ['measureName'],
      },
      columnFilters,
      expanded,
    },
    onExpandedChange: (updatedExpanded: Updater<ExpandedState>) => {
      // Handle both function and direct value updates
      const newExpanded =
        typeof updatedExpanded === 'function'
          ? updatedExpanded(expanded)
          : updatedExpanded

      // Get the keys of the newly expanded state
      const expandedRows = Object.keys(newExpanded)

      // If the row is already expanded, allow it to collapse
      if (expandedRows.length === 0) {
        setExpanded({})
        return
      }

      // Get the newly expanded row
      const newlyExpandedRow = expandedRows.find(
        (row) => !expanded[row as keyof typeof expanded]
      )

      if (newlyExpandedRow) {
        // If there's a newly expanded row, only show that one
        setExpanded({ [newlyExpandedRow]: true } as ExpandedState)
      } else {
        // If we're collapsing a row, allow it
        setExpanded(newExpanded)
      }
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
  } as React.CSSProperties

  if (result.isLoading || upsertGoalsMutation.isPending) {
    return (
      <div className="relative top-32 text-center">
        <Loader className="" />
      </div>
    )
  }

  return (
    <div className="flex flex-col w-full font-open-sans text-sm">
      <div
        className={cn(
          'max-h-[500px]',
          'overflow-y-auto',
          'border-x-[#DDDCDF]',
          'border-x-[1px]',
          'border-t-[#DDDCDF]',
          'border-t-[1px]',
          'mac-scrollbar'
        )}
      >
        <table className="w-full border-collapse">
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{
                      ...headerStyle,
                      ...{
                        minWidth: `${header.getSize()}px`,
                      },
                      position: 'sticky',
                      top: 0,
                      zIndex: 10,
                    }}
                    className={cn('border-b', 'border-l')}
                  >
                    {header.isPlaceholder ? null : (
                      <div className="flex justify-center items-center">
                        <span
                          className="flex items-center m-3 w-full text-center justify-center cursor-pointer"
                          onClick={
                            !header.id?.toString().startsWith('Q')
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {!header.id?.toString().startsWith('Actions') &&
                            getSortingIcon(
                              header.column.getIsSorted() as boolean,
                              header.column.getIsSorted() === 'desc'
                            )}
                        </span>
                        {header.column.getCanFilter() && (
                          <div className="flex">
                            <DataTableColumnFilter
                              column={header.column}
                              setColumnFilters={setColumnFilters}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <React.Fragment key={row.id}>
                <tr
                  key={row.id}
                  className={`${
                    row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                  } relative border-b border-[#DDDCDF]`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      style={{
                        fontSize: '14px',
                        padding: '10px 24px',
                        width: `${cell.column.getSize()}px`,
                        ...(cell.column.getIsPinned() === 'left' && {
                          boxShadow: '-4px 0px 4px -4px gray inset',
                          position: 'sticky',
                          left: 1,
                          zIndex: 1,
                        }),
                      }}
                      className={cn(
                        `p-2 align-middle border-r border-[#DDDCDF]`,
                        row.index % 2 === 0 ? 'bg-white' : 'bg-[#F9F8F9]'
                      )}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
                {row.getIsExpanded() && (
                  <tr className="relative">
                    <td
                      className="overflow-visible border-b border-[#DDDCDF]"
                      colSpan={row.getVisibleCells().length}
                    >
                      <PerformanceGoalsGrid
                        key={row.id}
                        performanceGoal={row.original!}
                      />
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
      <div className="px-4 border-t border-[#DDDCDF] flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <IoMdSkipForward size={16} className="rotate-180" />
            </button>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <IoCaretForward size={16} className="rotate-180" />
            </button>
            <ul
              className="flex"
              style={{
                paddingInlineStart: '40px',
                marginBlockStart: '1em',
                marginBlockEnd: '1em',
                marginInlineStart: '0px',
                marginInlineEnd: '0px',
                lineHeight: '2',
                position: 'relative',
                alignItems: 'center',
                padding: '6px 6px',
                alignSelf: 'stretch',
                alignContent: 'stretch',
              }}
            >
              <GeneratePageNumbers table={table} />
            </ul>
            <button
              className="px-2 py-1 rounded-full text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <IoCaretForward size={16} className="" />
            </button>
            <button
              className="px-2 py-1  text-black hover:bg-['#97A4BA'] disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <IoMdSkipForward size={16} className="" />
            </button>

            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value))
              }}
              className="pl-8 p-1 text-[14px] font-open-sans text-black"
            >
              {[10, 25, 50, 100].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
            <span
              className="text-[14px] font-open-sans text-black"
              style={{ padding: '10px 8px', lineHeight: '2' }}
            >
              Items per page
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
