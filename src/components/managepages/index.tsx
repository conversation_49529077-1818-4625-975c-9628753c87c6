'use client'
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  getFilteredRowModel,
  SortingState,
  getSortedRowModel,
} from '@tanstack/react-table'
import { useToast } from '@/hooks/use-toast'
import { useActionsStore } from '@/stores/actions'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { api } from '@/trpc/react'
import Loader from '@/components/ui/Loader'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'
import React, { useState } from 'react'
import { DataTableColumnFilter } from '../dataTable/dataTableColumnFilter'
import { useViewStore } from '@/stores/viewStore'

import { customStringFilter } from '@/lib/customStringFilter'
import { Pages } from '@/types/pages'
import appInsights from '@/lib/applicationInsights'

export const ManagePages = () => {
  const { toast } = useToast()

  //getting data from pages azurestoragetable
  const [pages, query] = api.pages.getAll.useSuspenseQuery<Pages[]>()
  // Sort pages by sortOrder
  pages.sort((a, b) => a.sortOrder - b.sortOrder)

  //Updating data in azurestoragetable
  const pageConfigurationsMutation = api.pages.savePageVisibility.useMutation()

  const [loadingStates, setLoadingStates] = useState(new Map<string, boolean>())

  const handleCheckboxChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const rowKey = event.target.value
    const isVisible = event.target.checked

    setLoadingStates((prev) => new Map(prev).set(rowKey, true))

    try {
      await pageConfigurationsMutation.mutateAsync(
        {
          pageconfigs: [{ rowKey, IsVisible: isVisible }],
        },
        {
          onSuccess: () => {
            toast({
              title: 'Success',
              description: 'Page visibility updated successfully',
            })
            query.refetch() // Refresh the data after successful update
            setLoadingStates((prev) => {
              const newMap = new Map(prev)
              newMap.delete(rowKey)
              return newMap
            })
          },
          onError: (error) => {
            appInsights.trackException({
              exception: new Error(error.message),
              properties: {
                rowKey,
                isVisible,
              },
            })

            toast({
              variant: 'destructive',
              title: 'Error',
              description: 'Failed to update page visibility',
            })
            setLoadingStates((prev) => {
              const newMap = new Map(prev)
              newMap.delete(rowKey)
              return newMap
            })
          },
        }
      )
    } catch (error) {
      console.error('Error updating page visibility:', error)
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update page visibility',
      })
      setLoadingStates((prev) => {
        const newMap = new Map(prev)
        newMap.delete(rowKey)
        return newMap
      })
    }
  }

  //columns function
  const { selectedOptions, setSelectedOptions, setOptions } = useActionsStore()
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const { setTableState, tableState, visibleColumnsForView, currentView } =
    useViewStore()
  const [filterValues, setFilterValues] = useState<{ [key: string]: string }>(
    {}
  )

  const filterFunction = customStringFilter<Pages>()
  const setFilter = (columnAccessorKey: string, value: string) => {
    setFilterValues((prev) => ({
      ...prev,
      [columnAccessorKey]: value,
    }))
  }

  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
  } as React.CSSProperties

  const table = useReactTable({
    data: pages,
    columns: React.useMemo(() => {
      if (pages.length === 0) return []

      if (!pages) return []

      return [
        {
          accessorKey: 'name',
          header: 'Page Name',
          filterFn: filterFunction,
          cell: (info: any) => (
            <div className="text-center">{String(info.getValue())}</div>
          ),
        },
        {
          accessorKey: 'isVisible',
          header: 'Is Visible',
          enableColumnFilter: false,
          cell: (info: any) => (
            <div className="text-center">
              <input
                type="checkbox"
                checked={info.getValue() === true}
                onChange={handleCheckboxChange}
                value={info.row.original?.rowKey}
                disabled={loadingStates.get(info.row.original?.rowKey) || false}
              />
              {loadingStates.get(info.row.original?.rowKey) && (
                <Loader2 className="ml-2 h-4 w-4 animate-spin inline-block" />
              )}
            </div>
          ),
        },
      ] as ColumnDef<any>[]
    }, [pages, loadingStates]),
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnFiltersChange: setColumnFilters,
    sortDescFirst: false,
    initialState: {
      pagination: {
        pageSize: 20,
      },
    },
    state: {
      sorting,
      columnFilters,
    },
  })

  return (
    <Card className="border">
      <CardHeader>
        <CardTitle>Manage admin Pages</CardTitle>
        <CardDescription>Configure visibility of admin pages</CardDescription>
      </CardHeader>
      <CardContent>
        {query.isPending || query.isRefetching ? (
          <div className="overflow-x-auto relative mt-10">
            <Loader />
          </div>
        ) : (
          <div>
            <table className="w-[50%] border-collapse mx-auto border">
              <thead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <th
                          key={header.id}
                          colSpan={header.colSpan}
                          style={{
                            ...headerStyle,
                            ...{
                              minWidth: `${header.getSize()}px`,
                            },
                            position: 'sticky',
                            top: 0,
                            zIndex: 10,
                          }}
                          className={cn(
                            header.column.getIsSorted() &&
                              (header.column.getIsSorted() === 'asc'
                                ? 'text-green-500'
                                : 'text-red-500'),
                            'text-left text-sm font-medium text-gray-900 px-6 py-4'
                          )}
                        >
                          {header.isPlaceholder ? null : (
                            <div className="flex justify-center items-center">
                              <span
                                className="flex m-3 w-full text-center justify-center"
                                onClick={header.column.getToggleSortingHandler()}
                              >
                                {flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                              </span>
                              {header.column.getCanFilter() && (
                                <div className="flex">
                                  <DataTableColumnFilter
                                    column={header.column}
                                    setColumnFilters={setColumnFilters}
                                  />
                                </div>
                              )}
                            </div>
                          )}
                        </th>
                      )
                    })}
                  </tr>
                ))}
              </thead>
              <tbody>
                {table.getRowModel().rows.map((row) => (
                  <tr key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={cell.id}
                        className="px-6 py-4 border-b text-center"
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
