'use client'

import { ChevronDown } from 'lucide-react'
import {
  <PERSON><PERSON><PERSON>,
  MenubarContent,
  MenubarMenu,
  MenubarTrigger,
} from '@/components/ui/menubar'
import { cn } from '@/lib/utils'
import SearchAndFilter from './searchAndFilter'
import { useUserSessionStore } from '@/stores/userSession'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth'
import { useSession } from 'next-auth/react'
import { api } from '@/trpc/react'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { useState, useMemo } from 'react'
import { useViewStore } from '@/stores/viewStore'
import { DefaultNames } from '@/enums/defaultNames'
import dayjs from 'dayjs'
import { useOrgSwitcherStore } from '@/stores/orgSwitcher'

const OrgSelector = () => {
  const router = useRouter()
  const { data: session } = useSession()
  const { isAuthorized } = useAuthStore()
  const [isPending, setIsPending] = useState<boolean>(true)

  useEffect(() => {
    if (!session || !isAuthorized) {
      router.push('/session/expired')
    }
  }, [session, isAuthorized])

  const { setCurrentView } = useViewStore()
  const { setInFlux } = useOrgSwitcherStore()

  const {
    switchToOrganization,
    setOrganization,
    organizationId: currentOrganizationId,
    organizationName,
  } = useUserSessionStore()

  const selectedOrganizationQuery = api.organizations.getSelected.useQuery(
    undefined,
    {
      refetchInterval: false,
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
    }
  )

  const [organizationsQuery, partnersQuery] = api.useQueries((t) => [
    t.organizations.getByUser(undefined, {
      refetchInterval: false,
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
    }),
    t.partners.getByUser(undefined, {
      refetchInterval: false,
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
    }),
  ])

  const [sortedOrganizations, sortedPartners] = useMemo(() => {
    return [
      (organizationsQuery.data ?? []).sort((a, b) =>
        a.organizationName!.localeCompare(b.organizationName!)
      ),
      (partnersQuery.data ?? []).sort((a, b) => a.name!.localeCompare(b.name!)),
    ]
  }, [organizationsQuery.data, partnersQuery.data])

  // Sync state
  useEffect(() => {
    if (
      session &&
      selectedOrganizationQuery.data?.id &&
      selectedOrganizationQuery.data?.name
    ) {
      setIsPending(false)
      setOrganization(
        session,
        {
          id: selectedOrganizationQuery.data.id,
          name: selectedOrganizationQuery.data.name,
          expansionConfiguration:
            selectedOrganizationQuery.data.expansionConfiguration,
          parameters: selectedOrganizationQuery.data.parameters,
        },
        selectedOrganizationQuery.data.selectionType!,
        selectedOrganizationQuery.data.globalParameters,
        selectedOrganizationQuery.data.orgRoles ?? [],
        selectedOrganizationQuery.data.primaryMeasureTypes[0] ??
          PrimaryMeasureTypeConstants.None
      )
    }
  }, [selectedOrganizationQuery.data])

  const [menuOpen, setMenuOpen] = useState(false)

  const trpcUtils = api.useUtils()

  return (
    <Menubar
      className="text-[#EFF1F9] border-0 bg-transparent p-0 rounded-[5px] relative top-1 -left-1"
      value={menuOpen ? 'main' : ''}
      onValueChange={(value: string) => {
        if (!isPending) {
          setMenuOpen(value === 'main')
        }
      }}
    >
      <MenubarMenu value="main">
        <MenubarTrigger asChild>
          <button
            className={cn(
              'mt-12 h-14',
              'bg-transparent',
              'transition-colors duration-200',
              menuOpen ? 'bg-blue-5' : 'hover:text-lightskyblue',
              'focus:bg-transparent focus:text-[#EFF1F9]',
              '-m-2'
            )}
          >
            <div className="flex flex-row space-x-2 items-center justify-between w-full">
              <span
                className={cn(
                  'text-[19px] hidden text-left font-normal font-open-sans',
                  menuOpen
                    ? 'text-lightskyblue'
                    : 'text-[#EFF1F9] group-hover:text-lightskyblue',
                  'max-w-[248px] line-clamp-2 leading-tight'
                )}
                title={organizationName ?? ''}
              >
                {organizationName}
              </span>
              {!isPending ? (
                <ChevronDown
                  className={cn(
                    'h-4 w-4 min-w-4 min-h-4 flex-shrink-0',
                    'transition-transform duration-200',
                    menuOpen
                      ? 'rotate-180 text-lightskyblue'
                      : 'rotate-0 text-[#EFF1F9] group-hover:text-lightskyblue'
                  )}
                />
              ) : (
                <></>
              )}
            </div>
          </button>
        </MenubarTrigger>
        <MenubarContent className="bg-[#163e5d] ml-[4px] -mt-[8px] border-0 rounded-none shadow-lg space-y-4 pl-[15px] pr-[15px] z-[101]">
          <SearchAndFilter
            selectedOrganization={selectedOrganizationQuery.data!}
            organizations={sortedOrganizations}
            partners={sortedPartners}
            onClose={() => setMenuOpen(false)}
            onBeforeOrgSwitch={async (organizationId, organizationName) => {
              setInFlux(true)
              setIsPending(true)
              setCurrentView({
                id: '0',
                viewName: DefaultNames.SavedView,
                isDefault: true,
                isFavorite: false,
                isShared: false,
                viewType: 'Quarterly',
                page: '',
                from: dayjs(),
                to: dayjs(),
                settings: {
                  version: 'nextGen',
                  measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
                },
              })
              router.push('/measures')
              setTimeout(() => {
                switchToOrganization(organizationId, organizationName)
              }, 200)
            }}
            onAfterOrgSwitch={async () => {
              setInFlux(false)
              await trpcUtils.invalidate()
            }}
          />
        </MenubarContent>
      </MenubarMenu>
    </Menubar>
  )
}

export default OrgSelector
