'use client'

import { cn } from '@/lib/utils'
import type { Organization } from '@/types/organization'
import type { Partner } from '@/types/partner'
import { useAuthStore } from '@/stores/auth'
import { Search } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { setCookie } from '@/actions/setCookie'
import { SelectionType } from '@/enums/selectionType'

type Props = {
  selectedOrganization: { id?: string | null }
  organizations: Organization[]
  partners: Partner[]
  onClose: () => void
  onBeforeOrgSwitch: (organizationId: string, organizationName: string, selectionType: SelectionType) => void
  onAfterOrgSwitch: () => void
}

const SearchAndFilter = ({
  selectedOrganization,
  organizations,
  partners,
  onClose,
  onBeforeOrgSwitch,
  onAfterOrgSwitch
}: Props) => {
  const router = useRouter()
  const { isAuthorized } = useAuthStore()
  const { data: session } = useSession()
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredOrganizations, setFilteredOrganizations] = useState<
    Organization[]
  >([])
  const [filteredPartners, setFilteredPartners] = useState<Partner[]>([])

  useEffect(() => {
    if (!session || !isAuthorized) {
      router.push('/session/expired')
    }
  }, [session, isAuthorized, router])

  useEffect(() => {
    const filteredOrgs = organizations?.filter((org) =>
      org.organizationName?.toLowerCase().includes(searchQuery.toLowerCase())
    )
    const filteredPrtnrs = partners?.filter((partner) =>
      partner.name?.toLowerCase().includes(searchQuery.toLowerCase())
    )

    setFilteredOrganizations(filteredOrgs ?? [])
    setFilteredPartners(filteredPrtnrs ?? [])
  }, [searchQuery, organizations, partners])

  const handleOrganizationChange = async (
    organizationId: string,
    organizationName: string,
    selectionType: SelectionType
  ) => {
    onClose()
    if (organizationId !== selectedOrganization.id) {
      onBeforeOrgSwitch(organizationId, organizationName, selectionType)

      setTimeout(async () => {
        try {
          // This causes server-side requests as that is the reliable way
          // to set cookies
          await setCookie('x-organization-id', organizationId)
          await setCookie('x-organization-selection-type', selectionType)
        } finally {
          onAfterOrgSwitch()
        }
      }, 200)
    }
  }

  const renderPartners = () => {
    return filteredPartners.map((partner) => (
      <form
        key={partner.id}
        action={async () => {
          if (!partner.id) return
          await handleOrganizationChange(partner.id, partner.name!, SelectionType.Partner)
        }}
      >
        <button
          type="submit"
          className={cn(
            'mb-[10px] hover:text-lightskyblue w-full text-left cursor-pointer text-white text-[13px] bg-transparent font-open-sans font-normal leading-[17px]'
          )}
        >
          <span className="leading-[20px]">
            {partner.name}
            {selectedOrganization?.id === partner.id && ' (active)'}
          </span>
        </button>
      </form>
    ))
  }

  const renderOrganizations = () => {
    return filteredOrganizations.map((org) => (
      <form
        key={org.organizationId}
        action={async () => {
          if (!org.organizationId) return
          await handleOrganizationChange(org.organizationId, org.organizationName!, SelectionType.Organization)
        }}
      >
        <button
          type="submit"
          className="mb-[10px] hover:text-lightskyblue w-full text-left cursor-pointer text-white text-[13px] bg-transparent font-open-sans font-normal leading-[17px]"
        >
          <span className="leading-[20px]">
            {org.organizationName}
            {selectedOrganization.id === org.organizationId && ' (active)'}
          </span>
        </button>
      </form>
    ))
  }

  return (
    <>
      <div className="relative">
        <input
          type="text"
          placeholder="Search"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full px-2 py-1 text-[12px] focus:outline-none text-white placeholder-white bg-[#163e5d] border-0 border-b border-white"
        />
        <button className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-transparent">
          <Search className="h-[9.75px] w-[9.75px] text-white" />
        </button>
      </div>

      <div className="max-h-[500px] overflow-auto text-white mac-scrollbar">
        {filteredOrganizations.length === 0 && filteredPartners.length === 0 ? (
          <p className="text-center text-[13px] font-open-sans font-normal text-white py-4 w-[278px]">
            No organizations found.
          </p>
        ) : (
          <>
            <p className="mb-[15px] tracking-[0.5px] font-semibold text-[13px] text-left font-open-sans">
              ORGANIZATIONS
            </p>
            {renderOrganizations()}
            <p className="mb-[15px] mt-[19px] tracking-[0.5px] font-semibold text-[13px] text-left">
              PARTNERS
            </p>
            {renderPartners()}
          </>
        )}
      </div>
    </>
  )
}

export default SearchAndFilter
