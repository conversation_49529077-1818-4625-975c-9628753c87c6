'use client'

import Logo from './Logo'
import OrgSelector from '@/components/navigation/orgSelector'
import AltNav from '@/components/navigation/AltNav'
import Views from './views/views'
import PlatformNavMenu from './platformNavMenu'
import { MeasureTitle } from './views/MeasureTitle'
import ReportTitleBar from './views/ReportTitleBar'
import DashboardSelector from '@/components/dashboard/DashboardSelector'
import { usePathname } from 'next/navigation'
import { useState, useEffect } from 'react'
import { MVPDetailsTitleBar } from '../reports/mvp/MVPDetailsTitleBar'

const PlatformNavigation = () => {
  const pathname = usePathname()
  const [showViews, setShowViews] = useState(false)
  const [showMeasureTitle, setShowMeasureTitle] = useState(false)
  const [showDashboardSelector, setShowDashboardSelector] = useState(false)
  const [showReportTitleBar, setShowReportTitleBar] = useState(false)
  const [showMVPDetailsTitleBar, setShowMVPDetailsTitleBar] = useState(false)

  useEffect(() => {
    setShowViews(pathname === '/measures' || pathname === '/scorecards')
    setShowMeasureTitle(pathname === '/measures/measure-details')
    setShowDashboardSelector(pathname === '/dashboard')
    setShowReportTitleBar(pathname === '/reports/aco-performance-analysis')
    setShowMVPDetailsTitleBar(
      pathname === '/reports/mvp/mvp-details' || pathname === '/reports/mvp'
    )
  }, [pathname])

  return (
    <div className="w-full bg-[#1b4a70] text-center text-[#EFF1F9] font-sans flex-none sticky top-0 z-[100]">
      <div className="grid grid-cols-2 lg:grid-cols-[280px_auto_250px] gap-4 px-4 xl:px-16">
        {/* Logo and Text */}
        <div className="flex flex-col space-y-4 py-8 items-start">
          <Logo />
          <OrgSelector />
        </div>

        <div className="col-span-2 lg:col-span-1 order-last lg:order-none mx-auto">
          <div className="flex flex-row space-x-6 pb-4 pt-0 lg:pt-20 max-w-xl mx-auto">
            <PlatformNavMenu />
          </div>
        </div>

        <div className="flex justify-end">
          <div className="bg-[#163e5d] rounded-t-none rounded-b-8xs [background:linear-gradient(rgba(0,_0,_0,_0.5),_rgba(0,_0,_0,_0.5)),_#2d7cba] w-[250px] h-11 ">
            <AltNav />
          </div>
        </div>
      </div>
      {/* Saved Views Drop */}
      {showViews && <Views />}
      {/* Measure Title if selected */}
      {showMeasureTitle && <MeasureTitle />}
      {/* Report Title Bar */}
      {showReportTitleBar && (
        <div className="bg-[#1b4a70]">
          <ReportTitleBar title="ACO PERFORMANCE ANALYSIS REPORT" />
        </div>
      )}
      {showDashboardSelector && <DashboardSelector />}

      {showMVPDetailsTitleBar && (
        <div className="bg-[#1b4a70]">
          <MVPDetailsTitleBar />
        </div>
      )}
    </div>
  )
}

export default PlatformNavigation
