'use client'

import React from 'react'
import { usePathname, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

type NavItemProps = {
  children: React.ReactNode
  href: string
  isVisible?: boolean
  onClick?: () => void
}

const NavItem = ({
  children,
  href,
  isVisible = true,
  onClick,
}: NavItemProps) => {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const queryParams = Object.fromEntries(searchParams.entries())

  const isActive = pathname.includes(href)

  return (
    <Button
      asChild
      variant="link"
      className={cn(
        'text-[#EFF1F9] text-[15px] px-0 uppercase hover:text-lightskyblue no-underline hover:no-underline font-open-sans font-semibold',
        isActive &&
          'text-lightskyblue underline decoration-2 underline-offset-[12px]',
        !isVisible && 'hidden'
      )}
    >
      <Link
        href={{ pathname: href, query: queryParams }}
        prefetch={true}
        onClick={onClick}
      >
        {children}
      </Link>
    </Button>
  )
}

export default NavItem
