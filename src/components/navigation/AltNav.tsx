'use client'

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ubar<PERSON>enu,
  Menubar<PERSON>rigger,
} from '@/components/ui/menubar'
import { Bell, ChevronDown, Grip, User } from 'lucide-react'

import AccountNav from './altNav/accountNav'
import NineDot from './altNav/nineDot'
import Notifications from './altNav/notifications'
import { useRef, useState } from 'react'
import { useClickOutside } from '@/hooks/useClickOutside'
import { api } from '@/trpc/react'
import { UserNotification } from '@/types/userNotification'

const AltNav = () => {
  const [isOpen, setIsOpen] = useState<{ [key: number]: boolean }>({
    0: false,
    1: false,
    2: false,
  })
  const ref = useRef<HTMLDivElement>(null)
  // Add a ref for each MenubarContent to track clicks inside them
  const contentRefs = useRef<(HTMLElement | null)[]>([null, null, null]);

  // Modify useClickOutside to check if click is inside any content
  useClickOutside(ref, (event) => {
    // Check if the click is inside any MenubarContent
    const isClickInsideContent = contentRefs.current.some(
      contentRef => contentRef && contentRef.contains(event.target as Node)
    );
    
    // Only close if click is outside both the trigger and all contents
    if (!isClickInsideContent) {
      setIsOpen({ 0: false, 1: false, 2: false });
    }
  });
  
  // Check if any menu is currently open
  const isAnyMenuOpen = Object.values(isOpen).some(value => value === true)

  // Add this to track which menu is being hovered
  const [hoveredMenu, setHoveredMenu] = useState<number | null>(null)

  const handleOpen = (idx: number) => {
    setIsOpen(prevState => {
      // If the clicked menu is already open, close all menus
      if (prevState[idx]) {
        return { 0: false, 1: false, 2: false };
      }
      // Otherwise, close all menus and open the clicked one
      return { 0: false, 1: false, 2: false, [idx]: true };
    });
  }

  const [orgRolesQuery, notificationsQuery, applicationsQuery] = api.useQueries(
    (t) => [
      t.users.getOrgRolesByUser(),
      t.notifications.getAll({}, {
        refetchInterval: 30000 // Refetch every 30 seconds
      }),
      t.applications.getAll(),
    ]
  )

  const miniBarItems = [
    {
      icon: (
        <Grip className="h-5 w-5 text-[#EFF1F9] group-hover:text-[#60b9e4] group-data-[state=open]:text-[#60b9e4]" />
      ),
      component: <NineDot applications={applicationsQuery.data ?? []} />,
    },
    {
      icon: (
        <div className="relative">
          <Bell
            className="h-5 w-5 text-[#EFF1F9] group-hover:text-[#60b9e4] group-data-[state=open]:text-[#60b9e4]"
            strokeWidth={1.5}
          />
          {notificationsQuery.data?.some(
            (notification: UserNotification) => !notification.readDateTime
          ) && (
            <div className="absolute -top-1 -right-[1] h-2 w-2 rounded-full bg-[#EFF1F9] group-hover:bg-[#60b9e4] group-data-[state=open]:bg-[#60b9e4]" />
          )}
        </div>
      ),
      component: (
        <Notifications notifications={notificationsQuery.data ?? []} />
      ),
    },
    {
      icon: (
        <User className="h-5 w-5 text-[#EFF1F9] group-hover:text-[#60b9e4] group-data-[state=open]:text-[#60b9e4]" />
      ),
      component: <AccountNav orgRoles={orgRolesQuery.data ?? []} />,
    },
  ]

  return (
    <div ref={ref}>
      <Menubar className="text-[#EFF1F9] border-0 bg-transparent flex">
        {miniBarItems.map((item, idx) => (
          <MenubarMenu key={idx}>
            <MenubarTrigger
              className="group bg-transparent text-[#EFF1F9] data-[state=open]:text-[#60b9e4] hover:text-[#60b9e4] focus:bg-transparent focus:text-[#EFF1F9] w-full justify-between"
              onClick={() => handleOpen(idx)}
              onMouseEnter={(e) => e.preventDefault()}
              onMouseOver={(e) => e.preventDefault()}
              onPointerEnter={(e) => e.preventDefault()}
              data-no-auto-focus="true"
            >
              {item.icon}
              <ChevronDown
                className={`h-5 w-5 transition-transform group-hover:text-[#60b9e4] group-data-[state=open]:text-[#60b9e4] ${isOpen[idx] ? 'rotate-180' : ''}`}
              />
            </MenubarTrigger>
            {(
              <MenubarContent
                align="end"
                className="bg-[#163e5d] rounded-none -mt-[4px] border-[#163e5d] z-[101]"
              >
                {item.component}
              </MenubarContent>
            )}
          </MenubarMenu>
        ))}
      </Menubar>
    </div>
  )
}

export default AltNav
