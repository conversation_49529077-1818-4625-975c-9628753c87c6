'use client'

import { api } from '@/trpc/react'
import { Loader2, Search } from 'lucide-react'
import React, { useMemo, useState, useEffect, useRef } from 'react'
import { useUserSessionStore } from '@/stores/userSession'
import { usePathname } from 'next/navigation'
import { SavedViewModel } from '@/types/savedViewModel'
import { useDateStore } from '@/stores/dates'
import { Periods } from '@/types/periods'
import { DatePickerMode } from '@/types/datePickerMode'
import { useFilterStore } from '@/stores/filter'
import { useViewStore } from '@/stores/viewStore'
import { TableState } from '@tanstack/react-table'
import { useClickOutside } from '@/hooks/useClickOutside'
import { toast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import appInsights from '@/lib/applicationInsights'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'

dayjs.extend(utc)

const Views = () => {
  const ref = useRef<HTMLDivElement>(null)
  const [isExpanded, setIsExpanded] = useState(false)
  const [pendingStarId, setPendingStarId] = useState<string | null>(null)

  useClickOutside(ref, () => setIsExpanded(false))
  const trpcUtils = api.useUtils()
  const { primaryMeasureType } = useUserSessionStore()
  const pathname = usePathname()
  const currentPage = pathname.substring(1)
  const { setSelectedPeriod, setMode, setSelectedRange } = useDateStore()
  const { setHideEmptyIndicators } = useFilterStore()
  const {
    setTableState,
    setVisibleColumns,
    currentView,
    setCurrentView,
    measureTypeSwitchProcessing,
    setViewType,
  } = useViewStore()

  const savedViews = api.savedViews.getSavedViews.useQuery({
    currentPage: currentPage,
  })

  const updateFavoriteView = api.savedViews.updateFavoriteView.useMutation({
    // Prevent automatic refetching of queries
    onSettled: () => {
      // Explicitly avoid invalidating queries
      return
    },
  })

  const [searchTerm, setSearchTerm] = useState('')
  const [views, setViews] = useState<SavedViewModel[] | undefined>([])

  const [hasReloaded, setHasReloaded] = useState(false)

  const previousPageRef = useRef(currentPage)

  useEffect(() => {
    const reloadKey = 'isReload'

    // Detect full reload or back/forward navigation
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        // Back/Forward navigation
        sessionStorage.setItem(reloadKey, 'true')
      }
    }

    // Mark reload explicitly
    window.addEventListener('beforeunload', () => {
      sessionStorage.setItem(reloadKey, 'true')
    })

    window.addEventListener('pageshow', handlePageShow)

    // Check for reload flag
    if (sessionStorage.getItem(reloadKey)) {
      setHasReloaded(true)
      sessionStorage.removeItem(reloadKey) // Clean up
    }

    return () => {
      window.removeEventListener('pageshow', handlePageShow)
    }
  }, [])

  useEffect(() => {
    if (savedViews.data) {
      setViews(
        savedViews.data.filter(
          (view: SavedViewModel) =>
            view.page === currentPage &&
            view.settings?.measureType === primaryMeasureType
        )
      )

      const defaultView = savedViews.data.find(
        (view: SavedViewModel) =>
          view.isDefault &&
          view.page === currentPage &&
          view.settings?.measureType === primaryMeasureType
      )

      if (defaultView && !currentView) {
        const { mode, selectedRange } = useDateStore.getState()

        setHideEmptyIndicators(
          defaultView.settings?.hideEmptyIndicators ?? false
        )
        setVisibleColumns(defaultView.settings?.visibleColumns ?? [])
        setTableState({
          columnFilters:
            defaultView.id === '0' ? [] : defaultView.settings?.columnFilters,
          columnOrder:
            defaultView.id === '0' ? [] : defaultView.settings?.columnOrder,
          pagination: {
            pageSize:
              defaultView.id === '0' ? 10 : defaultView.settings?.pageSize,
          },
        } as TableState)

        setSelectedPeriod(
          !hasReloaded ? (mode as Periods) : (defaultView.viewType as Periods)
        )

        setSelectedRange(
          !hasReloaded ? selectedRange : [defaultView.from, defaultView.to]
        )

        setCurrentView(defaultView)
      }
    }
  }, [savedViews.data, measureTypeSwitchProcessing])

  useEffect(() => {
    if (savedViews.data && !currentView) {
      // Find and select the default view on initial load
      const defaultView = savedViews.data.find(
        (view: SavedViewModel) => view.isDefault
      )
      if (defaultView) {
        handleViewClick(defaultView)
      } else {
        // Fallback to Medisolv Default View
        const medisolvDefaultView = savedViews.data.find(
          (view: SavedViewModel) => view.id === '0'
        )
        if (medisolvDefaultView) {
          handleViewClick(medisolvDefaultView)
        }
      }
    }
  }, [savedViews.data])

  // Track page changes
  useEffect(() => {
    // Check if the page has changed
    if (previousPageRef.current !== currentPage) {
      console.log(
        'Page changed from',
        previousPageRef.current,
        'to',
        currentPage
      )

      // Reset current view
      setCurrentView(undefined)

      // Invalidate and refetch saved views for the new page
      const fetchViews = async () => {
        await trpcUtils.savedViews.getSavedViews.invalidate({
          currentPage: currentPage,
        })

        // After invalidation, explicitly fetch the views for the new page
        const newViews = await trpcUtils.savedViews.getSavedViews.fetch({
          currentPage: currentPage,
        })

        if (newViews) {
          // Filter views for current page and measure type
          const relevantViews = newViews.filter(
            (view: SavedViewModel) =>
              view.page === currentPage &&
              view.settings?.measureType === primaryMeasureType
          )

          // Find default view
          const defaultView = relevantViews.find((view) => view.isDefault)

          if (defaultView) {
            handleViewClick(defaultView)
          } else {
            // Fallback to Medisolv Default View
            const medisolvDefaultView = relevantViews.find(
              (view) => view.id === '0'
            )
            if (medisolvDefaultView) {
              handleViewClick(medisolvDefaultView)
            }
          }
        }
      }

      fetchViews()

      // Update the previous page reference
      previousPageRef.current = currentPage
    }
  }, [currentPage, trpcUtils.savedViews.getSavedViews])

  const filteredViews = useMemo(() => {
    return !searchTerm
      ? views
      : views?.filter((view) =>
          view.viewName.toLowerCase().includes(searchTerm.toLowerCase())
        )
  }, [searchTerm, views])

  const sortViews = (viewsList: SavedViewModel[]) => {
    return [...viewsList].sort((a, b) => {
      // First sort by favorite status (favorites first)
      if (a.isFavorite && !b.isFavorite) return -1
      if (!a.isFavorite && b.isFavorite) return 1

      // If both are favorites or both are not favorites, sort by name
      if (a.isFavorite === b.isFavorite) {
        // Handle Medisolv Default View - should always be first if not comparing favorites
        if (a.id === '0') return -1
        if (b.id === '0') return 1

        // Sort alphabetically by viewName
        return a.viewName.localeCompare(b.viewName)
      }

      return 0
    })
  }

  const toggleStar = (id: string) => {
    if (!views?.length) return

    const viewToUpdate = views.find((view) => view.id === id)
    if (!viewToUpdate) return

    const newIsFavorite = !viewToUpdate.isFavorite

    // Update the views list with new favorite status
    const updatedViews = views.map((view) => ({
      ...view,
      isFavorite: view.id === id ? newIsFavorite : view.isFavorite,
    }))

    // Sort the updated views
    const sortedViews = sortViews(updatedViews)
    setViews(sortedViews)

    // If the starred view is the current view, update currentView state
    if (currentView && currentView.id === id) {
      setCurrentView({
        ...currentView,
        isFavorite: newIsFavorite,
      })
    }

    setPendingStarId(id)

    // Make API call without triggering refetches
    updateFavoriteView.mutate(
      {
        viewId: id,
        isFavorite: newIsFavorite,
        page: viewToUpdate.page!,
        primaryMeasureType,
      },
      {
        onSuccess: (response) => {
          setPendingStarId(null)
          // Re-sort views after successful update
          const finalSortedViews = sortViews(sortedViews)
          setViews(finalSortedViews)

          toast({
            className: cn('bg-green-600 text-white border-green-600'),
            title: 'Success',
            description: response.statusMesasge,
          })
        },
        onError: (error) => {
          appInsights.trackException({
            exception: new Error('Error updating favorite view', {
              cause: error,
            }),
            properties: {
              viewId: id,
              isFavorite: newIsFavorite,
            },
          })

          // Revert changes on error
          setPendingStarId(null)
          const revertedViews = views.map((view) => ({
            ...view,
            isFavorite: view.id === id ? !newIsFavorite : view.isFavorite,
          }))
          // Sort the reverted views
          const sortedRevertedViews = sortViews(revertedViews)
          setViews(sortedRevertedViews)

          if (currentView && currentView.id === id) {
            setCurrentView({
              ...currentView,
              isFavorite: !newIsFavorite,
            })
          }

          toast({
            className: cn('bg-red-600 text-white border-red-600'),
            title: 'Error',
            description: 'Failed to update favorite status',
          })
        },
      }
    )
  }

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const handleViewClick = (view: SavedViewModel) => {
    const viewId = view.id

    // First reset any existing state that might interfere
    // This ensures we don't have stale state from previous views

    // Convert dates and ensure they're valid Dayjs objects
    let fromDate = null
    let toDate = null

    if (view.from && view.to) {
      fromDate = dayjs(view.from)
      toDate = dayjs(view.to)

      // Apply appropriate date formatting based on the view type
      switch (view.viewType) {
        case 'Yearly':
          fromDate = fromDate.utc().month(0).startOf('month')
          toDate = toDate.utc().month(11).endOf('month')
          break
        case 'Quarterly':
          // Determine quarter start/end based on the month
          const startMonth = Math.floor(fromDate.utc().month() / 3) * 3
          const endMonth = Math.floor(toDate.utc().month() / 3) * 3 + 2
          fromDate = fromDate.utc().month(startMonth).startOf('month')
          toDate = toDate.utc().month(endMonth).endOf('month')
          break
        case 'Monthly':
          fromDate = fromDate.utc().startOf('month')
          toDate = toDate.utc().endOf('month')
          break
      }
    } else {
      // If no dates in the view, set default dates based on view type
      const now = dayjs()
      switch (view.viewType) {
        case 'Yearly':
          fromDate = now.utc().month(0).startOf('month')
          toDate = now.utc().month(11).endOf('month')
          break
        case 'Quarterly':
          const currentMonth = now.utc().month()
          const quarterStartMonth = Math.floor(currentMonth / 3) * 3
          fromDate = now.utc().month(quarterStartMonth).startOf('month')
          toDate = now
            .utc()
            .month(quarterStartMonth + 2)
            .endOf('month')
          break
        case 'Monthly':
          fromDate = now.utc().startOf('month')
          toDate = now.utc().endOf('month')
          break
        default:
          // Default to quarterly if no view type
          const defaultMonth = Math.floor(now.utc().month() / 3) * 3
          fromDate = now.utc().month(defaultMonth).startOf('month')
          toDate = now
            .utc()
            .month(defaultMonth + 2)
            .endOf('month')
      }
    }

    // Set the date range first, before changing other settings
    setSelectedRange([fromDate, toDate])

    // Then set the period and mode
    setSelectedPeriod(
      viewId === '0' ? ('Quarterly' as Periods) : (view.viewType as Periods)
    )

    setMode(
      viewId === '0'
        ? ('Quarterly' as DatePickerMode)
        : (view.viewType as DatePickerMode)
    )

    setViewType(
      viewId === '0'
        ? ('Quarterly' as DatePickerMode)
        : (view.viewType as DatePickerMode)
    )

    // Set other view settings
    setHideEmptyIndicators(view.settings?.hideEmptyIndicators ?? false)
    setVisibleColumns(viewId === '0' ? [] : view.settings?.visibleColumns!)

    // Set table state
    setTableState({
      columnFilters: viewId === '0' ? [] : (view.settings?.columnFilters ?? []),
      columnOrder: viewId === '0' ? [] : (view.settings?.columnOrder ?? []),
      pagination: {
        pageIndex: 0,
        pageSize: viewId === '0' ? 10 : (view.settings?.pageSize ?? 10),
      },
      sorting: [],
      columnVisibility: {},
      columnPinning: { left: [], right: [] },
      rowPinning: { top: [], bottom: [] },
      expanded: {},
      grouping: [],
      columnSizing: {},
      columnSizingInfo: {
        startOffset: 0,
        startSize: 0,
        deltaOffset: 0,
        deltaPercentage: 0,
        isResizingColumn: false,
        columnSizingStart: [],
      },
      globalFilter: '',
      rowSelection: {},
    })

    // Update current view
    setCurrentView(view)

    // Close the expanded view menu
    setIsExpanded(false)
  }

  return (
    <div className="flex flex-row border-t-[0.5px] border-[#fdfdfd] text-white mt-4 ">
      <div className="flex justify-center align-middle items-center content-center min-w-full">
        <div className="mx-auto  relative" ref={ref}>
          <div
            className={`${
              isExpanded ? 'bg-[#1e3a5f]' : 'bg-[#1B4A70]'
            } text-white rounded-t-lg overflow-hidden cursor-pointer`}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <div className="flex items-center p-3">
              {currentView?.isFavorite && (
                <svg
                  className={`w-4 h-4 cursor-pointer text-yellow-400 fill-current mr-2`}
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  fill={'currentColor'}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                  />
                </svg>
              )}
              <span className="font-semibold mr-3">
                {currentView?.viewName}
              </span>
              <svg
                className={`w-5 h-5 transition-transform ${
                  isExpanded ? 'rotate-180' : ''
                }`}
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </div>
          </div>
          {isExpanded && (
            <div className="absolute top-full left-0 right-0 bg-[#1e3a5f] text-white rounded-b-lg overflow-hidden shadow-lg z-40 w-80">
              <div className="p-3 overflow-y-auto max-h-96">
                <div className="relative mb-3">
                  <input
                    type="text"
                    placeholder="Search"
                    className="w-full bg-transparent text-white text-xs placeholder-white border-b border-white pb-1 pl-2 focus:outline-none"
                    value={searchTerm}
                    onChange={handleSearchChange}
                  />{' '}
                  <Search className="absolute right-0 top-1/2 transform -translate-y-1/2 text-white w-4 h-4" />
                </div>
                <ul className="space-y-2">
                  {filteredViews?.map((view) => (
                    <li
                      key={view.id}
                      className={cn(
                        'flex items-start space-x-2 p-2 rounded',
                        updateFavoriteView.isPending &&
                          pendingStarId === view.id
                          ? 'opacity-70 cursor-wait'
                          : 'cursor-pointer hover:bg-blue-800/50'
                      )}
                      onClick={() =>
                        !updateFavoriteView.isPending && handleViewClick(view)
                      }
                      tabIndex={0}
                      role="button"
                    >
                      {updateFavoriteView.isPending &&
                      pendingStarId === view.id ? (
                        <Loader2 className="w-4 h-4 animate-spin flex-shrink-0 mt-0.5" />
                      ) : (
                        <svg
                          className={`w-4 h-4 cursor-pointer flex-shrink-0 mt-0.5 ${
                            view.isFavorite
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-400'
                          }`}
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          fill={view.isFavorite ? 'currentColor' : 'none'}
                          onClick={(e) => {
                            e.stopPropagation()
                            toggleStar(view.id)
                          }}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                          />
                        </svg>
                      )}

                      <div className="flex items-center flex-wrap">
                        <span className="break-words text-left mr-1">
                          {view.viewName}
                        </span>
                        {view.isDefault && (
                          <span className="text-xs bg-[#97A4BA] text-white px-2 py-0.5 rounded flex-shrink-0">
                            DEFAULT
                          </span>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Views
