'use client'

import React from 'react'

interface ReportTitleBarProps {
  title: string
}

export const ReportTitleBar: React.FC<ReportTitleBarProps> = ({ title }) => {
  return (
    <div className="flex flex-row border-t-[0.5px] border-[#fdfdfd] text-white mt-4">
      <div className="flex justify-center align-middle items-center content-center min-w-full">
        <div className="mx-auto relative">
          <div className="text-white rounded-t-lg overflow-hidden">
            <div className="flex flex-col items-center p-4">
              <span className="text-[18px] font-bold">{title}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReportTitleBar
