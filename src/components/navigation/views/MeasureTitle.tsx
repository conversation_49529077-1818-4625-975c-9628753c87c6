'use client'

import { useUIStore } from '@/stores/ui'
import React from 'react'

export const MeasureTitle = () => {
  const { currentMeasureName, currentHospitalName } = useUIStore()

  return (
    <div className="flex flex-row border-t-[0.5px] border-[#fdfdfd] text-white mt-4 ">
      <div className="flex justify-center align-middle items-center content-center min-w-full">
        <div className="mx-auto  relative">
          <div
            className={` text-white rounded-t-lg overflow-hidden cursor-pointer`}
          >
            <div className="flex flex-col items-center p-3">
              <span className="font-[700] text-[18px] text-base uppercase">
                {currentHospitalName}
              </span>
              <span className="font-normal text-[23px]">
                {currentMeasureName}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
