'use client'

import { <PERSON>Check } from 'lucide-react'
import Link from 'next/link'
import { App } from '@/types/app'

type Props = {
  applications: App[]
}

const NineDot = ({ applications }: Props) => {
  return (
    <div className="p-2 w-64 space-y-2">
      {applications.length > 0 ? (
        applications?.map((application) => (
          <div
            key={application.appId}
            className="border rounded-sm border-white text-[#EFF1F9] font-normal text-xs font-open-sans"
          >
            <Link
              href={application.baseUrl ?? '#'}
              target="__blank"
              className="flex items-center justify-between p-2 no-underline text-[#EFF1F9] hover:bg-white hover:text-[#0d2538] "
            >
              {application.name}
              <CircleCheck className="h-5 w-5 text-[#163e5d]" />
            </Link>
          </div>
        ))
      ) : (
        <div className="flex p-2 text-[#EFF1F9] justify-center items-center">
          <span>No Applications</span>
        </div>
      )}
    </div>
  )
}

export default NineDot
