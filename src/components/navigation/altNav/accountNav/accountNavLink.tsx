'use client'

import { cn } from '@/lib/utils'
import { useUserSessionStore } from '@/stores/userSession'
import { OrganizationRole } from '@/types/organizationRole'
import { Session } from 'next-auth'
import Link from 'next/link'
import { useHasPermission } from '@/hooks/useHasPermission'
import { CitcUserRoles } from '@/shared/roles'

type Props = {
  children: React.ReactNode
  href: string
  organizationRoles: OrganizationRole[]
  protect: boolean
  session: Session
  className?: string
}

export const AccountNavLink = ({
  children,
  href,
  organizationRoles,
  protect,
  session,
  className,
}: Props) => {
  const { organizationId } = useUserSessionStore()
  const { checkPermission } = useHasPermission()

  let showAdminLink = false
  let showScoreCardLink = false

  if (organizationId) {
    showAdminLink = checkPermission(CitcUserRoles.ADMINISTRATOR)
    showScoreCardLink = checkPermission(CitcUserRoles.SCORECARD_MANAGER)
  }

  const shouldDisplayLink =
    (protect && href === '/hub/administration' && showAdminLink) ||
    (protect && href === '/performance-goals' && showScoreCardLink) ||
    !protect

  return shouldDisplayLink ? (
    <Link 
      href={href} 
      className={cn('text-[#EFF1F9] no-underline block w-full py-1 px-2 hover:bg-[#1e4a70] cursor-pointer', className)}
      style={{ width: '100%', display: 'block' }}
    >
      <div className="w-full h-full">
        {children}
      </div>
    </Link>
  ) : null
}
