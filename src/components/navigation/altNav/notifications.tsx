'use client'

import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import dayjs from 'dayjs'
import Link from 'next/link'
import { UserNotification } from '@/types/userNotification'
import { api } from '@/trpc/react'
import { useState } from 'react'

type Props = {
  notifications: UserNotification[]
}

const Notifications = ({ notifications: initialNotifications }: Props) => {
  const [notifications, setNotifications] = useState(initialNotifications)
  const utils = api.useContext()

  const markAsReadMutation = api.notifications.markAsRead.useMutation({
    onSuccess: (_, variables) => {
      // Update local state
      setNotifications((prevNotifications) =>
        prevNotifications.map((notification) =>
          notification.rowKey === variables.notificationId
            ? { ...notification, readDateTime: new Date() }
            : notification
        )
      )

      // Invalidate the notifications query to trigger a refresh
      utils.notifications.getAll.invalidate()
    },
  })

  return (
    <div className="p-2 w-64 space-y-2 font-open-sans">
      {notifications.length > 0 ? (
        <div className="flex flex-col space-y-4">
          {notifications?.map((notification, idx) => (
            <div key={idx}>
              <div className={cn('text-[#EFF1F9] flex flex-col text-[12px]')}>
                <div
                  className={cn(
                    'flex flex-col',
                    !notification.readDateTime ? 'font-bold' : 'font-normal'
                  )}
                >
                  <span className="text-xs leading-none">
                    {dayjs(notification.sentDateTime).format(
                      'MMMM DD, YYYY hh:mm:ss A'
                    )}
                  </span>

                  <span className="text-[12px]">{notification.message}</span>
                </div>

                {!notification.readDateTime && (
                  <Button
                    className="text-[#EFF1F9] p-0 justify-start bg-transparent font-normal text-xs"
                    variant={'link'}
                    onClick={() => {
                      markAsReadMutation.mutate({
                        notificationId: notification.rowKey,
                      })
                    }}
                  >
                    Mark as Read
                  </Button>
                )}
              </div>

              <Separator className="bg-white" />
            </div>
          ))}

          <Link
            href="/notifications"
            className="rounded-[5px] bg-white w-full text-ui-dark-gray text-[13px] no-underline text-center font-normal py-1 hover:bg-[#163e5d] hover:text-[#EFF1F9]"
          >
            {'View All Notifications'.toUpperCase()}
          </Link>
        </div>
      ) : (
        <div className="flex p-2 text-[#EFF1F9] justify-center items-center">
          <span className="text-[12px]">No New Notifications</span>
        </div>
      )}
    </div>
  )
}

export default Notifications
