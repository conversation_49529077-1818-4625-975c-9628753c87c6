'use client'

import { SignOutButton } from '@/components/SignOutButton'
import { MenubarSeparator } from '@/components/ui/menubar'
import { env } from '@/env'
import { AccountNavLink } from './accountNav/accountNavLink'
import { api } from '@/trpc/react'
import { useSession } from 'next-auth/react'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { OrganizationRole } from '@/types/organizationRole'
import { cn } from '@/lib/utils'

const miniBarNavItems = [
  {
    name: 'My Account',
    href: '/account',
    protect: false,
  },
  {
    name: 'Manage Goals',
    href: '/performance-goals',
    protect: true,
  },
  {
    name: 'Administration',
    href: '/hub/administration',
    protect: true,
  },
]

type Props = { orgRoles: OrganizationRole[] }

const AccountNav = ({ orgRoles }: Props) => {
  const { data: session } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (!session) {
      router.push('/session/expired')
    }
  }, [session])

  return (
    session && (
      <div className="p-2 flex flex-col space-y-2 text-white font-normal text-[12px] font-open-sans">
        <div className="flex flex-col space-y-2">
          {miniBarNavItems.map((item, idx) => (
            <AccountNavLink
              href={item.href}
              key={idx}
              organizationRoles={orgRoles}
              protect={item.protect}
              session={session}
              className={cn('font-normal')} 
            >
              {item.name}
            </AccountNavLink>
          ))}
        </div>
        <MenubarSeparator className="bg-white" />
        <SignOutButton className="bg-transparent border-0 p-0 hover:bg-transparent text-white font-normal font-open-sans text-left w-full" />
        <MenubarSeparator className="bg-white" />
        <span className="text-white text-[10px] font-open-sans">
          Medisolv Version {env.NEXT_PUBLIC_APP_VERSION}
        </span>
      </div>
    )
  )
}

export default AccountNav
