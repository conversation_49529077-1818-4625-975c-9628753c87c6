'use client'

import { MenuItem } from '@/types/menuItem'
import { Suspense, useEffect } from 'react'
import NavItem from './NavItem'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Skeleton } from '../ui/skeleton'
import { api } from '@/trpc/react'
import { useFilterStore } from '@/stores/filter'
import { useDateStore } from '@/stores/dates'

const PlatformNavMenu = () => {
  const router = useRouter()
  const { data: session } = useSession()
  const { clearAll: clearAllFilters } = useFilterStore()
  const { clearAll: clearAllDates } = useDateStore()

  const handleNavigation = () => {
    // Reset both filter and date stores
    clearAllFilters()
    clearAllDates()
  }

  useEffect(() => {
    if (!session) {
      router.push('/session/expired')
    }
  }, [session])

  const [pages, query] = api.pages.getAll.useSuspenseQuery()

  useEffect(() => {
    if (query.isError) console.error(query.error)
  }, [query.isError])

  return (
    <Suspense fallback={<Skeleton className="w-full h-10 bg-[#163e5d]" />}>
      <div className="flex space-x-[100px]">
        {(pages ?? [])
          ?.map((page) => {
            const menuItem: MenuItem = {
              displayText: page.name,
              url: page.routeOrExternalUrl ?? '',
              isVisible: page.isVisible,
            }

            return menuItem
          })
          .map((item, index) => (
            <NavItem
              key={index}
              href={item.url}
              isVisible={item.isVisible}
              onClick={handleNavigation}
            >
              {item.displayText}
            </NavItem>
          ))}
      </div>
    </Suspense>
  )
}

export default PlatformNavMenu
