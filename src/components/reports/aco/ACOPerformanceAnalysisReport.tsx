'use client'

import React from 'react'
import { IntervalType } from '@/types/intervalType'
import { api } from '@/trpc/react'
import ACOPerformanceAnalysisTable from './ACOPerformanceAnalysisTable'

// Define the props for the component
interface ACOPerformanceAnalysisReportProps {
  intervalType: string
  startDate: Date
  endDate: Date
  measures: string[]
  sourceContainerIdentifier: string
  universe: string
  runtime?: number // Optional timestamp when report was run
  hideEmptyIndicators?: boolean // Optional flag to hide empty indicators
  onDataReceived?: (data: any[]) => void // Optional callback to receive the data
}

const ACOPerformanceAnalysisReport: React.FC<
  ACOPerformanceAnalysisReportProps
> = ({
  intervalType,
  startDate,
  endDate,
  measures,
  sourceContainerIdentifier,
  universe,
  runtime,
  hideEmptyIndicators,
  onDataReceived,
}) => {
  const [acoPerformanceData, setAcoPerformanceData] = React.useState<any[]>([])

  // Create a unique key for the component to force re-render when parameters change
  const queryKey = React.useMemo(() => {
    return `${intervalType}-${startDate.toISOString()}-${endDate.toISOString()}-${measures.join(',')}-${sourceContainerIdentifier}-${universe}-${runtime}-${hideEmptyIndicators ? 'hide' : 'show'}`
  }, [
    intervalType,
    startDate,
    endDate,
    measures,
    sourceContainerIdentifier,
    universe,
    runtime,
    hideEmptyIndicators,
  ])

  // Fetch data using tRPC
  const {
    data: result,
    isLoading,
    error,
  } = api.report.getACOMeasurePerformanceByUniverse.useQuery({
    intervalType: intervalType as IntervalType,
    startDate,
    endDate,
    measureIdentifiers: measures,
    sourceContainerIdentifier,
    universe,
    runtime,
  })

  // Call the onDataReceived callback when data is available
  React.useEffect(() => {
    if (result && onDataReceived) {
      if (hideEmptyIndicators)
        setAcoPerformanceData(result.filter((x) => !!x.Performance))
      else setAcoPerformanceData(result)
      onDataReceived(result)
    }
  }, [result, onDataReceived])

  React.useEffect(() => {
    if (result) {
      if (hideEmptyIndicators)
        setAcoPerformanceData(result.filter((x) => !!x.Performance))
      else setAcoPerformanceData(result)
    }
  }, [hideEmptyIndicators])

  return (
    <ACOPerformanceAnalysisTable
      isLoading={isLoading}
      error={error}
      data={acoPerformanceData || []}
    />
  )
}

export default ACOPerformanceAnalysisReport
