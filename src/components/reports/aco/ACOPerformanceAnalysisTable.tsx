'use client'

import React, { useState, useEffect } from 'react'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
  getPaginationRowModel,
} from '@tanstack/react-table'
import { getSortingIcon } from '@/components/ui/sortIcon'
import Loader from '@/components/ui/Loader'
import { Pagination } from '@/components/measures/pagination'

// Create a common interface for table data
export interface CommonTableData {
  entityOrganization: string
  organization: string
  longMeasureName: string
  performance: string
  numerator: string
  performanceDenominator: string
}

// Create a type for the summary table data
export interface SummaryTableData extends CommonTableData {
  decile?: number | null
}

// Define the props for the component
export interface ACOPerformanceAnalysisTableProps {
  isLoading: boolean
  error: any // Accept any error type to accommodate tRPC error types
  data: any[] // Raw data from API
  transformData?: (data: any[]) => SummaryTableData[] // Optional custom data transformer
}

const ACOPerformanceAnalysisTable: React.FC<
  ACOPerformanceAnalysisTableProps
> = ({ isLoading, error, data, transformData }) => {
  const [summaryData, setSummaryData] = useState<SummaryTableData[]>([])
  const [sorting, setSorting] = useState<SortingState>([])

  // Create column helper for the table
  const summaryColumnHelper = createColumnHelper<SummaryTableData>()

  // Common column definitions
  const commonColumnDefs = [
    {
      id: 'entityOrganization',
      header: 'ENTITY (ORGANIZATION)',
      accessorKey: 'entityOrganization',
    },
    {
      id: 'organization',
      header: 'ORGANIZATION',
      accessorKey: 'organization',
    },
    {
      id: 'longMeasureName',
      header: 'LONG MEASURE NAME',
      accessorKey: 'longMeasureName',
    },
    {
      id: 'performance',
      header: 'PERFORMANCE',
      accessorKey: 'performance',
    },
    {
      id: 'numerator',
      header: 'NUMERATOR',
      accessorKey: 'numerator',
    },
    {
      id: 'performanceDenominator',
      header: 'DENOMINATOR',
      accessorKey: 'performanceDenominator',
    },
  ]

  // Create columns for the table using the common definitions
  const summaryColumns = commonColumnDefs.map((col) =>
    summaryColumnHelper.accessor(col.accessorKey as keyof SummaryTableData, {
      id: col.id,
      header: col.header,
      cell: (info) => info.getValue(),
    })
  )

  // Create table
  const summaryTable = useReactTable({
    data: summaryData,
    columns: summaryColumns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  })

  // Default data transformation function
  const defaultTransformData = (
    rawData: any[]
  ): {
    entityOrganization: `Entity ID: ${string}`
    organization: string
    longMeasureName: any
    performance: string
    numerator: any
    performanceDenominator: any
    decile: number | string
  }[] => {
    if (!rawData || rawData.length === 0) return []

    // Group by EntityId AND MeasureName to properly display multiple measures
    const entityGroups = rawData.reduce<Record<string, any[]>>(
      (groups: Record<string, any[]>, item: any) => {
        if (item) {
          // Create a composite key using both EntityId and MeasureName
          const key = `${item.EntityId}-${item.MeasureName}`
          if (!groups[key]) {
            groups[key] = []
          }
          groups[key].push(item)
        }
        return groups
      },
      {}
    )

    // Create one row per entity
    return Object.values(entityGroups)
      .filter((group: any[]) => group.length > 0 && group[0])
      .map((group: any[]) => {
        // Use the first item for common properties
        const item = group[0]! // Non-null assertion after filter check

        // Use actual data from the database
        const denominator =
          item.PerformanceDenominator === undefined ||
          item.PerformanceDenominator === null
            ? 'N/A'
            : item.PerformanceDenominator >= 0
              ? item.PerformanceDenominator
              : '-'
        const numerator =
          item.Numerator === undefined || item.Numerator === null
            ? 'N/A'
            : item.Numerator >= 0
              ? item.Numerator
              : '-'
        // Format performance as a percentage
        const performance =
          item.Performance === undefined || item.Performance === null
            ? 'N/A'
            : item.Performance >= 0
              ? `${item.Performance}%`
              : '-'

        return {
          entityOrganization:
            item.EntityDescription || `Entity ID: ${item.EntityId}`,
          organization: item.Organization || 'Unknown',
          longMeasureName: item.MeasureName,
          performance: performance,
          numerator: numerator.toString(),
          performanceDenominator: denominator.toString(),
          decile: item.PTile >= 0 ? Math.ceil(item.PTile / 10) : '-',
        }
      })
  }

  // Set local state based on data
  useEffect(() => {
    if (data && data.length > 0) {
      // Use custom transform function if provided, otherwise use default
      const transformedData = transformData
        ? transformData(data)
        : defaultTransformData(data)
      // @ts-ignore
      setSummaryData(transformedData)
    } else {
      setSummaryData([])
    }
  }, [data, transformData])

  // Header style for tables
  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
  } as React.CSSProperties

  // Loading state
  if (isLoading) {
    return <Loader />
  }

  // Error state
  if (error) {
    return <div className="text-red-500">{error.message}</div>
  }

  // Check if there's no data to display
  const noData = summaryData.length === 0

  return (
    <div className="flex flex-col w-full">
      {/* Summary Table */}
      <div className="mb-8">
        {noData ? (
          <div className="flex justify-center items-center h-64 border border-[#DDDCDF] bg-white">
            <div className="text-center">
              <p className="text-gray-500 font-medium mb-2">
                No data available for the selected measures
              </p>
              <p className="text-gray-400 text-sm">
                Try selecting different measures or date range
              </p>
            </div>
          </div>
        ) : (
          <div className="flex flex-col w-full font-open-sans text-sm">
            <div className="mac-scrollbar">
              <table
                className="w-full"
                style={{ borderCollapse: 'separate', borderSpacing: '0 10px' }}
              >
                <thead>
                  {summaryTable.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          colSpan={header.colSpan}
                          style={{
                            ...headerStyle,
                            minWidth: '150px',
                            position: 'sticky',
                            top: 0,
                            zIndex: 10,
                            textAlign: 'left',
                            fontSize: '12px',
                            fontWeight: 600,
                            padding: '12px',
                          }}
                        >
                          {header.isPlaceholder ? null : (
                            <span
                              className="w-full cursor-pointer flex items-center gap-1"
                              onClick={header.column.getToggleSortingHandler()}
                            >
                              <span>
                                {flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                              </span>
                              {getSortingIcon(
                                header.column.getIsSorted() as boolean,
                                header.column.getIsSorted() === 'desc'
                              )}
                            </span>
                          )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody>
                  {summaryTable.getRowModel().rows.map((row, rowIndex) => (
                    <tr key={row.id} className={`bg-white relative text-left`}>
                      {row.getVisibleCells().map((cell) => (
                        <td
                          key={cell.id}
                          className={`p-3 align-middle font-open-sans font-normal text-[12px] bg-[#F9F8F9]`}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {summaryTable.getPageCount() > 1 && (
              <Pagination table={summaryTable} />
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default ACOPerformanceAnalysisTable
