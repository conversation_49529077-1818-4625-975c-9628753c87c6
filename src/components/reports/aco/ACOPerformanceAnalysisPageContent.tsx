'use client'

import React, { useState, useRef, useEffect } from 'react'
import { IntervalType } from '@/types/intervalType'
import dayjs from 'dayjs'
import { extractStartDateFromRangeString } from '@/lib/dateRange'
import { Checkbox } from '@/components/ui/checkbox'
import ConfigureReportButton, {
  ConfigureReportButtonRef,
} from '../common/ConfigureReportButton'
import { ACOReportConfig } from '../filters/ACOFilters'
import ACOPerformanceReportActions from '../actions/ACOPerformanceReportActions'
import ACOPerformanceAnalysisReport from './ACOPerformanceAnalysisReport'
import ACOPerformanceAnalysisForHigherOrderOrganization from './ACOPerformanceAnalysisForHigherOrderOrganization'
import { useReportConfig } from '../common/useReportConfig'

interface ACOPerformanceAnalysisPageClientProps {
  initialMeasureNames: string[]
  initialDateRange: string
  reportId?: string | null
}

const ACOPerformanceAnalysisPageContent: React.FC<
  ACOPerformanceAnalysisPageClientProps
> = ({ initialMeasureNames, initialDateRange, reportId }) => {
  const [config, setConfig] = useState<ACOReportConfig>({
    intervalType: 'Y',
    startDate: dayjs().startOf('year').format('YYYY-MM-DD'),
    selectedMeasures: [],
    organizationType: '',
    universe: '',
    filterByUniverse: '',
    filterByCode: 0,
    selectedEntityId: '',
    name: 'ACO Performance Analysis',
    description: 'ACO Performance Analysis Report',
    reportId: reportId ?? undefined,
  })

  // Get today's date in YYYY-MM-DD format
  const getTodayDate = (): string => {
    return dayjs().format('YYYY-MM-DD')
  }

  const {
    config: reportConfig,
    isLoading,
    error: reportError,
  } = useReportConfig<ACOReportConfig>('aco-performance-analysis', reportId)

  useEffect(() => {
    if (reportConfig) setConfig(reportConfig)
  }, [reportConfig])

  // Effect to update state when config changes
  useEffect(() => {
    if (config) {
      setIntervalType(config.intervalType)
      setMeasureNames(config.selectedMeasures)
      setStartDate(config.startDate)
      setOrganizationType(config.organizationType)
      setUniverse(config.universe)
      setFilterByUniverse(config.filterByUniverse)
      setFilterByCode(config.filterByCode)
      setSelectedEntityId(config.selectedEntityId)
      setRuntime(config.runtime)

      // Update date range
      if (config.startDate) {
        const start = dayjs(config.startDate)
        let end = dayjs(calculateEndDate(start.toDate(), config.intervalType))
        const formattedRange = `${start.format('MMM YY').toUpperCase()} - ${end.format('MMM YY').toUpperCase()}`
        setDateRange(formattedRange)
      }
    }
  }, [config])

  const [measureNames, setMeasureNames] = useState(initialMeasureNames)
  const [dateRange, setDateRange] = useState(initialDateRange)
  const [intervalType, setIntervalType] = useState<IntervalType>('M')
  const [startDate, setStartDate] = useState(
    extractStartDateFromRangeString(initialDateRange, getTodayDate())
  )

  // State for analysis details
  const [organizationType, setOrganizationType] = useState('')
  const [universe, setUniverse] = useState('')
  const [filterByUniverse, setFilterByUniverse] = useState('')
  const [filterByCode, setFilterByCode] = useState(0)
  const [selectedEntityId, setSelectedEntityId] = useState('')
  const [runtime, setRuntime] = useState<number | undefined>(undefined)
  const [hideEmptyIndicators, setHideEmptyIndicators] = useState(false)

  // State to store the data for the ACOPerformanceExporter
  const [acoPerformanceData, setAcoPerformanceData] = useState<any[]>([])
  const [
    acoPerformanceForHigherOrderData,
    setAcoPerformanceForHigherOrderData,
  ] = useState<any[]>([])

  // Reference to the ConfigureReportButton component
  const configureReportButtonRef = useRef<ConfigureReportButtonRef>(null)

  // State to track if the ConfigureReport dropdown is open
  const [isConfigureReportOpen, setIsConfigureReportOpen] = useState(false)

  const handleConfigChange = (config: ACOReportConfig) => {
    setConfig(config)
  }

  // Calculate end date based on start date and interval type
  const calculateEndDate = (start: Date, intervalType: IntervalType): Date => {
    const startDayjs = dayjs(start)
    let endDayjs

    switch (intervalType) {
      case 'Y':
        endDayjs = startDayjs.add(1, 'year')
        break
      case 'Q':
        endDayjs = startDayjs.add(3, 'month')
        break
      case 'M':
      default:
        endDayjs = startDayjs.add(1, 'month')
        break
    }

    return endDayjs.toDate()
  }

  // Function to extract table headers from the data
  const getTableHeaders = (data: any[]): string[] => {
    if (!data || data.length === 0) return []

    // Use the common column definitions from ACOPerformanceAnalysisTable
    return [
      'ENTITY (ORGANIZATION)',
      'ORGANIZATION',
      'LONG MEASURE NAME',
      'PERFORMANCE',
      'NUMERATOR',
      'DENOMINATOR',
    ]
  }

  // Function to extract table data from the API response
  const getTableData = (
    data: any[]
  ): Record<string, string | number | boolean | null | undefined>[] => {
    if (!data || data.length === 0) return []

    // Group by EntityId AND MeasureName to properly display multiple measures
    const entityGroups = data.reduce<Record<string, any[]>>(
      (groups: Record<string, any[]>, item: any) => {
        if (item) {
          // Create a composite key using both EntityId and MeasureName
          const key = `${item.EntityId}-${item.MeasureName}`
          if (!groups[key]) {
            groups[key] = []
          }
          groups[key].push(item)
        }
        return groups
      },
      {}
    )

    // Create one row per entity
    return Object.values(entityGroups)
      .filter((group: any[]) => group.length > 0 && group[0])
      .map((group: any[]) => {
        // Use the first item for common properties
        const item = group[0]! // Non-null assertion after filter check

        // Use actual data from the database
        const denominator =
          item.PerformanceDenominator >= 0 ? item.PerformanceDenominator : '-'
        const numerator = item.Numerator >= 0 ? item.Numerator : '-'
        // Format performance as a percentage
        const performance = item.Performance >= 0 ? `${item.Performance}%` : '-'

        return {
          'ENTITY (ORGANIZATION)':
            item.EntityDescription || `Entity ID: ${item.EntityId}`,
          ORGANIZATION: item.Organization || 'Unknown',
          'LONG MEASURE NAME': item.MeasureName,
          PERFORMANCE: performance,
          NUMERATOR: numerator.toString(),
          DENOMINATOR: (() => {
            const denomStr = denominator != null ? denominator.toString() : '0'
            return denomStr.startsWith('=') ? "'" + denomStr : denomStr
          })(),
        }
      })
  }

  return (
    <div className="container min-w-full space-y-4 font-open-sans">
      {/* Filter and Action Bar - styled like the measures page */}
      <div className="relative flex w-full justify-between h-[36px]">
        <div className="flex items-center">
          <div className="relative rounded-[5px] hover:bg-white border border-white px-4 py-2">
            <ConfigureReportButton
              ref={configureReportButtonRef}
              reportType="aco-performance-analysis"
              onConfigChange={handleConfigChange}
              initialConfig={config}
              onOpenChange={setIsConfigureReportOpen}
            />
          </div>
        </div>
        {runtime !== undefined ? (
          <div className="flex items-center">
            <div className="ml-7 rounded-xl space-x-2 mr-4 flex items-center">
              <Checkbox
                id="terms"
                onCheckedChange={(checked) =>
                  setHideEmptyIndicators(checked as boolean)
                }
                checked={hideEmptyIndicators}
              />
              <label
                htmlFor="terms"
                className="text-ui-dark-gray font-semibold font-sans uppercase text-[13px] peer-disabled:cursor-not-allowed peer-disabled:opacity-70 hover:cursor-pointer"
              >
                Hide Empty Indicators
              </label>
            </div>
            <div className="z-[11]">
              <ACOPerformanceReportActions
                analysisReportHeader={getTableHeaders(acoPerformanceData)}
                analysisReportDataset={getTableData(acoPerformanceData).map(
                  (item) => ({
                    ...item,
                    DATE: startDate, // Add the start date to each row
                  })
                )}
                analysisReportForHigherOrderHeader={getTableHeaders(
                  acoPerformanceForHigherOrderData
                )}
                analysisReportForHigherOrderDataset={getTableData(
                  acoPerformanceForHigherOrderData
                )}
                reportConfig={config}
                // onSaveComplete={handleReportSaved}
              />
            </div>
          </div>
        ) : (
          <></>
        )}
      </div>

      {/* Report Content */}
      <div className="pl-6 bg-white rounded-md">
        {reportId && reportError ? (
          // Error state when there's an issue loading the report
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <div
              className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
              role="alert"
            >
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">
                {reportError instanceof Error
                  ? reportError.message.includes(
                      'You do not have access to this report'
                    )
                    ? 'You do not have permission to view this report.'
                    : reportError.message
                  : 'An error occurred while loading the report.'}
              </span>
            </div>
            <button
              onClick={() => (window.location.href = '/reports')}
              className="mt-4 bg-[#566582] hover:bg-[#1e3c5c] text-white pt-1 pb-1 pl-5 pr-5 rounded-md uppercase align-middle"
            >
              Return to Reports
            </button>
          </div>
        ) : reportId && isLoading ? (
          // Loading state when a reportId is provided and the report is still loading
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2B5380] mb-4"></div>
            <h1 className="text-[24px] font-bold mb-2">Loading Report</h1>
            <p className="text-[16px] text-gray-600">
              Please wait while we load your report...
            </p>
          </div>
        ) : runtime === undefined ? (
          // New report creation state when no runtime is set and no report is being loaded
          <div
            className={`${isConfigureReportOpen ? 'text-[#CCCCCC]' : ''} flex flex-col items-center justify-center p-8 text-center1`}
          >
            <h1
              data-testid="placeholder-content"
              className="mb-[35px] text-[35px] font-bold"
            >
              New Report Creation
            </h1>
            <div className="text-center mb-[35px] text-[20px] font-normal">
              <p className="mb-2">
                To create your report, please use the "configure report" button
                on the top left.
              </p>
              <p className="mb-2">Select your report requirements.</p>
              <p className="mb-2">Run report.</p>
              <p className="mb-2">
                To edit input data, return to the same menu and change inputs or
                use the filter bar.
              </p>
            </div>
            <button
              onClick={() => {
                // Use the ref to open the ConfigureReportButton
                if (configureReportButtonRef.current) {
                  configureReportButtonRef.current.setOpen(true)
                }
                setIsConfigureReportOpen(true)
              }}
              className={`mt-4 bg-[#566582] hover:bg-[#1e3c5c] text-white pt-1 pb-1 pl-5 pr-5 rounded-md uppercase align-middle font-semibold text-sm ${isConfigureReportOpen ? 'opacity-70' : ''}`}
            >
              Configure Report
            </button>
          </div>
        ) : (
          <>
            {/* Report Title */}
            <div className="mb-6">
              <h1 className="text-[28px] font-bold text-[#2B5380]">
                {typeof config?.name === 'string'
                  ? config.name
                  : 'ACO Performance Analysis'}
              </h1>
              {typeof config?.description === 'string' && (
                <p className="text-[16px] text-gray-600 mt-2">
                  {config.description}
                </p>
              )}
              <div className="mt-3 text-[14px] text-gray-500">
                <span className="font-semibold">Date Range:</span> {dateRange}
              </div>
            </div>

            {/* Use a unique key that includes all parameters to force re-render when any parameter changes */}
            <ACOPerformanceAnalysisReport
              key={`report-${measureNames.join('-')}-${dateRange}-${intervalType}-${runtime}-${hideEmptyIndicators}-${selectedEntityId}-${universe}`}
              intervalType={intervalType}
              startDate={new Date(startDate)}
              endDate={calculateEndDate(new Date(startDate), intervalType)}
              measureNames={measureNames}
              sourceContainerIdentifier={selectedEntityId}
              universe={universe}
              hideEmptyIndicators={hideEmptyIndicators}
              runtime={runtime}
              onDataReceived={setAcoPerformanceData}
            />

            <div className="mt-[10px]">
              <ACOPerformanceAnalysisForHigherOrderOrganization
                key={`higher-order-${measureNames.join('-')}-${dateRange}-${intervalType}-${runtime}-${hideEmptyIndicators}-${selectedEntityId}-${filterByCode}`}
                intervalType={intervalType}
                startDate={new Date(startDate)}
                endDate={calculateEndDate(new Date(startDate), intervalType)}
                measureNames={measureNames}
                sourceContainerIdentifier={selectedEntityId}
                code={filterByCode}
                runtime={runtime}
                hideEmptyIndicators={hideEmptyIndicators}
                onDataReceived={setAcoPerformanceForHigherOrderData}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default ACOPerformanceAnalysisPageContent
