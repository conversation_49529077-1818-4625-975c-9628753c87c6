'use client'

import React, { useEffect } from 'react'
import { IntervalType } from '@/types/intervalType'
import { api } from '@/trpc/react'
import ACOPerformanceAnalysisTable from './ACOPerformanceAnalysisTable'

// Define the props for the component
interface ACOPerformanceAnalysisForHigherOrderOrganizationProps {
  intervalType: string
  startDate: Date
  endDate: Date
  measures: string[]
  sourceContainerIdentifier: string
  code: number
  runtime?: number // Optional timestamp when report was run
  hideEmptyIndicators?: boolean // Optional flag to hide empty indicators
  onDataReceived?: (data: any[]) => void // Optional callback to receive the data
}

const ACOPerformanceAnalysisForHigherOrderOrganization: React.FC<
  ACOPerformanceAnalysisForHigherOrderOrganizationProps
> = ({
  intervalType,
  startDate,
  endDate,
  measures,
  sourceContainerIdentifier,
  code,
  runtime,
  hideEmptyIndicators,
  onDataReceived,
}) => {
  const [acoPerformanceData, setAcoPerformanceData] = React.useState<any[]>([])

  // Create a unique key for the component to force re-render when parameters change
  const queryKey = React.useMemo(() => {
    return `${intervalType}-${startDate.toISOString()}-${endDate.toISOString()}-${measures.join(',')}-${sourceContainerIdentifier}-${code}-${runtime}-${hideEmptyIndicators ? 'hide' : 'show'}`
  }, [
    intervalType,
    startDate,
    endDate,
    measures,
    sourceContainerIdentifier,
    code,
    runtime,
    hideEmptyIndicators,
  ])

  // Fetch data using tRPC
  const {
    data: result,
    isLoading,
    error,
  } = api.report.getACOMeasurePerformanceByOrgCode.useQuery({
    intervalType: intervalType as IntervalType,
    startDate,
    endDate,
    measureIdentifiers: measures,
    sourceContainerIdentifier,
    code,
    runtime,
  })

  // Call the onDataReceived callback when data is available
  React.useEffect(() => {
    if (result && onDataReceived) {
      if (hideEmptyIndicators)
        setAcoPerformanceData(result.filter((x) => !!x.Performance))
      else setAcoPerformanceData(result)
      onDataReceived(result)
    }
  }, [result, onDataReceived])

  useEffect(() => {
    if (result) {
      if (hideEmptyIndicators)
        setAcoPerformanceData(result.filter((x) => !!x.Performance))
      else setAcoPerformanceData(result)
    }
  }, [hideEmptyIndicators])

  return (
    <ACOPerformanceAnalysisTable
      isLoading={isLoading}
      error={error}
      data={acoPerformanceData || []}
    />
  )
}

export default ACOPerformanceAnalysisForHigherOrderOrganization
