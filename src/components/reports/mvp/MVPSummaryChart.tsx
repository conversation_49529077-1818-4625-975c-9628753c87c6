'use client'

import { AgCharts } from 'ag-charts-react'
import {
  AgCartesianChartOptions,
  AgCartesianSeriesOptions,
} from 'ag-charts-community'
import { api } from '@/trpc/react'
import { useMVPStore } from '@/stores/mvp'
import { useEffect, useMemo } from 'react'
import Loader from '@/components/ui/Loader'
import { getColor } from '@/lib/generateColorsForChart'
import { QualityScoreByMVP } from '@/types/reports/mvpTypes'
import dayjs from 'dayjs'

interface QualityScoreChartProps {
  timeframe: string
}

export function MVPSummaryChart({ timeframe }: QualityScoreChartProps) {
  const { mvpConfig, setChartData } = useMVPStore()
  const { data: QualityScoreData, isLoading: isloadingQualityScore } =
    api.report.getQualityScoreByMVP.useQuery(
      {
        year: mvpConfig.year,
        periodType: timeframe,
      },
      {
        staleTime: 0, // Always fetch fresh data
      }
    )

  useEffect(() => {
    if (QualityScoreData) {
      setChartData(QualityScoreData)
    }
  }, [QualityScoreData])

  const QualityScoreByMVP = useMemo(() => QualityScoreData, [QualityScoreData])

  const mvps = [...new Set(QualityScoreByMVP?.map((x) => x.MVP))]

  const groupesByPeriod = QualityScoreByMVP?.reduce(
    (acc, mvp) => {
      const key = mvp.Period
      if (!acc[key]) acc[key] = []
      acc[key].push(mvp)
      return acc
    },
    {} as { [key: string]: QualityScoreByMVP[] }
  )

  const transformedData: any = []
  if (groupesByPeriod) {
    for (const [key, group] of Object.entries(groupesByPeriod!)) {
      const obj: any = {}
      const period = dayjs(key)
      obj.Period =
        timeframe === 'Quarterly'
          ? `Q${period.quarter()}-${period.year()}`
          : period.format('MMM-YYYY')

      group.map((x) => {
        obj[x.MVP.replaceAll(' ', '')] = x.Score
      })

      transformedData.push(obj)
    }
  }
  const series = mvps?.map((mvp, index) => {
    const colorSet = getColor(index)
    return {
      type: 'area',
      xKey: 'Period',
      yKey: mvp.replaceAll(' ', ''),
      yName: mvp.replace('&&', '\n'),
      stroke: colorSet?.line,
      strokeWidth: 1,
      fill: colorSet?.background,
      interpolation: { type: 'smooth' },
      connectMissingData: true,
      label: {
        enabled: true,
        fontWeight: 'lighter',
        color: 'black',
      },
      marker: {
        fill: colorSet?.line,
        fillOpacity: 0.7,
      },
    } as AgCartesianSeriesOptions
  })

  const maxScore = Math.max(...(QualityScoreByMVP?.map((x) => x.Score) ?? []))
  const minScore = Math.min(...(QualityScoreByMVP?.map((x) => x.Score) ?? []))

  const options: AgCartesianChartOptions = {
    animation: {
      enabled: true,
    },
    data: transformedData,
    series: series,
    axes: [
      {
        type: 'category',
        position: 'bottom',
      },
      {
        type: 'number',
        position: 'left',
        min: minScore,
        max: maxScore > 10 ? maxScore : 10,
        title: {
          text: '',
        },
        line: {
          width: 0.5,
        },
        crosshair: {
          enabled: false,
        },
      },
    ],
    legend: {
      position: 'bottom',
      item: {
        line: {
          strokeWidth: 0,
          length: 0,
        },
        label: {
          formatter: (params: { value: string }) => {
            console.log(params.value)
            return params.value.replace('&&', '\n')
          },
        },
        marker: {
          shape: 'square',
        },
      },
    },
  }

  if (isloadingQualityScore) {
    return (
      <div className="relative top-4">
        <Loader className="" />
      </div>
    )
  }

  return <AgCharts options={options} />
}
