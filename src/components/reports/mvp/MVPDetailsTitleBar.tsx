'use client'

import { useMVPStore } from '@/stores/mvp'
import { usePathname } from 'next/navigation'
import React, { useEffect } from 'react'

export const MVPDetailsTitleBar = () => {
  const { mvpConfig } = useMVPStore()
  const pathname = usePathname()

  const [title, setTitle] = React.useState('')
  const [subtitle, setSubtitle] = React.useState<string | undefined>('')

  const isMVPDetailsPage = pathname.includes('mvp-details')

  useEffect(() => {
    setTitle(
      `${mvpConfig.year === 0 ? '' : mvpConfig.year} MVP ${isMVPDetailsPage ? 'DETAILS' : 'SUMMARY'}`
    )
    setSubtitle(mvpConfig.mvp)
  }, [mvpConfig.year, mvpConfig.mvp])

  return (
    <div className="flex flex-row border-t-[0.5px] border-[#fdfdfd] text-white mt-4 ">
      <div className="flex justify-center align-middle items-center content-center min-w-full">
        <div className="mx-auto  relative">
          <div
            className={` text-white rounded-t-lg overflow-hidden cursor-pointer`}
          >
            <div className="flex flex-col items-center p-3">
              <span className="font-[700] text-base uppercase">{title}</span>
              {isMVPDetailsPage && (
                <span className="font-normal text-[23px] mt-3">{subtitle}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
