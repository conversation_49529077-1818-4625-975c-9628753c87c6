'use client'

import { MVPSummary<PERSON>hart } from './MVPSummaryChart'
import { MVPSummaryTable } from './MVPSummaryTable'
import { useRef, useState, useEffect } from 'react'
import { MVPMenu } from './MVPMenu'
import { useMVPStore } from '@/stores/mvp'
import { ConfigureReportButtonRef } from '../common/ConfigureReportButton'
import { useSearchParams } from 'next/navigation'
import { useReportConfig } from '../common/useReportConfig'
import { MVPReportConfig } from '../filters/MVPFilters'

export const MVPSummaryComponent = () => {
  const { mvpConfig, openConfigureReport, setMVPConfig } = useMVPStore()
  const configureReportButtonRef = useRef<ConfigureReportButtonRef>(null)
  const searchParams = useSearchParams()
  const reportId = searchParams.get('reportId')

  const { config, isLoading, error } = useReportConfig<MVPReportConfig>(
    'mvp',
    reportId
  )

  // Effect to update MVP config when config changes
  useEffect(() => {
    if (config) {
      setMVPConfig(config)
    }
  }, [config, setMVPConfig])

  // Show loading state
  if (isLoading) {
    return (
      <div className="w-full flex flex-col items-center justify-center p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2B5380] mb-4"></div>
        <h1 className="text-[24px] font-bold mb-2">Loading Report</h1>
        <p className="text-[16px] text-gray-600">
          Please wait while we load your report...
        </p>
      </div>
    )
  }

  // Show error if loading fails
  if (reportId && error) {
    return (
      <div className="flex items-center justify-center p-8 text-center">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">
            {reportError instanceof Error
              ? error.message.includes('You do not have access to this report')
                ? 'You do not have permission to view this report.'
                : error.message
              : 'An error occurred while loading the report.'}
          </span>
        </div>
        <button
          onClick={() => (window.location.href = '/reports')}
          className="font-semibold text-sm mt-4 bg-[#566582] hover:bg-[#1e3c5c] text-white pt-1 pb-1 pl-5 pr-5 rounded-md uppercase align-middle"
        >
          Return to Reports
        </button>
      </div>
    )
  }

  return (
    <div className="flex items-center w-full font-open-sans">
      <div className="bg-white rounded-md w-full">
        <div className="w-full">
          <div className="w-full">
            <MVPMenu
              configureReportButtonRef={
                configureReportButtonRef as React.RefObject<ConfigureReportButtonRef>
              }
              config={config}
            />
          </div>
          {mvpConfig.year != 0 ? (
            <div>
              <div className="h-max-[500px] w-full">
                <MVPSummaryTable />
              </div>
              <div className="w-full mt-20 border-t-[3px] border-[#205782]">
                <MVPChart />
              </div>
            </div>
          ) : (
            <div
              className={`${openConfigureReport ? 'text-[#CCCCCC]' : ''} flex flex-col items-center justify-center p-8 text-center1`}
            >
              <h1
                data-testid="placeholder-content"
                className="mb-[55px] text-[35px] font-bold"
              >
                New Report Creation
              </h1>
              <div className="text-center mb-[35px] text-[20px] font-normal">
                <p className="mb-2">
                  To create your report, please use the "configure report"
                  button on the top left.
                </p>
                <p className="mb-2">Select your report requirements.</p>
                <p className="mb-2">Run report.</p>
                <p className="mb-2">
                  To edit input data, return to the same menu and change inputs
                  or use the filter bar.
                </p>
              </div>
              <button
                onClick={() => {
                  if (configureReportButtonRef.current) {
                    configureReportButtonRef.current.setOpen(true)
                  }
                }}
                className={`font-semibold text-sm mt-4 mb-[60px] bg-[#566582] hover:bg-[#1e3c5c] text-white pt-1 pb-1 pl-5 pr-5 rounded-md uppercase align-middle ${openConfigureReport ? 'opacity-70' : ''}`}
              >
                Configure Report
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

function MVPChart() {
  const [interval, setInterval] = useState('Quarterly')
  const handleINtervalChange = (interval: string) => {
    setInterval(interval)
  }

  return (
    <>
      <div className="text-left ml-4 mt-[18px] text-sm font-open-sans font-normal">
        <label>Quality Score Over Time</label>
      </div>
      <Intervals
        handleIntervalChange={handleINtervalChange}
        interval={interval}
      />
      <div className="w-full flex justify-center">
        <div className="h-max-[700px] w-[1000px]">
          <MVPSummaryChart timeframe={interval} />
        </div>
      </div>
    </>
  )
}

function Intervals({
  interval,
  handleIntervalChange,
}: {
  interval: string
  handleIntervalChange: (interval: string) => void
}) {
  // Base styles for all buttons
  const baseStyles = 'text-[13px] py-1 px-8 border-y border-r border-gray-300'
  // First button needs left border
  const firstButtonStyles = 'border-l border-gray-300 rounded-l-md'
  // Last button needs rounded right corners
  const lastButtonStyles = 'rounded-r-md'
  // Styles for unselected buttons
  const unselectedStyles = `${baseStyles} bg-gray-50 text-gray-400 font-semibold`
  // Styles for selected button
  const selectedStyles = `${baseStyles} bg-[#EBEFFD] text-[#205782] font-bold`

  return (
    <div className="p-4">
      <ul className="flex font-sans shadow-sm max-w-fit mx-auto rounded-md overflow-hidden my-8">
        <li>
          <button
            className={`${
              interval === 'Quarterly' ? selectedStyles : unselectedStyles
            } ${firstButtonStyles}`}
            onClick={() => handleIntervalChange('Quarterly')}
          >
            QUARTERLY
          </button>
        </li>
        <li>
          <button
            className={`${
              interval === 'Monthly' ? selectedStyles : unselectedStyles
            } ${lastButtonStyles}`}
            onClick={() => handleIntervalChange('Monthly')}
          >
            MONTHLY
          </button>
        </li>
      </ul>
    </div>
  )
}
