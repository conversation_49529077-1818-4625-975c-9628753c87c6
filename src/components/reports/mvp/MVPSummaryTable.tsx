'use client'

import { useMemo, useState, useEffect } from 'react'
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  type SortingState,
} from '@tanstack/react-table'
import { ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { api } from '@/trpc/react'
import { useMVPStore } from '@/stores/mvp'
import Loader from '@/components/ui/Loader'
import { MVPSummary } from '@/types/reports/mvpTypes'

// Custom cell renderers
const GroupCell = ({ value, row }: { value: string; row: any }) => {
  const { mvpConfig, setMVPConfig } = useMVPStore()

  const handleSubmissionGroupClick = () => {
    setMVPConfig({
      ...mvpConfig,
      submissionGroup: row.original.Entity,
      mvp: row.original.MVP,
    })
  }

  return (
    <div className="flex items-center gap-2">
      <Link href="/reports/mvp/mvp-details" legacyBehavior>
        <a
          className="text-[#205782] text-xs underline"
          onClick={handleSubmissionGroupClick}
        >
          {value}
        </a>
      </Link>
      <ArrowRight className="h-4 w-4 text-[#205782]" />
    </div>
  )
}
const ProgressCell = ({ value, max }: { value: number; max: number }) => (
  <div className="flex flex-col gap-2">
    <span className="text-xs">{value?.toFixed(2)}</span>
    <div className="w-[70px] h-[10px] bg-[#DDDCDF] rounded-full overflow-hidden">
      <div
        className="h-full bg-[#2D7CBA] rounded-full"
        style={{ width: `${(value / max) * 100}%` }}
      />
    </div>
  </div>
)

export function MVPSummaryTable() {
  const [sorting, setSorting] = useState<SortingState>([])
  const { mvpConfig, setSummaryData } = useMVPStore()

  const { data: MVPSummaryData, isLoading: isloadingMVPSummary } =
    api.report.getMVPSummaryResults.useQuery({
      year: mvpConfig.year,
    })

  // Pass data back to parent component when loaded
  useEffect(() => {
    if (MVPSummaryData) {
      setSummaryData(MVPSummaryData)
    }
  }, [MVPSummaryData])

  // Define columns
  const columns: ColumnDef<{
    Entity: string
    MVP: string
    Quality: number
    IA: number
    PIScore: number
    Cost: number
    Score: number
    QualityMax?: number
    IAMax?: number
    PIScoreMax?: number
    CostMax?: number
  }>[] = [
    {
      accessorKey: 'Entity',
      header: 'SUBMISSION GROUP',
      cell: ({ getValue, row }) => (
        <GroupCell value={String(getValue())} row={row} />
      ),
    },
    {
      accessorKey: 'MVP',
      header: 'MVP',
      cell: ({ getValue }) => <div>{getValue<string>()}</div>,
    },
    {
      accessorKey: 'Quality',
      header: 'QUALITY',
      cell: ({ row }) => {
        return (
          <ProgressCell
            value={row.original.Quality}
            max={row.original.QualityMax!}
          />
        )
      },
    },
    {
      accessorKey: 'IA',
      header: 'IA',
      cell: ({ row }) => {
        return (
          <ProgressCell value={row.original.IA} max={row.original.IAMax!} />
        )
      },
    },
    {
      accessorKey: 'PIScore',
      header: 'PI',
      cell: ({ row }) => {
        return (
          <ProgressCell
            value={row.original.PIScore}
            max={row.original.PIScoreMax!}
          />
        )
      },
    },
    {
      accessorKey: 'Cost',
      header: 'COST',
      cell: ({ row }) => {
        return (
          <ProgressCell value={row.original.Cost} max={row.original.CostMax!} />
        )
      },
    },
    {
      accessorKey: 'Score',
      header: 'SCORE',
      cell: ({ getValue }) => {
        return <div className="text-xs">{getValue<number>().toFixed(2)}</div>
      },
    },
  ]

  const memoColumns = useMemo(() => columns, [])

  const MVPSummary = useMemo(() => MVPSummaryData, [MVPSummaryData])

  const table = useReactTable({
    data: MVPSummary || [],
    columns: memoColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  })

  if (isloadingMVPSummary) {
    return (
      <div className="relative top-4">
        <Loader className="" />
      </div>
    )
  }

  return (
    <div className="w-full overflow-auto">
      <table className="w-full border-collapse">
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="bg-[#566582] text-white font-semibold text-xs uppercase p-0"
                  style={{
                    minWidth: header.id === 'group' ? '200px' : '120px',
                  }}
                >
                  <div className="flex items-center px-4 py-3">
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </div>
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr
              key={row.original.Entity}
              className="bg-[#F9F8F9] border-b-4 border-white"
            >
              {row.getVisibleCells().map((cell) => (
                <td key={cell.id} className="p-0">
                  <div className="px-4 py-3">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
