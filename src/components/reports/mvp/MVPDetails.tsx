'use client'

import { useCallback, useEffect, useMemo, useState, useRef } from 'react'
import { MVPMenu } from './MVPMenu'
import { AgCharts } from 'ag-charts-react'
import {
  AgCartesianChartOptions,
  AgCartesianSeriesOptions,
} from 'ag-charts-community'
import { ArrowRight, Check, X } from 'lucide-react'
import Loader from '@/components/ui/Loader'
import { getColor } from '@/lib/generateColorsForChart'
import { useMVPStore } from '@/stores/mvp'
import { api } from '@/trpc/react'
import { GaugeChart } from './GaugeChart'

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table'
import * as Dialog from '@radix-ui/react-dialog'
import {
  QualityMeasuresScore,
  Score,
  ProviderPerformance,
  QualityMeasures,
  IAPIMeasure,
  ProviderDetail,
} from '@/types/reports/mvpTypes'
import { ConfigureReportButtonRef } from '../common/ConfigureReportButton'
import { useSearchParams } from 'next/navigation'
import { useReportConfig } from '../common/useReportConfig'
import { MVPReportConfig } from '../filters/MVPFilters'
import dayjs from 'dayjs'

interface TabsProps {
  tabs: string[]
  selectedTab: string
  onTabChange: (tab: string) => void
}

export const MVPDetails = () => {
  const configureReportButtonRef = useRef<ConfigureReportButtonRef>(null)
  const searchParams = useSearchParams()
  const reportId = searchParams.get('reportId')

  const {
    mvpConfig,
    setMVPConfig,
    openConfigureReport,
    setConfigureReportOpen,
  } = useMVPStore()

  const { config, isLoading, error } = useReportConfig<MVPReportConfig>(
    'mvp',
    reportId
  )

  // Effect to update MVP config when config changes
  useEffect(() => {
    if (reportId && config) {
      setMVPConfig(config)
    }
  }, [config])

  // Show loading state
  if (reportId && isLoading) {
    return (
      <div className="w-full flex flex-col items-center justify-center p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2B5380] mb-4"></div>
        <h1 className="text-[24px] font-bold mb-2">Loading Report</h1>
        <p className="text-[16px] text-gray-600">
          Please wait while we load your report...
        </p>
      </div>
    )
  }

  // Show error if loading fails
  if (reportId && error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">
            {reportError instanceof Error
              ? error.message.includes('You do not have access to this report')
                ? 'You do not have permission to view this report.'
                : error.message
              : 'An error occurred while loading the report.'}
          </span>
        </div>
        <button
          onClick={() => (window.location.href = '/reports')}
          className="mt-4 bg-[#566582] hover:bg-[#1e3c5c] text-white pt-1 pb-1 pl-5 pr-5 rounded-md uppercase align-middle"
        >
          Return to Reports
        </button>
      </div>
    )
  }

  return (
    <div className="flex items-center w-full font-open-sans">
      <div className="bg-white rounded-md w-full">
        <div className="w-full">
          <div className="w-full">
            <MVPMenu
              configureReportButtonRef={
                configureReportButtonRef as React.RefObject<ConfigureReportButtonRef>
              }
              config={config}
            />
          </div>

          {mvpConfig.year != 0 ? (
            <div className="w-full">
              <OverallScore />
              <div className="flex w-full mt-20 gap-14">
                <div className="w-1/2 border-t-[3px] border-[#205782] p-5 text-center">
                  <QualityMeasuresChart />
                </div>
                <div className="w-1/2 border-t-[3px] border-[#205782] p-5 text-center">
                  <div className="text-left ml-4 mb-7 text-sm font-open-sans font-normal">
                    <label>Provider Performance</label>
                  </div>
                  <ProviderPerformanceTable />
                </div>
              </div>
              <MeasureTypeTables />
            </div>
          ) : (
            <div
              className={`${openConfigureReport ? 'text-[#CCCCCC]' : ''} flex flex-col items-center justify-center p-8 text-center1`}
            >
              <h1 className="mb-[55px] text-[35px] font-bold">
                New Report Creation
              </h1>
              <div className="text-center mb-[35px] text-[20px] font-normal">
                <p className="mb-2">
                  To create your report, please use the "configure report"
                  button on the top left.
                </p>
                <p className="mb-2">Select your report requirements.</p>
                <p className="mb-2">Run report.</p>
                <p className="mb-2">
                  To edit input data, return to the same menu and change inputs
                  or use the filter bar.
                </p>
              </div>
              <button
                onClick={() => {
                  // Use the ref to open the ConfigureReportButton
                  if (configureReportButtonRef.current) {
                    configureReportButtonRef.current.setOpen(true)
                  }
                  setConfigureReportOpen(true)
                }}
                className={`font-semibold text-sm mt-4 mb-[60px] bg-[#566582] hover:bg-[#1e3c5c] text-white pt-1 pb-1 pl-5 pr-5 rounded-md uppercase align-middle ${openConfigureReport ? 'opacity-70' : ''}`}
              >
                Configure Report
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

function OverallScore() {
  const { mvpConfig, setDetailsData, detailsData } = useMVPStore()
  const [configReady, setConfigReady] = useState(false)
  const [costScore, setCostScore] = useState<Score>({
    Score: 0,
    Min: 0,
    Max: 0,
  })
  const [overallScore, setOverallScore] = useState<Score>({ Score: 0 })

  // Set config ready when mvp and submissionGroup are available
  useEffect(() => {
    if (mvpConfig.mvp && mvpConfig.submissionGroup) {
      setConfigReady(true)
    }
  }, [mvpConfig.mvp, mvpConfig.submissionGroup])

  // Get score for each measure type
  const { data: scores } = api.report.getScoreByMeasureType.useQuery(
    {
      year: mvpConfig.year,
      mvp: mvpConfig.mvp!,
      entity: mvpConfig.submissionGroup!,
    },
    { enabled: configReady }
  )

  const qualityScore = scores?.find((x) => x.MeasureType === 'Quality')
  const iaScore = scores?.find((x) => x.MeasureType === 'IA')
  const piScore = scores?.find((x) => x.MeasureType === 'PI')

  // Get overall weighted score
  const { data: originalOverallScore } =
    api.report.getOverallWeightedScore.useQuery(
      {
        year: mvpConfig.year,
        mvp: mvpConfig.mvp!,
        entity: mvpConfig.submissionGroup!,
      },
      { enabled: configReady }
    )

  // Calculate cost score max value
  useEffect(() => {
    if (iaScore && piScore && qualityScore) {
      const maxScore = 100 - (iaScore.Max! + piScore.Max! + qualityScore.Max!)
      setCostScore((prev) => {
        // Only update if Max has changed to prevent loops
        if (prev.Max !== maxScore) {
          return { ...prev, Min: 0, Max: maxScore }
        }
        return prev
      })
    }
  }, [iaScore?.Score, piScore?.Score, qualityScore?.Score])

  // Calculate overall score
  useEffect(() => {
    if (originalOverallScore) {
      setOverallScore({
        Score: originalOverallScore.Score + costScore.Score,
      })
    }
  }, [originalOverallScore?.Score, costScore.Score])

  // Handler for cost updates
  const handleUpdatedCost = useCallback((cost: number) => {
    setCostScore((prev) => ({ ...prev, Score: cost }))
  }, [])

  useEffect(() => {
    setDetailsData({
      ...detailsData,
      overallScore,
      qualityScore,
      iaScore,
      piScore,
      costScore,
    })
  }, [overallScore, qualityScore, iaScore, piScore, costScore])

  return (
    <div className="w-full bg-white h-max-[500px]">
      <div className="flex justify-between gap-5">
        {/* Overall Weighted Score */}
        <div className="flex-1 min-w-[200px] bg-[#F5F7FF] border-t-[3px] border-[#205782] p-4 h-[158px]">
          <h3 className="text-sm mb-8 font-normal">Overall Weighted Score</h3>
          <p className="text-[45px] font-semibold text-[#2D7CBA]">
            {overallScore?.Score ?? ''}
          </p>
        </div>

        {/* Quality Score */}
        <div className="flex-1 border-t-[3px] border-[#205782] p-4 text-center">
          <h3 className="text-sm text-[#282828]">
            Quality Score ({qualityScore?.Score}%)
          </h3>
          <div className="min-w-[200px]">
            {!qualityScore ? <Loader /> : <GaugeChart score={qualityScore} />}
          </div>
        </div>

        {/* Improvement Activities */}
        <div className="flex-1 border-t-[3px] border-[#205782] p-4 text-center">
          <h3 className="text-sm text-[#282828] mb-2">
            Improvement Activities ({iaScore?.Score}%)
          </h3>
          <div className="min-w-[200px]">
            {!iaScore ? <Loader /> : <GaugeChart score={iaScore} />}
          </div>
        </div>

        {/* Promoting Interoperability */}
        <div className="flex-1 border-t-[3px] border-[#205782] p-4 text-center">
          <h3 className="text-sm text-[#282828] mb-2">
            Promoting Interoperability ({piScore?.Score}%)
          </h3>
          <div className="min-w-[200px]">
            {!piScore ? <Loader /> : <GaugeChart score={piScore} />}
          </div>
        </div>

        {/* Cost */}
        <div className="flex-1 min-w-[200px] border-t-[3px] border-[#205782] p-4 text-center">
          <h3 className="text-sm text-[#282828] mb-2">
            Cost ({costScore?.Score}%)
          </h3>
          <div className="min-w-[200px]">
            {costScore?.Max === 0 ? (
              <Loader />
            ) : (
              <GaugeChart
                score={costScore}
                isCostGauge={true}
                setUpdatedCost={handleUpdatedCost}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

const Tabs: React.FC<TabsProps> = ({ tabs, selectedTab, onTabChange }) => {
  // Base styles for all buttons
  const baseStyles = 'text-[13px] py-1 px-8 border-y border-r border-gray-300'
  // First button needs left border
  const firstButtonStyles = 'border-l border-gray-300 rounded-l-md'
  // Last button needs rounded right corners
  const lastButtonStyles = 'rounded-r-md'
  // Styles for unselected buttons
  const unselectedStyles = `${baseStyles} bg-gray-50 text-gray-400 font-semibold`
  // Styles for selected button
  const selectedStyles = `${baseStyles} bg-[#EBEFFD] text-[#205782] font-bold`

  return (
    <div className="p-4">
      <ul className="flex font-sans shadow-sm max-w-fit mx-auto rounded-md overflow-hidden my-8">
        {tabs.map((tab, index) => (
          <li key={index}>
            <button
              className={`${
                selectedTab === tab ? selectedStyles : unselectedStyles
              } ${index === 0 ? firstButtonStyles : ''} ${
                index === tabs.length - 1 ? lastButtonStyles : ''
              }`}
              onClick={() => onTabChange(tab)}
            >
              {tab.toUpperCase()}
            </button>
          </li>
        ))}
      </ul>
    </div>
  )
}

function QualityMeasuresChart() {
  const intervalTabs = ['Quarterly', 'Monthly']
  const [interval, setInterval] = useState(intervalTabs[0]!)
  const { mvpConfig, setDetailsData, detailsData } = useMVPStore()
  const trpcUtils = api.useUtils()
  const {
    data: QualityMeasureScoresData,
    isLoading: isLoadingQualityMeasureScores,
  } = api.report.getQualityMeasuresScore.useQuery(
    {
      year: mvpConfig.year,
      periodType: interval,
      mvp: mvpConfig.mvp!,
      entity: mvpConfig.submissionGroup!,
    },
    {
      staleTime: 0, // Always fetch fresh data
    }
  )

  useEffect(() => {
    if (interval) {
      trpcUtils.report.getQualityMeasuresScore.invalidate()
    }
  }, [interval])

  const QualityMeasureScores = useMemo(
    () => QualityMeasureScoresData,
    [QualityMeasureScoresData]
  )

  const measures = [...new Set(QualityMeasureScores?.map((x) => x.MeasureName))]

  const groupesByPeriod = QualityMeasureScores?.reduce(
    (acc, measure) => {
      const key = measure.Period
      if (!acc[key]) acc[key] = []
      acc[key].push(measure)
      return acc
    },
    {} as { [key: string]: QualityMeasuresScore[] }
  )

  const transformedData: any = []
  if (groupesByPeriod) {
    for (const [key, group] of Object.entries(groupesByPeriod!)) {
      const obj: any = {}
      const period = dayjs(key)
      obj.Period =
        interval === 'Quarterly'
          ? `Q${period.quarter()}-${period.year()}`
          : period.format('MMM-YYYY')

      group.map((x) => {
        obj[x.MeasureName.replaceAll(' ', '')] = x.Score
      })

      transformedData.push(obj)
    }
  }

  const series = measures.map((measure, index) => {
    const colorSet = getColor(index)
    return {
      type: 'area',
      xKey: 'Period',
      yKey: measure.replaceAll(' ', ''),
      yName: measure,
      stroke: colorSet?.line,
      strokeWidth: 1,
      fill: colorSet?.background,
      interpolation: { type: 'smooth' },
      connectMissingData: true,
      label: {
        enabled: true,
        fontWeight: 'lighter',
        color: 'black',
      },
      marker: {
        fill: colorSet?.line,
        fillOpacity: 0.7,
      },
    } as AgCartesianSeriesOptions
  })

  const maxScore = Math.max(
    ...(QualityMeasureScores?.map((x) => x.Score) ?? [])
  )

  const options: AgCartesianChartOptions = {
    animation: {
      enabled: true,
    },
    data: transformedData,
    series: series,
    axes: [
      {
        type: 'category',
        position: 'bottom',
      },
      {
        type: 'number',
        position: 'left',
        min: 0,
        max: maxScore > 10 ? maxScore : 10,
        title: {
          text: '',
        },
        line: {
          width: 0.5,
        },
        crosshair: {
          enabled: false,
        },
      },
    ],
    legend: {
      position: 'bottom',
      item: {
        line: {
          strokeWidth: 0,
          length: 0,
        },
        marker: {
          shape: 'square',
        },
      },
    },
  }

  useEffect(() => {
    if (QualityMeasureScoresData) {
      setDetailsData({
        ...detailsData,
        qualityMeasures: QualityMeasureScoresData,
      })
    }
  }, [QualityMeasureScoresData])

  return (
    <>
      <div className="text-left ml-4 text-sm font-open-sans font-normal">
        <label>Quality Score Over Time</label>
      </div>
      <Tabs
        tabs={intervalTabs}
        selectedTab={interval}
        onTabChange={(tab) => setInterval(tab)}
      />
      <div>
        {isLoadingQualityMeasureScores ? (
          <div className="relative top-4">
            <Loader className="" />
          </div>
        ) : (
          <AgCharts options={options} />
        )}
      </div>
    </>
  )
}

function GroupCell({ value }: { value: string }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <div
        className="flex items-center gap-2 cursor-pointer"
        onClick={() => setIsDialogOpen(true)}
      >
        <span className="text-[#205782] text-xs underline">{value}</span>
        <ArrowRight className="h-4 w-4 text-[#205782]" />
      </div>
      {isDialogOpen && (
        <ProviderDetailDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          providerName={value}
        />
      )}
    </>
  )
}

function ProviderPerformanceTable() {
  const [sorting, setSorting] = useState<SortingState>([])

  const { mvpConfig, setDetailsData, detailsData } = useMVPStore()

  const {
    data: ProviderPerformanceData,
    isLoading: isProviderPerformanceDataLoading,
  } = api.report.getProviderPerformance.useQuery(
    {
      year: mvpConfig.year,
      mvp: mvpConfig.mvp!,
      entity: mvpConfig.submissionGroup!,
    },
    {
      staleTime: 0, // Always fetch fresh data
    }
  )

  useEffect(() => {
    if (ProviderPerformanceData) {
      setDetailsData({
        ...detailsData,
        providerPerformance: ProviderPerformanceData,
      })
    }
  }, [ProviderPerformanceData])

  // Define columns
  const columns: ColumnDef<ProviderPerformance>[] = [
    {
      accessorKey: 'ProviderName',
      header: 'PROVIDER',
      cell: ({ getValue }) => <GroupCell value={String(getValue())} />,
    },
    {
      accessorKey: 'MeasureCount',
      header: 'MEASURES',
      cell: ({ getValue }) => {
        return <div className="text-xs text-center">{getValue<number>()}</div>
      },
    },
    {
      accessorKey: 'Points',
      header: 'AVERAGE POINTS',
      cell: ({ getValue }) => {
        return (
          <div className="text-xs text-center">
            {getValue<number>().toFixed(2)}
          </div>
        )
      },
    },
  ]

  const memoColumns = useMemo(() => columns, [])

  const ProviderPerformance = useMemo(
    () => ProviderPerformanceData,
    [ProviderPerformanceData]
  )

  const table = useReactTable({
    data: ProviderPerformance || [],
    columns: memoColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  })

  if (isProviderPerformanceDataLoading) {
    return (
      <div className="relative top-4">
        <Loader className="" />
      </div>
    )
  }

  return (
    <div className="w-full overflow-auto">
      <table className="w-full border-collapse">
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="bg-[#566582] text-white font-semibold text-xs uppercase p-0"
                  style={{
                    minWidth: header.id === 'group' ? '200px' : '120px',
                  }}
                >
                  <div className="flex items-center px-4 py-3 justify-center">
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </div>
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr
              key={row.original.ProviderName}
              className="bg-[#F9F8F9] border-b-4 border-white"
            >
              {row.getVisibleCells().map((cell) => (
                <td key={cell.id} className="p-0">
                  <div className="px-4 py-3">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </div>
                </td>
              ))}
            </tr>
          ))}
          {table.getRowModel().rows.length === 0 && (
            <tr>
              <td colSpan={memoColumns.length} className="p-0">
                <div className="px-4 py-6 text-center">No data available</div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  )
}

// Add this component for the provider details dialog
function ProviderDetailDialog({
  isOpen,
  onClose,
  providerName,
}: {
  isOpen: boolean
  onClose: () => void
  providerName: string
}) {
  const { mvpConfig } = useMVPStore()

  const { data: ProviderDetailData, isLoading: isProviderDetailDataLoading } =
    api.report.getProviderDetails.useQuery(
      {
        year: mvpConfig.year,
        providerName,
      },
      {
        staleTime: 0, // Always fetch fresh data
      }
    )

  const ProviderDetails = useMemo(
    () => ProviderDetailData,
    [ProviderDetailData]
  )

  const columns: ColumnDef<ProviderDetail>[] = [
    {
      accessorKey: 'MeasureName',
      header: 'MEASURE',
      cell: ({ getValue }) => (
        <div className="text-xs text-center">{getValue<string>()}</div>
      ),
    },
    {
      accessorKey: 'Numerator',
      header: 'NUMERATOR',
      cell: ({ getValue }) => (
        <div className="text-xs text-center">
          {getValue<number>() ? getValue<number>().toFixed(2) : '-'}
        </div>
      ),
    },
    {
      accessorKey: 'Denominator',
      header: () => <div>DENOMINATOR</div>,
      cell: ({ getValue }) => (
        <div className="text-xs text-center">
          {getValue<number>() ? getValue<number>().toFixed(2) : '-'}
        </div>
      ),
    },
    {
      accessorKey: 'Performance',
      header: 'PERFORMANCE',
      cell: ({ getValue }) => (
        <div className="text-xs text-center">
          {getValue<number>() ? getValue<number>().toFixed(2) : '-'}
        </div>
      ),
    },
    {
      accessorKey: 'PTile',
      header: 'DECILE',
      cell: ({ getValue }) => (
        <div className="text-xs text-center">
          {getValue<number>() ? getValue<number>().toFixed(2) : 'N/A'}
        </div>
      ),
    },
    {
      accessorKey: 'Points',
      header: 'POINTS',
      cell: ({ getValue }) => (
        <div className="text-xs text-center">
          {getValue<number>() ? getValue<number>().toFixed(2) : 'N/A'}
        </div>
      ),
    },
  ]

  const memoColumns = useMemo(() => columns, [])
  const table = useReactTable({
    data: ProviderDetails || [],
    columns: memoColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/20 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed top-[170px] bottom-0 right-0  z-[100] w-full max-w-[50%] bg-white shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right duration-300 overflow-auto">
          <div className="flex justify-between items-center p-4 border-b">
            <Dialog.Title className="text-lg font-medium">
              Provider Detail View
            </Dialog.Title>
            <Dialog.Close asChild>
              <button className="rounded-full p-1 hover:bg-gray-100">
                <X className="h-5 w-5" />
                <span className="sr-only">Close</span>
              </button>
            </Dialog.Close>
          </div>

          <div className="p-4">
            <div className="bg-[#45536D] p-4 mb-4 text-white font-semibold">
              <h3 className="text-center font-medium">Mitchell, Sierra MD</h3>
            </div>

            <div className="overflow-x-auto">
              {isProviderDetailDataLoading ? (
                <div className="relative top-4">
                  <Loader className="" />
                </div>
              ) : (
                <table className="w-full border-collapse">
                  <thead>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <tr key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <th
                            key={header.id}
                            className="bg-[#566582] text-white font-semibold text-xs uppercase p-0"
                            style={{
                              minWidth:
                                header.id === 'group' ? '200px' : '120px',
                            }}
                          >
                            <div className="flex items-center px-4 py-3 justify-center">
                              {flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                            </div>
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                  <tbody>
                    {table.getRowModel().rows.map((row) => (
                      <tr
                        key={row.original.MeasureName}
                        className="bg-[#F9F8F9] border-b-4 border-white"
                      >
                        {row.getVisibleCells().map((cell) => (
                          <td key={cell.id} className="p-0">
                            <div className="px-4 py-3">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </div>
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
          <Dialog.Description />
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

function MeasureTypeTables() {
  const measureTypeTabs = ['Quality', 'Activities', 'Interoperability']
  const [measureType, setMeasureType] = useState(measureTypeTabs[0])
  const { mvpConfig, setDetailsData, detailsData } = useMVPStore()
  const [sorting, setSorting] = useState<SortingState>([])

  const { data: QualityMeasuresData, isLoading: isQualityMeasuresLoading } =
    api.report.getQualityMeasures.useQuery({
      year: mvpConfig.year,
      mvp: mvpConfig.mvp!,
      entity: mvpConfig.submissionGroup!,
    })

  const { data: IAPIMeasuresData, isLoading: isLoadingIAPIMeasures } =
    api.report.getIAPIMeasures.useQuery({
      year: mvpConfig.year,
      mvp: mvpConfig.mvp!,
      entity: mvpConfig.submissionGroup!,
    })

  // Pass all measure types data back to parent component
  useEffect(() => {
    if (QualityMeasuresData && IAPIMeasuresData) {
      setDetailsData({
        ...detailsData,
        qualityMeasuresData: QualityMeasuresData,
        iaMeasuresData: IAPIMeasuresData.IAMeasures || [],
        piMeasuresData: IAPIMeasuresData.PIMeasures || [],
      })
    }
  }, [QualityMeasuresData, IAPIMeasuresData])

  // Define columns

  const getColumns = () => {
    const columns: ColumnDef<QualityMeasures | IAPIMeasure>[] = [
      {
        accessorKey: 'MeasureName',
        header:
          measureType === 'Quality'
            ? 'QUALITY MEASURE'
            : measureType === 'Activities'
              ? 'IMPROVEMENT ACTIVITY'
              : 'MEASURE',
        cell: ({ getValue }) => (
          <div className="text-xs text-left">{getValue<string>()}</div>
        ),
      },
    ]
    if (measureType === 'Quality') {
      columns.push({
        accessorKey: 'Rank',
        header: 'INCLUDED',
        cell: ({ getValue }) => {
          return (
            <div className="flex justify-center">
              {getValue<number>() <= 4 ? (
                <Check className="w-5 h-5 text-green-500 font-semibold" />
              ) : (
                <X className="w-5 h-5 text-red-500 font-semibold" />
              )}
            </div>
          )
        },
      })
      columns.push({
        accessorKey: 'Performance',
        header: 'RATE',
        cell: ({ getValue }) => {
          return (
            <div className="text-xs text-center">
              {getValue<number>() ? getValue<number>().toFixed(2) : '-'}
            </div>
          )
        },
      })
      columns.push({
        accessorKey: 'Denominator',
        header: 'DENOMINATOR',
        cell: ({ getValue }) => {
          return (
            <div className="text-xs text-center">
              {getValue<number>() ?? '-'}
            </div>
          )
        },
      })
      columns.push({
        accessorKey: 'Numerator',
        header: 'NUMERATOR',
        cell: ({ getValue }) => {
          return (
            <div className="text-xs text-center">
              {getValue<number>() ?? '-'}
            </div>
          )
        },
      })
      columns.push({
        accessorKey: 'PTile',
        header: 'DECILE',
        cell: ({ getValue }) => {
          return (
            <div className="text-xs text-center">
              {getValue<number>() ? getValue<number>().toFixed(2) : '-'}
            </div>
          )
        },
      })
    }

    if (measureType === 'Activities' || measureType === 'Interoperability') {
      columns.push({
        accessorKey: 'Completed',
        header: 'COMPLETED',
        cell: ({ getValue }) => {
          return (
            <div className="flex justify-center">
              {getValue<boolean>() === true ? (
                <Check className="w-5 h-5 text-green-500 font-semibold" />
              ) : (
                <X className="w-5 h-5 text-red-500 font-semibold" />
              )}
            </div>
          )
        },
      })
    }

    columns.push({
      accessorKey: 'Points',
      header: 'POINTS',
      cell: ({ getValue }) => {
        return (
          <div className="text-xs">
            {getValue<number>() ? getValue<number>().toFixed(2) : 'N/A'}
          </div>
        )
      },
    })

    return columns
  }
  const memoColumns = useMemo(() => getColumns(), [measureType])

  const measureResults = useMemo(() => {
    switch (measureType) {
      case 'Quality':
        return QualityMeasuresData
      case 'Activities':
        return IAPIMeasuresData?.IAMeasures
      case 'Interoperability':
        return IAPIMeasuresData?.PIMeasures
      default:
        return []
    }
  }, [QualityMeasuresData, IAPIMeasuresData, measureType])

  const table = useReactTable({
    data: measureResults || [],
    columns: memoColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  })

  const MeasureResultstable = () => {
    return (
      <div className="w-full overflow-auto">
        <table className="w-full border-collapse">
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="bg-[#566582] text-white font-semibold text-xs uppercase p-0"
                    style={{
                      minWidth: header.id === 'group' ? '200px' : '120px',
                    }}
                  >
                    <div className="flex items-center px-4 py-3 justify-center">
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <tr
                key={row.original.MeasureName}
                className="bg-[#F9F8F9] border-b-4 border-white"
              >
                {row.getVisibleCells().map((cell) => (
                  <td key={cell.id} className="p-0">
                    <div className="px-4 py-3">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </div>
                  </td>
                ))}
              </tr>
            ))}
            {table.getRowModel().rows.length === 0 && (
              <tr>
                <td colSpan={memoColumns.length} className="p-0">
                  <div className="px-4 py-6 text-center">No data available</div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    )
  }

  return (
    <div className="flex w-full flex-col">
      <Tabs
        tabs={measureTypeTabs}
        selectedTab={measureType!}
        onTabChange={setMeasureType}
      />
      <div className="text-center">
        {isLoadingIAPIMeasures || isQualityMeasuresLoading ? (
          <div className="relative top-4">
            <Loader className="" />
          </div>
        ) : (
          <MeasureResultstable />
        )}
      </div>
    </div>
  )
}
