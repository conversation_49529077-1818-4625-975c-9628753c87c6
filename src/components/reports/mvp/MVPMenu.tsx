'use client'

import React, { useRef, useState } from 'react'
import { usePathname } from 'next/navigation'
import { useMVPStore } from '@/stores/mvp'
import ConfigureReportButton, {
  ConfigureReportButtonRef,
} from '../common/ConfigureReportButton'
import { MVPReportConfig } from '../filters/MVPFilters'
import { Dayjs } from 'dayjs'
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from '@/components/ui/popover'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import dayjs from 'dayjs'
import { getDecadeRange } from '@/lib/dateRange'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import chevronDown from '../../../../public/images/chevronDown.svg'
import MVPReportActions from '../actions/MVPReportActions'
import {
  IAPIMeasure,
  ProviderPerformance,
  QualityMeasures,
  QualityMeasuresScore,
  Score,
} from '@/types/reports/mvpTypes'

interface MVPDetailsData {
  overallScore: Score | null
  qualityScore: Score | null
  iaScore: Score | null
  piScore: Score | null
  costScore: Score | null
  qualityMeasures: QualityMeasuresScore[]
  providerPerformance: ProviderPerformance[]
  qualityMeasuresData: QualityMeasures[]
  iaMeasuresData: IAPIMeasure[]
  piMeasuresData: IAPIMeasure[]
}

interface MVPMenuProps {
  configureReportButtonRef?: React.RefObject<ConfigureReportButtonRef>
  config: MVPReportConfig | null
}

export const MVPMenu = ({ configureReportButtonRef, config }: MVPMenuProps) => {
  const pathname = usePathname()
  const isMVPDetailsPage = pathname.includes('mvp-details')
  const datePickerRef = useRef<HTMLDivElement>(null)
  const internalConfigureReportButtonRef =
    useRef<ConfigureReportButtonRef>(null)

  const {
    mvpConfig,
    setMVPConfig,
    setConfigureReportOpen,
    summaryData,
    chartData,
  } = useMVPStore()

  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(
    mvpConfig.year == 0 ? null : dayjs().year(mvpConfig.year)
  )
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false)

  const buttonRef = configureReportButtonRef || internalConfigureReportButtonRef

  const handleConfigChange = (config: MVPReportConfig) => {
    setMVPConfig(config)
  }

  const handleDateChange = (
    date: Dayjs | null,
    isSelection: boolean = true
  ) => {
    // Ensure the date is valid before updating
    if (date && date.isValid()) {
      let adjustedDate = date

      adjustedDate = date.startOf('year')

      setSelectedDate(adjustedDate)

      // Only close the date picker if this is an actual date selection, not navigation
      if (isSelection) {
        setMVPConfig({ ...mvpConfig, year: adjustedDate.year() })
        setIsDatePickerOpen(false)
      }
    }
  }

  return (
    <div className={`w-full bg-white pt-6 pb-10`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-6">
          <div>
            <ConfigureReportButton
              ref={buttonRef}
              reportType="mvp"
              onConfigChange={handleConfigChange}
              initialConfig={{
                year: mvpConfig.year,
                mvp: mvpConfig.mvp || '',
                submissionGroup: mvpConfig.submissionGroup || '',
                runtime: mvpConfig.runtime,
              }}
              onOpenChange={(open) => setConfigureReportOpen(open)}
            />
          </div>
          {mvpConfig.runtime && !isMVPDetailsPage && (
            <div
              className="relative"
              ref={datePickerRef}
              onMouseDown={(e) => {
                e.preventDefault()
                e.stopPropagation()
              }}
            >
              <Popover
                open={isDatePickerOpen}
                onOpenChange={setIsDatePickerOpen}
              >
                <PopoverTrigger asChild>
                  <button className="flex items-center space-x-2 bg-[#F5F7FF] text-[#566582] font-semibold text-xs px-4 py-1.5 rounded">
                    <span className="uppercase">{mvpConfig.year}</span>
                    <Image
                      src={chevronDown}
                      alt="chevronDown"
                      height={14}
                      width={14}
                      className={cn(isDatePickerOpen && 'rotate-180')}
                    />
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-auto p-0 border-0 rounded-lg shadow-lg"
                  align="start"
                >
                  <div className="p-[10px] bg-ui-pale-blue w-fit rounded-lg date-picker-popup shadow-[0px_0px_4px_0px_#00000040]">
                    <div className="pt-4 bg-white">
                      <div className="flex justify-between items-center mx-8">
                        <div className="flex items-center text-ui-dark-gray font-bold w-full">
                          <button
                            onClick={() => {
                              const currentDecadeStart =
                                Math.floor(
                                  (selectedDate?.year() || dayjs().year()) / 10
                                ) * 10
                              const newDate = (selectedDate || dayjs()).year(
                                currentDecadeStart - 10
                              )
                              handleDateChange(newDate, false)
                            }}
                            className="focus:outline-none"
                          >
                            <ChevronLeft className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                          </button>
                          <div className="text-ui-dark-gray font-semibold w-full text-center text-sm  ">
                            {selectedDate
                              ? getDecadeRange(selectedDate)
                              : getDecadeRange(dayjs())}
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            const currentDecadeStart =
                              Math.floor(
                                (selectedDate?.year() || dayjs().year()) / 10
                              ) * 10
                            const newDate = (selectedDate || dayjs()).year(
                              currentDecadeStart + 10
                            )
                            handleDateChange(newDate, false)
                          }}
                          className="focus:outline-none"
                        >
                          <ChevronRight className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-4 bg-white p-4 rounded-lg">
                      {Array.from({ length: 10 }, (_, i) => {
                        const decadeStart =
                          Math.floor(
                            (selectedDate?.year() || dayjs().year()) / 10
                          ) * 10
                        const year = decadeStart + i
                        return (
                          <button
                            key={year}
                            className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans h-[30px] focus:outline-none 
                                              ${
                                                selectedDate &&
                                                selectedDate.year() === year
                                                  ? 'bg-ui-dark-gray text-white rounded-l rounded-r'
                                                  : 'text-black'
                                              }
                                              hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
                            onClick={() => {
                              const newDate = (selectedDate || dayjs())
                                .year(year)
                                .startOf('year')
                              handleDateChange(newDate)
                            }}
                          >
                            {year}
                          </button>
                        )
                      })}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          )}
        </div>

        {/* Actions Button */}
        <div className="flex items-center space-x-6">
          {mvpConfig.runtime && (
            <MVPReportActions
              reportConfig={{
                year: mvpConfig.year,
                mvp: mvpConfig.mvp || '',
                submissionGroup: mvpConfig.submissionGroup || '',
                runtime: config?.runtime,
                name: config?.name,
                description: config?.description,
                reportId: config?.reportId,
              }}
              summaryData={summaryData}
              chartData={chartData}
            />
          )}
        </div>
      </div>
    </div>
  )
}
