import React, { useMemo } from 'react'
import * as XLSX from 'xlsx'
import { ACOReportConfig } from '../filters/ACOFilters'
import ReportActions, { ExportHandlers } from './ReportActions'

interface ACOPerformanceReportActionsProps {
  analysisReportHeader: string[]
  analysisReportDataset: Record<
    string,
    string | number | boolean | null | undefined
  >[]
  analysisReportForHigherOrderHeader: string[]
  analysisReportForHigherOrderDataset: Record<
    string,
    string | number | boolean | null | undefined
  >[]
  reportConfig: ACOReportConfig
  onSaveComplete?: () => void
}

const ACOPerformanceReportActions: React.FC<
  ACOPerformanceReportActionsProps
> = ({
  analysisReportHeader,
  analysisReportDataset,
  analysisReportForHigherOrderHeader,
  analysisReportForHigherOrderDataset,
  reportConfig,
  onSaveComplete,
}) => {
  // Merge the two headers and datasets with a space between
  const mergedExportDataset = useMemo(() => {
    // Check if both datasets are empty
    const isFirstDatasetEmpty =
      !analysisReportDataset || analysisReportDataset.length === 0
    const isSecondDatasetEmpty =
      !analysisReportForHigherOrderDataset ||
      analysisReportForHigherOrderDataset.length === 0

    // If both datasets are empty, return undefined to disable the exporter
    if (isFirstDatasetEmpty && isSecondDatasetEmpty) {
      return undefined
    }

    // Add a blank row between datasets
    const spacerRow: Record<
      string,
      string | number | boolean | null | undefined
    > = {}

    // Create a combined dataset with a blank row in between
    const combinedDataset = [
      ...analysisReportDataset,
      spacerRow, // Add a blank row as a separator
      ...analysisReportForHigherOrderDataset,
    ]

    // Combine all headers (this will include all unique headers from both datasets)
    const combinedHeaders = [
      ...new Set([
        ...analysisReportHeader,
        ...analysisReportForHigherOrderHeader,
      ]),
    ]

    return {
      headers: combinedHeaders,
      dataset: combinedDataset,
    }
  }, [
    analysisReportHeader,
    analysisReportDataset,
    analysisReportForHigherOrderHeader,
    analysisReportForHigherOrderDataset,
  ])

  // Process a single dataset to handle special cases like HTML breaks
  const processDataset = (
    headers: string[],
    dataset: Record<string, string | number | boolean | null | undefined>[]
  ) => {
    // Process the dataset to handle special cases
    const processedData = dataset.map((row) => {
      const processedRow: Record<
        string,
        string | number | boolean | null | undefined
      > = {}

      // Process each value in the row
      headers.forEach((header) => {
        const value = row[header]

        // Handle special case for values with </br>
        if (typeof value === 'string' && value.includes('</br>')) {
          // Replace HTML break with space for CSV export
          processedRow[header] = value.replace('</br>', ' - ')
        } else {
          processedRow[header] = value ?? ''
        }
      })

      return processedRow
    })

    return { headers, dataset: processedData }
  }

  // Process the dataset if provided
  const processedDataset = useMemo(() => {
    if (!mergedExportDataset) {
      return undefined
    }

    const { headers, dataset } = mergedExportDataset
    return processDataset(headers, dataset)
  }, [mergedExportDataset])

  // Function to convert data to CSV format
  const convertToCSV = (headers: string[], data: Record<string, any>[]) => {
    // Create header row
    const headerRow = headers.join(',')

    // Create data rows
    const dataRows = data
      .map((row) => {
        return headers
          .map((header) => {
            // Handle values that might contain commas or quotes
            const value = row[header] !== undefined ? row[header] : ''
            const valueStr = String(value)

            // Escape quotes and wrap in quotes if needed
            if (
              valueStr.includes(',') ||
              valueStr.includes('"') ||
              valueStr.includes('\n')
            ) {
              return `"${valueStr.replace(/"/g, '""')}"`
            }
            return valueStr
          })
          .join(',')
      })
      .join('\n')

    // Combine header and data
    return `${headerRow}\n${dataRows}`
  }

  // Function to handle CSV export
  const handleCSVExport = () => {
    if (!processedDataset) return

    const { headers, dataset } = processedDataset
    const csvContent = convertToCSV(headers, dataset)

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')

    // Set up download link
    link.setAttribute('href', url)
    link.setAttribute(
      'download',
      `aco-performance_${new Date().toISOString().slice(0, 10)}.csv`
    )
    link.style.visibility = 'hidden'

    // Add to document, trigger download, and clean up
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Function to handle Excel export using the xlsx library with formatting
  const handleExcelExport = () => {
    if (!processedDataset) return

    // Create a workbook
    const workbook = XLSX.utils.book_new()

    // Process and add first dataset sheet if available
    if (analysisReportDataset && analysisReportDataset.length > 0) {
      // Process the first dataset
      const processedFirstDataset = analysisReportDataset.map((row) => {
        // Create a new object with only the headers we want
        const newRow: Record<string, any> = {}
        analysisReportHeader.forEach((header) => {
          newRow[header] = row[header] !== undefined ? row[header] : ''
        })
        return newRow
      })

      // Create first dataset worksheet
      const firstDatasetWorksheet = XLSX.utils.json_to_sheet(
        processedFirstDataset
      )

      // Apply styles to first dataset worksheet
      applyWorksheetStyles(firstDatasetWorksheet, analysisReportHeader)

      // Add first dataset worksheet to workbook
      XLSX.utils.book_append_sheet(
        workbook,
        firstDatasetWorksheet,
        'ACO Performance'
      )
    }

    // Process and add second dataset sheet if available
    if (
      analysisReportForHigherOrderDataset &&
      analysisReportForHigherOrderDataset.length > 0
    ) {
      // Process the second dataset
      const processedSecondDataset = analysisReportForHigherOrderDataset.map(
        (row) => {
          // Create a new object with only the headers we want
          const newRow: Record<string, any> = {}
          analysisReportForHigherOrderHeader.forEach((header) => {
            newRow[header] = row[header] !== undefined ? row[header] : ''
          })
          return newRow
        }
      )

      // Create second dataset worksheet
      const secondDatasetWorksheet = XLSX.utils.json_to_sheet(
        processedSecondDataset
      )

      // Apply styles to second dataset worksheet
      applyWorksheetStyles(
        secondDatasetWorksheet,
        analysisReportForHigherOrderHeader
      )

      // Add second dataset worksheet to workbook
      XLSX.utils.book_append_sheet(
        workbook,
        secondDatasetWorksheet,
        'Higher Order Measures'
      )
    }

    // Generate Excel file as an array buffer
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
      cellStyles: true, // Enable cell styles
    })

    // Convert to Blob
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })

    // Create download link
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute(
      'download',
      `aco-performance_${new Date().toISOString().slice(0, 10)}.xlsx`
    )
    link.style.visibility = 'hidden'

    // Add to document, trigger download, and clean up
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Helper function to apply styles to a worksheet
  const applyWorksheetStyles = (
    worksheet: XLSX.WorkSheet,
    headers: string[]
  ) => {
    // Set column widths
    const columnWidths = headers.map(() => ({ wch: 25 })) // Default width for all columns
    worksheet['!cols'] = columnWidths

    // Get the range of the worksheet
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')

    // Create styles for header row
    const headerStyle = {
      fill: { fgColor: { rgb: 'D9D9D9' } }, // Light gray background
      font: { bold: true, sz: 11, color: { rgb: '000000' } }, // Bold black text
      alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } },
      },
    }

    // Create styles for data rows (alternating colors)
    const evenRowStyle = {
      fill: { fgColor: { rgb: 'F2F2F2' } }, // Very light gray
      font: { sz: 11, color: { rgb: '000000' } },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } },
      },
    }

    const oddRowStyle = {
      fill: { fgColor: { rgb: 'FFFFFF' } }, // White
      font: { sz: 11, color: { rgb: '000000' } },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } },
      },
    }

    // Apply styles to cells
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: C })
      if (!worksheet[cellRef]) continue

      // Apply header style
      worksheet[cellRef].s = headerStyle
    }

    // Apply alternating row styles to data rows
    for (let R = 1; R <= range.e.r; ++R) {
      const rowStyle = R % 2 === 1 ? oddRowStyle : evenRowStyle

      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellRef = XLSX.utils.encode_cell({ r: R, c: C })
        if (!worksheet[cellRef]) continue

        // Apply row style
        worksheet[cellRef].s = rowStyle

        // Format numeric cells
        if (typeof worksheet[cellRef].v === 'number') {
          worksheet[cellRef].s = {
            ...rowStyle,
            alignment: { horizontal: 'right' },
          }
        }
      }
    }
  }

  // Create export handlers object
  const exportHandlers: ExportHandlers = {
    handleCSVExport,
    handleExcelExport,
    hasData: !!processedDataset,
  }

  return (
    <ReportActions
      reportType="aco-performance-analysis"
      reportConfig={reportConfig}
      onSaveComplete={onSaveComplete}
      exportHandlers={exportHandlers}
    />
  )
}

export default ACOPerformanceReportActions
