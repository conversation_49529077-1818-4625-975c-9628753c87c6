import React, { useMemo } from 'react'
import * as XLSX from 'xlsx'
import ReportActions, { ExportHandlers } from './ReportActions'
import { MVPReportConfig } from '../filters/MVPFilters'
import { usePathname } from 'next/navigation'
import { MVPSummary, QualityScoreByMVP } from '@/types/reports/mvpTypes'
import { useMVPStore } from '@/stores/mvp'

interface MVPReportActionsProps {
  reportConfig: MVPReportConfig
  summaryData?: MVPSummary[]
  chartData?: QualityScoreByMVP[]
}

const MVPReportActions: React.FC<MVPReportActionsProps> = ({
  reportConfig,
  summaryData,
  chartData,
}) => {
  const pathname = usePathname()
  const isMVPDetailsPage = pathname.includes('mvp-details')
  const { detailsData } = useMVPStore()

  // Process the datasets for export
  const processedDataset = useMemo(() => {
    if (isMVPDetailsPage) {
      // For MVPDetails page
      if (!detailsData) return undefined

      const hasQualityMeasures =
        detailsData.qualityMeasures && detailsData.qualityMeasures.length > 0
      const hasProviderPerformance =
        detailsData.providerPerformance &&
        detailsData.providerPerformance.length > 0
      const hasMeasureTypes =
        (detailsData && detailsData.qualityMeasures.length > 0) ||
        detailsData.iaMeasuresData.length > 0 ||
        detailsData.piMeasuresData.length > 0

      // If all datasets are empty, return undefined to disable the exporter
      if (
        !hasQualityMeasures &&
        !hasProviderPerformance &&
        !hasMeasureTypes &&
        !detailsData.overallScore &&
        !detailsData.qualityScore &&
        !detailsData.iaScore &&
        !detailsData.piScore &&
        !detailsData.costScore
      ) {
        return undefined
      }

      return { detailsData }
    } else {
      // Check if both datasets are empty
      const isSummaryDataEmpty = !summaryData || summaryData.length === 0
      const isChartDataEmpty = !chartData || chartData.length === 0
      // If both datasets are empty, return undefined to disable the exporter
      if (isSummaryDataEmpty && isChartDataEmpty) {
        return undefined
      }

      // Extract headers from summaryData, excluding the Max columns
      const summaryHeaders =
        summaryData && summaryData.length > 0
          ? Object.keys(summaryData[0]!).filter(
              (header) =>
                ![
                  'CostMax',
                  'ScoreMax',
                  'PIScoreMax',
                  'IAMax',
                  'QualityMax',
                ].includes(header)
            )
          : []

      // Extract headers from chartData
      const chartHeaders =
        chartData && chartData.length > 0 ? Object.keys(chartData[0]!) : []

      // Process summary data to exclude Max columns
      const processedSummaryData = summaryData
        ? summaryData.map((row: any) => {
            const newRow: Record<string, any> = {}
            summaryHeaders.forEach((header) => {
              newRow[header] = row[header]
            })
            return newRow
          })
        : []

      // Add a blank row between datasets
      const spacerRow: Record<
        string,
        string | number | boolean | null | undefined
      > = {}

      // Create a combined dataset with a blank row in between
      const combinedDataset = [
        ...processedSummaryData,
        spacerRow, // Add a blank row as a separator
        ...(chartData || []),
      ]

      // Combine all headers (this will include all unique headers from both datasets)
      const combinedHeaders = [...new Set([...summaryHeaders, ...chartHeaders])]

      return {
        headers: combinedHeaders,
        dataset: combinedDataset,
      }
    }
  }, [summaryData, chartData, detailsData, isMVPDetailsPage])

  // Function to convert data to CSV format
  const convertToCSV = (headers: string[], data: Record<string, any>[]) => {
    // Create header row
    const headerRow = headers.join(',')

    // Create data rows
    const dataRows = data
      .map((row) => {
        return headers
          .map((header) => {
            // Handle values that might contain commas or quotes
            const value = row[header] !== undefined ? row[header] : ''
            let valueStr = String(value)
            // Mitigate CSV injection by prefixing values starting with '=', '+', '-', or '@'
            if (/^[=+\-@]/.test(valueStr)) {
              valueStr = "'" + valueStr
            }

            // Escape quotes and wrap in quotes if needed
            if (
              valueStr.includes(',') ||
              valueStr.includes('"') ||
              valueStr.includes('\n')
            ) {
              return `"${valueStr.replace(/"/g, '""')}"`
            }
            return valueStr
          })
          .join(',')
      })
      .join('\n')

    // Combine header and data
    return `${headerRow}\n${dataRows}`
  }

  // Function to handle CSV export
  const handleCSVExport = () => {
    if (!processedDataset) return

    let csvContent = ''

    if (isMVPDetailsPage && processedDataset.detailsData) {
      const { detailsData } = processedDataset

      // Add Overall Score section
      csvContent += 'OVERALL SCORES\n'

      // Overall Score
      if (detailsData.overallScore) {
        csvContent += 'Overall Score\n'
        csvContent += convertToCSV(Object.keys(detailsData.overallScore), [
          detailsData.overallScore,
        ])
        csvContent += '\n'
      }

      // Quality Score
      if (detailsData.qualityScore) {
        csvContent += 'Quality Score\n'
        csvContent += convertToCSV(Object.keys(detailsData.qualityScore), [
          detailsData.qualityScore,
        ])
        csvContent += '\n'
      }

      // IA Score
      if (detailsData.iaScore) {
        csvContent += 'Improvement Activities Score\n'
        csvContent += convertToCSV(Object.keys(detailsData.iaScore), [
          detailsData.iaScore,
        ])
        csvContent += '\n'
      }

      // PI Score
      if (detailsData.piScore) {
        csvContent += 'Promoting Interoperability Score\n'
        csvContent += convertToCSV(Object.keys(detailsData.piScore), [
          detailsData.piScore,
        ])
        csvContent += '\n'
      }

      // Cost Score
      if (detailsData.costScore) {
        csvContent += 'Cost Score\n'
        csvContent += convertToCSV(Object.keys(detailsData.costScore), [
          detailsData.costScore,
        ])
        csvContent += '\n\n'
      }

      // Add Quality Measures Chart section if available
      if (
        detailsData.qualityMeasures &&
        detailsData.qualityMeasures.length > 0
      ) {
        csvContent += 'QUALITY MEASURES CHART\n'
        csvContent += convertToCSV(
          Object.keys(detailsData.qualityMeasures[0]!),
          detailsData.qualityMeasures
        )
        csvContent += '\n\n'
      }

      // Add Provider Performance section if available
      if (
        detailsData.providerPerformance &&
        detailsData.providerPerformance.length > 0
      ) {
        csvContent += 'PROVIDER PERFORMANCE\n'
        csvContent += convertToCSV(
          Object.keys(detailsData.providerPerformance[0]!),
          detailsData.providerPerformance
        )
        csvContent += '\n\n'
      }

      // Add Quality Measures Table section if available
      if (
        detailsData.qualityMeasuresData &&
        detailsData.qualityMeasuresData.length > 0
      ) {
        csvContent += 'QUALITY MEASURES\n'

        const qualityMeasuresExportFormat = detailsData.qualityMeasuresData.map(
          (row: any) => {
            const { Rank, PTile, ...rest } = row
            return {
              ...rest,
              Included: row.Rank <= 4 ? 'Yes' : 'No',
              Decile: PTile,
            }
          }
        )

        csvContent += convertToCSV(
          Object.keys(qualityMeasuresExportFormat[0]!),
          qualityMeasuresExportFormat
        )
        csvContent += '\n\n'
      }

      // Add IA Measures section if available
      if (detailsData.iaMeasuresData && detailsData.iaMeasuresData.length > 0) {
        csvContent += 'IMPROVEMENT ACTIVITIES\n'
        csvContent += convertToCSV(
          Object.keys(detailsData.iaMeasuresData[0]!),
          detailsData.iaMeasuresData
        )
        csvContent += '\n\n'
      }

      // Add PI Measures section if available
      if (detailsData.piMeasuresData && detailsData.piMeasuresData.length > 0) {
        csvContent += 'PROMOTING INTEROPERABILITY\n'
        csvContent += convertToCSV(
          Object.keys(detailsData.piMeasuresData[0]!),
          detailsData.piMeasuresData
        )
      }
    } else {
      // For MVPSummary page
      // Process summary data if available
      if (summaryData && summaryData.length > 0) {
        // Filter out the Max columns
        const filteredSummaryHeaders = Object.keys(summaryData[0]!).filter(
          (header) =>
            ![
              'CostMax',
              'ScoreMax',
              'PIScoreMax',
              'IAMax',
              'QualityMax',
            ].includes(header)
        )

        // Process the summary data
        const processedSummary = summaryData.map((row: any) => {
          const newRow: Record<string, any> = {}
          filteredSummaryHeaders.forEach((header) => {
            newRow[header] = row[header] !== undefined ? row[header] : ''
          })
          return newRow
        })

        csvContent += 'MVP SUMMARY\n'
        csvContent += convertToCSV(filteredSummaryHeaders, processedSummary)

        // Add space between datasets if chart data is available
        if (chartData && chartData.length > 0) {
          csvContent += '\n\n'
        }
      }

      // Process chart data if available
      if (chartData && chartData.length > 0) {
        const chartHeaders = Object.keys(chartData[0]!)

        csvContent += 'QUALITY SCORE CHART\n'
        csvContent += convertToCSV(chartHeaders, chartData)
      }
    }

    // Create and download the CSV file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')

    // Set up download link
    link.setAttribute('href', url)
    link.setAttribute(
      'download',
      `mvp-${isMVPDetailsPage ? 'details' : 'summary'}_${new Date().toISOString().slice(0, 10)}.csv`
    )
    link.style.visibility = 'hidden'

    // Add to document, trigger download, and clean up
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Function to handle Excel export using the xlsx library with formatting
  const handleExcelExport = () => {
    if (!processedDataset) return

    // Create a workbook
    const workbook = XLSX.utils.book_new()

    if (isMVPDetailsPage && processedDataset.detailsData) {
      const { detailsData } = processedDataset

      // Create a scores worksheet with all score types
      const scoresData = []

      // Add Overall Score if available
      if (detailsData.overallScore) {
        scoresData.push({
          ScoreType: 'Overall Score',
          Score: detailsData.overallScore.Score,
          Min: detailsData.overallScore.Min || '',
          Max: detailsData.overallScore.Max || '',
        })
      }

      // Add Quality Score if available
      if (detailsData.qualityScore) {
        scoresData.push({
          ScoreType: 'Quality Score',
          Score: detailsData.qualityScore.Score,
          Min: detailsData.qualityScore.Min || '',
          Max: detailsData.qualityScore.Max || '',
        })
      }

      // Add IA Score if available
      if (detailsData.iaScore) {
        scoresData.push({
          ScoreType: 'Improvement Activities Score',
          Score: detailsData.iaScore.Score,
          Min: detailsData.iaScore.Min || '',
          Max: detailsData.iaScore.Max || '',
        })
      }

      // Add PI Score if available
      if (detailsData.piScore) {
        scoresData.push({
          ScoreType: 'Promoting Interoperability Score',
          Score: detailsData.piScore.Score,
          Min: detailsData.piScore.Min || '',
          Max: detailsData.piScore.Max || '',
        })
      }

      // Add Cost Score if available
      if (detailsData.costScore) {
        scoresData.push({
          ScoreType: 'Cost Score',
          Score: detailsData.costScore.Score,
          Min: detailsData.costScore.Min || '',
          Max: detailsData.costScore.Max || '',
        })
      }

      // Add Scores worksheet if we have any score data
      if (scoresData.length > 0) {
        const scoresWorksheet = XLSX.utils.json_to_sheet(scoresData)
        applyWorksheetStyles(scoresWorksheet, Object.keys(scoresData[0]!))
        XLSX.utils.book_append_sheet(workbook, scoresWorksheet, 'Scores')
      }

      // Add Quality Measures Chart sheet if available
      if (
        detailsData.qualityMeasures &&
        detailsData.qualityMeasures.length > 0
      ) {
        const qualityMeasuresWorksheet = XLSX.utils.json_to_sheet(
          detailsData.qualityMeasures
        )
        applyWorksheetStyles(
          qualityMeasuresWorksheet,
          Object.keys(detailsData.qualityMeasures[0]!)
        )
        XLSX.utils.book_append_sheet(
          workbook,
          qualityMeasuresWorksheet,
          'Quality Score Over Time'
        )
      }

      // Add Provider Performance sheet if available
      if (
        detailsData.providerPerformance &&
        detailsData.providerPerformance.length > 0
      ) {
        const providerPerformanceWorksheet = XLSX.utils.json_to_sheet(
          detailsData.providerPerformance
        )
        applyWorksheetStyles(
          providerPerformanceWorksheet,
          Object.keys(detailsData.providerPerformance[0]!)
        )
        XLSX.utils.book_append_sheet(
          workbook,
          providerPerformanceWorksheet,
          'Provider Performance'
        )
      }

      // Add Quality Measures Table sheet if available
      if (
        detailsData.qualityMeasuresData &&
        detailsData.qualityMeasuresData.length > 0
      ) {
        const qualityMeasuresTableWorksheet = XLSX.utils.json_to_sheet(
          detailsData.qualityMeasuresData.map((row: any) => {
            const { Rank, PTile, ...rest } = row
            return {
              ...rest,
              Included: row.Rank <= 4 ? 'Yes' : 'No',
              Decile: PTile,
            }
          })
        )
        applyWorksheetStyles(
          qualityMeasuresTableWorksheet,
          Object.keys(detailsData.qualityMeasuresData[0]!)
        )
        XLSX.utils.book_append_sheet(
          workbook,
          qualityMeasuresTableWorksheet,
          'Quality Measures'
        )
      }

      // Add IA Measures sheet if available
      if (detailsData.iaMeasuresData && detailsData.iaMeasuresData.length > 0) {
        const iaMeasuresWorksheet = XLSX.utils.json_to_sheet(
          detailsData.iaMeasuresData
        )
        applyWorksheetStyles(
          iaMeasuresWorksheet,
          Object.keys(detailsData.iaMeasuresData[0]!)
        )
        XLSX.utils.book_append_sheet(
          workbook,
          iaMeasuresWorksheet,
          'Improvement Activities'
        )
      }

      // Add PI Measures sheet if available
      if (detailsData.piMeasuresData && detailsData.piMeasuresData.length > 0) {
        const piMeasuresWorksheet = XLSX.utils.json_to_sheet(
          detailsData.piMeasuresData
        )
        applyWorksheetStyles(
          piMeasuresWorksheet,
          Object.keys(detailsData.piMeasuresData[0]!)
        )
        XLSX.utils.book_append_sheet(
          workbook,
          piMeasuresWorksheet,
          'Promoting Interoperability'
        )
      }
    } else {
      // Process and add summary data sheet if available
      if (summaryData && summaryData.length > 0) {
        // Filter out the Max columns
        const filteredSummaryHeaders = Object.keys(summaryData[0]!).filter(
          (header) =>
            ![
              'CostMax',
              'ScoreMax',
              'PIScoreMax',
              'IAMax',
              'QualityMax',
            ].includes(header)
        )

        // Process the summary data
        const processedSummary = summaryData.map((row: any) => {
          const newRow: Record<string, any> = {}
          filteredSummaryHeaders.forEach((header) => {
            newRow[header] = row[header] !== undefined ? row[header] : ''
          })
          return newRow
        })

        // Create summary worksheet
        const summaryWorksheet = XLSX.utils.json_to_sheet(processedSummary)

        // Apply styles to summary worksheet
        applyWorksheetStyles(summaryWorksheet, filteredSummaryHeaders)

        // Add summary worksheet to workbook
        XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'MVP Summary')
      }

      // Process and add chart data sheet if available
      if (chartData && chartData.length > 0) {
        const chartHeaders = Object.keys(chartData[0]!)

        // Process the chart data
        const processedChart = chartData.map((row: any) => {
          const newRow: Record<string, any> = {}
          chartHeaders.forEach((header) => {
            newRow[header] = row[header] !== undefined ? row[header] : ''
          })
          return newRow
        })

        // Create chart worksheet
        const chartWorksheet = XLSX.utils.json_to_sheet(processedChart)

        // Apply styles to chart worksheet
        applyWorksheetStyles(chartWorksheet, chartHeaders)

        // Add chart worksheet to workbook
        XLSX.utils.book_append_sheet(
          workbook,
          chartWorksheet,
          'Quality Score Chart'
        )
      }
    }

    // Generate Excel file as an array buffer
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
      cellStyles: true, // Enable cell styles
    })

    // Convert to Blob
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })

    // Create download link
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute(
      'download',
      `mvp-${isMVPDetailsPage ? 'details' : 'summary'}_${new Date().toISOString().slice(0, 10)}.xlsx`
    )
    link.style.visibility = 'hidden'

    // Add to document, trigger download, and clean up
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Helper function to apply styles to a worksheet
  const applyWorksheetStyles = (
    worksheet: XLSX.WorkSheet,
    headers: string[]
  ) => {
    // Set column widths
    const columnWidths = headers.map(() => ({ wch: 25 })) // Default width for all columns
    worksheet['!cols'] = columnWidths

    // Get the range of the worksheet
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')

    // Create styles for header row
    const headerStyle = {
      fill: { fgColor: { rgb: 'D9D9D9' } }, // Light gray background
      font: { bold: true, sz: 11, color: { rgb: '000000' } }, // Bold black text
      alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } },
      },
    }

    // Create styles for data rows (alternating colors)
    const evenRowStyle = {
      fill: { fgColor: { rgb: 'F2F2F2' } }, // Very light gray
      font: { sz: 11, color: { rgb: '000000' } },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } },
      },
    }

    const oddRowStyle = {
      fill: { fgColor: { rgb: 'FFFFFF' } }, // White
      font: { sz: 11, color: { rgb: '000000' } },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } },
      },
    }

    // Apply styles to cells
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: C })
      if (!worksheet[cellRef]) continue

      // Apply header style
      worksheet[cellRef].s = headerStyle
    }

    // Apply alternating row styles to data rows
    for (let R = 1; R <= range.e.r; ++R) {
      const rowStyle = R % 2 === 1 ? oddRowStyle : evenRowStyle

      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellRef = XLSX.utils.encode_cell({ r: R, c: C })
        if (!worksheet[cellRef]) continue

        // Apply row style
        worksheet[cellRef].s = rowStyle

        // Format numeric cells
        if (typeof worksheet[cellRef].v === 'number') {
          worksheet[cellRef].s = {
            ...rowStyle,
            alignment: { horizontal: 'right' },
          }
        }
      }
    }
  }

  // Create export handlers object
  const exportHandlers: ExportHandlers = {
    handleCSVExport,
    handleExcelExport,
    hasData: !!processedDataset,
  }

  return (
    <ReportActions
      reportType={isMVPDetailsPage ? 'mvp-details' : 'mvp'}
      reportConfig={reportConfig}
      exportHandlers={exportHandlers}
    />
  )
}

export default MVPReportActions
