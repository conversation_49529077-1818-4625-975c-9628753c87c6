import React, { useState, useRef } from 'react'
import Image from 'next/image'
import chevronDown from '../../../../public/images/chevronDown.svg'
import download from '../../../../public/images/download.svg'
import { useClickOutside } from '@/hooks/useClickOutside'
import FilterSection from '@/components/filter/common/FilterSection'
import { api } from '@/trpc/react'
import { useSession } from 'next-auth/react'
import { useToast } from '@/hooks/use-toast'
import { BaseReportConfig } from '../common/ConfigureReport'
import { ChevronDown, ChevronUp, Loader2 } from 'lucide-react'
import { User } from '@/types/user'
import { ChipSection } from '@/components/filter/sections/chips/chipSection'
import { cn } from '@/lib/utils'

// Define an interface for export handlers
export interface ExportHandlers {
  handleCSVExport: () => void
  handleExcelExport: () => void
  hasData: boolean // Flag to determine if export buttons should be enabled
}

interface ReportActionsProps<T extends BaseReportConfig> {
  reportType: string
  reportConfig: T
  onSaveComplete?: () => void
  exportHandlers: ExportHandlers // Pass export handlers as props
}

function ReportActions<T extends BaseReportConfig>({
  reportType,
  reportConfig,
  onSaveComplete,
  exportHandlers,
}: ReportActionsProps<T>) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [shareMessage, setShareMessage] = useState('')
  const [expandedSections, setExpandedSections] = useState<string>('EXPORT')
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel'>('excel')
  const [customReportName, setCustomReportName] = useState(
    reportConfig.name ? `Copy Of ${reportConfig.name}` : ''
  )
  const [reportDescription, setReportDescription] = useState(
    reportConfig.description || ''
  )
  const [selectedUsers, setSelectedUsers] = useState<
    { id: string; label: string }[]
  >([])
  const [isUsersExpanded, setIsUsersExpanded] = useState(false)
  const [userSearchTerm, setUserSearchTerm] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)

  const { data: session } = useSession()
  const { toast } = useToast()
  const trpcUtils = api.useUtils()

  const users = api.users.getOrganizationUsers.useQuery()

  const saveReportMutation = api.report.saveReport.useMutation({
    onSuccess: () => {
      toast({
        title: 'Report saved',
        description: 'Your report has been saved successfully.',
      })
      setIsExpanded(false)
      if (onSaveComplete) onSaveComplete()
      trpcUtils.report.getUserReports.invalidate()
    },
    onError: (error) => {
      toast({
        title: 'Error saving report',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  const shareReportMutation = api.report.shareReport.useMutation({
    onSuccess: () => {
      toast({
        title: 'Report shared successfully!',
        description: 'Your report has been shared successfully.',
      })
      setIsExpanded(false)
      setSelectedUsers([])
    },
    onError: (error) => {
      toast({
        title: 'Error sharing report',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  useClickOutside(dropdownRef, () => {
    setIsExpanded(false)
  })

  const toggleSection = (section: string) => {
    setExpandedSections(expandedSections === section ? '' : section)
  }

  const handleSaveReport = () => {
    if (!customReportName) {
      toast({
        title: 'Report name required',
        description: 'Please enter a name for your report',
        variant: 'destructive',
      })
      return
    }

    // Serialize the report configuration to JSON
    const reportConfigJson = JSON.stringify({
      ...reportConfig,
      timestamp: Date.now(),
    })

    saveReportMutation.mutate({
      name: customReportName,
      description: reportDescription,
      reportConfig: reportConfigJson,
      userId: session?.uid || '',
      reportType: reportType,
    })
  }

  const handleUserSelect = (user: User) => {
    if (selectedUsers.find((e) => e.id === user.userId)) {
      setIsUsersExpanded(false)
      return
    }

    setSelectedUsers([
      ...selectedUsers,
      { id: user.userId!, label: user.displayName! },
    ])
    setIsUsersExpanded(false)
  }

  const removeUser = (Id: string) => {
    setSelectedUsers(selectedUsers.filter((x) => x.id != Id))
  }

  const handleShareView = () => {
    if (selectedUsers.length == 0) {
      toast({
        title: 'Select at least 1 user',
        variant: 'default',
      })
      return
    }

    shareReportMutation.mutate(
      {
        reportId: reportConfig.reportId!,
        reportType: reportType,
        users: selectedUsers.map((x) => ({
          id: x.id,
          email: users.data?.find((user) => user.userId === x.id)
            ?.emailAddress!,
          name: users.data?.find((user) => user.userId === x.id)?.firstName!,
        })),
        message: shareMessage,
      },
      {
        onSuccess: () => {
          setSelectedUsers([])
          toast({
            className: cn('bg-green-600 text-white border-green-600'),
            title: 'Report shared successfully!',
            variant: 'default',
          })
        },
        onError: () => {
          toast({
            className: cn('bg-red-600 text-white border-red-600'),
            title: 'Error sharing report',
            variant: 'default',
          })
        },
      }
    )
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => exportHandlers.hasData && setIsExpanded(!isExpanded)}
        disabled={!exportHandlers.hasData}
        className={`rounded-[5px] flex items-center space-x-2 px-[15px] py-2 font-semibold focus:outline-none focus:ring-0 
                  border bg-ui-pale-blue text-ui-dark-gray border-ui-pale-blue 
                  ${!isExpanded && 'hover:bg-white hover:border-ui-dark-gray hover:rounded-[5px]'}`}
      >
        <span
          className={`font-open-sans font-weight-600 text-[13px] ${exportHandlers.hasData ? 'text-ui-dark-gray' : 'text-gray-400'}`}
        >
          ACTIONS
        </span>
        <Image
          src={chevronDown}
          alt="chevronDown"
          height={14}
          width={14}
          className={`${isExpanded ? 'rotate-180' : ''} ${!exportHandlers.hasData ? 'opacity-50' : ''}`}
        />
      </button>

      {isExpanded && (
        <div className="absolute p-4 top-full z-20 bg-ui-pale-blue shadow-lg rounded -left-[135px] font-sans">
          <div className="space-y-3">
            <FilterSection
              title="EXPORT"
              expanded={expandedSections === 'EXPORT'}
              onToggle={() => toggleSection('EXPORT')}
            >
              <div className="space-y-3">
                <div className="space-y-2">
                  <label className="flex items-center space-x-2 py-1">
                    <input
                      type="radio"
                      name="exportFormat"
                      checked={exportFormat === 'csv'}
                      onChange={() => setExportFormat('csv')}
                      className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
                    />
                    <span className="text-[12px]">Export as CSV</span>
                  </label>
                  <label className="flex items-center space-x-2 py-1">
                    <input
                      type="radio"
                      name="exportFormat"
                      checked={exportFormat === 'excel'}
                      onChange={() => setExportFormat('excel')}
                      className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
                    />
                    <span className="text-[12px]">Export as Excel</span>
                  </label>
                </div>
                <button
                  className="w-full text-[#1B4A70] font-semibold border-ui-dark-gray border-[0.5px] rounded p-2 flex justify-center items-center text-[12px] outline-none hover:bg-white"
                  onClick={
                    exportFormat === 'csv'
                      ? exportHandlers.handleCSVExport
                      : exportHandlers.handleExcelExport
                  }
                >
                  <Image
                    src={download}
                    alt="download"
                    height={14}
                    width={14}
                    className="mr-2"
                  />
                  <span>EXPORT</span>
                </button>
              </div>
            </FilterSection>

            <FilterSection
              title="SAVE REPORT"
              expanded={expandedSections === 'SAVE'}
              onToggle={() => toggleSection('SAVE')}
            >
              <div className="space-y-3">
                <input
                  type="text"
                  placeholder="Report Name"
                  value={customReportName}
                  onChange={(e) => setCustomReportName(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded text-xs"
                />
                <textarea
                  placeholder="Description (optional)"
                  value={reportDescription}
                  onChange={(e) => setReportDescription(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded text-xs h-20"
                />
                <button
                  className="w-full bg-[#2B5380] text-white py-2 rounded text-xs font-semibold"
                  onClick={handleSaveReport}
                >
                  SAVE REPORT
                </button>
              </div>
            </FilterSection>

            {reportConfig.reportId && (
              <FilterSection
                title="SHARE"
                expanded={expandedSections === 'SHARE'}
                onToggle={() => toggleSection('SHARE')}
              >
                <div className="space-y-3">
                  <span className="px-1 mb-2 font-open-sans font-weight-400 text-[10px]">
                    User*
                  </span>
                  <div className="w-full">
                    <div
                      onClick={() => setIsUsersExpanded(!isUsersExpanded)}
                      className="w-full border-[0.5px] border-[solid] border-[#97A4BA] rounded-[4px] flex justify-between items-center px-2 py-1 text-ui-dark-gray"
                    >
                      <input
                        className="flex font-open-sans font-weight-400 text-[10px] text-[#787878] bg-transparent outline-none w-full"
                        placeholder="Select User"
                        value={userSearchTerm}
                        onChange={(e) => setUserSearchTerm(e.target.value)}
                        onClick={(e) => {
                          e.stopPropagation()
                          setIsUsersExpanded(true)
                        }}
                      />
                      {isUsersExpanded ? (
                        <ChevronUp
                          className="w-5 h-5 font-semibold"
                          strokeWidth={2}
                        />
                      ) : (
                        <ChevronDown
                          className="w-5 h-5 font-semibold"
                          strokeWidth={2}
                        />
                      )}
                    </div>
                  </div>
                  {isUsersExpanded && (
                    <div className="w-full flex flex-col px-2 pt-2 h-[150px] overflow-y-auto">
                      {users.data
                        ?.filter((user) =>
                          user.displayName
                            ?.toLowerCase()
                            .includes(userSearchTerm.toLowerCase())
                        )
                        ?.map((user: User) => (
                          <span
                            onClick={() => {
                              handleUserSelect(user)
                              setUserSearchTerm('')
                            }}
                            key={user.userId}
                            className="font-weight-400 text-[10px] py-1 hover:cursor-pointer"
                          >
                            {user.displayName}
                          </span>
                        ))}
                    </div>
                  )}
                  <span className="px-1 mb-2 mt-2 font-open-sans font-weight-400 text-[10px]">
                    Message*
                  </span>
                  <textarea
                    className="w-full p-2 h-[50px] font-weight-400 text-[10px] border-[0.5px] border-[solid] border-[#97A4BA] rounded-[4px] outline-none"
                    value={shareMessage}
                    onChange={(e) => setShareMessage(e.target.value)}
                  ></textarea>
                  <button
                    disabled={shareReportMutation.isPending}
                    className="w-[77px] font-semibold text-[10px] text-ui-dark-gray py-[4px] mt-4 border-[0.8px] border-[solid] border-ui-dark-gray rounded-[4px]"
                    onClick={() => {
                      handleShareView()
                    }}
                  >
                    {shareReportMutation.isPending ? (
                      <Loader2 size={13} className="animate-spin" />
                    ) : (
                      'SEND'
                    )}
                  </button>
                  <span className="px-1 mt-2 font-open-sans font-weight-400 text-[10px] text-[#13497C]">
                    Sharing With:
                  </span>
                  <div className="w-full mb-2">
                    <ChipSection
                      title=""
                      allSelected={false}
                      items={selectedUsers}
                      onRemove={removeUser}
                      noSelectionMessage="No Users Selected"
                    />
                  </div>
                </div>
              </FilterSection>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default ReportActions
