'use client'
import React, { useEffect, useState } from 'react'
import { Star as StarIcon, X as XIcon } from 'lucide-react'
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
} from '@tanstack/react-table'
import { api } from '@/trpc/react'
import Loader from '@/components/ui/Loader'
import ReportsDataTable from '@/components/reports/ReportsDataTable'
import { Report } from '@/types/reports/medisolvReport'
import { customStringFilter } from '@/lib/customStringFilter'
import Link from 'next/link'
import { Trash2 } from 'lucide-react'
import { SortingState } from '@tanstack/react-table'
import { useToast } from '@/hooks/use-toast'
import { reportToast } from '@/components/ui/ReportToast'

import { SortOption } from '@/app/(platform)/reports/page'
import { useMVPStore } from '@/stores/mvp'
import { cn } from '@/lib/utils'

interface ReportsTableProps {
  searchQuery?: string
  sortOption?: SortOption
}

const linksToReports: any = {
  'aco-performance-analysis': '/reports/aco-performance-analysis',
  mvp: '/reports/mvp',
  'mvp-details': '/reports/mvp/mvp-details',
}

export const ReportsTable = ({
  searchQuery = '',
  sortOption,
}: ReportsTableProps) => {
  const { mvpConfig, setMVPConfig, setConfigureReportOpen } = useMVPStore()
  const { data: session } = api.auth.getSession.useQuery()

  const [reports, setReports] = useState<Report[]>([])
  const [favorites, setFavorites] = useState<Record<string, boolean>>({})
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<string | null>(null)
  const [sorting, setSorting] = useState<SortingState>([])
  const { toast } = useToast()

  const reportsQuery = api.report.getUserReports.useQuery()

  // Mutations for report operations
  const deleteReportMutation = api.report.deleteReport.useMutation({
    onSuccess: () => {
      reportsQuery.refetch()
    },
  })

  const addReportMutation = api.report.saveReport.useMutation({
    onSuccess: () => {
      reportsQuery.refetch()
    },
  })

  const updateFavoriteMutation = api.report.updateFavoriteReport.useMutation()

  useEffect(() => {
    if (mvpConfig.year !== 0) {
      setMVPConfig({ year: 0, submissionGroup: '', mvp: '' })
      setConfigureReportOpen(false)
    }
  }, [mvpConfig])

  // Update sorting when sortOption changes
  useEffect(() => {
    if (sortOption) {
      // Only set table sorting state for actual columns
      if (sortOption.sortBy !== 'isFavorite') {
        setSorting([
          {
            id: sortOption.sortBy,
            desc: sortOption.sortOrder === 'desc',
          },
        ])
      } else {
        // For favorites sorting, clear the table sorting state
        // as we'll handle it in our custom sort function
        setSorting([])
      }
    }
  }, [sortOption])

  useEffect(() => {
    if (reportsQuery.data && session?.uid) {
      // Filter to only show active reports and reports the user has access to
      const activeReports = reportsQuery.data.filter((report) => {
        // Always include active non-custom reports
        if (report.isActive && !report.isCustom) {
          return true
        }

        // For custom reports, only include if they're active and belong to the current user
        if (report.isActive && report.isCustom) {
          return report.userId === session.uid
        }

        return false
      })

      // Filter reports based on search query if provided
      const filteredReports = searchQuery
        ? activeReports.filter((report) =>
          report.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
        : activeReports

      setReports(filteredReports)

      // Initialize all reports as favorited for the initial state
      const initialFavorites: Record<string, boolean> = {}
      activeReports.forEach((report) => {
        initialFavorites[report.rowKey] = report.isFavorite || false
      })
      setFavorites(initialFavorites)
    }
  }, [reportsQuery.data, searchQuery, session?.uid])

  // Apply sorting to reports - memoize to prevent recalculation on every render
  const sortedReports = React.useMemo(() => {
    if (!sortOption || !reports.length) return reports

    return [...reports].sort((a, b) => {
      if (sortOption.sortBy === 'name') {
        return sortOption.sortOrder === 'asc'
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name)
      }

      if (sortOption.sortBy === 'isFavorite') {
        return sortOption.sortOrder === 'desc'
          ? (favorites[b.rowKey] ? 1 : 0) - (favorites[a.rowKey] ? 1 : 0)
          : (favorites[a.rowKey] ? 1 : 0) - (favorites[b.rowKey] ? 1 : 0)
      }

      if (
        sortOption.sortBy === 'lastModified' &&
        a.lastModified &&
        b.lastModified
      ) {
        const dateA = new Date(a.lastModified)
        const dateB = new Date(b.lastModified)
        return sortOption.sortOrder === 'asc'
          ? dateA.getTime() - dateB.getTime()
          : dateB.getTime() - dateA.getTime()
      }

      if (sortOption.sortBy === 'type') {
        const typeA = a.isCustom ? 'Custom' : 'Default'
        const typeB = b.isCustom ? 'Custom' : 'Default'
        return sortOption.sortOrder === 'asc'
          ? typeA.localeCompare(typeB)
          : typeB.localeCompare(typeA)
      }

      // Default fallback sorting
      return sortOption.sortOrder === 'asc' ? 1 : -1
    })
  }, [reports, sortOption, favorites])

  const handleFavorite = (reportId: string) => {
    updateFavoriteMutation.mutate({
      reportId,
      isFavorite: !favorites[reportId],
    })

    setFavorites((prev) => ({
      ...prev,
      [reportId]: !prev[reportId],
    }))
  }

  const openDeleteDialog = (reportId: string) => {
    setDeleteDialogOpen(reportId)
  }

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(null)
  }

  const handleDelete = (reportId: string) => {
    const report = reports.find((report) => report.rowKey === reportId)

    if (!report) {
      toast({
        title: 'Error',
        description: 'Report not found',
        variant: 'destructive',
      })
      return
    }

    // Check if user has permission to delete this report
    if (report.isCustom && report.userId && report.userId !== session?.uid) {
      toast({
        title: 'Permission Denied',
        description: "You don't have permission to delete this report",
        variant: 'destructive',
      })
      return
    }

    const reportName = report.name || 'Report'

    // Call the delete mutation
    deleteReportMutation.mutate({ reportId })
    setReports((prev) => prev.filter((report) => report.rowKey !== reportId))

    // Close the dialog
    closeDeleteDialog()

    reportToast({
      reportName,
      action: 'deleted',
      undoAction: () => {
        // Implement undo functionality by restoring the report
        if (report) {
          addReportMutation.mutate({
            name: report.name,
            description: report.description,
            reportConfig: report.reportConfig,
            userId: report.userId,
            reportType: report.reportType,
          })

          // Show toast notification for the undo action
          reportToast({
            reportName,
            action: 'restored',
          })
        }
      },
    })
  }

  const filterFunction = customStringFilter<Report>()

  const columns: ColumnDef<Report>[] = React.useMemo(
    () => [
      // Add a hidden accessor for favorite status to support sorting
      {
        id: 'isFavorite',
        accessorFn: (row) => (favorites[row.rowKey] ? 1 : 0),
        enableSorting: true,
        enableHiding: true,
        size: 0,
        minSize: 0,
        maxSize: 0,
        header: () => null,
        cell: () => null,
      },
      {
        accessorKey: 'name',
        header: 'NAME',
        filterFn: filterFunction,
        enableSorting: true,
        maxSize: 300, // Set max width to 300px
        cell: (info) => {
          const reportId = info.row.original.rowKey
          const isFavorite = favorites[reportId]

          const link = info.row.original.isCustom
            ? `${linksToReports[info.row.original.reportType!]}?reportId=${reportId}`
            : linksToReports[info.row.original.reportType!]

          return (
            <div className="flex items-center gap-2 text-left">
              <button
                onClick={() => handleFavorite(reportId)}
                className="focus:outline-none self-start"
              >
                <StarIcon
                  className={`h-5 w-5 text-[#566582] ${isFavorite ? 'fill-[#566582]' : 'fill-none'}`}
                />
              </button>
              <Link
                href={link}
                className="text-[#205782] underline hover:text-[#2D7CBA]"
                prefetch={true}
              >
                {String(info.getValue())}
              </Link>
            </div>
          )
        },
      },
      {
        id: 'type',
        header: 'TYPE',
        enableColumnFilter: false,
        enableSorting: true,
        maxSize: 100, // Set max width to 100px
        cell: (info) => {
          const isCustom = info.row.original.isCustom
          const isShared = info.row.original.sharedBy
          return (
            <div className="text-left">
              <span
                className={cn(
                  'px-3 py-1 bg-blue-100 text-[#566582] rounded-full text-xs font-bold',
                  {
                    'bg-[#FFD3A6] text-[#A95A09]': isCustom && !isShared,
                    'bg-[#DEEEDE] text-[#496049]': isShared,
                  }
                )}
              >
                {isCustom ? (isShared ? 'SHARED' : 'CUSTOM') : 'DEFAULT'}
              </span>
            </div>
          )
        },
      },
      {
        id: 'description',
        header: 'DESCRIPTION',
        enableColumnFilter: false,
        enableSorting: false,
        cell: (info) => (
          <div className="text-left">
            {info.row.original.description || 'No description available'}
          </div>
        ),
      },
      {
        id: 'lastModified',
        header: 'LAST MODIFIED',
        enableColumnFilter: false,
        enableSorting: true,
        maxSize: 150, // Set max width to 150px
        cell: (info) => {
          const lastModified = info.row.original.lastModified
          const formattedDate = lastModified
            ? new Date(lastModified).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            })
            : 'N/A'

          return <div className="text-left">{formattedDate}</div>
        },
      },
      {
        id: 'actions',
        header: '',
        enableColumnFilter: false,
        enableSorting: false,
        maxSize: 50, // Set max width to 50px
        cell: (info) => {
          const report = info.row.original
          const reportId = report.rowKey

          // Only show delete button if the report is custom and belongs to the current user
          const canDelete =
            report.isCustom &&
            (!report.userId || report.userId === session?.uid)

          return (
            <div className="flex justify-end relative">
              {canDelete && (
                <button
                  className="p-1"
                  onClick={(e) => {
                    e.stopPropagation()
                    openDeleteDialog(reportId)
                  }}
                >
                  <Trash2 className="h-5 w-5 text-[#97A4BA] hover:text-[#173E5D]" />
                </button>
              )}
            </div>
          )
        },
      },
    ],
    [favorites, handleFavorite, deleteDialogOpen, filterFunction, session?.uid]
  )

  // Memoize the table options to prevent unnecessary re-renders
  const tableOptions = React.useMemo(
    () => ({
      data: sortedReports,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getSortedRowModel: getSortedRowModel(),
      state: {
        sorting,
      },
      onSortingChange: setSorting,
      initialState: {
        pagination: {
          pageSize: 10,
        },
      },
      enableSorting: true,
    }),
    [sortedReports, columns, sorting, session?.uid]
  )

  // Create the table instance at the top level
  const table = useReactTable(tableOptions)

  return (
    <div>
      {reportsQuery.isPending ? (
        <div className="overflow-x-auto relative mt-10">
          <Loader />
        </div>
      ) : reportsQuery.data?.filter((report) => report.isActive).length ===
        0 ? (
        <div className="text-center py-8 text-gray-500">
          No active reports available. Please contact your administrator to
          activate reports.
        </div>
      ) : reports.length === 0 && searchQuery ? (
        <div className="text-center py-8 text-gray-500">
          No reports match your search criteria.
        </div>
      ) : (
        <div>
          <ReportsDataTable
            table={table}
            deleteDialogOpen={deleteDialogOpen}
            onCancelDelete={closeDeleteDialog}
            onConfirmDelete={handleDelete}
          />
        </div>
      )}
    </div>
  )
}
