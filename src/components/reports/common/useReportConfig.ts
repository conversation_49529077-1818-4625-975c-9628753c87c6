import { useState, useEffect } from 'react'
import { api } from '@/trpc/react'
import { BaseReportConfig } from './ConfigureReport'
import { getReportDefinition } from './reportRegistry'

/**
 * Hook to load report configuration, either default or from a saved report
 */
export function useReportConfig<T extends BaseReportConfig>(
  reportType: string,
  reportId?: string | null
) {
  // Get report definition
  const reportDef = getReportDefinition<T>(reportType)

  const [config, setConfig] = useState<T | null>(
    reportDef?.defaultConfig || null
  )
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Query for saved report if reportId is provided
  const {
    data: reportData,
    isLoading: isLoadingReport,
    error: reportError,
  } = api.report.getReportById.useQuery(
    { id: reportId || '' },
    {
      enabled: !!reportId,
      retry: false,
      retryOnMount: false,
    }
  )

  // Effect to set the config based on report data or default
  useEffect(() => {
    if (!reportDef) {
      setError(new Error(`Report type "${reportType}" not found in registry`))
      setIsLoading(false)
      return
    }

    // If we're still loading the report, wait
    if (reportId && isLoadingReport) {
      return
    }

    try {
      // Start with the default config
      let finalConfig = {
        ...reportDef.defaultConfig,
      } as T

      // If we have report data, merge it with the default config
      if (reportData) {
        if (
          reportData.reportConfig &&
          typeof reportData.reportConfig === 'string'
        ) {
          // Modern format - stored as JSON string
          const parsedConfig = JSON.parse(reportData.reportConfig)
          finalConfig = {
            ...finalConfig,
            ...parsedConfig,
            runtime: Date.now(),
            name: reportData.name,
            description: reportData.description,
            reportId: reportId,
          }
        }
      }
      if (config?.runtime != finalConfig.runtime) setConfig(finalConfig)
      setError(null)
    } catch (err) {
      console.error('Error loading report config:', err)
      setError(
        err instanceof Error
          ? err
          : new Error('Unknown error loading report config')
      )
      // Still set the default config
      setConfig({ ...reportDef.defaultConfig, runtime: Date.now() } as T)
    } finally {
      setIsLoading(false)
    }
  }, [reportDef, reportData, reportId, isLoadingReport, reportType])

  return {
    config,
    isLoading: isLoading || (!!reportId && isLoadingReport),
    error: error || reportError,
    setConfig,
  }
}
