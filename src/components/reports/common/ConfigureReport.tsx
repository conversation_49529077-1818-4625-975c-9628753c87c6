import React, { useState, useRef, useEffect } from 'react'
import { ListFilter } from 'lucide-react'
import { useClickOutside } from '@/hooks/useClickOutside'
import FilterFooter from '@/components/filter/common/FilterFooter'
import reportRegistry from './reportRegistry'

// Base configuration interface that all report configs will extend
export interface BaseReportConfig {
  timestamp?: number
  runtime?: number
  name?: string
  description?: string
  reportId?: string
}

interface ConfigureReportProps<T extends BaseReportConfig> {
  trigger: React.ReactNode
  onApply: (config: T) => void
  initialConfig: T
  onClose?: () => void
  reportType: string
  children: React.ReactNode // Filter sections specific to each report
}

function ConfigureReport<T extends BaseReportConfig>({
  trigger,
  onApply,
  onClose,
  initialConfig,
  reportType,
  children,
}: ConfigureReportProps<T>) {
  const [isOpen, setIsOpen] = useState(false)
  const [config, setConfig] = useState<T>(initialConfig)
  const [isValid, setIsValid] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (config.runtime != initialConfig.runtime) setConfig(initialConfig)
  }, [initialConfig])

  // Get the report definition from registry
  const reportDef = reportRegistry[reportType]

  // Validate config whenever it changes
  useEffect(() => {
    if (reportDef && reportDef.validateConfig) {
      setIsValid(reportDef.validateConfig(config))
    } else {
      setIsValid(true)
    }
  }, [config, reportDef])

  // Close dropdown when clicking outside
  useClickOutside(dropdownRef, () => {
    setIsOpen(false)
    if (onClose) onClose()
  })

  const handleTriggerClick = () => {
    setIsOpen(!isOpen)
  }

  const handleClose = () => {
    setIsOpen(false)
    if (onClose) onClose()
  }

  const handleRunReport = () => {
    onApply({
      ...config,
      timestamp: Date.now(),
      runtime: Date.now(),
    })
    setIsOpen(false)
  }

  const handleClearAll = () => {
    setConfig(initialConfig)
  }

  // Provide context to children to update config
  const configContext = {
    config,
    setConfig: (newConfig: Partial<T>) => {
      setConfig((prev) => ({ ...prev, ...newConfig }))
    },
    isOpen, // Add the isOpen state to the context
  }

  return (
    <div className="relative">
      <div
        onClick={handleTriggerClick}
        data-triggerid="configure-report-trigger"
      >
        {trigger}
      </div>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute right-0 z-20 w-[360px] top-0 left-0 bg-ghostwhite
                    !shadow-[0px_0px_7px_5px_rgba(0,_0,_0,_0.1)] rounded flex flex-col
                    max-h-full min-h-[calc(100vh-285px)] overflow-hidden"
        >
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center">
              <ListFilter className="w-5 h-5 mr-2 text-[#2B5380]" />
              <h3 className="text-sm text-[#2B5380]">
                CONFIGURE {reportType.toUpperCase()}
              </h3>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>

          {/* Scrollable content area */}
          <div className="flex-1 overflow-y-auto mac-scrollbar">
            <div className="p-4 space-y-4">
              {/* Pass context to children with isOpen state */}
              {React.Children.map(children, (child) =>
                React.isValidElement(child)
                  ? React.cloneElement(child as React.ReactElement<any>, {
                      configContext,
                    })
                  : child
              )}
            </div>
          </div>

          <FilterFooter
            onClear={handleClearAll}
            onApply={handleRunReport}
            clearText="Clear All"
            applyText="RUN REPORT"
            applyDisabled={!isValid}
          />
        </div>
      )}
    </div>
  )
}

export default ConfigureReport
