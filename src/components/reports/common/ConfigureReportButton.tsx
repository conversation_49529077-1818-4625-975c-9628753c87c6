import React, { useState, useRef, forwardRef, useImperativeHandle } from 'react'
import { ListFilter } from 'lucide-react'
import ConfigureReport, { BaseReportConfig } from './ConfigureReport'
import reportRegistry from './reportRegistry'
import { cn } from '@/lib/utils'
import { usePathname } from 'next/navigation'

interface ConfigureReportButtonProps<T extends BaseReportConfig> {
  reportType: string
  onConfigChange: (config: T) => void
  initialConfig: T
  onOpenChange?: (isOpen: boolean) => void
}

export interface ConfigureReportButtonRef {
  setOpen: (open: boolean) => void
}

function ConfigureReportButton<T extends BaseReportConfig>(
  {
    reportType,
    onConfigChange,
    initialConfig,
    onOpenChange,
  }: ConfigureReportButtonProps<T>,
  ref: React.Ref<ConfigureReportButtonRef>
) {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedSections, setExpandedSections] = useState<string>('')
  const buttonRef = useRef<HTMLDivElement>(null)
  const configureReportRef = useRef<HTMLDivElement>(null)

  const pathname = usePathname()
  const isMVPReport =
    pathname.includes('mvp') && !pathname.includes('mvp-details')
  // Expose setOpen method to parent components
  useImperativeHandle(ref, () => ({
    setOpen: (open: boolean) => {
      setIsOpen(open)
      if (onOpenChange) {
        onOpenChange(open)
      }

      // Directly trigger the configuration panel to open
      if (open && configureReportRef.current) {
        const triggerElement = configureReportRef.current.querySelector(
          '[data-triggerid="configure-report-trigger"]'
        ) as HTMLElement
        if (triggerElement) {
          triggerElement.click()
        } else {
          // Fallback: manually trigger the configuration
          toggleFilter()
        }
      }
    },
  }))

  const handleApplyConfig = (config: T) => {
    onConfigChange(config)
  }

  const toggleFilter = () => {
    const newIsOpen = !isOpen
    setIsOpen(newIsOpen)
    if (onOpenChange) {
      onOpenChange(newIsOpen)
    }
  }

  const toggleSection = (section: string) => {
    setExpandedSections(expandedSections === section ? '' : section)
  }

  // Get report definition from registry
  const reportDef = reportRegistry[reportType]
  if (!reportDef) {
    console.error(`Report type "${reportType}" not found in registry`)
    return null
  }
  const FilterComponent = reportDef.filterComponent
  const trigger = (
    <div
      role="button"
      onClick={toggleFilter}
      className={`relative ${isOpen ? '' : 'rounded-[5px] border border-white px-4 hover:border-ui-dark-gray py-2'}
      ${!initialConfig.runtime || (initialConfig.runtime && isMVPReport) ? 'hidden' : ''}`}
    >
      <div className={cn('flex items-center text-[#566582] cursor-pointer')}>
        <ListFilter size={18} className="mr-1" />
        <span className="font-open-sans text-[13px] font-normal">
          CONFIGURE REPORT
        </span>
      </div>
    </div>
  )

  return (
    <div ref={buttonRef}>
      <div ref={configureReportRef}>
        <ConfigureReport
          trigger={trigger}
          onApply={handleApplyConfig}
          initialConfig={initialConfig}
          onClose={() => {
            setIsOpen(false)
            if (onOpenChange) {
              onOpenChange(false)
            }
          }}
          reportType={reportType}
        >
          <FilterComponent
            expandedSections={expandedSections}
            toggleSection={toggleSection}
          />
        </ConfigureReport>
      </div>
    </div>
  )
}

export default forwardRef(ConfigureReportButton) as <
  T extends BaseReportConfig,
>(
  props: ConfigureReportButtonProps<T> & {
    ref?: React.Ref<ConfigureReportButtonRef>
  }
) => React.ReactElement
