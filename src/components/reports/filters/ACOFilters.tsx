import React, { useEffect, useMemo, useRef, useState } from 'react'
import FilterSection from '@/components/filter/common/FilterSection'
import FilterChipSection from '@/components/filter/common/FilterChipSection'
import { IntervalType } from '@/types/intervalType'
import dayjs, { Dayjs } from 'dayjs'
import { formatReportDate } from '@/lib/dateRange'
import { BaseReportConfig } from '../common/ConfigureReport'
import { api } from '@/trpc/react'
import {
  EntityDescriptionRequest,
  OrganizationTypesRequest,
} from '@/types/reports/medisolvReport'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Calendar,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Search,
} from 'lucide-react'
import { EntityCodeType } from '@/services/entityOrganizationType/EntityOrganizationTypeRepository'

// Define the specific config for ACO reports
export interface ACOReportConfig extends BaseReportConfig {
  intervalType: IntervalType
  startDate: string
  selectedMeasures: string[]
  organizationType: string
  universe: string
  filterByUniverse: string
  filterByCode: number
  selectedEntityId: string
}

interface ACOFiltersProps {
  configContext: {
    config: ACOReportConfig
    setConfig: (config: Partial<ACOReportConfig>) => void
    isOpen?: boolean
  }
  expandedSections: string
  toggleSection: (section: string) => void
}

function intervalTypeToReadable(intervalType: IntervalType) {
  return intervalType === 'M'
    ? 'Month'
    : intervalType === 'Q'
      ? 'Quarter'
      : intervalType === 'Y'
        ? 'Year'
        : undefined
}

// Define CSS animation for smooth transitions
const animationStyles = `
@keyframes fadeInSlideDown {
  from {
    opacity: 0
    transform: translateY(-10px)
  }
  to {
    opacity: 1
    transform: translateY(0)
  }
}
`

const ACOFilters: React.FC<ACOFiltersProps> = ({
  configContext,
  expandedSections,
  toggleSection,
}) => {
  const { config, setConfig, isOpen } = configContext || {
    config: {
      intervalType: 'Y',
      startDate: dayjs().startOf('year').format('YYYY-MM-DD'),
      selectedMeasures: [],
      organizationType: '',
      universe: '',
      filterByUniverse: '',
      filterByCode: 0,
      selectedEntityId: '',
    },
    setConfig: () => {},
    isOpen: false,
  }

  const [searchTerm, setSearchTerm] = useState('')

  // Calculate end date
  const endDate = new Date(config.startDate)
  endDate.setMonth(endDate.getMonth() + 1)

  // Fetch measures from the API
  const { data: measuresData, isLoading: isLoadingMeasures } =
    api.report.getACOMeasures.useQuery(
      {
        codeNumeric: '3', // '3' is the code for ACO organizations
        intervalType: config.intervalType,
        startDate: new Date(config.startDate),
        endDate: endDate,
      },
      {
        enabled: isOpen, // Only fetch when dropdown is open
        staleTime: 0, // Always fetch fresh data
      }
    )

  // Fetch organization types from the API
  const { data: orgTypesData, isLoading: isLoadingOrgTypes } =
    api.organizationType.getOrganizationTypes.useQuery(
      {
        intervalType: config.intervalType,
        startDate: new Date(config.startDate),
        endDate: endDate,
        measureNames: config.selectedMeasures,
      } as OrganizationTypesRequest,
      {
        enabled: isOpen && config.selectedMeasures.length > 0, // Only fetch when dialog is open
        staleTime: 0, // Always fetch fresh data
      }
    )

  // Fetch higher level organization types when an organization type is selected
  const {
    data: higherLevelOrgTypesData,
    isLoading: isLoadingHigherLevelOrgTypes,
  } = api.organizationType.getHigherLevelOrganizationTypes.useQuery(
    {
      intervalType: config.intervalType,
      startDate: new Date(config.startDate),
      endDate: endDate,
      measureNames: config.selectedMeasures,
      code: parseInt(config.organizationType || '0'),
      universe:
        orgTypesData?.organizationTypes.find(
          (org) => org.Code === config.organizationType
        )?.Universe ||
        config.universe ||
        '',
    } as OrganizationTypesRequest,
    {
      enabled:
        isOpen &&
        config.selectedMeasures.length > 0 &&
        !!config.organizationType, // Only fetch when an organization type is selected
      staleTime: 0, // Always fetch fresh data
    }
  )

  // Fetch entity descriptions when a filter is selected
  const { data: entityData, isLoading: isLoadingEntityData } =
    api.organizationType.getEntityDescription.useQuery(
      {
        intervalType: config.intervalType,
        startDate: new Date(config.startDate),
        endDate: endDate,
        measureNames: config.selectedMeasures,
        universe: config.filterByUniverse || '',
      } as EntityDescriptionRequest,
      {
        enabled: isOpen && !!config.filterByUniverse, // Only fetch when filter by is selected
        staleTime: 0, // Always fetch fresh data
      }
    )

  // When config changes and the dropdown is open, expand the Analysis Details section
  useEffect(() => {
    if (isOpen) {
      if (expandedSections !== 'INTERVAL_TYPE') toggleSection('INTERVAL_TYPE')

      if (config.organizationType) {
        setTimeout(() => {
          console.log('Setting up UI elements for loaded report configuration')

          // Try to find the radio button by ID first
          let radioButton = document.querySelector(
            `input[id="org-type-${config.organizationType}"]`
          ) as HTMLInputElement

          // If not found by ID, try to find it by value
          if (!radioButton) {
            radioButton = document.querySelector(
              `input[name="organizationType"][value="${config.organizationType}.${config.universe}"]`
            ) as HTMLInputElement
          }

          // If still not found, try to find it by checking if the value starts with the organizationType
          if (!radioButton) {
            const allRadioButtons = document.querySelectorAll(
              'input[name="organizationType"]'
            )
            for (let i = 0; i < allRadioButtons.length; i++) {
              const rb = allRadioButtons[i] as HTMLInputElement
              if (rb.value.startsWith(`${config.organizationType}.`)) {
                radioButton = rb
                break
              }
            }
          }

          if (radioButton) {
            console.log(
              'Found organization type radio button, setting checked to true'
            )
            // Just set the radio button as checked, don't call handleOrganizationTypeChange
            // to avoid resetting the filter by values
            radioButton.checked = true
          } else {
            console.log(
              'Organization type radio button not found, will try again when API data loads'
            )
          }

          // If we have filter by values, force the filter by radio button to be checked
          if (config.filterByUniverse && config.filterByCode > 0) {
            console.log('Forcing a click on the filter by radio button')

            // Try to find the filter by radio button
            const filterByRadioButton = document.querySelector(
              `input[name="filterBy"][value="${config.filterByCode}"]`
            ) as HTMLInputElement

            if (filterByRadioButton) {
              console.log(
                'Found filter by radio button, setting checked to true'
              )
              filterByRadioButton.checked = true
            } else {
              console.log(
                'Filter by radio button not found, will try again when API data loads'
              )
            }
          }

          // If we have a selected entity, set the select element value
          if (config.selectedEntityId) {
            console.log('Setting selected entity:', config.selectedEntityId)

            // Find the select element
            const selectElement = document.querySelector(
              '.entity-select'
            ) as HTMLSelectElement
            if (selectElement) {
              console.log('Found select element, setting value')
              selectElement.value = config.selectedEntityId
            } else {
              console.log(
                'Select element not found, will try again when API data loads'
              )
            }
          }
        }, 0) // Use a longer timeout to ensure all API data has been loaded
      }
    }
  }, [isOpen, config])

  // When organization types data is loaded, ensure the correct organization type is selected
  useEffect(() => {
    if (orgTypesData?.organizationTypes && config.organizationType) {
      // Check if the saved organization type exists in the available options
      const matchingOrgType = orgTypesData.organizationTypes.find(
        (ot) =>
          ot.Code === config.organizationType ||
          String(ot.Code) === config.organizationType
      )

      if (matchingOrgType) {
        console.log(
          'Found matching organization type in API data:',
          matchingOrgType
        )

        // Just set the radio button as checked, don't call handleOrganizationTypeChange
        // to avoid resetting the filter by values
        setTimeout(() => {
          // Try to find the radio button by ID first
          let radioButton = document.querySelector(
            `input[id="org-type-${matchingOrgType.Code}"]`
          ) as HTMLInputElement

          // If not found by ID, try to find it by value
          if (!radioButton) {
            radioButton = document.querySelector(
              `input[name="organizationType"][value="${matchingOrgType.Code}.${matchingOrgType.Universe}"]`
            ) as HTMLInputElement
          }

          if (radioButton) {
            console.log('Found radio button, setting checked to true')
            radioButton.checked = true
          } else {
            console.log('Radio button not found')
          }
        }, 0)

        // IMPORTANT: Do NOT update the universe from API data if we're loading from a saved report
        // This preserves the original universe value from the report
        const isLoadingFromSavedReport =
          config.organizationType === config.organizationType &&
          config.universe === config.universe

        if (!isLoadingFromSavedReport) {
          console.log(
            'Not loading from saved report, updating universe from API data'
          )
          // Update the config with the universe from the API data
          setConfig({
            universe: matchingOrgType.Universe,
          })
        } else {
          console.log(
            'Loading from saved report, preserving original universe:',
            config.universe
          )
        }
      } else if (orgTypesData.organizationTypes.length > 0) {
        console.log(
          'Saved organization type not found in available options, selecting first available option'
        )
        // If the saved organization type doesn't exist, select the first available option
        const firstOrgType = orgTypesData.organizationTypes[0]
        if (firstOrgType) {
          // Only call handleOrganizationTypeChange if we don't have filter by values
          if (!config.filterByUniverse && !config.filterByCode) {
            handleOrganizationTypeChange(
              firstOrgType.Code,
              firstOrgType.Universe
            )
          } else {
            // Just update the organization type and universe, preserve filter by values
            setConfig({
              organizationType: firstOrgType.Code,
              universe: firstOrgType.Universe,
            })

            // Set the radio button as checked
            setTimeout(() => {
              const radioButton = document.querySelector(
                `input[id="org-type-${firstOrgType.Code}"]`
              ) as HTMLInputElement
              if (radioButton) {
                radioButton.checked = true
              }
            }, 0)
          }
        }
      }
    }
  }, [orgTypesData])

  // When higher level organization types data is loaded, ensure the correct filter by value is selected
  useEffect(() => {
    if (higherLevelOrgTypesData?.organizationTypes) {
      console.log(
        'Higher level organization types data loaded:',
        higherLevelOrgTypesData
      )
      console.log('Current filter by code:', config.filterByCode)
      console.log('Current filter by universe:', config.filterByUniverse)

      // Only proceed if we have a saved filter by value
      if (config.filterByCode > 0 && config.filterByUniverse) {
        // Check if the saved filter by value exists in the available options
        const matchingFilterBy = higherLevelOrgTypesData.organizationTypes.find(
          (ot) =>
            Number.parseInt(ot.Code) === config.filterByCode ||
            Number(ot.Code) === config.filterByCode
        )

        if (matchingFilterBy) {
          console.log('Found matching filter by in API data:', matchingFilterBy)

          // Force the radio button to be checked
          setTimeout(() => {
            const radioButton = document.querySelector(
              `input[name="filterBy"][value="${matchingFilterBy.Code}"]`
            ) as HTMLInputElement
            if (radioButton) {
              console.log(
                'Found filter by radio button, setting checked to true'
              )
              radioButton.checked = true
            } else {
              console.log('Filter by radio button not found')
            }
          }, 0)
        } else if (higherLevelOrgTypesData.organizationTypes.length > 0) {
          console.log(
            'Saved filter by value not found in available options, selecting first available option'
          )
          // If the saved filter by value doesn't exist, select the first available option
          const firstFilterBy = higherLevelOrgTypesData.organizationTypes[0]
          if (firstFilterBy) {
            handleFilterByChange(firstFilterBy.Universe, firstFilterBy.Code)
          }
        }
      }
    }
  }, [higherLevelOrgTypesData])

  // When entity descriptions data is loaded, ensure the correct entity is selected
  useEffect(() => {
    if (entityData?.entityDescriptions) {
      console.log('Entity descriptions data loaded:', entityData)
      console.log('Current select option:', config.selectedEntityId)

      // Only proceed if we have a saved select option
      if (config.selectedEntityId) {
        // Check if the saved entity exists in the available options
        const matchingEntity = entityData.entityDescriptions.find(
          (entity) =>
            entity.SourceContainerIdentifier === config.selectedEntityId
        )

        if (matchingEntity) {
          console.log('Found matching entity in API data:', matchingEntity)

          // Force the select option to be selected
          setTimeout(() => {
            // Find the select element in the Entity Description Section
            // We can't use select[value="..."] because that's not how select elements work
            const selectElement = document.querySelector(
              '.entity-select'
            ) as HTMLSelectElement
            if (selectElement) {
              console.log('Found select element, setting selected to true')
              selectElement.value = config.selectedEntityId
            } else {
              console.log('Select element not found')
            }
          }, 0)
        } else if (entityData.entityDescriptions.length > 0) {
          console.log(
            'Saved entity not found in available options, selecting first available option'
          )
          // If the saved entity doesn't exist, select the first available option
          const firstEntity = entityData.entityDescriptions[0]
          if (firstEntity) {
            handleSelectOptionChange(firstEntity.SourceContainerIdentifier)
          }
        }
      }
    }
  }, [entityData])

  // Extract measure names from the API response - memoize to prevent unnecessary recalculations
  const measures = useMemo(
    () => measuresData?.measures?.map((measure) => measure.MeasureName) || [],
    [measuresData]
  )

  // Filter measures based on search term - memoize to prevent unnecessary recalculations
  const filteredMeasures = useMemo(
    () =>
      measures.filter((measure) =>
        measure.toLowerCase().includes(searchTerm.toLowerCase())
      ),
    [measures, searchTerm]
  )

  // Date picker state
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(null)
  const datePickerRef = useRef<HTMLDivElement>(null)

  // Initialize selected date from config
  useEffect(() => {
    if (config.startDate) {
      setSelectedDate(dayjs(config.startDate))
    }
  }, [config.startDate])

  const handleIntervalTypeChange = (type: IntervalType) => {
    // Get the current date
    const currentDate = dayjs(config.startDate)
    let newDate = currentDate

    // Adjust date based on interval type
    if (type === 'Y') {
      // For Year, use January 1st of the selected year
      newDate = currentDate.startOf('year')
    } else if (type === 'Q') {
      // For Quarter, ensure the month is a valid quarter start month (Jan, Apr, Jul, Oct)
      const month = currentDate.month()
      // Find the nearest quarter start month (0, 3, 6, 9)
      const quarterMonth = Math.floor(month / 3) * 3
      newDate = currentDate.month(quarterMonth).startOf('month')
    } else if (type === 'M') {
      // For Month, use the first day of the selected month
      newDate = currentDate.startOf('month')
    }

    setSelectedDate(newDate)
    setConfig({
      intervalType: type,
      startDate: newDate.format('YYYY-MM-DD'),
    })
  }

  const handleDateChange = (
    date: Dayjs | null,
    isSelection: boolean = true
  ) => {
    // Ensure the date is valid before updating
    if (date && date.isValid()) {
      let adjustedDate = date

      // Adjust date based on current interval type
      if (config.intervalType === 'Y') {
        // For Year, use January 1st of the selected year
        adjustedDate = date.startOf('year')
      } else if (config.intervalType === 'Q') {
        // For Quarter, ensure it's the first day of the quarter month
        adjustedDate = date.startOf('month')
      } else if (config.intervalType === 'M') {
        // For Month, use the first day of the selected month
        adjustedDate = date.startOf('month')
      }

      setSelectedDate(adjustedDate)
      setConfig({
        startDate: adjustedDate.format('YYYY-MM-DD'),
      })
      // Only close the date picker if this is an actual date selection, not navigation
      if (isSelection) {
        setIsDatePickerOpen(false)
      }
    }
  }

  const handleMeasureToggle = (measure: string) => {
    const isSelected = config.selectedMeasures.includes(measure)
    setConfig({
      selectedMeasures: isSelected
        ? config.selectedMeasures.filter((m) => m !== measure)
        : [...config.selectedMeasures, measure],
    })
  }

  const handleOrganizationTypeChange = (code: string, universe: string) => {
    console.log('handleOrganizationTypeChange called with:', code, universe)

    // If we are, preserve the filter by values
    // Check if we're loading from a saved report with filter by values
    // We need to be more flexible with the matching to handle string/number type differences
    const isLoadingFromSavedReport =
      // Match by code (handling both string and number types)
      (config.organizationType === code ||
        config.organizationType === String(code) ||
        Number(config.organizationType) === Number(code)) &&
      // Match by universe
      config.universe === universe &&
      // Ensure we have filter values to preserve
      config.filterByUniverse &&
      config.filterByCode > 0

    const newConfig = {
      organizationType: code,
      universe: universe,
    }

    setConfig(newConfig)

    // Only reset filter by values if we're not loading from a saved report
    if (!isLoadingFromSavedReport) {
      console.log(
        'Not loading from saved report or no filter values to preserve, resetting filter values'
      )

      setConfig({
        filterByUniverse: '',
        filterByCode: 0,
        selectedEntityId: '',
      })
    } else console.log('New config after organization type change:', newConfig)

    // Force the radio button to be checked
    setTimeout(() => {
      // Try to find the radio button by ID first
      let radioButton = document.querySelector(
        `input[id="org-type-${code}"]`
      ) as HTMLInputElement

      // If not found by ID, try to find it by value
      if (!radioButton) {
        radioButton = document.querySelector(
          `input[name="organizationType"][value="${code}.${universe}"]`
        ) as HTMLInputElement
      }

      // If still not found, try to find it by checking if the value starts with the code
      if (!radioButton) {
        const allRadioButtons = document.querySelectorAll(
          'input[name="organizationType"]'
        )
        for (let i = 0; i < allRadioButtons.length; i++) {
          const rb = allRadioButtons[i] as HTMLInputElement
          if (rb.value.startsWith(`${code}.`)) {
            radioButton = rb
            break
          }
        }
      }

      if (radioButton) {
        console.log('Found radio button, setting checked to true')
        radioButton.checked = true
      } else {
        console.log('Radio button not found')
      }
    }, 0)
  }

  const handleFilterByChange = (universe: string, code: string) => {
    console.log('handleFilterByChange called with:', universe, code)

    // When filterBy changes, reset selectOption
    setConfig({
      filterByUniverse: universe,
      filterByCode: Number.parseInt(code),
      selectedEntityId: '', // Reset selectedEntityId when filterBy changes
    })

    // Force the radio button to be checked
    setTimeout(() => {
      const radioButton = document.querySelector(
        `input[name="filterBy"][value="${code}"]`
      ) as HTMLInputElement
      if (radioButton) {
        console.log('Found filter by radio button, setting checked to true')
        radioButton.checked = true
      } else {
        console.log('Filter by radio button not found')
      }
    }, 0)
  }

  const handleSelectOptionChange = (value: string) => {
    console.log('handleSelectOptionChange called with:', value)

    setConfig({
      selectedEntityId: value,
    })

    toggleSection('ANALYSIS_DETAILS')
  }

  const handleRemoveMeasure = (measure: string) => {
    setConfig({
      filterByUniverse: '',
      filterByCode: 0,
      selectedEntityId: '',
      organizationType: '',
      selectedMeasures: config.selectedMeasures.filter((m) => m !== measure),
    })
  }

  const handleSelectAllMeasures = () => {
    if (config.selectedMeasures.length === filteredMeasures.length) {
      setConfig({ selectedMeasures: [] })
    } else {
      setConfig({
        selectedMeasures: [...filteredMeasures],
      })
    }
  }

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      {/* Interval Type Section */}
      <FilterSection
        title="INTERVAL TYPE"
        expanded={expandedSections === 'INTERVAL_TYPE'}
        onToggle={() => toggleSection('INTERVAL_TYPE')}
      >
        <div className="space-y-2">
          <label className="flex items-center space-x-2 py-1">
            <input
              type="radio"
              name="intervalType"
              checked={config.intervalType === 'M'}
              onChange={() => handleIntervalTypeChange('M')}
              className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
            />
            <span className="text-[12px]">Month</span>
          </label>

          <label className="flex items-center space-x-2 py-1">
            <input
              type="radio"
              name="intervalType"
              checked={config.intervalType === 'Q'}
              onChange={() => handleIntervalTypeChange('Q')}
              className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
            />
            <span className="text-[12px]">Quarter</span>
          </label>

          <label className="flex items-center space-x-2 py-1">
            <input
              type="radio"
              name="intervalType"
              checked={config.intervalType === 'Y'}
              onChange={() => handleIntervalTypeChange('Y')}
              className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
            />
            <span className="text-[12px]">Year</span>
          </label>
        </div>
      </FilterSection>

      {/* Date Section */}
      <FilterSection
        title={
          intervalTypeToReadable(config.intervalType)?.toUpperCase() ?? 'DATE'
        }
        expanded={expandedSections === 'DATE'}
        onToggle={() => toggleSection('DATE')}
        className="focus:outline-none"
      >
        <div
          className="relative"
          ref={datePickerRef}
          onMouseDown={(e) => {
            e.preventDefault()
            e.stopPropagation()
          }}
        >
          <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
            <PopoverTrigger asChild>
              <div
                data-testid="date-field-container"
                className="flex items-center border border-gray-300 rounded-md p-2 cursor-pointer"
              >
                <Calendar className="mr-2 text-gray-400" size={16} />
                <span className="text-[12px] flex-grow">
                  {selectedDate
                    ? formatReportDate(selectedDate, config.intervalType)
                    : 'Select date'}
                </span>
                {isDatePickerOpen ? (
                  <ChevronUp className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                )}
              </div>
            </PopoverTrigger>
            <PopoverContent
              className="w-auto p-0 border-0 rounded-lg shadow-lg"
              align="start"
            >
              <div className="p-[10px] bg-ui-pale-blue w-fit rounded-lg date-picker-popup shadow-[0px_0px_4px_0px_#00000040]">
                <div className="pt-4 bg-white">
                  <div className="flex justify-between items-center mx-8">
                    <div className="flex items-center text-ui-dark-gray font-bold w-full">
                      <button
                        onClick={() => {
                          const newDate = selectedDate
                            ? selectedDate.subtract(1, 'year')
                            : dayjs().subtract(1, 'year')
                          handleDateChange(newDate, false)
                        }}
                        className="focus:outline-none"
                      >
                        <ChevronLeft className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                      </button>
                      <div className="text-ui-dark-gray font-semibold w-full text-center">
                        {selectedDate ? selectedDate.year() : dayjs().year()}
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        const newDate = selectedDate
                          ? selectedDate.add(1, 'year')
                          : dayjs().add(1, 'year')
                        handleDateChange(newDate, false)
                      }}
                      className="focus:outline-none"
                    >
                      <ChevronRight className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                    </button>
                  </div>
                </div>

                {/* Year Picker */}
                {config.intervalType === 'Y' && (
                  <div className="grid grid-cols-4 bg-white p-4 rounded-lg">
                    {Array.from({ length: 12 }, (_, i) => {
                      const year =
                        (selectedDate ? selectedDate.year() : dayjs().year()) -
                        5 +
                        i
                      return (
                        <button
                          key={year}
                          className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans h-[30px] focus:outline-none 
                                       ${
                                         selectedDate &&
                                         selectedDate.year() === year
                                           ? 'bg-ui-dark-gray text-white rounded-l rounded-r'
                                           : 'text-black'
                                       }
                                       hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
                          onClick={() => {
                            const newDate = (selectedDate || dayjs())
                              .year(year)
                              .startOf('year')
                            handleDateChange(newDate)
                          }}
                        >
                          {year}
                        </button>
                      )
                    })}
                  </div>
                )}

                {/* Month Picker */}
                {config.intervalType === 'M' && (
                  <div className="grid grid-cols-4 bg-white p-4 rounded-lg">
                    {[
                      'Jan',
                      'Feb',
                      'Mar',
                      'Apr',
                      'May',
                      'Jun',
                      'Jul',
                      'Aug',
                      'Sep',
                      'Oct',
                      'Nov',
                      'Dec',
                    ].map((month, index) => (
                      <button
                        key={month}
                        className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans w-[36px] h-[30px] focus:outline-none 
                                     ${
                                       selectedDate &&
                                       selectedDate.month() === index
                                         ? 'bg-ui-dark-gray text-white rounded-l rounded-r'
                                         : 'text-black'
                                     }
                                     hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
                        onClick={() => {
                          const newDate = (selectedDate || dayjs())
                            .month(index)
                            .startOf('month')
                          handleDateChange(newDate)
                        }}
                      >
                        {month}
                      </button>
                    ))}
                  </div>
                )}

                {/* Quarter Picker */}
                {config.intervalType === 'Q' && (
                  <div className="grid grid-cols-4 bg-white p-4 rounded-lg">
                    {[
                      'Jan',
                      'Feb',
                      'Mar',
                      'Apr',
                      'May',
                      'Jun',
                      'Jul',
                      'Aug',
                      'Sep',
                      'Oct',
                      'Nov',
                      'Dec',
                    ].map((month, index) => {
                      const isQuarterStart = [0, 3, 6, 9].includes(index)
                      return (
                        <button
                          key={month}
                          className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans w-[36px] h-[30px] focus:outline-none 
                                       ${
                                         !isQuarterStart
                                           ? 'text-[#D1D5DB] cursor-not-allowed'
                                           : selectedDate &&
                                               selectedDate.month() === index
                                             ? 'bg-ui-dark-gray text-white rounded-l rounded-r'
                                             : 'text-black'
                                       }
                                       ${isQuarterStart ? 'hover:bg-[#EBEFFD] hover:text-[#2D7CBA]' : ''}`}
                          onClick={() => {
                            if (isQuarterStart) {
                              const newDate = (selectedDate || dayjs())
                                .month(index)
                                .startOf('month')
                              handleDateChange(newDate)
                            }
                          }}
                          disabled={!isQuarterStart}
                        >
                          {month}
                        </button>
                      )
                    })}
                  </div>
                )}
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </FilterSection>

      {/* Measure Name Section */}
      <FilterSection
        title="MEASURES"
        expanded={expandedSections === 'MEASURES'}
        onToggle={() => toggleSection('MEASURES')}
        count={`${config.selectedMeasures.length}/${filteredMeasures.length}`}
        className="focus:outline-none"
      >
        <div className="space-y-3">
          <div className="relative mb-4">
            <input
              key="measureSearch"
              autoFocus={true}
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-4 pr-8 py-2 border-b border-[#7396B0] text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
              style={{
                borderTop: 'none',
                borderLeft: 'none',
                borderRight: 'none',
              }}
            />
            <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-[#7396B0]" />
          </div>

          {searchTerm.length === 0 && (
            <div className="flex items-center space-x-2 mb-2">
              <input
                type="checkbox"
                id="selectAllMeasures"
                className="rounded border-ui-dark-gray accent-[#2B5380] w-[13px] h-[16.34px]"
                checked={
                  filteredMeasures &&
                  config.selectedMeasures.length === filteredMeasures.length
                }
                onChange={handleSelectAllMeasures}
              />
              <label
                htmlFor="selectAllMeasures"
                className="text-[12px] font-open-sans font-normal"
              >
                Select All
              </label>
            </div>
          )}

          <div className="space-y-2 max-h-48 overflow-y-auto mac-scrollbar">
            {isLoadingMeasures ? (
              <div className="flex justify-center items-center py-4">
                <div
                  data-testid="spinner"
                  className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#2B5380]"
                ></div>
              </div>
            ) : filteredMeasures.length > 0 ? (
              filteredMeasures.map((measure) => (
                <label
                  key={measure}
                  className="flex items-start space-x-2 py-1"
                >
                  <input
                    type="checkbox"
                    id={measure}
                    className="rounded border-ui-dark-gray accent-[#2B5380] w-[13px] h-[16.34px] mt-1"
                    checked={config.selectedMeasures.includes(measure)}
                    onChange={() => handleMeasureToggle(measure)}
                  />
                  <span data-testid="measure-name" className="text-[12px]">
                    {measure}
                  </span>
                </label>
              ))
            ) : (
              <div className="text-center py-4 text-gray-500">
                {searchTerm
                  ? 'No measures found matching your search'
                  : 'No measures available'}
              </div>
            )}
          </div>
        </div>
      </FilterSection>

      {/* Analysis Details Section - Always shown but with placeholder when no measures selected */}
      <FilterSection
        title="ANALYSIS DETAILS"
        expanded={expandedSections === 'ANALYSIS_DETAILS'}
        onToggle={() => toggleSection('ANALYSIS_DETAILS')}
        className="focus:outline-none"
      >
        {config.selectedMeasures.length === 0 ? (
          // Placeholder content when no measures are selected
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <div className="text-gray-500 mb-2">
              <p className="text-sm font-medium">
                Please select a measure to continue
              </p>
            </div>
            <button
              onClick={() => toggleSection('MEASURES')}
              className="mt-2 text-[#2B5380] hover:text-[#1e3c5c] text-xs font-semibold px-3 py-1 border border-[#2B5380] rounded-md"
            >
              Select Measures
            </button>
          </div>
        ) : (
          // Actual content when measures are selected
          <div className="space-y-4">
            {/* Organization Type for Reporting Output */}
            <div className="space-y-2">
              <label className="text-[12px] font-semibold text-[#2B5380] flex items-center">
                Organization Type for Reporting Output
                {config.organizationType === '' && (
                  <span className="ml-1 text-red-500">*</span>
                )}
              </label>
              <div className="space-y-2">
                {isLoadingOrgTypes ? (
                  <div className="flex justify-center items-center py-2">
                    <div
                      data-testid="spinner"
                      className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#2B5380]"
                    ></div>
                  </div>
                ) : orgTypesData?.organizationTypes &&
                  orgTypesData.organizationTypes.length > 0 ? (
                  <>
                    {/* We don't want to add a manual radio button here, as it should only show valid organization types */}
                    {/* Render the organization types from the API */}
                    {orgTypesData.organizationTypes.map(
                      (orgType: EntityCodeType) => {
                        const isChecked =
                          config.organizationType === orgType.Code ||
                          config.organizationType === String(orgType.Code)
                        console.log(
                          `Radio button for ${orgType.Universe} (${orgType.Code}): isChecked=${isChecked}, config.organizationType=${config.organizationType}`
                        )
                        return (
                          <label
                            key={orgType.Code}
                            className="flex items-center space-x-2 py-1"
                          >
                            <input
                              type="radio"
                              name="organizationType"
                              value={`${orgType.Code}.${orgType.Universe}`}
                              checked={isChecked}
                              onChange={() => {
                                console.log(
                                  'Selecting organization type:',
                                  orgType.Code,
                                  orgType.Universe
                                )
                                handleOrganizationTypeChange(
                                  orgType.Code,
                                  orgType.Universe
                                )
                              }}
                              className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
                              id={`org-type-${orgType.Code}`}
                            />
                            <span className="text-[12px]">
                              {orgType.Universe}
                            </span>
                          </label>
                        )
                      }
                    )}
                  </>
                ) : (
                  // Message when no organization types are returned
                  <div className="text-center py-4 text-gray-500">
                    <p className="text-sm">
                      No Organization Types for selected Measure, period, and
                      date
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Filter by - Only show if Organization Type is selected */}
            {config.organizationType && (
              <div
                className="space-y-2 transition-all duration-300 ease-in-out opacity-100 transform translate-y-0"
                style={{
                  animation: 'fadeInSlideDown 0.3s ease-in-out',
                }}
              >
                <label className="text-[12px] font-semibold text-[#2B5380] flex items-center">
                  Filter by
                  {config.filterByUniverse === '' && (
                    <span className="ml-1 text-red-500">*</span>
                  )}
                </label>

                {/* Show loading spinner while fetching higher level organization types */}
                {isLoadingHigherLevelOrgTypes ? (
                  <div className="flex justify-center items-center py-4">
                    <div
                      data-testid="spinner"
                      className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#2B5380]"
                    ></div>
                  </div>
                ) : (
                  <div
                    className="space-y-2 transition-all duration-300 ease-in-out opacity-100 transform translate-y-0"
                    style={{
                      animation: 'fadeInSlideDown 0.3s ease-in-out',
                    }}
                  >
                    {higherLevelOrgTypesData?.organizationTypes &&
                    higherLevelOrgTypesData.organizationTypes.length > 0 ? (
                      <>
                        {/* Add a manual radio button for the saved filter by value if it's not in the list */}
                        {config.filterByCode > 0 &&
                          config.filterByUniverse &&
                          !higherLevelOrgTypesData.organizationTypes.some(
                            (ot) =>
                              Number.parseInt(ot.Code) === config.filterByCode
                          ) && (
                            <label className="flex items-center space-x-2 py-1">
                              <input
                                type="radio"
                                name="filterBy"
                                value={config.filterByCode.toString()}
                                checked={true}
                                onChange={() => {}} // No need to change since it's already selected
                                className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
                              />
                              <span className="text-[12px]">
                                {config.filterByUniverse}
                              </span>
                            </label>
                          )}
                        {/* Render the higher level organization types from the API */}
                        {higherLevelOrgTypesData.organizationTypes.map(
                          (orgType: EntityCodeType) => (
                            <label
                              key={orgType.Code}
                              className="flex items-center space-x-2 py-1"
                            >
                              <input
                                type="radio"
                                name="filterBy"
                                value={orgType.Code}
                                checked={
                                  config.filterByCode ===
                                    Number.parseInt(orgType.Code) ||
                                  config.filterByCode === Number(orgType.Code)
                                }
                                onChange={() =>
                                  handleFilterByChange(
                                    orgType.Universe,
                                    orgType.Code
                                  )
                                }
                                className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
                              />
                              <span className="text-[12px]">
                                {orgType.Universe}
                              </span>
                            </label>
                          )
                        )}
                      </>
                    ) : (
                      // Message when no organization types are returned
                      <div className="text-center py-4 text-gray-500">
                        <p className="text-sm">
                          No higher level Organizations for selected
                          Organization Type
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Entity Description Section - Only show if Filter by is selected */}
            {config.filterByUniverse && (
              <div
                className="space-y-2 transition-all duration-300 ease-in-out opacity-100 transform translate-y-0"
                style={{
                  animation: 'fadeInSlideDown 0.3s ease-in-out',
                }}
              >
                <label className="text-[12px] font-semibold text-[#2B5380] flex items-center">
                  Select {config.filterByUniverse}
                  {config.selectedEntityId === '' && (
                    <span className="ml-1 text-red-500">*</span>
                  )}
                </label>

                {isLoadingEntityData ? (
                  <div className="flex justify-center items-center py-4">
                    <div
                      data-testid="spinner"
                      className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#2B5380]"
                    ></div>
                  </div>
                ) : !entityData?.entityDescriptions ||
                  entityData.entityDescriptions.length === 0 ? (
                  // If there are no entities but we have a saved entity, show it as a disabled select
                  config.selectedEntityId ? (
                    <div className="relative">
                      <select
                        value={config.selectedEntityId}
                        onChange={(e) => {}} // No need to change since it's already selected
                        className="entity-select w-full p-2 text-[12px] border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2B5380] focus:border-transparent"
                      >
                        <option value={config.selectedEntityId}>
                          {/* Use a default description if we don't have the actual one */}
                          {config.selectedEntityId}
                        </option>
                      </select>
                    </div>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <p className="text-sm">
                        No entities found for selected filter
                      </p>
                    </div>
                  )
                ) : (
                  <div className="relative">
                    <select
                      value={config.selectedEntityId}
                      onChange={(e) => handleSelectOptionChange(e.target.value)}
                      className="entity-select w-full p-2 text-[12px] border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2B5380] focus:border-transparent"
                    >
                      <option value="">Select an entity</option>
                      {/* Add the saved entity if it's not in the list */}
                      {config.selectedEntityId &&
                        !entityData.entityDescriptions.some(
                          (entity) =>
                            entity.SourceContainerIdentifier ===
                            config.selectedEntityId
                        ) && (
                          <option value={config.selectedEntityId}>
                            {config.selectedEntityId}
                          </option>
                        )}
                      {entityData.entityDescriptions.map((entity, index) => (
                        <option
                          key={index}
                          value={entity.SourceContainerIdentifier}
                        >
                          {entity.Description}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </FilterSection>

      {/* All Chip Sections moved to the bottom */}

      {/* Interval Type Chip Section */}
      {config.intervalType && (
        <>
          <div className="text-[12px] font-semibold text-[#2B5380] mt-4">
            INTERVAL TYPE
          </div>
          <FilterChipSection
            allSelected={false}
            removable={false}
            items={[intervalTypeToReadable(config.intervalType) ?? 'Year']}
            onRemove={() => {}} // Interval type is required, so we don't allow removal
          />
        </>
      )}

      {/* Date Chip Section */}
      {config.startDate && (
        <>
          <div className="text-[12px] font-semibold text-[#2B5380] mt-4">
            {intervalTypeToReadable(config.intervalType)?.toUpperCase() ??
              'DATE'}
          </div>
          <FilterChipSection
            removable={false}
            allSelected={false}
            items={[
              formatReportDate(dayjs(config.startDate), config.intervalType),
            ]}
            onRemove={() => {}} // Date is required, so we don't allow removal
          />
        </>
      )}

      {/* Measures Chip Section */}
      <div className="text-[12px] font-semibold text-[#2B5380] mt-4">
        MEASURES
      </div>
      <FilterChipSection
        allSelected={
          measures.length > 0 &&
          config.selectedMeasures.length === measures.length
        }
        items={config.selectedMeasures}
        onRemove={handleRemoveMeasure}
      />

      {/* Analysis Details Chip Section */}
      <div className="text-[12px] font-semibold text-[#2B5380] mt-4">
        ANALYSIS DETAILS
      </div>
      <FilterChipSection
        allSelected={false}
        noSelectionMessage={'No Analysis Details'}
        items={(() => {
          // IMPORTANT: Always use the universe from config directly
          // This ensures we show the original universe value from the saved report
          const orgTypeUniverse = config.organizationType ? config.universe : ''

          console.log('Organization Type Universe for chips:', orgTypeUniverse)

          const items = [
            ...(config.organizationType && orgTypeUniverse
              ? [orgTypeUniverse]
              : []),
            ...(config.filterByUniverse ? [config.filterByUniverse] : []),
            ...(config.selectedEntityId
              ? [
                  entityData?.entityDescriptions.find(
                    (entity) =>
                      entity.SourceContainerIdentifier ===
                      config.selectedEntityId
                  )?.Description || config.selectedEntityId,
                ]
              : []),
          ].filter(Boolean)

          return items
        })()}
        onRemove={() => {}} // Analysis details are required once set, so we don't allow removal
      />
    </>
  )
}

export default ACOFilters
