import React, { useState, useEffect, useMemo, useRef } from 'react'
import FilterSection from '@/components/filter/common/FilterSection'
import FilterChipSection from '@/components/filter/common/FilterChipSection'
import {
  Calendar,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Search,
} from 'lucide-react'
import { api } from '@/trpc/react'
import { BaseReportConfig } from '../common/ConfigureReport'
import dayjs, { Dayjs } from 'dayjs'
import { usePathname } from 'next/navigation'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { formatReportDate, getDecadeRange } from '@/lib/dateRange'

// Define the specific config for MVP reports
export interface MVPReportConfig extends BaseReportConfig {
  year: number
  mvp: string
  submissionGroup: string
}

interface MVPFiltersProps {
  configContext?: {
    config: MVPReportConfig
    setConfig: (config: Partial<MVPReportConfig>) => void
    isOpen?: boolean
  }
  expandedSections: string
  toggleSection: (section: string) => void
}

const MVPFilters: React.FC<MVPFiltersProps> = ({
  configContext,
  expandedSections,
  toggleSection,
}) => {
  const pathname = usePathname()
  const isMVPDetailsPage = pathname.includes('mvp-details')

  const datePickerRef = useRef<HTMLDivElement>(null)

  const [searchTerm, setSearchTerm] = useState('')
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(
    configContext?.config.year == 0
      ? null
      : dayjs().year(configContext?.config.year!)
  )

  const { config, setConfig, isOpen } = configContext || {
    config: { year: 0, mvp: '', submissionGroup: '' },
    setConfig: () => {},
    isOpen: false,
  }

  useEffect(() => {
    if (isOpen) {
      if (expandedSections !== 'DATE') toggleSection('DATE')
    }
  }, [isOpen])

  const { data: MVPsData, isLoading: isloadingMVPs } =
    api.report.getMVPs.useQuery(
      {},
      {
        enabled: isMVPDetailsPage,
        staleTime: 0, // Always fetch fresh data
      }
    )

  const { data: submissionGroupsData, isLoading: isloadingSubmissionGroups } =
    api.report.getSubmissionGroups.useQuery(
      {},
      {
        enabled: isMVPDetailsPage,
        staleTime: 0, // Always fetch fresh data
      }
    )

  const handleDateChange = (
    date: Dayjs | null,
    isSelection: boolean = true
  ) => {
    // Ensure the date is valid before updating
    if (date && date.isValid()) {
      let adjustedDate = date

      adjustedDate = date.startOf('year')

      setSelectedDate(adjustedDate)
      setConfig({ year: adjustedDate.year() })
      // Only close the date picker if this is an actual date selection, not navigation
      if (isSelection) {
        setIsDatePickerOpen(false)
      }
    }
  }

  const mvps = useMemo(() => {
    return MVPsData || []
  }, [MVPsData])

  const filteredMVPs = useMemo(
    () =>
      mvps
        .filter((mvp) =>
          mvp.MVPCategory.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .map((mvp) => mvp.MVPCategory),
    [mvps, searchTerm]
  )

  const submissionGroups = useMemo(() => {
    return submissionGroupsData || []
  }, [submissionGroupsData])

  const filteredSubmissionGroups = useMemo(
    () =>
      submissionGroups
        .filter((submissionGroup) =>
          submissionGroup.submissionGroupName
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
        )
        .map((submissionGroup) => submissionGroup.submissionGroupName),
    [submissionGroups, searchTerm]
  )

  return (
    <>
      {/* Year Section */}
      <FilterSection
        title="Year"
        expanded={expandedSections === 'DATE'}
        onToggle={() => toggleSection('DATE')}
        className="focus:outline-none"
      >
        <div
          className="relative"
          ref={datePickerRef}
          onMouseDown={(e) => {
            e.preventDefault()
            e.stopPropagation()
          }}
        >
          <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
            <PopoverTrigger asChild>
              <div
                data-testid="date-field-container"
                className="flex items-center border border-gray-300 rounded-md p-2 cursor-pointer"
              >
                <Calendar className="mr-2 text-gray-400" size={16} />
                <span className="text-[12px] flex-grow">
                  {selectedDate
                    ? formatReportDate(selectedDate, 'Y')
                    : 'Select date'}
                </span>
                {isDatePickerOpen ? (
                  <ChevronUp className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                )}
              </div>
            </PopoverTrigger>
            <PopoverContent
              className="w-auto p-0 border-0 rounded-lg shadow-lg"
              align="start"
            >
              <div className="p-[10px] bg-ui-pale-blue w-fit rounded-lg date-picker-popup shadow-[0px_0px_4px_0px_#00000040]">
                <div className="pt-4 bg-white">
                  <div className="flex justify-between items-center mx-8">
                    <div className="flex items-center text-ui-dark-gray font-bold w-full">
                      <button
                        onClick={() => {
                          const currentDecadeStart =
                            Math.floor(
                              (selectedDate?.year() || dayjs().year()) / 10
                            ) * 10
                          const newDate = (selectedDate || dayjs()).year(
                            currentDecadeStart - 10
                          )
                          handleDateChange(newDate, false)
                        }}
                        className="focus:outline-none"
                      >
                        <ChevronLeft className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                      </button>
                      <div className="text-ui-dark-gray font-semibold w-full text-center">
                        {selectedDate
                          ? getDecadeRange(selectedDate)
                          : getDecadeRange(dayjs())}
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        const currentDecadeStart =
                          Math.floor(
                            (selectedDate?.year() || dayjs().year()) / 10
                          ) * 10
                        const newDate = (selectedDate || dayjs()).year(
                          currentDecadeStart + 10
                        )
                        handleDateChange(newDate, false)
                      }}
                      className="focus:outline-none"
                    >
                      <ChevronRight className="h-4 w-4 text-ui-dark-gray font-semibold focus:outline-none" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-4 bg-white p-4 rounded-lg">
                  {Array.from({ length: 10 }, (_, i) => {
                    const decadeStart =
                      Math.floor(
                        (selectedDate?.year() || dayjs().year()) / 10
                      ) * 10
                    const year = decadeStart + i
                    return (
                      <button
                        key={year}
                        className={`rounded-none text-[12px] p-2 m-0 gap-[10px] font-[600] font-open-sans h-[30px] focus:outline-none 
                                  ${
                                    selectedDate && selectedDate.year() === year
                                      ? 'bg-ui-dark-gray text-white rounded-l rounded-r'
                                      : 'text-black'
                                  }
                                  hover:bg-[#EBEFFD] hover:text-[#2D7CBA]`}
                        onClick={() => {
                          const newDate = (selectedDate || dayjs())
                            .year(year)
                            .startOf('year')
                          handleDateChange(newDate)
                        }}
                      >
                        {year}
                      </button>
                    )
                  })}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </FilterSection>

      {isMVPDetailsPage && (
        <>
          <FilterSection
            title="MVP"
            expanded={expandedSections === 'MVP'}
            onToggle={() => toggleSection('MVP')}
            className="focus:outline-none"
          >
            <div className="space-y-3">
              <div className="relative mb-4">
                <input
                  key="mvpSearch"
                  autoFocus={true}
                  type="text"
                  placeholder="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-4 pr-8 py-2 border-b border-[#7396B0] text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
                  style={{
                    borderTop: 'none',
                    borderLeft: 'none',
                    borderRight: 'none',
                  }}
                />
                <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-[#7396B0]" />
              </div>

              <div className="space-y-2 max-h-48 overflow-y-auto mac-scrollbar">
                {isloadingMVPs ? (
                  <div className="flex justify-center items-center py-4">
                    <div
                      data-testid="spinner"
                      className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#2B5380]"
                    ></div>
                  </div>
                ) : filteredMVPs.length > 0 ? (
                  filteredMVPs.map((mvp, index) => (
                    <label
                      key={index}
                      className="flex items-center space-x-2 py-1"
                    >
                      <input
                        type="radio"
                        name="mvp"
                        checked={config.mvp === mvp}
                        onChange={() => {
                          setConfig({ mvp: mvp })
                        }}
                        className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
                      />
                      <span className="text-[12px]">{mvp}</span>
                    </label>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    {searchTerm
                      ? 'No MVP found matching your search'
                      : 'No MVPs available'}
                  </div>
                )}
              </div>
            </div>
          </FilterSection>

          <FilterSection
            title="SUBMISSION GROUPS"
            expanded={expandedSections === 'SUBMISSION_GROUPS'}
            onToggle={() => toggleSection('SUBMISSION_GROUPS')}
            className="focus:outline-none"
          >
            <div className="space-y-3">
              <div className="relative mb-4">
                <input
                  key="submisionGroupSearch"
                  autoFocus={true}
                  type="text"
                  placeholder="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-4 pr-8 py-2 border-b border-[#7396B0] text-[12px] font-normal leading-[16.34px] bg-transparent focus:outline-none placeholder-[#7396B0] h-[24px] w-full"
                  style={{
                    borderTop: 'none',
                    borderLeft: 'none',
                    borderRight: 'none',
                  }}
                />
                <Search className="absolute right-0 top-1 h-[11px] w-[11px] text-[#7396B0]" />
              </div>

              <div className="space-y-2 max-h-48 overflow-y-auto mac-scrollbar">
                {isloadingSubmissionGroups ? (
                  <div className="flex justify-center items-center py-4">
                    <div
                      data-testid="spinner"
                      className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#2B5380]"
                    ></div>
                  </div>
                ) : filteredSubmissionGroups.length > 0 ? (
                  filteredSubmissionGroups.map((submissionGroup, index) => (
                    <label
                      key={index}
                      className="flex items-center space-x-2 py-1"
                    >
                      <input
                        type="radio"
                        name="submissionGroup"
                        checked={config.submissionGroup === submissionGroup}
                        onChange={() => {
                          setConfig({ submissionGroup: submissionGroup })
                        }}
                        className="h-4 w-4 text-[#2B5380] accent-[#2B5380]"
                      />
                      <span className="text-[12px]">{submissionGroup}</span>
                    </label>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    {searchTerm
                      ? 'No Submission Group found matching your search'
                      : 'No Submission Groups available'}
                  </div>
                )}
              </div>
            </div>
          </FilterSection>
        </>
      )}
      {selectedDate && (
        <>
          <div className="text-[12px] font-semibold text-[#2B5380] mt-4">
            {'YEAR'}
          </div>
          <FilterChipSection
            removable={false}
            allSelected={false}
            items={[formatReportDate(dayjs(selectedDate), 'Y')]}
            onRemove={() => {}}
          />
        </>
      )}

      {isMVPDetailsPage && (
        <>
          {config.mvp && (
            <>
              <div className="text-[12px] font-semibold text-[#2B5380] mt-4">
                {'MVP'}
              </div>
              <FilterChipSection
                removable={false}
                allSelected={false}
                items={[config.mvp]}
                onRemove={() => {}}
              />
            </>
          )}
          {config.submissionGroup && (
            <>
              <div className="text-[12px] font-semibold text-[#2B5380] mt-4">
                {'SUBMISSION GROUP'}
              </div>
              <FilterChipSection
                removable={false}
                allSelected={false}
                items={[config.submissionGroup!]}
                onRemove={() => {}}
              />
            </>
          )}
        </>
      )}
    </>
  )
}

export default MVPFilters
