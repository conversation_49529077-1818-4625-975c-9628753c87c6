import { flexRender, Table } from '@tanstack/react-table'
import React from 'react'
import { Report } from '@/types/reports/medisolvReport'
import { Pagination } from '../measures/pagination'

type Props<Data> = {
  table: Table<Data>
  deleteDialogOpen: string | null
  onCancelDelete: () => void
  onConfirmDelete: (reportId: string) => void
}

const ReportsDataTable = <Data,>({
  table,
  deleteDialogOpen,
  onCancelDelete,
  onConfirmDelete,
}: Props<Data>) => {
  // Header style for tables - matches ACOPerformanceAnalysisTable
  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 12px',
    textAlign: 'left',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
  } as React.CSSProperties

  return (
    <div className="flex flex-col w-full font-open-sans">
      <div className="mac-scrollbar">
        <table
          className="w-full"
          style={{ borderCollapse: 'separate', borderSpacing: '0 10px' }}
        >
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  // Skip rendering the isFavorite column header
                  if (header.id.includes('isFavorite')) {
                    return null
                  }

                  return (
                    <th
                      key={header.id}
                      colSpan={header.colSpan}
                      style={{
                        ...headerStyle,
                        minWidth: header.id.includes('type')
                          ? '100px'
                          : header.id.includes('actions')
                            ? '50px'
                            : header.id.includes('name')
                              ? '300px'
                              : header.id.includes('lastModified')
                                ? '150px'
                                : '150px',
                        maxWidth: header.id.includes('type')
                          ? '100px'
                          : header.id.includes('actions')
                            ? '50px'
                            : header.id.includes('name')
                              ? '300px'
                              : header.id.includes('lastModified')
                                ? '150px'
                                : undefined,
                        width: header.id.includes('type')
                          ? '100px'
                          : header.id.includes('actions')
                            ? '50px'
                            : header.id.includes('name')
                              ? '300px'
                              : header.id.includes('lastModified')
                                ? '150px'
                                : undefined,
                        position: 'sticky',
                        top: 0,
                        zIndex: 10,
                      }}
                    >
                      {header.isPlaceholder ? null : (
                        <div className="flex items-center">
                          {header.column.id === 'name' ? (
                            <span className="ml-[26px] text-left text-[11px]">
                              {flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                            </span>
                          ) : (
                            <span className="text-left text-[11px]">
                              {flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                            </span>
                          )}
                        </div>
                      )}
                    </th>
                  )
                })}
              </tr>
            ))}
          </thead>
          <tbody className="text-[12px]">
            {table.getRowModel().rows.map((row) => (
              <tr
                key={row.id}
                className={`bg-white relative ${deleteDialogOpen === (row.original as Report).rowKey ? 'delete-row' : ''}`}
                style={{
                  position: 'relative',
                  backgroundColor:
                    deleteDialogOpen === (row.original as Report).rowKey
                      ? 'rgba(249, 248, 249, 0.7)'
                      : '#F9F8F9',
                  height: '45px', // Fixed height to prevent size changes
                }}
              >
                {row.getVisibleCells().map((cell) => {
                  // Skip rendering the isFavorite column cell
                  if (cell.column.id.includes('isFavorite')) {
                    return null
                  }

                  return (
                    <td
                      key={cell.id}
                      className={`px-3 py-2 align-middle font-open-sans font-normal ${
                        deleteDialogOpen === (row.original as Report).rowKey
                          ? 'relative'
                          : ''
                      } ${deleteDialogOpen === (row.original as Report).rowKey && cell.column.id !== 'actions' ? 'opacity-30' : ''}`}
                      style={{
                        maxWidth: cell.column.id.includes('type')
                          ? '100px'
                          : cell.column.id.includes('actions')
                            ? '50px'
                            : cell.column.id.includes('name')
                              ? '300px'
                              : cell.column.id.includes('lastModified')
                                ? '150px'
                                : undefined,
                        width: cell.column.id.includes('type')
                          ? '100px'
                          : cell.column.id.includes('actions')
                            ? '50px'
                            : cell.column.id.includes('name')
                              ? '300px'
                              : cell.column.id.includes('lastModified')
                                ? '150px'
                                : undefined,
                        backgroundColor:
                          deleteDialogOpen === (row.original as Report).rowKey
                            ? 'transparent'
                            : '#F9F8F9',
                        height: '45px', // Fixed height to prevent size changes
                      }}
                    >
                      {deleteDialogOpen === (row.original as Report).rowKey &&
                      cell.column.id === 'actions' ? (
                        <div className="absolute right-0 top-0 bottom-0 left-[-300px] z-20 flex items-center justify-end">
                          <div className="bg-[#FAF9FA] py-2 px-3 flex items-center gap-3">
                            <span className="text-black font-bold text-xs">
                              Are you sure?
                            </span>
                            <button
                              className="px-4 py-2 text-[#1B4A70] underline hover:text-[#173E5D]"
                              onClick={onCancelDelete}
                            >
                              Cancel
                            </button>
                            <button
                              className="px-[15px] py-[6px] bg-[#566582] text-white rounded-lg text-2xs hover:bg-[#455471] uppercase font-medium"
                              onClick={() =>
                                onConfirmDelete((row.original as Report).rowKey)
                              }
                            >
                              YES, DELETE
                            </button>
                          </div>
                        </div>
                      ) : (
                        flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )
                      )}
                    </td>
                  )
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {table.getPageCount() > 1 && <Pagination table={table} />}
    </div>
  )
}

export default ReportsDataTable
