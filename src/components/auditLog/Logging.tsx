'use client'

import React, { useEffect, useState } from 'react'
import { AppInsightsProvider } from '@/components/providers/AppInsightsProvider'
import { SessionProvider, useSession } from 'next-auth/react'
import { TRPCReactProvider } from '@/trpc/react'
import { CustomToaster } from '@/components/ui/CustomToaster'
import { usePathname, useSearchParams } from 'next/navigation'
import { api } from '@/trpc/react'

type LoggingProps = {
  children: React.ReactNode
  session: any
  cookies: any
}

// Inner component to handle the actual logging
export const AuditLogger = ({ children }: { children: React.ReactNode }) => {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const session = useSession()
  const [prevPath, setPrevPath] = useState<string | null>(null)

  // Get the full URL including search params
  const fullUrl = `${pathname}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`

  // Create mutation for posting audit logs
  const postAuditLog = api.admin.postAuditLogs.useMutation()

  useEffect(() => {
    // Only log if the path has changed and we have a session
    if (prevPath !== fullUrl && session.data && pathname) {
      try {
        const auditLogData = {
          url: fullUrl,
          userAgent: navigator.userAgent,
          pageTitle: document.title || pathname,
          actionType: 'PAGE_NAVIGATION',
        }

        // Log the page navigation
        postAuditLog.mutate(auditLogData, {
          onSuccess: () => {
            console.debug('Audit log recorded successfully')
          },
          onError: (error) => {
            console.error('Failed to log audit event:', error)
          },
        })
      } catch (error) {
        console.error('Error in audit logging:', error)
      }

      // Update previous path regardless of success/failure
      setPrevPath(fullUrl)
    }
  }, [fullUrl, session.data, postAuditLog, pathname])

  return <>{children}</>
}

export const Logging = ({ children, session, cookies }: LoggingProps) => {
  return (
    <div className="flex flex-col min-h-screen">
      <AppInsightsProvider>
        <TRPCReactProvider cookies={cookies}>
          <SessionProvider session={session}>
            <AuditLogger>{children}</AuditLogger>
          </SessionProvider>
        </TRPCReactProvider>
      </AppInsightsProvider>
      <CustomToaster />
    </div>
  )
}

export default Logging
