'use client'
import { api } from '@/trpc/react'
import { cn } from '@/lib/utils'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useEffect, useMemo, useState, useContext, useRef } from 'react'
import { IoMdSkipForward } from 'react-icons/io'
import { IoCaretForward } from 'react-icons/io5'
import { GeneratePageNumbers } from '../dataTable/generatePageNumbers'
import { Search, HelpCircle, Download } from 'lucide-react'
import React from 'react'
import { useViewStore } from '@/stores/viewStore'
import { format } from 'date-fns'
import { DateRangeContext } from '@/app/(admin)/dateRangeContext'

const AuditLogGrid = () => {
  // Use the shared context instead of local state
  const { startDate, endDate, exportDataRef } = useContext(DateRangeContext)

  // Initialize filteredData with auditLogs
  const [filteredData, setFilteredData] = useState<typeof auditLogs>([])
  const [itemsPerPage, setItemsPerPage] = useState(10)
  //for search row
  const [searchInputs, setSearchInputs] = useState<{ [key: string]: string }>(
    {}
  )

  const { setTableState, tableState } = useViewStore()
  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
  } as React.CSSProperties

  const [filters, setFilters] = useState({
    startTimeMMDDYYYY: '',
    endTimeMMDDYYYY: '',
  })

  // Initialize filters with today's date if startDate and endDate are available
  useEffect(() => {
    if (startDate && endDate) {
      setFilters({
        startTimeMMDDYYYY: format(startDate, 'MMddyyyy'),
        endTimeMMDDYYYY: format(endDate, 'MMddyyyy'),
      })
    }
  }, [startDate, endDate])

  // API call using the filters with refetch capability
  const { data: auditLogs = [], refetch } = api.admin.getAuditLogs.useQuery(
    filters,
    {
      enabled: !!(filters.startTimeMMDDYYYY && filters.endTimeMMDDYYYY),
      suspense: true,
    }
  )

  // Trigger refetch when filters change
  useEffect(() => {
    if (filters.startTimeMMDDYYYY && filters.endTimeMMDDYYYY) {
      refetch()
    }
  }, [filters, refetch])

  // Update filteredData when auditLogs changes
  useEffect(() => {
    setFilteredData(auditLogs)
  }, [auditLogs])

  // Memoize the export data
  const exportData = useMemo(() => {
    if (filteredData.length === 0) {
      return { headers: [], dataset: [] };
    }
    return {
      headers: ['userName', 'userEmail', 'organization', 'url', 'auditDateTime'],
      dataset: filteredData,
    };
  }, [filteredData]);

  // Update export data when filteredData changes
  useEffect(() => {
    if (exportDataRef) {
      exportDataRef.current = exportData;
    }
  }, [exportData, exportDataRef]);

  const columnsDef = [
    {
      accessorKey: 'userName',
      header: 'User Name',
      size: 100,
    },
    {
      accessorKey: 'userEmail',
      header: 'User Email',
      size: 100,
    },
    {
      accessorKey: 'organization',
      header: 'Organization',
      size: 100,
    },
    {
      accessorKey: 'url',
      header: 'URL',
      size: 100,
    },
    {
      accessorKey: 'auditDateTime',
      header: 'Audit Date Time',
      size: 100,
    },
  ] as ColumnDef<any>[]

  const auditLogsColumns = useMemo(() => {
    return columnsDef
  }, [auditLogs])

  // 🔹 Filter Data Whenever `searchInputs` Change
  useEffect(() => {
    if (!auditLogs) return

    const filtered = auditLogs.filter((row) =>
      Object.entries(searchInputs).every(([columnId, searchValue]) =>
        searchValue
          ? (row[columnId as keyof typeof row] as string)
            ?.toLowerCase()
            .includes(searchValue.toLowerCase())
          : true
      )
    )

    setFilteredData(filtered)
  }, [searchInputs, auditLogs])

  const table = useReactTable({
    data: filteredData,
    columns: auditLogsColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onStateChange: () => setTableState(table.getState()),
    initialState: { pagination: { pageSize: itemsPerPage } },
  })

  const handleFilterChange = (columnId: string) => {
    const searchValue = searchInputs[columnId]?.toLowerCase() || ''

    if (!searchValue) {
      setFilteredData(auditLogs) // Reset table to show all logs when search box is cleared
      return
    }

    const newFilteredData = auditLogs[0]
    setFilteredData(
      Array.isArray(newFilteredData) && newFilteredData.length > 0
        ? newFilteredData.filter(
          (
            item
          ): item is {
            userName: string
            url: string
            userEmail: string
            organization: string
            auditDateTime: string
          } => item !== undefined
        )
        : []
    )
  }


  // Update the export data whenever filteredData changes
  useEffect(() => {
    if (exportDataRef && exportDataRef.current) {
      exportDataRef.current = {
        headers: columnsDef.map((col) => col.header as string),
        dataset: filteredData.map((row) => {
          const rowData: Record<string, any> = {}
          columnsDef.forEach((col) => {
            const key = col.header?.toString() ?? ''
            const value =
              'accessorKey' in col
                ? row[col.accessorKey as keyof typeof row]
                : row[col.header as keyof typeof row]
            rowData[key] = value ?? ''
          })
          return rowData
        }),
      }
    }
  }, [filteredData, columnsDef, exportDataRef])

  return (
    <div className="flex flex-col w-full h-full font-open-sans text-sm">
      {/* Help bar with date range and export */}


      {/* Title section */}
      <div className="w-full text-center py-4 bg-white">
        <h2 className="text-[28px] font-bold font-open-sans">Audit Logs</h2>
      </div>

      {/* Table section - full width with no horizontal padding */}
      <div className="w-full h-full overflow-hidden">
        <div className={cn(
          'overflow-y-auto',
          'border-x-[#DDDCDF]',
          'border-x-[1px]',
          'border-t-[#DDDCDF]',
          'border-t-[1px]',
          'mac-scrollbar'
        )}
        >
          <table className="w-full border-collapse">
            <thead>
              {table?.getHeaderGroups && table.getHeaderGroups()?.length > 0 ? (
                <>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <React.Fragment key={headerGroup.id}>
                      <tr>
                        {headerGroup.headers.map((header) => (
                          <th
                            key={header.id}
                            style={{
                              ...headerStyle,
                              position: 'sticky',
                              top: 0,
                              zIndex: 10,
                            }}
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                          </th>
                        ))}
                      </tr>

                      <tr>
                        {headerGroup.headers.map((header) => (
                          <th
                            key={`filter-${header.id}`}
                            className="p-0 bg-white"
                            style={{
                              borderTop: 'none',
                              borderBottom: '1px solid #DDDCDF',
                            }}
                          >
                            <div className="relative">
                              <input
                                type="text"
                                placeholder="Search"
                                value={searchInputs[header.column.id] || ''}
                                onChange={(e) =>
                                  setSearchInputs((prev) => ({
                                    ...prev,
                                    [header.column.id]: e.target.value,
                                  }))
                                }
                                className="p-2 pr-8 w-full focus:outline-none bg-white text-[#4B5563]"
                                style={{
                                  fontWeight: 'normal',
                                  border: 'none',
                                  borderBottom: '1px solid #DDDCDF',
                                  color: '#1B4A70',
                                }}
                              />
                              <button
                                onClick={() =>
                                  handleFilterChange(header.column.id)
                                }
                                className="absolute right-2 top-1/2 transform -translate-y-1/2"
                              >
                                <Search className="h-4 w-4 text-[#1B4A70] cursor-pointer" />
                              </button>
                            </div>
                          </th>
                        ))}
                      </tr>
                    </React.Fragment>
                  ))}
                </>
              ) : null}
            </thead>
            <tbody>
              {table.getRowModel().rows.length > 0 ? (
                table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="border-b hover:bg-gray-50">
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="px-4 py-3 text-[#333333]">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={columnsDef.length} className="p-4 text-center">
                    No matching results found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        <div className="px-4 py-2 border-t border-[#DDDCDF] flex items-center justify-between bg-white">
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <button
                className="px-2 py-1 rounded-full text-black hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <IoMdSkipForward size={16} className="rotate-180" />
              </button>
              <button
                className="px-2 py-1 rounded-full text-black hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <IoCaretForward size={16} className="rotate-180" />
              </button>
              <ul className="flex items-center mx-2">
                <GeneratePageNumbers table={table} />
              </ul>
              <button
                className="px-2 py-1 rounded-full text-black hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <IoCaretForward size={16} />
              </button>
              <button
                className="px-2 py-1 rounded-full text-black hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <IoMdSkipForward size={16} />
              </button>

              <select
                value={table.getState().pagination.pageSize}
                onChange={(e) => {
                  table.setPageSize(Number(e.target.value))
                }}
                className="ml-4 p-1 text-[14px] font-open-sans text-black focus:outline-none border border-gray-200 rounded"
              >
                {[10, 25, 50, 100].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
              <span className="text-[14px] font-open-sans text-black ml-2">
                Items per page
              </span>
            </div>
          </div>

          <div className="text-[14px] font-open-sans text-gray-600">
            {`${table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}-${Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, filteredData.length)} of ${filteredData.length} results`}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AuditLogGrid
