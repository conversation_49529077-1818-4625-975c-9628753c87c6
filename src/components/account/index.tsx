'use client'

import Image from 'next/image'
import { ReactNode, useEffect, useRef, useState } from 'react'
import personIcon from '../../../public/images/person.svg'
import lockIcon from '../../../public/images/lock.svg'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import {
  ColumnDef,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import React from 'react'
import { Card, CardContent } from '../ui/card'
import BasicDataTable from '../dataTable/basicDataTable'
import Loader from '../ui/Loader'
import { api } from '@/trpc/react'
import { OrganizationRole } from '@/types/organizationRole'
import { User } from '@/types/user'
import { toast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'

type MenuItem = {
  id: number
  label: string
  icon: ReactNode
}

enum AccountType {
  PERSONAL_INFO = 1,
  ACCESS = 2,
}

const AccountItems: MenuItem[] = [
  {
    id: AccountType.PERSONAL_INFO,
    label: 'PERSONAL INFO',
    icon: <Image src={personIcon} alt="check" width={18} height={18} />,
  },
  {
    id: AccountType.ACCESS,
    label: 'ACCESS',
    icon: <Image src={lockIcon} alt="check" width={18} height={18} />,
  },
]

export const MyAccount = () => {
  const [accountType, setAccountType] = useState(1)

  return (
    <div className="flex h-full w-full">
      <ul className="w-48 relative top-0 pl-3">
        {AccountItems.map((item) => (
          <li
            key={item.id}
            onClick={() => {
              setAccountType(item.id)
            }}
            className={`w-full px-3 py-3 text-left flex items-center space-x-2 transition-colors font-semibold text-[14px] hover:cursor-pointer
              ${
                accountType === item.id
                  ? 'bg-ui-pale-blue text-[#074880] border-r-[#2970A7] border-r-[4px]'
                  : 'text-ui-dark-gray hover:bg-gray-50'
              }`}
          >
            <span className="text-right flex justify-end w-full font-open-sans text-[13px] font-semibold ]">
              <div className="mr-2">{item.icon}</div>
              <div className="nowrap">{item.label}</div>
            </span>
          </li>
        ))}
      </ul>
      <div className="flex-1 bg-transparent">
        <div className="rounded-[10px] border-gray-300 border">
          {accountType === AccountType.PERSONAL_INFO ? (
            <AccountInfo />
          ) : (
            <AccountAccess />
          )}
        </div>
      </div>
    </div>
  )
}

export const AccountInfo = () => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [profileImage, setProfileImage] = useState<any>(null)
  const [userInfo, setUserInfo] = useState<User | null>(null)

  const userInfoQuery = api.users.getUserInfo.useQuery()
  const updateUserInfoMutation = api.users.updateUserInfo.useMutation()

  useEffect(() => {
    if (userInfoQuery.data) {
      setUserInfo(userInfoQuery.data)
      if (userInfoQuery.data.profileImage) {
        setProfileImage(userInfoQuery.data.profileImage)
      }
    }
  }, [
    userInfoQuery.data?.profileImage,
    userInfoQuery.data?.displayName,
    userInfoQuery.data?.cellPhone,
    userInfoQuery.data?.workPhone,
    userInfoQuery.data?.homePhone,
    userInfoQuery.data?.emailAddress,
  ])

  const handleEditClick = () => {
    fileInputRef!.current!.click()
  }

  const handleFileChange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()

      reader.onload = () => {
        const binaryData = reader.result
        setProfileImage(binaryData)
      }

      reader.readAsDataURL(file)
    }
  }

  const handleDeleteImage = () => {
    setProfileImage(null)
  }

  const handleSaveClick = async () => {
    if (!userInfo?.displayName) {
      toast({
        title: 'Enter the valid display name',
        variant: 'default',
      })
      return
    }

    if (!userInfo?.cellPhone) {
      toast({
        title: 'Enter the valid cell phone number',
        variant: 'default',
      })
      return
    }
    try {
      await updateUserInfoMutation.mutateAsync({
        displayName: userInfo.displayName!,
        emailAddress: userInfo.emailAddress!,
        profileImageUrl: profileImage || '', // Ensure it's a string, use empty string if null
        cellPhoneNumber: userInfo.cellPhone || '',
        workPhoneNumber: userInfo.workPhone || '',
        homePhoneNumber: userInfo.homePhone || '',
      })

      toast({
        className: cn('bg-green-600 text-white border-green-600'),
        title: 'Profile updated successfully',
        variant: 'default',
      })
    } catch (error) {
      toast({
        title: 'Server error',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      })
    }
  }

  if (userInfoQuery.isPending || updateUserInfoMutation.isPending) {
    return (
      <div className="overflow-x-auto relative mt-10">
        <Loader />
      </div>
    )
  }

  return (
    <div className="flex w-full font-open-sans text-sm ml-4 p-4">
      <div>
        <div className="text-left">
          <Label
            htmlFor="screen-name"
            className="font-semibold text-xs text-[#205782]"
          >
            SCREEN NAME*
          </Label>
          <Input
            className="w-[300px] mt-1"
            id="screen-name"
            placeholder="Name"
            value={userInfo?.displayName ?? ''}
            onChange={(e) => {
              setUserInfo({ ...userInfo, displayName: e.target.value })
            }}
          />
        </div>
        <div className="text-left pt-6">
          <Label
            htmlFor="work-email"
            className="font-semibold text-xs text-[#205782]"
          >
            WORK EMAIL*
          </Label>
          <Input
            id="work-email"
            className="w-[300px] mt-1"
            value={userInfo?.emailAddress ?? ''}
            disabled={true}
            onChange={(e) => {
              setUserInfo({ ...userInfo, emailAddress: e.target.value })
            }}
          />
        </div>
        <div className="text-left pt-6">
          <Label
            htmlFor="work-phone"
            className="font-semibold text-xs text-[#205782] "
          >
            WORK PHONE NUMBER
          </Label>
          <Input
            id="work-phone"
            type="text"
            className="w-[300px] mt-1"
            value={userInfo?.workPhone ?? ''}
            onChange={(e) => {
              setUserInfo({ ...userInfo, workPhone: e.target.value })
            }}
          />
        </div>
        <div className="text-left pt-6">
          <Label
            htmlFor="cell-phone"
            className="font-semibold text-xs text-[#205782]"
          >
            CELL PHONE NUMBER
          </Label>
          <Input
            id="cell-phone"
            className="w-[300px] mt-1"
            value={userInfo?.cellPhone ?? ''}
            onChange={(e) => {
              setUserInfo({ ...userInfo, cellPhone: e.target.value })
            }}
          />
        </div>
        <div className="text-left pt-6">
          <Label
            htmlFor="home-phone"
            className="font-semibold text-xs text-[#205782]"
          >
            HOME PHONE NUMBER
          </Label>
          <Input
            id="home-phone"
            className="w-[300px] mt-1"
            value={userInfo?.homePhone ?? ''}
            onChange={(e) => {
              setUserInfo({ ...userInfo, homePhone: e.target.value })
            }}
          />
        </div>

        <Button
          className="w-full mt-4 bg-[#205782] font-semibold"
          id="save-button"
          onClick={handleSaveClick}
        >
          SAVE
        </Button>
      </div>
      <div className="ml-24 mt-4">
        <div className="flex flex-col items-center mt-4">
          <Image
            id="profileImage"
            src={profileImage ?? '/images/blank-profile.png'}
            alt="Profile Image"
            width={200}
            height={200}
            className="rounded-full"
          />
          <input
            id="profileImgSelector"
            type="file"
            hidden={true}
            ref={fileInputRef}
            value={''}
            onChange={handleFileChange}
            accept="image/*"
          />
          <div className="flex space-x-4 mt-3">
            <a
              href="#"
              className="text-sm text-blue-500 hover:underline"
              onClick={handleEditClick}
            >
              EDIT
            </a>
            <span>|</span>
            <a
              href="#"
              className="text-sm text-blue-500 hover:underline"
              onClick={handleDeleteImage}
            >
              DELETE
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

const AccountAccess = () => {
  const accessQuery = api.users.getUserAcceses.useQuery()

  const [accessData, setAccessData] = React.useState<OrganizationRole[]>([])

  useEffect(() => {
    if (accessQuery.data) {
      setAccessData(accessQuery.data)
    }
  }, [accessQuery.data?.length])

  const columns: ColumnDef<any>[] = React.useMemo(
    () => [
      {
        accessorKey: 'organizationName',
        header: 'Organization',
        size: 500,
        cell: (info: any) => (
          <div className="text-left">{String(info.getValue())}</div>
        ),
      },
      {
        accessorKey: 'roles',
        header: 'Access Type(s)',
        size: 500,
        enableColumnFilter: false,
        cell: (info: any) => (
          <div className="text-left">{String(info.getValue())}</div>
        ),
      },
    ],
    []
  )

  const table = useReactTable({
    data: accessData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 20,
      },
    },
  })

  return (
    <Card className="pt-5 mt-4 border-none">
      <CardContent className="mt-5">
        {accessQuery.isPending ? (
          <div className="overflow-x-auto relative mt-10">
            <Loader />
          </div>
        ) : (
          <div>
            <BasicDataTable table={table} />
          </div>
        )}
      </CardContent>
    </Card>
  )
}
