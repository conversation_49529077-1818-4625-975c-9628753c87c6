'use client'

import { Search } from 'lucide-react'
import { Input } from './ui/input'
import { Button } from './ui/button'
import { cn } from '@/lib/utils'

type InputWithButtonProps = {
  query: string
  setQuery: (query: string) => void
  onSearch: () => void
  className?: string
} & React.InputHTMLAttributes<HTMLInputElement>

const InputWithButton = ({
  onSearch,
  query,
  setQuery,
  className,
  ...props
}: InputWithButtonProps) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      onSearch()
    }
  }

  return (
    <div className={cn('relative flex items-center', className)}>
      <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
      <Input
        id="search"
        name="search"
        value={query}
        autoComplete="off"
        autoCorrect="off"
        autoCapitalize="off"
        spellCheck="false"
        autoFocus={false}
        type="search"
        onChange={(e) => setQuery(e.target.value)}
        onKeyDown={handleKeyDown}
        className="pl-9 pr-[5rem] h-[37px]  font-open-sans font-normal"
        placeholder="Search..."
        {...props}
      />
      <Button
        type="button"
        variant="ghost"
        className="absolute font-open-sans font-semibold right-2 h-[70%] px-3 py-2 bg-blue-3 hover:bg-blue-2 text-white hover:text-white"
        onClick={onSearch}
      >
        Search
      </Button>
    </div>
  )
}

export default InputWithButton
