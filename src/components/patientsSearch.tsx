'use client'

import { useEffect, useState } from 'react'
import InputWithButton from './inputWithButton'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from '@/hooks/use-toast'

type Props = {
  useFullWidth?: boolean
}

const PatientsSearch = ({ useFullWidth }: Props) => {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    if (searchParams.get('search') && searchParams.get('type') === 'partners') {
      setSearchTerm(searchParams.get('search')!)
    }
  }, [searchParams.get('search'), searchParams.get('type')])

  return (
    <InputWithButton
      className={useFullWidth ? 'w-full' : 'max-w-[500px]'}
      query={searchTerm}
      setQuery={setSearchTerm}
      onSearch={() => {
        if (!searchTerm) {
          toast({
            variant: 'destructive',
            title: 'Please enter valid search text to proceed.',
          })
          return
        }

        const params = new URLSearchParams(searchParams)
        params.set('search', searchTerm)
        params.set('type', 'partners')

        router.push(`/explorer/patients/results?${params}`)
      }}
    />
  )
}

export default PatientsSearch
