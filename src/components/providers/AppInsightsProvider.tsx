'use client'

import { <PERSON>actN<PERSON>, useEffect } from 'react'
import { ApplicationInsights } from '@microsoft/applicationinsights-web'
import {
  ReactPlugin,
  AppInsightsContext,
} from '@microsoft/applicationinsights-react-js'
import { ClickAnalyticsPlugin } from '@microsoft/applicationinsights-clickanalytics-js'
import { env } from '@/env'

export function AppInsightsProvider({ children }: { children: ReactNode }) {
  const reactPlugin = new ReactPlugin()
  useEffect(() => {
    const clickPluginInstance = new ClickAnalyticsPlugin()
    const clickPluginConfig = {
      autoCapture: true,
    }
    const appInsights = new ApplicationInsights({
      config: {
        connectionString: env.NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING,
        extensions: [reactPlugin, clickPluginInstance],
        extensionConfig: {
          [reactPlugin.identifier]: {},
          [clickPluginInstance.identifier]: clickPluginConfig,
        },
      },
    })
    appInsights.loadAppInsights()

    return () => {
      appInsights.unload()
    }
  }, [])

  return (
    <AppInsightsContext.Provider value={reactPlugin}>
      {children}
    </AppInsightsContext.Provider>
  )
}
