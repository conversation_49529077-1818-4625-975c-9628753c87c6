import dayjs from 'dayjs'
import Image from 'next/image'
import { env } from '@/env'
import DynamicFooterContent from './DynamicFooterContent'

const Footer = () => {
  return (
    <footer className="font-open-sans bg-white border-t border-t-blue-3 text-[13px] text-blue-3 px-16 py-8 flex justify-between flex-none">
      <div className="flex items-end uppercase">
        <span className="leading-[.8]">
          {env.NEXT_PUBLIC_APP_VERSION} &copy; {dayjs().format('YYYY')}
        </span>
      </div>

      <div className="flex flex-col items-end gap-4">
        <DynamicFooterContent />

        <div className="flex items-end gap-2 uppercase">
          <span className="leading-[.8]">Powered By:</span>
          <Image
            src="/images/<EMAIL>"
            alt="logo"
            width={90}
            height={28}
          />
        </div>
      </div>
    </footer>
  )
}

export default Footer