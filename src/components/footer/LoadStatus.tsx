'use client'

import { InfoIcon } from 'lucide-react'
import {
  <PERSON><PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import dayjs from 'dayjs'
import { useUserSessionStore } from '@/stores/userSession'
import { api } from '@/trpc/react'
import { useMemo } from 'react'

export const LoadStatus = () => {
  const { primaryMeasureType, organizationId } = useUserSessionStore()

  const loadStatusResponseQuery = api.loadStatusInfo.get.useQuery({
    primaryMeasureType,
  })

  const loadTooltipContent = () => {
    if (
      loadStatusResponseQuery.data?.showLoadStatus &&
      loadStatusResponseQuery.data.encorELoadDataStatus.isDataAvailable
    ) {
      // Case 1: Show full load info and calculation dates
      return (
        <>
          <p>
            <span className="font-bold">Start Date:</span>{' '}
            {dayjs(
              loadStatusResponseQuery.data.loadStatusInfo?.startDate
            ).format('MM/DD/YYYY hh:mm A')}
          </p>
          <p>
            <span className="font-bold">End Date:</span>{' '}
            {dayjs(loadStatusResponseQuery.data.loadStatusInfo?.endDate).format(
              'MM/DD/YYYY hh:mm A'
            )}
          </p>
          <p>
            <span className="font-bold">Total Duration:</span>{' '}
            {loadStatusResponseQuery.data.loadStatusInfo?.timeDuration}
          </p>
          <p>
            <span className="font-bold">Status:</span>{' '}
            {loadStatusResponseQuery.data.loadStatusInfo?.successStatus}
          </p>
          <p>
            <span className="font-bold">Calculations Completion Date:</span>{' '}
            {dayjs(
              loadStatusResponseQuery.data.encorELoadDataStatus.startDate
            ).format('MM/DD/YYYY hh:mm A')}
          </p>
          <p className="text-red-500">
            {
              loadStatusResponseQuery.data.loadStatusInfo
                ?.procedureRunningStatus
            }
          </p>
        </>
      )
    } else if (
      loadStatusResponseQuery.data?.showLoadStatus &&
      !loadStatusResponseQuery.data.encorELoadDataStatus.isDataAvailable
    ) {
      // Case 2: Show load info without calculation dates
      return (
        <>
          <p>
            <span className="font-bold">Start Date:</span>{' '}
            {dayjs(
              loadStatusResponseQuery.data.loadStatusInfo?.startDate
            ).format('MM/DD/YYYY hh:mm A')}
          </p>
          <p>
            <span className="font-bold">End Date:</span>{' '}
            {dayjs(loadStatusResponseQuery.data.loadStatusInfo?.endDate).format(
              'MM/DD/YYYY hh:mm A'
            )}
          </p>
          <p>
            <span className="font-bold">Total Duration:</span>{' '}
            {loadStatusResponseQuery.data.loadStatusInfo?.timeDuration}
          </p>
          <p>
            <span className="font-bold">Status:</span>{' '}
            {loadStatusResponseQuery.data.loadStatusInfo?.successStatus}
          </p>
          <p className="text-red-500">
            {
              loadStatusResponseQuery.data.loadStatusInfo
                ?.procedureRunningStatus
            }
          </p>
        </>
      )
    } else if (
      !loadStatusResponseQuery.data?.showLoadStatus &&
      loadStatusResponseQuery.data?.encorELoadDataStatus.isDataAvailable
    ) {
      // Case 3: Loads are running or not, show calculation dates
      if (loadStatusResponseQuery.data.areLoadsRunning) {
        return (
          <>
            <p>
              <b>Loads are currently running</b>
            </p>
            <p>
              <span className="font-bold">Calculations Completion Date:</span>{' '}
              {dayjs(
                loadStatusResponseQuery.data.encorELoadDataStatus.startDate
              ).format('MM/DD/YYYY hh:mm A')}
            </p>
            <p className="text-red-500">
              {
                loadStatusResponseQuery.data.loadStatusInfo
                  ?.procedureRunningStatus
              }
            </p>
          </>
        )
      } else {
        return (
          <>
            <p>
              <span className="font-bold">Calculations Completion Date:</span>{' '}
              {dayjs(
                loadStatusResponseQuery.data.encorELoadDataStatus.startDate
              ).format('MM/DD/YYYY hh:mm A')}
            </p>
            <p className="text-red-500">
              {
                loadStatusResponseQuery.data.loadStatusInfo
                  ?.procedureRunningStatus
              }
            </p>
          </>
        )
      }
    } else if (loadStatusResponseQuery.data?.areLoadsRunning) {
      // Case 4: Loads are currently running, no load status or data available
      return (
        <>
          <p>
            <b>Loads are currently running</b>
          </p>
        </>
      )
    }

    return null // Default case: No tooltip content
  }

  const tooltipContent = useMemo(
    () => loadTooltipContent(),
    [organizationId, loadStatusResponseQuery.data]
  )

  return (
    <TooltipProvider>
      <Tooltip delayDuration={0}>
        <TooltipTrigger className="bg-transparent flex font-semibold items-center gap-2 text-blue-3 text-[11px]">
          <InfoIcon className="h-4 w-4" />
          <span>Load Status</span>
        </TooltipTrigger>
        <TooltipContent
          align="end"
          alignOffset={0}
          className="border-0 border-t-8 border-t-blue-3 p-4 bg-[#F7F7F7]"
        >
          {tooltipContent}
          {/* <Arrow width={'100px'} /> */}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export default LoadStatus
