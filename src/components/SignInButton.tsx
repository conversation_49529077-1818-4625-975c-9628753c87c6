'use client'
import { useState } from 'react'
import { useTransition } from 'react'
import login from '@/actions/login'
import { Button } from '@/components/ui/button'

const SignInButton = () => {
  const [isPending, startTransition] = useTransition()
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setError(null)
    startTransition(async () => {
      try {
        await login()
      } catch (err) {
        setError('Sign in failed. Please try again.')
      }
    })
  }

  return (
    <form onSubmit={handleSubmit}>
      <Button
        className="w-[120px] h-[53px] bg-[#cd9557] border border-[#cd9557] font-bold text-[.8rem] tracking-[1px] p-[1rem] inline-block text-center align-middle leading-normal disabled:opacity-50 disabled:cursor-not-allowed"
        id="btnSignIn"
        type="submit"
        disabled={isPending}
      >
        {isPending ? 'Signing in...' : 'Sign In!'}
      </Button>
      {error && <p className="text-red-500 mt-2 text-sm">{error}</p>}
    </form>
  )
}

export default SignInButton