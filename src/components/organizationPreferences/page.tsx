'use client'
import { useToast } from '@/hooks/use-toast'
import { customStringFilter } from '@/lib/customStringFilter'
import { cn, GuidGenerator } from '@/lib/utils'
import { api } from '@/trpc/react'
import { OrganizationPreference } from '@/types/organizationPreference'
import { Value } from '@radix-ui/react-select'
import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table'
import { Key, Loader, Plus, Loader2, Trash2 } from 'lucide-react'
import Link from 'next/link'
import React from 'react'
import { useEffect, useState, useRef } from 'react'
// UI components are used in the JSX
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

export default function OrganizationPreferences() {
  const { toast } = useToast()
  const [isEditMode, setIsEditMode] = useState<boolean>(false)
  const [editModeRowIndex, setEditModeRowIndex] = useState<number | null>(null)
  const [newRow, setNewRow] = useState<OrganizationPreference | null>(null)
  const [editedValues, setEditedValues] = useState<{ [key: string]: string }>(
    {}
  )

  // Refs for input fields to maintain focus
  const keyNameInputRef = useRef<HTMLInputElement>(null)
  const keyValueInputRef = useRef<HTMLInputElement>(null)

  // Track which field is being edited
  const [activeField, setActiveField] = useState<'key' | 'value' | null>(null)

  const [preferencesList, query] =
    api.organizationPreferences.getAllPreferences.useSuspenseQuery<
      OrganizationPreference[]
    >()

  // Effect to focus input field when entering edit mode
  useEffect(() => {
    // Only run this effect when we have a new row or are in edit mode
    if (newRow || isEditMode) {
      // Use a short timeout to ensure the DOM has updated
      const timeoutId = setTimeout(() => {
        try {
          if (activeField === 'value' && keyValueInputRef.current) {
            keyValueInputRef.current.focus()
          } else if (keyNameInputRef.current) {
            // Default to KeyName field for new rows or when activeField is not set
            keyNameInputRef.current.focus()
          }
        } catch (error) {
          console.error('Error focusing input:', error)
        }
      }, 100)

      // Clean up the timeout if the component unmounts or dependencies change
      return () => clearTimeout(timeoutId)
    }
  }, [newRow, isEditMode, activeField])

  const SaveOrganizationPreference =
    api.organizationPreferences.UpsertPreference.useMutation()

  const handleAddNew = () => {
    // Reset all edit states first

    setIsEditMode(false)
    setEditModeRowIndex(null)

    // Create a new empty row
    const emptyRow: OrganizationPreference = {
      partitionKey: '',
      key: '',
      value: '',
      rowKey: `new-${Date.now()}`,
    }

    // Update state
    setNewRow(emptyRow)
    setEditedValues({
      KeyName: '',
      KeyValue: '',
    })

    // Set focus to KeyName field by default when adding a new row
    setActiveField('key')
  }

  const handleClickEditRow = (
    rowIndex: number,
    row: OrganizationPreference
  ) => {
    setNewRow(null)
    setIsEditMode(true)
    setEditModeRowIndex(rowIndex)
    setEditedValues({
      KeyName: row.key || '',
      KeyValue: row.value! || '',
    })
    // Set focus to KeyName field by default when editing
    setActiveField('key')
  }

  const handleCancel = () => {
    setIsEditMode(false)
    setEditModeRowIndex(null)
    setNewRow(null)
    setEditedValues({})
    // Reset active field
    setActiveField(null)
  }

  const handleSave = async (row: OrganizationPreference, isNew: boolean) => {
    try {
      // Validate required fields
      if (!editedValues.KeyName?.trim() || !editedValues.KeyValue?.trim()) {
        toast({
          title: 'Both Key and Value are required',
          variant: 'destructive',
        })
        return
      }

      const keyName = editedValues.KeyName?.trim() || ''
      const keyValue = editedValues.KeyValue?.trim() || ''

      // Check for duplicate key-value pair
      const keyValuePairExists = preferencesList.some(
        (pref) =>
          pref.key.toLowerCase() === keyName.toLowerCase() &&
          pref.value?.toLowerCase() === keyValue.toLowerCase() &&
          pref.rowKey !== row.rowKey
      )

      if (keyValuePairExists) {
        toast({
          title: 'Key-Value pair already exists',
          description: 'This combination of Key and Value already exists',
          variant: 'destructive',
        })
        return
      }

      if (isNew) {
        await SaveOrganizationPreference.mutateAsync({
          rowKey: crypto.randomUUID(),
          key: keyName,
          value: keyValue,
        })
      } else {
        await SaveOrganizationPreference.mutateAsync({
          rowKey: row.rowKey,
          key: keyName,
          value: keyValue,
        })
      }

      toast({
        title: isNew
          ? 'Preference created successfully'
          : 'Preference updated successfully',
        className: cn('bg-green-600 text-white border-green-600'),
      })

      handleCancel()
      query.refetch()
    } catch (error) {
      console.error('Error saving preference:', error)
      toast({
        title: 'Error saving preference',
        variant: 'destructive',
      })
    }
  }

  // State for sorting and filtering
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])

  // Custom filter function
  const filterFunction = customStringFilter<OrganizationPreference>()

  const headerStyle = {
    backgroundColor: '#566582',
    verticalAlign: 'middle',
    color: '#F5F7FE',
    height: '45px',
    fontSize: '14px',
    fontWeight: 600,
    padding: '0px 18px',
    textAlign: 'center',
    fontFamily: '"Open Sans"',
    borderColor: '#dddcdf',
  } as React.CSSProperties

  // Prepare table data - add new row at the top if it exists
  const tableData = React.useMemo(() => {
    return newRow ? [newRow, ...preferencesList] : preferencesList
  }, [newRow, preferencesList])

  const table = useReactTable<OrganizationPreference>({
    data: tableData,
    columns: React.useMemo(() => {
      return [
        {
          accessorKey: 'key',
          header: 'Key',
          filterFn: filterFunction,
          cell: (info: any) => {
            // Check if this cell is in edit mode
            const rowIsNewRow =
              newRow && info.row.original.rowKey === newRow.rowKey
            const rowIsBeingEdited =
              isEditMode && info.row.index === editModeRowIndex
            const isEditing = rowIsNewRow || rowIsBeingEdited

            return (
              <div className="text-center">
                {isEditing ? (
                  <input
                    type="text"
                    id="key"
                    name="key"
                    ref={keyNameInputRef}
                    value={editedValues.KeyName || ''} // Changed from keyName to KeyName
                    onChange={(e) =>
                      setEditedValues((prev) => ({
                        ...prev,
                        KeyName: e.target.value,
                      }))
                    }
                    onFocus={() => setActiveField('key')}
                    className="w-full px-2 py-0.5 border rounded text-sm h-7"
                    autoFocus={activeField === 'key'}
                  />
                ) : (
                  <label className="font-open-sans text-[14px] text-black font-normal">
                    {String(info.getValue())}
                  </label>
                )}
              </div>
            )
          },
        },
        {
          accessorKey: 'value',
          header: 'Value',
          filterFn: filterFunction,
          cell: (info: any) => {
            // Check if this cell is in edit mode
            const rowIsNewRow =
              newRow && info.row.original.rowKey === newRow.rowKey
            const rowIsBeingEdited =
              isEditMode && info.row.index === editModeRowIndex
            const isEditing = rowIsNewRow || rowIsBeingEdited

            return (
              <div className="text-center">
                {isEditing ? (
                  <input
                    type="text"
                    id="value"
                    name="value"
                    ref={keyValueInputRef}
                    value={editedValues.KeyValue || ''}
                    onChange={(e) =>
                      setEditedValues((prev) => ({
                        ...prev,
                        KeyValue: e.target.value,
                      }))
                    }
                    onFocus={() => setActiveField('value')}
                    className="w-full px-2 py-0.5 border rounded text-sm h-7"
                    autoFocus={activeField === 'value'}
                  />
                ) : (
                  <label className="font-open-sans text-[14px] text-black font-normal">
                    {String(info.getValue())}
                  </label>
                )}
              </div>
            )
          },
        },
        {
          id: 'actions',
          header: 'Actions',
          cell: (info: any) => {
            const rowIsNewRow =
              newRow && info.row.original.rowKey === newRow.rowKey
            const rowIsBeingEdited =
              isEditMode && info.row.index === editModeRowIndex
            const isEditing = rowIsNewRow || rowIsBeingEdited

            return (
              <div className="flex justify-center gap-2">
                {!isEditing && (
                  <>
                    <button
                      className="bg-[#f5f7ff] text-[13px] font-semibold uppercase text-ui-dark-gray font-open-sans px-3 py-1 rounded border border-transparent hover:bg-white hover:text-[#566582] hover:border-[#566582] transition-colors"
                      onClick={() =>
                        handleClickEditRow(info.row.index, info.row.original)
                      }
                    >
                      Edit
                    </button>
                    <button
                      className="hidden bg-[#f5f7ff] text-[13px] font-semibold uppercase text-ui-dark-gray font-open-sans border border-[#566582] px-3 py-1 rounded"
                      onClick={() => handleDelete(info.row.original)}
                      disabled={isDeleting}
                    >
                      Delete
                    </button>
                  </>
                )}
                {isEditing && (
                  <div className="flex gap-2">
                    <button
                      className="bg-[#f5f7ff] text-[13px] font-semibold uppercase text-ui-dark-gray font-open-sans px-3 py-1 rounded border border-transparent hover:bg-white hover:text-[#566582] hover:border-[#566582] transition-colors"
                      onClick={() =>
                        handleSave(info.row.original, Boolean(rowIsNewRow))
                      }
                    >
                      Save
                    </button>
                    <button
                      className="bg-[#f5f7ff] text-[13px] font-semibold uppercase text-ui-dark-gray font-open-sans px-3 py-1 rounded border border-transparent hover:bg-white hover:text-[#566582] hover:border-[#566582] transition-colors"
                      onClick={handleCancel}
                    >
                      Cancel
                    </button>
                  </div>
                )}
              </div>
            )
          },
        },
      ]
    }, [newRow, isEditMode, editModeRowIndex, editedValues]),
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnFiltersChange: setColumnFilters,
    state: {
      sorting,
      columnFilters,
    },
  })

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deletingPreference, setDeletingPreference] =
    useState<OrganizationPreference | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const DeletePreferenceMutation =
    api.organizationPreferences.deletePreference.useMutation()

  const handleDelete = async (preference: OrganizationPreference) => {
    setDeletingPreference(preference)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!deletingPreference) return

    setIsDeleting(true)
    try {
      await DeletePreferenceMutation.mutateAsync({
        rowKey: deletingPreference.rowKey,
      })

      toast({
        className: cn('bg-green-600 text-white border-green-600'),
        title: 'Preference deleted successfully',
        variant: 'default',
      })

      query.refetch()
    } catch (error) {
      toast({
        title: 'Error deleting preference',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
      setDeleteDialogOpen(false)
      setDeletingPreference(null)
    }
  }

  return (
    <>
      <div>
        <h1 className="text-center text-[28px] font-bold mb-6 font-open-sans">
          Manage Organization Preferences
        </h1>

        <div className="w-[85%] mx-auto">
          <button
            type="button"
            onClick={handleAddNew}
            className="bg-[#f5f7ff] text-[13px] font-semibold uppercase text-ui-dark-gray font-open-sans px-4 py-2 flex items-center gap-1 border border-transparent hover:bg-white hover:text-[#566582] hover:border-[#566582] transition-colors"
          >
            <Plus className="h-4 w-4" /> Add New Record
          </button>
        </div>

        {query.isPending || query.isRefetching ? (
          <div className="overflow-x-auto relative mt-10">
            <Loader />
          </div>
        ) : (
          <div>
            <table className="w-[85%] border-collapse mx-auto border mt-8  font-open-sans">
              <thead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <th
                        key={header.id}
                        className=" border-r border-[#DDDCDF]"
                        colSpan={header.colSpan}
                        style={{
                          ...headerStyle,
                          minWidth: `${header.getSize()}px`,
                          position: 'sticky',
                          top: 0,
                          zIndex: 10,
                        }}
                      >
                        {header.isPlaceholder ? null : (
                          <div className="flex justify-center items-center">
                            <span className="flex m-3 w-full text-center justify-center  font-open-sans">
                              {flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                            </span>
                          </div>
                        )}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody>
                {table.getRowModel().rows.map((row) => (
                  <tr key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={cell.id}
                        className="px-6 py-4 border-b text-center  font-open-sans  border-r border-[#DDDCDF]"
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Delete Preference</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-4">
            <p className="text-center">
              Are you sure you want to delete this preference?
            </p>
            <div className="flex justify-center gap-4">
              <button
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                onClick={confirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  'Delete'
                )}
              </button>
              <button
                className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
                onClick={() => setDeleteDialogOpen(false)}
                disabled={isDeleting}
              >
                Cancel
              </button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
