'use client'

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { api } from '@/trpc/react'
import Loader from '@/components/ui/Loader'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'
import {
  EntityLevelConstants,
  ExpansionConfiguration,
} from '@/types/expansionConfiguration'
import { ExtensionLevels } from "@/enums/extensionLevels"

export const ExpansionManager = () => {
  const { toast } = useToast()

  const expansionConfigurationsQuery =
    api.admin.getLevelConfigurations.useQuery()

  const levelConfigurationsMutation =
    api.admin.saveLevelConfigurations.useMutation()

  // Separate configurations for Hospital and Ambulatory
  const [hospitalLevels, setHospitalLevels] = useState<
    ExpansionConfiguration[]
  >([])
  const [ambulatoryLevels, setAmbulatoryLevels] = useState<
    ExpansionConfiguration[]
  >([])
  const [measureTypes, setPrimaryMeasureTypes] = useState<
    PrimaryMeasureTypeConstants[]
  >([])
  const [activeTab, setActiveTab] = useState<PrimaryMeasureTypeConstants>(
    PrimaryMeasureTypeConstants.HospitalMeasures
  )

  useEffect(() => {
    if (expansionConfigurationsQuery.data) {
      setPrimaryMeasureTypes([
        ...new Set(expansionConfigurationsQuery.data.map((x) => x.measureType)),
      ])
      setHospitalLevels(
        expansionConfigurationsQuery.data.filter(
          (x) => x.measureType === PrimaryMeasureTypeConstants.HospitalMeasures
        )
      )
      setAmbulatoryLevels(
        expansionConfigurationsQuery.data.filter(
          (x) =>
            x.measureType === PrimaryMeasureTypeConstants.AmbulatoryMeasures
        )
      )
    }
  }, [expansionConfigurationsQuery.data])

  const handleLevelChange = (
    levelId: string,
    value: string,
    category: PrimaryMeasureTypeConstants
  ) => {
    const updateLevels = (levels: ExpansionConfiguration[]) =>
      levels.map((level: ExpansionConfiguration) =>
        level.rowKey === levelId
          ? ({ ...level, selectedLevel: value } as ExpansionConfiguration)
          : level
      )

    if (category === PrimaryMeasureTypeConstants.HospitalMeasures) {
      setHospitalLevels(updateLevels(hospitalLevels))
    } else {
      setAmbulatoryLevels(updateLevels(ambulatoryLevels))
    }
  }

  const handleSave = async () => {
    try {
      if (
        hospitalLevels
          .concat(ambulatoryLevels)
          .some((x) => x.level !== ExtensionLevels.level1 && x.selectedLevel === '')
      ) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Please select the valid display level',
        })

        return
      }
      const config = hospitalLevels
        .concat(ambulatoryLevels)
        .filter((x) => x.selectedLevel !== '')
        .map((x) => ({
          rowKey: x.rowKey,
          level: x.level,
          selectedLevel: x.selectedLevel,
          measureType: x.measureType,
        }))
      await levelConfigurationsMutation.mutateAsync(
        {
          configs: config,
        },
        {
          onSuccess: () => {
            toast({
              title: 'Success',
              description: 'Expansion configurations saved successfully',
            })
          },
        }
      )
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save configurations',
      })
    }
  }

  const LevelSelectionGrid = ({
    levels,
    category,
  }: {
    levels: ExpansionConfiguration[]
    category: PrimaryMeasureTypeConstants
  }) => (
    <div className="space-y-6">
      {levels
        .sort(
          (a, b) =>
            parseInt(a.label.split(' ')[1]!) - parseInt(b.label.split(' ')[1]!)
        )
        .map((level) => (
          <div key={level.rowKey} className="mb-6">
            <div className="text-[#205782] font-open-sans mb-2 w-[313px] uppercase font-semibold text-[12px]">
              LEVEL {level.label.split(' ')[1]} ENTITIES
            </div>
            <Select
              disabled={levelConfigurationsMutation.isPending}
              value={level.selectedLevel}
              onValueChange={(value) =>
                handleLevelChange(level.rowKey, value, category)
              }
            >
              <SelectTrigger className="bg-white w-[313px] h-[22px] border border-[#566582]  rounded-none">
                <SelectValue placeholder="Select a level" className='font-open-sans font-normal text-[10px] text-[#000000]' />
              </SelectTrigger>
              <SelectContent>
                {JSON.parse(level.entries).map(
                  (entry: EntityLevelConstants) => (
                    <SelectItem
                      key={level.entries.indexOf(entry)}
                      value={entry}
                    >
                      {entry}
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>
          </div>
        ))}
    </div>
  )

  return (
    <div className="mx-auto">
      {expansionConfigurationsQuery.isPending ? (
        <div className="overflow-x-auto relative mt-10">
          <Loader />
        </div>
      ) : (
        <div className="mt-4">
          {/* Custom tab buttons that look like the image */}
          <div className="flex justify-center mb-4">
            <div className="flex font-sans shadow-sm max-w-fit mx-auto rounded-md overflow-hidden w-[471px]">
              <button
                onClick={() => setActiveTab(PrimaryMeasureTypeConstants.HospitalMeasures)}
                className={`px-4 w-[232px] h-[27px] text-center font-weight-700 border  text-[13px] border-[#DDDCDF] ${activeTab === PrimaryMeasureTypeConstants.HospitalMeasures
                  ? 'bg-[#EBEFFD] text-[#205782] font-open-sans text-[13px] font-bold'
                  : 'bg-white text-[#566582]  font-open-sans font-normal'
                  } rounded-l-md`}
              >
                HOSPITAL MEASURES
              </button>
              <button
                onClick={() => setActiveTab(PrimaryMeasureTypeConstants.AmbulatoryMeasures)}
                className={` px-4  w-[245px] h-[27px] text-center border font-weight-700  text-[13px] border-[#D1D9E6] ${activeTab === PrimaryMeasureTypeConstants.AmbulatoryMeasures
                  ? 'bg-[#EBEFFD] text-[#205782] font-open-sans font-bold'
                  : 'bg-white text-[#566582]  font-open-sans font-normal'
                  } rounded-r-md`}
              >
                AMBULATORY MEASURES
              </button>
            </div>
          </div>

          {/* Main content area with blue border */}
          <div className="border border-[#DDDCDF] rounded-md p-8">
            {activeTab === PrimaryMeasureTypeConstants.HospitalMeasures && (
              <div className="pt-4 pl-4">
                <LevelSelectionGrid
                  levels={hospitalLevels}
                  category={PrimaryMeasureTypeConstants.HospitalMeasures}
                />
              </div>
            )}

            {activeTab === PrimaryMeasureTypeConstants.AmbulatoryMeasures && (
              <div className="pt-4 pl-4">
                <LevelSelectionGrid
                  levels={ambulatoryLevels}
                  category={PrimaryMeasureTypeConstants.AmbulatoryMeasures}
                />
              </div>
            )}

            <div className="mt-8 ml-4">
              <button
                onClick={handleSave}
                disabled={levelConfigurationsMutation.isPending}
                className="bg-[#205782] hover:bg-[#164666] text-white font-open-sans py-2 px-4 w-[313px] uppercase text-[12px] font-weight-700 "
              >
                {levelConfigurationsMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin inline" />
                )}
                {levelConfigurationsMutation.isPending
                  ? 'Saving...'
                  : 'Save Configurations'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
