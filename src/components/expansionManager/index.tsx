'use client'

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { api } from '@/trpc/react'
import Loader from '@/components/ui/Loader'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'
import {
  EntityLevelConstants,
  ExpansionConfiguration,
} from '@/types/expansionConfiguration'
import { ExtensionLevels } from "@/enums/extensionLevels"

export const ExpansionManager = () => {
  const { toast } = useToast()

  const expansionConfigurationsQuery =
    api.admin.getLevelConfigurations.useQuery()

  const levelConfigurationsMutation =
    api.admin.saveLevelConfigurations.useMutation()

  // Separate configurations for Hospital and Ambulatory
  const [hospitalLevels, setHospitalLevels] = useState<
    ExpansionConfiguration[]
  >([])
  const [ambulatoryLevels, setAmbulatoryLevels] = useState<
    ExpansionConfiguration[]
  >([])
  const [measureTypes, setPrimaryMeasureTypes] = useState<
    PrimaryMeasureTypeConstants[]
  >([])

  useEffect(() => {
    if (expansionConfigurationsQuery.data) {
      setPrimaryMeasureTypes([
        ...new Set(expansionConfigurationsQuery.data.map((x) => x.measureType)),
      ])
      setHospitalLevels(
        expansionConfigurationsQuery.data.filter(
          (x) => x.measureType === PrimaryMeasureTypeConstants.HospitalMeasures
        )
      )
      setAmbulatoryLevels(
        expansionConfigurationsQuery.data.filter(
          (x) =>
            x.measureType === PrimaryMeasureTypeConstants.AmbulatoryMeasures
        )
      )
    }
  }, [expansionConfigurationsQuery.data])

  const handleLevelChange = (
    levelId: string,
    value: string,
    category: PrimaryMeasureTypeConstants
  ) => {
    // const entityId = value === 'none' ? null : value
    const updateLevels = (levels: ExpansionConfiguration[]) =>
      levels.map((level: ExpansionConfiguration) =>
        level.rowKey === levelId
          ? ({ ...level, selectedLevel: value } as ExpansionConfiguration)
          : level
      )

    if (category === PrimaryMeasureTypeConstants.HospitalMeasures) {
      setHospitalLevels(updateLevels(hospitalLevels))
    } else {
      setAmbulatoryLevels(updateLevels(ambulatoryLevels))
    }
  }

  const handleSave = async () => {
    try {
      if (
        hospitalLevels
          .concat(ambulatoryLevels)
          .some((x) => x.level !== ExtensionLevels.level1 && x.selectedLevel === '')
      ) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Please select the valid display level',
        })

        return
      }
      const config = hospitalLevels
          .concat(ambulatoryLevels)
          .filter((x) => x.selectedLevel !== '')
          .map((x) => ({
        rowKey: x.rowKey,
        level: x.level,
        selectedLevel: x.selectedLevel,
        measureType: x.measureType,
      }))
      await levelConfigurationsMutation.mutateAsync(
        {
          configs: config,
        },
        {
          onSuccess: () => {
            toast({
              title: 'Success',
              description: 'Expansion configurations saved successfully',
            })
          },
        }
      )
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save configurations',
      })
    } finally {
    }
  }

  const LevelSelectionGrid = ({
    levels,
    category,
  }: {
    levels: ExpansionConfiguration[]
    category: PrimaryMeasureTypeConstants
  }) => (
    <div className="space-y-6">
      <div className="grid grid-cols-12 gap-4 mb-4 font-semibold">
        <div className="col-span-4">Entity Level</div>
        <div className="col-span-8">Display Level</div>
      </div>

      {levels
        .sort(
          (a, b) =>
            parseInt(a.label.split(' ')[1]!) - parseInt(b.label.split(' ')[1]!)
        )
        .map((level) => (
          <div
            key={level.rowKey}
            className="grid grid-cols-12 gap-4 items-center"
          >
            <div className="col-span-4 font-medium">{level.label}</div>
            <div className="col-span-8">
              <Select
                disabled={levelConfigurationsMutation.isPending}
                value={level.selectedLevel}
                onValueChange={(value) =>
                  handleLevelChange(level.rowKey, value, category)
                }
              >
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Select a level" />
                </SelectTrigger>
                <SelectContent>
                  {JSON.parse(level.entries).map(
                    (entry: EntityLevelConstants) => (
                      <SelectItem
                        key={level.entries.indexOf(entry)}
                        value={entry}
                      >
                        {entry}
                      </SelectItem>
                    )
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
        ))}
    </div>
  )

  return (
    <Card className="border">
      <CardHeader>
        <CardTitle>Entity Level Configuration</CardTitle>
        <CardDescription>
          Configure entity hierarchy for hospital and ambulatory measures
        </CardDescription>
      </CardHeader>
      <CardContent>
        {expansionConfigurationsQuery.isPending ? (
          <div className="overflow-x-auto relative mt-10">
            <Loader />
          </div>
        ) : (
          <Tabs
            defaultValue={
              measureTypes.length > 0
                ? measureTypes[0]
                : PrimaryMeasureTypeConstants.HospitalMeasures
            }
          >
            <TabsList className="grid w-full grid-cols-2 mb-6 bg-muted p-1 rounded-lg">
              {measureTypes.map((measureType) => (
                <TabsTrigger
                  key={measureType}
                  value={measureType}
                  className="data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-sm"
                >
                  {measureType.toString()}
                </TabsTrigger>
              ))}
            </TabsList>

            {[
              ...new Set(
                expansionConfigurationsQuery.data?.map((x) => x.measureType)
              ),
            ].map((measureType) => (
              <TabsContent
                key={measureType}
                value={measureType}
                className="bg-muted/50 rounded-lg p-6"
              >
                <LevelSelectionGrid
                  levels={
                    measureType === PrimaryMeasureTypeConstants.HospitalMeasures
                      ? hospitalLevels
                      : ambulatoryLevels
                  }
                  category={measureType}
                />
              </TabsContent>
            ))}

            <div className="mt-6 flex justify-end">
              <Button
                onClick={handleSave}
                disabled={levelConfigurationsMutation.isPending}
                variant="default"
                className={cn(
                  'border bg-ui-dark-gray hover:bg-white hover:text-ui-dark-gray hover:border-ui-dark-gray',
                  levelConfigurationsMutation.isPending &&
                    'bg-white text-ui-dark-gray border-ui-dark-gray'
                )}
              >
                {levelConfigurationsMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {levelConfigurationsMutation.isPending
                  ? 'Saving...'
                  : 'Save Configurations'}
              </Button>
            </div>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}
