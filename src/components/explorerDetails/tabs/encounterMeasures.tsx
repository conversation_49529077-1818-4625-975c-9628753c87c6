'use client'

import { useMemo, useState } from 'react'
import { TableLegend } from '../tableLegend'
import { PatientData } from '../patientData'
import { EncounterDetails } from '../encounterDetails'
import { Checkbox } from '@/components/ui/checkbox'
import { NoDataAvailable } from '@/components/dataTable/noDataAvailable'
import BasicDataTable from '@/components/dataTable/basicDataTable'
import { useExplorerStore } from '@/stores/explorer'
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'

const EncounterMeasures = () => {
  const [showExcluded, setShowExcluded] = useState(false)
  const { currentEncounterMeasures } = useExplorerStore()

  const generateColumns = (): ColumnDef<
    (typeof currentEncounterMeasures)[number]
  >[] => {
    return [
      {
        accessorKey: 'measureName',
        header: 'Measure Name',
        cell: ({ getValue }) => getValue(),
      },
      {
        accessorKey: 'standardOfCareMet',
        header: 'Performance',
        cell: ({ row }) => {
          const data = row.original
          const standardOfCareMet = Number(data.standardOfCareMet)
          const excluded = data.excluded
          const continuous = data.continuous
          const hoverText = data.hoverText

          const tooltipContent = hoverText
            ? `
            <div>
              <span>${hoverText.substring(0, hoverText.indexOf('Facility'))}</span>
              <p>${hoverText.substring(hoverText.indexOf('Facility'))}</p>
            </div>
          `
            : ''

          const commonClasses = 'w-full flex justify-center items-center'

          if (excluded === 1) {
            return (
              <div className={commonClasses}>
                <img
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                  src="/images/excluded e.svg"
                  alt="Excluded"
                />
              </div>
            )
          } else if (continuous === 1) {
            return (
              <div className={commonClasses}>
                <div
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                >
                  {standardOfCareMet}
                </div>
              </div>
            )
          } else if (standardOfCareMet === 1) {
            return (
              <div className={commonClasses}>
                <img
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                  src="/images/check.svg"
                  alt="Standard of Care Met"
                />
              </div>
            )
          } else if (standardOfCareMet === 0) {
            return (
              <div className={commonClasses}>
                <img
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                  src="/images/close.svg"
                  alt="Standard of Care Not Met"
                />
              </div>
            )
          }

          return null
        },
      },
    ]
  }

  const data = useMemo(() => {
    return (currentEncounterMeasures ?? []).filter((measure) => {
      if (!showExcluded) {
        return measure.description !== 'Excluded (only)'
      }
      return true
    })
  }, [currentEncounterMeasures, showExcluded])

  const columns = useMemo(() => generateColumns(), [])

  const currentEncounterMeasuresTable = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  return (
    <>
      <PatientData />
      <EncounterDetails />

      <div className="w-full mx-auto space-y-4 py-4">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="flex-1 flex justify-end">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="excluded"
                checked={showExcluded}
                onCheckedChange={(checked) =>
                  setShowExcluded(checked as boolean)
                }
              />
              <label
                htmlFor="excluded"
                className="text-sm text-muted-foreground"
              >
                Show excluded measures
              </label>
            </div>
          </div>
        </div>

        <div className="flex flex-col space-y-6">
          {data.length > 0 ? (
            <BasicDataTable table={currentEncounterMeasuresTable} />
          ) : (
            <NoDataAvailable content="No data available for encounter measures" />
          )}
          <TableLegend showExcluded={showExcluded} />
        </div>
      </div>
    </>
  )
}

export default EncounterMeasures
