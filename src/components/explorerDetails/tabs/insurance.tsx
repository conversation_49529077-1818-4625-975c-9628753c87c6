'use client'

import { NoDataAvailable } from '@/components/dataTable/noDataAvailable'
import { EncounterDetails } from '../encounterDetails'
import { PatientData } from '../patientData'
import { useExplorerStore } from '@/stores/explorer'
import { useMemo } from 'react'
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import BasicDataTable from '@/components/dataTable/basicDataTable'
import dayjs from 'dayjs'

const Insurance = () => {
  const { insurance } = useExplorerStore()

  const insuranceColumns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        accessorKey: 'insuranceCarrier',
        header: 'Insurance Carrier',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'coverage',
        header: 'Coverage Type',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'effectiveDate',
        header: 'Effective Date',
        cell: ({ row }) => {
          const date = row.original.effectiveDate
          return date.isValid() ? dayjs.utc(date).format('MM/DD/YYYY') : '-'
        },
      },
      {
        accessorKey: 'expirationDate',
        header: 'Expiration Date',
        cell: ({ row }) => {
          const date = row.original.expirationDate
          return date.isValid() ? dayjs.utc(date).format('MM/DD/YYYY') : '-'
        },
      },
    ],
    []
  )

  const insuranceTable = useReactTable({
    data: insurance,
    columns: insuranceColumns,
    getCoreRowModel: getCoreRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  return (
    <>
      <PatientData />

      <EncounterDetails />

      <div className="w-full mx-auto">
        {insurance && insurance.length > 0 ? (
          <BasicDataTable table={insuranceTable} />
        ) : (
          <NoDataAvailable content="No insurance available for selected criteria" />
        )}
      </div>
    </>
  )
}

export default Insurance
