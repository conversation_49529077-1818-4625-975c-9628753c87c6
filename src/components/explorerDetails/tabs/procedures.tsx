'use client'

import { NoDataAvailable } from '@/components/dataTable/noDataAvailable'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { EncounterDetails } from '../encounterDetails'
import { PatientData } from '../patientData'
import { useState, useMemo } from 'react'
import { useExplorerStore } from '@/stores/explorer'
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import BasicDataTable from '@/components/dataTable/basicDataTable'

const Procedures = () => {
  const [procedureTab, setProcedureTab] = useState('icd-10-procedures')
  const { procedures } = useExplorerStore()

  // story/MP-5 TODO: Make sure the cols are pulling from the correct keys. accessorKey should match the key in the store

  const icd10ProceduresColumns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        accessorKey: 'procedureCode',
        header: 'ICD-10 Code',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'procedureDescription',
        header: 'Description',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'codeSystem',
        header: 'Type',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'procedureProvider',
        header: 'Provider',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'procedureDate',
        header: 'Date',
        cell: ({ row }) => {
          const date = row.original.procedureDate
          return date ? date.format('MM/DD/YYYY') : 'N/A'
        },
      },
    ],
    []
  )

  // story/MP-5 TODO: Make sure the cols are pulling from the correct keys. accessorKey should match the key in the store

  const otherProceduresColumns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        accessorKey: 'procedureCode',
        header: 'Code',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'procedureDescription',
        header: 'Description',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'codeSystem',
        header: 'Code Type',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'provider',
        header: 'Provider',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'procedureDate',
        header: 'Date',
        cell: ({ row }) => {
          const date = row.original.procedureDate
          return date ? date.format('MM/DD/YYYY') : 'N/A'
        },
      },
    ],
    []
  )

  const filteredProcedures = useMemo(() => {
    if (!procedures) return []

    return procedures.filter((procedure) => {
      if (procedureTab === 'icd-10-procedures') {
        return procedure.codeType === 'ICD10'
      } else {
        return procedure.codeType !== 'ICD10'
      }
    })
  }, [procedures, procedureTab])

  const proceduresTable = useReactTable({
    data: filteredProcedures,
    columns:
      procedureTab === 'icd-10-procedures'
        ? icd10ProceduresColumns
        : otherProceduresColumns,
    getCoreRowModel: getCoreRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  return (
    <>
      <PatientData />

      <EncounterDetails />

      <div className="w-full mx-auto space-y-4">
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <Tabs
            defaultValue="icd-10-procedures"
            className="min-w-[240px] pt-4"
            onValueChange={(value) => setProcedureTab(value)}
          >
            <TabsList className="grid w-full grid-cols-2 !p-0 bg-white">
              <TabsTrigger
                className="data-[state=active]:bg-[#EBEFFD] data-[state=active]:font-bold border border-ui-medium-blue data-[state=active]:border-blue-3 rounded-r-none"
                value="icd-10-procedures"
              >
                ICD-10 PROCEDURES
              </TabsTrigger>
              <TabsTrigger
                className="data-[state=active]:bg-[#EBEFFD] data-[state=active]:font-bold border border-ui-medium-blue data-[state=active]:border-blue-3 rounded-l-none"
                value="other-procedures"
              >
                OTHER PROCEDURES
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="flex flex-col">
          {filteredProcedures.length > 0 ? (
            <BasicDataTable table={proceduresTable} />
          ) : (
            <NoDataAvailable
              content={`No ${procedureTab === 'icd-10-procedures' ? 'ICD-10' : 'other'} procedures available for selected criteria`}
            />
          )}
        </div>
      </div>
    </>
  )
}

export default Procedures
