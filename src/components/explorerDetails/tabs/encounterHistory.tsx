'use client'

import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import React, { useMemo } from 'react'
import { PatientData } from '../patientData'
import { useExplorerStore } from '@/stores/explorer'
import dayjs from 'dayjs'
import BasicDataTable from '@/components/dataTable/basicDataTable'

const EncounterHistory = () => {
  const { encounterHistory } = useExplorerStore()

  type EncounterHistoryType = typeof encounterHistory extends (infer T)[]
    ? T
    : never

  const generateColumns = (): ColumnDef<EncounterHistoryType>[] => {
    return [
      {
        accessorKey: 'caseIdentifier',
        header: 'Case Identifier',
        cell: ({ getValue }) => getValue(),
      },
      {
        accessorKey: 'admitDateTime',
        header: 'Admit Date',
        cell: ({ getValue }) => {
          const date = dayjs(getValue() as Date)
          return date.format('MM/DD/YYYY')
        },
      },
      {
        accessorKey: 'dischargeDate',
        header: 'Discharge Date',
        cell: ({ getValue }) => {
          const date = dayjs(getValue() as Date)
          return date.format('MM/DD/YYYY')
        },
      },
      {
        accessorKey: 'encounterType',
        header: 'Encounter Type',
        cell: ({ getValue }) => getValue(),
      },
      {
        accessorKey: 'facility',
        header: 'Facility',
        cell: ({ getValue }) => getValue(),
      },
    ]
  }

  const data = useMemo(() => {
    return encounterHistory ?? []
  }, [encounterHistory])

  const columns = useMemo(() => generateColumns(), [])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  return (
    <>
      <PatientData />

      <BasicDataTable table={table} />
    </>
  )
}

export default EncounterHistory
