'use Client'

import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PatientData } from '../patientData'
import { useMemo, useState, useEffect } from 'react'
import { Checkbox } from '@/components/ui/checkbox'
import { NoDataAvailable } from '@/components/dataTable/noDataAvailable'
import { cn } from '@/lib/utils'
import { TableLegend } from '../tableLegend'
import { useExplorerStore } from '@/stores/explorer'
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import React from 'react'
import BasicDataTable from '@/components/dataTable/basicDataTable'
import dayjs from 'dayjs'

const AllMeasures = () => {
  const { encounterMeasures, patientMeasures } = useExplorerStore()
  const [yearTab, setYearTab] = useState<string>('')

  const getYearTabs = () => {
    const years = new Set<number>()
    const currentYear = dayjs.utc().year()

    // Only add years if we have measures
    if (encounterMeasures?.length || patientMeasures?.length) {
      encounterMeasures?.forEach((measure) => {
        if (measure.yearOfMeasure) years.add(measure.yearOfMeasure)
      })
      patientMeasures?.forEach((measure) => {
        if (measure.yearOfMeasure) years.add(measure.yearOfMeasure)
      })
    } else {
      // If no measures are available yet, default to current year and previous year
      years.add(currentYear)
      years.add(currentYear - 1)
    }

    let yearTabs = Array.from(years).sort((a, b) => b - a)

    // Ensure we always have at least two years
    if (yearTabs.length === 1) {
      const existingYear = yearTabs[0]
      if (typeof existingYear === 'number') {
        yearTabs = [existingYear, existingYear - 1]
      } else {
        yearTabs = [dayjs.utc().year(), dayjs.utc().year() - 1]
      }
    }

    return yearTabs
  }

  const yearTabs = useMemo(getYearTabs, [encounterMeasures, patientMeasures])

  // Reset yearTab when measures change
  useEffect(() => {
    if (yearTabs.length > 0) {
      setYearTab(yearTabs[0]?.toString()!)
    }
  }, [yearTabs])

  const [showExcluded, setShowExcluded] = useState(false)

  const commonClasses = 'w-full flex justify-center items-center'

  const generatePatientMeasureColumns = (): ColumnDef<
    (typeof patientMeasures)[number]
  >[] => {
    return [
      {
        accessorKey: 'measureName',
        header: 'Measure Name',
        cell: ({ getValue }) => getValue(),
      },
      {
        accessorKey: 'standardOfCareMet',
        header: 'Performance',
        cell: ({ row }) => {
          const data = row.original
          const standardOfCareMet = data.standardOfCareMet
          const excluded = data.excluded
          const continuous = data.continuous
          const hoverText = data.hoverText

          const tooltipContent = hoverText
            ? `
            <div>
              <span>${hoverText.substring(0, hoverText.indexOf('Visit'))}</span>
              <p>${hoverText.substring(hoverText.indexOf('Visit'), hoverText.indexOf('Rendering'))}</p>
              <p>${hoverText.substring(hoverText.indexOf('Rendering'))}</p>
            </div>
          `
            : ''

          if (excluded === 1) {
            return (
              <div className={commonClasses}>
                <img
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                  src="/images/excluded e.svg"
                  alt="Excluded"
                />
              </div>
            )
          } else if (continuous === 1) {
            return (
              <div className={commonClasses}>
                <div
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                >
                  {standardOfCareMet}
                </div>
              </div>
            )
          } else if (standardOfCareMet === 1) {
            return (
              <div className={commonClasses}>
                <img
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                  src="/images/check.svg"
                  alt="Standard of Care Met"
                />
              </div>
            )
          } else if (standardOfCareMet === 0) {
            return (
              <div className={commonClasses}>
                <img
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                  src="/images/close.svg"
                  alt="Standard of Care Not Met"
                />
              </div>
            )
          }

          return null
        },
      },
    ]
  }

  const generateEncounterMeasureColumns = (): ColumnDef<
    (typeof encounterMeasures)[number]
  >[] => {
    return [
      {
        accessorKey: 'caseIdentifier',
        header: 'Case Identifier',
        cell: ({ getValue }) => getValue(),
      },
      {
        accessorKey: 'measureName',
        header: 'Measure Name',
        cell: ({ getValue }) => getValue(),
      },
      {
        accessorKey: 'standardOfCareMet',
        header: 'Performance',
        cell: ({ row }) => {
          const data = row.original
          const standardOfCareMet = data.standardOfCareMet
          const excluded = data.excluded
          const continuous = data.continuous
          const hoverText = data.hoverText

          const tooltipContent = hoverText
            ? `
            <div>
              <span>${hoverText.substring(0, hoverText.indexOf('Facility'))}</span>
              <p>${hoverText.substring(hoverText.indexOf('Facility'))}</p>
            </div>
          `
            : ''

          if (excluded === 1) {
            return (
              <div className={commonClasses}>
                <img
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                  src="/images/excluded e.svg"
                  alt="Excluded"
                />
              </div>
            )
          } else if (continuous === 1) {
            return (
              <div className={commonClasses}>
                <div
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                >
                  {standardOfCareMet}
                </div>
              </div>
            )
          } else if (standardOfCareMet === 1) {
            return (
              <div className={commonClasses}>
                <img
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                  src="/images/check.svg"
                  alt="Standard of Care Met"
                />
              </div>
            )
          } else if (standardOfCareMet === 0) {
            return (
              <div className={commonClasses}>
                <img
                  className="performanace-img"
                  data-tooltip-id="explorer-tooltip"
                  data-tooltip-content={tooltipContent}
                  data-tooltip-place="bottom"
                  src="/images/close.svg"
                  alt="Standard of Care Not Met"
                />
              </div>
            )
          }

          return null
        },
      },
    ]
  }

  const encounterMeasuresData = useMemo(() => {
    return (encounterMeasures ?? []).filter((measure) => {
      const yearMatch = measure.yearOfMeasure?.toString() === yearTab

      // If showExcluded is false, filter out measures with categoryAssignment 'Excluded (only)'
      // If showExcluded is true, show all measures for that year
      if (!showExcluded) {
        return yearMatch && measure.categoryAssignment !== 'Excluded (only)'
      }
      return yearMatch
    })
  }, [encounterMeasures, yearTab, showExcluded])

  const patientMeasuresData = useMemo(() => {
    return (patientMeasures ?? []).filter((measure) => {
      const yearMatch = measure.yearOfMeasure?.toString() === yearTab

      // Same logic as encounterMeasuresData
      if (!showExcluded) {
        return yearMatch && measure.categoryAssignment !== 'Excluded (only)'
      }
      return yearMatch
    })
  }, [patientMeasures, yearTab, showExcluded])

  const patientMeasuresColumns = useMemo(
    () => generatePatientMeasureColumns(),
    []
  )

  const encounterMeasuresColumns = useMemo(
    () => generateEncounterMeasureColumns(),
    []
  )

  const encounterMeasuresTable = useReactTable({
    data: encounterMeasuresData,
    columns: encounterMeasuresColumns,
    getCoreRowModel: getCoreRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  const patientMeasuresTable = useReactTable({
    data: patientMeasuresData,
    columns: patientMeasuresColumns,
    getCoreRowModel: getCoreRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  return (
    <>
      <PatientData />

      <div className="w-full  mx-auto space-y-4 p-4">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="flex-1" />
          <Tabs
            value={yearTab}
            className="w-[240px]"
            onValueChange={(value) => setYearTab(value)}
          >
            <TabsList className="grid w-full grid-cols-2 !p-0 bg-white">
              {yearTabs.map((year, index) => (
                <TabsTrigger
                  key={year}
                  value={year.toString()}
                  className={cn(
                    'data-[state=active]:bg-[#EBEFFD] data-[state=active]:font-bold border border-ui-medium-blue data-[state=active]:border-blue-3',
                    index === 0 ? 'rounded-l-md rounded-r-none' : '',
                    index === yearTabs.length - 1
                      ? 'rounded-r-md rounded-l-none'
                      : ''
                  )}
                >
                  {year}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>

          <div className="flex-1 flex justify-end">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="excluded"
                checked={showExcluded}
                onCheckedChange={(checked) =>
                  setShowExcluded(checked as boolean)
                }
              />
              <label
                htmlFor="excluded"
                className="text-sm text-muted-foreground"
              >
                Show excluded measures
              </label>
            </div>
          </div>
        </div>

        <div className="flex flex-col space-y-6">
          <div className="order-1">
            <div className="bg-ui-dark-gray py-2">
              <h1 className="font-open-sans font-semibold text-[14px] leading-[19.07px] text-white text-center">
                Encounter Measures
              </h1>
            </div>

            {encounterMeasuresData.length > 0 ? (
              <BasicDataTable table={encounterMeasuresTable} />
            ) : (
              <NoDataAvailable content="No data available for encounter measures" />
            )}
          </div>

          <div
            className={cn(
              patientMeasuresData.length > 0 ? 'order-2' : 'order-last'
            )}
          >
            <div className="bg-ui-dark-gray py-2">
              <h1 className="font-open-sans font-semibold text-[14px] leading-[19.07px] text-white text-center">
                Patient Measures
              </h1>
            </div>

            {patientMeasuresData.length > 0 ? (
              <BasicDataTable table={patientMeasuresTable} />
            ) : (
              <NoDataAvailable content="No data available for patient measures" />
            )}
          </div>

          <div
            className={cn(
              patientMeasuresData.length > 0 ? 'order-last' : 'order-2'
            )}
          >
            <TableLegend showExcluded={showExcluded} />
          </div>
        </div>
      </div>
    </>
  )
}

export default AllMeasures
