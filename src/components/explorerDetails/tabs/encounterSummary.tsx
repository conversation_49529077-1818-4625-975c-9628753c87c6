import { Separator } from '@/components/ui/separator'
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { EncounterDetails } from '../encounterDetails'
import { PatientData } from '../patientData'
import { useExplorerStore } from '@/stores/explorer'

const EncounterSummary = () => {
  const { encounter } = useExplorerStore()

  return (
    <>
      <PatientData />

      <EncounterDetails />

      <div className="bg-gray-pale-gray font-open-sans text-[15px] leading-[20.43px]">
        {/* Second Section with the Same Grid Layout */}
        <div className="grid grid-cols-3 gap-x-4 p-4">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                Admitting MD
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.admittingMD}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                Discharge Disposition
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.dischargeDisposition}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                Principal Procedure Code
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.principalPxCode}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                Charges
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.totalCharges}
              </span>
            </div>
          </div>

          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                Attending MD
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.attendingMD}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                Principal Diagnosis Code
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.principalDxCode}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                Principal Procedure
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.principalPxDescription}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                MS-DRG Code
              </span>
              <Separator orientation="vertical" />

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger className="truncate whitespace-nowrap">
                    <span>{encounter.msDrg}</span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{encounter.msDrg}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                Discharge MD
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.dischargeMD}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                Principal Diagnosis
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.principalDxDescription}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                LOS
              </span>
              <Separator orientation="vertical" />
              <span className="truncate whitespace-nowrap">
                {encounter.los}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
                MS-DRG Description
              </span>
              <Separator orientation="vertical" />

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger className="truncate whitespace-nowrap">
                    <span>{encounter.msDrgDescription}</span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{encounter.msDrgDescription}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>

        <Separator />
      </div>
    </>
  )
}

export default EncounterSummary
