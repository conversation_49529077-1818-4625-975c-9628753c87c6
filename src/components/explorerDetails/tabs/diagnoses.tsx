'use client'

import { NoDataAvailable } from '@/components/dataTable/noDataAvailable'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PatientData } from '../patientData'
import { EncounterDetails } from '../encounterDetails'
import { useState, useMemo } from 'react'
import { useExplorerStore } from '@/stores/explorer'
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import BasicDataTable from '@/components/dataTable/basicDataTable'

const Diagnoses = () => {
  const [diagnosesTab, setDiagnosesTab] = useState('icd-10-diagnoses')
  const { diagnoses } = useExplorerStore()

  const icd10DiagnosesColumns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        accessorKey: 'diagnosis',
        header: 'ICD-10 Diagnosis',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'diagnosisDescription',
        header: 'ICD-10 Diagnosis Description',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'ordinality',
        header: 'Ordinality',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'poaStatus',
        header: 'POA Status',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
    ],
    []
  )

  const otherDiagnosesColumns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        accessorKey: 'diagnosis',
        header: 'Diagnosis',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'diagnosisDescription',
        header: 'Diagnosis Description',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'ordinality',
        header: 'Ordinality',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
      {
        accessorKey: 'poaStatus',
        header: 'POA Status',
        cell: ({ getValue }) => getValue() || 'N/A',
      },
    ],
    []
  )

  const filteredDiagnoses = useMemo(() => {
    if (!diagnoses) return []

    return diagnoses.filter((diagnosis) => {
      if (diagnosesTab === 'icd-10-diagnoses') {
        return diagnosis.codeType === 'ICD10'
      } else {
        return diagnosis.codeType !== 'ICD10'
      }
    })
  }, [diagnoses, diagnosesTab])

  const diagnosesTable = useReactTable({
    data: filteredDiagnoses,
    columns:
      diagnosesTab === 'icd-10-diagnoses'
        ? icd10DiagnosesColumns
        : otherDiagnosesColumns,
    getCoreRowModel: getCoreRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  })

  return (
    <>
      <PatientData />

      <EncounterDetails />

      <div className="w-full mx-auto space-y-4">
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <Tabs
            defaultValue="icd-10-diagnoses"
            className="min-w-[240px] pt-4"
            onValueChange={(value) => setDiagnosesTab(value)}
          >
            <TabsList className="grid w-full grid-cols-2 !p-0 bg-white">
              <TabsTrigger
                className="data-[state=active]:bg-[#EBEFFD] data-[state=active]:font-bold border border-ui-medium-blue data-[state=active]:border-blue-3 rounded-r-none"
                value="icd-10-diagnoses"
              >
                ICD-10 DIAGNOSES
              </TabsTrigger>
              <TabsTrigger
                className="data-[state=active]:bg-[#EBEFFD] data-[state=active]:font-bold border border-ui-medium-blue data-[state=active]:border-blue-3 rounded-l-none"
                value="other-diagnoses"
              >
                OTHER DIAGNOSES
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="flex flex-col">
          {filteredDiagnoses.length > 0 ? (
            <BasicDataTable table={diagnosesTable} />
          ) : (
            <NoDataAvailable
              content={`No ${diagnosesTab === 'icd-10-diagnoses' ? 'ICD-10' : 'other'} diagnoses available for selected criteria`}
            />
          )}
        </div>
      </div>
    </>
  )
}

export default Diagnoses
