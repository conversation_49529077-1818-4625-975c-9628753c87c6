import Image from 'next/image'

export const TableLegend = ({ showExcluded }: { showExcluded: boolean }) => (
  <div className="flex justify-center items-center space-x-2">
    <span className="flex items-center space-x-2 font-open-sans font-semibold text-[15px] leading-[20.43px] text-center text-ui-dark-gray">
      <Image src="/images/check.svg" alt="check" width={24} height={24} />
      STANDARD OF CARE MET
    </span>
    <span className="flex items-center space-x-2 font-open-sans font-semibold text-[15px] leading-[20.43px] text-center text-ui-dark-gray">
      <Image src="/images/close.svg" alt="check" width={24} height={24} />
      STANDARD OF CARE NOT MET
    </span>
    {showExcluded && (
      <span className="flex items-center space-x-2 font-open-sans font-semibold text-[15px] leading-[20.43px] text-center text-ui-dark-gray">
        <Image
          src="/images/excluded e.svg"
          alt="check"
          width={24}
          height={24}
        />
        EXCLUDED MEASURE
      </span>
    )}
  </div>
)
