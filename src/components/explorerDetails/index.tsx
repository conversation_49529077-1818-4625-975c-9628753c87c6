'use client'

import { useEffect, useState } from 'react'
import { ExplorerNav } from '../explorerNav'
import { useSearchParams } from 'next/navigation'
import { ExplorerTabValue } from '@/types/explorerTabValue'
import React from 'react'
import PatientProfile from './tabs/patientProfile'
import EncounterHistory from './tabs/encounterHistory'
import AllMeasures from './tabs/allMeasures'
import EncounterSummary from './tabs/encounterSummary'
import Diagnoses from './tabs/diagnoses'
import Procedures from './tabs/procedures'
import EncounterMeasures from './tabs/encounterMeasures'
import Insurance from './tabs/insurance'
import { api } from '@/trpc/react'
import { useExplorerStore } from '@/stores/explorer'
import {
  EncounterDataResult,
  EncounterHistoryResult,
  EncounterMeasureResult,
  PatientMeasureResult,
  PatientDataResult,
  DiagnosesResult,
  InsuranceResult,
  ProceduresResult,
  CurrentEncounterMeasureResult,
} from '@/services/explorer/getSearchResultQuery'
import dayjs, { Dayjs } from 'dayjs'
import Loader from '../ui/Loader'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

export const ExplorerDetails = () => {
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState<ExplorerTabValue>()

  useEffect(() => {
    const searchType = searchParams.get('type')

    if (searchType) {
      setActiveTab(
        searchType === 'patients' ? 'patientProfile' : 'encounterSummary'
      )
    }
  }, [searchParams.get('type')])

  const searchResults = api.explorer.search.useQuery(
    {
      patientId: searchParams.get('pid') ?? '',
      encounterId: searchParams.get('eid') ?? '',
      tab: activeTab ?? '',
    },
    {
      enabled: !!activeTab,
    }
  )

  useEffect(() => {
    if (searchResults.data) {
      const data = searchResults.data

      if (Array.isArray(data) && data.length > 0 && Array.isArray(data[0])) {
        let patientRecord: {
          patientName: string
          age: number
          dob: Dayjs
          gender: string
          patientIdentifier: string
        }
        let demography
        let contactInfo
        let encounter: {
          caseIdentifier: string
          sourceEncounterIdentifier: string
          encounterTypeText: string
          admitPriority: string
          facility: string
          edArrivalDateTime: string
          edEndDateTime: string
          admitDateTime: Dayjs
          dischargeDate: Dayjs
          admittingMD: string
          attendingMD: string
          dischargeMD: string
          dischargeDisposition: string
          principalDxCode: string
          principalDxDescription: string
          dischargeName: string
          principalPxCode: string
          principalPxDescription: string
          los: string
          totalCharges: string
          msDrg: string
          msDrgDescription: string
        }

        switch (activeTab) {
          case 'patientProfile':
            // update patient info
            patientRecord = {
              patientName: (data[0][0] as PatientDataResult)?.PatientName!,
              age: (data[0][0] as PatientDataResult)?.Age!,
              dob: (data[0][0] as PatientDataResult)?.DOB!,
              gender: (data[0][0] as PatientDataResult)?.Gender!,
              patientIdentifier: (data[0][0] as PatientDataResult)
                ?.PatientIdentifier!,
            }

            // Update patient demography
            demography = {
              sourcePatientIdentifier: (data[0][0] as PatientDataResult)
                ?.SourcePatientIdentifier,
              race: (data[0][0] as PatientDataResult)?.Race,
              ethnicity: (data[0][0] as PatientDataResult)?.Ethnicity,
              maritalStatus: (data[0][0] as PatientDataResult)?.MaritalStatus,
            }

            contactInfo = {
              address1: (data[0][0] as PatientDataResult)?.Address1,
              address2: (data[0][0] as PatientDataResult)?.Address2,
              city: (data[0][0] as PatientDataResult)?.City,
              state: (data[0][0] as PatientDataResult)?.State,
              zipcode: (data[0][0] as PatientDataResult)?.Zipcode,
              phone: (data[0][0] as PatientDataResult)?.Phone,
            }

            useExplorerStore.setState({
              patient: {
                ...patientRecord,
                ...demography,
                ...contactInfo,
              },
            })
            break
          case 'encounterSummary':
            // update patient info
            patientRecord = {
              patientName: (data[0][0] as PatientDataResult)?.PatientName!,
              age: (data[0][0] as PatientDataResult)?.Age!,
              dob: (data[0][0] as PatientDataResult)?.DOB!,
              gender: (data[0][0] as PatientDataResult)?.Gender!,
              patientIdentifier: (data[0][0] as PatientDataResult)
                ?.PatientIdentifier!,
            }

            encounter = {
              caseIdentifier: (data?.[0]?.[0] as EncounterDataResult)
                ?.CaseIdentifier,
              sourceEncounterIdentifier: (data?.[0]?.[0] as EncounterDataResult)
                ?.SourceEncounterIdentifier,
              encounterTypeText: (data?.[0]?.[0] as EncounterDataResult)
                ?.EncounterTypeText,
              admitPriority: (data?.[0]?.[0] as EncounterDataResult)
                ?.AdmitPriority,
              facility: (data?.[0]?.[0] as EncounterDataResult)?.Facility,
              edArrivalDateTime: (data?.[0]?.[0] as EncounterDataResult)
                ?.EDArrivalDateTime,
              edEndDateTime: (data?.[0]?.[0] as EncounterDataResult)
                ?.EDEndDateTime,
              admitDateTime: (data?.[0]?.[0] as EncounterDataResult)
                ?.AdmitDateTime,
              dischargeDate: (data?.[0]?.[0] as EncounterDataResult)
                ?.DischargeDate,
              admittingMD: (data?.[0]?.[0] as EncounterDataResult)?.AdmittingMD,
              attendingMD: (data?.[0]?.[0] as EncounterDataResult)?.AttendingMD,
              dischargeMD: (data?.[0]?.[0] as EncounterDataResult)?.DischargeMD,
              dischargeDisposition: (data?.[0]?.[0] as EncounterDataResult)
                ?.DischargeDisposition,
              principalDxCode: (data?.[0]?.[0] as EncounterDataResult)
                ?.PrincipalDxCode,
              principalDxDescription: (data?.[0]?.[0] as EncounterDataResult)
                ?.PrincipalDxDescription,
              dischargeName: (data?.[0]?.[0] as EncounterDataResult)
                ?.DischargeName,
              principalPxCode: (data?.[0]?.[0] as EncounterDataResult)
                ?.PrincipalPxCode,
              principalPxDescription: (data?.[0]?.[0] as EncounterDataResult)
                ?.PrincipalPxDescription,
              los: (data?.[0]?.[0] as EncounterDataResult)?.LOS,
              totalCharges: (data?.[0]?.[0] as EncounterDataResult)
                ?.TotalCharges,
              msDrg: (data?.[0]?.[0] as EncounterDataResult)?.MSDRG,
              msDrgDescription: (data?.[0]?.[0] as EncounterDataResult)
                ?.MSDRGDescription,
            }

            useExplorerStore.setState((state) => ({
              patient: {
                ...state.patient,
                ...patientRecord,
              },
              encouter: {
                ...state.encounter,
                ...encounter,
              },
            }))
            break
          case 'encounterMeasures':
            const currentEncounterMeasures =
              data[0] as CurrentEncounterMeasureResult[]

            useExplorerStore.setState({
              currentEncounterMeasures: currentEncounterMeasures.map(
                (measure) => ({
                  description: measure.Description,
                  continuous: measure.Continuous,
                  facility: measure.Facility,
                  excluded: measure.Excluded,
                  hoverText: measure.HoverText,
                  inverseMeasure: measure.InverseMeasure,
                  measureName: measure.MeasureName,
                  standardOfCareMet: measure.StandardOfCareMet,
                })
              ),
            })
            break
          case 'encounterHistory':
            useExplorerStore.setState({
              encounterHistory: (data[0] as EncounterHistoryResult[])?.map(
                (encounterHistory) => ({
                  admitDateTime: dayjs.utc(encounterHistory.AdmitDateTime),
                  caseIdentifier: encounterHistory.CaseIdentifier,
                  dischargeDate: dayjs.utc(encounterHistory.DischargeDate),
                  encounterId: encounterHistory.EncounterId,
                  encounterType: encounterHistory.EncounterType,
                  facility: encounterHistory.Facility,
                  patientId: encounterHistory.PatientId,
                })
              ),
            })
            break
          case 'allMeasures':
            const patientMeasures = data[0] as PatientMeasureResult[]
            const encounterMeasures = data[1] as EncounterMeasureResult[]

            useExplorerStore.setState({
              patientMeasures: patientMeasures.map((patientMeasure) => ({
                measureName: patientMeasure.MeasureName,
                standardOfCareMet: patientMeasure.StandardOfCareMet,
                excluded: patientMeasure.Excluded,
                continuous: patientMeasure.Continuous,
                hoverText: patientMeasure.HoverText,
                yearOfMeasure: patientMeasure.YearOfMeasure,
                categoryAssignment: patientMeasure.CategoryAssignment,
              })),
              encounterMeasures: encounterMeasures.map((encounterMeasure) => ({
                caseIdentifier: encounterMeasure.CaseIdentifier,
                measureName: encounterMeasure.MeasureName,
                standardOfCareMet: encounterMeasure.StandardOfCareMet,
                excluded: encounterMeasure.Excluded,
                continuous: encounterMeasure.Continuous,
                hoverText: encounterMeasure.HoverText,
                yearOfMeasure: encounterMeasure.YearOfMeasure,
                categoryAssignment: encounterMeasure.CategoryAssignment,
              })),
            })

            break
          case 'diagnoses':
            const diagnoses = data[0] as DiagnosesResult[]

            useExplorerStore.setState({
              diagnoses: diagnoses.map((diagnosis) => ({
                codeType: diagnosis.CodeType,
                diagnosis: diagnosis.Diagnosis,
                diagnosisDescription: diagnosis.DiagnosisDescription,
                ordinality: diagnosis.Ordinality,
                poaStatus: diagnosis.POAStatus,
              })),
            })

            break
          case 'procedures':
            const procedures = data[0] as ProceduresResult[]

            useExplorerStore.setState({
              procedures: procedures.map((procedure) => ({
                procedureCode: procedure.ProcedureCode,
                codeSystem: procedure.CodeSystem,
                procedureDescription: procedure.ProcedureDescription,
                ordinality: procedure.Ordinality,
                procedureProvider: procedure.ProcedureProvider,
                procedureDate: dayjs.utc(procedure.ProcedureDate),
                codeType: procedure.CodeType,
              })),
            })

            break
          case 'insurance':
            const insurances = data[0] as InsuranceResult[]

            useExplorerStore.setState({
              insurance: insurances.map((insurance) => ({
                coverage: insurance.Coverage,
                effectiveDate: dayjs.utc(insurance.EffectiveDate),
                expirationDate: dayjs.utc(insurance.ExpirationDate),
                insuranceCarrier: insurance.InsuranceCarrier,
                ordinality: insurance.Ordinality,
              })),
            })

            break

          default:
            console.log('data', data)

            // story/MP-5 Similar process as insurance and procedures

            break
        }
      }
    }
  }, [searchResults.data])

  return (
    <>
      <ExplorerNav activeTab={activeTab} setActiveTab={setActiveTab} />

      <div className="w-full shadow-[-9px_-7px_4px_-9px_#00000024] px-6">
        {searchResults.isPending ? (
          <div className="flex justify-center items-center min-h-[200px]">
            <Loader />
          </div>
        ) : (
          <>
            {activeTab === 'patientProfile' && <PatientProfile />}

            {activeTab === 'encounterHistory' && <EncounterHistory />}

            {activeTab === 'allMeasures' && <AllMeasures />}

            {activeTab === 'encounterSummary' && <EncounterSummary />}

            {activeTab === 'encounterMeasures' && <EncounterMeasures />}

            {activeTab === 'diagnoses' && <Diagnoses />}

            {activeTab === 'procedures' && <Procedures />}

            {activeTab === 'insurance' && <Insurance />}
          </>
        )}
      </div>
    </>
  )
}
