import { useExplorerStore } from '@/stores/explorer'
import { Separator } from '../ui/separator'
import dayjs from 'dayjs'

export const EncounterDetails = () => {
  const { encounter } = useExplorerStore()

  return (
    <div className="bg-gray-pale-gray font-open-sans text-[15px] leading-[20.43px]">
      {/* Grid Layout for Three Equal Columns */}
      <div className="grid grid-cols-3 gap-x-4 p-4">
        {/* Column 1 */}
        <div className="flex flex-col space-y-2">
          <div className="flex items-center space-x-2">
            <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
              Case Identifier
            </span>
            <Separator orientation="vertical" />
            <span className="truncate whitespace-nowrap">
              {encounter.caseIdentifier}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
              Admit Priority
            </span>
            <Separator orientation="vertical" />
            <span className="truncate whitespace-nowrap">
              {encounter.admitPriority}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
              ED Departure
            </span>
            <Separator orientation="vertical" />
            <span className="truncate whitespace-nowrap">
              {encounter.edEndDateTime}
            </span>
          </div>
        </div>

        {/* Column 2 */}
        <div className="flex flex-col space-y-2">
          <div className="flex items-center space-x-2">
            <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
              Source Encounter
            </span>
            <Separator orientation="vertical" />
            <span className="truncate whitespace-nowrap">
              {encounter.sourceEncounterIdentifier}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
              Facility
            </span>
            <Separator orientation="vertical" />
            <span className="truncate whitespace-nowrap">
              {encounter.facility}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
              Admit Date
            </span>
            <Separator orientation="vertical" />
            <span className="truncate whitespace-nowrap">
              {dayjs(encounter.admitDateTime).format('MMM DD YYYY h:mmA')}
            </span>
          </div>
        </div>

        {/* Column 3 */}
        <div className="flex flex-col space-y-2">
          <div className="flex items-center space-x-2">
            <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
              Encounter Type
            </span>
            <Separator orientation="vertical" />
            <span className="truncate whitespace-nowrap">
              {encounter.encounterTypeText}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
              ED Arrival
            </span>
            <Separator orientation="vertical" />
            <span className="truncate whitespace-nowrap">
              {encounter.edArrivalDateTime}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-ui-dark-gray font-semibold truncate whitespace-nowrap">
              Discharge Date
            </span>
            <Separator orientation="vertical" />
            <span className="truncate whitespace-nowrap">
              {encounter.dischargeDate.format('MMM DD YYYY h:mmA')}
            </span>
          </div>
        </div>
      </div>

      {/* Separator Between Sections */}
      <Separator />
    </div>
  )
}
