import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import { isSessionAuthenticated } from './lib/isSessionAuthenticated'
import { isSessionExpired } from './lib/isSessionExpired'
import { CitcUserRoles } from './shared/roles'
import { decode as jwtDecode, JwtPayload } from 'jsonwebtoken'

// import appInsights from './lib/applicationInsights'
// import { headers } from 'next/headers'



export default auth(async (req) => {
  const isAuthenticated = isSessionAuthenticated(req.auth)
  const isExpired = isSessionExpired(req.auth)

  // Allow public routes without redirect
  const publicPaths = ['/auth/error', '/session/expired', '/signout-callback-oidc']
  const isPublic = publicPaths.some(path => req.nextUrl.pathname.startsWith(path))

  // 1. If the user is NOT AUTHENTICATED:
  //    - Usually, send them to sign-in if they are trying to access something other than '/'
  if (!isAuthenticated && !isPublic) {
    if (req.nextUrl.pathname !== '/') {
      const signInUrl = new URL(`/api/auth/signin-oidc`, req.url)
      return NextResponse.redirect(signInUrl)
    }
  }

  // 2. If this is a public page, let it pass through
  if (isPublic) {
    return NextResponse.next()
  }

  // 3. If the user IS AUTHENTICATED but the session is EXPIRED:
  //    - Typically, send them to a dedicated “session expired” page
  if (isAuthenticated && isExpired) {
    console.log('Session has expired for an authenticated user.')
    return NextResponse.redirect(new URL('/session/expired', req.url))
  }

  // 4. If the user is authenticated, not expired, and at '/', redirect them
  //    to whichever page you consider your "main" page.
  if (isAuthenticated && !isExpired && req.nextUrl.pathname === '/') {
    return NextResponse.redirect(new URL('/init', req.url)) // TODO: This may be dashboard in some cases. Need to figure out how to handle one day
  }

  // 5. For role-based routes, we'll do a preliminary check
  if (isAuthenticated && !isExpired) {
    let userRoles: string[] = []

    if (req.auth?.accessToken) {
      try {
        const decodedToken = jwtDecode(req.auth.accessToken) as JwtPayload

        // Get the x-organization-id from cookies
        const cookies = req.cookies
        const currentOrgId = cookies.get('x-organization-id')?.value
        if (currentOrgId && decodedToken['role:Organization']) {
          const citcRoles = decodedToken['role:Organization']
          // Handle both array and single object cases
          if (Array.isArray(citcRoles)) {
            const orgRoles = citcRoles
              .map(role => JSON.parse(role))
              .find(ur => ur.Id === currentOrgId)

            if (orgRoles) {
              userRoles = orgRoles.Roles
            }
          } else if (typeof citcRoles === 'string') {
            const parsedRole = JSON.parse(citcRoles)
            if (parsedRole.Id === currentOrgId) {
              userRoles = parsedRole.Roles
            }
          }
        }

      } catch (error) {
        console.error('Error decoding JWT or processing roles:', error)
      }
    }

    if (req.nextUrl.pathname.startsWith('/hub/administration/manage-reports')) {
      if (!userRoles.includes(CitcUserRoles.REPORT_ADMINISTRATOR)) {
        console.warn('User attempting to access report admin area without proper role. Roles: ', userRoles)
        return NextResponse.redirect(new URL('/auth/error', req.url))
      }
    }

    // Add other page/role checks as needed
  }

  // 6. If none of the above conditions apply, allow the request to continue:
  return NextResponse.next()
})

export const config = {
  runtime: 'nodejs', // Enforce the Node.js runtime
  matcher: [
    '/((?!assets|static|favicon.ico|_next|images|vids|robots.txt|sitemap.xml|api/auth|session/expired|signout-callback-oidc|api/health).*)',
  ], // Exclude assets, static, API routes, Next.js system paths, auth session pages, and public files
}

// [auth][error] InvalidCheck: pkceCodeVerifier value could not be parsed. Read more at https://errors.authjs.dev#invalidcheck
// at parseCookie (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/lib/actions/callback/oauth/checks.js:53:15)
//   at Object.eval [as use] (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/lib/actions/callback/oauth/checks.js:75:30)
//   at handleOAuth (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/lib/actions/callback/oauth/callback.js:117:77)
//   at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
//   at async Module.callback (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/lib/actions/callback/index.js:47:41)
//   at async AuthInternal (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/lib/index.js:43:24)
//   at async Auth (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/index.js:130:34)
//   at async AppRouteRouteModule.do (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:33313)
//   at async AppRouteRouteModule.handle (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:40382)
//   at async doRender (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:1455:42)
//   at async responseGenerator (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:1814:28)
//   at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:1824:28)
//   at async DevServer.renderPageComponent (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:2240:24)
//   at async DevServer.renderToResponseImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:2278:32)
//   at async DevServer.pipeImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:960:25)
//   at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/next-server.js:281:17)
//   at async DevServer.handleRequestImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:853:17)
//   at async /home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/dev/next-dev-server.js:373:20
//   at async Span.traceAsyncFn (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/trace/trace.js:153:20)
//   at async DevServer.handleRequest (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/dev/next-dev-server.js:370:24)
//   at async invokeRender (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/lib/router-server.js:183:21)
//   at async handleRequest (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/lib/router-server.js:360:24)
//   at async requestHandlerImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/lib/router-server.js:384:13)
//   at async Server.requestListener (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/lib/start-server.js:142:13)

// After logging out
// [auth][cause]: OperationProcessingError: response parameter "iss" (issuer) missing
//     at OPE (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/oauth4webapi/build/index.js:179:12)
//     at Module.validateAuthResponse (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/oauth4webapi/build/index.js:2119:15)
//     at handleOAuth (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/lib/actions/callback/oauth/callback.js:104:69)
//     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
//     at async Module.callback (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/lib/actions/callback/index.js:47:41)
//     at async AuthInternal (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/lib/index.js:43:24)
//     at async Auth (webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/index.js:130:34)
//     at async AppRouteRouteModule.do (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:33313)
//     at async AppRouteRouteModule.handle (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:40382)
//     at async doRender (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:1455:42)
//     at async responseGenerator (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:1814:28)
//     at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:1824:28)
//     at async DevServer.renderPageComponent (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:2240:24)
//     at async DevServer.renderToResponseImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:2278:32)
//     at async DevServer.pipeImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:960:25)
//     at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/next-server.js:281:17)
//     at async DevServer.handleRequestImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/base-server.js:853:17)
//     at async /home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/dev/next-dev-server.js:373:20
//     at async Span.traceAsyncFn (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/trace/trace.js:153:20)
//     at async DevServer.handleRequest (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/dev/next-dev-server.js:370:24)
//     at async invokeRender (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/lib/router-server.js:183:21)
//     at async handleRequest (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/lib/router-server.js:360:24)
//     at async requestHandlerImpl (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/lib/router-server.js:384:13)
//     at async Server.requestListener (/home/<USER>/Projects/platform-nextgen/node_modules/next/dist/server/lib/start-server.js:142:13)
// app-page.runtime.dev.js:114
// [auth][details]: {
//   "parameters": {},
//   "provider": "citc-oidc"
// }
