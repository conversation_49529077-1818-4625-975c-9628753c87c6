import NextAuth from 'next-auth'
import { env } from '@/env'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { decodeToken, decodedToken } from './lib/decodeAccessToken'
import { JwtPayload } from 'jsonwebtoken'
import type { Account } from 'next-auth'
import crypto from 'crypto'

const refreshPromises = new Map<string, Promise<any>>()
const CACHE_TTL_SECONDS = 300

// Add max age to cached tokens
export let tokenCache = new Map<
  string,
  {
    access_token: string
    refresh_token: string
    expires_at: number
    cachedAt: number
  }
>()

export function __resetTokenCache() {
  tokenCache = new Map<string, any>()
}

export const signInCallback = async ({ account }: { account?: Account | null }) => {
  if (account) {
    let accessToken: JwtPayload | null = null
    if (account?.access_token) {
      accessToken = decodeToken(account.access_token)
    }

    // Check for required roles
    const hasRequiredPermissions =
      (Array.isArray(accessToken?.['role:Organization']) && accessToken['role:Organization'].length > 0) ||
      (accessToken?.['role:Organization'] && !Array.isArray(accessToken['role:Organization'])) ||
      !!accessToken?.['global:Partner']
    if (hasRequiredPermissions) {
      return true // allow login
    }
  }
  return false
}

export const jwtCallback = async (props: {
  token: any
  trigger?: "signIn" | "signUp" | "update"
  session?: any
  account?: Account | null
}) => {
  const { token, trigger, session, account } = props

  // Strategy: This function handles token management, including initial login,
  // session updates, and token refreshing. It uses caching to optimize performance
  // and prevent unnecessary token refreshes.

  // Handle initial login
  if (account && account.expires_in) {
    return {
      ...token,
      access_token: account.access_token,
      refresh_token: account.refresh_token,
      expires_at: Math.floor(Date.now() / 1000) + account.expires_in,
    }
  }

  // Handle session updates
  if (trigger === 'update' && session) {
    return {
      ...token,
      expires_at: Math.min(token.expires_at as number, Math.floor(new Date(session.expires).getTime() / 1000)),
      uid: session.uid || token.uid,
    }
  }

  // Check if token is expired (with a 30-second buffer)
  const isExpired = token.expires_at ? Date.now() + 30 * 1000 > (token.expires_at as number) * 1000 : true
  if (!isExpired) return token

  // Token refresh logic
  if (token.refresh_token) {
    // Key decision: Use a hash of the refresh token as the cache key for security
    const cacheKey = token.sub || token.jti || crypto.createHash('sha256').update(token.refresh_token).digest('hex')

    // Check cache for a recently refreshed token
    const cachedResult = tokenCache.get(cacheKey)
    if (cachedResult && Date.now() / 1000 - cachedResult.cachedAt < CACHE_TTL_SECONDS) {
      tokenCache.delete(cacheKey)
      return { ...token, ...cachedResult }
    }

    // Key decision: Use a promise-based approach to prevent multiple simultaneous refreshes
    if (refreshPromises.has(cacheKey)) {
      return await refreshPromises.get(cacheKey)
    }

    // Perform token refresh
    const refreshPromise = (async () => {
      try {
        // Fetch the token endpoint from the well-known configuration
        const wellKnown = await fetch(env.NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG!).then((res) => res.json())
        const tokenEndpoint = wellKnown.token_endpoint
        if (!tokenEndpoint) throw new Error('Token endpoint not found')

        // Perform the token refresh request
        const response = await fetch(tokenEndpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: new URLSearchParams({
            client_id: env.NEXT_PUBLIC_OIDC_CLIENT_ID!,
            client_secret: env.NEXT_PUBLIC_OIDC_CLIENT_SECRET!,
            grant_type: 'refresh_token',
            refresh_token: token.refresh_token,
          }),
        })

        const refreshedTokens = await response.json()
        if (!response.ok || !refreshedTokens.access_token) {
          throw new Error(refreshedTokens.error || 'Failed to refresh token')
        }

        // Create the new token object
        const newToken = {
          ...token,
          access_token: refreshedTokens.access_token,
          refresh_token: refreshedTokens.refresh_token || token.refresh_token,
          expires_at: Math.floor(Date.now() / 1000) + refreshedTokens.expires_in,
        }

        // Cache the new token
        tokenCache.set(cacheKey, {
          access_token: newToken.access_token,
          refresh_token: newToken.refresh_token,
          expires_at: newToken.expires_at,
          cachedAt: Math.floor(Date.now() / 1000),
        })

        return newToken
      } catch (error: unknown) {
        console.log('Error refreshing token', { 
          error: error instanceof Error ? error.message : String(error), 
          cacheKey 
        })
        // Return an error object if refresh fails
        return {
          ...token,
          error: 'RefreshTokenError',
          status: 'INVALID',
          message: error instanceof Error ? error.message : String(error),
          expires_at: 0,
        }
      } finally {
        // Clean up the cache and promise
        tokenCache.delete(cacheKey)
      }
    })()

    // Store the refresh promise and wait for it to resolve
    refreshPromises.set(cacheKey, refreshPromise)
    try {
      return await refreshPromise
    } finally {
      refreshPromises.delete(cacheKey)
    }
  }

  // Handle case where refresh token is missing
  console.log('Missing refresh_token', { cacheKey: token.sub || token.jti })
  return {
    ...token,
    error: 'NoRefreshToken',
    status: 'INVALID',
    expires_at: 0,
  }
}

const cleanupStaleCache = () => {
  const now = Date.now()
  for (const [key, value] of tokenCache.entries()) {
    if (now - value.cachedAt > 2 * 60 * 1000) {
      // 2 minutes instead of 5
      tokenCache.delete(key)
    }
  }
}

// Call cleanup periodically
setInterval(cleanupStaleCache, 60 * 1000) // Every minute

dayjs.extend(utc)

const {
  auth,
  handlers,
  signIn,
  signOut,
  unstable_update: update,
} = NextAuth({
  // debug: true,
  providers: [
    {
      id: 'citc-oidc',
      name: 'CitC OIDC Provider', // This will appear on the sign-in button
      type: 'oidc',
      issuer: env.NEXT_PUBLIC_OIDC_AUTHORITY,
      clientId: env.NEXT_PUBLIC_OIDC_CLIENT_ID,
      clientSecret: env.NEXT_PUBLIC_OIDC_CLIENT_SECRET,
      wellKnown: env.NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG,
      authorization: {
        params: {
          scope: env.NEXT_PUBLIC_OIDC_SCOPE,
          redirect_uri: env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNIN,
          audience: env.NEXT_PUBLIC_OIDC_AUDIENCE,
          response_type: 'code',
        },
      },
    },
  ],
  callbacks: {
    authorized: async ({ auth }) => {
      return !!auth
    },
    signIn: signInCallback,
    jwt: jwtCallback,
    async session(props) {
      const { session, token } = props

      return {
        ...session,
        expires: dayjs
          .unix(token.expires_at as number)
          .utc()
          .toISOString(), // Convert `expires_at` to ISO format
        accessToken: token.access_token,
        idToken: token.id_token,
        uid: decodedToken.getUserId(token.access_token as string),
      }
    },
  },
  pages: {
    error: '/auth/error',
  },
})

export { auth, handlers, signIn, signOut, update }
