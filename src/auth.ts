import NextAuth from 'next-auth'
import { env } from '@/env'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { decodeToken, decodedToken } from './lib/decodeAccessToken'
import { JwtPayload } from 'jsonwebtoken'
import type { Account } from 'next-auth'
import crypto from 'crypto'
import CitCWellKnownConfig from '@/services/citc/citCWellKnownConfig'

const refreshPromises = new Map<string, Promise<any>>()
const CACHE_TTL_SECONDS = 300

// Leaving this defined, but have actually commented out any calls to setting this cache. It is in memory, so no approriate for our cloud deployemnt.
// Could convert it to redis, or, with this change, just not use it at all.
export let tokenCache = new Map<
  string,
  {
    access_token: string
    refresh_token: string
    expires_at: number
    cachedAt: number
  }
>()

export function __resetTokenCache() {
  tokenCache = new Map<string, any>()
}

export const signInCallback = async ({ account }: { account?: Account | null }) => {
  if (account) {
    let accessToken: JwtPayload | null = null
    if (account?.access_token) {
      accessToken = decodeToken(account.access_token)
    }

    // Check for required roles
    const hasRequiredPermissions =
      (Array.isArray(accessToken?.['role:Organization']) && accessToken['role:Organization'].length > 0) ||
      (accessToken?.['role:Organization'] && !Array.isArray(accessToken['role:Organization'])) ||
      !!accessToken?.['global:Partner']
    if (hasRequiredPermissions) {
      return true // allow login
    }
  }
  return false
}

export const jwtCallback = async (props: {
  token: any
  trigger?: "signIn" | "signUp" | "update"
  session?: any
  account?: Account | null
}) => {
  const { token, trigger, session, account } = props
  // Strategy: This function handles token management, including initial login,
  // session updates, and token refreshing. It uses caching to optimize performance
  // and prevent unnecessary token refreshes.
  // Handle initial login
  if (account && account.expires_in) {
    return {
      ...token,
      access_token: account.access_token,
      refresh_token: account.refresh_token,
      id_token: account.id_token,
      expires_at: Math.floor(Date.now() / 1000) + account.expires_in,
    }
  }

  const isExpired = token.expires_at ? Date.now() + 30 * 1000 > (token.expires_at as number) * 1000 : true
  // Handle session updates
  if (!isExpired) {
    return token
  }

  // Token refresh logic
  if (token.refresh_token) {
    // Key decision: Use a hash of the refresh token as the cache key for security
    const cacheKey = token.sub || token.jti || crypto.createHash('sha256').update(token.refresh_token).digest('hex')

    // Key decision: Use a promise-based approach to prevent multiple simultaneous refreshes
    if (refreshPromises.has(cacheKey)) {
      return await refreshPromises.get(cacheKey)
    }

    // Perform token refresh
    const refreshPromise = (async () => {
      try {
        console.log('Refreshing token')
        // Fetch the token endpoint from the well-known configuration
        const citcService = CitCWellKnownConfig.getInstance()
        
        // Perform the token refresh request
        const refreshedTokens = await citcService.tokenEndpoint(token.refresh_token, 'refresh_token')

        // Create the new token object
        const newToken = {
          ...token,
          access_token: refreshedTokens.access_token,
          refresh_token: refreshedTokens.refresh_token || token.refresh_token,
          expires_at: Math.floor(Date.now() / 1000) + refreshedTokens.expires_in,
        }

        return newToken
      } catch (error: unknown) {
        console.log('Error refreshing token', { 
          error: error instanceof Error ? error.message : String(error), 
          cacheKey 
        })
        // Return an error object if refresh fails
        return token
      }
    })()

    // Store the refresh promise and wait for it to resolve
    refreshPromises.set(cacheKey, refreshPromise)
    try {
      return await refreshPromise
    } finally {
      refreshPromises.delete(cacheKey)
    }
  }

  // Handle case where refresh token is missing
  console.log('Missing refresh_token', { cacheKey: token.sub || token.jti })
  return token
}

export function clearUserTokenCache(userId: string) {
  for (const [key, value] of tokenCache.entries()) {
    // Check if the key starts with the userId (which should be the token.sub)
    if (key.startsWith(userId)) {
      tokenCache.delete(key)
      continue; // Move to the next iteration after deleting
    }
    
    // If the key is a hash (64 characters long), we need to check the decoded token
    if (key.length === 64) {
      try {
        const decodedToken = decodeToken(value.access_token)
        if (decodedToken && decodedToken.sub === userId) {
          tokenCache.delete(key)
        }
      } catch (error) {
        console.error('Error decoding token while clearing cache:', error)
      }
    }
  }
}

const cleanupStaleCache = () => {
  const now = Date.now()
  for (const [key, value] of tokenCache.entries()) {
    if (now - value.cachedAt > 2 * 60 * 1000) {
      // 2 minutes instead of 5
      tokenCache.delete(key)
    }
  }
}

// Call cleanup periodically
setInterval(cleanupStaleCache, 60 * 1000) // Every minute

dayjs.extend(utc)

const {
  auth,
  handlers,
  signIn,
  signOut,
  unstable_update: update,
} = NextAuth({
  // debug: true,
  providers: [
    {
      id: 'citc-oidc',
      name: 'CitC OIDC Provider', // This will appear on the sign-in button
      type: 'oidc',
      issuer: env.NEXT_PUBLIC_OIDC_AUTHORITY,
      clientId: env.NEXT_PUBLIC_OIDC_CLIENT_ID,
      clientSecret: env.NEXT_PUBLIC_OIDC_CLIENT_SECRET,
      wellKnown: env.NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG,
      authorization: {
        params: {
          scope: env.NEXT_PUBLIC_OIDC_SCOPE,
          redirect_uri: env.NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNIN,
          audience: env.NEXT_PUBLIC_OIDC_AUDIENCE,
          response_type: 'code',
        },
      },
      idToken: true,
      checks: ["pkce", "state"],
    },
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    authorized: async ({ auth }) => {
      return !!auth
    },
    signIn: signInCallback,
    jwt: jwtCallback,
    async session(props) {
      const { session, token } = props
      // console.log('session update', { session, token })
      return {
        ...session,
        expires: dayjs
          .unix(token.expires_at as number)
          .utc()
          .toISOString(), // Convert `expires_at` to ISO format
        accessToken: token.access_token,
        idToken: token.id_token,
        uid: decodedToken.getUserId(token.access_token as string),
      }
    },
  },
  pages: {
    error: '/auth/error',
  },
})

export { auth, handlers, signIn, signOut, update }
