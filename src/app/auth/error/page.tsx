'use client'

import { useSearchParams } from 'next/navigation'
import { ErrorPageHeader } from '@/components/errorPageHeader'
import { cn } from '@/lib/utils'

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')

  return (
      <>
        <ErrorPageHeader />
        <div className="py-32">
          <div className="flex flex-col space-y-8 text-center">
            <h2
              className={cn(
                'font-bold font-open-sans text-[28px] font-bold',
                'leading=[38.13px]'
              )}
            >
              Insufficient Permissions
            </h2>
            <p
              className={cn(
                'font-open-sans text-[25px] font-normal',
                'leading-[34.05px]',
                'text-center'
              )}
            >
              Please contact your organization administrator to request the necessary permissions.
            </p>
          </div>
        </div>
      </>
    )
}