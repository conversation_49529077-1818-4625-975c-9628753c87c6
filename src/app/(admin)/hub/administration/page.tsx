'use client'

import { ClipboardList, Contact, Info } from 'lucide-react'
import { useMemo } from 'react'
import { AdminItem } from '@/types/adminItem'
import { AdminItemContainer } from '@/components/adminItemContainer'
import { MdSettings as MdSettingsIcon } from 'react-icons/md'
import { LuFiles } from 'react-icons/lu'
import { CiViewList } from 'react-icons/ci'
import { Search } from 'lucide-react'
import { BiSolidUserDetail } from 'react-icons/bi'
import { CitcUserRoles } from '@/shared/roles'
import { useHasPermission } from '@/hooks/useHasPermission'
import Loader from '@/components/ui/Loader'

const testAdminItems: AdminItem[] = [
  {
    name: 'Load Status',
    description: 'Overall load status',
    icon: <Info />,
    link: '/hub/administration/data-load-status',
    requiredRoles: [],
  },
  {
    name: 'Audit Log',
    description: 'Display audit logs',
    icon: <ClipboardList />,
    link: '/hub/administration/audit-log',
    requiredRoles: [],
  },
  {
    name: 'Contacts',
    description: 'Display contact list',
    icon: <Contact />,
    link: '/hub/administration/contact-list',
    requiredRoles: [],
  },
  {
    name: 'Measure Expansion Config',
    description: 'Manage nested columns',
    icon: <MdSettingsIcon />,
    link: '/hub/administration/manage-expansions',
    requiredRoles: [],
  },
  {
    name: 'Manage Pages',
    description: 'Manage page visibility.',
    icon: <LuFiles />,
    link: '/hub/administration/manage-pages',
    requiredRoles: [],
  },
  {
    name: 'Manage Reports',
    description: 'Manage report visibility.',
    icon: <CiViewList />,
    link: '/hub/administration/manage-reports',
    requiredRoles: [CitcUserRoles.REPORT_ADMINISTRATOR],
  },
  {
    name: 'Manage Organization\nPreferences',
    description: 'Manage primary measure type for the organization.',
    icon: <BiSolidUserDetail />,
    link: '/hub/administration/manage-organization-preferences',
    requiredRoles: [],
  },
  {
    name: 'Clear Cache',
    description: 'Clear cache for the organization.',
    icon: <MdSettingsIcon />,
    link: '/hub/administration/clear-cache',
    requiredRoles: [],
  },
  {
    name: 'Application Insights\nLogs',
    description: 'Query Application Insights logs and telemetry data.',
    icon: <Search />,
    link: '/hub/administration/app-insights-logs',
    requiredRoles: [],
  },
]

const Page = () => {
  const { checkPermission, isLoaded } = useHasPermission()

  const filteredAdminItems = useMemo(() => {
    return testAdminItems.filter((item) => {
      if (item.requiredRoles.length === 0) return true
      return item.requiredRoles.some((role) => checkPermission(role))
    })
  }, [isLoaded, checkPermission])

  if (!isLoaded) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white">
        <Loader />
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center">
      <h1 className="font-open-sans text-[28px] font-bold mb-6">
        Administration
      </h1>

      <div className="grid grid-cols-3 gap-12 w-full max-w-[1200px] mx-auto">
        {filteredAdminItems.map((item, index) => (
          <AdminItemContainer key={index} item={item} />
        ))}
      </div>
    </div>
  )
}

export default Page
