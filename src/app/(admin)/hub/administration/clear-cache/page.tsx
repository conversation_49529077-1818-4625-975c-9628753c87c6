// 'use client'

// import { useState, useEffect } from 'react'
// import { useSearchParams } from 'next/navigation'
// import { useUserSessionStore } from '@/stores/userSession'
// import { toast } from '@/hooks/use-toast'
// import appInsights from '@/lib/applicationInsights'
// import Loader from '@/components/ui/Loader'

// export default function ClearCachePage() {
//   const searchParams = useSearchParams()
//   const paramOrgId = searchParams.get('organizationId')
//   const { organizationId: storeOrgId, organizationName } = useUserSessionStore()
//   const organization = {
//     id: paramOrgId || storeOrgId,
//     name: paramOrgId ? `Organization ID: ${paramOrgId}` : organizationName,
//   }
//   const [isLoading, setIsLoading] = useState(false)
//   const [result, setResult] = useState<{ keysCleared: number } | null>(null)

//   if (!paramOrgId) {
//     return (
//       <div className="container mx-auto py-10 font-open-sans">
//         <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
//           <h1 className="text-xl font-bold text-amber-800 mb-2">
//             Missing Organization ID
//           </h1>
//           <p className="text-amber-700 mb-4">
//             Please provide an organization ID in the URL parameters.
//           </p>
//           <p className="text-sm text-amber-600 bg-amber-100 p-2 rounded">
//             Example: /admin/clear-cache?organizationId=your-org-id
//           </p>
//         </div>
//       </div>
//     )
//   }

//   const clearCache = async () => {
//     try {
//       setIsLoading(true)
//       const response = await fetch(
//         `/api/cache/clear?organizationId=${organization.id}`
//       )

//       if (!response.ok) {
//         throw new Error('Failed to clear cache')
//       }

//       const data = await response.json()
//       setResult(data)

//       toast({
//         title: 'Success',
//         description: `Cache cleared successfully. ${data.keysCleared} keys removed.`,
//       })
//     } catch (error) {
//       appInsights.trackException({
//         exception: error instanceof Error ? error : new Error('Unknown error'),
//         properties: {
//           component: 'ClearCachePage',
//         },
//       })
//       toast({
//         title: 'Error',
//         description:
//           error instanceof Error ? error.message : 'Failed to clear cache',
//         variant: 'destructive',
//       })
//     } finally {
//       setIsLoading(false)
//     }
//   }

//   // Start clearing cache as soon as the component mounts
//   useEffect(() => {
//     clearCache()
//   }, [paramOrgId]) // Re-run if organizationId changes

//   return (
//     <div className="container mx-auto py-10 font-open-sans">
//       <div className="max-w-2xl mx-auto">
//         <h1 className="text-2xl font-bold mb-2">Clear Organization Cache</h1>
//         <p className="text-gray-500 mb-6">
//           Clearing all cached data for the organization.
//         </p>

//         <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
//           <h2 className="text-sm font-medium text-gray-700 mb-2">
//             Organization Details
//           </h2>
//           <div className="flex flex-col space-y-1">
//             <p className="text-sm text-gray-900">{organization.name}</p>
//             <p className="text-sm text-gray-500">{organization.id}</p>
//           </div>
//         </div>

//         {isLoading ? (
//           <div className="flex flex-col items-center py-10">
//             <Loader text="Clearing cache..." />
//           </div>
//         ) : (
//           result && (
//             <div className="bg-green-50 border border-green-200 rounded-lg p-6">
//               <div className="flex items-center mb-2">
//                 <svg
//                   className="w-5 h-5 text-green-500 mr-2"
//                   fill="currentColor"
//                   viewBox="0 0 20 20"
//                   xmlns="http://www.w3.org/2000/svg"
//                 >
//                   <path
//                     fillRule="evenodd"
//                     d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
//                     clipRule="evenodd"
//                   ></path>
//                 </svg>
//                 <h2 className="text-lg font-medium text-green-800">
//                   Cache cleared successfully
//                 </h2>
//               </div>
//               <p className="text-green-700">
//                 {result.keysCleared} {result.keysCleared === 1 ? 'key' : 'keys'}{' '}
//                 removed from cache
//               </p>
//             </div>
//           )
//         )}
//       </div>
//     </div>
//   )
// }
