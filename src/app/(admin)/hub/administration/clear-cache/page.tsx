'use client'

import { useState, useEffect } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { useUserSessionStore } from '@/stores/userSession'
import { toast } from '@/hooks/use-toast'
import appInsights from '@/lib/applicationInsights'
import Loader from '@/components/ui/Loader'

const ClearCachePage = () => {
  const pathname = usePathname()
  const router = useRouter()
  const [inputOrgId, setInputOrgId] = useState('')

  const searchParams = useSearchParams()
  const paramOrgId = searchParams.get('organizationId')
  const { organizationId: storeOrgId, organizationName } = useUserSessionStore()
  const organization = {
    id: paramOrgId || storeOrgId,
    name: paramOrgId ? `Organization ID: ${paramOrgId}` : organizationName,
  }
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{ keysCleared: number } | null>(null)

  const clearCache = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(
        `/api/cache/clear?organizationId=${organization.id}`
      )

      if (!response.ok) {
        throw new Error('Failed to clear cache')
      }

      const data = await response.json()
      setResult(data)

      toast({
        title: 'Success',
        description: `Cache cleared successfully. ${data.keysCleared} keys removed.`,
      })
    } catch (error) {
      appInsights.trackException({
        exception: error instanceof Error ? error : new Error('Unknown error'),
        properties: {
          component: 'ClearCachePage',
        },
      })
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to clear cache',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Start clearing cache as soon as the component mounts
  useEffect(() => {
    if (paramOrgId) {
      clearCache()
    }
  }, [paramOrgId]) // Re-run if organizationId changes

  return (
    <div className="w-full">
      <h1 className="text-center font-open-sans text-[28px] font-bold text-black leading-normal mb-6">
        Clear Cache
      </h1>
      <div className="  mx-auto pt-10 pb-20  border border-[#DDDCDF] rounded-lg shadow-sm   ">
        <div className="bg-white">
          {!paramOrgId ? (
            <div className="p-8 text-center">
              <h2 className="text-center font-open-sans text-[20px] font-bold text-black leading-normal mb-6">
                Missing Organization ID
              </h2>
              <p className="text-black font-open-sans text-[20px] font-normal leading-normal mb-8">
                Please provide an organization ID in the URL parameters.
              </p>
              <p className="text-black font-open-sans text-[20px] font-normal leading-normal mb-12">
                Example: {pathname}?organizationId=your-org-id
              </p>

              <div className="flex items-center space-x-2 mx-auto w-fit">
                <input
                  type="text"
                  placeholder="Enter organization ID"
                  value={inputOrgId}
                  onChange={(e) => setInputOrgId(e.target.value)}
                  className="border border-gray-300 rounded px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-400"
                />
                <button
                  onClick={() => {
                    if (inputOrgId.trim()) {
                      router.push(
                        `${pathname}?organizationId=${encodeURIComponent(inputOrgId.trim())}`
                      )
                    }
                  }}
                  className="bg-amber-600 hover:bg-amber-700 text-white text-sm font-semibold px-4 py-2 rounded"
                >
                  Submit
                </button>
              </div>

              {/* Blue vertical line on the left side */}
              <div className="absolute left-0 top-0 bottom-0 w-1 bg-[#13497C]"></div>
            </div>
          ) : (
            <div className="p-8">
              {isLoading ? (
                <div className="flex flex-col items-center py-10">
                  <Loader text="Clearing cache..." />
                </div>
              ) : (
                result && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                    <div className="flex items-center mb-2">
                      <svg
                        className="w-5 h-5 text-green-500 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                      <h2 className="text-center font-open-sans text-[20px] font-bold text-black leading-normal">
                        Cache cleared successfully
                      </h2>
                    </div>
                    <p className="text-green-700">
                      {result.keysCleared}{' '}
                      {result.keysCleared === 1 ? 'key' : 'keys'} removed from
                      cache
                    </p>
                  </div>
                )
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ClearCachePage
