'use client'

import { ClipboardList, Contact, Info, SquareChartGantt } from 'lucide-react'
import { usePathname, useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useState, useEffect, useMemo } from 'react'
import { MdSettings as MdSettingsIcon } from 'react-icons/md'
import { BiSolidUserDetail } from 'react-icons/bi'
import { CitcUserRoles } from '@/shared/roles'
import { useHasPermission } from '@/hooks/useHasPermission'

// Define the admin navigation itemsimport { MdSettings as MdSettingsIcon } from 'react-icons/md'
import { LuFiles } from 'react-icons/lu'
import { CiViewList } from 'react-icons/ci'
import { Search } from 'lucide-react'
import React from 'react'
import { AdminSection } from '@/enums/adminSection'
const adminNavItems = [
  {
    name: 'LOAD STATUS',
    icon: <Info className="w-5 h-5" />,
    id: AdminSection.LOAD_STATUS,
    component: () =>
      import('./data-load-status/page').then((mod) => <mod.default />),
  },
  {
    name: 'AUDIT LOG',
    icon: <ClipboardList className="w-5 h-5" />,
    id: AdminSection.AUDIT_LOG,
    component: () => import('./audit-log/page').then((mod) => <mod.default />),
  },
  {
    name: 'CONTACTS',
    icon: <Contact className="w-5 h-5" />,
    id: AdminSection.CONTACTS,
    component: () =>
      import('./contact-list/page').then((mod) => <mod.default />),
  },
  {
    name: 'MEASURE EXPANSION CONFIG',
    icon: <MdSettingsIcon />,
    id: AdminSection.MEASURE_EXPANSION,
    component: () =>
      import('./manage-expansions/page').then((mod) => <mod.default />),
  },
  {
    name: 'MANAGE PAGES',
    icon: <LuFiles />,
    id: AdminSection.MANAGE_PAGES,
    component: () =>
      import('./manage-pages/page').then((mod) => <mod.default />),
  },
  {
    name: 'MANAGE REPORTS',
    icon: <CiViewList />,
    id: AdminSection.MANAGE_REPORTS,
    component: () =>
      import('./manage-reports/page').then((mod) => <mod.default />),
    requiredPermissions: [CitcUserRoles.REPORT_ADMINISTRATOR],
  },
  {
    name: 'MANAGE ORGANIZATION PREFERENCES',
    icon: <BiSolidUserDetail />,
    id: AdminSection.MANAGE_ORG_PREFS,
    component: () =>
      import('./manage-organization-preferences/page').then((mod) => (
        <mod.default />
      )),
  },
  {
    name: 'CLEAR CACHE',
    icon: <MdSettingsIcon />,
    id: AdminSection.CLEAR_CACHE,
    component: () =>
      import('./clear-cache/page').then((mod) => <mod.default />),
  },
  {
    name: 'APPLICATION INSIGHTS LOGS',
    icon: <Search className="w-5 h-5" />,
    id: AdminSection.APP_INSIGHTS_LOGS,
    component: () =>
      import('./app-insights-logs/page').then((mod) => <mod.default />),
  },
]

export default function AdministrationLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const router = useRouter()
  const { checkPermission } = useHasPermission()

  // Initialize with a default value
  const [activeSection, setActiveSection] = useState<string>('')

  // Only show sidebar if we're not on the main administration page
  const showSidebar = pathname !== '/hub/administration'

  // Filter adminNavItems based on permissions
  const filteredAdminNavItems = useMemo(() => {
    return adminNavItems.filter((item) => {
      if (!item.requiredPermissions || item.requiredPermissions.length === 0) {
        return true
      }
      return item.requiredPermissions.some((permission) =>
        checkPermission(permission)
      )
    })
  }, [checkPermission])

  // Update active section whenever pathname changes
  useEffect(() => {
    // Extract the current section from the pathname
    const pathSegments = pathname.split('/')
    const lastSegment = pathSegments[pathSegments.length - 1]

    // Find matching nav item
    const matchedItem = adminNavItems.find((item) => item.id === lastSegment)

    if (matchedItem) {
      setActiveSection(matchedItem.id)
    } else {
      // Default to first item if no match found and we're on a sub-page
      if (showSidebar && pathSegments.length > 3) {
        setActiveSection(adminNavItems[0]?.id || AdminSection.LOAD_STATUS)
      }
    }
  }, [pathname, showSidebar])

  // Handle navigation item click
  const handleSectionChange = (sectionId: string) => {
    setActiveSection(sectionId)

    // Navigate to the selected section
    router.push(`/hub/administration/${sectionId}`)
  }

  return (
    <div className="flex h-full w-full">
      {showSidebar && (
        <div className="w-64 relative top-0 pl-0 mt-20 border-r">
          <ul className="pt-4">
            {filteredAdminNavItems.map((item) => (
              <li
                key={item.id}
                onClick={() => handleSectionChange(item.id)}
                className={`w-full px-3 py-3 text-left flex items-center space-x-2 transition-colors font-semibold text-[14px] hover:cursor-pointer
                  ${
                    activeSection === item.id
                      ? 'bg-ui-pale-blue text-[#2970A7] border-r-[#2970A7] border-r-[4px]'
                      : 'text-ui-dark-gray hover:bg-[#F5F7FF]'
                  }`}
              >
                <span className="text-right flex justify-end w-full font-open-sans text-[13px] font-semibold">
                  <div className="mr-2">
                    {/* Clone the icon element with the desired size */}
                    {React.cloneElement(item.icon, {
                      className: 'w-[19px] h-[19px] weight-400',
                    })}
                  </div>
                  <div className="whitespace-nowrap overflow-hidden text-ellipsis">
                    {item.name}
                  </div>
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Main Content - Moved even further upward */}
      <div
        className={cn(
          'flex-1 flex flex-col overflow-hidden pl-0', // Added pl-0
          !showSidebar && 'w-full' // Add full width when sidebar is hidden
        )}
      >
        <div className="flex-1 overflow-y-auto mac-scrollbar">
          <div className="p-0">
            {' '}
            {/* Changed p-2 pt-0 pb-12 to p-0 */}
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}
