import { ExportDataset } from '@/components/ui/Exporter'
import { useState, createContext, useRef } from 'react'
// Create a context to share date range state and export data


export const DateRangeContext = createContext<{
    startDate: Date | null;
    endDate: Date | null;
    handleDateRangeChange: (start: Date | null, end: Date | null) => void;
    exportDataRef: React.MutableRefObject<ExportDataset>;
}>({
    startDate: null,
    endDate: null,
    handleDateRangeChange: () => { },
    exportDataRef: { current: { headers: [], dataset: [] } } as any,
});
