'use client'

import Footer from '@/components/footer/Footer'
import PlatformNavigation from '@/components/navigation/PlatformNavigation'
import { NoticeContainer } from '@/components/noticeContainer'
import { HelpBar } from '@/components/helpBar'
import { usePathname } from 'next/navigation'
import DateRangeFilter from '@/components/ui/DateRangeFilter'
import Exporter from '@/components/ui/Exporter'
import { Download } from 'lucide-react'
import { useState, useRef, useEffect } from 'react'
import { ExportDataset } from '@/components/ui/Exporter'
import { DateRangeContext } from './dateRangeContext'
import { SingleDateContext } from './singleDateContext'
import { SingleDatePicker } from '@/components/ui/SingleDatePicker'
import ExcelExporter from '@/components/ui/ExcelExporter'
import { DataLoadStatusContext } from './dataLoadStatusContext'

type Props = { children: React.ReactNode }

const Layout = ({ children }: Props) => {
  const pathname = usePathname()
  const isAuditLogPage = pathname?.includes('/audit-log')
  const isDataLoadStatusPage = pathname?.includes('data-load-status')
  // State for DateRangeFilter (only used on audit-log page)
  // Initialize with today's date if on audit log page
  const [startDate, setStartDate] = useState<Date | null>(isAuditLogPage ? new Date() : null)
  const [endDate, setEndDate] = useState<Date | null>(isAuditLogPage ? new Date() : null)
  // const { setSingleStartDate } = useContext(SingleDateContext);
  const [singleStartDate, setSingleStartDate] = useState<Date | undefined>(isDataLoadStatusPage ? new Date() : undefined);

  const handleDateRangeChange = (start: Date | null, end: Date | null) => {
    setStartDate(start)
    setEndDate(end)
  }

  const handleSingleDateChange = (date: Date | null) => {
    //setSingleStartDateLocal(date);
    setSingleStartDate(date);
  };

  // Update the export dataset to be a function that gets data from a ref
  const exportDataRef = useRef<ExportDataset>({
    headers: ['userName', 'userEmail', 'organization', 'url', 'auditDateTime'],
    dataset: [],
  })

  // State to store data load status data for export
  const [dataLoadStatusData, setDataLoadStatusData] = useState<any[]>([]);
  const [dataLoadStatusColumns, setDataLoadStatusColumns] = useState<any[]>([]);

  // Function to update data load status
  const setDataLoadStatus = (data: any[], columns: any[]) => {
    if (data?.length > 0 && columns?.length > 0) {
      setDataLoadStatusData(data);
      setDataLoadStatusColumns(columns);
    }
  };

  // Log when data changes
  useEffect(() => {
    console.log("Data load status data updated:", {
      dataLength: dataLoadStatusData?.length || 0,
      columnsLength: dataLoadStatusColumns?.length || 0
    });
  }, [dataLoadStatusData, dataLoadStatusColumns]);

  return (
    <DateRangeContext.Provider value={{
      startDate,
      endDate,
      handleDateRangeChange,
      exportDataRef
    }}>
      <SingleDateContext.Provider value={{ singleStartDate, handleSingleDateChange }}>
        <DataLoadStatusContext.Provider value={{ setDataLoadStatus }}>
          <div className="flex flex-col min-h-screen">
            <PlatformNavigation />

            <main className="flex-grow">
              <NoticeContainer />

              {/* Conditionally render either HelpBar or DateRange+Export based on URL */}
              <div className="flex justify-between items-center  mt-4">

                {isAuditLogPage ? (
                  <>
                    <div className="xl:px-16">
                      <DateRangeFilter
                        startDate={startDate}
                        endDate={endDate}
                        onDateRangeChange={handleDateRangeChange}
                      />
                    </div>
                    <div>
                      <Exporter
                        exportDataset={exportDataRef.current}
                        buttonText="EXPORT TO EXCEL"
                      />
                    </div>
                  </>
                ) : isDataLoadStatusPage ? (
                  <>
                    <div className="pl-8">
                      <SingleDatePicker
                        onSelect={handleSingleDateChange}
                        selectedDay={singleStartDate}
                      />
                    </div>
                    <div style={{ paddingRight: '85px' }}>
                      <ExcelExporter
                        data={dataLoadStatusData}
                        columns={dataLoadStatusColumns}
                        buttonText="EXPORT TO EXCEL"
                        filePrefix="data_load_status"
                      />
                    </div>
                  </>
                ) : (
                  <div className="flex justify-end w-full">
                    <HelpBar />
                  </div>
                )}
              </div>

              <section className="mx-auto py-4 pl-[70px] pr-[70px]">
                {children}
              </section>
            </main>
            <Footer />
          </div>
        </DataLoadStatusContext.Provider>
      </SingleDateContext.Provider>
    </DateRangeContext.Provider>
  )
}

export default Layout
