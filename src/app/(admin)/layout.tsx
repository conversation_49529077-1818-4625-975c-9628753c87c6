'use client'

import Footer from '@/components/footer/Footer'
import PlatformNavigation from '@/components/navigation/PlatformNavigation'
import { NoticeContainer } from '@/components/noticeContainer'
import { HelpBar } from '@/components/helpBar'
import { usePathname } from 'next/navigation'
import DateRangeFilter from '@/components/ui/DateRangeFilter'
import Exporter from '@/components/ui/Exporter'
import { Download } from 'lucide-react'
import { useState, createContext, useRef } from 'react'
import { ExportDataset } from '@/components/ui/Exporter'
import { DateRangeContext } from './dateRangeContext'

type Props = { children: React.ReactNode }

const Layout = ({ children }: Props) => {
  const pathname = usePathname()
  const isAuditLogPage = pathname?.includes('/audit-log')

  // State for DateRangeFilter (only used on audit-log page)
  // Initialize with today's date if on audit log page
  const [startDate, setStartDate] = useState<Date | null>(isAuditLogPage ? new Date() : null)
  const [endDate, setEndDate] = useState<Date | null>(isAuditLogPage ? new Date() : null)

  const handleDateRangeChange = (start: Date | null, end: Date | null) => {
    setStartDate(start)
    setEndDate(end)
  }

  // Update the export dataset to be a function that gets data from a ref
  const exportDataRef = useRef<ExportDataset>({
    headers: ['userName', 'userEmail', 'organization', 'url', 'auditDateTime'],
    dataset: []
  })

  return (
    <DateRangeContext.Provider value={{
      startDate,
      endDate,
      handleDateRangeChange,
      exportDataRef
    }}>
      <div className="flex flex-col min-h-screen">
        <PlatformNavigation />

        <main className="flex-grow">
          <NoticeContainer />

          {/* Conditionally render either HelpBar or DateRange+Export based on URL */}
          <div className="flex justify-between items-center pr-8 mt-4">
            {isAuditLogPage ? (
              <>
                <div className="pl-8">
                  <DateRangeFilter
                    startDate={startDate}
                    endDate={endDate}
                    onDateRangeChange={handleDateRangeChange}
                    className="text-blue-700 font-semibold"
                  />
                </div>
                <div>
                  <Exporter
                    exportDataset={exportDataRef.current}
                    buttonText="EXPORT TO EXCEL"
                    className="flex items-center text-blue-700 font-semibold"
                    icon={<Download className="h-4 w-4 mr-2" />}
                  />
                </div>
              </>
            ) : (
              <div className="flex justify-end w-full">
                <HelpBar />
              </div>
            )}
          </div>

          <section className="mx-auto py-4 pl-[70px] pr-[70px]">
            {children}
          </section>
        </main>

        <Footer />
      </div>
    </DateRangeContext.Provider>
  )
}

export default Layout
