import EncountersSearch from '@/components/encountersSearch'
import PatientsSearch from '@/components/patientsSearch'
import ResultsTable from '@/components/search/resultsTable'
import { MoveLeft } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

type Props = {
  params: Promise<{
    searchType: 'patients' | 'encounters'
  }>
}

const Page = async ({ params }: Props) => {
  const { searchType } = await params

  if (!['patients', 'encounters'].includes(searchType)) {
    return notFound()
  }

  return (
    <div className="w-full flex flex-col items-center mx-auto">
      <div className="flex justify-between w-full mb-4 py-4 bg-white rounded-lg items-center">
        <Link
          href="/explorer"
          className="flex space-x-2 items-center text-ui-dark-gray justify-between"
        >
          <MoveLeft size={24} />
          <span className="font-open-sans text-[13px] font-semibold leading-[17.7px] text-center">
            {`${(searchType === 'patients' ? 'encounters' : 'patients').slice(0, -1)} Search`.toUpperCase()}
          </span>
        </Link>

        <div className="w-[33%]">
          {searchType === 'patients' ? (
            <PatientsSearch />
          ) : (
            <EncountersSearch />
          )}
        </div>

        {/* Just taking up space */}
        <div className="flex justify-end" />

        {/* <Dialog>
          <DialogTrigger asChild>
            <button className="space-x-2 rounded-[5px] flex justify-between items-center px-4 py-2 font-semibold bg-ui-pale-blue text-ui-dark-gray">
              <CircleAlert size={20} />
              <span>Help</span>
            </button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                <h1 className="font-open-sans text-[14px] font-bold leading-[19.07px] align-left">
                  EXPLORER HELP
                </h1>
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col space-y-2 font-open-sans text-[12px] leading-[16.34px]">
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec
                dictum molestie porttitor. Fusce in sem non velit faucibus
                maximus ut ut enim. Curabitur blandit felis quis odio porta, ac
                interdum elit placerat. Suspendisse justo tortor, tempor at
                finibus quis, elementum ut odio.
              </p>
              <span>
                <span className="font-bold">Donec at eleifend odio, </span>ac
                congue metus.
              </span>
              <ul className="list-disc pl-6">
                <li>Donec at eleifend odio, ac congue metus.</li>
                <li>
                  Nunc posuere est id nulla luctus, vitae rutrum est maximus.
                </li>
                <li>In vitae nulla neque. Sed bibendum nisi risus.</li>
                <li>Cras non felis nec sem suscipit scelerisque.</li>
                <li>Donec pharetra diam sit amet viverra rhoncus.</li>
              </ul>
              <p>
                Maecenas ac efficitur augue, quis lobortis massa. Maecenas
                posuere diam mi, in cursus enim malesuada ut. Nullam nulla
                nulla, convallis eu nunc eget, mattis vestibulum dolor. Lorem
                ipsum dolor sit amet, consectetur adipiscing elit. Curabitur
                eget dolor tempus, consequat nibh vel, porttitor enim.
              </p>
              <p>
                Quisque eget lectus ligula. Donec vehicula scelerisque diam, in
                rhoncus justo rutrum nec. Integer tempor imperdiet nibh, nec
                viverra quam suscipit vitae. Vestibulum aliquet nisl massa.
                Donec blandit mi ut ligula ultricies, vel sodales elit
                consectetur. Duis a sapien purus.
              </p>
            </div>
          </DialogContent>
        </Dialog> */}
      </div>

      <ResultsTable searchType={searchType} />
    </div>
  )
}

export default Page
