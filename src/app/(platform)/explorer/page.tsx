import EncountersSearch from '@/components/encountersSearch'
import PatientsSearch from '@/components/patientsSearch'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import Image from 'next/image'

const Page = async () => {
  return (
    <div className="w-full flex flex-col items-center mx-auto">
      {/* <div className="flex justify-end w-full mb-4 p-4 bg-white rounded-lg">
        <Dialog>
          <DialogTrigger asChild>
            <button className="space-x-2 rounded-[5px] flex justify-between items-center px-4 py-2 font-semibold bg-ui-pale-blue text-ui-dark-gray">
              <CircleAlert size={20} />
              <span>Help</span>
            </button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                <h1 className="font-open-sans text-[14px] font-bold leading-[19.07px] align-left">
                  EXPLORER HELP
                </h1>
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col space-y-2 font-open-sans text-[12px] leading-[16.34px]">
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec
                dictum molestie porttitor. Fusce in sem non velit faucibus
                maximus ut ut enim. Curabitur blandit felis quis odio porta, ac
                interdum elit placerat. Suspendisse justo tortor, tempor at
                finibus quis, elementum ut odio.
              </p>
              <span>
                <span className="font-bold">Donec at eleifend odio, </span>ac
                congue metus.
              </span>
              <ul className="list-disc pl-6">
                <li>Donec at eleifend odio, ac congue metus.</li>
                <li>
                  Nunc posuere est id nulla luctus, vitae rutrum est maximus.
                </li>
                <li>In vitae nulla neque. Sed bibendum nisi risus.</li>
                <li>Cras non felis nec sem suscipit scelerisque.</li>
                <li>Donec pharetra diam sit amet viverra rhoncus.</li>
              </ul>
              <p>
                Maecenas ac efficitur augue, quis lobortis massa. Maecenas
                posuere diam mi, in cursus enim malesuada ut. Nullam nulla
                nulla, convallis eu nunc eget, mattis vestibulum dolor. Lorem
                ipsum dolor sit amet, consectetur adipiscing elit. Curabitur
                eget dolor tempus, consequat nibh vel, porttitor enim.
              </p>
              <p>
                Quisque eget lectus ligula. Donec vehicula scelerisque diam, in
                rhoncus justo rutrum nec. Integer tempor imperdiet nibh, nec
                viverra quam suscipit vitae. Vestibulum aliquet nisl massa.
                Donec blandit mi ut ligula ultricies, vel sodales elit
                consectetur. Duis a sapien purus.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      </div> */}

      <h1 className="text-center text-[28px] font-bold mb-6 font-open-sans">
        Patient /Encounter Search
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="w-full">
          <CardHeader className="text-center font-bold font-open-sans text-ui-dark-gray flex flex-col items-center">
            <Image
              src="/images/patientSearch.svg"
              alt="Patient Search Icon"
              width={40}
              height={40}
            />
            Patient Search
          </CardHeader>
          <CardContent>
            <PatientsSearch useFullWidth />
            <div className="ml-[15px]">
              <ul className="font-open-sans font-normal text-ui-dark-gray text-[12px] mt-4">
                <li>• Patient Identifier*</li>
                <li>• First Name*</li>
                <li>• Last Name*</li>
                <li>• Date of Birth (mm/dd/yyyy)</li>
              </ul>
              <p className="font-open-sans font-normal text-ui-dark-gray text-[12px] mt-2">
                It is recommended that you limit your initial search to the
                items above labeled with an asterisk.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full">
          <CardHeader className="text-center font-bold font-open-sans text-ui-dark-gray flex flex-col items-center">
            <Image
              src="/images/encounterSearch.svg"
              alt="Encounter Search Icon"
              width={40}
              height={40}
            />
            Encounter Search
          </CardHeader>
          <CardContent>
            <EncountersSearch useFullWidth />
            <div className="ml-[15px]">
              <ul className="font-open-sans font-normal text-ui-dark-gray text-[12px] mt-4">
                <li>• Case Identifier*</li>
                <li>• First Name*</li>
                <li>• Last Name*</li>
                <li>• Encounter Type (e.g., inpatient, outpatient, etc.)</li>
                <li>• Start Date (mm/dd/yyyy)</li>
                <li>• Discharge Date (mm/dd/yyyy)</li>
              </ul>
              <p className="font-open-sans font-normal text-ui-dark-gray text-[12px] mt-2">
                It is recommended that you limit your initial search to the
                items above labeled with an asterisk.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default Page
