import { auth } from '@/auth'
import { ScorecardGrids } from '@/components/scorecards/ScorecardGrids'
import FilterAndActionBar from '@/components/ui/filterAndActionBar'
import { redirect } from 'next/navigation'

const Scorecard = async () => {
  const session = await auth()
  if (!session) return redirect('/session/expired')

  return (
    <div className="container min-w-full my-4 space-y-4">
      <FilterAndActionBar showMeasureTypeToggle={true} />

      <ScorecardGrids />

    </div>
  )
}

export default Scorecard
