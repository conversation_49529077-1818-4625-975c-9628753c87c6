import MeasureDetailsActionBar from '@/components/ui/MeasureDetailsActionBar'
import MeasureDetailsContent from '@/components/ui/MeasureDetailsContent'
import { type Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Measures Details - Medisolv Platform',
}
const MeasureDetails = async () => {
  return (
    <div className="container min-w-full">
      <MeasureDetailsActionBar />
      <div className="space-y-8">
        <MeasureDetailsContent />
      </div>
    </div>
  )
}

export default MeasureDetails
