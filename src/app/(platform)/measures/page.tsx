import { MeasuresGrid } from '@/components/measures/MeasuresGrid'
import FilterAndActionBar from '@/components/ui/filterAndActionBar'
import { type Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Measures - Medisolv Platform',
}

const Measures = async () => {
  return (
    <div className="container min-w-full my-4 space-y-4">
      <FilterAndActionBar />

      <MeasuresGrid />
    </div>
  )
}

export default Measures
