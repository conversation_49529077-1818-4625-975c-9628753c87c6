'use client'

import { Button } from '@/components/ui/button'

type Props = { error: Error & { digest?: string }; reset: () => void }

const Error = ({ error, reset }: Props) => {
  // NOTE: Cause does not seem to be psased into this component. Only the generic error. I assume a new Error instance is being created without all other details. Same behavior happens when attempting to use a custom Error. The orig Error class is provided and not the custom
  type ErrWithCause = Error & {
    cause?: {
      code?: number
      message?: string
      response?: { status: number }
    }
  }

  const { message, cause } = error as ErrWithCause

  // Log the error to an error reporting service

  return (
    <div className="redirectstatus w-full flex flex-col items-center">
      <h1 className="text-[9rem] font-bold m-0 text-shadow-[4px_4px_0px_#014A76]">
        {cause?.code ?? 'Oops!'}
      </h1>

      <div className="redirectstatus-bg absolute inset-0 overflow-hidden z-[-1]">
        <div className="absolute top-0 bottom-0 w-px bg-gray-200 left-[20%] after:content-[''] after:absolute after:top-0 after:left-[-0.5px] after:h-[160px] after:w-[2px] after:bg-[#014A76] after:transform after:translate-y-[-160px] after:animate-drop-1"></div>
        <div className="absolute top-0 bottom-0 w-px bg-gray-200 left-[40%] after:content-[''] after:absolute after:top-0 after:left-[-0.5px] after:h-[160px] after:w-[2px] after:bg-[#014A76] after:transform after:translate-y-[-160px] after:animate-drop-2"></div>
        <div className="absolute top-0 bottom-0 w-px bg-gray-200 left-[60%] after:content-[''] after:absolute after:top-0 after:left-[-0.5px] after:h-[160px] after:w-[2px] after:bg-[#014A76] after:transform after:translate-y-[-160px] after:animate-drop-3"></div>
        <div className="absolute top-0 bottom-0 w-px bg-gray-200 left-[80%] after:content-[''] after:absolute after:top-0 after:left-[-0.5px] after:h-[160px] after:w-[2px] after:bg-[#014A76] after:transform after:translate-y-[-160px] after:animate-drop-4"></div>
      </div>

      <h2 className="text-[42px] font-bold m-0 tracking-[1.6px]">
        Something went wrong
      </h2>

      <p className="mt-[20px] mb-[25px] font-lato text-black">
        {message ??
          'The page you are looking for might have been removed had its name changed or is temporarily unavailable.'}
      </p>

      <Button
        onClick={reset}
        className=" rounded-none bg-transparent inline-block px-[30px] py-[10px] text-black font-normal shadow-[0px_0px_0px_2px_black,2px_2px_0px_2px_#014A76] transition-all duration-200 hover:bg-[#014A76] hover:shadow-[0px_0px_0px_0px_black,0px_0px_0px_2px_#014A76]"
      >
        Retry
      </Button>
    </div>
  )
}

export default Error
