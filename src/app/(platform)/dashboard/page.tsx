import DashboardReport from '@/components/dashboard/DashboardReport'
import { type Metadata } from 'next'
import ConfigureDashboards from "@/components/dashboard/ConfigureDashboards";
import DashboardActionBar from "@/components/dashboard/DashboardActionBar";

export const metadata: Metadata = {
  title: 'Dashboards - Medisolv Platform',
}

const Dashboard = async () => {

    return (
        <div className="dashboard w-full ">
            <DashboardActionBar />
            <div className="absolute left-0 w-full bg-[#F9F8F9]">
                <div className="container mx-auto">
                    <DashboardReport />
                    <ConfigureDashboards />
                </div>
            </div>
        </div>
  )
}

export default Dashboard
