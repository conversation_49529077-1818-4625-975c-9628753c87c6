'use client'

import React, { useState, useRef } from 'react'
import { ReportsTable } from '@/components/reports/ReportsTable'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'
import { useClickOutside } from '@/hooks/useClickOutside'
import Image from 'next/image'
import chevronDown from '../../../../public/images/chevronDown.svg'
import checkedRadio from '../../../../public/images/checked-radio.svg'
import emptyRadio from '../../../../public/images/empty-radio.svg'
import { cn } from '@/lib/utils'

export type SortOption = {
  label: string
  value: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

const Reports = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [sortByOpen, setSortByOpen] = useState(false)
  const [selectedSort, setSelectedSort] = useState<SortOption>({
    label: 'Report Name (A-Z)',
    value: 'nameAsc',
    sortBy: 'name',
    sortOrder: 'asc',
  })

  const sortByRef = useRef<HTMLDivElement>(null)

  // Memoize the callback to prevent unnecessary re-renders
  const handleClickOutside = React.useCallback(() => {
    setSortByOpen(false)
  }, [])

  useClickOutside(sortByRef, handleClickOutside)

  const sortOptions: SortOption[] = [
    { label: 'Report Type', value: 'type', sortBy: 'type', sortOrder: 'asc' },
    {
      label: 'Favorites First',
      value: 'favorites',
      sortBy: 'isFavorite',
      sortOrder: 'desc',
    },
    {
      label: 'Report Name (A-Z)',
      value: 'nameAsc',
      sortBy: 'name',
      sortOrder: 'asc',
    },
    {
      label: 'Report Name (Z-A)',
      value: 'nameDesc',
      sortBy: 'name',
      sortOrder: 'desc',
    },
    {
      label: 'Last Modified (New-Old)',
      value: 'dateDesc',
      sortBy: 'lastModified',
      sortOrder: 'desc',
    },
    {
      label: 'Last Modified (Old-New)',
      value: 'dateAsc',
      sortBy: 'lastModified',
      sortOrder: 'asc',
    },
  ]

  const handleSortChange = (option: SortOption) => {
    setSelectedSort(option)
    setSortByOpen(false)
  }

  return (
    <div className="container mx-auto p-2">
      <div className="mb-6 flex justify-between items-center">
        <div className="relative w-full max-w-[300px] rounded-none">
          <Input
            type="text"
            placeholder="Search Reports"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="border-0 border-b border-[#3F4E66] bg-transparent rounded-none 
            focus-visible:ring-0 focus-visible:ring-offset-0 pl-3 pr-10 
            placeholder:text-[12px] placeholder:font-normal placeholder:font-['Open_Sans'] placeholder:text-[#3F4E66] 
            flex items-center font-normal font-['Open_Sans'] text-[12px] text-[#3F4E66]"
          />

          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <Search className="h-3 w-3 text-gray-500" />
          </div>
        </div>

        <div className="relative flex justify-end" ref={sortByRef}>
          <button
            onClick={() => setSortByOpen(!sortByOpen)}
            className={`flex items-center space-x-2 px-[15px] py-2 font-semibold bg-ui-pale-blue text-[#13497C] focus:outline-none focus:ring-0 border border-ui-pale-blue ${
              !sortByOpen &&
              'hover:bg-white hover:border-ui-dark-gray rounded-[5px]'
            } ${sortByOpen ? 'rounded-t-[5px]' : 'rounded-[5px]'}`}
            aria-expanded={sortByOpen}
            aria-haspopup="true"
          >
            <span className="font-open-sans font-semibold text-[13px] text-[#566582]">
              SORT BY
            </span>
            <Image
              src={chevronDown}
              alt="chevronDown"
              height={14}
              width={14}
              className={cn(sortByOpen && 'rotate-180')}
            />
          </button>

          {sortByOpen && (
            <div
              className="absolute right-0 top-full bg-[#F5F7FE] p-3 rounded-tl-[5px] rounded-bl-[5px] rounded-br-[5px] shadow-lg z-50"
              style={{ width: '250px' }}
            >
              <div className="bg-white border border-gray-200 rounded-sm">
                <div className="p-[10px]">
                  <h3 className="font-semibold text-[#13497C] text-xs font-open-sans">
                    Sort By
                  </h3>
                </div>
                <div className="py-1 px-2">
                  {sortOptions.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center py-1 cursor-pointer"
                    >
                      <div
                        className="flex items-center w-full cursor-pointer"
                        onClick={() => handleSortChange(option)}
                      >
                        <Image
                          src={
                            selectedSort.value === option.value
                              ? checkedRadio
                              : emptyRadio
                          }
                          alt={
                            selectedSort.value === option.value
                              ? 'Selected'
                              : 'Not selected'
                          }
                          width={14}
                          height={14}
                          className="mr-2"
                        />
                        <span className="text-gray-800 text-xs font-normal font-open-sans">
                          {option.label}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <ReportsTable searchQuery={searchQuery} sortOption={selectedSort} />
    </div>
  )
}

export default Reports
