'use client'

import React from 'react'
import ACOPerformanceAnalysisPageContent from '@/components/reports/aco/ACOPerformanceAnalysisPageContent'
import dayjs from 'dayjs'
import { useSearchParams } from 'next/navigation'

function ACOPerformanceAnalysisPage() {
  // Get the reportId from the URL query parameters
  const searchParams = useSearchParams()
  const reportId = searchParams.get('reportId')

  // These are just initial default values for the first render
  // The client component will handle updates when the user selects different measures

  // Use January 1st of the previous year
  const previousYear = dayjs().subtract(1, 'year').startOf('year')
  const initialDateRange = previousYear.format('YYYY-MM-DD')
  const initialMeasures: string[] = [] // Empty array to align with PageClient behavior

  return (
    <ACOPerformanceAnalysisPageContent
      initialMeasures={initialMeasures}
      initialDateRange={initialDateRange}
      reportId={reportId}
    />
  )
}

export default ACOPerformanceAnalysisPage
