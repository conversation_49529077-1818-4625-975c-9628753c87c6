import Footer from '@/components/footer/Footer'
import PlatformNavigation from '@/components/navigation/PlatformNavigation'
import { NoticeContainer } from '@/components/noticeContainer'

type Props = {
  children: React.ReactNode
}

const PlatformLayout = async ({ children }: Props) => {
  // void api.pages.getAll.prefetch()
  // void api.loadStatusInfo.get.prefetch({})
  // NOTE: These guys do not read the updated cookie values causing issues downstream
  // void api.organizations.getByUser.prefetch()
  // void api.partners.getByUser.prefetch()
  // void api.applications.getAll.prefetch()
  // void api.notifications.getAll.prefetch({})
  // void api.organizations.getSelected.prefetch()

  return (
    <div className="flex flex-col min-h-screen  ">
      <PlatformNavigation />

      <main className="flex flex-col flex-grow">
        <NoticeContainer />

        <section className="flex-grow flex py-4 pl-[70px] pr-[70px]">
          {children}
        </section>
      </main>

      <Footer />
    </div>
  )
}

export default PlatformLayout
