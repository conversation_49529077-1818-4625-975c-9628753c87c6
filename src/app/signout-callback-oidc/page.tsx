'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function SignoutCallbackPage() {
  const router = useRouter()

  useEffect(() => {
    // Clear any remaining client-side state
    if (typeof window !== 'undefined') {
      // Clear localStorage
      localStorage.clear()
      
      // Clear sessionStorage
      sessionStorage.clear()
      
      // Clear any other client-side auth state
      // You might need to clear other state management stores here
    }

    // Redirect to home page after a short delay
    const timer = setTimeout(() => {
      router.push('/')
    }, 1000)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Signing out...</h1>
        <p className="text-gray-600">You have been successfully signed out.</p>
        <p className="text-sm text-gray-500 mt-2">Redirecting to home page...</p>
      </div>
    </div>
  )
}
