import RefreshSessionButton from '@/components/RefreshSessionButton'
import { cn } from '@/lib/utils'

// Export the component as default
export default function SessionExpiredPage() {
  return (
    <div className="flex flex-col space-y-8 text-center">
      <span className={cn(
        'font-bold font-open-sans text-[28px]',
        'leading-[38.13px]'
      )}>
        Your session has expired. We apologize for the inconvenience.
      </span>
      <RefreshSessionButton
        className={cn(
          'bg-ui-dark-gray',
          'text-lg text-center',
          'font-semibold font-open-sans text-[13px]',
          'leading-[17.7px]'
        )}
      />
    </div>
  )
}
