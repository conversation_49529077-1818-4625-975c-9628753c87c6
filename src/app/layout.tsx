import './globals.css'

import { GeistSans } from 'geist/font/sans'

import { Merriweather, Source_Sans_3 } from 'next/font/google'
import Logging from '@/components/auditLog/Logging'
import React from 'react'
import { SessionProvider } from 'next-auth/react'
import { auth } from '@/auth'
import { TRPCReactProvider } from '@/trpc/react'
import { cookies } from 'next/headers'
import { CustomToaster } from '@/components/ui/CustomToaster'

const MerriweatherFont = Merriweather({
  subsets: ['latin'],
  weight: ['400', '700'],
  style: ['normal'],
  variable: '--font-merriweather',
})

const SourceSansFont = Source_Sans_3({
  subsets: ['latin'],
  weight: ['300'],
  style: ['normal'],
})

const RootLayout = async ({
  children,
}: Readonly<{ children: React.ReactNode }>) => {
  const session = await auth()

  return (
    <html
      lang="en"
      className={`${GeistSans.variable} ${MerriweatherFont.variable} ${SourceSansFont.className}`}
    >
      <body>
        <Logging session={session} cookies={(await cookies()).getAll()}>
          {children}
        </Logging>
      </body>
    </html>
  )
}

export default RootLayout
