'use client'

import { But<PERSON> } from '@/components/ui/button'
import type { ErrorWithCause } from '@/types/errorWithCause'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { ErrorPageHeader } from '@/components/errorPageHeader'
import { cn } from '@/lib/utils'

type Props = { error: Error & { digest?: string }; reset: () => void }

const Error = ({ error, reset }: Props) => {
  const router = useRouter()
  // NOTE: Cause does not seem to be psased into this component. Only the generic error. I assume a new Error instance is being created without all other details. Same behavior happens when attempting to use a custom Error. The orig Error class is provided and not the custom

  const { message, cause } = error as ErrorWithCause

  // Log the error to an error reporting service

  useEffect(() => {
    if (message === 'Unauthorized') {
      return router.push('/session/expired')
    }
  }, [message, router])

  return (
    message !== 'Unauthorized' && (
      <>
        <ErrorPageHeader />
        <div className="py-32">
          <div className="flex flex-col space-y-8 text-center">
            <h2
              className={cn(
                'font-bold font-open-sans text-[28px] font-bold',
                'leading=[38.13px]'
              )}
            >
              Oops! Something went wrong
            </h2>
            <p
              className={cn(
                'font-open-sans text-[25px] font-normal',
                'leading-[34.05px]',
                'text-center'
              )}
            >
              {message ??
                'The page you are looking for might have been removed had its name changed or is temporarily unavailable.'}
            </p>

            <div>
              <Button
                onClick={reset}
                className={cn(
                  'text-lg text-center',
                  'font-semibold font-open-sans text-[13px]',
                  'leading-[17.7px]',
                  'bg-ui-dark-gray'
                )}
              >
                Retry
              </Button>
            </div>
          </div>
        </div>
      </>
    )
  )
}

export default Error
