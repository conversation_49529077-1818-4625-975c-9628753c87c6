import SignInButton from '@/components/SignInButton'
import { FaTwitter, FaFacebookF, FaLinkedinIn } from 'react-icons/fa'
import '@fontsource/source-sans-pro/400.css'
import '@fontsource/source-sans-pro/300.css'

const socialLinks = [
  {
    icon: <FaTwitter className="w-[29px] h-[32px]" />,
    href: 'https://twitter.com/Medisolv',
  },
  {
    icon: <FaFacebookF className="w-[29px] h-[32px]" />,
    href: 'https://www.facebook.com/medisolv',
  },
  {
    icon: <FaLinkedinIn className="w-[29px] h-[32px]" />,
    href: 'https://www.linkedin.com/company/medisolv"',
  },
]

const Home = async () => (
  <div className="relative h-[100vh] bg-[url(/images/provo1.jpg)] bg-cover bg-center bg-no-repeat overflow-hidden bg-center bg-color-[#002e66];">
    <div className="absolute top-0 h-full w-full bg-[#cd9557]/70"></div>

    <div className="relative h-[100vh] ">
      <div className="grid grid-cols-1 md:grid-cols-[40.5rem_auto] md:h-full">
        <div className="relative flex md:w-[40.5rem]">
          <div className="absolute top-0 w-full h-full bg-[#002e66cc]/80 origin-top-right md:skew-x-[-8deg]"></div>
          <span className="relative flex flex-col  text-white justify-center pl-[3.4rem] pr-[2rem]">
            <h1 className=" text-[3.5rem] mb-[1rem] font-bold font-['Merriweather'] relative ">
              Medisolv Platform
            </h1>
            <p className="mb-[3rem] text-[1.3rem] font-['Source_Sans_Pro'] font-light leading-normal text-left">
              Welcome to the <strong className="font-bold">medisolv</strong>{' '}
              platform! This is a one-stop spot for <br /> all things{' '}
              <strong className="font-bold">medisolv</strong>!!
            </p>
            <div className="input-group input-group-newsletter">
              <div className="input-group-append">
                <SignInButton />
              </div>
            </div>
          </span>
        </div>

        <div className="relative">
          <div className="md:absolute md:bottom-5 md:right-5">
            <ul className="mt-8 list-unstyled mb-0 text-center">
              {socialLinks.map((link, idx) => (
                <li
                  key={idx}
                  className="list-unstyled-item inline-block md:block mx-4 mb-8"
                >
                  <a href={link.href} target="_blank">
                    <div className="flex h-16 w-16 bg-[#002e66cc] text-3xl text-white rounded-full items-center justify-center">
                      {link.icon}
                    </div>
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export default Home
