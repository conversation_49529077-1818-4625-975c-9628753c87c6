import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { redisHelper } from '@/lib/redis'
import { auth } from '@/auth'
import appInsights from '@/lib/applicationInsights'


const requestSchema = z.object({
  organizationId: z.string().min(1, 'organizationId is required'),
})

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const session = await auth()
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized - Please login' },
        { status: 401 }
      )
    }

    // // Check if user has admin role
    // if (!isGlobalAdmin(decodedToken.getUserRoles(session?.accessToken!))!) {
    //   appInsights.trackEvent({
    //     name: 'UnauthorizedCacheClearAttempt',
    //     properties: {
    //       userId: session.uid,
    //     },
    //   })
    //   return NextResponse.json(
    //     { message: 'Forbidden - Administrator access required' },
    //     { status: 403 }
    //   )
    // }

    // Parse request body
    const body = await request.json()
    const params = requestSchema.safeParse(body)

    if (!params.success) {
      return NextResponse.json(
        { message: 'Invalid request', error: params.error },
        { status: 400 }
      )
    }

    const { organizationId } = params.data

    // Clear all cache keys containing this organizationId
    const keysCleared = await redisHelper.clearOrgKeys(organizationId)

    appInsights.trackEvent({
      name: 'CacheClearedForOrganization',
      properties: {
        organizationId,
        keysCleared,
        userId: session.uid,
      },
    })

    return NextResponse.json({
      success: true,
      message: `Cache cleared for organization ${organizationId}`,
      keysCleared,
    })
  } catch (error) {
    appInsights.trackException({
      exception: error instanceof Error ? error : new Error('Unknown error'),
      properties: {
        component: 'AdminCacheClearRoute',
      },
    })

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to clear cache',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    )
  }
}
