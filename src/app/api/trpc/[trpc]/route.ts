import { fetchRe<PERSON><PERSON><PERSON><PERSON> } from '@trpc/server/adapters/fetch'
import { NextResponse, type NextRequest } from 'next/server'

import { env } from '@/env'
import { appRouter } from '@/server/api/root'
import { createTRPCContext } from '@/server/api/trpc'
import appInsights from '@/lib/applicationInsights'

/**
 * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when
 * handling a HTTP request (e.g. when you make requests from Client Components).
 */
const createContext = async (req: NextRequest) => {
  return createTRPCContext({
    headers: req.headers,
  })
}

const handler = (req: NextRequest) =>
  fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => createContext(req),
    onError:
      env.NODE_ENV === 'development'
        ? ({ path, error }) => {
            appInsights.trackException({
              properties: {
                url: req.url,
                method: req.method,
                path: path ?? '<no-path>',
                request: req,
                success: false,
              },
              exception: error,
            })

            console.error(
              `❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`
            )

            if (error.message === 'Unauthorized') {
              console.log('throwing unauth 1')

              throw new Error('Unauthorized', {
                cause: {
                  statusCode: 401,
                  message: error.message,
                },
              })

              return NextResponse.redirect(new URL('/session/expired', req.url))
            }
          }
        : ({ error }) => {
            if (error.message === 'Unauthorized') {
              throw new Error('Unauthorized', {
                cause: {
                  statusCode: 401,
                  message: error.message,
                },
              })

              console.log('redirecting 2', error)
              return NextResponse.redirect(new URL('/session/expired', req.url))
            }
          },
  })

export { handler as GET, handler as POST }
