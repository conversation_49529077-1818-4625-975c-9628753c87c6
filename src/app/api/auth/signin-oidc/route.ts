import { signIn } from '@/auth'
import appInsights from '@/lib/applicationInsights'
import { NextApiResponse } from 'next'
import { NextRequest } from 'next/server'

const provider = 'citc-oidc'

const GET = async (request: NextRequest, res: NextApiResponse) => {
  appInsights?.trackTrace({
    message: 'GET /api/auth/signin-oidc',
    properties: {
      url: request.url,
      body: request.body,
      headers: request.headers,
      cookies: request.cookies,
      query: request.nextUrl.searchParams,
    },
  })

  await signIn(provider, { callbackUrl: '/measures' })
}

const POST = async (request: NextRequest, res: NextApiResponse) => {
  appInsights?.trackTrace({
    message: 'POST /api/auth/signin-oidc',
    properties: {
      url: request.url,
      body: request.body,
      headers: request.headers,
      cookies: request.cookies,
      query: request.nextUrl.searchParams,
    },
  })

  await signIn(provider, { callbackUrl: '/measures' })
}

export { GET, POST }
