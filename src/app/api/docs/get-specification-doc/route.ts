import { BlobServiceClient } from '@azure/storage-blob'
import { NextRequest, NextResponse } from 'next/server'
import { env } from '@/env'
import appInsights from '@/lib/applicationInsights'
export async function GET(req: NextRequest) {
  try {
    const path = req.nextUrl.searchParams.get('path')

    if (!path) {
      throw new Error('Path parameter is missing.')
    }
    //will base 64 encode on the client and use this later
    //const url = Buffer.from(path, 'base64').toString('ascii'); // Decode the path
    const url = path
    const urlParts = url.match(
      /https:\/\/(.*?)\.blob\.core\.windows\.net\/(.*?)\/(.*?)\/(.*)/
    )

    if (!urlParts) {
      throw new Error('Invalid URL format.')
    }

    const containerName = urlParts[2]
    const subdirectory = urlParts[3] || ''
    let fileName = urlParts[4]

    // Remove any trailing query string parameters from the file name
    fileName = fileName?.split('?')[0]

    const connString = env.MAPLE_BLOB_STORAGE_CONN_STRING
    if (!connString) {
      throw new Error(
        'STORAGE_ACCOUNT_CONNSTRING environment variable is not set.'
      )
    }

    const blobServiceClient = BlobServiceClient.fromConnectionString(connString)
    const containerClient = blobServiceClient.getContainerClient(containerName!)
    const blobClient = containerClient.getBlobClient(
      `${subdirectory}/${fileName}`
    )
    const blobDownloadResponse = await blobClient.download()
    const blobStream = blobDownloadResponse.readableStreamBody

    if (!blobStream) {
      throw new Error('Blob stream is null.')
    }

    const headers = new Headers()

    if (fileName?.endsWith('.html')) {
      // For HTML files, set Content-Type without Content-Disposition
      headers.set('Content-Type', 'text/html')
    } else {
      // For other files, set both Content-Type and Content-Disposition
      headers.set('Content-Disposition', `attachment; filename="${fileName}"`)
      headers.set(
        'Content-Type',
        blobDownloadResponse.contentType ?? 'application/octet-stream'
      )
    }

    return new Response(blobStream as any, {
      headers,
    })
  } catch (error: unknown) {
    appInsights.trackException({
      exception: error as Error,
      properties: { path: req.nextUrl.searchParams.get('path') },
    })

    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}
