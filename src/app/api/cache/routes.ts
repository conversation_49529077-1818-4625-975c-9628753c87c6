import type { NextRequest } from 'next/server'
import { z } from 'zod'
import { redisHelper } from '@/lib/redis'
import { NextApiResponse } from 'next'

const querySchema = z.object({
  organizationId: z.string().min(1, 'organizationId is required'),
})

/** @deprecated - This may not ever be used */
const GET = async (request: NextRequest, res: NextApiResponse) => {
  try {
    const params = querySchema.safeParse(
      Object.fromEntries(request.nextUrl.searchParams)
    )

    if (!params.success) return res.status(400).json(params.error)

    const { organizationId } = params.data

    const keysCleared = await redisHelper.clearOrgKeys(organizationId)

    if (keysCleared === 0)
      return res.status(200).json({
        message: `Cache Cleared for Organization ${organizationId} and ${keysCleared} keys`,
      }) //handleSuccess('No keys to clear', {organizationId, keysCleared})

    return res.status(200).json({
      message: `Cache Cleared for Organization ${organizationId} and ${keysCleared} keys`,
    })
  } catch (error) {
    return res.status(500).json({
      message: 'Internal Server Error - Failed to clear cache',
      Error: JSON.stringify(error),
    })
  }
}

export { GET }
