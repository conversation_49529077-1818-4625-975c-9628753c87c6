import { type NextRequest } from 'next/server'
import { handleError } from './apiResponseHandler'
import appInsights from '../../lib/applicationInsights'

const withAppInsights = (handler: (req: NextRequest) => Promise<Response>) => {
  return async (req: NextRequest) => {
    const start = Date.now()
    try {
      const res = await handler(req)
      appInsights.trackTrace({
        message: req.method + ' ' + req.url,
        properties: {
          url: req.url,
          duration: Date.now() - start,
          responseCode: res.status,
          success: true,
        },
      })
      return res
    } catch (error) {
      appInsights.trackException({ exception: error as Error, properties:{
        body:getObjectFromRequestBodyStream(req.body)
      } })
      return handleError(error)
    }
  }
}

export default withAppInsights


async function getObjectFromRequestBodyStream(body:ReadableStream<Uint8Array> | null) {
  try {
    if(!body) return "Empty Body";
    const input = await body.getReader().read();
    const decoder = new TextDecoder();
    const string = decoder.decode(input.value);
     return string;
  } catch (error) {
    //if we cant parse out the body swallow the error
    console.log(error)
  }
  return "";
}
