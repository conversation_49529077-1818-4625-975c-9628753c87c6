import { type ErrorWithCause } from '@/types/errorWithCause'
import { NextResponse } from 'next/server'
import { ZodError } from 'zod'

type ApiResponse<T = unknown, E = { message: string }> = {
  success: boolean
  message?: string
  data?: T
  errors?: E[]
}

type ErrorResponse<E = { message: string }> = ApiResponse<undefined, E>

/** @deprecated - all apis are moved to tprc. This is no longer needed for the time being */
const handleError = (
  error: unknown
): NextResponse<ErrorResponse> | NextResponse<unknown> => {
  // Check if the error is an instance of ZodError
  if (error instanceof ZodError) {
    return NextResponse.json(
      {
        success: false,
        errors: error.errors.map((err) => ({
          path: err.path.join('.'),
          message: err.message,
        })),
      },
      {
        status: 400,
        statusText: 'Bad Request',
      }
    )
  }

  const err = error as ErrorWithCause // Cast error to our custom Error type

  // Check if the error is a general Error instance
  if (err instanceof Error) {
    let statusCode = err.cause?.statusCode ?? 500
    let statusText = err.cause?.message ?? 'Internal Server Error'

    // Ensure the status code is valid (must be between 200 and 599)
    if (
      typeof statusCode !== 'number' ||
      statusCode < 200 ||
      statusCode > 599
    ) {
      statusCode = 500 // Default to 500 if the provided status code is invalid
    }

    if (err.message === 'Unauthorized') {
      statusCode = 401
      statusText = 'Unauthorized'
    }

    if (statusCode === 401) {
      return NextResponse.json(
        {
          success: false,
          errors: [{ message: statusText }],
        },
        {
          status: 401,
          statusText: statusText,
          headers: {
            'x-redirect': '/session/expired',
          },
        }
      )
    }

    return NextResponse.json(
      {
        success: false,
        errors: [{ message: err.message ?? 'An unexpected error occurred' }],
      },
      {
        status: statusCode,
        statusText: statusText,
      }
    )
  }

  return NextResponse.json(
    {
      success: false,
      errors: [{ message: 'Something went wrong' }],
    },
    {
      status: 500,
      statusText: 'Internal server error',
    }
  )
}

export type SuccessResponse<T = unknown> = ApiResponse<T, undefined>

/** @deprecated - all apis are moved to tprc. This is no longer needed for the time being */
const handleSuccess = <T>(
  message: string,
  data?: T
): NextResponse<SuccessResponse<T>> => {
  return NextResponse.json(
    {
      success: true,
      message,
      data,
    },
    {
      status: 200,
      statusText: 'OK',
    }
  )
}

export { handleSuccess, handleError }
