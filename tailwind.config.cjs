/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: ['./src/**/*.{ts,tsx}'],
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        'blue-1': '#2D7CBA',
        'blue-4': '#1b4a70',
        white: '#fff',
        lightskyblue: '#60b5e5',
        blueviolet: '#9747ff',
        'gray-1': '#7A7A7A',
        'gray-pale-gray': '#f9f8f9',
        'blue-3': '#205782',
        'ui-pale-blue': '#f5f7ff',
        'ui-dark-gray': '#566582',
        ghostwhite: '#f5f7fe',
        gray: {
          100: '#838383',
          200: '#787878',
        },
        'blue-5': '#173e5d',
        black: '#000',
        'ui-medium-blue': '#97a4ba',
        steelblue: '#13497c',
        'blue-2': '#2970a7',
        lavender: '#dee2f0',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
        drop: {
          '90%': {
            height: '20px',
          },
          '100%': {
            height: '160px',
            transform: 'translateY(calc(100vh + 160px))',
          },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'drop-1': 'drop 3s infinite linear 0.2s',
        'drop-2': 'drop 2s infinite linear 0.7s',
        'drop-3': 'drop 3s infinite linear 0.9s',
        'drop-4': 'drop 2s infinite linear 1.2s',
      },
      spacing: {},
      fontFamily: {
        'open-sans': 'Open Sans',
        sans: ['Open Sans', 'sans-serif'],
      },
      borderRadius: {
        '8xs': '5px',
        '12xs-5': '0.5px',
        '12xs': '1px',
        '8xs-1': '4.1px',
        '10xs': '3px',
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
    },
    fontSize: {
      mini: '0.938rem',
      sm: '0.813rem',
      xs: '0.75rem',
      '2xs': '0.688rem',
      '2xs-6': '0.663rem',
      '3xs': '0.625rem',
      '4xs': '0.563rem',
      xl: '1.25rem',
      '2xl': '1.563rem',
      '3xl': '1.875rem',
      base: '1rem',
      inherit: 'inherit',
    },
  },
  plugins: [require('tailwindcss-animate')],
}
