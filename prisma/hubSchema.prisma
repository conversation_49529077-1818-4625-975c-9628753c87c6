generator client {
  provider        = "prisma-client-js"
  output          = "./generated/hub"
  previewFeatures = ["multiSchema"]
  binaryTargets   = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlserver"
  url      = env("HUB_DATABASE_URL")
  schemas  = ["HUB"]
}

model ApplicationInfo {
  ApplicationId             Int     @id(map: "PK_ApplicationInfo")
  ApplicationName           String? @db.VarChar(500)
  URLForMeasureResult       String? @db.VarChar(500)
  URLForMeasureResultDetail String? @db.VarChar(500)

  @@schema("HUB")
}

model CustomWidget {
  Id              String    @id(map: "PK_CustomWidget") @db.VarChar(255)
  UserWidgetId    String    @db.VarChar(255)
  WidgetType      String    @db.VarChar(255)
  WidgetName      String    @db.VarChar(255)
  DisplayType     String?   @db.Var<PERSON><PERSON>(50)
  MeasureSelected String?   @db.Var<PERSON>har(255)
  StartDate       DateTime? @db.Date
  EndDate         DateTime? @db.Date
  Frequency       Int?

  @@schema("HUB")
}

model DashboardDetails {
  MappingId   Int        @id(map: "PK__Dashboar__8B57819DCE1848FF") @default(autoincrement())
  DashboardId String     @db.VarChar(255)
  UserId      String     @db.VarChar(255)
  IsDefault   Boolean
  IsShared    Boolean
  Dashboards  Dashboards @relation(fields: [DashboardId], references: [DashboardId], onUpdate: NoAction, map: "FK_DashboardDetails_Dashboards")

  @@schema("HUB")
}

model DashboardFavouritesReport {
  Id                  Int     @id(map: "PK__Dashboar__3214EC0765CEF2DF") @default(autoincrement())
  DashboardId         String  @db.VarChar(255)
  IsAddedToFavourites Boolean
  UserId              String  @db.VarChar(255)

  @@schema("HUB")
}

model DashboardReport {
  Id                 String    @id(map: "PK_HUB.DashboardReport") @db.VarChar(255)
  ReportName         String    @db.VarChar(100)
  SisenseDashboardId String?   @db.VarChar(255)
  ReportType         Int
  FilterSettings     String?   @db.VarChar(Max)
  Status             Boolean?
  LastUpdatedBy      String?   @db.UniqueIdentifier
  LastUpdatedDate    DateTime? @db.DateTime

  @@schema("HUB")
}

model DashboardReportType {
  ReportId   Int    @id(map: "PK__Dashboar__D5BD4805596A1291")
  ReportType String @db.VarChar(100)

  @@schema("HUB")
}

model DashboardReportWidgets {
  Id                String  @id(map: "PK_DashboardReportWidgets") @db.VarChar(255)
  DashboardReportID String? @db.VarChar(255)
  SisenseWidgetId   String? @db.VarChar(255)
  Settings          String? @db.VarChar(Max)

  @@schema("HUB")
}

model Dashboards {
  DashboardId      String             @id(map: "PK__Dashboar__C711E1D01879EE78") @db.VarChar(255)
  Name             String             @db.VarChar(50)
  Description      String             @db.VarChar(255)
  DashboardDetails DashboardDetails[]
  HubDashboards    HubDashboards[]
  UserWidgets      UserWidgets[]

  @@schema("HUB")
}

model ExternalDashboards {
  DashboardGuid  String  @id(map: "PK__External__267B23266FC87994") @db.VarChar(255)
  DashboardTitle String? @db.VarChar(255)
  DashboardOid   String  @db.VarChar(255)

  @@schema("HUB")
}

model HubDashboards {
  MappingId   Int        @id(map: "PK__HubDashb__8B57819DCCF70889") @default(autoincrement())
  HubId       String     @db.VarChar(255)
  DashboardId String     @db.VarChar(255)
  UserId      String     @db.VarChar(255)
  IsDefault   Boolean
  IsShared    Boolean
  Dashboards  Dashboards @relation(fields: [DashboardId], references: [DashboardId], onUpdate: NoAction, map: "FK_HubDashboards_Dashboards")
  Hubs        Hubs       @relation(fields: [HubId], references: [HubId], onUpdate: NoAction, map: "FK_HubDashboards_Hubs")

  @@schema("HUB")
}

model Hubs {
  HubId         String          @id(map: "PK__Hubs__9F4FFEEF3CDACDA0") @db.VarChar(255)
  Name          String          @db.VarChar(255)
  OwnerId       String?         @db.VarChar(255)
  ExternalUrl   String?         @db.VarChar(255)
  IsPrimary     Boolean
  SortOrder     Int             @db.TinyInt
  Message       String?         @db.VarChar(Max)
  HubDashboards HubDashboards[]
  SubPages      SubPages[]

  @@schema("HUB")
}

model MeasureGoals {
  HubGoalsID                     Int      @id(map: "PK_MeasureGoals") @default(autoincrement())
  MeasureIdentifier              String   @db.UniqueIdentifier
  SubOrganizationId              String   @db.UniqueIdentifier
  StartDate                      DateTime @db.DateTime
  EndDate                        DateTime @db.DateTime
  GoalLower                      Decimal? @db.Decimal(15, 2)
  GoalUpper                      Decimal? @db.Decimal(15, 2)
  Benchmark                      Decimal? @db.Decimal(15, 2)
  IsYellowZoneFixedNumber        Boolean?
  YellowZone                     Decimal? @db.Decimal(15, 2)
  IsExceptionalPerformanceNumber Boolean?
  ExceptionalPerformance         Decimal? @db.Decimal(15, 2)
  LastUpdatedByUserId            String   @db.UniqueIdentifier
  LastUpdatedDateTime            DateTime @db.DateTime

  @@schema("HUB")
}

model MeasureResultDetail {
  MeasureResultDetailId   BigInt    @id(map: "PK_MeasureResultDetail")
  MeasureResultId         BigInt
  UniqueMeasureIdentifier String    @db.VarChar(50)
  PatientIdentifier       String    @db.VarChar(50)
  CaseIdentifier          String    @db.VarChar(50)
  EncounterId             Int?
  PatientName             String?   @db.VarChar(200)
  Age                     Int?      @db.SmallInt
  Sex                     String?   @db.VarChar(10)
  FacilityId              String?   @db.VarChar(50)
  Result                  String?   @db.VarChar(50)
  DischargeDateTime       DateTime? @db.DateTime

  @@schema("HUB")
}

model MeasureResults {
  MeasureResultId         BigInt    @id(map: "PK_MeasureResults") @default(autoincrement())
  UniqueMeasureIdentifier String    @db.VarChar(50)
  CmsId                   String?   @db.VarChar(250)
  ApplicationID           String    @db.VarChar(50)
  PatientIdentifier       String    @db.VarChar(50)
  CaseIdentifier          String?   @db.VarChar(50)
  EncounterId             Int?
  PatientName             String?   @db.VarChar(200)
  Age                     Int?      @db.SmallInt
  Sex                     String?   @db.VarChar(10)
  FacilityId              String?   @db.VarChar(50)
  Result                  String?   @db.VarChar(50)
  DischargeDateTime       DateTime? @db.DateTime
  RelevantDateTime        DateTime? @db.DateTime

  @@schema("HUB")
}

model MeasuresSortOrder {
  Id               Int    @id(map: "PK__Measures__3214EC07776EA481")
  MeasureIdentfier String @db.UniqueIdentifier
  SortOrder        Int?

  @@schema("HUB")
}

model OrganizationPreferences {
  Id    Int    @id(map: "PK__Organiza__3214EC0761052D8E") @default(autoincrement())
  Key   String @db.NVarChar(255)
  Value String @db.NVarChar(255)

  @@schema("HUB")
}

model Pages {
  PageId             String     @id(map: "PK__Pages__C565B104CAAF4C0E") @db.VarChar(255)
  Name               String     @db.VarChar(50)
  OwnerId            String?    @db.VarChar(255)
  IsPrimary          Boolean
  RouteOrExternalUrl String?    @db.VarChar(255)
  IsExternal         Boolean
  SortOrder          Int        @db.TinyInt
  Message            String?    @db.VarChar(Max)
  IsVisible          Boolean    @default(true, map: "DF__Pages__IsVisible__22751F6C")
  SubPages           SubPages[]

  @@schema("HUB")
}

model RawMeasureResult {
  MeasureResultId          BigInt   @id(map: "PK_RawMeasureResult")
  ApplicationId            Int
  OrganizationId           String   @db.VarChar(50)
  SubOrganizationId        String   @db.VarChar(50)
  UniqueMeasureIdentifier  String   @db.VarChar(50)
  MeasureIdentifier        String   @db.VarChar(50)
  RelevantDateTime         DateTime @db.DateTime
  RelevantSubOrganizations String?  @db.NVarChar(Max)
  MeasureResult            String   @db.VarChar(50)

  @@schema("HUB")
}

model ReportTypes {
  Id         Int     @id(map: "PK_Hub.ReportTypes")
  ReportType String? @db.VarChar(100)

  @@schema("HUB")
}

model SavedFilters {
  Id             Int    @id(map: "PK__SavedFil__3214EC07178BB920") @default(autoincrement())
  FilterName     String @db.VarChar(100)
  FilterMetadata String @db.VarChar(Max)
  UserId         String @db.VarChar(255)

  @@schema("HUB")
}

model SavedViews {
  Id           Int      @id(map: "PK__SavedVie__3214EC078A834384") @default(autoincrement())
  ViewName     String   @db.VarChar(100)
  ViewMetadata String   @db.VarChar(Max)
  UserId       String   @db.VarChar(255)
  IsDefault    Boolean?
  IsShared     Boolean?

  @@schema("HUB")
}

model SisenseUserDetails {
  Id            Int     @id(map: "PK_HUB.SisenseUsreDetails") @default(autoincrement())
  UserID        String  @db.VarChar(255)
  UserEmail     String? @db.NVarChar(255)
  SisenseUserID String  @db.VarChar(255)

  @@schema("HUB")
}

model SubPages {
  SubPageId  String  @id(map: "PK_SubPages") @db.VarChar(255)
  Identifier String  @db.VarChar(50)
  ApiUrl     String  @db.VarChar(Max)
  Owner      String  @db.VarChar(50)
  PageId     String  @db.VarChar(255)
  HubId      String  @db.VarChar(255)
  IsParent   Boolean
  Hubs       Hubs    @relation(fields: [HubId], references: [HubId], onUpdate: NoAction, map: "FK_SubPages_Hubs")
  Pages      Pages   @relation(fields: [PageId], references: [PageId], onUpdate: NoAction, map: "FK_SubPages_Pages")

  @@schema("HUB")
}

model UserNotificationMap {
  Id                Int               @id(map: "PK__UserNoti__3214EC07C5C90DD2") @default(autoincrement())
  UserId            String            @db.VarChar(255)
  ReadDateTime      DateTime?         @db.DateTime
  NotificationId    String            @db.VarChar(255)
  UserNotifications UserNotifications @relation(fields: [NotificationId], references: [NotificationId], onUpdate: NoAction, map: "FK_UserNotificationMap_UserNotifications")

  @@schema("HUB")
}

model UserNotifications {
  NotificationId      String                @id(map: "PK__UserNoti__20CF2E127B61FD74") @db.VarChar(255)
  Message             String                @db.VarChar(512)
  OwnerId             String                @db.VarChar(255)
  SentDateTime        DateTime              @db.DateTime
  UserNotificationMap UserNotificationMap[]

  @@schema("HUB")
}

model UserPreferences {
  Id     Int    @id(map: "PK__UserPref__3214EC077A390F24") @default(autoincrement())
  Key    String @db.VarChar(50)
  Value  String @db.VarChar(255)
  UserId String @db.VarChar(255)

  @@schema("HUB")
}

model UserWidgets {
  Id                 String     @id(map: "PK__UserWidg__3214EC0792C4C903") @db.VarChar(255)
  WidgetId           String     @db.VarChar(255)
  SisenseDashboardId String?    @db.VarChar(255)
  Title              String     @db.VarChar(255)
  Description        String?    @db.VarChar(1028)
  Spotlight          Boolean
  Disabled           Boolean
  Affinity           Int        @db.TinyInt
  DashboardId        String     @db.VarChar(255)
  Settings           String?    @db.VarChar(Max)
  Owner              String?    @db.VarChar(50)
  SandboxMode        Boolean    @default(false, map: "DF__UserWidge__Sandb__2180FB33")
  Dashboards         Dashboards @relation(fields: [DashboardId], references: [DashboardId], onUpdate: NoAction, map: "FK_UserWidgets_Dashboards")

  @@schema("HUB")
}
