generator client {
  provider        = "prisma-client-js"
  output          = "./generated/adm"
  previewFeatures = ["multiSchema"]
  binaryTargets   = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlserver"
  url      = env("ADM_DATABASE_URL")
  schemas  = ["ADM", "AHRQ", "CMAP", "CMS", "CSRC", "DQ", "MECA", "MPRE", "MSTD", "MTOOL", "RISK", "STG_ADM", "STG_CSRC", "STG_RISK"]
}

model ActiveMeasures {
  Id                  BigInt    @id(map: "PK__ActiveMe__3214EC074D41B28B") @default(autoincrement())
  MeasureGUID         String    @db.UniqueIdentifier
  MeasureSubId        String?   @db.NVarChar(20)
  EntitiesId          BigInt?
  OnDate              DateTime? @db.DateTime
  ProcessingStartDate DateTime? @db.DateTime
  LastUpdateDateTime  DateTime? @db.DateTime
  IsMIPSMeasure       Boolean?
  Entities            Entities? @relation(fields: [EntitiesId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__ActiveMea__Entit__7152C524")

  @@index([EntitiesId, OnDate], map: "idx_mca_activemeasures_entitiesid_ondate")
  @@schema("MECA")
}

model AuditLog {
  Id            BigInt    @id(map: "PK_RISK_AuditLog_Id") @default(autoincrement())
  JobName       String    @db.VarChar(255)
  UserName      String?   @db.VarChar(255)
  StartDateTime DateTime  @db.DateTime
  EndDateTime   DateTime? @db.DateTime
  SuccessStatus Boolean
  Note          String?   @db.VarChar(Max)

  @@schema("RISK")
}

model BilledCodeModifiers {
  Id                BigInt            @id(map: "PK_CSRC_BilledCodeModifiers") @default(autoincrement())
  SourceCode        String            @db.NVarChar(500)
  SourceDescription String?           @db.NVarChar(500)
  SourceCodeSystem  String?           @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  BilledCodes       ADM_BilledCodes[] @ignore

  @@schema("CSRC")
}

model CSRC_BilledCodes {
  Id                BigInt            @id(map: "PK_CSRC_BilledCodes") @default(autoincrement())
  SourceCode        String            @db.NVarChar(500)
  SourceDescription String?           @db.NVarChar(500)
  SourceCodeSystem  String?           @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  BilledCodes       ADM_BilledCodes[] @ignore

  @@map("BilledCodes")
  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ADM_BilledCodes {
  Id                                                   BigInt               @default(autoincrement())
  SourceTransactionId                                  String?              @db.VarChar(100)
  SourceLinkedTransactionId                            String?              @db.VarChar(100)
  PatientsId                                           BigInt?
  EncountersId                                         BigInt?
  ServiceDateTime                                      DateTime?            @db.DateTime
  BilledCodeId                                         BigInt?
  BilledCodeModifierId                                 BigInt?
  OrderingProviderId                                   BigInt?
  RenderingProviderId                                  BigInt?
  BillingProviderId                                    BigInt?
  Quantity                                             Decimal?             @db.Decimal(7, 2)
  ChargedAmount                                        Float?               @db.Money
  CostAmount                                           Float?               @db.Money
  RevenueCode                                          String?              @db.VarChar(100)
  DataSourceId                                         Int
  SourcePrimaryKey                                     String?              @db.VarChar(500)
  IsActive                                             Boolean?
  HashValue                                            Bytes?               @db.Binary(16)
  SourceTransactionChargeCode                          String?              @db.VarChar(100)
  EhrTypeId                                            Int?
  EhrInstance                                          Int?
  BilledCodes                                          CSRC_BilledCodes?    @relation(fields: [BilledCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_BilledCodes_BilledCodeId")
  BilledCodeModifiers                                  BilledCodeModifiers? @relation(fields: [BilledCodeModifierId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_BilledCodes_BilledCodeModifierId")
  Providers_BilledCodes_BillingProviderIdToProviders   Providers?           @relation("BilledCodes_BillingProviderIdToProviders", fields: [BillingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_BilledCodes_BillingProviderId")
  Encounters                                           ADM_Encounters?      @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_BilledCodes_EncountersId")
  Providers_BilledCodes_OrderingProviderIdToProviders  Providers?           @relation("BilledCodes_OrderingProviderIdToProviders", fields: [OrderingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_BilledCodes_OrderingProviderId")
  Patients                                             Patients?            @relation(fields: [PatientsId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_BilledCodes_PatientsId")
  Providers_BilledCodes_RenderingProviderIdToProviders Providers?           @relation("BilledCodes_RenderingProviderIdToProviders", fields: [RenderingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_BilledCodes_RenderingProviderId")

  @@map("BilledCodes")
  @@ignore
  @@schema("ADM")
}

model MECA_CategoryAssignments {
  Id                         Int                          @id(map: "PK__Category__3214EC07292CC0BC") @default(autoincrement()) @db.SmallInt
  Code                       String                       @db.NVarChar(100)
  Description                String?                      @db.NVarChar(255)
  MeasureCategoryAssignments MeasureCategoryAssignments[]

  @@map("CategoryAssignments")
  @@schema("MECA")
}

model RISK_CategoryAssignments {
  Id                      Int                       @id(map: "PK_CategoryAssignments_Id") @default(autoincrement()) @db.SmallInt
  Name                    String                    @unique(map: "UQ_CategoryAssignments_Name") @db.NVarChar(100)
  EncounterMeasureResults EncounterMeasureResults[]

  @@map("CategoryAssignments")
  @@schema("RISK")
}

model Charges {
  Id                  BigInt          @id(map: "PK__Charges__3214EC07E72830F0") @default(autoincrement())
  EncounterId         BigInt
  SourceTransactionId Int?
  ServiceDateTime     DateTime?       @db.DateTime
  Quantity            Int?
  ChargedAmount       Float?          @db.Money
  CostAmount          Float?          @db.Money
  RevenueCode         String?         @db.NVarChar(10)
  Description         String?         @db.NVarChar(100)
  DerivedCost         Float?          @db.Money
  Encounters          RISK_Encounters @relation(fields: [EncounterId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__Charges__Encount__009508B4") @ignore

  @@schema("RISK")
}

model Cohorts {
  Id                  Int                   @id(map: "PK_RISK_Cohorts_Id") @default(autoincrement())
  Code                String                @db.VarChar(100)
  Description         String                @db.VarChar(255)
  Division            String?               @db.VarChar(100)
  MeasureCategoryId   Int?
  CohortStandardCodes CohortStandardCodes[] @ignore

  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CohortStandardCodes {
  Id                  BigInt   @id(map: "PK_CohortCodes") @default(autoincrement())
  CohortId            Int
  StandardCode        String   @db.VarChar(100)
  IsInclusion         Boolean
  CMSGroupCode        String?  @db.VarChar(10)
  CodeType            String?  @db.VarChar(2)
  Subcategory         String?  @db.VarChar(50)
  Principal           Boolean?
  Secondary           Boolean?
  RequirePOA          Boolean?
  RequirePOASecondary Boolean?
  ProgramYearStart    Int?
  ProgramYearEnd      Int?
  Cohorts             Cohorts  @relation(fields: [CohortId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__CohortSta__Cohor__60FC61CA")

  @@ignore
  @@schema("RISK")
}

model ControlChartDetails {
  Id                    BigInt              @id(map: "PK__ControlC__3214EC073D3BA874") @default(autoincrement())
  ControlChartSummaryId BigInt
  MeasureSummaryId      BigInt
  Value                 Float?
  LowerLimit            Float?
  LowerWarning          Float?
  CenterLine            Float?
  UpperLimit            Float?
  UpperWarning          Float?
  Comment               String?             @db.NVarChar(255)
  ControlChartSummary   ControlChartSummary @relation(fields: [ControlChartSummaryId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__ControlCh__Contr__733B0D96")
  MeasureSummary        MECA_MeasureSummary @relation(fields: [MeasureSummaryId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__ControlCh__Measu__742F31CF") @ignore

  @@schema("MECA")
}

model ControlChartLog {
  Id                 Int       @id(map: "PK__ControlC__3214EC07EF0A1F96") @default(autoincrement())
  Exclude            Int?
  NewPhase           Boolean?
  Comment            String?   @db.VarChar(500)
  SavedBy            String?   @db.VarChar(500)
  SavedByName        String?   @db.VarChar(500)
  StartDate          DateTime
  Period             String    @db.VarChar(1)
  MedisolvMeasureId  String    @db.UniqueIdentifier
  EntityId           BigInt
  IsActive           Boolean?
  LastUpdateDateTime DateTime?
  AddedFromYearly    Boolean?
  AddedFromQuarterly Boolean?
  AddedFromMonthly   Boolean?
  ApplyToQuarter     Boolean?
  ApplyToYear        Boolean?

  @@schema("MECA")
}

model ControlChartSummary {
  Id                  BigInt                @id(map: "PK__ControlC__3214EC0703B0F97F") @default(autoincrement())
  MeasureGUID         String                @db.UniqueIdentifier
  EntitiesId          BigInt?
  Period              String                @db.NVarChar(5)
  Type                String                @db.NVarChar(20)
  SuppressionReason   String?               @db.NVarChar(500)
  RuleViolations      String?               @db.NVarChar(500)
  RiskAdjusted        Boolean
  ControlChartDetails ControlChartDetails[]
  Entities            Entities?             @relation(fields: [EntitiesId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__ControlCh__Entit__1229A90A")

  @@schema("MECA")
}

model DashboardDetails {
  Id             Int      @id(map: "PK__Dashboar__3214EC07275DC30C") @default(autoincrement())
  DashboardId    Int
  UserId         String   @db.VarChar(255)
  IsDefault      Boolean
  IsShared       Boolean
  IsFavorite     Boolean?
  OrganizationId String?  @db.VarChar(255)

  @@schema("MECA")
}

model Dashboards {
  Id             Int     @id(map: "PK__Dashboar__3214EC07A7B02DB2") @default(autoincrement())
  Name           String  @db.VarChar(255)
  Description    String? @db.VarChar(255)
  Configurations String? @db.VarChar(Max)

  @@schema("MECA")
}

model Diagnoses {
  Id                 BigInt          @id(map: "PK__Diagnose__3214EC07A5E80AB5") @default(autoincrement())
  EncounterId        BigInt
  DiagnosisCode      String          @db.NVarChar(10)
  Ordinality         Int?
  PresentOnAdmission String?         @db.NVarChar(1)
  AdmitDateTime      DateTime?       @db.DateTime
  IsHistorical       Boolean?
  Encounters         RISK_Encounters @relation(fields: [EncounterId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__Diagnoses__Encou__0EE3280B") @ignore

  @@schema("RISK")
}

model DiagnosisVariables {
  Id                    Int     @id(map: "PK_DiagnosesVariablesId") @default(autoincrement())
  DiagnosisCode         String? @db.VarChar(50)
  CCS                   Int?
  CCSModified           String? @db.VarChar(10)
  Description           String? @db.VarChar(100)
  Acute                 Int?    @db.SmallInt
  APD                   Int?    @db.SmallInt
  AcuteProgramYearStart Int?
  AcuteProgramYearEnd   Int?
  APDProgramYearStart   Int?
  APDProgramYearEnd     Int?
  FiscalYear            Int?

  @@schema("RISK")
}

model EncorAEncounters {
  Id                    BigInt          @id(clustered: false, map: "PK_ADM_EncorAEncounters") @default(autoincrement())
  CM_MEDICARE_ID        String          @db.VarChar(6)
  PATIENT_ID            String          @db.VarChar(40)
  MEDICAL_RECORD_NUMBER String          @db.VarChar(15)
  PatientsId            BigInt?
  EncountersId          BigInt?
  Encounters            ADM_Encounters? @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncorAEncounters_EncountersId") @ignore
  Patients              Patients?       @relation(fields: [PatientsId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncorAEncounters_PatientsId") @ignore

  @@schema("ADM")
}

model EncorAGapEncounters {
  Id                    BigInt             @id(clustered: false, map: "PK_ADM_EncorAGapEncounters") @default(autoincrement())
  PatientsId            BigInt?
  GapPatientsId         BigInt?
  CM_MEDICARE_ID        String             @db.VarChar(6)
  PATIENT_ID            String             @db.VarChar(40)
  MEDICAL_RECORD_NUMBER String             @db.VarChar(15)
  CaseIdentifier        String             @db.VarChar(255)
  AdmitDate             DateTime?          @db.DateTime
  DischargeDate         DateTime?          @db.DateTime
  DischargeDisposition  String?            @db.VarChar(2)
  PrincipalDX           String?            @db.VarChar(10)
  EncorAGapPatients     EncorAGapPatients? @relation(fields: [GapPatientsId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncorAGapEncounters_GapPatientsId")
  Patients              Patients?          @relation(fields: [PatientsId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncorAGapEncounters_PatientsId") @ignore

  @@schema("ADM")
}

model EncorAGapPatients {
  Id                  BigInt                @id(clustered: false, map: "PK_ADM_EncorAGapPatients") @default(autoincrement())
  FirstName           String                @db.VarChar(255)
  LastName            String                @db.VarChar(255)
  BirthDateTime       DateTime              @db.DateTime
  Gender              String?               @db.VarChar(1)
  Race                String?               @db.VarChar(1)
  EncorAGapEncounters EncorAGapEncounters[]

  @@schema("ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncorAStatus {
  Id                 BigInt    @default(autoincrement())
  Client             String    @db.VarChar(255)
  LastFileDateTime   DateTime? @db.DateTime
  LastUpdateDateTime DateTime  @db.DateTime
  LastSPStatus       Int?
  MaxFileDateTime    DateTime? @db.DateTime

  @@ignore
  @@schema("ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterAdmitPriorityCodes {
  Id                BigInt           @id(map: "PK_CSRC_EncounterAdmitPriorityCodes") @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Encounters        ADM_Encounters[]

  @@ignore
  @@schema("CSRC")
}

model EncounterAdmitServiceCodes {
  Id                BigInt           @id(map: "PK_CSRC_EncounterAdmitServiceCodes") @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Encounters        ADM_Encounters[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterAdmitSourceCodes {
  Id                BigInt           @id @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Encounters        ADM_Encounters[]

  @@ignore
  @@schema("CSRC")
}

model EncounterBillingClass {
  Id                BigInt           @id(map: "PK_CSRC_EncounterBillingClass") @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Encounters        ADM_Encounters[] @ignore

  @@schema("CSRC")
}

model EncounterConditionDiagnosisCodes {
  Id                  BigInt                @id(map: "PK_CSRC_EncounterConditionDiagnosisCodes") @default(autoincrement())
  SourceCode          String                @db.NVarChar(40)
  SourceDescription   String?               @db.NVarChar(255)
  SourceCodeSystem    String?               @db.NVarChar(500)
  DataSourceId        Int
  EhrTypeId           Int?
  EhrInstance         Int?
  EncounterConditions EncounterConditions[] @ignore

  @@schema("CSRC")
}

model EncounterConditionPresentOnAdmits {
  Id                  BigInt                @id(map: "PK_CSRC_EncounterConditionPresentOnAdmits") @default(autoincrement())
  SourceCode          String                @db.NVarChar(30)
  SourceDescription   String?               @db.NVarChar(255)
  DataSourceId        Int
  EhrTypeId           Int?
  EhrInstance         Int?
  EncounterConditions EncounterConditions[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterConditions {
  Id                                BigInt                             @id(clustered: false, map: "PK_ADM_EncounterConditions") @default(autoincrement())
  EncountersId                      BigInt
  DiagnosisCodeId                   BigInt?
  Ordinality                        String?                            @db.VarChar(25)
  PresentOnAdmitId                  BigInt?
  SeverityCodeId                    BigInt?
  IsBillingDiagnosis                Boolean?
  DataSourceId                      Int
  SourcePrimaryKey                  String?                            @db.VarChar(500)
  IsActive                          Boolean?
  HashValue                         Bytes?                             @db.Binary(16)
  EhrTypeId                         Int?
  EhrInstance                       Int?
  EncounterConditionDiagnosisCodes  EncounterConditionDiagnosisCodes?  @relation(fields: [DiagnosisCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncounterConditions_DiagnosisCodeId")
  Encounters                        ADM_Encounters                     @relation(fields: [EncountersId], references: [Id], onUpdate: NoAction, map: "FK_ADM_EncounterConditions_EncountersId")
  EncounterConditionPresentOnAdmits EncounterConditionPresentOnAdmits? @relation(fields: [PresentOnAdmitId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncounterConditions_PresentOnAdmitId")
  EncounterConditionSeverityCodes   EncounterConditionSeverityCodes?   @relation(fields: [SeverityCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncounterConditions_SeverityCodeId")

  @@ignore
  @@schema("ADM")
}

model EncounterConditionSeverityCodes {
  Id                  BigInt                @id(map: "PK_CSRC_EncounterConditionSeverityCodes") @default(autoincrement())
  SourceCode          String                @db.NVarChar(500)
  SourceDescription   String?               @db.NVarChar(500)
  SourceCodeSystem    String?               @db.NVarChar(500)
  DataSourceId        Int
  EhrTypeId           Int?
  EhrInstance         Int?
  EncounterConditions EncounterConditions[] @ignore

  @@schema("CSRC")
}

model EncounterDecisionToAdmitCodes {
  Id                BigInt           @id(map: "PK_CSRC_EncounterDecisionToAdmitCodes") @default(autoincrement())
  SourceCode        String
  SourceDescription String?
  SourceCodeSystem  String?
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Encounters        ADM_Encounters[] @ignore

  @@schema("CSRC")
}

model EncounterDetailCodes {
  Id                BigInt             @id(map: "PK_CSRC_EncounterDetailCodes") @default(autoincrement())
  SourceCode        String             @db.NVarChar(500)
  SourceDescription String?            @db.NVarChar(500)
  SourceCodeSystem  String?            @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  EncounterDetails  EncounterDetails[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterDetails {
  Id                     BigInt                @default(autoincrement())
  EncountersId           BigInt
  FacilitiesId           BigInt?
  LocationsId            BigInt?
  EncounterStartDateTime DateTime?             @db.DateTime
  EncounterEndDateTime   DateTime?             @db.DateTime
  EncounterDetailCodeId  BigInt?
  EncounterDetailTypeId  BigInt?
  DataSourceId           Int
  SourcePrimaryKey       String?               @db.VarChar(500)
  IsActive               Boolean?
  HashValue              Bytes?                @db.Binary(16)
  EhrTypeId              Int?
  EhrInstance            Int?
  EncounterDetailCodes   EncounterDetailCodes? @relation(fields: [EncounterDetailCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncounterDetails_EncounterDetailCodeId")
  EncounterDetailTypes   EncounterDetailTypes? @relation(fields: [EncounterDetailTypeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncounterDetails_EncounterDetailTypeId")
  Encounters             ADM_Encounters        @relation(fields: [EncountersId], references: [Id], onUpdate: NoAction, map: "FK_ADM_EncounterDetails_EncountersId")
  Facilities             Facilities?           @relation(fields: [FacilitiesId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncounterDetails_FacilitiesId")
  Locations              Locations?            @relation(fields: [LocationsId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncounterDetails_LocationsId")

  @@index([EncounterDetailCodeId], map: "idx_adm_encounterdetails_encounterdetailcodeid_include_encounterid_facilitiesid_encounterstartdatetime_encounterenddatetime")
  @@index([EncountersId], map: "idx_adm_encounterdetails_encountersid_include_encounterdetailcodeid_facilitiesid_encounterstartdatetime_encounterenddatetime")
  @@index([FacilitiesId], map: "idx_adm_encounterdetails_facilitiesid_include_encountersid")
  @@index([SourcePrimaryKey], map: "idx_ADM_EncounterDetails_SourcePrimarKey")
  @@ignore
  @@schema("ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterDetails_BKP {
  Id                     BigInt    @default(autoincrement())
  EncountersId           BigInt
  FacilitiesId           BigInt?
  LocationsId            BigInt?
  EncounterStartDateTime DateTime? @db.DateTime
  EncounterEndDateTime   DateTime? @db.DateTime
  EncounterDetailCodeId  BigInt?
  EncounterDetailTypeId  BigInt?
  DataSourceId           Int
  SourcePrimaryKey       String?   @db.VarChar(500)
  IsActive               Boolean?
  HashValue              Bytes?    @db.Binary(16)
  EhrTypeId              Int?
  EhrInstance            Int?

  @@ignore
  @@schema("ADM")
}

model EncounterDetailTypes {
  Id                BigInt             @id(map: "PK_CSRC_EncounterDetailTypeCodes") @default(autoincrement())
  SourceCode        String             @db.NVarChar(500)
  SourceDescription String?            @db.NVarChar(500)
  SourceCodeSystem  String?            @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  EncounterDetails  EncounterDetails[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterDiagnosisRelatedGroup {
  Id                                     BigInt          @default(autoincrement())
  EncountersId                           BigInt?
  EncounterDiagnosisRelatedGroupCodeId   BigInt?
  EncounterDiagnosisRelatedGroupStatusId BigInt?
  DiagnosisRelatedGroupGrouperVersion    String?         @db.VarChar(255)
  ApDrgSeverityOfIllness                 String?         @db.VarChar(255)
  ApDrgRiskofMortality                   String?         @db.VarChar(255)
  DataSourceId                           Int
  SourcePrimaryKey                       String?         @db.VarChar(500)
  IsActive                               Boolean?
  HashValue                              Bytes?          @db.Binary(16)
  EhrTypeId                              Int?
  EhrInstance                            Int?
  Encounters                             ADM_Encounters? @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncounterDiagnosisRelatedGroup_EncountersId")

  @@index([SourcePrimaryKey], map: "idx_ADM_EncounterDiagnosisRelatedGroup_SourcePrimarKey")
  @@ignore
  @@schema("ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterDischargeDispositionCodes {
  Id                BigInt           @id @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Encounters        ADM_Encounters[]

  @@ignore
  @@schema("CSRC")
}

model EncounterDRGCodes {
  Id                BigInt  @id(map: "PK_CSRC_EncounterDRGCodes") @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@schema("CSRC")
}

model EncounterDRGStatus {
  Id                BigInt  @id(map: "PK_CSRC_EncounterDRGStatus") @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@schema("CSRC")
}

model EncounterFeatures {
  Id           BigInt          @id(map: "PK_RISK_EncounterFeatures_Id") @default(autoincrement())
  EncounterId  BigInt
  FeatureId    Int
  FeatureValue Float
  Encounters   RISK_Encounters @relation(fields: [EncounterId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__Encounter__Encou__168449D3") @ignore
  Features     Features        @relation(fields: [FeatureId], references: [Id], onUpdate: NoAction, map: "FK__Encounter__Featu__1590259A")

  @@schema("RISK")
}

model EncounterMeasureResults {
  Id                   BigInt                   @id(map: "PK_EncounterMeasureResults_Id") @default(autoincrement())
  EncounterId          BigInt
  MeasureId            Int
  CategoryAssignmentId Int                      @db.SmallInt
  Value                String?                  @db.NVarChar(100)
  Note                 String?                  @db.NVarChar(500)
  CategoryAssignments  RISK_CategoryAssignments @relation(fields: [CategoryAssignmentId], references: [Id], onUpdate: NoAction, map: "FK_EncounterMeasureResults_CategoryAssignmentId")
  Encounters           RISK_Encounters          @relation(fields: [EncounterId], references: [Id], onUpdate: NoAction, map: "FK_EncounterMeasureResults_EncounterId") @ignore
  Measures             RISK_Measures            @relation(fields: [MeasureId], references: [Id], onUpdate: NoAction, map: "FK_EncounterMeasureResults_MeasureId")

  @@index([EncounterId, MeasureId], map: "idx_Risk_Encounter_MeasureResults_EncounterId_MeasureId")
  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterProviders {
  Id                      BigInt                  @default(autoincrement())
  EncountersId            BigInt
  ProvidersId             BigInt
  EncounterProviderTypeId BigInt?
  DataSourceId            Int
  SourcePrimaryKey        String?                 @db.VarChar(500)
  IsActive                Boolean?
  HashValue               Bytes?                  @db.Binary(16)
  EhrTypeId               Int?
  EhrInstance             Int?
  EncounterProviderTypes  EncounterProviderTypes? @relation(fields: [EncounterProviderTypeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_EncounterProviders_EncounterProviderTypeId")
  Encounters              ADM_Encounters          @relation(fields: [EncountersId], references: [Id], onUpdate: NoAction, map: "FK_ADM_EncounterProviders_EncountersId")
  Providers               Providers               @relation(fields: [ProvidersId], references: [Id], onUpdate: NoAction, map: "FK_ADM_EncounterProviders_ProvidersId")

  @@index([EncountersId], map: "idx_adm_encounterproviders_encounterid_include_encounterprovidertypeid")
  @@index([IsActive], map: "idx_adm_encounterproviders_isactive_include_encountersid_providersid_encounterprovidertypeid")
  @@index([SourcePrimaryKey], map: "idx_ADM_EncounterProviders_SourcePrimarKey")
  @@index([ProvidersId, IsActive], map: "idx_encounterproviders_providerid_isactive_include_encountersid_encounterprovidertypeid")
  @@ignore
  @@schema("ADM")
}

model EncounterProviderTaxonomyCodes {
  Id                BigInt  @id(map: "PK_CSRC_EncounterProviderTaxonomyCodes") @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@schema("CSRC")
}

model EncounterProviderTypes {
  Id                 BigInt               @id(map: "PK_CSRC_EncounterProviderTypes") @default(autoincrement())
  SourceCode         String               @db.NVarChar(500)
  SourceDescription  String?              @db.NVarChar(500)
  SourceCodeSystem   String?              @db.NVarChar(500)
  DataSourceId       Int
  EhrTypeId          Int?
  EhrInstance        Int?
  EncounterProviders EncounterProviders[] @ignore
  EntityProviders    EntityProviders[]

  @@schema("CSRC")
}

model EncounterRegistrationClass {
  Id                BigInt           @id(map: "PK_CSRC_EncounterRegistrationClass") @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Encounters        ADM_Encounters[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ADM_Encounters {
  Id                                     BigInt                                   @id(clustered: false, map: "PK_ADM_Encounters") @default(autoincrement())
  SourceEncounterIdentifier              String                                   @db.VarChar(255)
  CaseIdentifier                         String                                   @db.VarChar(255)
  PatientsId                             BigInt
  CCN                                    String?                                  @db.VarChar(6)
  NewbornEncounterIndicator              Boolean?
  ExpiredDateTime                        DateTime?                                @db.DateTime
  IsAdmittedFromEd                       Boolean?
  DecisionToAdmitDateTime                DateTime?                                @db.DateTime
  DecisionToAdmitCodeId                  BigInt?
  AdmitSourceCodeId                      BigInt?
  AdmitPriorityCodeId                    BigInt?
  AdmitServiceCodeId                     BigInt?
  DischargeDispositionCodeId             BigInt?
  EncounterRegistrationClassId           BigInt?
  EncounterBillingClassId                BigInt?
  TotalCharges                           Float?                                   @db.Money
  DataSourceId                           Int
  AuthorDateTime                         DateTime?                                @db.DateTime
  SourcePrimaryKey                       String?                                  @db.VarChar(255)
  IsActive                               Boolean?
  HashValue                              Bytes?                                   @db.Binary(16)
  EhrTypeId                              Int?
  EhrInstance                            Int?
  BilledCodes                            ADM_BilledCodes[]
  EncorAEncounters                       EncorAEncounters[]
  EncounterConditions                    EncounterConditions[]
  EncounterDetails                       EncounterDetails[]
  EncounterDiagnosisRelatedGroup         EncounterDiagnosisRelatedGroup[]
  EncounterProviders                     EncounterProviders[]
  EncounterAdmitPriorityCodes            EncounterAdmitPriorityCodes?             @relation(fields: [AdmitPriorityCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Encounters_AdmitPriorityCodeId")
  EncounterAdmitServiceCodes             EncounterAdmitServiceCodes?              @relation(fields: [AdmitServiceCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Encounters_AdmitServiceCodeId")
  EncounterAdmitSourceCodes              EncounterAdmitSourceCodes?               @relation(fields: [AdmitSourceCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Encounters_AdmitSourceCodeId")
  EncounterDecisionToAdmitCodes          EncounterDecisionToAdmitCodes?           @relation(fields: [DecisionToAdmitCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Encounters_DecisionToAdmitCodeId")
  EncounterDischargeDispositionCodes     EncounterDischargeDispositionCodes?      @relation(fields: [DischargeDispositionCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Encounters_DischargeDispositionCodeId")
  EncounterBillingClass                  EncounterBillingClass?                   @relation(fields: [EncounterBillingClassId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Encounters_EncounterBillingClassId")
  EncounterRegistrationClass             EncounterRegistrationClass?              @relation(fields: [EncounterRegistrationClassId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Encounters_EncounterRegistrationClassId")
  Patients                               Patients                                 @relation(fields: [PatientsId], references: [Id], onUpdate: NoAction, map: "FK_ADM_Encounters_PatientsId")
  Insurances                             Insurances[]                             @ignore
  LabTests                               LabTests[]
  LocationHistory                        ADM_LocationHistory[]
  MeasureCategoryAssignments             MeasureCategoryAssignments[]
  Medications                            Medications[]
  Procedures                             ADM_Procedures[]
  RiskAdjustedMeasureCategoryAssignments RiskAdjustedMeasureCategoryAssignments[]
  Vitals                                 ADM_Vitals[]

  @@map("Encounters")
  @@ignore
  @@schema("ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RISK_Encounters {
  Id                        BigInt                    @id @default(autoincrement())
  SourceEncounterIdentifier String                    @db.NVarChar(100)
  SourcePatientIdentifier   String                    @db.NVarChar(100)
  CCN                       String                    @db.NVarChar(6)
  Gender                    String?                   @db.NVarChar(1)
  Age                       Float?
  AdmitDateTime             DateTime?                 @db.DateTime
  DischargeDateTime         DateTime?                 @db.DateTime
  DischargeDispositionCode  String?                   @db.NVarChar(2)
  AdmitSource               String?                   @db.NVarChar(5)
  TotalCharges              Float?                    @db.Money
  DataSource                String?                   @db.NVarChar(50)
  LastUpdateDateTime        DateTime?                 @default(now(), map: "DF__Encounter__LastU__7928F116") @db.DateTime
  MSDRG                     String?                   @db.VarChar(5)
  MedicareFFS               Boolean?
  AdmitType                 String?                   @db.VarChar(5)
  AdmitService              String?                   @db.VarChar(5)
  SourcePrimaryKey          String?                   @db.VarChar(255)
  Charges                   Charges[]
  Diagnoses                 Diagnoses[]
  EncounterFeatures         EncounterFeatures[]
  EncounterMeasureResults   EncounterMeasureResults[]
  Labs                      Labs[]
  LocationHistory           RISK_LocationHistory[]
  Payers                    Payers[]
  Results                   Results[]
  Vitals                    RISK_Vitals[]

  @@map("Encounters")
  @@ignore
  @@schema("RISK")
}

model Entities {
  Id                        BigInt                    @id(map: "PK__Entities__3214EC0755D2DD16") @default(autoincrement())
  Code                      String                    @db.VarChar(100)
  Description               String?                   @db.VarChar(255)
  SourceContainerIdentifier String?                   @db.NVarChar(255)
  EntityTypeId              Int?                      @db.SmallInt
  OrganizationTypeId        Int?                      @db.SmallInt
  UseForComparativeData     Boolean?
  ActiveMeasures            ActiveMeasures[]
  ControlChartSummary       ControlChartSummary[]
  EntityTypes               EntityTypes?              @relation(fields: [EntityTypeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__Entities__Entity__5887175A")
  OrganizationTypes         OrganizationTypes?        @relation(fields: [OrganizationTypeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__Entities__Organi__5792F321")
  EntityFacilities          EntityFacilities[]
  EntityGroup               EntityGroup[]
  EntityPercentileSources   EntityPercentileSources[]
  EntityPeriods             EntityPeriods[]
  EntityProviders           EntityProviders[]
  MeasureSummary            MECA_MeasureSummary[]
  PerformanceGoals          PerformanceGoals[]

  @@schema("MECA")
}

model EntityFacilities {
  Id                  Int         @id(map: "PK__EntityFa__3214EC0769CE62BC") @default(autoincrement())
  EntitiesId          BigInt?
  FacilitiesId        BigInt?
  EntityStartDateTime DateTime?
  EntityEndDateTime   DateTime?
  Entities            Entities?   @relation(fields: [EntitiesId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__EntityFac__Entit__65E11278")
  Facilities          Facilities? @relation(fields: [FacilitiesId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__EntityFac__Facil__66D536B1")

  @@unique([EntitiesId, FacilitiesId, EntityStartDateTime, EntityEndDateTime], map: "UQ_Entity_Facility_Id")
  @@schema("MECA")
}

model EntityGroup {
  Id                 Int                @id(map: "PK__EntityGr__3214EC07D0756BFC") @default(autoincrement())
  GroupId            Int
  EntityId           BigInt
  Entities           Entities           @relation(fields: [EntityId], references: [Id], onUpdate: NoAction, map: "FK__EntityGro__Entit__5B638405")
  EntityMeasureGroup EntityMeasureGroup @relation(fields: [GroupId], references: [GroupId], onUpdate: NoAction, map: "FK__EntityGro__Group__5D4BCC77")

  @@unique([GroupId, EntityId], map: "UQ_GroupId_EntityId")
  @@schema("MECA")
}

model EntityMeasureGroup {
  GroupId         Int            @id(map: "PK__EntityMe__149AF36ACCD9A237") @default(autoincrement())
  GroupName       String         @db.VarChar(255)
  Active          Boolean
  UserEmail       String?        @db.VarChar(500)
  CreatedByName   String?        @db.VarChar(500)
  CreatedDateTime DateTime?
  DefaultGroup    Boolean?
  EntityGroup     EntityGroup[]
  MeasureGroup    MeasureGroup[]

  @@schema("MECA")
}

model EntityPercentileSources {
  Id                  Int                @id(map: "PK__EntityPe__3214EC07F3790AB2") @default(autoincrement())
  EntitiesId          BigInt?
  PercentileSourcesId Int?               @db.SmallInt
  Entities            Entities?          @relation(fields: [EntitiesId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__EntityPer__Entit__77FFC2B3")
  PercentileSources   PercentileSources? @relation(fields: [PercentileSourcesId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__EntityPer__Perce__770B9E7A")

  @@unique([EntitiesId, PercentileSourcesId], map: "UQ_Entity_PercentileSource_Id")
  @@schema("MECA")
}

model EntityPeriods {
  Id                  Int       @id(map: "PK__EntityPe__3214EC07902ECC92") @default(autoincrement())
  EntitiesId          BigInt?
  EntityStartDateTime DateTime
  EntityEndDateTime   DateTime
  Entities            Entities? @relation(fields: [EntitiesId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__EntityPer__Entit__78F3E6EC")

  @@index([EntitiesId], map: "idx_EntitiesId")
  @@schema("MECA")
}

model EntityProviders {
  Id                     Int                     @id(map: "PK__EntityPr__3214EC070D7D32BF") @default(autoincrement())
  EntitiesId             BigInt?
  ProvidersId            BigInt?
  ProviderTypeId         BigInt?
  EntityStartDateTime    DateTime?
  EntityEndDateTime      DateTime?
  Entities               Entities?               @relation(fields: [EntitiesId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__EntityPro__Entit__63F8CA06")
  Providers              Providers?              @relation(fields: [ProvidersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__EntityPro__Provi__62108194") @ignore
  EncounterProviderTypes EncounterProviderTypes? @relation(fields: [ProviderTypeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__EntityPro__Provi__6304A5CD")

  @@unique([EntitiesId, ProvidersId, ProviderTypeId, EntityStartDateTime, EntityEndDateTime], map: "UQ_Entity_Provider_ProviderType_Id")
  @@schema("MECA")
}

model EntityTypes {
  Id          Int        @id(map: "PK__EntityTy__3214EC07C8C59BC3") @default(autoincrement()) @db.SmallInt
  Code        String     @unique(map: "UQ_MECA_EntityType_Code") @db.NVarChar(100)
  Description String?    @db.NVarChar(255)
  Entities    Entities[]

  @@schema("MECA")
}

model Facilities {
  Id                      BigInt             @id(map: "PK_CSRC_Facility") @default(autoincrement())
  SourceCode              String             @db.NVarChar(500)
  SourceDescription       String?            @db.NVarChar(500)
  SourceFacilityAddress1  String?            @db.VarChar(100)
  SourceFacilityAddress2  String?            @db.VarChar(100)
  SourceFacilityCity      String?            @db.VarChar(100)
  SourceFacilityState     String?            @db.VarChar(100)
  SourceFacilityZipPostal String?            @db.VarChar(100)
  CCN                     String?            @db.VarChar(6)
  DataSourceId            Int
  EhrTypeId               Int?
  EhrInstance             Int?
  EncounterDetails        EncounterDetails[] @ignore
  EntityFacilities        EntityFacilities[]
  Procedures              ADM_Procedures[]   @ignore

  @@schema("CSRC")
}

model FeatureCohort {
  Id        Int  @id(map: "PK_FeatureCohort") @default(autoincrement())
  FeatureId Int?
  CohortId  Int?

  @@schema("RISK")
}

model FeatureDiagnosisMap {
  Id                  Int      @id(map: "PK_RISK_FeatureDiagnosisMap_Id") @default(autoincrement())
  FeatureId           Int
  DiagnosisCode       String?  @db.VarChar(10)
  PrincipalDiagnosis  Int?     @db.TinyInt
  SecondaryDiagnosis  Int?     @db.TinyInt
  HistoricalDiagnosis Int?     @db.TinyInt
  Features            Features @relation(fields: [FeatureId], references: [Id], onUpdate: NoAction, map: "FK__FeatureDi__Featu__0FD74C44")

  @@schema("RISK")
}

model FeatureProcedureMap {
  Id                  Int      @id(map: "PK_RISK_FeatureProcedureMap_Id") @default(autoincrement())
  FeatureId           Int
  ProcedureCode       String   @db.VarChar(10)
  PrincipalProcedure  Int?     @db.TinyInt
  SecondaryProcedure  Int?     @db.TinyInt
  HistoricalProcedure Int?     @db.TinyInt
  Features            Features @relation(fields: [FeatureId], references: [Id], onUpdate: NoAction, map: "FK__FeaturePr__Featu__0B129727")

  @@schema("RISK")
}

model Features {
  Id                        Int                         @id(map: "PK_RISK_Features_Id") @default(autoincrement())
  FeatureName               String                      @db.VarChar(255)
  Description               String                      @db.NVarChar(400)
  RiskModelId               Int
  DefinedByCodes            Boolean?
  EncounterFeatures         EncounterFeatures[]
  FeatureDiagnosisMap       FeatureDiagnosisMap[]
  FeatureProcedureMap       FeatureProcedureMap[]
  RiskModels                RiskModels                  @relation(fields: [RiskModelId], references: [Id], onUpdate: NoAction, map: "FK__Features__RiskMo__0A1E72EE")
  RiskModelSelectedFeatures RiskModelSelectedFeatures[]

  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Insurances {
  Id                            BigInt                         @default(autoincrement())
  PatientsId                    BigInt
  EncountersId                  BigInt?
  SourceOfPaymentCodeId         BigInt?
  Ordinality                    String?                        @db.VarChar(25)
  PolicyNumber                  String?                        @db.VarChar(255)
  HICNumber                     String?                        @db.VarChar(255)
  CarrierName                   String?                        @db.VarChar(255)
  EffectiveDateTime             DateTime?                      @db.DateTime
  ExpirationDateTime            DateTime?                      @db.DateTime
  DataSourceId                  Int
  SourcePrimaryKey              String?                        @db.VarChar(500)
  IsActive                      Boolean?
  HashValue                     Bytes?                         @db.Binary(16)
  EhrTypeId                     Int?
  EhrInstance                   Int?
  Encounters                    ADM_Encounters?                @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Insurances_EncountersId")
  Patients                      Patients                       @relation(fields: [PatientsId], references: [Id], onUpdate: NoAction, map: "FK_ADM_Insurances_PatientsId")
  InsuranceSourceOfPaymentCodes InsuranceSourceOfPaymentCodes? @relation(fields: [SourceOfPaymentCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Insurances_SourceOfPaymentCodeId")

  @@index([SourcePrimaryKey], map: "idx_ADM_Insurances_SourcePrimarKey")
  @@ignore
  @@schema("ADM")
}

model InsuranceSourceOfPaymentCodes {
  Id                BigInt       @id(map: "PK_CSRC_InsuranceSopcodes") @default(autoincrement())
  SourceCode        String       @db.NVarChar(500)
  SourceDescription String?      @db.NVarChar(500)
  SourceCodeSystem  String?      @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Insurances        Insurances[] @ignore

  @@schema("CSRC")
}

model Labs {
  Id                   BigInt          @id(map: "PK__Labs__3214EC07FB1A8E2D") @default(autoincrement())
  EncounterId          BigInt
  TestId               String?         @db.NVarChar(100)
  LabType              String          @db.NVarChar(50)
  ResultDateTime       DateTime?       @db.DateTime
  Result               Float?
  NormalRangeLow       Float?
  NormalRangeHigh      Float?
  Bin                  Int?
  EarliestWithinWindow Boolean?
  Encounters           RISK_Encounters @relation(fields: [EncounterId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__Labs__EncounterI__04659998") @ignore

  @@schema("RISK")
}

model LabTestCodes {
  Id                BigInt     @id(map: "PK_CSRC_LabTestCodes") @default(autoincrement())
  SourceCode        String     @db.NVarChar(500)
  SourceDescription String?    @db.NVarChar(500)
  SourceCodeSystem  String?    @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  LabTests          LabTests[] @ignore

  @@schema("CSRC")
}

model LabTestNegationCodes {
  Id                BigInt     @id(map: "PK_CSRC_LabTestNegationCodes") @default(autoincrement())
  SourceCode        String     @db.NVarChar(500)
  SourceDescription String?    @db.NVarChar(500)
  SourceCodeSystem  String?    @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  LabTests          LabTests[] @ignore

  @@schema("CSRC")
}

model LabTestReasonCodes {
  Id                BigInt     @id(map: "PK_CSRC_LabTestReasonCodes") @default(autoincrement())
  SourceCode        String     @db.NVarChar(500)
  SourceDescription String?    @db.NVarChar(500)
  SourceCodeSystem  String?    @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  LabTests          LabTests[] @ignore

  @@schema("CSRC")
}

model LabTestResultCodes {
  Id                BigInt     @id(map: "PK_CSRC_LabTestResultCodes") @default(autoincrement())
  SourceCode        String     @db.NVarChar(500)
  SourceDescription String?    @db.NVarChar(500)
  SourceCodeSystem  String?    @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  LabTests          LabTests[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model LabTests {
  Id                                                BigInt                @default(autoincrement())
  PatientsId                                        BigInt
  EncountersId                                      BigInt?
  SpecimensId                                       BigInt?
  OrdersId                                          BigInt?
  OrderingProviderId                                BigInt?
  LabTestCodeId                                     BigInt?
  LabTestOrderedDateTime                            DateTime?             @db.DateTime
  LabTestPerformedDateTime                          DateTime?             @db.DateTime
  SourceLabTestResult                               String?               @db.VarChar(1000)
  SourceLabTestResultUnits                          String?               @db.VarChar(255)
  LabTestStatusId                                   BigInt?
  NegationCodeId                                    BigInt?
  DataSourceId                                      Int
  LabTestMethod                                     String?               @db.VarChar(255)
  AuthorDateTime                                    DateTime?             @db.DateTime
  LabReferenceRangeLow                              String?               @db.VarChar(500)
  LabReferenceRangeHigh                             String?               @db.VarChar(500)
  LabTestStartDateTime                              DateTime?             @db.DateTime
  LabTestEndDateTime                                DateTime?             @db.DateTime
  LabTestResultDateTime                             DateTime?             @db.DateTime
  RenderingProviderId                               BigInt?
  SourcePrimaryKey                                  String?               @db.VarChar(500)
  LabTestReasonCodeId                               BigInt?
  LabTestResultCodeId                               BigInt?
  IsActive                                          Boolean?
  HashValue                                         Bytes?                @db.Binary(16)
  EhrTypeId                                         Int?
  EhrInstance                                       Int?
  Encounters                                        ADM_Encounters?       @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LabTests_EncountersId")
  LabTestCodes                                      LabTestCodes?         @relation(fields: [LabTestCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LabTests_LabTestCodeId")
  LabTestReasonCodes                                LabTestReasonCodes?   @relation(fields: [LabTestReasonCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LabTests_LabTestReasonCodeId")
  LabTestResultCodes                                LabTestResultCodes?   @relation(fields: [LabTestResultCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LabTests_LabTestResultCodeId")
  LabTestStatus                                     LabTestStatus?        @relation(fields: [LabTestStatusId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LabTests_LabTestStatusId")
  LabTestNegationCodes                              LabTestNegationCodes? @relation(fields: [NegationCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LabTests_NegationCodeId")
  Providers_LabTests_OrderingProviderIdToProviders  Providers?            @relation("LabTests_OrderingProviderIdToProviders", fields: [OrderingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LabTests_OrderingProviderId")
  Patients                                          Patients              @relation(fields: [PatientsId], references: [Id], onUpdate: NoAction, map: "FK_ADM_LabTests_PatientsId")
  Providers_LabTests_RenderingProviderIdToProviders Providers?            @relation("LabTests_RenderingProviderIdToProviders", fields: [RenderingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LabTests_RenderingProviderId")

  @@index([EncountersId], map: "idx_adm_labtests_encountersid")
  @@index([SourcePrimaryKey], map: "idx_ADM_LabTests_SourcePrimarKey")
  @@index([LabTestCodeId], map: "idx_adm_vitals_labcodeid_include_encountersid")
  @@ignore
  @@schema("ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model LabTestStatus {
  Id                BigInt     @id @default(autoincrement())
  SourceCode        String     @db.NVarChar(500)
  SourceDescription String?    @db.NVarChar(500)
  SourceCodeSystem  String?    @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  LabTests          LabTests[]

  @@ignore
  @@schema("CSRC")
}

model LabVitalBins {
  Id            Int    @id(map: "PK_RISK_LabVitalBins_Id") @default(autoincrement())
  Name          String @db.NVarChar(50)
  Category      Int
  LowerType     String @db.NVarChar(50)
  LowerValue    Float?
  LowerOperator String @db.NVarChar(50)
  UpperType     String @db.NVarChar(50)
  UpperValue    Float?
  UpperOperator String @db.NVarChar(50)

  @@schema("RISK")
}

model LabVitalThresholds {
  Id            Int        @id(map: "PK_RISK_LabVitalThresholds_Id") @default(autoincrement())
  Name          String     @db.NVarChar(50)
  Category      Int
  LowerType     String     @db.NVarChar(50)
  LowerValue    Float?
  LowerOperator String     @db.NVarChar(50)
  UpperType     String     @db.NVarChar(50)
  UpperValue    Float?
  UpperOperator String     @db.NVarChar(50)
  RiskModelId   Int
  RiskModels    RiskModels @relation(fields: [RiskModelId], references: [Id], onUpdate: NoAction, map: "FK__LabVitalT__RiskM__0CFADF99")

  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RISK_LocationHistory {
  Id                   BigInt          @default(autoincrement())
  EncounterId          BigInt
  EventDateTime        DateTime        @db.DateTime
  LocationId           String          @db.NVarChar(100)
  RoomRateAccomodation String?         @db.NVarChar(100)
  Room                 String?         @db.NVarChar(50)
  Bed                  String?         @db.NVarChar(50)
  Encounters           RISK_Encounters @relation(fields: [EncounterId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__LocationH__Encou__0C06BB60")

  @@map("LocationHistory")
  @@ignore
  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ADM_LocationHistory {
  Id                    BigInt          @default(autoincrement())
  PatientsId            BigInt
  EncountersId          BigInt?
  LocationId            BigInt?
  TransferStartDateTime DateTime?       @db.DateTime
  TransferEndDateTime   DateTime?       @db.DateTime
  DataSourceId          Int
  SourcePrimaryKey      String?         @db.VarChar(500)
  IsActive              Boolean?
  HashValue             Bytes?          @db.Binary(16)
  EhrTypeId             Int?
  EhrInstance           Int?
  Encounters            ADM_Encounters? @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LocationHistory_EncountersId")
  Locations             Locations?      @relation(fields: [LocationId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_LocationHistory_LocationId")
  Patients              Patients        @relation(fields: [PatientsId], references: [Id], onUpdate: NoAction, map: "FK_ADM_LocationHistory_PatientsId")

  @@index([EncountersId], map: "idx_adm_encounters_locationhistory_encounterid")
  @@index([SourcePrimaryKey], map: "idx_ADM_LocationHistory_SourcePrimarKey")
  @@map("LocationHistory")
  @@ignore
  @@schema("ADM")
}

model Locations {
  Id                BigInt                @id(map: "PK_CSRC_Location") @default(autoincrement())
  SourceCode        String                @db.NVarChar(500)
  SourceDescription String?               @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  EncounterDetails  EncounterDetails[]    @ignore
  LocationHistory   ADM_LocationHistory[] @ignore

  @@schema("CSRC")
}

model ManageReports {
  Id             Int      @id(map: "PK_ManageReport") @default(autoincrement())
  OrganizationId String   @db.UniqueIdentifier
  EntityId       BigInt?
  ReportId       String?  @db.UniqueIdentifier
  IsActive       Boolean?

  @@schema("MECA")
}

model ManageUserRoles {
  Id             Int      @id(map: "PK_ManageUserRoles") @default(autoincrement())
  OrganizationId String   @db.UniqueIdentifier
  UserId         String   @db.VarChar(255)
  EntitiesId     BigInt
  CanAccess      Boolean?
  CanDrillDown   Boolean?

  @@schema("MECA")
}

model MeasureAttributes {
  Id            Int     @id(map: "PK__MeasureA__3214EC07C776406A") @default(autoincrement())
  AttributeName String? @db.NVarChar(100)

  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MeasureCategories {
  Id                  Int     @default(autoincrement())
  MeasureCategory     String  @db.NVarChar(200)
  CategoryDescription String? @db.NVarChar(200)

  @@ignore
  @@schema("RISK")
}

model MeasureCategoryAssignmentProviders {
  Id                           Int                         @id(map: "PK__MeasureC__3214EC07D714FCC6") @default(autoincrement())
  MeasureCategoryAssignmentsId BigInt?
  ProvidersId                  BigInt?
  MeasureCategoryAssignments   MeasureCategoryAssignments? @relation(fields: [MeasureCategoryAssignmentsId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__MeasureCa__Measu__75235608")
  Providers                    Providers?                  @relation(fields: [ProvidersId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__MeasureCa__Provi__76177A41") @ignore

  @@unique([MeasureCategoryAssignmentsId, ProvidersId], map: "UQ_MeasureCategoryaAssignments_Providers_Id")
  @@schema("MECA")
}

model MeasureCategoryAssignments {
  Id                                 BigInt                               @id(map: "PK__MeasureC__3214EC07730E7B6E") @default(autoincrement())
  SourceContainerIdentifier          String?                              @db.NVarChar(255)
  MeasureGUID                        String?                              @db.UniqueIdentifier
  MeasureSubId                       String?                              @db.NVarChar(20)
  PatientsId                         BigInt?
  EncountersId                       BigInt?
  MeasureTallyIdentifier             String?                              @db.NVarChar(255)
  ReferenceDate                      DateTime?                            @db.DateTime
  CategoryAssignmentsId              Int                                  @db.SmallInt
  NumeratorValue                     Decimal?                             @db.Decimal(18, 10)
  DenominatorValue                   Decimal?                             @db.Decimal(18, 10)
  Note                               String?                              @db.NVarChar(500)
  IsGenerated                        Boolean?
  MeasureCategoryAssignmentProviders MeasureCategoryAssignmentProviders[]
  CategoryAssignments                MECA_CategoryAssignments             @relation(fields: [CategoryAssignmentsId], references: [Id], onUpdate: NoAction, map: "FK__MeasureCa__Categ__611C5D5B")
  Encounters                         ADM_Encounters?                      @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_MECA_MCA_EncountersId") @ignore
  Patients                           Patients?                            @relation(fields: [PatientsId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_MECA_MCA_PatientsId") @ignore

  @@index([SourceContainerIdentifier, MeasureGUID], map: "idx_meca_mca_stg_merge")
  @@index([CategoryAssignmentsId], map: "idx_meca_measure_categoryassignments_all")
  @@index([EncountersId], map: "idx_meca_measurecategoryassignments_encountersid_include_all")
  @@index([MeasureGUID, ReferenceDate], map: "idx_meca_measurecategoryassignments_guid_refdate_plus_includes")
  @@index([MeasureTallyIdentifier], map: "idx_meca_measurecategoryassignments_measuretallyidentifier_include_all")
  @@index([MeasureGUID, ReferenceDate], map: "nci_wi_MeasureCategoryAssignments_D417652ACAC8AFF086372D3F0C699FCA")
  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MeasureCategoryAssignments_bkp {
  Id                        BigInt    @default(autoincrement())
  SourceContainerIdentifier String?   @db.NVarChar(255)
  MeasureGUID               String?   @db.UniqueIdentifier
  MeasureSubId              String?   @db.NVarChar(20)
  PatientsId                BigInt?
  EncountersId              BigInt?
  MeasureTallyIdentifier    String?   @db.NVarChar(255)
  ReferenceDate             DateTime? @db.DateTime
  CategoryAssignmentsId     Int       @db.SmallInt
  NumeratorValue            Decimal?  @db.Decimal(18, 10)
  DenominatorValue          Decimal?  @db.Decimal(18, 10)
  Note                      String?   @db.NVarChar(500)
  IsGenerated               Boolean?

  @@ignore
  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MeasureCategoryAssignments_BKP_06_24_2024 {
  Id                        BigInt    @default(autoincrement())
  SourceContainerIdentifier String?   @db.NVarChar(255)
  MeasureGUID               String?   @db.UniqueIdentifier
  MeasureSubId              String?   @db.NVarChar(20)
  PatientsId                BigInt?
  EncountersId              BigInt?
  MeasureTallyIdentifier    String?   @db.NVarChar(255)
  ReferenceDate             DateTime? @db.DateTime
  CategoryAssignmentsId     Int       @db.SmallInt
  NumeratorValue            Decimal?  @db.Decimal(18, 10)
  DenominatorValue          Decimal?  @db.Decimal(18, 10)
  Note                      String?   @db.NVarChar(500)
  IsGenerated               Boolean?

  @@ignore
  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MeasureEAV {
  Id          Int     @default(autoincrement())
  EncounterId BigInt
  AttributeId Int?
  Value       String? @db.NVarChar(100)
  ProgramYear Int?

  @@ignore
  @@schema("RISK")
}

model MeasureGroup {
  Id                 Int                @id(map: "PK__MeasureG__3214EC0713800845") @default(autoincrement())
  GroupId            Int
  MeasureGuid        String             @db.VarChar(255)
  PercentileSourceId Int                @db.SmallInt
  EntityMeasureGroup EntityMeasureGroup @relation(fields: [GroupId], references: [GroupId], onUpdate: NoAction, map: "FK__MeasureGr__Group__6D823440")
  PercentileSources  PercentileSources  @relation(fields: [PercentileSourceId], references: [Id], onUpdate: NoAction, map: "FK__MeasureGr__Perce__6F6A7CB2")

  @@unique([GroupId, MeasureGuid, PercentileSourceId], map: "UQ_GroupId_MeasureGuid_PercentileSourceId")
  @@schema("MECA")
}

model MeasureModels {
  Id          Int           @id(map: "PK_MeasureModels_Id") @default(autoincrement())
  MeasureId   Int
  RiskModelId Int
  Measures    RISK_Measures @relation(fields: [MeasureId], references: [Id], onUpdate: NoAction, map: "FK_MeasureModels_MeasureId")
  RiskModels  RiskModels    @relation(fields: [RiskModelId], references: [Id], onUpdate: NoAction, map: "FK_MeasureModels_RiskModelId")

  @@schema("RISK")
}

model MECA_Measures {
  Id                         Int       @id(map: "PK__Measures__3214EC07C91ACDB1") @default(autoincrement())
  MedisolvMeasureId          String    @db.UniqueIdentifier
  DenominatorQualifyingType  String    @db.VarChar(255)
  StratifcationDescription   String?   @db.VarChar(500)
  Stratification             Boolean?
  MeasureSubId               String?   @db.VarChar(255)
  MeasureScoring             String?   @db.VarChar(255)
  InverseMeasureFlag         Boolean?
  MedisolvApplication        String?   @db.VarChar(255)
  DataCompletenessRequired   Boolean?
  DataCompletenessThreshold  Float?
  CalculationFactor          String?   @db.VarChar(255)
  DecimalPointsCount         Float?
  Name                       String?   @db.NVarChar(255)
  EquityStrata               String?   @db.NVarChar(255)
  Cohort                     String?   @db.VarChar(255)
  Outcome                    String?   @db.VarChar(255)
  EncounterType              String?   @db.VarChar(255)
  MeasureType                String?   @db.VarChar(255)
  HighIsGood                 Boolean?  @default(false, map: "DF__Measures__HighIs__6F9F86DC")
  IgnoreMinimumCaseThreshold Boolean?  @default(false, map: "DF__Measures__Ignore__7187CF4E")
  LastUpdateDateTime         DateTime? @db.DateTime
  IsActive                   Boolean?  @default(false, map: "DF__Measures__IsActi__7093AB15")
  StoredProcedureName        String?   @db.NVarChar(200)
  SmallestInterval           String    @db.VarChar(50)
  LongMeasureName            String?   @db.VarChar(1000)
  CompositeMeasCNT           Int?
  NullRate                   Boolean?
  NullIPP                    Boolean?
  NullDenomOnly              Boolean?
  NullDenExcl                Boolean?
  NullNumerator              Boolean?
  NullDenominator            Boolean?
  NullException              Boolean?
  NullNumExcl                Boolean?
  NoDrill                    Boolean?
  CompositeType              String?   @db.VarChar(50)

  @@index([LongMeasureName], map: "idx_longmeasurename")
  @@map("Measures")
  @@schema("MECA")
}

model RISK_Measures {
  Id                         Int                       @id(map: "PK_Measures_Id") @default(autoincrement())
  Name                       String                    @db.NVarChar(200)
  MedisolvMeasureId          String?                   @db.UniqueIdentifier
  CohortId                   Int
  OutcomeId                  Int
  EncounterTypeId            Int                       @db.SmallInt
  MeasureTypeId              Int                       @db.SmallInt
  HighIsGood                 Boolean?                  @default(false, map: "DF__Measures__HighIs__764C846B")
  IgnoreMinimumCaseThreshold Boolean?                  @default(false, map: "DF__Measures__Ignore__7834CCDD")
  LastUpdateDateTime         DateTime?                 @db.DateTime
  IsActive                   Boolean?                  @default(false, map: "DF__Measures__IsActi__7740A8A4")
  StoredProcedureName        String?                   @db.NVarChar(200)
  Medicare                   Boolean?
  CMS                        Boolean?
  ProgramYearId              Int?
  MeasureCategoryId          Int?
  EncounterMeasureResults    EncounterMeasureResults[]
  MeasureModels              MeasureModels[]
  MeasureTypes               MeasureTypes              @relation(fields: [MeasureTypeId], references: [Id], onUpdate: NoAction, map: "FK_Measures_MeasureTypeId")
  Outcomes                   Outcomes                  @relation(fields: [OutcomeId], references: [Id], onUpdate: NoAction, map: "FK_Measures_OutcomeId")
  MeasureStandardCodes       MeasureStandardCodes[]
  RiskModels                 RiskModels[]

  @@map("Measures")
  @@schema("RISK")
}

model MeasureStandardCodes {
  Id           BigInt        @id(map: "PK_MeasureCodes") @default(autoincrement())
  MeasureId    Int
  StandardCode String        @db.VarChar(100)
  IsInclusion  Boolean
  Measures     RISK_Measures @relation(fields: [MeasureId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__MeasureSt__Measu__0559BDD1")

  @@schema("RISK")
}

model MECA_MeasureSummary {
  Id                         BigInt                            @id(map: "PK__MeasureS__3214EC072E8F06B0") @default(autoincrement())
  MeasureGUID                String                            @db.UniqueIdentifier
  MeasureSubId               String?                           @db.NVarChar(20)
  EntitiesId                 BigInt?
  StartDate                  DateTime                          @db.DateTime
  EndDate                    DateTime                          @db.DateTime
  Period                     String                            @db.NVarChar(5)
  Numerator                  Float?
  Denominator                Float?
  DenominatorOnly            Float?
  NumeratorExclusion         Float?
  DenominatorExclusion       Float?
  DenominatorException       Float?
  Performance                Float?
  PerformanceRaw             Float?
  NumeratorValue             Float?
  DenominatorValue           Float?
  IncompleteCases            Float?
  IPP                        Float?
  StringResult               String?                           @db.NVarChar(255)
  HoverText                  String?                           @db.NVarChar(Max)
  ControlChartDetails        ControlChartDetails[]
  Entities                   Entities?                         @relation(fields: [EntitiesId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__MeasureSu__Entit__69B1A35C")
  MeasureSummaryPercentiles  MeasureSummaryPercentiles[]
  RiskAdjustedMeasureSummary MECA_RiskAdjustedMeasureSummary[]

  @@map("MeasureSummary")
  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RISK_MeasureSummary {
  Id                         BigInt                            @id(map: "Pk_RISK_MeasureSummary") @default(autoincrement())
  MeasureGUID                String                            @db.UniqueIdentifier
  CCN                        String                            @db.NVarChar(6)
  StartDate                  DateTime                          @db.DateTime
  EndDate                    DateTime                          @db.DateTime
  Period                     String                            @db.NVarChar(5)
  Numerator                  Float?
  Denominator                Float?
  NumeratorExclusion         Float?
  DenominatorExclusion       Float?
  DenominatorException       Float?
  Percentile                 Float?
  RiskAdjustedMeasureSummary RISK_RiskAdjustedMeasureSummary[]

  @@map("MeasureSummary")
  @@ignore
  @@schema("RISK")
}

model MeasureSummaryPercentiles {
  Id                 BigInt               @id(map: "PK__MeasureS__3214EC072C2BB1B8") @default(autoincrement())
  MeasureSummaryId   BigInt?
  Percentile         Float?
  PercentileSourceId Int?                 @db.SmallInt
  Points             Float?
  PointsRaw          Float?
  MeasureSummary     MECA_MeasureSummary? @relation(fields: [MeasureSummaryId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__MeasureSu__Measu__79E80B25") @ignore
  PercentileSources  PercentileSources?   @relation(fields: [PercentileSourceId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__MeasureSu__Perce__7ADC2F5E")

  @@schema("MECA")
}

model MeasureTypes {
  Id       Int             @id(map: "PK_MeasureTypes_Id") @default(autoincrement()) @db.SmallInt
  Type     String          @unique(map: "UQ_MeasureTypes_Type") @db.NVarChar(100)
  Measures RISK_Measures[]

  @@schema("RISK")
}

model MedicationCodes {
  Id                BigInt        @id(map: "PK_CSRC_MedicationCodes") @default(autoincrement())
  SourceCode        String        @db.NVarChar(500)
  SourceDescription String?       @db.NVarChar(500)
  SourceCodeSystem  String?       @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Medications       Medications[] @ignore

  @@schema("CSRC")
}

model MedicationNegationCodes {
  Id                BigInt        @id(map: "PK_CSRC_MedicationNegationCodes") @default(autoincrement())
  SourceCode        String        @db.NVarChar(500)
  SourceDescription String?       @db.NVarChar(500)
  SourceCodeSystem  String?       @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Medications       Medications[] @ignore

  @@schema("CSRC")
}

model MedicationRouteOfAdministrationCodes {
  Id                BigInt        @id(map: "PK_CSRC_MedicationRouteOfAdministrationCodes") @default(autoincrement())
  SourceCode        String        @db.NVarChar(500)
  SourceDescription String?       @db.NVarChar(500)
  SourceCodeSystem  String?       @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Medications       Medications[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Medications {
  Id                                                     BigInt                                @default(autoincrement())
  PatientsId                                             BigInt
  EncountersId                                           BigInt?
  SourceMedicationIdentifier                             String?                               @db.VarChar(255)
  OrdersId                                               BigInt?
  OrderingProviderId                                     BigInt?
  MedicationCodeId                                       BigInt?
  MedicationStatusId                                     BigInt?
  MedicationStartDateTime                                DateTime?                             @db.DateTime
  MedicationEndDateTime                                  DateTime?                             @db.DateTime
  MedicationRouteOfAdministrationId                      BigInt?
  SourceMedicationDosage                                 String?                               @db.VarChar(255)
  SourceMedicationDosageUnits                            String?                               @db.VarChar(255)
  IsAdministered                                         Boolean?
  NegationCodeId                                         BigInt?
  DataSourceId                                           Int
  MedicationFrequency                                    String?                               @db.VarChar(255)
  AuthorDateTime                                         DateTime?                             @db.DateTime
  MedicationReason                                       String?                               @db.VarChar(255)
  RenderingProviderId                                    BigInt?
  MedicationSupply                                       String?                               @db.VarChar(255)
  MedicationDaysSupplied                                 String?                               @db.VarChar(255)
  MediationRefills                                       String?                               @db.VarChar(255)
  PrescribingProviderId                                  BigInt?
  Recorder                                               String?                               @db.VarChar(255)
  MedicationCategory                                     String?                               @db.VarChar(255)
  SourcePrimaryKey                                       String?                               @db.VarChar(500)
  IsActive                                               Boolean?
  HashValue                                              Bytes?                                @db.Binary(16)
  EhrTypeId                                              Int?
  EhrInstance                                            Int?
  Encounters                                             ADM_Encounters?                       @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Medications_EncountersId")
  MedicationCodes                                        MedicationCodes?                      @relation(fields: [MedicationCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Medications_MedicationCodeId")
  MedicationRouteOfAdministrationCodes                   MedicationRouteOfAdministrationCodes? @relation(fields: [MedicationRouteOfAdministrationId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Medications_MedicationRouteOfAdministrationId")
  MedicationStatus                                       MedicationStatus?                     @relation(fields: [MedicationStatusId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Medications_MedicationStatusId")
  MedicationNegationCodes                                MedicationNegationCodes?              @relation(fields: [NegationCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Medications_NegationCodeId")
  Providers_Medications_OrderingProviderIdToProviders    Providers?                            @relation("Medications_OrderingProviderIdToProviders", fields: [OrderingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Medications_OrderingProviderId")
  Patients                                               Patients                              @relation(fields: [PatientsId], references: [Id], onUpdate: NoAction, map: "FK_ADM_Medications_PatientsId")
  Providers_Medications_PrescribingProviderIdToProviders Providers?                            @relation("Medications_PrescribingProviderIdToProviders", fields: [PrescribingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Medications_PrescribingProviderId")
  Providers_Medications_RenderingProviderIdToProviders   Providers?                            @relation("Medications_RenderingProviderIdToProviders", fields: [RenderingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Medications_RenderingProviderId")

  @@index([SourcePrimaryKey], map: "idx_ADM_Medications_SourcePrimarKey")
  @@ignore
  @@schema("ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MedicationStatus {
  Id                BigInt        @id @default(autoincrement())
  SourceCode        String        @db.NVarChar(500)
  SourceDescription String?       @db.NVarChar(500)
  SourceCodeSystem  String?       @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Medications       Medications[]

  @@ignore
  @@schema("CSRC")
}

model MedisolvPatientsXref {
  Id                       Int       @id(map: "PK__Medisolv__3214EC07D35F1ED0") @default(autoincrement())
  MedisolvPatientXrefPK    BigInt
  MedisolvPatientIndex     BigInt
  SourceNationalIdentifier String?   @db.VarChar(500)
  SourcePatientIdentifier  String    @db.VarChar(500)
  FirstName                String?   @db.VarChar(500)
  LastName                 String?   @db.VarChar(500)
  BirthDateTime            DateTime? @db.DateTime
  BirthSexCode             String?   @db.VarChar(50)
  EHRID                    Int?
  ValidFrom                DateTime  @db.DateTime
  ValidTo                  DateTime  @db.DateTime
  EHRInstance              Int?
  DuplicatePrimary         Int?
  DuplicatePatientHandling String?   @db.VarChar(255)
  DataSourceId             Int

  @@schema("ADM")
}

model Modules {
  Id          BigInt  @id(map: "PK__Modules__3214EC07171F1E30") @default(autoincrement())
  Code        String? @db.VarChar(100)
  Description String? @db.VarChar(500)

  @@schema("MECA")
}

model MECA_Mortality {
  Id                             Int     @id(map: "PK__Mortalit__3214EC07B8F1FAAA") @default(autoincrement())
  EntitiesId                     BigInt
  MeasureGUID                    String  @db.UniqueIdentifier
  MeasureSubId                   String? @db.VarChar(50)
  IndexSourceEncounterIdentifier String  @db.VarChar(255)
  MortalityFlag                  Boolean
  SourcePatientIdentifier        String  @db.VarChar(255)

  @@map("Mortality")
  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RISK_Mortality {
  Id                 Int       @default(autoincrement())
  MecaMeasureId      Int
  IndexEncounter     BigInt
  MortalityFlag      Boolean?
  CohortId           Int?
  LastUpdateDateTime DateTime?

  @@map("Mortality")
  @@ignore
  @@schema("RISK")
}

model MVP {
  Id              Int               @id(map: "PK_Meca_MVP") @default(autoincrement())
  MVPCategory     String?           @db.NVarChar(255)
  MVPCompleteness MVPCompleteness[]
  MVPMeasures     MVPMeasures[]

  @@ignore
  @@schema("MECA")
}

model MVPCompleteness {
  Id                      Int    @id(map: "PK_MVPCompleteness") @default(autoincrement())
  MeasureGUID             String @db.UniqueIdentifier
  CompletenessMeasureGUID String @db.UniqueIdentifier
  MVPId                   Int?
  MVP                     MVP?   @relation(fields: [MVPId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__MVPComple__MVPId__34D3C6C9") @ignore

  @@schema("MECA")
}

model MVPMeasures {
  Id          Int    @id(map: "PK_Meca_Measures_MVP") @default(autoincrement())
  MVPId       Int?
  MeasureGUID String @db.UniqueIdentifier
  MVP         MVP?   @relation(fields: [MVPId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__MVPMeasur__MVPId__35C7EB02") @ignore

  @@schema("MECA")
}

model NormalClinicalValues {
  FeatureName      String? @db.VarChar(50)
  NormalRangeValue Int?    @db.SmallInt
  Id               Int     @id(map: "PK__NormalCl__3214EC07D00D351E") @default(autoincrement())

  @@schema("RISK")
}

model ObservedMortalityRate {
  Id            Int     @id(map: "PK_ObservedMortalityRate") @default(autoincrement())
  CohortId      Int?    @db.SmallInt
  ServiceLine   String? @db.VarChar(100)
  MortalityRate Float?  @db.Real
  ProgramYear   Int?

  @@schema("RISK")
}

model OrderCodes {
  Id                BigInt  @id(map: "PK_CSRC_OrderCodes") @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@schema("CSRC")
}

model OrderNegationCodes {
  Id                BigInt  @id(map: "PK_CSRC_OrderNegationCodes") @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Orders {
  Id                     BigInt    @default(autoincrement())
  PatientsId             BigInt
  EncountersId           BigInt?
  OrderingProviderId     BigInt?
  SourceOrderIdentifier  String?   @db.VarChar(255)
  OrderCodeId            BigInt?
  OrderStatusId          BigInt?
  SourceOrderResult      String?   @db.VarChar(255)
  SourceOrderResultUnits String?   @db.VarChar(255)
  NegationCodeId         BigInt?
  DataSourceId           Int
  OrderReason            String?   @db.VarChar(255)
  OrderStartDateTime     DateTime? @db.DateTime
  OrderEndDateTime       DateTime? @db.DateTime
  OrderIntent            String?   @db.VarChar(255)
  AnatomicalLocationSite String?   @db.VarChar(255)
  OrderRank              String?   @db.VarChar(255)
  AuthorDateTime         DateTime? @db.DateTime
  SourcePrimaryKey       String?   @db.VarChar(500)
  IsActive               Boolean?
  HashValue              Bytes?    @db.Binary(16)
  EhrTypeId              Int?
  EhrInstance            Int?

  @@index([SourcePrimaryKey], map: "idx_ADM_Orders_SourcePrimarKey")
  @@ignore
  @@schema("ADM")
}

model OrderStatus {
  Id                BigInt  @id(map: "PK_CSRC_OrderStatusCodes") @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@schema("CSRC")
}

model OrganizationTypes {
  Id          Int        @id(map: "PK__Organiza__3214EC074A9F5AF7") @default(autoincrement()) @db.SmallInt
  Code        String     @db.VarChar(255)
  Description String?    @db.VarChar(255)
  Entities    Entities[]

  @@schema("MECA")
}

model Outcomes {
  Id          Int             @id(map: "PK_RISK_Outcomes_Id") @default(autoincrement())
  Code        String          @db.VarChar(100)
  Description String          @db.VarChar(255)
  Measures    RISK_Measures[]

  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model PatientEthnicityCodes {
  Id                Int        @id @default(autoincrement()) @db.SmallInt
  SourceCode        String     @db.NVarChar(500)
  SourceDescription String?    @db.NVarChar(500)
  SourceCodeSystem  String?    @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Patients          Patients[]

  @@ignore
  @@schema("CSRC")
}

model PatientGenderCodes {
  Id                Int        @id(map: "PK_CSRC_PatientGenderCodes") @default(autoincrement()) @db.SmallInt
  SourceCode        String     @db.NVarChar(500)
  SourceDescription String?    @db.NVarChar(500)
  SourceCodeSystem  String?    @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Patients          Patients[] @ignore

  @@schema("CSRC")
}

model PatientMaritalStatusCodes {
  Id                Int        @id(map: "PK_CSRC_PatientMaritalStatusCodes") @default(autoincrement()) @db.SmallInt
  SourceCode        String     @db.NVarChar(500)
  SourceDescription String?    @db.NVarChar(500)
  SourceCodeSystem  String?    @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Patients          Patients[] @ignore

  @@schema("CSRC")
}

model PatientRaceCodes {
  Id                Int        @id(map: "PK_CSRC_PatientRaceCodes") @default(autoincrement()) @db.SmallInt
  SourceCode        String     @db.NVarChar(500)
  SourceDescription String?    @db.NVarChar(500)
  SourceCodeSystem  String?    @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Patients          Patients[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Patients {
  Id                                     BigInt                                   @id @default(autoincrement())
  SourcePatientIdentifier                String                                   @db.VarChar(255)
  SourceNationalIdentifier               String?                                  @db.VarChar(255)
  FirstName                              String                                   @db.VarChar(255)
  MiddleInitial                          String?                                  @db.VarChar(255)
  LastName                               String                                   @db.VarChar(255)
  Suffix                                 String?                                  @db.VarChar(255)
  MothersMaidenName                      String?                                  @db.VarChar(255)
  BirthDateTime                          DateTime                                 @db.DateTime
  Address1                               String?                                  @db.VarChar(255)
  Address2                               String?                                  @db.VarChar(255)
  City                                   String?                                  @db.VarChar(255)
  StateProvince                          String?                                  @db.VarChar(255)
  ZipPostal                              String?                                  @db.VarChar(20)
  Phone                                  String?                                  @db.VarChar(50)
  PatientGenderCodeId                    Int?                                     @db.SmallInt
  PatientMaritalStatusCodeId             Int?                                     @db.SmallInt
  PatientRaceCodeId                      Int?                                     @db.SmallInt
  PatientEthnicityCodeId                 Int?                                     @db.SmallInt
  ExpiredDateTime                        DateTime?                                @db.DateTime
  LastUpdateDateTime                     DateTime?                                @db.DateTime
  DisplayPatientIdentifier               String?                                  @db.VarChar(255)
  DataSourceId                           Int
  SourcePrimaryKey                       String?                                  @db.VarChar(255)
  IsActive                               Boolean?
  HashValue                              Bytes?                                   @db.Binary(16)
  EhrTypeId                              Int?
  EhrInstance                            Int?
  BilledCodes                            ADM_BilledCodes[]
  EncorAEncounters                       EncorAEncounters[]
  EncorAGapEncounters                    EncorAGapEncounters[]
  Encounters                             ADM_Encounters[]
  Insurances                             Insurances[]
  LabTests                               LabTests[]
  LocationHistory                        ADM_LocationHistory[]
  MeasureCategoryAssignments             MeasureCategoryAssignments[]
  Medications                            Medications[]
  PatientEthnicityCodes                  PatientEthnicityCodes?                   @relation(fields: [PatientEthnicityCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Patients_PatientEthnicityCodeId")
  PatientGenderCodes                     PatientGenderCodes?                      @relation(fields: [PatientGenderCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Patients_PatientGenderCodeId")
  PatientMaritalStatusCodes              PatientMaritalStatusCodes?               @relation(fields: [PatientMaritalStatusCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Patients_PatientMaritalStatusCodeId")
  PatientRaceCodes                       PatientRaceCodes?                        @relation(fields: [PatientRaceCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Patients_PatientRaceCodeId")
  Procedures                             ADM_Procedures[]
  RiskAdjustedMeasureCategoryAssignments RiskAdjustedMeasureCategoryAssignments[]
  Vitals                                 ADM_Vitals[]

  @@index([SourcePrimaryKey], map: "idx_ADM_Patients_SourcePrimarKey")
  @@index([StateProvince], map: "idx_state")
  @@ignore
  @@schema("ADM")
}

model Payers {
  Id                 BigInt          @id(map: "PK__Payers__3214EC077D7309A2") @default(autoincrement())
  EncounterId        BigInt
  InsuranceId        String?         @db.NVarChar(100)
  InsuranceName      String?         @db.NVarChar(100)
  FinancialClassId   String?         @db.NVarChar(100)
  FinancialClassName String?         @db.NVarChar(100)
  Encounters         RISK_Encounters @relation(fields: [EncounterId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__Payers__Encounte__149C0161") @ignore

  @@schema("RISK")
}

model PercentileSources {
  Id                                    Int                                     @id(map: "PK__Percenti__3214EC0707ECF7EE") @default(autoincrement()) @db.SmallInt
  Code                                  String                                  @db.VarChar(100)
  Description                           String?                                 @db.VarChar(255)
  DecileFlag                            Int?
  EntityPercentileSources               EntityPercentileSources[]
  MeasureGroup                          MeasureGroup[]
  MeasureSummaryPercentiles             MeasureSummaryPercentiles[]
  RiskAdjustedMeasureSummaryPercentiles RiskAdjustedMeasureSummaryPercentiles[]
  Stats                                 MECA_Stats[]

  @@schema("MECA")
}

model PerformanceGoals {
  Id                             BigInt   @id(map: "PK_MeasureGoals") @default(autoincrement())
  MeasureIdentifier              String   @db.UniqueIdentifier
  EntitiesId                     BigInt
  StartDate                      DateTime @db.DateTime
  EndDate                        DateTime @db.DateTime
  GoalLower                      Decimal? @db.Decimal(15, 2)
  GoalUpper                      Decimal? @db.Decimal(15, 2)
  Benchmark                      Decimal? @db.Decimal(15, 2)
  IsYellowZoneFixedNumber        Boolean?
  YellowZone                     Decimal? @db.Decimal(15, 2)
  IsExceptionalPerformanceNumber Boolean?
  ExceptionalPerformance         Decimal? @db.Decimal(15, 2)
  LastUpdatedByUserId            String   @db.UniqueIdentifier
  LastUpdatedDateTime            DateTime @db.DateTime
  Entities                       Entities @relation(fields: [EntitiesId], references: [Id], onUpdate: NoAction, map: "FK__Performan__Entit__64ECEE3F")

  @@schema("MECA")
}

model POAExemptDiagnoses {
  Id            Int     @id(map: "PK_POAExemptDiagnoses") @default(autoincrement())
  DiagnosisCode String? @db.VarChar(50)
  ProgramYear   Int?

  @@schema("RISK")
}

model ProcedureAnatomicalDirectionCodes {
  Id                BigInt           @id(map: "PK_CSRC_ProcedureAnatomicalDirectionCodes") @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Procedures        ADM_Procedures[] @ignore

  @@schema("CSRC")
}

model ProcedureAnatomicalLocationCodes {
  Id                BigInt           @id(map: "PK_CSRC_ProcedureAnatomicalLocationCodes") @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Procedures        ADM_Procedures[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ProcedureCodes {
  Id                BigInt           @id(map: "PK_CSRC_ProcedureCodes") @default(autoincrement())
  SourceCode        String           @db.NVarChar(40)
  SourceDescription String?
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Procedures        ADM_Procedures[]

  @@ignore
  @@schema("CSRC")
}

model ProcedureNegationCodes {
  Id                BigInt           @id(map: "PK_CSRC_ProcedureNegationCodes") @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Procedures        ADM_Procedures[] @ignore

  @@schema("CSRC")
}

model ProcedureReasonCodes {
  Id                BigInt           @id(map: "PK_CSRC_ProcedureReasonCodes") @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Procedures        ADM_Procedures[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ProcedureResultCodes {
  Id                BigInt           @id @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Procedures        ADM_Procedures[]

  @@ignore
  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ADM_Procedures {
  Id                                                  BigInt                             @default(autoincrement())
  PatientsId                                          BigInt
  EncountersId                                        BigInt?
  OrdersId                                            BigInt?
  OrderingProviderId                                  BigInt?
  ProcedureCodeId                                     BigInt?
  ProcedureStatusCodeId                               BigInt?
  Ordinality                                          String?                            @db.VarChar(255)
  ProcedureStartDateTime                              DateTime?                          @db.DateTime
  ProcedureEndDateTime                                DateTime?                          @db.DateTime
  NegationCodeId                                      BigInt?
  IncisionDateTime                                    DateTime?                          @db.DateTime
  SourceProcedureResult                               String?                            @db.VarChar(Max)
  SourceProcedureResultUnits                          String?                            @db.VarChar(255)
  ProcedureResultCodeId                               BigInt?
  ProcedureReasonCodeId                               BigInt?
  ProcedureAnatomicalLocationCodeId                   BigInt?
  ProcedureAnatomicalDirectionCodeId                  BigInt?
  DataSourceId                                        Int
  RenderingProviderId                                 BigInt?
  FacilitiesId                                        BigInt?
  ProcedureResultDateTime                             DateTime?                          @db.DateTime
  ProcedureMethod                                     String?                            @db.VarChar(255)
  ProcedurePriority                                   String?                            @db.VarChar(255)
  Recorder                                            String?                            @db.VarChar(255)
  AuthorDateTime                                      DateTime?                          @db.DateTime
  SourcePrimaryKey                                    String?                            @db.VarChar(500)
  IsActive                                            Boolean?
  HashValue                                           Bytes?                             @db.Binary(16)
  EhrTypeId                                           Int?
  EhrInstance                                         Int?
  Encounters                                          ADM_Encounters?                    @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_EncountersId")
  Facilities                                          Facilities?                        @relation(fields: [FacilitiesId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_FacilitiesId")
  ProcedureNegationCodes                              ProcedureNegationCodes?            @relation(fields: [NegationCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_NegationCodeId")
  Providers_Procedures_OrderingProviderIdToProviders  Providers?                         @relation("Procedures_OrderingProviderIdToProviders", fields: [OrderingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_OrderingProviderId")
  Patients                                            Patients                           @relation(fields: [PatientsId], references: [Id], onUpdate: NoAction, map: "FK_ADM_Procedures_PatientsId")
  ProcedureAnatomicalDirectionCodes                   ProcedureAnatomicalDirectionCodes? @relation(fields: [ProcedureAnatomicalDirectionCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_ProcedureAnatomicalDirectionCodeId")
  ProcedureAnatomicalLocationCodes                    ProcedureAnatomicalLocationCodes?  @relation(fields: [ProcedureAnatomicalLocationCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_ProcedureAnatomicalLocationCodeId")
  ProcedureCodes                                      ProcedureCodes?                    @relation(fields: [ProcedureCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_ProcedureCodeId")
  ProcedureReasonCodes                                ProcedureReasonCodes?              @relation(fields: [ProcedureReasonCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_ProcedureReasonCodeId")
  ProcedureResultCodes                                ProcedureResultCodes?              @relation(fields: [ProcedureResultCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_ProcedureResultCodeId")
  ProcedureStatus                                     ProcedureStatus?                   @relation(fields: [ProcedureStatusCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_ProcedureStatusCodeId")
  Providers_Procedures_RenderingProviderIdToProviders Providers?                         @relation("Procedures_RenderingProviderIdToProviders", fields: [RenderingProviderId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Procedures_RenderingProviderId")

  @@index([EncountersId, RenderingProviderId], map: "idx_adm_procedures_encountersid_renderingproviderid_include_procedurecodeid")
  @@index([ProcedureCodeId], map: "idx_adm_procedures_procedurecodeid_include_all")
  @@index([SourcePrimaryKey], map: "idx_ADM_Procedures_SourcePrimarKey")
  @@map("Procedures")
  @@ignore
  @@schema("ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RISK_Procedures {
  Id            BigInt    @id(map: "PK__Procedur__3214EC0784549F31") @default(autoincrement())
  EncounterId   BigInt
  ProcedureDate DateTime? @db.DateTime
  ProcedureCode String    @db.NVarChar(50)
  SequenceId    Int?
  IsHistorical  Boolean?

  @@map("Procedures")
  @@ignore
  @@schema("RISK")
}

model ProcedureStatus {
  Id                BigInt           @id(map: "PK_CSRC_ProcedureStatusCodes") @default(autoincrement())
  SourceCode        String           @db.NVarChar(500)
  SourceDescription String?          @db.NVarChar(500)
  SourceCodeSystem  String?          @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Procedures        ADM_Procedures[] @ignore

  @@schema("CSRC")
}

model ProcedureVariables {
  Id                  Int     @id(map: "PK_Procedures") @default(autoincrement())
  ProcedureCode       String? @db.VarChar(50)
  CCS                 Int?
  Description         String? @db.VarChar(250)
  APP                 Int?
  PPP                 Int?    @db.SmallInt
  AppProgramYearStart Int?
  AppProgramYearEnd   Int?
  PppProgramYearStart Int?
  PppProgramYearEnd   Int?

  @@schema("RISK")
}

model Products {
  Id          Int     @id(map: "PK__Products__3214EC07DB65F20A") @default(autoincrement()) @db.SmallInt
  Code        String  @unique(map: "UQ_MECA_Products_Code") @db.NVarChar(100)
  Description String? @db.NVarChar(255)

  @@schema("MECA")
}

model ProgramYears {
  Id           Int       @id(map: "PK__ProgramY__3214EC07A96D9C09") @default(autoincrement())
  Program      String?   @db.NVarChar(10)
  ProgramYear  Int
  MeasureGUID  String?   @db.UniqueIdentifier
  MeasureSubId String?   @db.NVarChar(20)
  StartDate    DateTime
  EndDate      DateTime?
  LatestLive   Int?      @db.SmallInt

  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Providers {
  Id                                                       BigInt                               @id @default(autoincrement())
  SourceProviderIdentifier                                 String                               @db.VarChar(255)
  ProviderNPI                                              String?                              @db.VarChar(255)
  ProviderName                                             String?                              @db.VarChar(200)
  DataSourceId                                             Int
  ProviderTaxonomy                                         String?                              @db.VarChar(50)
  SourcePrimaryKey                                         String?                              @db.VarChar(500)
  EhrTypeId                                                Int?
  EhrInstance                                              Int?
  BilledCodes_BilledCodes_BillingProviderIdToProviders     ADM_BilledCodes[]                    @relation("BilledCodes_BillingProviderIdToProviders")
  BilledCodes_BilledCodes_OrderingProviderIdToProviders    ADM_BilledCodes[]                    @relation("BilledCodes_OrderingProviderIdToProviders")
  BilledCodes_BilledCodes_RenderingProviderIdToProviders   ADM_BilledCodes[]                    @relation("BilledCodes_RenderingProviderIdToProviders")
  EncounterProviders                                       EncounterProviders[]
  EntityProviders                                          EntityProviders[]
  LabTests_LabTests_OrderingProviderIdToProviders          LabTests[]                           @relation("LabTests_OrderingProviderIdToProviders")
  LabTests_LabTests_RenderingProviderIdToProviders         LabTests[]                           @relation("LabTests_RenderingProviderIdToProviders")
  MeasureCategoryAssignmentProviders                       MeasureCategoryAssignmentProviders[]
  Medications_Medications_OrderingProviderIdToProviders    Medications[]                        @relation("Medications_OrderingProviderIdToProviders")
  Medications_Medications_PrescribingProviderIdToProviders Medications[]                        @relation("Medications_PrescribingProviderIdToProviders")
  Medications_Medications_RenderingProviderIdToProviders   Medications[]                        @relation("Medications_RenderingProviderIdToProviders")
  Procedures_Procedures_OrderingProviderIdToProviders      ADM_Procedures[]                     @relation("Procedures_OrderingProviderIdToProviders")
  Procedures_Procedures_RenderingProviderIdToProviders     ADM_Procedures[]                     @relation("Procedures_RenderingProviderIdToProviders")

  @@ignore
  @@schema("CSRC")
}

model QualifyingCodes {
  Id               Int    @id(map: "PK_COVID_QualifyingCodes") @default(autoincrement())
  MappedCode       String @db.NVarChar(10)
  ConceptType      String @db.NVarChar(10)
  MappedCodeSystem String @db.NVarChar(10)

  @@schema("RISK")
}

model MECA_Readmissions {
  Id                                   Int     @id(map: "PK_Readmissions") @default(autoincrement())
  EntitiesId                           BigInt
  MeasureGUID                          String  @db.UniqueIdentifier
  MeasureSubId                         String? @db.VarChar(50)
  IndexSourceEncounterIdentifier       String  @db.VarChar(255)
  ReadmissionSourceEncounterIdentifier String  @db.VarChar(255)

  @@map("Readmissions")
  @@schema("MECA")
}

model RISK_Readmissions {
  Id                   Int       @id(map: "PK_Readmissions_1") @default(autoincrement())
  MecaMeasureId        Int
  IndexEncounter       BigInt
  ReadmissionEncounter BigInt?
  CohortId             Int?
  LastUpdateDateTime   DateTime?

  @@map("Readmissions")
  @@schema("RISK")
}

model Results {
  Id          BigInt          @id(map: "PK_RISK_Results_Id") @default(autoincrement())
  EncounterId BigInt
  Result      Float?
  RiskModelId Int?
  Encounters  RISK_Encounters @relation(fields: [EncounterId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__Results__Encount__17786E0C") @ignore
  RiskModels  RiskModels?     @relation(fields: [RiskModelId], references: [Id], onUpdate: NoAction, map: "FK__Results__RiskMod__186C9245")

  @@schema("RISK")
}

model RiskAdjustedMeasureCategoryAssignments {
  Id           BigInt          @id(map: "PK__RiskAdju__3214EC070CDC092C") @default(autoincrement())
  MeasureGUID  String?         @db.UniqueIdentifier
  PatientsId   BigInt?
  EncountersId BigInt?
  Expected     Float?
  Encounters   ADM_Encounters? @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__RiskAdjus__Encou__5A6F5FCC") @ignore
  Patients     Patients?       @relation(fields: [PatientsId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__RiskAdjus__Patie__597B3B93") @ignore

  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RISK_RiskAdjustedMeasureSummary {
  Id               BigInt               @id(map: "Pk_RISK_RiskAdjustedMeasureSummary") @default(autoincrement())
  MeasureSummaryId BigInt?
  Expected         Float
  Percentile       Float?
  MeasureSummary   RISK_MeasureSummary? @relation(fields: [MeasureSummaryId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__RiskAdjus__Measu__12B3B8EF")

  @@map("RiskAdjustedMeasureSummary")
  @@ignore
  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MECA_RiskAdjustedMeasureSummary {
  Id                                    BigInt                                  @id @default(autoincrement())
  MeasureSummaryId                      BigInt?
  Expected                              Float
  MeasureSummary                        MECA_MeasureSummary?                    @relation(fields: [MeasureSummaryId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__RiskAdjus__Measu__6B99EBCE")
  RiskAdjustedMeasureSummaryPercentiles RiskAdjustedMeasureSummaryPercentiles[]

  @@map("RiskAdjustedMeasureSummary")
  @@schema("MECA")
}

model RiskAdjustedMeasureSummaryPercentiles {
  Id                           BigInt                           @id(map: "PK__RiskAdju__3214EC075B75B038") @default(autoincrement())
  RiskAdjustedMeasureSummaryId BigInt?
  Percentile                   Float?
  PercentileSourceId           Int?                             @db.SmallInt
  PercentileSources            PercentileSources?               @relation(fields: [PercentileSourceId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__RiskAdjus__Perce__68BD7F23")
  RiskAdjustedMeasureSummary   MECA_RiskAdjustedMeasureSummary? @relation(fields: [RiskAdjustedMeasureSummaryId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__RiskAdjus__RiskA__67C95AEA") @ignore

  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RISK_RiskAdjustedStats {
  Id      BigInt     @id(map: "Pk_RISK_RiskAdjustedStats") @default(autoincrement())
  StatsId BigInt
  Ratio   Float?
  Stats   RISK_Stats @relation(fields: [StatsId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__RiskAdjus__Stats__13A7DD28")

  @@map("RiskAdjustedStats")
  @@ignore
  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MECA_RiskAdjustedStats {
  Id      BigInt     @default(autoincrement())
  StatsId BigInt
  Ratio   Float?
  Stats   MECA_Stats @relation(fields: [StatsId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__RiskAdjus__Stats__7246E95D")

  @@map("RiskAdjustedStats")
  @@ignore
  @@schema("MECA")
}

model RiskModels {
  Id                        Int                         @id(map: "PK_RISK_RiskModels_Id") @default(autoincrement())
  Version                   String?                     @db.VarChar(10)
  Description               String?                     @db.VarChar(255)
  LastUpdateDateTime        DateTime?                   @default(now(), map: "DF__RiskModel__LastU__7A1D154F") @db.DateTime
  IsActive                  Boolean?
  Note                      String?                     @db.VarChar(255)
  MeasureId                 Int?
  Features                  Features[]
  LabVitalThresholds        LabVitalThresholds[]
  MeasureModels             MeasureModels[]
  Results                   Results[]
  Measures                  RISK_Measures?              @relation(fields: [MeasureId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__RiskModel__Measu__0371755F")
  RiskModelSelectedFeatures RiskModelSelectedFeatures[]

  @@schema("RISK")
}

model RiskModelSelectedFeatures {
  Id          Int        @id(map: "PK_RISK_RiskModelSelectedFeatures_Id") @default(autoincrement())
  FeatureId   Int
  Coefficient Float?
  RiskModelId Int
  Features    Features   @relation(fields: [FeatureId], references: [Id], onUpdate: NoAction, map: "FK__RiskModel__Featu__07420643")
  RiskModels  RiskModels @relation(fields: [RiskModelId], references: [Id], onUpdate: NoAction, map: "FK__RiskModel__RiskM__08362A7C")

  @@schema("RISK")
}

model MECA_Stats {
  Id                 BigInt                   @id(map: "PK__Stats__3214EC0724F91ECA") @default(autoincrement())
  MeasureGUID        String                   @db.UniqueIdentifier
  MeasureSubId       String?                  @db.NVarChar(20)
  StartDate          DateTime                 @db.DateTime
  EndDate            DateTime                 @db.DateTime
  Period             String                   @db.NVarChar(5)
  Ratio              Float?
  Decile             Float?
  PercentileSourceId Int?                     @db.SmallInt
  RiskAdjustedStats  MECA_RiskAdjustedStats[] @ignore
  PercentileSources  PercentileSources?       @relation(fields: [PercentileSourceId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__Stats__Percentil__6C8E1007")

  @@map("Stats")
  @@schema("MECA")
}

model RISK_Stats {
  Id                BigInt                   @id(map: "Pk_RISK_Stats") @default(autoincrement())
  MeasureGUID       String                   @db.UniqueIdentifier
  StartDate         DateTime                 @db.DateTime
  EndDate           DateTime                 @db.DateTime
  Period            String                   @db.NVarChar(5)
  Ratio             Float?
  Decile            Float?
  RiskAdjustedStats RISK_RiskAdjustedStats[] @ignore

  @@map("Stats")
  @@schema("RISK")
}

model UserPreferences {
  Id                       Int     @id(map: "PK_MECA.UserPreferences") @default(autoincrement())
  UserId                   String  @db.NVarChar(100)
  DefaultModuleId          String  @db.NVarChar(100)
  DashboardEnabledOverride Boolean

  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model VitalCodes {
  Id                BigInt       @id @default(autoincrement())
  SourceCode        String       @db.NVarChar(500)
  SourceDescription String?      @db.NVarChar(500)
  SourceCodeSystem  String?      @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Vitals            ADM_Vitals[]

  @@ignore
  @@schema("CSRC")
}

model VitalNegationCodes {
  Id                BigInt       @id(map: "PK_CSRC_VitalStatisticsNegationCodes") @default(autoincrement())
  SourceCode        String       @db.NVarChar(500)
  SourceDescription String?      @db.NVarChar(500)
  SourceCodeSystem  String?      @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Vitals            ADM_Vitals[] @ignore

  @@schema("CSRC")
}

model VitalReasonCodes {
  Id                BigInt       @id(map: "PK_CSRC_VitalStatisticsReasonCodes") @default(autoincrement())
  SourceCode        String       @db.NVarChar(500)
  SourceDescription String?      @db.NVarChar(500)
  SourceCodeSystem  String?      @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Vitals            ADM_Vitals[] @ignore

  @@schema("CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RISK_Vitals {
  Id                   BigInt          @default(autoincrement())
  EncounterId          BigInt
  QueryCode            String?         @db.NVarChar(100)
  VitalType            String          @db.NVarChar(50)
  ResultDateTime       DateTime?       @db.DateTime
  Result               Float?
  ResultUnits          String?         @db.NVarChar(50)
  Bin                  Int?
  EarliestWithinWindow Boolean?
  Encounters           RISK_Encounters @relation(fields: [EncounterId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__Vitals__Encounte__064DE20A")

  @@map("Vitals")
  @@ignore
  @@schema("RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ADM_Vitals {
  Id                     BigInt              @default(autoincrement())
  PatientsId             BigInt
  EncountersId           BigInt?
  VitalCodeId            BigInt?
  VitalStatusCodeId      BigInt?
  VitalStartDateTime     DateTime?           @db.DateTime
  VitalEndDateTime       DateTime?           @db.DateTime
  VitalReasonCodeId      BigInt?
  SourceVitalResult      String?             @db.VarChar(8000)
  SourceVitalResultUnits String?             @db.VarChar(255)
  NegationCodeId         BigInt?
  DataSourceId           Int
  SourcePrimaryKey       String?             @db.VarChar(500)
  IsActive               Boolean?
  HashValue              Bytes?              @db.Binary(16)
  EhrTypeId              Int?
  EhrInstance            Int?
  Encounters             ADM_Encounters?     @relation(fields: [EncountersId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Vitals_EncountersId")
  VitalNegationCodes     VitalNegationCodes? @relation(fields: [NegationCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Vitals_NegationCodeId")
  Patients               Patients            @relation(fields: [PatientsId], references: [Id], onUpdate: NoAction, map: "FK_ADM_Vitals_PatientsId")
  VitalCodes             VitalCodes?         @relation(fields: [VitalCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Vitals_VitalCodeId")
  VitalReasonCodes       VitalReasonCodes?   @relation(fields: [VitalReasonCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Vitals_VitalReasonCodeId")
  VitalStatus            VitalStatus?        @relation(fields: [VitalStatusCodeId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ADM_Vitals_VitalStatusCodeId")

  @@index([EncountersId], map: "idx_adm_vitals_encountersid")
  @@index([SourcePrimaryKey], map: "idx_ADM_Vitals_SourcePrimarKey")
  @@index([VitalCodeId], map: "idx_adm_vitals_vitalscodeid_include_encountersid")
  @@map("Vitals")
  @@ignore
  @@schema("ADM")
}

model VitalStatus {
  Id                BigInt       @id(map: "PK_CSRC_VitalStatisticsStatusCodes") @default(autoincrement())
  SourceCode        String       @db.NVarChar(500)
  SourceDescription String?      @db.NVarChar(500)
  SourceCodeSystem  String?      @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?
  Vitals            ADM_Vitals[] @ignore

  @@schema("CSRC")
}

model ZipCodeCentroids {
  Id      Int    @id(map: "PK_ZipCodeCentroids") @default(autoincrement())
  ZipCode Int?
  Lat     Float? @db.Real
  Lon     Float? @db.Real

  @@schema("MECA")
}

model AdmissionService {
  Id          Int     @id(map: "Pk_Master_AdmissionService") @default(autoincrement())
  Code        String? @db.VarChar(5)
  Description String? @db.VarChar(255)

  @@schema("MSTD")
}

model AdmissionSource {
  Id          Int     @id(map: "Pk_MSTD_AdmissionSource") @default(autoincrement())
  Code        String  @db.VarChar(5)
  Description String? @db.VarChar(255)

  @@schema("MSTD")
}

model AdmissionType {
  Id          BigInt  @id(map: "PK_AdmissionType") @default(autoincrement())
  Code        String  @db.VarChar(5)
  Description String? @db.VarChar(500)

  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_BilledCodeModifiers {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("BilledCodeModifiers")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_ADM_BilledCodes {
  Id                          BigInt    @id(map: "PK_ADM_BilledCodeTransactions") @default(autoincrement())
  SourceTransactionId         String?   @db.VarChar(100)
  SourceLinkedTransactionId   String?   @db.VarChar(100)
  PatientsId                  BigInt?
  EncountersId                BigInt?
  ServiceDateTime             DateTime? @db.DateTime
  BilledCodeId                BigInt?
  BilledCodeModifierId        BigInt?
  OrderingProviderId          BigInt?
  RenderingProviderId         BigInt?
  BillingProviderId           BigInt?
  Quantity                    Decimal?  @db.Decimal(7, 2)
  ChargedAmount               Float?    @db.Money
  CostAmount                  Float?    @db.Money
  RevenueCode                 String?   @db.VarChar(100)
  DataSourceId                Int
  SourcePrimaryKey            String?   @db.VarChar(500)
  IsActive                    Boolean?
  HashValue                   Bytes?    @db.Binary(16)
  SourceTransactionChargeCode String?   @db.VarChar(100)
  EhrTypeId                   Int?
  EhrInstance                 Int?

  @@map("BilledCodes")
  @@ignore
  @@schema("STG_ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_BilledCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("BilledCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_BilledCodes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("BilledCodes")
  @@ignore
  @@schema("CMAP")
}

model CDMConfigurations {
  Id                     Int     @id(map: "PK__CDMConfi__3214EC07EFC5322D") @default(autoincrement())
  EHRInstance            Int
  EHRInstanceName        String? @db.VarChar(100)
  EHRInstanceDescription String? @db.VarChar(100)
  OrgID                  String? @db.VarChar(100)
  ehrID                  String? @db.VarChar(100)
  client                 String? @db.VarChar(100)
  storageAccount         String? @db.VarChar(100)
  extractorType          String? @db.VarChar(100)
  extractorVariation     String? @db.VarChar(100)
  scope                  String? @db.VarChar(100)
  secret                 String? @db.VarChar(100)
  CDMVersion             String? @db.VarChar(100)
  citcGuid               String? @db.VarChar(100)
  ParentCDM              String? @db.VarChar(100)

  @@schema("MSTD")
}

model DQ_CohortStandardCodes {
  Id           BigInt   @id(map: "PK_CohortStandardCodes") @default(autoincrement())
  CohortCode   String   @db.VarChar(50)
  StandardCode String   @db.VarChar(100)
  CodeType     String?  @db.VarChar(50)
  IsInclusion  Boolean
  SubCategory  String?  @db.VarChar(255)
  Principal    Boolean?
  Secondary    Boolean?
  RequirePOA   Boolean?

  @@map("CohortStandardCodes")
  @@schema("DQ")
}

model ConversionHistory {
  Id                           BigInt    @id(map: "PK_DataQuality_ConversionHistory") @default(autoincrement())
  IndicatorId                  Int
  ConversionStartDateTime      DateTime?
  ConversionEndDateTime        DateTime?
  EncounterDetailEndDateTime   DateTime?
  EncounterDetailStartDateTime DateTime?
  CCN                          String    @db.VarChar(6)
  Indicator                    Indicator @relation(fields: [IndicatorId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__Conversio__Indic__50E5F592")

  @@schema("DQ")
}

model CVX {
  Id              BigInt  @id(map: "PK_CVX") @default(autoincrement())
  Code            String? @db.VarChar(5)
  Description     String? @db.VarChar(500)
  FullVaccineName String? @db.VarChar(500)
  Note            String? @db.VarChar(500)
  VaccineStatus   String? @db.VarChar(50)
  InternalId      Int
  NonVaccine      Boolean
  UpdateDate      String? @db.VarChar(Max)

  @@schema("MSTD")
}

model DataSource {
  Id                         BigInt     @id(map: "PK_DataSource") @default(autoincrement())
  Code                       String     @db.VarChar(50)
  Description                String?    @db.VarChar(500)
  LastUpdateDateTime         DateTime?  @db.DateTime
  Version                    String?    @db.VarChar(50)
  SourceEMRType              String?    @db.VarChar(50)
  CDMDatabaseName            String?    @db.VarChar(50)
  ADMDatabaseName            String?    @db.VarChar(50)
  CDMContainerName           String?    @db.VarChar(50)
  DataManagementDatabaseName String?    @db.VarChar(50)
  IsClaim                    Boolean
  IsActive                   Boolean
  UseCaseIdentifier          Boolean?
  IsFlatFile                 Boolean?
  Facility                   Facility[]

  @@schema("MSTD")
}

model DischargeStatus {
  Id          BigInt  @id(map: "PK_DischargeStatus") @default(autoincrement())
  Code        String  @db.VarChar(5)
  Description String? @db.VarChar(500)

  @@schema("MSTD")
}

model EhrTypes {
  Id          BigInt  @id(map: "PK_MSTD_EhrTypes") @default(autoincrement())
  Code        String  @db.VarChar(10)
  Description String? @db.VarChar(500)
  Version     String? @db.VarChar(255)
  Instance    String? @db.VarChar(255)

  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterAdmitPriorityCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterAdmitPriorityCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_EncounterAdmitPriorityCodes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterAdmitPriorityCodes")
  @@ignore
  @@schema("CMAP")
}

model STG_CSRC_EncounterAdmitServiceCodes {
  Id                BigInt  @id(map: "PK_STG_CSRC_EncounterAdmitServiceCodes") @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterAdmitServiceCodes")
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterAdmitSourceCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterAdmitSourceCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_EncounterAdmitSourceCodes {
  Id               BigInt  @id(map: "PK__Encounte__3214EC0740C28884") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterAdmitSourceCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterBillingClass {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterBillingClass")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterConditionDiagnosisCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterConditionDiagnosisCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_EncounterConditionDiagnosisCodes {
  Id               BigInt  @id(map: "PK__Encounte__3214EC073085A934") @default(autoincrement())
  SourceCode       String? @db.VarChar(255)
  MappedCode       String? @db.VarChar(255)
  MappedCodeSystem String? @db.VarChar(255)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterConditionDiagnosisCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterConditionPresentOnAdmits {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterConditionPresentOnAdmits")
  @@ignore
  @@schema("STG_CSRC")
}

model CMAP_EncounterConditionPresentOnAdmits {
  Id               BigInt  @id(map: "PK__Encounte__3214EC07C91E198B") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterConditionPresentOnAdmits")
  @@schema("CMAP")
}

model STG_ADM_EncounterConditions {
  Id                 BigInt   @id(map: "PK_ADM_EncounterConditions") @default(autoincrement())
  EncountersId       BigInt
  DiagnosisCodeId    BigInt?
  Ordinality         String?  @db.VarChar(25)
  PresentOnAdmitId   BigInt?
  SeverityCodeId     BigInt?
  IsBillingDiagnosis Boolean?
  DataSourceId       Int
  SourcePrimaryKey   String?  @db.VarChar(500)
  IsActive           Boolean?
  HashValue          Bytes?   @db.Binary(16)
  EhrTypeId          Int?
  EhrInstance        Int?

  @@index([SourcePrimaryKey, Id], map: "idx_STG_ADM_EncounterConditions")
  @@map("EncounterConditions")
  @@schema("STG_ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterConditionSeverityCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterConditionSeverityCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_EncounterConditionSeverityCodes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterConditionSeverityCodes")
  @@ignore
  @@schema("CMAP")
}

model CMAP_EncounterDecisionToAdmitCodes {
  Id               BigInt  @id(map: "PK__Encounte__3214EC0743960C84") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterDecisionToAdmitCodes")
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterDecisionToAdmitCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String
  SourceDescription String?
  SourceCodeSystem  String?
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterDecisionToAdmitCodes")
  @@ignore
  @@schema("STG_CSRC")
}

model EncounterDetail {
  Id                             BigInt    @id(map: "PK_DataQuality_EncounterDetail") @default(autoincrement())
  EncountersId                   BigInt
  CCN                            String    @db.VarChar(6)
  SourceEncounterIdentifier      String?   @db.VarChar(100)
  EncounterType                  String?   @db.VarChar(100)
  AdmitSource                    String?   @db.VarChar(10)
  AdmitStatus                    String?   @db.VarChar(10)
  Age                            Int?
  Gender                         String?   @db.VarChar(10)
  MaritalStatus                  String?   @db.VarChar(100)
  Ethnicity                      String?   @db.VarChar(100)
  Race                           String?   @db.VarChar(100)
  ZipCode                        String?   @db.VarChar(20)
  AdmitDateTime                  DateTime?
  DischargeDateTime              DateTime?
  DischargeDisposition           String?   @db.VarChar(10)
  MDC                            String?   @db.VarChar(255)
  MSDRG                          String?   @db.VarChar(255)
  PrincipalDiagnosis             String?   @db.VarChar(255)
  PrincipalProcedure             String?   @db.VarChar(255)
  PrincipalProcedureDateTime     DateTime?
  PrincipalProcedureProvider     String?   @db.VarChar(500)
  DischargeProviderName          String?   @db.VarChar(500)
  AdmittingProviderName          String?   @db.VarChar(500)
  PrimaryCareProviderName        String?   @db.VarChar(500)
  PrincipalActivePayer           String?   @db.VarChar(500)
  TotalDiagnosisCodes            Int?
  TotalProblems                  Int?
  TotalProcedures                Int?
  TotalProcedureDates            Int?
  TotalProcedureProviders        Int?
  TotalEMCodes                   Int?
  TotalCPT4Codes                 Int?
  TotalMedicationOrders          Int?
  TotalMedicationAdministrations Int?
  TotalBloodTransfusions         Int?
  TotalLabOrders                 Int?
  TotalLabResults                Int?
  TotalHeartRates                Int?
  TotalBloodPressures            Int?
  TotalRespiratoryRate           Int?
  TotalTemperature               Int?
  TotalOxygenSaturation          Int?
  TotalBodyWeight                Int?
  TotalCharges                   Float?
  TotalRevenueCodes              Int?

  @@schema("DQ")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_EncounterDetailCodes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterDetailCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterDetailCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterDetailCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_ADM_EncounterDetails {
  Id                     BigInt    @default(autoincrement())
  EncountersId           BigInt
  FacilitiesId           BigInt?
  LocationsId            BigInt?
  EncounterStartDateTime DateTime? @db.DateTime
  EncounterEndDateTime   DateTime? @db.DateTime
  EncounterDetailCodeId  BigInt?
  EncounterDetailTypeId  BigInt?
  DataSourceId           Int
  SourcePrimaryKey       String?   @db.VarChar(500)
  IsActive               Boolean?
  HashValue              Bytes?    @db.Binary(16)
  EhrTypeId              Int?
  EhrInstance            Int?

  @@map("EncounterDetails")
  @@ignore
  @@schema("STG_ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterDetailTypes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterDetailTypes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_EncounterDetailTypes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterDetailTypes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_ADM_EncounterDiagnosisRelatedGroup {
  Id                                     BigInt   @default(autoincrement())
  EncountersId                           BigInt?
  EncounterDiagnosisRelatedGroupCodeId   BigInt?
  EncounterDiagnosisRelatedGroupStatusId BigInt?
  DiagnosisRelatedGroupGrouperVersion    String?  @db.VarChar(255)
  ApDrgSeverityOfIllness                 String?  @db.VarChar(255)
  ApDrgRiskofMortality                   String?  @db.VarChar(255)
  DataSourceId                           Int
  SourcePrimaryKey                       String?  @db.VarChar(500)
  IsActive                               Boolean?
  HashValue                              Bytes?   @db.Binary(16)
  EhrTypeId                              Int?
  EhrInstance                            Int?

  @@map("EncounterDiagnosisRelatedGroup")
  @@ignore
  @@schema("STG_ADM")
}

model CMAP_EncounterDischargeDispositionCodes {
  Id               BigInt  @id(map: "PK__Encounte__3214EC07DAC2F78D") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterDischargeDispositionCodes")
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterDischargeDispositionCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterDischargeDispositionCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterDRGCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterDRGCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterDRGStatus {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterDRGStatus")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_RISK_EncounterMeasureResults {
  Id                        BigInt  @default(autoincrement())
  MeasureGUID               String? @db.UniqueIdentifier
  CCN                       String? @db.NVarChar(6)
  SourceEncounterIdentifier String? @db.NVarChar(100)
  DataSource                String? @db.NVarChar(255)
  CategoryAssignment        String? @db.NVarChar(100)
  Value                     String? @db.NVarChar(100)
  Note                      String? @db.VarChar(500)

  @@map("EncounterMeasureResults")
  @@ignore
  @@schema("STG_RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterMeasureSummary {
  Id                        BigInt    @default(autoincrement())
  SourceContainerIdentifier String?   @db.NVarChar(255)
  MeasureGUID               String?   @db.UniqueIdentifier
  MeasureSubId              String?   @db.NVarChar(20)
  MeasureTallyIdentifier    String?   @db.NVarChar(255)
  CategoryAssignmentsId     Int?      @db.SmallInt
  EntityId                  BigInt?
  ReferenceDate             DateTime?
  PatientsId                BigInt?
  EncountersId              BigInt?
  Age                       Int?
  AgeBand                   Int?
  Expected                  Float?
  NumeratorValue            Decimal?  @db.Decimal(18, 10)
  FalloutIndicator          Int?
  ReferenceDay              DateTime? @db.Date
  ReferenceDateNumeric      BigInt?
  IsGenerated               Int?
  DenominatorValue          Decimal?  @db.Decimal(18, 10)
  ProvidersId               BigInt?
  PartitionKey              String    @db.NVarChar(261)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterMeasureSummary_bkp {
  Id                        BigInt    @default(autoincrement())
  SourceContainerIdentifier String?   @db.NVarChar(255)
  MeasureGUID               String?   @db.UniqueIdentifier
  MeasureSubId              String?   @db.NVarChar(20)
  MeasureTallyIdentifier    String?   @db.NVarChar(255)
  CategoryAssignmentsId     Int?      @db.SmallInt
  EntityId                  BigInt?
  ReferenceDate             DateTime?
  PatientsId                BigInt?
  EncountersId              BigInt?
  Age                       Int?
  AgeBand                   Int?
  Expected                  Float?
  NumeratorValue            Decimal?  @db.Decimal(18, 10)
  FalloutIndicator          Int?
  ReferenceDay              DateTime? @db.Date
  ReferenceDateNumeric      BigInt?
  IsGenerated               Int?
  DenominatorValue          Decimal?  @db.Decimal(18, 10)
  ProvidersId               BigInt?

  @@ignore
  @@schema("MPRE")
}

model EncounterMeasureSummaryPeriod {
  Id                     BigInt    @id(map: "PK__Encounte__3214EC07AD45D067") @default(autoincrement())
  MeasureGUID            String?   @db.UniqueIdentifier
  MeasureTallyIdentifier String?   @db.NVarChar(255)
  PatientsId             BigInt?
  EncountersId           BigInt?
  ReferenceDate          DateTime? @db.Date
  CategoryAssignmentsId  Int?      @db.SmallInt
  EntityId               BigInt?
  NumeratorValue         Decimal?  @db.Decimal(18, 10)
  DenominatorValue       Decimal?  @db.Decimal(18, 10)
  Period                 String?   @db.VarChar(1)
  StartDate              DateTime? @db.DateTime
  P10                    Decimal?  @db.Decimal(10, 2)
  P20                    Decimal?  @db.Decimal(10, 2)
  P25                    Decimal?  @db.Decimal(10, 2)
  P30                    Decimal?  @db.Decimal(10, 2)
  P40                    Decimal?  @db.Decimal(10, 2)
  P50                    Decimal?  @db.Decimal(10, 2)
  P60                    Decimal?  @db.Decimal(10, 2)
  P70                    Decimal?  @db.Decimal(10, 2)
  P75                    Decimal?  @db.Decimal(10, 2)
  P80                    Decimal?  @db.Decimal(10, 2)
  P90                    Decimal?  @db.Decimal(10, 2)
  P100                   Decimal?  @db.Decimal(10, 2)
  P50Y                   Decimal?  @db.Decimal(10, 2)
  PTile                  Float?
  PercentileSourceId     Int?
  FixedPeriod            Int
  FixedYear              String    @db.VarChar(14)
  FixedYearInt           Int?
  Points                 Float?

  @@index([CategoryAssignmentsId, FixedPeriod, FixedYear, ReferenceDate], map: "idx_encountermeasuresummaryperiod_cat_fixedperiod_fixedyear_date")
  @@index([EntityId, FixedPeriod, FixedYear, ReferenceDate], map: "idx_encountermeasuresummaryperiod_entity_fixedperiod_fixedyear_date")
  @@index([FixedPeriod, FixedYear, ReferenceDate], map: "idx_encountermeasuresummaryperiod_patients")
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterMeasureSummaryPeriod_bkp {
  Id                     BigInt    @default(autoincrement())
  MeasureGUID            String?   @db.UniqueIdentifier
  MeasureTallyIdentifier String?   @db.NVarChar(255)
  PatientsId             BigInt?
  EncountersId           BigInt?
  ReferenceDate          DateTime? @db.Date
  CategoryAssignmentsId  Int?      @db.SmallInt
  EntityId               BigInt?
  NumeratorValue         Decimal?  @db.Decimal(18, 10)
  DenominatorValue       Decimal?  @db.Decimal(18, 10)
  Period                 String?   @db.VarChar(1)
  StartDate              DateTime? @db.DateTime
  P10                    Decimal?  @db.Decimal(10, 2)
  P20                    Decimal?  @db.Decimal(10, 2)
  P25                    Decimal?  @db.Decimal(10, 2)
  P30                    Decimal?  @db.Decimal(10, 2)
  P40                    Decimal?  @db.Decimal(10, 2)
  P50                    Decimal?  @db.Decimal(10, 2)
  P60                    Decimal?  @db.Decimal(10, 2)
  P70                    Decimal?  @db.Decimal(10, 2)
  P75                    Decimal?  @db.Decimal(10, 2)
  P80                    Decimal?  @db.Decimal(10, 2)
  P90                    Decimal?  @db.Decimal(10, 2)
  P100                   Decimal?  @db.Decimal(10, 2)
  P50Y                   Decimal?  @db.Decimal(10, 2)
  PTile                  Float?
  PercentileSourceId     Int?
  FixedPeriod            Int
  FixedYear              String    @db.VarChar(14)
  FixedYearInt           Int?
  Points                 Float?

  @@ignore
  @@schema("MPRE")
}

model STG_ADM_EncounterProviders {
  Id                      BigInt   @id(map: "PK_ADM_EncounterProviders") @default(autoincrement())
  EncountersId            BigInt
  ProvidersId             BigInt
  EncounterProviderTypeId BigInt?
  DataSourceId            Int
  SourcePrimaryKey        String?  @db.VarChar(500)
  IsActive                Boolean?
  HashValue               Bytes?   @db.Binary(16)
  EhrTypeId               Int?
  EhrInstance             Int?

  @@index([SourcePrimaryKey, Id], map: "idx_STG_ADM_EncounterProviders")
  @@map("EncounterProviders")
  @@schema("STG_ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterProviderTaxonomyCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterProviderTaxonomyCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterProviderTypes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterProviderTypes")
  @@ignore
  @@schema("STG_CSRC")
}

model CMAP_EncounterProviderTypes {
  Id               BigInt  @id(map: "PK__Encounte__3214EC078BE6A5BF") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("EncounterProviderTypes")
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_EncounterRegistrationClass {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("EncounterRegistrationClass")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EncounterRiskModelResults {
  Id                        BigInt  @default(autoincrement())
  MeasureGUID               String? @db.UniqueIdentifier
  CCN                       String? @db.NVarChar(6)
  SourceEncounterIdentifier String? @db.NVarChar(100)
  DataSource                String? @db.NVarChar(255)
  Expected                  Float?

  @@ignore
  @@schema("STG_RISK")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Encounters {
  Id                           BigInt    @default(autoincrement())
  SourceEncounterIdentifier    String    @db.VarChar(255)
  CaseIdentifier               String    @db.VarChar(255)
  PatientsId                   BigInt
  CCN                          String?   @db.VarChar(6)
  NewbornEncounterIndicator    Boolean?
  ExpiredDateTime              DateTime? @db.DateTime
  IsAdmittedFromEd             Boolean?
  DecisionToAdmitDateTime      DateTime? @db.DateTime
  DecisionToAdmitCodeId        BigInt?
  AdmitSourceCodeId            BigInt?
  AdmitPriorityCodeId          BigInt?
  AdmitServiceCodeId           BigInt?
  DischargeDispositionCodeId   BigInt?
  EncounterRegistrationClassId BigInt?
  EncounterBillingClassId      BigInt?
  TotalCharges                 Float?    @db.Money
  DataSourceId                 Int
  AuthorDateTime               DateTime? @db.DateTime
  SourcePrimaryKey             String?   @db.VarChar(500)
  IsActive                     Boolean?
  HashValue                    Bytes?    @db.Binary(16)
  EhrTypeId                    Int?
  EhrInstance                  Int?

  @@index([SourcePrimaryKey, Id], map: "idx_STG_ADM_Encounters")
  @@ignore
  @@schema("STG_ADM")
}

model EncounterType {
  Id          BigInt  @id(map: "PK_EncounterType") @default(autoincrement())
  Code        String  @db.VarChar(5)
  Description String? @db.VarChar(500)

  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EntityEhr {
  SourceContainerIdentifier String? @db.NVarChar(255)
  EhrTypeId                 Int

  @@ignore
  @@schema("MPRE")
}

model Ethnicity {
  Id          BigInt  @id(map: "PK_Ethnicity") @default(autoincrement())
  Code        String  @db.VarChar(20)
  Description String? @db.VarChar(500)
  Level       Int
  Definition  String? @db.VarChar(500)

  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_Facilities {
  Id                      BigInt  @default(autoincrement())
  SourceCode              String? @db.NVarChar(Max)
  SourceDescription       String? @db.NVarChar(Max)
  SourceFacilityAddress1  String? @db.NVarChar(Max)
  SourceFacilityAddress2  String? @db.NVarChar(Max)
  SourceFacilityCity      String? @db.NVarChar(Max)
  SourceFacilityState     String? @db.NVarChar(Max)
  SourceFacilityZipPostal String? @db.NVarChar(Max)
  CCN                     String? @db.NVarChar(Max)
  DataSourceId            Int?
  EhrTypeId               Int?
  EhrInstance             Int?

  @@map("Facilities")
  @@ignore
  @@schema("STG_CSRC")
}

model Facility {
  Id           BigInt     @id(map: "PK_MSTD_Facility") @default(autoincrement())
  CCN          String?    @db.VarChar(6)
  Code         String?    @db.VarChar(50)
  Description  String?    @db.VarChar(500)
  FacilityType String?    @db.VarChar(100)
  Address      String?    @db.VarChar(500)
  City         String?    @db.VarChar(100)
  State        String?    @db.VarChar(100)
  ZipCode      String?    @db.VarChar(100)
  County       String?    @db.VarChar(100)
  Phone        String?    @db.VarChar(100)
  Latitude     Float?     @db.Real
  Longitude    Float?     @db.Real
  BedSize      Int?
  DataSourceId BigInt
  DataSource   DataSource @relation(fields: [DataSourceId], references: [Id], onUpdate: NoAction, map: "FK__Facility__DataSo__7BD05397")

  @@schema("MSTD")
}

model FacilityCodes {
  Id               BigInt  @id(map: "PK__Facility__3214EC077B103810") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@schema("CMAP")
}

model Gender {
  Id          BigInt  @id(map: "PK_Gender") @default(autoincrement())
  Code        String  @db.VarChar(20)
  Description String? @db.VarChar(500)
  Level       Int
  Definition  String? @db.VarChar(500)

  @@schema("MSTD")
}

model ICD10CMCodes {
  Id          Int     @id(map: "Pk_AHRQ_ICD10CMCodes") @default(autoincrement())
  set_name    String  @db.VarChar(50)
  module      String  @db.VarChar(5)
  code        String  @db.VarChar(50)
  description String? @db.VarChar(255)
  ver         String  @db.VarChar(10)

  @@index([set_name, code], map: "idx_AHRQ_ICD10CMCodes_set_code")
  @@index([code], map: "idx_AHRQ_ICD10CMCodes_set_code2")
  @@index([module], map: "idx_AHRQ_ICD10CMCodes_set_module")
  @@schema("AHRQ")
}

model ICD10CMSets {
  Id          Int    @id(map: "Pk_AHRQ_ICD10CMSets") @default(autoincrement())
  module      String @db.VarChar(5)
  set_type    String @db.VarChar(50)
  name        String @db.VarChar(50)
  description String @db.VarChar(255)

  @@index([name, set_type], map: "idx_AHRQ_ICD10CMSets_name_type")
  @@schema("AHRQ")
}

model ICD10DX {
  Id                     BigInt    @id(map: "PK_ICD10DX") @default(autoincrement())
  Code                   String    @db.VarChar(10)
  Description            String?   @db.VarChar(500)
  EffectiveStartDateTime DateTime?
  IsActive               Boolean?
  EffectiveEndDateTime   DateTime?

  @@schema("MSTD")
}

model ICD10PC {
  Id                     BigInt    @id(map: "PK_ICD10PC") @default(autoincrement())
  Code                   String    @db.VarChar(10)
  Description            String?   @db.VarChar(500)
  EffectiveStartDateTime DateTime?
  IsActive               Boolean?
  EffectiveEndDateTime   DateTime?

  @@index([Code], map: "idx_mstd_icd10pc_code_include_description")
  @@schema("MSTD")
}

model Inactive {
  Id        BigInt @id(map: "PK_STG_ADM_Inactive") @default(autoincrement())
  TableId   BigInt
  TableName String @db.VarChar(255)

  @@index([TableName], map: "idx_STG_ADM_Inactive")
  @@schema("STG_ADM")
}

model Indicator {
  Id                                       Int                 @id(map: "PK_DataQuality_Indicator") @default(autoincrement())
  Code                                     String?             @db.VarChar(10)
  Description                              String?             @db.VarChar(255)
  Type                                     String?             @db.VarChar(255)
  DataQualityType                          String?             @db.VarChar(255)
  NumeratorId                              Int?
  DenominatorId                            Int?
  YellowThreshold                          Float?
  GreenThreshold                           Float?
  HighIsGood                               Boolean
  IsActive                                 Boolean
  ConversionHistory                        ConversionHistory[]
  Measure_Indicator_DenominatorIdToMeasure Measure?            @relation("Indicator_DenominatorIdToMeasure", fields: [DenominatorId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__Indicator__Denom__55AAAAAF")
  Measure_Indicator_NumeratorIdToMeasure   Measure?            @relation("Indicator_NumeratorIdToMeasure", fields: [NumeratorId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "FK__Indicator__Numer__569ECEE8")
  WorkList                                 WorkList[]

  @@schema("DQ")
}

model STG_ADM_Insurances {
  Id                    BigInt    @id(map: "PK_ADM_Insurances") @default(autoincrement())
  PatientsId            BigInt
  EncountersId          BigInt?
  SourceOfPaymentCodeId BigInt?
  Ordinality            String?   @db.VarChar(25)
  PolicyNumber          String?   @db.VarChar(255)
  HICNumber             String?   @db.VarChar(255)
  CarrierName           String?   @db.VarChar(255)
  EffectiveDateTime     DateTime? @db.DateTime
  ExpirationDateTime    DateTime? @db.DateTime
  DataSourceId          Int
  SourcePrimaryKey      String?   @db.VarChar(500)
  IsActive              Boolean?
  HashValue             Bytes?    @db.Binary(16)
  EhrTypeId             Int?
  EhrInstance           Int?

  @@index([SourcePrimaryKey, Id], map: "idx_STG_ADM_Insurances")
  @@map("Insurances")
  @@schema("STG_ADM")
}

model InsuranceSOPCodes {
  Id               BigInt  @id(map: "PK__Insuranc__3214EC077CDA0662") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_InsuranceSourceOfPaymentCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("InsuranceSourceOfPaymentCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_LabTestCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("LabTestCodes")
  @@ignore
  @@schema("STG_CSRC")
}

model CMAP_LabTestCodes {
  Id               BigInt  @id(map: "PK__LabTestC__3214EC072AA4DAD8") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("LabTestCodes")
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_LabTestNegationCodes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("LabTestNegationCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_LabTestNegationCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("LabTestNegationCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_LabTestReasonCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("LabTestReasonCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_LabTestResultCodes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("LabTestResultCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_LabTestResultCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("LabTestResultCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_ADM_LabTests {
  Id                       BigInt    @default(autoincrement())
  PatientsId               BigInt
  EncountersId             BigInt?
  SpecimensId              BigInt?
  OrdersId                 BigInt?
  OrderingProviderId       BigInt?
  LabTestCodeId            BigInt?
  LabTestOrderedDateTime   DateTime? @db.DateTime
  LabTestPerformedDateTime DateTime? @db.DateTime
  SourceLabTestResult      String?   @db.VarChar(1000)
  SourceLabTestResultUnits String?   @db.VarChar(255)
  LabTestStatusId          BigInt?
  NegationCodeId           BigInt?
  DataSourceId             Int
  LabTestMethod            String?   @db.VarChar(255)
  AuthorDateTime           DateTime? @db.DateTime
  LabReferenceRangeLow     String?   @db.VarChar(500)
  LabReferenceRangeHigh    String?   @db.VarChar(500)
  LabTestStartDateTime     DateTime? @db.DateTime
  LabTestEndDateTime       DateTime? @db.DateTime
  LabTestResultDateTime    DateTime? @db.DateTime
  RenderingProviderId      BigInt?
  SourcePrimaryKey         String?   @db.VarChar(500)
  LabTestReasonCodeId      BigInt?
  LabTestResultCodeId      BigInt?
  IsActive                 Boolean?
  HashValue                Bytes?    @db.Binary(16)
  EhrTypeId                Int?
  EhrInstance              Int?

  @@map("LabTests")
  @@ignore
  @@schema("STG_ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_LabTestStatus {
  Id               BigInt  @id(map: "PK__LabTestS__3214EC07F2BD4E19") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("LabTestStatus")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_LabTestStatus {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("LabTestStatus")
  @@ignore
  @@schema("STG_CSRC")
}

model LabType {
  Id          Int     @id(map: "PK_Master_LabType_Id") @default(autoincrement())
  Code        String? @db.VarChar(50)
  Description String? @db.VarChar(255)

  @@schema("MSTD")
}

model LocationCodes {
  Id               BigInt  @id(map: "PK__Location__3214EC07893D9F46") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model LocationHistory {
  Id                    BigInt    @id(map: "PK_ADM_Transfers") @default(autoincrement())
  PatientsId            BigInt
  EncountersId          BigInt?
  LocationId            BigInt?
  TransferStartDateTime DateTime? @db.DateTime
  TransferEndDateTime   DateTime? @db.DateTime
  DataSourceId          Int
  SourcePrimaryKey      String?   @db.VarChar(500)
  IsActive              Boolean?
  HashValue             Bytes?    @db.Binary(16)
  EhrTypeId             Int?
  EhrInstance           Int?

  @@ignore
  @@schema("STG_ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_Locations {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("Locations")
  @@ignore
  @@schema("STG_CSRC")
}

model LOINC {
  Id                 BigInt  @id(map: "PK_LOINC") @default(autoincrement())
  Code               String  @db.VarChar(10)
  Description        String? @db.VarChar(500)
  Status             String? @db.VarChar(20)
  VersionLastChanged String? @db.VarChar(20)

  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Mapping_zips {
  SourceCode             String?   @db.VarChar(255)
  SourceCodeSystem       String?   @db.VarChar(255)
  MappedCode             String?   @db.VarChar(255)
  MappedCodeSystem       String?   @db.VarChar(255)
  StandardContext        String?   @db.VarChar(255)
  ClientContext          String?   @db.VarChar(255)
  FacilityIdentifier     String?   @db.VarChar(255)
  MedisolvAppIdentifier  String?   @db.VarChar(255)
  EffectiveStartDateTime DateTime? @db.DateTime
  EffectiveEndDateTime   DateTime? @db.DateTime
  CreateDateTime         DateTime? @db.DateTime
  ModifyDateTime         DateTime? @db.DateTime
  DataSourceId           Int?
  EhrTypeId              Int?
  EhrInstance            Int?

  @@ignore
  @@schema("CMAP")
}

model Mappings {
  Id                     BigInt    @id(map: "PK__Mappings__3214EC07858625B6") @default(autoincrement())
  SourceCode             String?   @db.VarChar(255)
  SourceCodeSystem       String?   @db.VarChar(255)
  MappedCode             String?   @db.VarChar(255)
  MappedCodeSystem       String?   @db.VarChar(255)
  StandardContext        String?   @db.VarChar(255)
  ClientContext          String?   @db.VarChar(255)
  FacilityIdentifier     String?   @db.VarChar(255)
  MedisolvAppIdentifier  String?   @db.VarChar(255)
  EffectiveStartDateTime DateTime? @db.DateTime
  EffectiveEndDateTime   DateTime? @db.DateTime
  CreateDateTime         DateTime? @db.DateTime
  ModifyDateTime         DateTime? @db.DateTime
  DataSourceId           Int?
  EhrTypeId              Int?
  EhrInstance            Int?

  @@schema("CMAP")
}

model Measure {
  Id                                         Int         @id(map: "PK_DataQuality_Measure") @default(autoincrement())
  Code                                       String?     @db.VarChar(10)
  Description                                String?     @db.VarChar(255)
  Population                                 String?     @db.VarChar(255)
  DataElement                                String?     @db.VarChar(255)
  DataType                                   String?     @db.VarChar(255)
  EncounterType                              String?     @db.VarChar(5)
  MeasureDefinition                          String?     @db.VarChar(Max)
  Query                                      String?     @db.VarChar(Max)
  TableName                                  String?     @db.VarChar(100)
  FieldName                                  String?     @db.VarChar(100)
  Indicator_Indicator_DenominatorIdToMeasure Indicator[] @relation("Indicator_DenominatorIdToMeasure")
  Indicator_Indicator_NumeratorIdToMeasure   Indicator[] @relation("Indicator_NumeratorIdToMeasure")
  Summary                                    Summary[]

  @@schema("DQ")
}

model MeasureStratification {
  Id                        Int      @id(map: "PK__MeasureS__3214EC07171FA8FE") @default(autoincrement())
  DenominatorQualifyingType String?  @db.VarChar(255)
  StratificationName        String?  @db.VarChar(255)
  QRDA_Active               Boolean?
  EntityType                String?  @db.VarChar(255)
  StratificationField       String?  @db.VarChar(255)

  @@schema("MPRE")
}

model MeasureStratificationDetail {
  Id                          Int     @id(map: "PK__MeasureS__3214EC07E6A603EE") @default(autoincrement())
  DenominatorQualifyingType   String? @db.VarChar(255)
  Stratification              String? @db.VarChar(255)
  StratificationDetail        String? @db.VarChar(255)
  StratificationDetailDisplay String? @db.VarChar(255)

  @@schema("MPRE")
}

model MedicareFacility {
  Id           BigInt  @id(map: "PK_Facility") @default(autoincrement())
  CCN          String? @db.VarChar(6)
  Description  String? @db.VarChar(500)
  FacilityType String? @db.VarChar(100)
  Address      String? @db.VarChar(500)
  City         String? @db.VarChar(100)
  State        String? @db.VarChar(100)
  ZipCode      String? @db.VarChar(100)
  County       String? @db.VarChar(100)
  Phone        String? @db.VarChar(100)
  Latitude     Float?  @db.Real
  Longitude    Float?  @db.Real
  BedSize      Int?

  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_MedicationCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("MedicationCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_MedicationCodes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("MedicationCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_MedicationNegationCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("MedicationNegationCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_MedicationRouteOfAdministrationCodes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("MedicationRouteOfAdministrationCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_MedicationRouteOfAdministrationCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("MedicationRouteOfAdministrationCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_ADM_Medications {
  Id                                BigInt    @default(autoincrement())
  PatientsId                        BigInt
  EncountersId                      BigInt?
  SourceMedicationIdentifier        String?   @db.VarChar(255)
  OrdersId                          BigInt?
  OrderingProviderId                BigInt?
  MedicationCodeId                  BigInt?
  MedicationStatusId                BigInt?
  MedicationStartDateTime           DateTime? @db.DateTime
  MedicationEndDateTime             DateTime? @db.DateTime
  MedicationRouteOfAdministrationId BigInt?
  SourceMedicationDosage            String?   @db.VarChar(255)
  SourceMedicationDosageUnits       String?   @db.VarChar(255)
  IsAdministered                    Boolean?
  NegationCodeId                    BigInt?
  DataSourceId                      Int
  MedicationFrequency               String?   @db.VarChar(255)
  AuthorDateTime                    DateTime? @db.DateTime
  MedicationReason                  String?   @db.VarChar(255)
  RenderingProviderId               BigInt?
  MedicationSupply                  String?   @db.VarChar(255)
  MedicationDaysSupplied            String?   @db.VarChar(255)
  MediationRefills                  String?   @db.VarChar(255)
  PrescribingProviderId             BigInt?
  Recorder                          String?   @db.VarChar(255)
  MedicationCategory                String?   @db.VarChar(255)
  SourcePrimaryKey                  String?   @db.VarChar(500)
  IsActive                          Boolean?
  HashValue                         Bytes?    @db.Binary(16)
  EhrTypeId                         Int?
  EhrInstance                       Int?

  @@map("Medications")
  @@ignore
  @@schema("STG_ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_MedicationStatus {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("MedicationStatus")
  @@ignore
  @@schema("STG_CSRC")
}

model CMAP_MedicationStatus {
  Id               BigInt  @id(map: "PK__Medicati__3214EC079EFBF072") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("MedicationStatus")
  @@schema("CMAP")
}

model MedisolvDashboardReportInsight {
  DashboardName      String  @id(map: "PK_MedisolvDashboardReportInsight") @db.VarChar(255)
  EntityCount        Int?
  MeasureCount       Int?
  EntityType         String? @db.VarChar(255)
  ReportInsightsLink String? @db.NVarChar(Max)
  PostMessageConfig  String? @db.NVarChar(Max)
  ExcludeOrgCode     String? @db.VarChar(255)

  @@schema("MPRE")
}

model MSDRG {
  Id                     BigInt   @id(map: "PK_MSDRG_IdxId") @default(autoincrement())
  Code                   String   @db.VarChar(5)
  Description            String?  @db.VarChar(255)
  Type                   String?  @db.VarChar(5)
  MDCCode                String?  @db.VarChar(3)
  MDCDescription         String?  @db.VarChar(255)
  IsActive               Boolean?
  Version                String   @db.NVarChar(5)
  EffectiveStartDateTime DateTime
  EffectiveEndDateTime   DateTime

  @@schema("MSTD")
}

model MSDRGCodes {
  Id          Int     @id(map: "Pk_AHRQ_MSDRGCodes") @default(autoincrement())
  set_name    String  @db.VarChar(50)
  module      String  @db.VarChar(5)
  code        String  @db.VarChar(50)
  description String? @db.VarChar(255)

  @@index([set_name, code], map: "idx_AHRQ_MSDRGCodes_set_code")
  @@index([code], map: "idx_AHRQ_MSDRGCodes_set_code2")
  @@index([module], map: "idx_AHRQ_MSDRGCodes_set_module")
  @@schema("AHRQ")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MVPMeasuresClean {
  MVP                 String? @db.NVarChar(Max)
  CollectionType      String? @db.NVarChar(Max)
  MeasureGUID         String? @db.NVarChar(Max)
  MeasureType         String? @db.NVarChar(128)
  MeasureName         String? @db.NVarChar(Max)
  MedisolvApplication String? @db.VarChar(15)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MVPMeasuresJustin {
  MVP            String? @db.NVarChar(Max)
  CollectionType String? @db.NVarChar(Max)
  MeasureGUID    String? @db.NVarChar(Max)
  MeasureType    String? @db.NVarChar(128)
  MeasureName    String? @db.NVarChar(Max)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MVPMeasuresWorksheet {
  MVP                  String? @db.NVarChar(Max)
  Measure_Type         String? @map("Measure Type") @db.NVarChar(Max)
  ID_                  String? @map("ID#") @db.NVarChar(Max)
  eCQM_                String? @map("eCQM#") @db.NVarChar(Max)
  MEASURE_TITLE        String? @map("MEASURE TITLE") @db.NVarChar(Max)
  ExistingGUID         String? @db.NVarChar(Max)
  Medisolv_Application String? @map("Medisolv Application") @db.NVarChar(Max)
  Percentile_Data      String? @map("Percentile Data") @db.NVarChar(Max)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MVPMeasuresWorksheetJustin {
  mvp                                                                    String? @db.NVarChar(Max)
  Measure_Type                                                           String? @map("Measure Type") @db.NVarChar(Max)
  ID                                                                     String? @db.NVarChar(Max)
  eCQM_CMSID                                                             String? @map("eCQM CMSID") @db.NVarChar(Max)
  Measure_Name                                                           String? @map("Measure Name") @db.NVarChar(Max)
  Collection_Type                                                        String? @map("Collection Type") @db.NVarChar(Max)
  MapleGUIDEncorQStrataa                                                 String? @db.NVarChar(Max)
  MapleGUIDEncorEStrataA                                                 String? @db.NVarChar(Max)
  MapleGUIDEncorEScoringAdditionalStrata_if_multi_performance_           String? @map("MapleGUIDEncorEScoringAdditionalStrata(if multi-performance)") @db.NVarChar(Max)
  MapleGUIDEncorEDataCompletenessAdditionalStrata_if_multi_performance_  String? @map("MapleGUIDEncorEDataCompletenessAdditionalStrata(if multi-performance)") @db.NVarChar(Max)
  MapleGUIDEncorQScoringAdditionalStrata_if_multi_performance_           String? @map("MapleGUIDEncorQScoringAdditionalStrata(if multi-performance)") @db.NVarChar(Max)
  MapleGUIDEncorQDataCompletenessAdditionalStrata__if_multi_performance_ String? @map("MapleGUIDEncorQDataCompletenessAdditionalStrata (if multi-performance)") @db.NVarChar(Max)
  MultiPerformanceScoringCalculationforBenchmarking                      String? @db.NVarChar(Max)
  MultiPerformanceScoringCalculationforDataCompleteness                  String? @db.NVarChar(Max)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MVPRemoveNonMeasures {
  MVP            String? @db.NVarChar(Max)
  CollectionType String? @db.NVarChar(Max)
  MeasureGUID    String? @db.UniqueIdentifier
  MeasureType    String? @db.NVarChar(128)
  MeasureName    String? @db.NVarChar(Max)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model NPITaxonomy {
  Id   Int?
  NPI  String? @db.VarChar(12)
  Code String? @db.VarChar(12)

  @@ignore
  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model NUCCTaxonomy {
  Id             Int?
  Code           String? @db.VarChar(12)
  Grouping       String? @db.VarChar(255)
  Classification String? @db.VarChar(255)
  Specialization String? @db.VarChar(255)

  @@ignore
  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_OrderCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("OrderCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_OrderNegationCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("OrderNegationCodes")
  @@ignore
  @@schema("STG_CSRC")
}

model STG_ADM_Orders {
  Id                     BigInt    @id(map: "PK_ADM_Orders") @default(autoincrement())
  PatientsId             BigInt
  EncountersId           BigInt?
  OrderingProviderId     BigInt?
  SourceOrderIdentifier  String?   @db.VarChar(255)
  OrderCodeId            BigInt?
  OrderStatusId          BigInt?
  SourceOrderResult      String?   @db.VarChar(255)
  SourceOrderResultUnits String?   @db.VarChar(255)
  NegationCodeId         BigInt?
  DataSourceId           Int
  OrderReason            String?   @db.VarChar(255)
  OrderStartDateTime     DateTime? @db.DateTime
  OrderEndDateTime       DateTime? @db.DateTime
  OrderIntent            String?   @db.VarChar(255)
  AnatomicalLocationSite String?   @db.VarChar(255)
  OrderRank              String?   @db.VarChar(255)
  AuthorDateTime         DateTime? @db.DateTime
  SourcePrimaryKey       String?   @db.VarChar(500)
  IsActive               Boolean?
  HashValue              Bytes?    @db.Binary(16)
  EhrTypeId              Int?
  EhrInstance            Int?

  @@index([SourcePrimaryKey, Id], map: "idx_STG_ADM_Orders")
  @@map("Orders")
  @@schema("STG_ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_OrderStatus {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("OrderStatus")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_PatientEthnicityCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("PatientEthnicityCodes")
  @@ignore
  @@schema("STG_CSRC")
}

model CMAP_PatientEthnicityCodes {
  Id               BigInt  @id(map: "PK__PatientE__3214EC07F599617E") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("PatientEthnicityCodes")
  @@schema("CMAP")
}

model CMAP_PatientGenderCodes {
  Id               BigInt  @id(map: "PK__PatientG__3214EC0746BE76B6") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("PatientGenderCodes")
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_PatientGenderCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("PatientGenderCodes")
  @@ignore
  @@schema("STG_CSRC")
}

model PatientMaritalStatus {
  Id               BigInt  @id(map: "PK__PatientM__3214EC078F8DCBE0") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_PatientMaritalStatusCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("PatientMaritalStatusCodes")
  @@ignore
  @@schema("STG_CSRC")
}

model CMAP_PatientRaceCodes {
  Id               BigInt  @id(map: "PK__PatientR__3214EC0749E2B644") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("PatientRaceCodes")
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_PatientRaceCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("PatientRaceCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_ADM_Patients {
  Id                         BigInt    @id(map: "PK_ADM_Patients") @default(autoincrement())
  SourcePatientIdentifier    String    @db.VarChar(255)
  SourceNationalIdentifier   String?   @db.VarChar(255)
  FirstName                  String    @db.VarChar(255)
  MiddleInitial              String?   @db.VarChar(255)
  LastName                   String    @db.VarChar(255)
  Suffix                     String?   @db.VarChar(50)
  MothersMaidenName          String?   @db.VarChar(255)
  BirthDateTime              DateTime  @db.DateTime
  Address1                   String?   @db.VarChar(255)
  Address2                   String?   @db.VarChar(255)
  City                       String?   @db.VarChar(255)
  StateProvince              String?   @db.VarChar(255)
  ZipPostal                  String?   @db.VarChar(20)
  Phone                      String?   @db.VarChar(50)
  PatientGenderCodeId        Int?      @db.SmallInt
  PatientMaritalStatusCodeId Int?      @db.SmallInt
  PatientRaceCodeId          Int?      @db.SmallInt
  PatientEthnicityCodeId     Int?      @db.SmallInt
  ExpiredDateTime            DateTime? @db.DateTime
  LastUpdateDateTime         DateTime? @db.DateTime
  DisplayPatientIdentifier   String?   @db.VarChar(500)
  DataSourceId               Int
  SourcePrimaryKey           String?   @db.VarChar(500)
  IsActive                   Boolean?
  HashValue                  Bytes?    @db.Binary(16)
  EhrTypeId                  Int?
  EhrInstance                Int?

  @@map("Patients")
  @@ignore
  @@schema("STG_ADM")
}

model Period {
  Id                   BigInt  @id(map: "PK_MSTD_Period") @default(autoincrement())
  Code                 String  @db.VarChar(10)
  Description          String? @db.VarChar(500)
  MinimumCaseThreshold Int?

  @@schema("MSTD")
}

model PresentOnAdmission {
  Id          BigInt  @id(map: "PK_PresentOnAdmission") @default(autoincrement())
  Code        String  @db.VarChar(1)
  Description String? @db.VarChar(500)

  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_ProcedureAnatomicalDirectionCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("ProcedureAnatomicalDirectionCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_ProcedureAnatomicalLocationCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("ProcedureAnatomicalLocationCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_ProcedureCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("ProcedureCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_ProcedureCodes {
  Id               BigInt  @default(autoincrement())
  SourceCode       String? @db.VarChar(255)
  MappedCode       String? @db.VarChar(255)
  MappedCodeSystem String? @db.VarChar(255)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("ProcedureCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_ProcedureNegationCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("ProcedureNegationCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_ProcedureReasonCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("ProcedureReasonCodes")
  @@ignore
  @@schema("STG_CSRC")
}

model CMAP_ProcedureReasonCodes {
  Id               BigInt  @id(map: "PK__Procedur__3214EC07E426E97C") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("ProcedureReasonCodes")
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_ProcedureResultCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("ProcedureResultCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_ProcedureResultCodes {
  Id               BigInt  @id(map: "PK__Procedur__3214EC07A655B567") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("ProcedureResultCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Procedures {
  Id                                 BigInt    @default(autoincrement())
  PatientsId                         BigInt
  EncountersId                       BigInt?
  OrdersId                           BigInt?
  OrderingProviderId                 BigInt?
  ProcedureCodeId                    BigInt?
  ProcedureStatusCodeId              BigInt?
  Ordinality                         String?   @db.VarChar(255)
  ProcedureStartDateTime             DateTime? @db.DateTime
  ProcedureEndDateTime               DateTime? @db.DateTime
  NegationCodeId                     BigInt?
  IncisionDateTime                   DateTime? @db.DateTime
  SourceProcedureResult              String?   @db.VarChar(Max)
  SourceProcedureResultUnits         String?   @db.VarChar(255)
  ProcedureResultCodeId              BigInt?
  ProcedureReasonCodeId              BigInt?
  ProcedureAnatomicalLocationCodeId  BigInt?
  ProcedureAnatomicalDirectionCodeId String?   @db.VarChar(255)
  DataSourceId                       Int
  RenderingProviderId                BigInt?
  FacilitiesId                       BigInt?
  ProcedureResultDateTime            DateTime? @db.DateTime
  ProcedureMethod                    String?   @db.VarChar(255)
  ProcedurePriority                  String?   @db.VarChar(255)
  Recorder                           String?   @db.VarChar(255)
  AuthorDateTime                     DateTime? @db.DateTime
  SourcePrimaryKey                   String?   @db.VarChar(500)
  IsActive                           Boolean?
  HashValue                          Bytes?    @db.Binary(16)
  EhrTypeId                          Int?
  EhrInstance                        Int?

  @@index([SourcePrimaryKey, Id], map: "idx_STG_ADM_Procedures")
  @@ignore
  @@schema("STG_ADM")
}

model CMAP_ProcedureStatus {
  Id               BigInt  @id(map: "PK__Procedur__3214EC07E917C6C3") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("ProcedureStatus")
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_ProcedureStatus {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("ProcedureStatus")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_Providers {
  Id                       BigInt  @default(autoincrement())
  SourceProviderIdentifier String  @db.VarChar(255)
  ProviderNPI              String? @db.VarChar(255)
  ProviderName             String? @db.VarChar(200)
  DataSourceId             Int
  ProviderTaxonomy         String? @db.VarChar(50)
  SourcePrimaryKey         String? @db.VarChar(500)
  EhrTypeId                Int?
  EhrInstance              Int?

  @@map("Providers")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_ADM_Providers {
  Id                       BigInt   @default(autoincrement())
  SourceProviderIdentifier String   @db.VarChar(255)
  ProviderNPI              String?  @db.VarChar(255)
  ProviderName             String?  @db.VarChar(200)
  DataSourceId             Int
  ProviderTaxonomy         String?  @db.VarChar(50)
  SourcePrimaryKey         String?  @db.VarChar(500)
  IsActive                 Boolean?
  HashValue                Bytes?   @db.Binary(16)
  EhrTypeId                Int?
  EhrInstance              Int?

  @@index([SourcePrimaryKey, Id], map: "idx_STG_ADM_Providers")
  @@map("Providers")
  @@ignore
  @@schema("STG_ADM")
}

model PSIArrays {
  Id             Int              @id(map: "PK__PSIArray__3214EC07F4C043C5") @default(autoincrement())
  VersionId      Int
  ArrayName      String           @db.NVarChar(25)
  Description    String?          @db.NVarChar(255)
  Rows           Int
  Columns        Int?
  PSIFormats     CMS_PSIFormats   @relation(fields: [VersionId], references: [Id], onUpdate: NoAction, map: "FK_PSIArrays_VersionId") @ignore
  PSIArrayValues PSIArrayValues[]

  @@schema("CMS")
}

model PSIArrayValues {
  Id           Int       @id(map: "PK__PSIArray__3214EC07EB5F3B7B") @default(autoincrement())
  ArrayId      Int
  RowIndex     Int
  ColumnIndex  Int?
  ElementName  String?   @db.NVarChar(25)
  ElementValue String?   @db.NVarChar(50)
  PSIArrays    PSIArrays @relation(fields: [ArrayId], references: [Id], onUpdate: NoAction, map: "FK_PSIArrayValues_ArrayId")

  @@schema("CMS")
}

model PSICoefficients {
  Id          Int             @id(map: "PK__PSICoeff__3214EC0774744875") @default(autoincrement())
  ModelId     Int
  VersionId   Int
  Variable    String          @db.NVarChar(25)
  Description String?         @db.NVarChar(255)
  Coefficient Float
  PSIModels   CMS_PSIModels   @relation(fields: [ModelId], references: [Id], onUpdate: NoAction, map: "FK_PSICoefficients_ModelId") @ignore
  PSIVersions CMS_PSIVersions @relation(fields: [VersionId], references: [Id], onUpdate: NoAction, map: "FK_PSICoefficients_VersionId")

  @@schema("CMS")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMS_PSIFormats {
  Id              Int                   @id @default(autoincrement())
  VersionId       Int
  Name            String                @db.NVarChar(25)
  Description     String?               @db.NVarChar(255)
  PSIArrays       PSIArrays[]
  PSIVersions     CMS_PSIVersions       @relation(fields: [VersionId], references: [Id], onUpdate: NoAction, map: "FK_PSIFormats_VersionId")
  PSIFormatValues CMS_PSIFormatValues[]

  @@map("PSIFormats")
  @@ignore
  @@schema("CMS")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model AHRQ_PSIFormats {
  Id              Int                    @id(map: "PK__PSIForma__3214EC075DA93989") @default(autoincrement())
  VersionId       Int
  Name            String                 @db.NVarChar(25)
  Description     String?                @db.NVarChar(255)
  PSIFormatValues AHRQ_PSIFormatValues[]

  @@map("PSIFormats")
  @@ignore
  @@schema("AHRQ")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMS_PSIFormatValues {
  Id          Int            @default(autoincrement())
  FormatId    Int
  KeyName     String         @db.NVarChar(50)
  Value       String?        @db.NVarChar(100)
  Description String?        @db.NVarChar(255)
  PSIFormats  CMS_PSIFormats @relation(fields: [FormatId], references: [Id], onUpdate: NoAction, map: "FK_PSIFormatValues_FormatId")

  @@map("PSIFormatValues")
  @@ignore
  @@schema("CMS")
}

model AHRQ_PSIFormatValues {
  Id          Int             @id(map: "PK__PSIForma__3214EC0750E440AB") @default(autoincrement())
  FormatId    Int
  KeyName     String          @db.NVarChar(50)
  Value       String?         @db.NVarChar(100)
  Description String?         @db.NVarChar(255)
  PSIFormats  AHRQ_PSIFormats @relation(fields: [FormatId], references: [Id], onUpdate: NoAction, map: "FK_APSIFormatValues_FormatId") @ignore

  @@map("PSIFormatValues")
  @@schema("AHRQ")
}

model AHRQ_PSIModels {
  Id          Int    @id(map: "PK__PSIModel__3214EC07E97B94A3") @default(autoincrement())
  Model       String @db.NVarChar(15)
  Description String @db.NVarChar(255)

  @@map("PSIModels")
  @@schema("AHRQ")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMS_PSIModels {
  Id              Int               @id(map: "PK__PSIModel__3214EC074FBBF27F") @default(autoincrement())
  Model           String            @db.NVarChar(15)
  Description     String            @db.NVarChar(255)
  PSICoefficients PSICoefficients[]
  PSIWeights      CMS_PSIWeights[]

  @@map("PSIModels")
  @@ignore
  @@schema("CMS")
}

model CMS_PSIVersions {
  Id              Int               @id(map: "PK__PSIVersi__3214EC07EE2C9EE4") @default(autoincrement())
  Version         String            @db.NVarChar(15)
  PSICoefficients PSICoefficients[]
  PSIFormats      CMS_PSIFormats[]  @ignore
  PSIWeights      CMS_PSIWeights[]

  @@map("PSIVersions")
  @@schema("CMS")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model AHRQ_PSIVersions {
  Id      Int    @id(map: "PK__PSIVersi__3214EC0705CF32CA") @default(autoincrement())
  Version String @db.NVarChar(15)

  @@map("PSIVersions")
  @@ignore
  @@schema("AHRQ")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model AHRQ_PSIWeights {
  Id              Int   @id(map: "PK__PSIWeigh__3214EC072DFDDBA6") @default(autoincrement())
  ModelId         Int
  VersionId       Int
  CompositeWeight Float

  @@map("PSIWeights")
  @@ignore
  @@schema("AHRQ")
}

model CMS_PSIWeights {
  Id              Int             @id(map: "PK__PSIWeigh__3214EC07057C001A") @default(autoincrement())
  ModelId         Int
  VersionId       Int
  CompositeWeight Float
  PSIModels       CMS_PSIModels   @relation(fields: [ModelId], references: [Id], onUpdate: NoAction, map: "FK_PSIWeights_ModelId") @ignore
  PSIVersions     CMS_PSIVersions @relation(fields: [VersionId], references: [Id], onUpdate: NoAction, map: "FK_PSIWeights_VersionId")

  @@map("PSIWeights")
  @@schema("CMS")
}

model Race {
  Id          BigInt  @id(map: "PK_Race") @default(autoincrement())
  Code        String  @db.VarChar(20)
  Description String? @db.VarChar(500)
  Level       Int
  Definition  String? @db.VarChar(500)

  @@schema("MSTD")
}

model RevenueCode {
  Id          BigInt  @id(map: "PK_RevenueCode") @default(autoincrement())
  Code        String  @db.VarChar(10)
  Description String? @db.VarChar(500)

  @@schema("MSTD")
}

model RXNORM {
  Id          BigInt  @id(map: "PK_RXNORM") @default(autoincrement())
  Code        String  @db.VarChar(10)
  Description String? @db.VarChar(500)

  @@schema("MSTD")
}

model Setting {
  Id          BigInt        @id(map: "PK_DataQuality_Setting") @default(autoincrement())
  Code        String?       @db.VarChar(255)
  Description String?       @db.VarChar(255)
  Type        String?       @db.VarChar(50)
  UserSetting UserSetting[]

  @@schema("DQ")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseCounties {
  County      String?  @db.VarChar(50)
  CountyGEOID String?  @db.VarChar(30)
  CountyLat   Decimal? @db.Decimal(18, 10)
  CountyLon   Decimal? @db.Decimal(18, 10)

  @@ignore
  @@schema("MPRE")
}

model SisenseDashboards {
  Id                 Int    @id(map: "PK_medisolv_dashboards") @default(autoincrement())
  DashboardId        String @db.NVarChar(255)
  EntityTypeID       Int?
  OrganizationTypeId Int?

  @@schema("MPRE")
}

model SisenseDimDate {
  ReferenceDate DateTime @id(map: "PK__SisenseD__629742D52E133499") @db.Date

  @@schema("MPRE")
}

model SisenseDimRegionZip {
  Id          Int      @id(map: "PK__SisenseD__3214EC07F083FE4A") @default(autoincrement())
  Region      String?  @db.VarChar(255)
  ZipGEOID    Int?
  StateAbbrev String?  @db.VarChar(2)
  RegionLat   Decimal? @db.Decimal(38, 10)
  RegionLon   Decimal? @db.Decimal(38, 10)

  @@index([Region], map: "idx_region_sisensedimregionzip")
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseDimRegionZip_bkp {
  Id          Int      @default(autoincrement())
  Region      String?  @db.VarChar(255)
  ZipGEOID    Int?
  StateAbbrev String?  @db.VarChar(2)
  RegionLat   Decimal? @db.Decimal(38, 10)
  RegionLon   Decimal? @db.Decimal(38, 10)

  @@ignore
  @@schema("MPRE")
}

model SisenseEncounters {
  EncountersId              BigInt    @id(clustered: false, map: "PK_MPRE_Encounters")
  PatientsId                BigInt
  ArrivalDateTime           DateTime? @db.DateTime
  ArrivalLocation           String?   @db.NVarChar(500)
  DischargeDateTime         DateTime? @db.DateTime
  DischargeLocation         String?   @db.NVarChar(500)
  SourceEncounterIdentifier String    @db.VarChar(255)
  CaseIdentifier            String    @db.VarChar(255)
  CCN                       String?   @db.VarChar(6)
  AdmitSource               String?   @db.NVarChar(500)
  AdmitPriority             String?   @db.NVarChar(500)
  AdmitServiceCodeId        BigInt?
  DischargeDisposition      String?   @db.NVarChar(500)
  RegistrationClass         String?   @db.NVarChar(500)
  EncounterBillingClassId   BigInt?
  TotalCharges              Float?    @db.Money
  MSDRG                     BigInt?
  MSDRGDescription          String?   @db.VarChar(500)
  AdmittingName             String?   @db.VarChar(200)
  AttendingName             String?   @db.VarChar(200)
  DischargeName             String?   @db.VarChar(200)
  PrincipalDxCode           String?   @db.VarChar(200)
  PrincipalDxDescription    String?   @db.VarChar(500)
  PrincipalDxSystem         String?   @db.VarChar(500)
  EncounterTypeCode         String?   @db.VarChar(20)
  EncounterTypeText         String?   @db.VarChar(100)
  PrincipalPxCode           String?   @db.VarChar(200)
  PrincipalPxDescription    String?   @db.VarChar(500)
  PrincipalPxSystem         String?   @db.VarChar(500)
  RenderingName             String?   @db.VarChar(200)
  AdmissionDayofWeek        String?   @db.VarChar(25)
  DischargeDayofWeek        String?   @db.VarChar(25)
  EncounterStartDateTime    DateTime? @db.DateTime
  EncounterEndDateTime      DateTime? @db.DateTime
  Facility                  String?   @db.VarChar(200)
  EDArrivalDateTime         DateTime? @db.DateTime
  EDEndDateTime             DateTime? @db.DateTime
  AdmitDateTime             DateTime? @db.DateTime
  InpDischargeDateTime      DateTime? @db.DateTime
  LOS                       Decimal?  @db.Decimal(18, 7)

  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseEncounters_bkp {
  EncountersId              BigInt
  PatientsId                BigInt
  ArrivalDateTime           DateTime? @db.DateTime
  ArrivalLocation           String?   @db.NVarChar(500)
  DischargeDateTime         DateTime? @db.DateTime
  DischargeLocation         String?   @db.NVarChar(500)
  SourceEncounterIdentifier String    @db.VarChar(255)
  CaseIdentifier            String    @db.VarChar(255)
  CCN                       String?   @db.VarChar(6)
  AdmitSource               String?   @db.NVarChar(500)
  AdmitPriority             String?   @db.NVarChar(500)
  AdmitServiceCodeId        BigInt?
  DischargeDisposition      String?   @db.NVarChar(500)
  RegistrationClass         String?   @db.NVarChar(500)
  EncounterBillingClassId   BigInt?
  TotalCharges              Float?    @db.Money
  MSDRG                     BigInt?
  MSDRGDescription          String?   @db.VarChar(500)
  AdmittingName             String?   @db.VarChar(200)
  AttendingName             String?   @db.VarChar(200)
  DischargeName             String?   @db.VarChar(200)
  PrincipalDxCode           String?   @db.VarChar(200)
  PrincipalDxDescription    String?   @db.VarChar(500)
  PrincipalDxSystem         String?   @db.VarChar(500)
  EncounterTypeCode         String?   @db.VarChar(20)
  EncounterTypeText         String?   @db.VarChar(100)
  PrincipalPxCode           String?   @db.VarChar(200)
  PrincipalPxDescription    String?   @db.VarChar(500)
  PrincipalPxSystem         String?   @db.VarChar(500)
  RenderingName             String?   @db.VarChar(200)
  AdmissionDayofWeek        String?   @db.VarChar(25)
  DischargeDayofWeek        String?   @db.VarChar(25)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseEntities_Group {
  Id                        BigInt
  Code                      String   @db.VarChar(100)
  Description               String?  @db.VarChar(255)
  SourceContainerIdentifier String?  @db.NVarChar(255)
  EntityTypeId              Int?     @db.SmallInt
  OrganizationTypeId        Int?     @db.SmallInt
  UseForComparativeData     Boolean?
  Grouping                  String?  @db.VarChar(255)
  Classification            String?  @db.VarChar(255)
  Specialization            String?  @db.VarChar(255)

  @@ignore
  @@schema("MPRE")
}

model SisenseMeasSumm {
  ID                     Int       @id(map: "PK_SisenseMeasSumm") @default(autoincrement())
  MeasureGUID            String    @db.UniqueIdentifier
  Name                   String?   @db.NVarChar(271)
  EntitiesId             BigInt?
  StartDate              DateTime?
  Period                 String    @db.NVarChar(5)
  Numerator              Float?
  Denominator            Float?
  DenominatorOnly        Float?
  NumeratorExclusion     Float?
  DenominatorException   Float?
  DenominatorExclusion   Float?
  PerformanceDenominator Float?
  Performance            Float?
  Expected               Float?
  OE                     Float?
  IPP                    Float?
  P10                    Decimal?  @db.Decimal(10, 2)
  P20                    Decimal?  @db.Decimal(10, 2)
  P25                    Decimal?  @db.Decimal(10, 2)
  P30                    Decimal?  @db.Decimal(10, 2)
  P40                    Decimal?  @db.Decimal(10, 2)
  P50                    Decimal?  @db.Decimal(10, 2)
  P60                    Decimal?  @db.Decimal(10, 2)
  P70                    Decimal?  @db.Decimal(10, 2)
  P75                    Decimal?  @db.Decimal(10, 2)
  P80                    Decimal?  @db.Decimal(10, 2)
  P90                    Decimal?  @db.Decimal(10, 2)
  P100                   Decimal?  @db.Decimal(10, 2)
  P50Y                   Decimal?  @db.Decimal(10, 2)
  PTile                  Float?
  PercentileSourceId     Int?
  Points                 Float?
  Organization           String?   @db.NVarChar(271)
  NumeratorValue         Float?
  DenominatorValue       Float?
  SummaryDataRowCount    Int?

  @@index([Period], map: "idx_period_meassumm")
  @@index([MeasureGUID, EntitiesId, StartDate, Period], map: "idx_period_percentiles")
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseMeasSumm_bkp {
  ID                     Int       @default(autoincrement())
  MeasureGUID            String    @db.UniqueIdentifier
  Name                   String?   @db.NVarChar(271)
  EntitiesId             BigInt?
  StartDate              DateTime?
  Period                 String    @db.NVarChar(5)
  Numerator              Float?
  Denominator            Float?
  DenominatorOnly        Float?
  NumeratorExclusion     Float?
  DenominatorException   Float?
  DenominatorExclusion   Float?
  PerformanceDenominator Float?
  Performance            Float?
  Expected               Float?
  OE                     Float?
  IPP                    Float?
  P10                    Decimal?  @db.Decimal(10, 2)
  P20                    Decimal?  @db.Decimal(10, 2)
  P25                    Decimal?  @db.Decimal(10, 2)
  P30                    Decimal?  @db.Decimal(10, 2)
  P40                    Decimal?  @db.Decimal(10, 2)
  P50                    Decimal?  @db.Decimal(10, 2)
  P60                    Decimal?  @db.Decimal(10, 2)
  P70                    Decimal?  @db.Decimal(10, 2)
  P75                    Decimal?  @db.Decimal(10, 2)
  P80                    Decimal?  @db.Decimal(10, 2)
  P90                    Decimal?  @db.Decimal(10, 2)
  P100                   Decimal?  @db.Decimal(10, 2)
  P50Y                   Decimal?  @db.Decimal(10, 2)
  PTile                  Float?
  PercentileSourceId     Int?
  Points                 Float?
  Organization           String?   @db.NVarChar(271)

  @@ignore
  @@schema("MPRE")
}

model SisenseMeasSummStrat {
  Id                        BigInt   @default(autoincrement())
  MeasureGUID               String   @db.UniqueIdentifier
  Stratification            String?  @db.NVarChar(255)
  StratificationDetail      String?  @db.NVarChar(255)
  EntitiesId                BigInt?
  StartDate                 DateTime @db.DateTime
  EndDate                   DateTime @db.DateTime
  Period                    String   @db.NVarChar(5)
  Numerator                 Float?
  Denominator               Float?
  DenominatorOnly           Float?
  NumeratorExclusion        Float?
  DenominatorExclusion      Float?
  DenominatorException      Float?
  Performance               Float?
  NumeratorValue            Float?
  DenominatorValue          Float?
  IPP                       Float?
  PerformanceDenominator    BigInt?
  RadiusDenominator         BigInt?
  SourceContainerIdentifier String?  @db.NVarChar(255)
  PartitionKey              String   @db.NVarChar(261)

  @@id([PartitionKey, Id], map: "strat_pk")
  @@schema("MPRE")
}

model SisenseMeasures {
  Id                         Int       @id(map: "PK__SisenseM__3214EC071641DCDB") @default(autoincrement())
  MedisolvMeasureId          String?   @db.UniqueIdentifier
  DenominatorQualifyingType  String?   @db.VarChar(255)
  StratifcationDescription   String?   @db.VarChar(500)
  Stratification             Boolean?
  MeasureSubId               String?   @db.VarChar(255)
  MeasureScoring             String?   @db.VarChar(255)
  InverseMeasureFlag         Boolean?
  MedisolvApplication        String?   @db.VarChar(255)
  DataCompletenessRequired   Boolean?
  DataCompletenessThreshold  Float?
  CalculationFactor          Int?
  DecimalPointsCount         Float?
  Name                       String?   @db.NVarChar(255)
  Cohort                     String?   @db.VarChar(255)
  Outcome                    String?   @db.VarChar(255)
  EncounterType              String?   @db.VarChar(255)
  MeasureType                String?   @db.VarChar(255)
  HighIsGood                 Int?
  IgnoreMinimumcaseThreshold Boolean?
  LastUpdateDateTime         DateTime? @db.DateTime
  IsActive                   Boolean?
  StoredProcedureName        String?   @db.NVarChar(200)
  SmallestInterval           String?   @db.VarChar(50)
  Long_Measure_Name          String?   @map("Long Measure Name") @db.VarChar(1000)
  MedianScoring              Int
  MeasureScoringFilter       String?   @db.VarChar(255)
  MeasureMessage             String    @db.VarChar(58)
  NumeratorValueOnly         Int?
  RatioMeasure               Int
  CompositeMeasCNT           Int?
  NullRate                   Int?
  NullIPP                    Int?
  NullDenomOnly              Int?
  NullDenExcl                Int?
  NullNumerator              Int?
  NullDenominator            Int?
  NullException              Int?
  NullNumExcl                Int?
  NoDrill                    Int?
  MeanScoring                Int?
  NumeratorValueScoring      Int?
  EquityStrata               String?   @db.VarChar(500)

  @@index([Long_Measure_Name], map: "idx_longmeasurename")
  @@schema("MPRE")
}

model SisensePatientRank {
  Id          Int     @id(map: "PK__SisenseP__3214EC078348BC2F") @default(autoincrement())
  EMSId       BigInt?
  PatientRank Int

  @@index([EMSId], map: "idx_emsid_ptrank")
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisensePatientRank_bkp {
  Id          Int    @default(autoincrement())
  EMSId       BigInt
  PatientRank Int

  @@ignore
  @@schema("MPRE")
}

model SisensePatients {
  Id                       BigInt    @id(clustered: false, map: "PK_MPRE_Patients")
  SourcePatientIdentifier  String    @db.VarChar(255)
  SourceNationalIdentifier String?   @db.VarChar(255)
  FirstName                String    @db.VarChar(255)
  MiddleInitial            String?   @db.VarChar(255)
  AgeToday                 Int?
  LastName                 String    @db.VarChar(255)
  Suffix                   String?   @db.VarChar(255)
  MothersMaidenName        String?   @db.VarChar(255)
  BirthDateTime            DateTime  @db.DateTime
  Address1                 String?   @db.VarChar(255)
  Address2                 String?   @db.VarChar(255)
  City                     String?   @db.VarChar(255)
  StateProvince            String?   @db.VarChar(255)
  ZipPostal                String?   @db.VarChar(20)
  ShortZip                 String?   @db.VarChar(5)
  Phone                    String?   @db.VarChar(50)
  Gender                   String?   @db.NVarChar(500)
  MaritalStatus            String?   @db.NVarChar(500)
  Race                     String?   @db.NVarChar(500)
  Ethnicity                String?   @db.NVarChar(500)
  ExpiredDateTime          DateTime? @db.DateTime
  DisplayPatientIdentifier String?   @db.VarChar(255)
  PatientName              String    @db.VarChar(511)
  ZipGEOID                 Int?
  County                   String?   @db.VarChar(50)
  CountyGEOID              Int?
  Region                   String?   @db.VarChar(255)
  CountyLat                Decimal?  @db.Decimal(18, 10)
  CountyLon                Decimal?  @db.Decimal(18, 10)
  Ziplat                   Decimal?  @db.Decimal(18, 10)
  ZipLon                   Decimal?  @db.Decimal(18, 10)
  RegionLat                Decimal?  @db.Decimal(18, 10)
  RegionLon                Decimal?  @db.Decimal(18, 10)

  @@index([County], map: "idx_SisensePatients_County")
  @@index([CountyLat], map: "idx_sisensepatients_countylat")
  @@index([Region], map: "idx_sisensepatients_region")
  @@index([Region], map: "idx_sisensepatients_region_county")
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisensePatients_bkp {
  Id                       BigInt
  SourcePatientIdentifier  String    @db.VarChar(255)
  SourceNationalIdentifier String?   @db.VarChar(255)
  FirstName                String    @db.VarChar(255)
  MiddleInitial            String?   @db.VarChar(255)
  AgeToday                 Int?
  LastName                 String    @db.VarChar(255)
  Suffix                   String?   @db.VarChar(255)
  MothersMaidenName        String?   @db.VarChar(255)
  BirthDateTime            DateTime  @db.DateTime
  Address1                 String?   @db.VarChar(255)
  Address2                 String?   @db.VarChar(255)
  City                     String?   @db.VarChar(255)
  StateProvince            String?   @db.VarChar(255)
  ZipPostal                String?   @db.VarChar(20)
  ShortZip                 String?   @db.VarChar(5)
  Phone                    String?   @db.VarChar(50)
  Gender                   String?   @db.NVarChar(500)
  MaritalStatus            String?   @db.NVarChar(500)
  Race                     String?   @db.NVarChar(500)
  Ethnicity                String?   @db.NVarChar(500)
  ExpiredDateTime          DateTime? @db.DateTime
  DisplayPatientIdentifier String?   @db.VarChar(255)
  PatientName              String    @db.VarChar(511)
  ZipGEOID                 Int?
  County                   String?   @db.VarChar(50)
  CountyGEOID              Int?
  Region                   String?   @db.VarChar(255)
  CountyLat                Decimal?  @db.Decimal(18, 10)
  CountyLon                Decimal?  @db.Decimal(18, 10)
  Ziplat                   Decimal?  @db.Decimal(18, 10)
  ZipLon                   Decimal?  @db.Decimal(18, 10)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisensePerformanceGoalsStrat {
  Id                     Int       @id(map: "PK_SisensePerformanceGoalsStrat") @default(autoincrement())
  MeasureGUID            String    @db.UniqueIdentifier
  Stratification         String?   @db.NVarChar(255)
  StratificationDetail   String?   @db.NVarChar(255)
  EntitiesId             BigInt?
  GoalLower              Decimal?  @db.Decimal(15, 2)
  Performance            Float?
  GoalStartDate          DateTime?
  LowerGoalMissed        Int
  Period                 String    @db.NVarChar(5)
  PerformanceDenominator Float?
  YellowZone             Decimal?  @db.Decimal(15, 2)

  @@index([LowerGoalMissed, GoalStartDate], map: "idx_goals_lowergoalmissed")
  @@index([Stratification, StratificationDetail, PerformanceDenominator], map: "idx_stratification_goals")
  @@ignore
  @@schema("MPRE")
}

model SisensePeriod {
  Id     Int    @id(map: "PK_SisensePeriod") @default(autoincrement())
  Period String @db.VarChar(1)

  @@schema("MPRE")
}

model SisenseRegionDetail {
  Id                     Int       @id(clustered: false, map: "PK_id") @default(autoincrement())
  MeasureGUID            String?   @db.UniqueIdentifier
  CategoryAssignmentsId  Int?      @db.SmallInt
  MeasureTallyIdentifier String?   @db.NVarChar(255)
  PatientsId             BigInt?
  NumeratorValue         Decimal?  @db.Decimal(18, 10)
  ReferenceDate          DateTime?
  EntityId               BigInt?
  Region                 String?   @db.VarChar(255)
  FalloutIndicator       Int?
  ReferenceDay           DateTime? @db.Date
  DenominatorValue       Decimal?  @db.Decimal(18, 10)
  MedianScoring          Int?

  @@index([EntityId], map: "idx_entityid_sisenseregiondetail")
  @@index([MeasureGUID, ReferenceDay], map: "idx_measureguid_referenceday_sisenseregiondetail")
  @@index([MeasureGUID], map: "idx_measureguid_sisenseregiondetail")
  @@index([ReferenceDay], map: "idx_referenceday_sisenseregiondetail")
  @@index([ReferenceDay], map: "idx_referenceday_sisenseRegionDetail_All")
  @@index([ReferenceDay], map: "idx_referenceday_sisenseregiondetail_numvalue")
  @@index([Region], map: "idx_region_sisenseregiondetail")
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseRegionDetail_bkp {
  Id                     Int       @default(autoincrement())
  MeasureGUID            String?   @db.UniqueIdentifier
  CategoryAssignmentsId  Int?      @db.SmallInt
  MeasureTallyIdentifier String?   @db.NVarChar(255)
  PatientsId             BigInt?
  NumeratorValue         Decimal?  @db.Decimal(18, 10)
  ReferenceDate          DateTime?
  EntityId               BigInt?
  Region                 String?   @db.VarChar(255)
  FalloutIndicator       Int?
  ReferenceDay           DateTime? @db.Date
  DenominatorValue       Decimal?  @db.Decimal(18, 10)
  MedianScoring          Int?

  @@ignore
  @@schema("MPRE")
}

model SisenseRegionDetailMap {
  Id                     Int       @id(map: "PK__SisenseR__3214EC074478F32A") @default(autoincrement())
  MeasureGUID            String?   @db.UniqueIdentifier
  Numerator              Int?
  PerformanceDenominator Int?
  NumeratorValue         Int?
  ReferenceDay           DateTime? @db.Date
  EntityId               BigInt?
  Region                 String?   @db.VarChar(255)
  StateAbbrev            String?   @db.VarChar(2)
  MeasureTallyIdentifier String?   @db.VarChar(255)
  RadiusDen              BigInt?
  ReferenceDate          DateTime?
  DenominatorValue       Decimal?  @db.Decimal(18, 10)
  MedianScoring          Int?

  @@index([MeasureGUID, ReferenceDay], map: "idx_measureguid_referencedat_sisenseregdetailmap")
  @@index([MeasureGUID, ReferenceDay], map: "idx_measureguid_referenceday_detailmap")
  @@index([EntityId, ReferenceDay], map: "idx_measureguid_referenceday_entityid")
  @@index([ReferenceDay], map: "idx_measureguid_referenceday_radius")
  @@index([MeasureGUID, ReferenceDay], map: "idx_measureguid_referenceday_sisenseregiondetail_")
  @@index([ReferenceDay, MedianScoring], map: "idx_medianscoring_sisenseregiondetailmap")
  @@index([ReferenceDay], map: "idx_referencedate_sisenseregiondetailmap")
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseRegionDetailMap_bkp {
  Id                     Int       @default(autoincrement())
  MeasureGUID            String?   @db.UniqueIdentifier
  Numerator              Int?
  PerformanceDenominator Int?
  NumeratorValue         Int?
  ReferenceDay           DateTime? @db.Date
  EntityId               BigInt?
  Region                 String?   @db.VarChar(255)
  StateAbbrev            String?   @db.VarChar(2)
  MeasureTallyIdentifier String?   @db.VarChar(255)
  RadiusDen              BigInt?
  ReferenceDate          DateTime?
  DenominatorValue       Decimal?  @db.Decimal(18, 10)
  MedianScoring          Int?

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseSPC {
  Value              Float?
  LowerLimit         Float?
  CenterLine         Float?
  UpperLimit         Float?
  LowerWarning       Float?
  UpperWarning       Float?
  Comment            String?   @db.NVarChar(4000)
  ControlChartType   String?   @db.NVarChar(20)
  EntityName         String?   @db.VarChar(255)
  MeasureGUID        String?   @db.UniqueIdentifier
  Period             String    @db.NVarChar(5)
  SuppressionReason  String?   @db.NVarChar(500)
  StartDate          DateTime? @db.DateTime
  RuleViolationCount Int?
  RuleViolations     String?   @db.NVarChar(4000)
  PeriodFormatted    String?   @db.NVarChar(9)
  CCDPrimaryKey      BigInt?
  UserComment        String?   @db.VarChar(500)
  Exclude            Int?
  NewPhase           Int?
  SisenseURL         String?   @db.VarChar(8000)
  DataPointCount     Int?

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseSPC_BKP {
  Value              Float?
  LowerLimit         Float?
  CenterLine         Float?
  UpperLimit         Float?
  LowerWarning       Float?
  UpperWarning       Float?
  Comment            String?   @db.NVarChar(4000)
  ControlChartType   String?   @db.NVarChar(20)
  EntityName         String?   @db.VarChar(255)
  MeasureGUID        String?   @db.UniqueIdentifier
  Period             String    @db.NVarChar(5)
  SuppressionReason  String?   @db.NVarChar(500)
  StartDate          DateTime? @db.DateTime
  RuleViolationCount Int?
  RuleViolations     String?   @db.NVarChar(4000)
  PeriodFormatted    String?   @db.NVarChar(9)
  CCDPrimaryKey      BigInt?
  UserComment        String?   @db.VarChar(500)
  Exclude            Int?
  NewPhase           Int?
  SisenseURL         String?   @db.VarChar(8000)
  DataPointCount     Int?

  @@ignore
  @@schema("MPRE")
}

model SisenseStratDrillDown {
  Id                   Int     @id(clustered: false, map: "PK_MPRE_Strat_enc_Drilldown") @default(autoincrement())
  PatientsId           BigInt
  EncountersId         BigInt?
  Stratification       String? @db.VarChar(100)
  StratificationDetail String? @db.VarChar(100)

  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseStratPerformanceGoals {
  MeasureGUID          String   @db.UniqueIdentifier
  Stratification       String?  @db.NVarChar(255)
  StratificationDetail String?  @db.NVarChar(255)
  EntitiesId           BigInt?
  GoalLower            Decimal? @db.Decimal(15, 2)
  Performance          Float?
  GoalStartDate        DateTime @db.DateTime
  LowerGoalMissed      Int
  Period               String   @db.NVarChar(5)

  @@index([GoalStartDate], map: "idx_perfgoals_startdate")
  @@ignore
  @@schema("MPRE")
}

model SNOMED {
  Id                     BigInt    @id(map: "PK_SNOMED") @default(autoincrement())
  Code                   String    @db.VarChar(20)
  Description            String?   @db.VarChar(500)
  EffectiveStartDateTime DateTime?
  IsActive               Boolean?
  EffectiveEndDateTime   DateTime?

  @@schema("MSTD")
}

model Summary {
  Id            BigInt          @id(map: "PK_DataQuality_Summary") @default(autoincrement())
  MeasureId     Int
  FacilityId    Int
  StartDateTime DateTime
  EndDateTime   DateTime
  Period        String          @db.VarChar(1)
  Value         Int?
  Measure       Measure         @relation(fields: [MeasureId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__Summary__Measure__51DA19CB")
  SummaryDetail SummaryDetail[]

  @@schema("DQ")
}

model SummaryDetail {
  Id          BigInt  @id(map: "PK_DataQuality_SummaryDetail") @default(autoincrement())
  EncounterId BigInt
  SummaryId   BigInt
  Summary     Summary @relation(fields: [SummaryId], references: [Id], onUpdate: NoAction, map: "FK__SummaryDe__Summa__54B68676")

  @@schema("DQ")
}

model UserSetting {
  Id                BigInt   @id(map: "PK_DataQuality_UserSetting") @default(autoincrement())
  Description       String?  @db.VarChar(255)
  ApplicationUserId String?  @db.VarChar(200)
  SettingId         BigInt?
  Value             String?  @db.VarChar(100)
  Setting           Setting? @relation(fields: [SettingId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__UserSetti__Setti__52CE3E04")

  @@schema("DQ")
}

model ValueSetMappings {
  Id                     BigInt    @id(map: "PK__ValueSet__3214EC078D298539") @default(autoincrement())
  ValueSetId             Int
  Code                   String    @db.VarChar(100)
  Description            String?   @db.VarChar(255)
  Taxonomy               String?   @db.VarChar(100)
  EffectiveDateTimeStart DateTime?
  EffectiveDateTimeEnd   DateTime?
  ValueSets              ValueSets @relation(fields: [ValueSetId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__ValueSetM__Value__7CC477D0")

  @@schema("MTOOL")
}

model ValueSets {
  Id                     Int                @id(map: "PK__ValueSet__3214EC07D1F7B4A9") @default(autoincrement())
  ValueSetCode           String             @db.VarChar(100)
  ValueSetDescription    String             @db.VarChar(255)
  TableName              String             @db.VarChar(50)
  FieldName              String             @db.VarChar(100)
  DisplayFieldName       String             @db.VarChar(255)
  MapTableName           String             @db.VarChar(50)
  CDMTableName           String             @db.VarChar(50)
  CDMFieldName           String             @db.VarChar(100)
  ADMTableName           String             @db.VarChar(50)
  ADMFieldName           String             @db.VarChar(100)
  CSRCTableName          String             @db.VarChar(50)
  CMAPTableName          String             @db.VarChar(50)
  BaseTableName          String             @db.VarChar(50)
  CompletenessThreshold  Float
  DenominatorType        String             @db.VarChar(50)
  Version                String             @db.VarChar(10)
  EffectiveDateTimeStart DateTime           @default(dbgenerated("getutcdate()"), map: "DF__ValueSets__Effec__737017C0")
  EffectiveDateTimeEnd   DateTime?
  IsSystem               Boolean            @default(false, map: "DF__ValueSets__IsSys__74643BF9")
  IsActive               Boolean            @default(true, map: "DF__ValueSets__IsAct__727BF387")
  IncompleteOverride     Boolean            @default(false, map: "DF__ValueSets__Incom__75586032")
  ApplicationUsername    String?            @db.VarChar(255)
  LastUpdateDateTime     DateTime?          @db.DateTime
  ValueSetMappings       ValueSetMappings[]

  @@schema("MTOOL")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_VitalCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("VitalCodes")
  @@ignore
  @@schema("STG_CSRC")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model CMAP_VitalCodes {
  Id               BigInt  @id(map: "PK__VitalCod__3214EC076CF83B67") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("VitalCodes")
  @@ignore
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_VitalNegationCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("VitalNegationCodes")
  @@ignore
  @@schema("STG_CSRC")
}

model CMAP_VitalReasonCodes {
  Id               BigInt  @id(map: "PK__VitalRea__3214EC07B6878A2C") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("VitalReasonCodes")
  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_VitalReasonCodes {
  Id                BigInt  @default(autoincrement())
  SourceCode        String  @db.NVarChar(500)
  SourceDescription String? @db.NVarChar(500)
  SourceCodeSystem  String? @db.NVarChar(500)
  DataSourceId      Int
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("VitalReasonCodes")
  @@ignore
  @@schema("STG_CSRC")
}

model VitalResultCodes {
  Id               BigInt  @id(map: "PK__VitalRes__3214EC07D46D1018") @default(autoincrement())
  SourceCode       String? @db.VarChar(8000)
  MappedCode       String? @db.VarChar(8000)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@schema("CMAP")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Vitals {
  Id                     BigInt    @default(autoincrement())
  PatientsId             BigInt
  EncountersId           BigInt?
  VitalCodeId            BigInt?
  VitalStatusCodeId      BigInt?
  VitalStartDateTime     DateTime? @db.DateTime
  VitalEndDateTime       DateTime? @db.DateTime
  VitalReasonCodeId      BigInt?
  SourceVitalResult      String?   @db.VarChar(8000)
  SourceVitalResultUnits String?   @db.VarChar(255)
  NegationCodeId         BigInt?
  DataSourceId           Int
  SourcePrimaryKey       String?   @db.VarChar(500)
  IsActive               Boolean?
  HashValue              Bytes?    @db.Binary(16)
  EhrTypeId              Int?
  EhrInstance            Int?

  @@index([SourcePrimaryKey, Id], map: "idx_STG_ADM_Vitals")
  @@ignore
  @@schema("STG_ADM")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model STG_CSRC_VitalStatus {
  Id                BigInt  @default(autoincrement())
  SourceCode        String? @db.NVarChar(Max)
  SourceDescription String? @db.NVarChar(Max)
  SourceCodeSystem  String? @db.NVarChar(Max)
  DataSourceId      Int?
  EhrTypeId         Int?
  EhrInstance       Int?

  @@map("VitalStatus")
  @@ignore
  @@schema("STG_CSRC")
}

model CMAP_VitalStatus {
  Id               BigInt  @id(map: "PK__VitalSta__3214EC079FCA2FAE") @default(autoincrement())
  SourceCode       String? @db.VarChar(500)
  MappedCode       String? @db.VarChar(500)
  MappedCodeSystem String? @db.VarChar(1000)
  DataSourceId     Int?
  EhrTypeId        Int?
  EhrInstance      Int?

  @@map("VitalStatus")
  @@schema("CMAP")
}

model VitalType {
  Id          Int     @id(map: "PK_Master_VitalType_Id") @default(autoincrement())
  Code        String? @db.VarChar(50)
  Description String? @db.VarChar(255)

  @@schema("MSTD")
}

model WorkList {
  Id                BigInt    @id(map: "PK_DataQuality_WorkList") @default(autoincrement())
  ApplicationUserId String?   @db.VarChar(200)
  CCN               String    @db.VarChar(6)
  IndicatorId       Int
  AlertMessage      String?   @db.VarChar(500)
  AlertDate         DateTime?
  TPStatus          String?   @db.VarChar(50)
  Indicator         Indicator @relation(fields: [IndicatorId], references: [Id], onDelete: Cascade, onUpdate: NoAction, map: "FK__WorkList__Indica__53C2623D")

  @@schema("DQ")
}

model ZipCodesGeo {
  Id          Int      @id(map: "PK_ZipCodesGeo") @default(autoincrement())
  ZipCode     String?  @db.VarChar(20)
  ZipGEOID    Int?
  ZipLat      Decimal? @db.Decimal(18, 10)
  ZipLon      Decimal? @db.Decimal(18, 10)
  City        String?  @db.VarChar(50)
  State       String?  @db.VarChar(50)
  StateAbbrev String?  @db.VarChar(2)
  County      String?  @db.VarChar(50)
  CountyGEOID Int?
  CountyLat   Decimal? @db.Decimal(18, 10)
  CountyLon   Decimal? @db.Decimal(18, 10)

  @@index([ZipCode], map: "idx_zipcode")
  @@schema("MSTD")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MPRE_ActiveMeasures {
  Id                  BigInt    @default(autoincrement())
  MeasureGUID         String    @db.UniqueIdentifier
  MeasureSubId        String?   @db.NVarChar(20)
  EntitiesId          BigInt?
  OnDate              DateTime? @db.DateTime
  ProcessingStartDate DateTime? @db.DateTime
  LastUpdateDateTime  DateTime? @db.DateTime
  IsMIPSMeasure       Boolean?

  @@map("ActiveMeasures")
  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EntityRollUp {
  EntityId        BigInt?
  EntityId_RollUp BigInt?
  RollUpType      String? @db.NVarChar(50)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EntityTaxonomy {
  Id             BigInt
  Grouping       String? @db.VarChar(255)
  Classification String? @db.VarChar(255)
  Specialization String? @db.VarChar(255)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model EntityUniverse {
  OrganizationTypeId Int?
  EntityTypeId       Int?
  Universe           String? @db.NVarChar(50)

  @@ignore
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MCA_bkp_9302024 {
  Id                        BigInt    @default(autoincrement())
  SourceContainerIdentifier String?   @db.NVarChar(255)
  MeasureGUID               String?   @db.UniqueIdentifier
  MeasureSubId              String?   @db.NVarChar(20)
  PatientsId                BigInt?
  EncountersId              BigInt?
  MeasureTallyIdentifier    String?   @db.NVarChar(255)
  ReferenceDate             DateTime? @db.DateTime
  CategoryAssignmentsId     Int       @db.SmallInt
  NumeratorValue            Decimal?  @db.Decimal(18, 10)
  DenominatorValue          Decimal?  @db.Decimal(18, 10)
  Note                      String?   @db.NVarChar(500)
  IsGenerated               Boolean?

  @@ignore
  @@schema("MECA")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseMeasSummGeo {
  Id                        BigInt   @default(autoincrement())
  MeasureGUID               String   @db.UniqueIdentifier
  Stratification            String?  @db.NVarChar(255)
  StratificationDetail      String?  @db.NVarChar(255)
  EntitiesId                BigInt?
  StartDate                 DateTime @db.DateTime
  EndDate                   DateTime @db.DateTime
  Period                    String   @db.NVarChar(5)
  Numerator                 Float?
  Denominator               Float?
  DenominatorOnly           Float?
  NumeratorExclusion        Float?
  DenominatorExclusion      Float?
  DenominatorException      Float?
  Performance               Float?
  NumeratorValue            Float?
  DenominatorValue          Float?
  IPP                       Float?
  PerformanceDenominator    BigInt?
  RadiusDenominator         BigInt?
  SourceContainerIdentifier String?  @db.NVarChar(255)
  PartitionKey              String   @db.NVarChar(261)

  @@ignore
  @@schema("MPRE")
}

model SisensePerformanceGoalsGeo {
  Id                     Int       @id(map: "PK_SisensePerformanceGoalsGeo") @default(autoincrement())
  MeasureGUID            String    @db.UniqueIdentifier
  Stratification         String?   @db.NVarChar(255)
  StratificationDetail   String?   @db.NVarChar(255)
  EntitiesId             BigInt?
  GoalLower              Decimal?  @db.Decimal(15, 2)
  Performance            Float?
  GoalStartDate          DateTime?
  LowerGoalMissed        Int
  Period                 String    @db.NVarChar(5)
  PerformanceDenominator Float?
  YellowZone             Decimal?  @db.Decimal(15, 2)

  @@index([LowerGoalMissed, GoalStartDate], map: "idx_goals_lowergoalmissed_geo")
  @@index([Stratification, StratificationDetail, PerformanceDenominator], map: "idx_stratification_goals_geo")
  @@schema("MPRE")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SisenseStratMeasureTally {
  MeasureTallyIdentifier String? @db.NVarChar(255)
  PatientsId             BigInt?
  EncountersId           BigInt?

  @@ignore
  @@schema("MPRE")
}
