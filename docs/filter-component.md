# Filter Component Technical Documentation

## Overview

The Filter component is a complex filtering system that manages multiple types of filters (measures, providers, submission groups, etc.) and integrates with Zustand for state management. It supports saving filter configurations and real-time updates of filtered results.

## Core Components

### Main Filter Component (`src/components/filter/index.tsx`)

- Serves as the container for all filter-related functionality
- Manages filter panel visibility state (`isFilterOpen`)
- Handles initial data loading through TRPC queries
- Coordinates different filter sections based on measure type (Ambulatory vs Hospital)

### State Management Architecture

#### Zustand Store Structure (`src/stores/filter.ts`)

The filter store maintains several key state pieces:

```typescript
type State = {
  // Currently selected but not yet applied filters
  checkedMeasures: MeasureFilterModel[]
  checkedSubOrganizations: GroupsnSubOrgs[]
  checkedSubmissionGroups: SubmissionGroup[]
  checkedProviders: Provider[]

  // Applied filters that affect the results
  appliedFilters: {
    measures: string[]
    subOrganizations: string[]
    submissionGroups: string[]
    providers: string[]
  }

  // Available options for each filter type
  measures: MeasureFilterModel[]
  subOrganizations: GroupsnSubOrgs[]
  submissionGroups: SubmissionGroup[]
  providers: Provider[]

  // UI state
  expandedSections: string[]
  hideEmptyIndicators: boolean
}
```

The filter system uses multiple Zustand stores:

- `useFilterStore`: Manages filter selections and states
- `useViewStore`: Handles view-related settings
- `useUserSessionStore`: Manages user session data including measure types

### Key Filter Sections

1. **Saved Filters** (`SavedFiltersSection`)

   - Allows users to save and load filter configurations
   - Stores configurations in backend (DB or blob storage for partners)

2. **Measures** (`MeasuresSection`)

   - Handles measure-specific filtering
   - Supports search and selection

3. **Context-Specific Sections**

   - For Ambulatory Measures:
     - `SubmissionGroupsSection`
     - `ProvidersSection`
   - For Hospital Measures:
     - `HospitalCCNGroupsSection`

4. **Chip Sections**
   - Display active filters as chips
   - Allow quick removal of applied filters
   - Include separate sections for different filter types

## Data Flow and State Management

### Initial Data Loading

```typescript
const [
  measuresQuery,
  savedFiltersQuery,
  subOrganizationsQuery,
  providersQuery,
  submissionGroupQuery,
] = api.useQueries((t) => [
  t.measures.get({ primaryMeasureType }),
  t.filters.getSavedFilters({ path: pathname, primaryMeasureType }),
  t.organizations.getSubOrganizations(),
  t.providers.get({ primaryMeasureType }),
  t.submissionGroups.get(),
])
```

### Filter Application Flow

1. **Selection Stage**

   - Users select filters in various sections
   - Selections update the `checked*` states in Zustand

   ```typescript
   addProvider: (provider) => {
     set((state) => ({
       checkedProviders: [
         ...state.checkedProviders,
         { id: provider.npi, label: provider.providerName },
       ],
     }))
   }
   ```

2. **Application Stage**

   - When user clicks "Apply", the `applyFilters` action is triggered
   - Converts checked selections into applied filter IDs

   ```typescript
   applyFilters: () => {
     set((state) => ({
       appliedFilters: {
         measures:
           state.checkedMeasures.length === state.measures.length
             ? ['*']
             : state.checkedMeasures.map((measure) => measure.id),
         // Similar pattern for other filter types...
       },
     }))
   }
   ```

3. **Results Update**
   - Applied filters trigger result recalculation through TRPC
   - Results are stored in separate Zustand store (`useMeasureResultsStore`)

### Filter Operations

#### Adding/Removing Individual Filters

```typescript
// Adding a filter
addMeasure: (measure) => {
  set((state) => ({
    checkedMeasures: [
      ...state.checkedMeasures,
      { id: measure.id, label: measure.name },
    ],
  }))
}

// Removing a filter
removeMeasure: (measureId) => {
  set((state) => ({
    checkedMeasures: state.checkedMeasures.filter(
      (measure) => measure.id !== measureId
    ),
  }))
}
```

#### Bulk Operations

```typescript
// Adding all filters
addAllMeasures: (ids) => {
  set((state) => ({
    checkedMeasures:
      ids && ids.length > 0 && !ids.includes('*')
        ? state.measures.filter((measure) => ids.includes(measure.id))
        : state.measures,
  }))
}

// Clearing all filters
removeAllMeasures: () => {
  set(() => ({
    checkedMeasures: [],
  }))
}
```

## Filter Persistence and Management

### Saving Filters

1. User enters filter name
2. System validates name uniqueness
3. Saves configuration to backend:

```typescript
const saveFilters = async () => {
  const filterData = {
    filterName,
    primaryMeasureType,
    page: pathname,
    hospitals: checkedSubOrganizations.map((org) => org.id),
    measures: checkedMeasures.map((measure) => measure.id),
    providers: checkedProviders.map((provider) => provider.id),
  }

  await saveFilterMutation.mutate(filterData)
}
```

### Loading Saved Filters

```typescript
const handleEnableSavedFilter = (savedFilter: SavedFilterModel) => {
  addSavedFilter(savedFilter)
  addAllMeasures(savedFilter.measures)

  if (primaryMeasureType === PrimaryMeasureTypeConstants.HospitalMeasures) {
    addAllSubOrganizations(savedFilter.hospitals)
  } else {
    addAllSubmissionGroups(savedFilter.hospitals)
    addAllProviders(savedFilter.providers)
  }
}
```

## Integration Points

### UI Components Integration

#### Filter Footer Component

```typescript
const Footer = () => {
  const { clearFilters, applyFilters } = useFilterStore()

  const handleApply = async () => {
    applyFilters()
    await calculateMeasuresMutation.mutateAsync({
      filters: memoizedFilters,
      startDate,
      endDate,
      hideEmptyIndicators
    })
  }

  return (
    <div>
      <Button onClick={clearFilters}>Clear</Button>
      <Button onClick={handleApply}>Apply</Button>
    </div>
  )
}
```

#### Filter Chip Display

```typescript
const FilterChips = () => {
  const { checkedMeasures, removeMeasure } = useFilterStore()

  return checkedMeasures.map(measure => (
    <Chip
      key={measure.id}
      label={measure.name}
      onRemove={() => removeMeasure(measure.id)}
    />
  ))
}
```

## Best Practices

1. **State Updates**

   - Always use store actions for state modifications
   - Avoid direct state mutations
   - Use appropriate store methods for specific updates
   - Use bulk operations when possible

   ```typescript
   // Good
   addAllMeasures(selectedIds)

   // Avoid
   selectedIds.forEach((id) => addMeasure(id))
   ```

2. **Filter Application**

   - Validate selections before applying
   - Handle loading states appropriately
   - Provide user feedback for actions
   - Consider memoization for computed values

   ```typescript
   const memoizedFilters = useMemo(
     () =>
       Object.entries(appliedFilters).reduce((acc, [key, value]) => {
         acc[key] = value
         return acc
       }, {}),
     [appliedFilters]
   )
   ```

3. **Error Handling**
   - Validate filter names
   - Handle network errors gracefully
   - Provide meaningful error messages
   - Validate inputs before state updates
   ```typescript
   const validateAndAddMeasure = (measure) => {
     if (!measure.id) {
       throw new Error('Invalid measure: missing ID')
     }
     addMeasure(measure)
   }
   ```
