# UTC Date Handling Technical Guidelines

## Overview
Our application deals with date comparisons across different components, particularly in measure calculations and filtering operations. We consistently use UTC dates to ensure reliable date comparisons and prevent timezone-related bugs.

## Why UTC?

### 1. Consistent Date Comparisons
As seen in `src/services/measures/getECMeasureResultBySubmissionGroupQuery.ts`, we compare dates using:
```typescript
dayjs.utc(x.startDate).isSame(period.startDate, 'day') &&
dayjs.utc(x.endDate).isSame(period.endDate.add(1, 'day'), 'day')
```

Without UTC:
- A date stored as "2023-12-31T23:00:00" in EST
- Could become "2024-01-01T04:00:00" in UTC
- Leading to incorrect period matching and data filtering

### 2. Database Consistency
Our measure results and date ranges are stored in UTC in the database. When comparing these dates with user-selected ranges or period calculations, we must ensure consistent timezone handling to prevent off-by-one errors in date comparisons.

### 3. Cross-timezone Support
As evidenced in `src/lib/getDateString.ts`:
```typescript
const getDateString = (value: Date | null) => {
  if (!value) return 'Unknown'
  const easternTime = dayjs(value).tz('America/New_York')
  return easternTime.isValid() ? easternTime.format() : 'Unknown'
}
```
We explicitly handle timezone conversions for display purposes, but keep internal date operations in UTC.

## Implementation Guidelines

### 1. Date Storage
Always store dates in UTC format:
```typescript
// Good
const startDate = dayjs.utc(inputDate)

// Avoid
const startDate = dayjs(inputDate) // Local timezone
```

### 2. Date Comparisons
When comparing dates, ensure both dates are in UTC:
```typescript
// Good
const isSamePeriod = dayjs.utc(date1).isSame(dayjs.utc(date2), 'day')

// Avoid
const isSamePeriod = dayjs(date1).isSame(date2, 'day') // Timezone dependent
```

### 3. Date Range Calculations
For date range calculations, maintain UTC throughout:
```typescript
// Good
const periodEnd = dayjs.utc(startDate).endOf('month')

// Avoid
const periodEnd = dayjs(startDate).endOf('month') // Local timezone
```

### 4. API Interactions
When sending/receiving dates via API:
- Always send dates in UTC ISO format
- Parse received dates using UTC
```typescript
// Sending
const requestBody = {
  startDate: dayjs.utc(startDate).toISOString(),
  endDate: dayjs.utc(endDate).toISOString()
}

// Receiving
const responseDate = dayjs.utc(response.date)
```

## Common Pitfalls

### 1. Implicit Local Timezone Conversion
```typescript
// Problematic
const date = new Date('2023-12-31') // Implicit local timezone

// Correct
const date = dayjs.utc('2023-12-31').toDate()
```

### 2. Date Math Without UTC
```typescript
// Problematic
const nextDay = dayjs(date).add(1, 'day')

// Correct
const nextDay = dayjs.utc(date).add(1, 'day')
```

### 3. Period Comparisons
```typescript
// Problematic
const isInPeriod = date.isBetween(periodStart, periodEnd)

// Correct
const isInPeriod = dayjs.utc(date).isBetween(
  dayjs.utc(periodStart),
  dayjs.utc(periodEnd)
)
```

## Best Practices

1. **Always Use UTC for Internal Operations**
   - Keep all date calculations and comparisons in UTC
   - Only convert to local timezone for display purposes

2. **Explicit UTC Conversion**
   - Always explicitly call `dayjs.utc()` when creating date objects
   - Don't rely on implicit timezone conversion

3. **Consistent Date Formats**
   - Use ISO string format for API communications
   - Use UTC for database storage
   - Use formatted local time only for UI display

4. **Date Range Handling**
   - Always include both start and end dates in UTC
   - Be explicit about range boundaries (inclusive/exclusive)
   - Consider day boundaries when comparing dates

## Testing Considerations

1. **Test Different Timezones**
   - Write tests with different timezone scenarios
   - Include edge cases around UTC date boundaries

2. **Mock Date Objects**
   - Use UTC dates in test mocks
   - Verify date comparisons work across timezone boundaries

3. **Validation Tests**
   - Test date range calculations
   - Verify period matching logic
   - Test date filtering operations