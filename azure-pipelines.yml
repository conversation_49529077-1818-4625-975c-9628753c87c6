trigger:
  branches:
    include:
      - main

variables:
  azureSubscription: 'Development-MS-Work (Platform-nextgen)'

  # Web app name
  webAppName: 'msc-platform-app-nextgen-dev'

  # Environment name
  environmentName: 'msc-platform-app-nextgen-dev'

  # Agent VM image name  vmImageName: 'ubuntu-latest'
  vmImageName: 'ubuntu-latest'

stages:
  - stage: Build
    displayName: Build stage
    jobs:
      - job: Build
        displayName: Build
        pool:
          vmImage: $(vmImageName)

        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '22.x'
            displayName: 'Install Node.js'

          - script: |
              echo "##vso[task.setvariable variable=NEXT_PUBLIC_APP_VERSION]$(Build.BuildNumber)"
            displayName: 'Set NEXT_PUBLIC_APP_VERSION to Build Number'

          - script: |
              npm install --force
              npm install pm2 -g
              npm run build:standalone
              shopt -s extglob
              rm -rf !(standalone|package.json|package-lock.json|ecosystem.config.cjs)
            displayName: 'npm install and build'

          - task: ArchiveFiles@2
            displayName: 'Archive files'
            inputs:
              rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
              includeRootFolder: false
              archiveType: zip
              archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
              replaceExistingArchive: true

          - publish: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
            artifact: drop

  - stage: Deploy
    displayName: Deploy stage
    dependsOn: Build
    condition: succeeded()
    jobs:
      - deployment: Deploy
        displayName: Deploy
        environment: $(environmentName)
        pool:
          vmImage: $(vmImageName)
        strategy:
          runOnce:
            deploy:
              steps:
                - task: AzureAppServiceSettings@1
                  inputs:
                    azureSubscription: $(azureSubscription)
                    appName: 'msc-platform-app-nextgen-dev'
                    resourceGroupName: 'msc-platform-qa-alternate'
                    appSettings: |
                      [
                         {
                          "name": "DATABASE_URL",
                          "value": "$(DATABASE_URL)",
                          "slotSetting": false
                          },
                          {
                          "name": "NEXTAUTH_URL",
                          "value": "$(NEXTAUTH_URL)",
                          "slotSetting": true
                          },
                          {
                          "name": "NEXT_PUBLIC_APP_VERSION",
                          "value": "$(NEXT_PUBLIC_APP_VERSION)",
                          "slotSetting": true
                          },
                          {
                          "name": "CITC_CLIENT_ID",
                          "value": "$(CITC_CLIENT_ID)",
                          "slotSetting": true
                          },
                          {
                          "name": "CITC_CLIENT_SECRET",
                          "value": "$(CITC_CLIENT_SECRET)",
                          "slotSetting": true
                          },
                          {
                          "name": "CITC_ISSUER_URL",
                          "value": "$(CITC_ISSUER_URL)",
                          "slotSetting": true
                          },
                          {
                          "name": "NEXT_PUBLIC_OIDC_AUTHORITY",
                          "value": "$(NEXT_PUBLIC_OIDC_AUTHORITY)",
                          "slotSetting": true
                          },
                          {
                          "name": "NEXT_PUBLIC_OIDC_CLIENT_ID",
                          "value": "$(NEXT_PUBLIC_OIDC_CLIENT_ID)",
                          "slotSetting": true
                          },
                          {
                          "name": "NEXT_PUBLIC_OIDC_CLIENT_SECRET",
                          "value": "$(NEXT_PUBLIC_OIDC_CLIENT_SECRET)",
                          "slotSetting": true
                          },
                          {
                          "name": "NEXT_PUBLIC_OIDC_RESPONSE_TYPE",
                          "value": "$(NEXT_PUBLIC_OIDC_RESPONSE_TYPE)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_OIDC_SCOPE",
                          "value": "$(NEXT_PUBLIC_OIDC_SCOPE)",
                          "slotSetting": true
                          },
                          {
                          "name": "NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNIN",
                          "value": "$(NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNIN)",
                          "slotSetting": true
                          },
                          {
                          "name": "NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT",
                          "value": "$(NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNOUT)",
                          "slotSetting": true
                          },
                          {
                          "name": "ADM_DATABASE_URL",
                          "value": "$(ADM_DATABASE_URL)",
                          "slotSetting": true
                          },
                          {
                          "name": "HUB_DATABASE_URL",
                          "value": "$(HUB_DATABASE_URL)",
                          "slotSetting": true
                          },
                          {
                          "name": "REDIS_CACHE_TIME_DURATION_SECONDS",
                          "value": "$(REDIS_CACHE_TIME_DURATION_SECONDS)",
                          "slotSetting": true
                          },
                          {
                          "name": "SERVER_CACHING_DISABLED",
                          "value": "$(SERVER_CACHING_DISABLED)",
                          "slotSetting": true
                          },
                           {
                          "name": "MAPLE_BLOB_STORAGE_CONN_STRING",
                          "value": "$(MAPLE_BLOB_STORAGE_CONN_STRING)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG",
                          "value": "$(NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG)",
                          "slotSetting": true
                          },
                          {
                          "name": "NEXT_PUBLIC_OIDC_AUDIENCE",
                          "value": "$(NEXT_PUBLIC_OIDC_AUDIENCE)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_PLATFORM_ADM_SERVER_URL",
                          "value": "$(NEXT_PUBLIC_PLATFORM_ADM_SERVER_URL)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_MAPLE_SERVER_URL",
                          "value": "$(NEXT_PUBLIC_MAPLE_SERVER_URL)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_API_BASE_URL",
                          "value": "$(NEXT_PUBLIC_API_BASE_URL)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING",
                          "value": "$(NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_SISENSE_SETTINGS_PARAMETER_NAME",
                          "value": "$(NEXT_PUBLIC_SISENSE_SETTINGS_PARAMETER_NAME)",
                          "slotSetting": true
                          },
                            {
                          "name": "NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING",
                          "value": "$(NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING)",
                          "slotSetting": true
                          },
                            {
                          "name": "NEXT_PUBLIC_APPINSIGHTS_INSTRUMENTATIONKEY",
                          "value": "$(NEXT_PUBLIC_APPINSIGHTS_INSTRUMENTATIONKEY)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_AUTH_SECRET",
                          "value": "$(NEXT_PUBLIC_AUTH_SECRET)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL",
                          "value": "$(NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL)",
                          "slotSetting": true
                          },
                           {
                          "name": "REDIS_URL",
                          "value": "$(REDIS_URL)",
                          "slotSetting": true
                          },
                          {
                          "name": "REDIS_KEY",
                          "value": "$(REDIS_KEY)",
                          "slotSetting": true
                          },
                          {
                          "name": "PRISMA_CLI_BINARY_TARGETS",
                          "value": "$(PRISMA_CLI_BINARY_TARGETS)",
                          "slotSetting": true
                          },
                          {
                          "name": "NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL_IDENTIFIER",
                          "value": "$(NEXT_PUBLIC_SISENSE_SETTINGS_BASE_URL_IDENTIFIER)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_SISENSE_SETTINGS_USERNAME_IDENTIFIER",
                          "value": "$(NEXT_PUBLIC_SISENSE_SETTINGS_USERNAME_IDENTIFIER)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_SISENSE_SETTINGS_PASSWORD_IDENTIFIER",
                          "value": "$(NEXT_PUBLIC_SISENSE_SETTINGS_PASSWORD_IDENTIFIER)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_SISENSE_SETTINGS_TOKEN_URL_SUFFIX",
                          "value": "$(NEXT_PUBLIC_SISENSE_SETTINGS_TOKEN_URL_SUFFIX)",
                          "slotSetting": true
                          },
                           {
                          "name": "NODE_TLS_REJECT_UNAUTHORIZED",
                          "value": "$(NODE_TLS_REJECT_UNAUTHORIZED)",
                          "slotSetting": true
                          },
                            {
                          "name": "AUTH_TRUST_HOST",
                          "value": "$(AUTH_TRUST_HOST)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_POSTHOG_HOST",
                          "value": "$(NEXT_PUBLIC_POSTHOG_HOST)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_POSTHOG_KEY",
                          "value": "$(NEXT_PUBLIC_POSTHOG_KEY)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_HYDRA_SERVER_URL",
                          "value": "$(NEXT_PUBLIC_HYDRA_SERVER_URL)",
                          "slotSetting": true
                          },
                           {
                          "name": "NEXT_PUBLIC_AUDIT_LOG_SERVER_URL",
                          "value": "$(NEXT_PUBLIC_AUDIT_LOG_SERVER_URL)",
                          "slotSetting": true
                          },
                           {
                          "name": "PRODUCT_ID",
                          "value": "$(PRODUCT_ID)",
                          "slotSetting": true
                          }
                      ]
                - task: AzureWebApp@1
                  inputs:
                    azureSubscription: $(azureSubscription)
                    appType: webAppLinux
                    appName: $(webAppName)
                    package: '$(Pipeline.Workspace)/drop/$(Build.BuildId).zip'
                #     runtimeStack: 'NODE|20-lts'
                # - task: AzureRmWebAppDeployment@4
                #   displayName: 'Azure App Service Deploy: platform next gen dev '
                #   inputs:
                #     azureSubscription: $(azureSubscription)
                #     appType: webAppLinux
                #     WebAppName: $(webAppName)
                #     packageForLinux: '$(Pipeline.Workspace)/drop/$(Build.BuildId).zip'
                #     RuntimeStack: 'NODE|20-lts'
                #     ScriptType: 'Inline Script'
                #     InlineScript: |
                #       npm ci --force
                #       npm run build
