{"version": "0.2.0", "configurations": [{"name": "Run Individual Unit Test", "type": "node", "request": "launch", "runtimeExecutable": "node", "runtimeArgs": ["${workspaceFolder}/node_modules/.bin/jest", "--config=jest.config.cjs", "${file}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Run Individual Integration Test", "type": "node", "request": "launch", "runtimeExecutable": "node", "runtimeArgs": ["--experimental-vm-modules", "--disable-warning=ExperimentalWarning", "${workspaceFolder}/node_modules/.bin/jest", "--config=jest.config.integration.cjs", "${file}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Node App Service Remote Debugging Dev", "port": 9229, "request": "attach", "address": "https://platformnextgen.medisolvdev.com", "remoteRoot": "/home/<USER>/wwwroot", "localRoot": "${workspaceFolder}", "type": "node", "outFiles": ["${workspaceFolder}/.next/**/*.js", "${workspaceFolder}/dist/**/*.js"]}, {"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npm run dev", "outFiles": ["${workspaceFolder}/.next/**/*.js", "${workspaceFolder}/dist/**/*.js"]}, {"name": "Next.js: debug-windows server-side", "type": "node-terminal", "request": "launch", "command": "npm run dev:windows", "outFiles": ["${workspaceFolder}/.next/**/*.js", "${workspaceFolder}/dist/**/*.js"]}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000"}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/next", "runtimeArgs": ["--inspect"], "skipFiles": ["<node_internals>/**"], "serverReadyAction": {"killOnServerStop": true, "pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "webRoot": "${workspaceFolder}"}}, {"type": "node", "request": "launch", "name": "Debug Integration Tests", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--config=jest.config.integration.cjs", "--testPathPattern=integration/services", "--runInBand"], "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--experimental-vm-modules --disable-warning=ExperimentalWarning"}}]}