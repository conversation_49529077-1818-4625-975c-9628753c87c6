import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardReportType } from '@/types/dashboardReportType'
import { upsertDashboardReportType } from '@/services/dashboardReportType/upsertDashboardReportType'
import { StorageTables } from '@/enums/storageTables'

dayjs.extend(utc)

const SECOND = 1000

describe('upsertDashboardReportType', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = 'test-org-123'

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.DashboardReportTypes}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert new dashboard report type when it does not exist', async () => {
    // Arrange
    const dashboardReportType: DashboardReportType = {
      partitionKey: testOrgId,
      rowKey: `report-type-${dayjs.utc().valueOf()}`,
      reportId: 'test-report-id',
      reportType: 'Test Report Type',
    }

    // Act
    const result = await upsertDashboardReportType(
      tableStorageWrapper,
      dashboardReportType
    )

    // Assert
    expect(result).toBeDefined()
    expect(result).toMatchObject({
      partitionKey: testOrgId,
      rowKey: dashboardReportType.rowKey,
      reportId: 'test-report-id',
      reportType: 'Test Report Type',
    })

    // Verify in storage
    const savedTypes =
      await tableStorageWrapper.queryEntities<DashboardReportType>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(savedTypes.length).toBe(1)
    expect(savedTypes[0]).toMatchObject(dashboardReportType)
  }, 70 * SECOND)

  test('should update existing dashboard report type', async () => {
    // Arrange
    const rowKey = `report-type-${dayjs.utc().valueOf()}`
    const initialType: DashboardReportType = {
      partitionKey: testOrgId,
      rowKey,
      reportId: 'initial-report-id',
      reportType: 'Initial Report Type',
    }
    await tableStorageWrapper.insertEntity(initialType)

    const updatedType: DashboardReportType = {
      partitionKey: testOrgId,
      rowKey,
      reportId: 'updated-report-id',
      reportType: 'Updated Report Type',
    }

    // Act
    const result = await upsertDashboardReportType(
      tableStorageWrapper,
      updatedType
    )

    // Assert
    expect(result).toBeDefined()
    expect(result).toMatchObject({
      partitionKey: testOrgId,
      rowKey,
      reportId: 'updated-report-id',
      reportType: 'Updated Report Type',
    })

    // Verify in storage
    const savedTypes =
      await tableStorageWrapper.queryEntities<DashboardReportType>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(savedTypes.length).toBe(1)
    expect(savedTypes[0]).toMatchObject(updatedType)
  }, 70 * SECOND)

  test('should handle dashboard report type with minimal required fields', async () => {
    // Arrange
    const minimalType: DashboardReportType = {
      partitionKey: testOrgId,
      rowKey: `report-type-${dayjs.utc().valueOf()}`,
      reportId: 'minimal-report-id',
      reportType: 'Minimal Report Type',
    }

    // Act
    const result = await upsertDashboardReportType(
      tableStorageWrapper,
      minimalType
    )

    // Assert
    expect(result).toBeDefined()
    expect(result).toMatchObject({
      partitionKey: testOrgId,
      rowKey: minimalType.rowKey,
      reportId: 'minimal-report-id',
      reportType: 'Minimal Report Type',
    })

    // Verify in storage
    const savedTypes =
      await tableStorageWrapper.queryEntities<DashboardReportType>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(savedTypes.length).toBe(1)
    expect(savedTypes[0]).toMatchObject(minimalType)
  }, 70 * SECOND)
})
