import { jest } from '@jest/globals'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardReportType } from '@/types/dashboardReportType'
import { getAllDashboardReportTypes } from '@/services/dashboardReportType/getAllDashboardReportTypes'
import { MigrationConfig } from '@/types/migrationConfig'
import { StorageTables } from '@/enums/storageTables'
import { SelectionType } from '@/enums/selectionType'

dayjs.extend(utc)

describe('getAllDashboardReportTypes', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = 'test-org-123'

  beforeEach(async () => {
    // Create a unique table name for each test
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.DashboardReportTypes}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
    // Clear mock prisma client
    globalThis.prismaClients = {}
  })

  test('should return empty array when no report types exist', async () => {
    const results = await getAllDashboardReportTypes(
      tableStorageWrapper,
      testOrgId
    )

    expect(results).toBeDefined()
    expect(Array.isArray(results)).toBeTruthy()
    expect(results.length).toBe(0)
  })

  test('should return all report types for an organization', async () => {
    const testReportTypes: DashboardReportType[] = [
      {
        partitionKey: testOrgId,
        rowKey: `report-1-${dayjs.utc().valueOf()}`,
        reportId: 'report-1',
        reportType: 'Test Report Type 1',
      },
      {
        partitionKey: testOrgId,
        rowKey: `report-2-${dayjs.utc().valueOf()}`,
        reportId: 'report-2',
        reportType: 'Test Report Type 2',
      },
    ]

    await Promise.all(
      testReportTypes.map((reportType) =>
        tableStorageWrapper.insertEntity(reportType)
      )
    )

    const results = await getAllDashboardReportTypes(
      tableStorageWrapper,
      testOrgId
    )

    expect(results.length).toBe(2)
    results.forEach((result, index) => {
      expect(result.partitionKey).toBe(testOrgId)
      expect(result.reportId).toBe(testReportTypes[index]!.reportId)
      expect(result.reportType).toBe(testReportTypes[index]!.reportType)
    })
  })

  describe('with migrationConfig', () => {
    test('should migrate and return report types when migrationConfig is provided', async () => {
      // Mock SQL results
      const sqlReportTypes = [
        {
          Id: 1,
          ReportId: 'report-1',
          ReportType: 'SQL Report Type 1',
        },
        {
          Id: 2,
          ReportId: 'report-2',
          ReportType: 'SQL Report Type 2',
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardReportType: {
              findMany: jest.fn().mockResolvedValue(sqlReportTypes as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardReportTypes(
        tableStorageWrapper,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results.length).toBe(2)
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            partitionKey: testOrgId,
            reportId: 'report-1',
            reportType: 'SQL Report Type 1',
          }),
          expect.objectContaining({
            partitionKey: testOrgId,
            reportId: 'report-2',
            reportType: 'SQL Report Type 2',
          }),
        ])
      )

      // Verify report types were migrated to table storage
      const storedReportTypes =
        await tableStorageWrapper.queryEntities<DashboardReportType>(
          `PartitionKey eq '${testOrgId}'`
        )
      expect(storedReportTypes.length).toBe(2)
    })

    test('should handle empty SQL results during migration', async () => {
      // Mock empty SQL results
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardReportType: {
              findMany: jest.fn().mockResolvedValue([] as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardReportTypes(
        tableStorageWrapper,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should handle missing prisma client during migration', async () => {
      // Clear mock prisma client
      globalThis.prismaClients = {}

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardReportTypes(
        tableStorageWrapper,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should attempt migration when selectionType is Partner', async () => {
      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Partner,
      }

      const mockPrismaFindMany = jest.fn()
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardReportType: {
              findMany: mockPrismaFindMany,
            },
          },
        },
      } as any

      // Act
      const results = await getAllDashboardReportTypes(
        tableStorageWrapper,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
      expect(mockPrismaFindMany).toHaveBeenCalled()
    })

    test('should migrate when migrationConfig exists and no report types exist in table storage', async () => {
      // Mock SQL results
      const sqlReportTypes = [
        {
          Id: 1,
          ReportId: 'report-1',
          ReportType: 'SQL Report Type 1',
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardReportType: {
              findMany: jest.fn().mockResolvedValue(sqlReportTypes as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Verify no report types exist initially
      const initialReportTypes = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testOrgId}'`
      )
      expect(initialReportTypes).toHaveLength(0)

      // Act
      const results = await getAllDashboardReportTypes(
        tableStorageWrapper,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testOrgId,
          reportId: 'report-1',
          reportType: 'SQL Report Type 1',
        })
      )

      // Verify report types were migrated to table storage
      const storedReportTypes = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testOrgId}'`
      )
      expect(storedReportTypes).toHaveLength(1)
    })

    test('should not migrate when migrationConfig exists but report types already exist in table storage', async () => {
      // First create some existing report types
      const existingReportType = {
        partitionKey: testOrgId,
        rowKey: 'existing-1',
        reportId: 'existing-report',
        reportType: 'Existing Report Type',
      }
      await tableStorageWrapper.insertEntity(existingReportType)

      // Mock SQL results that shouldn't be migrated
      const sqlReportTypes = [
        {
          Id: 1,
          ReportId: 'sql-report',
          ReportType: 'SQL Report Type',
        },
      ]

      // Mock prisma client
      const mockFindMany = jest.fn().mockResolvedValue(sqlReportTypes as never)
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardReportType: {
              findMany: mockFindMany,
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardReportTypes(
        tableStorageWrapper,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testOrgId,
          reportId: 'existing-report',
          reportType: 'Existing Report Type',
        })
      )

      // Verify no new report types were migrated
      const storedReportTypes = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testOrgId}'`
      )
      expect(storedReportTypes).toHaveLength(1)
      expect(storedReportTypes[0]).toEqual(
        expect.objectContaining({
          reportId: 'existing-report',
          reportType: 'Existing Report Type',
        })
      )

      // Verify the SQL query was not called
      expect(mockFindMany).not.toHaveBeenCalled()
    })
  })

  test('should only return report types for the specified organization', async () => {
    const anotherOrgId = 'another-org-456'
    const testReportTypes: DashboardReportType[] = [
      {
        partitionKey: testOrgId,
        rowKey: `report-1-${dayjs.utc().valueOf()}`,
        reportId: 'report-1',
        reportType: 'Test Report Type 1',
      },
      {
        partitionKey: anotherOrgId,
        rowKey: `report-2-${dayjs.utc().valueOf()}`,
        reportId: 'report-2',
        reportType: 'Test Report Type 2',
      },
    ]

    await Promise.all(
      testReportTypes.map((reportType) =>
        tableStorageWrapper.insertEntity(reportType)
      )
    )

    const results = await getAllDashboardReportTypes(
      tableStorageWrapper,
      testOrgId
    )

    expect(results.length).toBe(1)
    expect(results[0]!.partitionKey).toBe(testOrgId)
    expect(results[0]!.reportId).toBe('report-1')
    expect(results[0]!.reportType).toBe('Test Report Type 1')
  })
})
