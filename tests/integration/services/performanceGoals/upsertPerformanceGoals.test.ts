import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import {
  PerformanceGoalUpsertRequest,
  upsertPerformanceGoals,
} from '@/services/performanceGoals/upsertPerformanceGoals'
import { StorageTables } from '@/enums/storageTables'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'

dayjs.extend(utc)

describe('upsertPerformanceGoals service', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  const testUserId = `test-user-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.PerformanceGoals}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )

    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  it('should successfully create goals for a single month range', async () => {
    const input: PerformanceGoalUpsertRequest = {
      organizationId: testOrgId,
      isPartner: false,
      isEditProcess: false,
      measureIdentifier: 'm1',
      entityId: 'e1',
      startDate: dayjs.utc().startOf('month').toDate(),
      endDate: dayjs.utc().startOf('month').toDate(),
      userId: testUserId,
    }

    const result = await upsertPerformanceGoals(tableStorageWrapper, input)

    expect(result.success).toBe(true)
    expect(result.message).toBe('Goal set successfully')
  })

  it('should create multiple goals across months', async () => {
    const input: PerformanceGoalUpsertRequest = {
      organizationId: testOrgId,
      isPartner: false,
      isEditProcess: false,
      measureIdentifier: 'm2',
      entityId: 'e2',
      startDate: dayjs.utc().startOf('month').toDate(),
      endDate: dayjs.utc().add(2, 'month').startOf('month').toDate(),
      userId: testUserId,
    }

    const result = await upsertPerformanceGoals(tableStorageWrapper, input)
    expect(result.success).toBe(true)

    const entities = await tableStorageWrapper.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )

    expect(entities.length).toBe(3)
  })

  it('should not create goals if they already exist', async () => {
    const input: PerformanceGoalUpsertRequest = {
      organizationId: testOrgId,
      isPartner: false,
      isEditProcess: false,
      measureIdentifier: 'm3',
      entityId: 'e3',
      startDate: dayjs.utc().startOf('month').toDate(),
      endDate: dayjs.utc().startOf('month').toDate(),
      userId: testUserId,
    }

    // First time should succeed
    await upsertPerformanceGoals(tableStorageWrapper, input)

    // Second time should detect duplicates
    const result = await upsertPerformanceGoals(tableStorageWrapper, input)
    expect(result.success).toBe(false)
    expect(result.message).toMatch(/already exists/)
  })

  it('should handle invalid date range', async () => {
    const input: PerformanceGoalUpsertRequest = {
      organizationId: testOrgId,
      isPartner: false,
      isEditProcess: false,
      measureIdentifier: 'm4',
      entityId: 'e4',
      startDate: dayjs.utc().toDate(),
      endDate: dayjs.utc().subtract(1, 'month').toDate(), // invalid range
      userId: testUserId,
    }

    const result = await upsertPerformanceGoals(tableStorageWrapper, input)
    expect(result.success).toBe(true)
    expect(result.message).toBe('Goal set successfully') // should it silently pass or error? (Consider updating logic if needed)
  })

  it('should handle missing optional fields', async () => {
    const input: PerformanceGoalUpsertRequest = {
      organizationId: testOrgId,
      isPartner: false,
      isEditProcess: false,
      measureIdentifier: 'm5',
      entityId: 'e5',
      startDate: dayjs.utc().startOf('month').toDate(),
      endDate: dayjs.utc().startOf('month').toDate(),
      userId: testUserId,
      goalUpper: 90,
    }

    const result = await upsertPerformanceGoals(tableStorageWrapper, input)
    expect(result.success).toBe(true)
  })

  it('should handle failure inside try-catch gracefully', async () => {
    const faultyWrapper = {
      queryEntities: jest.fn().mockImplementation(() => {
        throw new Error('Query failed')
      }),
      generateODataQuery: jest.fn(),
    } as unknown as AzureTableStorageWrapper

    const input: PerformanceGoalUpsertRequest = {
      organizationId: testOrgId,
      isPartner: false,
      isEditProcess: false,
      measureIdentifier: 'm6',
      entityId: 'e6',
      startDate: dayjs.utc().toDate(),
      endDate: dayjs.utc().toDate(),
      userId: testUserId,
    }

    const result = await upsertPerformanceGoals(faultyWrapper, input)
    expect(result.success).toBe(false)
    expect(result.message).toBe('Some error occurred.')
  })
})
