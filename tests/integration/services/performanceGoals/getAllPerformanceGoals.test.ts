import { getAllPerformanceGoals } from '@/services/performanceGoals/getAllPerformanceGoals'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { SelectionType } from '@/enums/selectionType'
import { PerformanceGoal } from '@/types/scorecards/performanceGoal'
import { StorageTables } from '@/enums/storageTables'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'

dayjs.extend(utc)

describe('getAllPerformanceGoals', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const organizationId = 'test-org'
  const userId = 'test-user'

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.PerformanceGoals}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )

    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  it('returns existing performance goals without migration if migrationConfig not provided', async () => {
    const existingGoals: PerformanceGoal[] = [
      {
        partitionKey: organizationId,
        rowKey: 'existing-1',
        measureIdentifier: 'existing-measure',
        entityId: 'existing-entity',
        startDate: dayjs.utc().toISOString(),
        endDate: dayjs.utc().add(1, 'month').toISOString(),
        goalLower: 1,
        goalUpper: 1,
        benchmark: 1,
        isYellowZoneFixedNumber: true,
        yellowZone: 1,
        isExceptionalPerformanceNumber: true,
        exceptionalPerformance: 1,
        lastUpdatedByUserId: userId,
        lastUpdatedDateTime: new Date(),
      },
      {
        partitionKey: organizationId,
        rowKey: 'existing-2',
        measureIdentifier: 'existing-measure-2',
        entityId: 'existing-entity-2',
        startDate: dayjs.utc().toISOString(),
        endDate: dayjs.utc().add(1, 'month').toISOString(),
        goalLower: 1,
        goalUpper: 1,
        benchmark: 1,
        isYellowZoneFixedNumber: true,
        yellowZone: 1,
        isExceptionalPerformanceNumber: true,
        exceptionalPerformance: 1,
        lastUpdatedByUserId: userId,
        lastUpdatedDateTime: new Date(),
      },
    ]

    await Promise.all(
      existingGoals.map((goal) => tableStorageWrapper.insertEntity(goal))
    )

    const result = await getAllPerformanceGoals(
      tableStorageWrapper,
      organizationId,
      userId
    )

    expect(result).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: organizationId,
          rowKey: 'existing-1',
        }),
        expect.objectContaining({
          partitionKey: organizationId,
          rowKey: 'existing-2',
        }),
      ])
    )
  })

  it('returns existing goals when present even if migrationConfig is provided', async () => {
    const existingGoals: PerformanceGoal[] = [
      {
        partitionKey: organizationId,
        rowKey: 'existing-1',
        measureIdentifier: 'existing-measure',
        entityId: 'existing-entity',
        startDate: dayjs.utc().toISOString(),
        endDate: dayjs.utc().add(1, 'month').toISOString(),
        goalLower: 1,
        goalUpper: 1,
        benchmark: 1,
        isYellowZoneFixedNumber: true,
        yellowZone: 1,
        isExceptionalPerformanceNumber: true,
        exceptionalPerformance: 1,
        lastUpdatedByUserId: userId,
        lastUpdatedDateTime: new Date(),
      },
      {
        partitionKey: organizationId,
        rowKey: 'existing-2',
        measureIdentifier: 'existing-measure-2',
        entityId: 'existing-entity-2',
        startDate: dayjs.utc().toISOString(),
        endDate: dayjs.utc().add(1, 'month').toISOString(),
        goalLower: 1,
        goalUpper: 1,
        benchmark: 1,
        isYellowZoneFixedNumber: true,
        yellowZone: 1,
        isExceptionalPerformanceNumber: true,
        exceptionalPerformance: 1,
        lastUpdatedByUserId: userId,
        lastUpdatedDateTime: new Date(),
      },
    ]

    await Promise.all(
      existingGoals.map((goal) => tableStorageWrapper.insertEntity(goal))
    )

    const migrationConfig = {
      organizationId,
      selectionType: SelectionType.Organization,
    }

    const result = await getAllPerformanceGoals(
      tableStorageWrapper,
      organizationId,
      userId,
      undefined,
      undefined,
      migrationConfig
    )

    expect(result).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: organizationId,
          rowKey: 'existing-1',
        }),
        expect.objectContaining({
          partitionKey: organizationId,
          rowKey: 'existing-2',
        }),
      ])
    )
  })

  it('passes measureIdentifier and entityId to OData query', async () => {
    const existingGoals: PerformanceGoal[] = [
      {
        partitionKey: organizationId,
        rowKey: 'existing-1',
        measureIdentifier: 'measure1',
        entityId: 'entity1',
        startDate: dayjs.utc().toISOString(),
        endDate: dayjs.utc().add(1, 'month').toISOString(),
        goalLower: 1,
        goalUpper: 1,
        benchmark: 1,
        isYellowZoneFixedNumber: true,
        yellowZone: 1,
        isExceptionalPerformanceNumber: true,
        exceptionalPerformance: 1,
        lastUpdatedByUserId: userId,
        lastUpdatedDateTime: new Date(),
      },
      {
        partitionKey: organizationId,
        rowKey: 'existing-2',
        measureIdentifier: 'measure1',
        entityId: 'entity1',
        startDate: dayjs.utc().toISOString(),
        endDate: dayjs.utc().add(1, 'month').toISOString(),
        goalLower: 1,
        goalUpper: 1,
        benchmark: 1,
        isYellowZoneFixedNumber: true,
        yellowZone: 1,
        isExceptionalPerformanceNumber: true,
        exceptionalPerformance: 1,
        lastUpdatedByUserId: userId,
        lastUpdatedDateTime: new Date(),
      },
    ]

    await Promise.all(
      existingGoals.map((goal) => tableStorageWrapper.insertEntity(goal))
    )

    const result = await getAllPerformanceGoals(
      tableStorageWrapper,
      organizationId,
      userId,
      'measure1',
      'entity1'
    )

    expect(result).toHaveLength(2)
  })

  it('returns results after successful migration', async () => {
    const migrationConfig = {
      organizationId,
      selectionType: SelectionType.Organization,
    }

    const mockGoals = [
      {
        Id: 1,
        MeasureIdentifier: 'measure1',
        EntitiesId: 1,
        StartDate: new Date(),
        EndDate: new Date(),
        GoalLower: 1,
        GoalUpper: 1,
        Benchmark: 1,
        IsYellowZoneFixedNumber: true,
        YellowZone: 1,
        IsExceptionalPerformanceNumber: true,
        ExceptionalPerformance: 1,
        LastUpdatedByUserId: userId,
      },
    ]

    // Mock Prisma client
    globalThis.prismaClients = {
      [organizationId]: {
        admClient: {
          performanceGoals: {
            findMany: jest.fn().mockResolvedValue(mockGoals as never),
          },
        },
      },
    } as any

    const result = await getAllPerformanceGoals(
      tableStorageWrapper,
      organizationId,
      userId,
      undefined,
      undefined,
      migrationConfig
    )

    expect(result.length).toBe(1)
    expect(result[0]).toEqual(
      expect.objectContaining({
        partitionKey: organizationId,
        rowKey: expect.any(String),
        measureIdentifier: 'measure1',
        entityId: '1',
        startDate: expect.any(String),
        endDate: expect.any(String),
        goalLower: 1,
        goalUpper: 1,
        benchmark: 1,
        isYellowZoneFixedNumber: true,
        yellowZone: 1,
        isExceptionalPerformanceNumber: true,
        exceptionalPerformance: 1,
        lastUpdatedByUserId: userId,
      })
    )
  })
})
