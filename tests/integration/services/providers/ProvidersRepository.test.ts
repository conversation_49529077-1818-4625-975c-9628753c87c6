import { ProvidersRepository } from '@/services/providers/ProvidersRepository'
import { factory, closePool } from '@/services/snowflake/SnowflakeRepositoryFactory'
import { createSnowflakeConfiguration } from '@/services/snowflake/SnowflakeHelper'
import { env } from '@/env'
import dayjs from 'dayjs'
import { fileURLToPath } from "url";
import path from "path";
import fs from "fs";
import { Provider } from "@/types/provider";

// Clean up resources after all tests
afterAll(async () => {
    await closePool()
})

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const expectedResults = JSON.parse(fs.readFileSync(`${__dirname}/expected-providers-results.json`, 'utf8'))
const SECONDS = 1000

describe('ProvidersRepository', () => {
    let providersRepository: ProvidersRepository

    beforeAll(() => {
        // Create the repository using the factory method with Snowflake configuration
        providersRepository = factory({
            ...createSnowflakeConfiguration(),
            role: 'PLATFORM_DEV_ROLE'
        }).createProvidersRepository()
    })

    test('should return all Providers', async () => {
        // Act
        let results = await providersRepository.findAll()
        expect(results.length).toBeGreaterThan(0)
    }, 70 * SECONDS)

    describe('findActiveProvidersBySubmissionGroup', () => {
        test('should return providers for a specific submission group', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = "6627bdd879564eb80253a0fc_660da37246bc4726049a607d"
            const startDate = dayjs('2024-03-01')
            const endDate = dayjs('2025-06-30')
            const checkSourceContainerIdentifier = true
            const ecOverallGroupId = ""
            const isPartner = false

            // Act
            let results = await providersRepository.findActiveProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                startDate,
                endDate,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner
            )

            // Assert
            // If we have results, verify their structure
            expect(results.length).toEqual(7972)
            results = results.slice(0, 100)
            assertResults(results, expectedResults.expected1);
        })

        test('should return providers for all submission groups when submissionGroupId is "*"', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = '*'
            const startDate = dayjs('2024-03-01')
            const endDate = dayjs('2025-06-30')
            const checkSourceContainerIdentifier = false
            const ecOverallGroupId = ''
            const isPartner = false

            // Act
            let results = await providersRepository.findActiveProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                startDate,
                endDate,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner
            )

            expect(results.length).toEqual(7972)
            results = results.slice(0, 100)
            assertResults(results, expectedResults.expected2);
        })

        test('should return providers for all submission groups when submissionGroupId is "*" and ecOverGroupId is set', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = '*'
            const startDate = dayjs('2024-03-01')
            const endDate = dayjs('2025-06-30')
            const checkSourceContainerIdentifier = false
            const ecOverallGroupId = '6627bdd879564eb80253a0fc_660da37246bc4726049a607d'
            const isPartner = false

            // Act
            let results = await providersRepository.findActiveProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                startDate,
                endDate,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner
            )

            expect(results.length).toEqual(7972)
            results = results.slice(0, 100)
            assertResults(results, expectedResults.expected3);
        })

        test('should return providers for a partner organization', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = '*'
            const startDate = dayjs('2024-03-01')
            const endDate = dayjs('2025-06-30')
            const checkSourceContainerIdentifier = true
            const ecOverallGroupId = ''
            const isPartner = true

            // Act
            let results = await providersRepository.findActiveProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                startDate,
                endDate,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner
            )

            // Assert
            expect(results.length).toEqual(7972)
            results = results.slice(0, 100)
            assertResults(results, expectedResults.expected4);
        })

        test('should return providers for a partner organization without checkSourceContainerIdentifier', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = '*'
            const startDate = dayjs('2024-03-01')
            const endDate = dayjs('2025-06-30')
            const checkSourceContainerIdentifier = false
            const ecOverallGroupId = ''
            const isPartner = true

            // Act
            let results = await providersRepository.findActiveProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                startDate,
                endDate,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner
            )

            // Assert
            expect(results.length).toEqual(7972)
            results = results.slice(0, 100)
            assertResults(results, expectedResults.expected5);
        })

        test('should handle empty results', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange - use a non-existent submission group ID
            const organizationId = 'non-existent-org'
            const submissionGroupId = 'non-existent-group'
            const startDate = dayjs('2023-01-01')
            const endDate = dayjs('2023-12-31')
            const checkSourceContainerIdentifier = false
            const ecOverallGroupId = 'ec-123'
            const isPartner = false

            // Act
            const results = await providersRepository.findActiveProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                startDate,
                endDate,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner
            )

            // Assert
            expect(results.length).toEqual(0)
        })
    })

    describe('findProvidersBySubmissionGroup', () => {
        test('should return providers for a specific submission group', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = "6627bdd879564eb80253a0fc_660da37246bc4726049a607d"
            const checkSourceContainerIdentifier = false
            const ecOverallGroupId = ""
            const isPartner = false
            const suffix = ""

            // Act
            let results = await providersRepository.findProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner,
                suffix
            )

            // Assert
            // If we have results, verify their structure
            expect(results.length).toBe(7972)
            results = results.slice(0, 100)
            assertResults(results, expectedResults.expected6);
        })

        test('should return providers for all submission groups when submissionGroupId is "*" and ecOverallGroupId is specified', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = '*'
            const checkSourceContainerIdentifier = false
            const ecOverallGroupId = "6627bdd879564eb80253a0fc_660da37246bc4726049a607d"
            const isPartner = false
            const suffix = ""

            // Act
            let results = await providersRepository.findProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner,
                suffix
            )

            // Assert
            expect(results.length).toBe(7972)
            results = results.slice(0, 100)
            assertResults(results, expectedResults.expected7);
        })

        test('should return providers for a partner organization', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = '*'
            const checkSourceContainerIdentifier = true
            const ecOverallGroupId = ''
            const isPartner = true
            const suffix = ""

            // Act
            let results = await providersRepository.findProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner,
                suffix
            )

            // Assert
            expect(results.length).toBe(0)
        })

        test('should return providers for a partner organization without checkSourceContainerIdentifier', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = '*'
            const checkSourceContainerIdentifier = false
            const ecOverallGroupId = ''
            const isPartner = true
            const suffix = ""

            // Act
            let results = await providersRepository.findProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner,
                suffix
            )

            // Assert
            expect(results.length).toBe(7972)
            results = results.slice(0, 100)
            assertResults(results, expectedResults.expected8);
        })

        test('should apply suffix to source container identifier when provided', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const organizationId = "cc956396-c75a-43c0-a7fe-e7236dd41014"
            const submissionGroupId = "6627bdd879564eb80253a0fc_660da37246bc4726049a607d"
            const checkSourceContainerIdentifier = true
            const ecOverallGroupId = ""
            const isPartner = false
            const suffix = "_EC"

            // Act
            let results = await providersRepository.findProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner,
                suffix
            )

            // Assert
            // Even with suffix, we should still get results since the test data is set up to handle this
            expect(results.length).toBe(7972)
            results = results.slice(0, 100)
            assertResults(results, expectedResults.expected9);
        })

        test('should handle empty results', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange - use a non-existent submission group ID
            const organizationId = 'non-existent-org'
            const submissionGroupId = 'non-existent-group'
            const checkSourceContainerIdentifier = false
            const ecOverallGroupId = 'ec-123'
            const isPartner = false
            const suffix = ""

            // Act
            const results = await providersRepository.findProvidersBySubmissionGroup(
                organizationId,
                submissionGroupId,
                checkSourceContainerIdentifier,
                ecOverallGroupId,
                isPartner,
                suffix
            )

            // Assert
            expect(results.length).toEqual(0)
        })
    })
})

function assertResults(results: Provider[], expectedResults: any[]) {
    expect(results).toBeDefined()
    expect(results.length).toBe(expectedResults.length)
    expectedResults.forEach((row: any) => {
        const actualList = results.filter((result: any) => result.npi == row.NPI)
        expect(actualList.length).toBe(1)
        const actual = actualList[0]!
        expect(actual.submissionGroupId).toBe(row.SubmissionGroupId)
        expect(actual.submissionGroupContainerIdentifier?.startsWith(row.SubmissionGroupContainerIdentifier)).toBe(true)
    })
}
