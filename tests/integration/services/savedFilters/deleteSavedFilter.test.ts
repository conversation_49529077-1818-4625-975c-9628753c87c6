import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { SavedFilter } from '@/types/savedFilter'
import { RestError } from '@azure/data-tables'
import { ChunkMetadata } from '@/types/filterQuery'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { StorageTables } from '@/enums/storageTables'
import { jest } from '@jest/globals'

dayjs.extend(utc)

const mockRedisHelper = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
}

jest.unstable_mockModule('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => {
    return fn()
  }),
  redisHelper: mockRedisHelper
}))

let tableStorageWrapper: AzureTableStorageWrapper
const testUserId = `test-user-${dayjs.utc().valueOf()}`

let deleteSavedFilter: (tableStorageWrapper: AzureTableStorageWrapper, userId: string, filterId: string) => Promise<void>

beforeAll(async () => {
  deleteSavedFilter = (await import('@/services/savedFilters/deleteSavedFilter')).deleteSavedFilter
})

beforeEach(async () => {
  tableStorageWrapper = new AzureTableStorageWrapper(
    `${StorageTables.SavedFilters}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
  )
  await tableStorageWrapper.createTable()
})

afterEach(async () => {
  // Clean up any existing entities
  const allEntities = await tableStorageWrapper.queryEntities<
    SavedFilter | ChunkMetadata
  >(`PartitionKey eq '${testUserId}'`)
  await Promise.all(
    allEntities.map((entity) =>
      tableStorageWrapper.deleteEntity(entity.partitionKey, entity.rowKey)
    )
  )
  await tableStorageWrapper.deleteTable()
})

describe('deleteSavedFilter', () => {
  test('should delete an existing filter and its chunks', async () => {
    // Arrange
    const filterId = `filter-${dayjs.utc().valueOf()}`
    const testFilter: SavedFilter = {
      partitionKey: testUserId,
      rowKey: filterId,
      userId: testUserId,
      filterName: 'Test Filter',
      filterMetadata: JSON.stringify({ key: 'value' }),
    }

    const testChunk: ChunkMetadata = {
      partitionKey: testUserId,
      rowKey: `chunk-${dayjs.utc().valueOf()}`,
      mainEntityId: filterId,
      isChunk: true,
      chunkIndex: 0,
      chunkData: 'chunk data',
    }

    // Verify clean initial state
    const initialFilters = await tableStorageWrapper.queryEntities<SavedFilter>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(initialFilters.length).toBe(0)

    // Insert test entities
    await tableStorageWrapper.insertEntity<SavedFilter>(testFilter)
    await tableStorageWrapper.insertEntity<ChunkMetadata>(testChunk)

    // Verify filter and chunk were inserted
    const filters = await tableStorageWrapper.queryEntities<SavedFilter>(
      `PartitionKey eq '${testUserId}'`
    )
    const chunks = await tableStorageWrapper.queryEntities<ChunkMetadata>(
      `PartitionKey eq '${testUserId}' and mainEntityId eq '${filterId}'`
    )
    expect(filters.length).toBe(2)
    expect(chunks.length).toBe(1)

    // Reset mock before test
    mockRedisHelper.del.mockClear();

    // Act
    await deleteSavedFilter(tableStorageWrapper, testUserId, filterId)

    // Assert
    const remainingFilters =
      await tableStorageWrapper.queryEntities<SavedFilter>(
        `PartitionKey eq '${testUserId}'`
      )
    const remainingChunks =
      await tableStorageWrapper.queryEntities<ChunkMetadata>(
        `PartitionKey eq '${testUserId}' and mainEntityId eq '${filterId}'`
      )
    expect(remainingFilters.length).toBe(0)
    expect(remainingChunks.length).toBe(0)

    // Verify redisHelper.del was called with the correct key
    expect(mockRedisHelper.del).toHaveBeenCalledTimes(1);
    expect(mockRedisHelper.del).toHaveBeenCalledWith(`savedFilters.${testUserId}`);
  })

  test('should not affect other user filters and chunks when deleting', async () => {
    // Arrange
    const otherUserId = `other-user-${dayjs.utc().valueOf()}`
    const filterId = `filter-${dayjs.utc().valueOf()}`

    const testFilters: SavedFilter[] = [
      {
        partitionKey: testUserId,
        rowKey: filterId,
        userId: testUserId,
        filterName: 'Test Filter',
        filterMetadata: JSON.stringify({ key: 'value1' }),
        isChunk: false,
      },
      {
        partitionKey: otherUserId,
        rowKey: `other-${filterId}`,
        userId: otherUserId,
        filterName: 'Other Filter',
        filterMetadata: JSON.stringify({ key: 'value2' }),
        isChunk: false,
      },
    ]

    // Insert test data sequentially
    for (const filter of testFilters) {
      await tableStorageWrapper.insertEntity<SavedFilter>(filter)
    }

    // Reset mock before test
    mockRedisHelper.del.mockClear();

    // Act
    await deleteSavedFilter(tableStorageWrapper, testUserId, filterId)

    // Assert
    const remainingFilters =
      await tableStorageWrapper.queryEntities<SavedFilter>(
        `PartitionKey eq '${otherUserId}' and isChunk ne true`
      )

    expect(remainingFilters.length).toBe(1)
    expect(remainingFilters[0]).toEqual(
      expect.objectContaining({
        filterName: 'Other Filter',
        partitionKey: otherUserId,
      })
    )

    // Verify redisHelper.del was called with the correct key
    expect(mockRedisHelper.del).toHaveBeenCalledTimes(1);
    expect(mockRedisHelper.del).toHaveBeenCalledWith(`savedFilters.${testUserId}`);
  })

  test('should handle non-existent filter by throwing ResourceNotFound', async () => {
    // Reset mock before test
    mockRedisHelper.del.mockClear();

    // Act & Assert
    await expect(
      deleteSavedFilter(tableStorageWrapper, testUserId, 'non-existent-filter')
    ).rejects.toThrow(/ResourceNotFound/)

    // Verify redisHelper.del was not called since the function throws an error
    expect(mockRedisHelper.del).not.toHaveBeenCalled();
  })
})
