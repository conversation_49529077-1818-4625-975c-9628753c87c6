import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { SavedFilter } from '@/types/savedFilter'
import { SelectionType } from '@/enums/selectionType'
import { MigrationConfig } from '@/types/migrationConfig'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'

const mockRedisHelper = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
}

jest.unstable_mockModule('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => {
    return fn()
  }),
  redisHelper: mockRedisHelper
}))

dayjs.extend(utc)

let tableStorageWrapper: AzureTableStorageWrapper
const testUserId = `test-user-${dayjs.utc().valueOf()}`
const testOrgId = 'test-org-id'

let getAllSavedFilters: (storage: AzureTableStorageWrapper, userId: string, migrationConfig?: MigrationConfig) => Promise<SavedFilter[]>

beforeAll(async () => {
  getAllSavedFilters = (await import('@/services/savedFilters/getAllSavedFilters')).getAllSavedFilters
})

beforeEach(async () => {
  tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedFilters}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
  )
  await tableStorageWrapper.createTable()
})

afterEach(async () => {
  await tableStorageWrapper.deleteTable()
})

describe('getAllSavedFilters', () => {

  test('should return empty array when no filters exist', async () => {
    const results = await getAllSavedFilters(tableStorageWrapper, testUserId)
    expect(results).toEqual([])
  })

  test('should return all saved filters for a user', async () => {
    // Insert test filters
    const testFilters: SavedFilter[] = [
      {
        partitionKey: testUserId,
        rowKey: `filter-1-${dayjs.utc().valueOf()}`,
        userId: testUserId,
        filterName: 'Test Filter 1',
        filterMetadata: JSON.stringify({
          page: 'measures',
          measures: [],
          hospitals: [],
        }),
        isChunk: false,
      },
      {
        partitionKey: testUserId,
        rowKey: `filter-2-${dayjs.utc().valueOf()}`,
        userId: testUserId,
        filterName: 'Test Filter 2',
        filterMetadata: JSON.stringify({
          page: 'measures',
          measures: [],
          hospitals: [],
        }),
        isChunk: false,
      },
    ]

    // Insert filters one by one
    for (const filter of testFilters) {
      await tableStorageWrapper.insertEntity<SavedFilter>(filter)
    }

    const results = await getAllSavedFilters(tableStorageWrapper, testUserId)

    expect(results.length).toBe(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ filterName: 'Test Filter 1' }),
        expect.objectContaining({ filterName: 'Test Filter 2' }),
      ])
    )
  })

  test('should only return filters for specified user', async () => {
    const otherUserId = `other-user-${dayjs.utc().valueOf()}`

    await tableStorageWrapper.insertEntity<SavedFilter>({
      partitionKey: testUserId,
      rowKey: `filter-1-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      filterName: 'Test User Filter',
      filterMetadata: JSON.stringify({ key: 'value1' }),
      isChunk: false,
    })

    await tableStorageWrapper.insertEntity<SavedFilter>({
      partitionKey: otherUserId,
      rowKey: `filter-2-${dayjs.utc().valueOf()}`,
      userId: otherUserId,
      filterName: 'Other User Filter',
      filterMetadata: JSON.stringify({ key: 'value2' }),
      isChunk: false,
    })

    const results = await getAllSavedFilters(tableStorageWrapper, testUserId)

    expect(results.length).toBe(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        filterName: 'Test User Filter',
        partitionKey: testUserId,
      })
    )
  })

  describe('with migrationConfig', () => {
    test('should migrate SQL filters when selectionType is not Partner', async () => {
      // Mock SQL results
      const sqlResults = [
        {
          Id: 1,
          UserId: testUserId,
          FilterName: 'SQL Filter 1',
          FilterMetadata: JSON.stringify({
            page: 'measures',
            measures: ['measure1'],
            hospitals: ['hospital1'],
          }),
        },
        {
          Id: 2,
          UserId: testUserId,
          FilterName: 'SQL Filter 2',
          FilterMetadata: JSON.stringify({
            page: 'measures',
            measures: ['measure2'],
            hospitals: ['hospital2'],
          }),
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            savedFilters: {
              findMany: jest.fn().mockResolvedValue(sqlResults as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllSavedFilters(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results.length).toBe(2)
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            filterName: 'SQL Filter 1',
            partitionKey: testUserId,
            rowKey: '1',
          }),
          expect.objectContaining({
            filterName: 'SQL Filter 2',
            partitionKey: testUserId,
            rowKey: '2',
          }),
        ])
      )

      // Verify filters were migrated to table storage
      const storedFilters =
        await tableStorageWrapper.queryEntities<SavedFilter>(
          `PartitionKey eq '${testUserId}'`
        )
      expect(storedFilters.length).toBe(2)
    })

    test('should handle empty SQL results during migration', async () => {
      // Mock empty SQL results
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            savedFilters: {
              findMany: jest.fn().mockResolvedValue([] as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllSavedFilters(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should handle missing prisma client during migration', async () => {
      // Clear mock prisma client
      globalThis.prismaClients = {}

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllSavedFilters(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should migrate when migrationConfig exists and no filters exist in table storage', async () => {
      // Mock SQL results
      const sqlFilters = [
        {
          Id: 1,
          UserId: testUserId,
          FilterName: 'SQL Filter 1',
          FilterMetadata: JSON.stringify({
            page: 'measures',
            measures: ['measure1'],
            hospitals: ['hospital1'],
          }),
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            savedFilters: {
              findMany: jest.fn().mockResolvedValue(sqlFilters as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Verify no filters exist initially
      const initialFilters = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(initialFilters).toHaveLength(0)

      // Act
      const results = await getAllSavedFilters(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: '1',
          filterName: 'SQL Filter 1',
          filterMetadata: JSON.stringify({
            page: 'measures',
            measures: ['measure1'],
            hospitals: ['hospital1'],
          }),
        })
      )

      // Verify filters were migrated to table storage
      const storedFilters = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedFilters).toHaveLength(1)
    })

    test('should not migrate when migrationConfig exists but filters already exist in table storage', async () => {
      // First create some existing filters
      const existingFilter = {
        partitionKey: testUserId,
        rowKey: 'existing-1',
        userId: testUserId,
        filterName: 'Existing Filter',
        filterMetadata: JSON.stringify({
          page: 'measures',
          measures: [],
          hospitals: [],
        }),
        isChunk: false,
      }
      await tableStorageWrapper.insertEntity(existingFilter)

      // Mock SQL results that shouldn't be migrated
      const sqlFilters = [
        {
          Id: 'sql-1',
          UserId: testUserId,
          FilterName: 'SQL Filter',
          FilterMetadata: JSON.stringify({
            page: 'measures',
            measures: ['measure1'],
            hospitals: ['hospital1'],
          }),
        },
      ]

      // Mock prisma client
      const mockFindMany = jest.fn().mockResolvedValue(sqlFilters as never)
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            savedFilters: {
              findMany: mockFindMany,
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllSavedFilters(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: 'existing-1',
          filterName: 'Existing Filter',
        })
      )

      // Verify no additional filters were migrated
      const storedFilters = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedFilters).toHaveLength(1)
      expect(mockFindMany).not.toHaveBeenCalled()
    })
  })
})
