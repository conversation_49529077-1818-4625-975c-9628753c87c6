import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { SavedFilter } from '@/types/savedFilter'
import { ChunkMetadata } from '@/types/filterQuery'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)
import { jest } from '@jest/globals'

let tableStorageWrapper: AzureTableStorageWrapper
const testUserId = `test-user-${dayjs.utc().valueOf()}`

const mockRedisHelper = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
}

jest.unstable_mockModule('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => {
    return fn()
  }),
  redisHelper: mockRedisHelper
}))

let upsertSavedFilter: (
  tableStorageWrapper: AzureTableStorageWrapper,
  filter: SavedFilter,
  chunkSize?: number
) => Promise<{
  [p: string]: unknown
  filterName: string
  filterMetadata: string
  userId: string
  partitionKey: string
  rowKey: string
  isChunk: boolean
} | ({ filterName: string; filterMetadata: string; userId: string } & Record<string, unknown> & {
  partitionKey: string
  rowKey: string
})>

beforeAll(async () => {
  upsertSavedFilter = (await import('@/services/savedFilters/upsertSavedFilter')).upsertSavedFilter
})

beforeEach(async () => {
  tableStorageWrapper = new AzureTableStorageWrapper(
    // @ts-ignore
    'SavedFiltersIntegrationTest-' + dayjs.utc().valueOf()
  )
  await tableStorageWrapper.createTable()
})

afterEach(async () => {
  await tableStorageWrapper.deleteTable()
})

describe('upsertSavedFilter', () => {
  test('should insert new filter when it does not exist', async () => {
    // Arrange
    const filterId = `filter-${dayjs.utc().valueOf()}`
    const testFilter: SavedFilter = {
      partitionKey: testUserId,
      rowKey: filterId,
      userId: testUserId,
      filterName: 'Test Filter',
      filterMetadata: JSON.stringify({ key: 'value' }),
    }

    // Reset mock before test
    mockRedisHelper.del.mockClear()

    // Act
    await upsertSavedFilter(tableStorageWrapper, testFilter)

    // Assert
    const filters = await tableStorageWrapper.queryEntities<SavedFilter>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(filters.length).toBe(1)
    expect(filters[0]).toMatchObject({
      partitionKey: testUserId,
      rowKey: filterId,
      userId: testUserId,
      filterName: 'Test Filter',
      filterMetadata: JSON.stringify({ key: 'value' }),
    })

    // Verify redisHelper.del was called with the correct key
    expect(mockRedisHelper.del).toHaveBeenCalledTimes(1)
    expect(mockRedisHelper.del).toHaveBeenCalledWith(`savedFilters.${testUserId}`)
  })

  test('should update existing filter', async () => {
    // Arrange
    const filterId = `filter-${dayjs.utc().valueOf()}`
    const initialFilter: SavedFilter = {
      partitionKey: testUserId,
      rowKey: filterId,
      userId: testUserId,
      filterName: 'Initial Filter',
      filterMetadata: JSON.stringify({ key: 'initial' }),
    }

    await tableStorageWrapper.insertEntity<SavedFilter>(initialFilter)

    const updatedFilter: SavedFilter = {
      partitionKey: testUserId,
      rowKey: filterId,
      userId: testUserId,
      filterName: 'Updated Filter',
      filterMetadata: JSON.stringify({ key: 'updated' }),
    }

    // Reset mock before test
    mockRedisHelper.del.mockClear()

    // Act
    await upsertSavedFilter(tableStorageWrapper, updatedFilter)

    // Assert
    const filters = await tableStorageWrapper.queryEntities<SavedFilter>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(filters.length).toBe(1)
    expect(filters[0]).toMatchObject({
      partitionKey: testUserId,
      rowKey: filterId,
      userId: testUserId,
      filterName: 'Updated Filter',
      filterMetadata: JSON.stringify({ key: 'updated' }),
    })

    // Verify redisHelper.del was called with the correct key
    expect(mockRedisHelper.del).toHaveBeenCalledTimes(1)
    expect(mockRedisHelper.del).toHaveBeenCalledWith(`savedFilters.${testUserId}`)
  })

  test('should not affect other user filters when upserting', async () => {
    // Arrange
    const otherUserId = `other-user-${dayjs.utc().valueOf()}`
    const filterId = `filter-${dayjs.utc().valueOf()}`

    const otherUserFilter: SavedFilter = {
      partitionKey: otherUserId,
      rowKey: `other-${filterId}`,
      userId: otherUserId,
      filterName: 'Other Filter',
      filterMetadata: JSON.stringify({ key: 'other' }),
    }

    await tableStorageWrapper.insertEntity<SavedFilter>(otherUserFilter)

    const newFilter: SavedFilter = {
      partitionKey: testUserId,
      rowKey: filterId,
      userId: testUserId,
      filterName: 'Test Filter',
      filterMetadata: JSON.stringify({ key: 'value' }),
    }

    // Reset mock before test
    mockRedisHelper.del.mockClear()

    // Act
    await upsertSavedFilter(tableStorageWrapper, newFilter)

    // Assert
    const otherUserFilters =
      await tableStorageWrapper.queryEntities<SavedFilter>(
        `PartitionKey eq '${otherUserId}'`
      )
    expect(otherUserFilters.length).toBe(1)
    expect(otherUserFilters[0]).toMatchObject(otherUserFilter)

    // Verify redisHelper.del was called with the correct key
    expect(mockRedisHelper.del).toHaveBeenCalledTimes(1)
    expect(mockRedisHelper.del).toHaveBeenCalledWith(`savedFilters.${testUserId}`)
    // Verify it was not called with the other user's ID
    expect(mockRedisHelper.del).not.toHaveBeenCalledWith(`savedFilters.${otherUserId}`)
  })

  test('should handle filter with complex metadata', async () => {
    // Arrange
    const filterId = `filter-${dayjs.utc().valueOf()}`
    const complexMetadata = {
      measures: ['measure1', 'measure2'],
      hospitals: ['hospital1', 'hospital2'],
      groups: ['group1'],
      providers: ['provider1', 'provider2'],
      page: 'measures',
      measureType: 'type1',
      facilities: ['facility1'],
    }

    const testFilter: SavedFilter = {
      partitionKey: testUserId,
      rowKey: filterId,
      userId: testUserId,
      filterName: 'Complex Filter',
      filterMetadata: JSON.stringify(complexMetadata),
    }

    // Reset mock before test
    mockRedisHelper.del.mockClear()

    // Act
    await upsertSavedFilter(tableStorageWrapper, testFilter)

    // Assert
    const filters = await tableStorageWrapper.queryEntities<SavedFilter>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(filters.length).toBe(1)
    expect(filters[0]).toMatchObject(testFilter)
    expect(JSON.parse(filters[0]?.filterMetadata!)).toEqual(complexMetadata)

    // Verify redisHelper.del was called with the correct key
    expect(mockRedisHelper.del).toHaveBeenCalledTimes(1)
    expect(mockRedisHelper.del).toHaveBeenCalledWith(`savedFilters.${testUserId}`)
  })

  test('should chunk data when length is greater than chunk size', async () => {
    // Arrange
    const filterId = `filter-${dayjs.utc().valueOf()}`

    // Create a large metadata object that will exceed the chunk size
    // Use a string with commas between items to ensure proper JSON formatting
    const largeArray = Array(100).fill('test-data-that-takes-up-space').join(',')
    const largeMetadata = {
      measures: largeArray,
      hospitals: largeArray,
      groups: largeArray,
      providers: largeArray,
      page: 'measures',
      measureType: 'type1',
      facilities: largeArray,
    }

    const metadataString = JSON.stringify(largeMetadata)

    const testFilter: SavedFilter = {
      partitionKey: testUserId,
      rowKey: filterId,
      userId: testUserId,
      filterName: 'Large Filter',
      filterMetadata: metadataString,
    }

    // Use a small chunk size to force chunking
    const smallChunkSize = 1000

    // Reset mock before test
    mockRedisHelper.del.mockClear()

    // Act
    await upsertSavedFilter(tableStorageWrapper, testFilter, smallChunkSize)

    // Assert
    // 1. Check that the main filter entity exists
    const mainFilter = await tableStorageWrapper.getEntity<SavedFilter>(testUserId, filterId)
    expect(mainFilter).toBeDefined()
    if (mainFilter) {
      expect(mainFilter.filterName).toBe('Large Filter')
      // The main entity should have either empty metadata or a reference to chunks
      expect(mainFilter.isChunk).toBe(false)
    }

    // 2. Check that chunks were created
    const chunks = await tableStorageWrapper.queryEntities<ChunkMetadata>(
      `PartitionKey eq '${testUserId}' and isChunk eq true and mainEntityId eq '${filterId}'`
    )

    chunks.sort((a, b) => a.chunkIndex! < b.chunkIndex! ? -1 : 1)

    // Verify we have multiple chunks
    expect(chunks.length).toBeGreaterThan(1)

    // 3. Verify chunk properties
    chunks.forEach((chunk, index) => {
      expect(chunk.mainEntityId).toBe(filterId)
      expect(chunk.isChunk).toBe(true)
      expect(chunk.chunkIndex).toBe(index)
      expect(chunk.chunkData).toBeDefined()
      expect(typeof chunk.chunkData).toBe('string')
      expect(chunk.chunkData!.length).toBeLessThanOrEqual(smallChunkSize)
    })

    // 4. Verify that the chunks together contain the complete metadata
    const reassembledData = chunks
      .sort((a, b) => (a.chunkIndex || 0) - (b.chunkIndex || 0))
      .map(chunk => chunk.chunkData)
      .join('')

    expect(reassembledData).toBe(metadataString)

    // Verify redisHelper.del was called with the correct key
    expect(mockRedisHelper.del).toHaveBeenCalledTimes(1)
    expect(mockRedisHelper.del).toHaveBeenCalledWith(`savedFilters.${testUserId}`)
  })
})
