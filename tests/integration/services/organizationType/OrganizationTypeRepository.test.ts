import { env } from '@/env'
import { createSnowflakeConfiguration } from '@/services/snowflake/SnowflakeHelper'
import { factory } from '@/services/snowflake/SnowflakeRepositoryFactory'

describe('OrganizationTypeRepository Integration Tests', () => {
  test('should find organization types by entity types', async () => {
    const repo = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createOrganizationTypeRepository()
    if (env.SNOWFLAKE_ACCOUNT) {
      const expectedResults = [
        {
          OrganizationTypeCode: '4',
          EntityTypeCode: 'Rendering Provider',
        },
        {
          OrganizationTypeCode: '2',
          EntityTypeCode: 'Facility',
        },
        {
          OrganizationTypeCode: '3',
          EntityTypeCode: 'Facility',
        },
        {
          OrganizationTypeCode: '3',
          EntityTypeCode: 'Rendering Provider',
        },
        {
          OrganizationTypeCode: '1',
          EntityTypeCode: 'Rendering Provider',
        },
        {
          OrganizationTypeCode: '2',
          EntityTypeCode: 'Rendering Provider',
        },
      ]

      const results = await repo.findOrganizationTypesByEntityTypes()
      results.forEach((result: any) => {
        const record = expectedResults.find(
          (r) =>
            r.EntityTypeCode === result.EntityTypeCode &&
            r.OrganizationTypeCode === result.OrganizationTypeCode
        )
        expect(result.EntityTypeCode).toEqual(record?.EntityTypeCode)
        expect(result.OrganizationTypeCode).toEqual(
          record?.OrganizationTypeCode
        )
      })
      expect(results.length).toEqual(expectedResults.length)
    }
  })
})
