import { ProcedureStatusRepository } from '@/services/procedureStatus/ProcedureStatusRepository'
import { factory, closePool } from '@/services/snowflake/SnowflakeRepositoryFactory'
import {
    createConnection,
    createSnowflakeConfiguration, executeAsAResultSet,
    executeAsSingleValue
} from '@/services/snowflake/SnowflakeHelper'
import { env } from '@/env'
import { ProcedureStatus } from '@/services/procedureStatus/ProcedureStatus'

const fixtureDataId = 9999999
const connectionConfiguration = {
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE'
}
const connection = createConnection(connectionConfiguration)

let procedureRepository: ProcedureStatusRepository

beforeAll(() => {
    // Create the repository using the factory method with Snowflake configuration
    procedureRepository = factory({
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE'
    }).createProcedureRepository()
})

beforeEach(async () => {
    await executeAsSingleValue(connection,
        `DELETE FROM AUD."LoadProcedureStatus" WHERE "Id" = ${fixtureDataId}`)
})

// Clean up resources after all tests
afterAll(async () => {
    await executeAsSingleValue(connection,
        `DELETE FROM AUD."LoadProcedureStatus" WHERE "Id" = ${fixtureDataId}`)
    await closePool()
})

describe('ProcedureRepository', () => {

    describe('findByJobName', () => {
        test('should return procedure status records for a specific job name', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const jobName = "OAK2ADMLoad"

            // Act
            const results = await procedureRepository.findByJobName(jobName)

            // Assert
            expect(results).toBeDefined()
            expect(Array.isArray(results)).toBe(true)

            // If we have results, verify their structure
            if (results!.length > 0) {
                results!.forEach((result: ProcedureStatus) => {
                    expect(result.JobName).toBe(jobName)
                    expect(result.Id).toBeDefined()
                    expect(result.SuccessStatus).toBeDefined()

                    // Validate StartDateTime is a valid date
                    expect(result.StartDateTime).toBeDefined()
                    expect(result.StartDateTime.getTime()).not.toBeNaN()

                    // Validate EndDateTime if it exists
                    if (result.EndDateTime) {
                        expect(result.EndDateTime.getTime()).not.toBeNaN()
                    }

                    // Other fields may be null depending on the data
                })
            }
        })

        test('should handle empty results for non-existent job name', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const jobName = "NonExistentJobName"

            // Act
            const results = await procedureRepository.findByJobName(jobName)

            // Assert
            expect(results).toBeDefined()
            expect(Array.isArray(results)).toBe(true)
            expect(results!.length).toBe(0)
        })
    })

    describe('isReportPrecompiling', () => {
        test('should return true for report precompiling', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const testDate = new Date()
            await executeAsAResultSet(connection,
                `INSERT INTO AUD."LoadProcedureStatus" 
                ("Id", "JobName", "StartDateTime", "EndDateTime", "ApplicationUserId", "SuccessStatus") 
                VALUES 
                (${fixtureDataId}, 'Precompile Reports', '${testDate.toISOString()}', null, '', false)`)

            // Act
            const result = await procedureRepository.isReportPrecompiling()

            // Assert
            expect(result).toBeDefined()
            expect(result).toBe(true)
        })

        test('should return false for report precompiling', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
                return
            }

            // Arrange
            const testDate = new Date()
            await executeAsAResultSet(connection,
                `INSERT INTO AUD."LoadProcedureStatus" 
                ("Id", "JobName", "StartDateTime", "EndDateTime", "ApplicationUserId", "SuccessStatus") 
                VALUES 
                (${fixtureDataId}, 'Precompile Complete', '${testDate.toISOString()}', null, '', false)`)

            // Act
            const result = await procedureRepository.isReportPrecompiling()

            // Assert
            expect(result).toBeDefined()
            expect(result).toBe(false)
        })
    })
})
