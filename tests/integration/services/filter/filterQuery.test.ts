import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { FilterQuery } from '@/types/filterQuery'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from "@jest/globals";
import { MigrationConfig } from "@/types/migrationConfig";
import { SavedFilter } from "@/types/savedFilter";
import { StatusResponse } from "@/types/statusResponse";

dayjs.extend(utc)

const mockRedisHelper = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
}

jest.unstable_mockModule('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => {
    return fn()
  }),
  redisHelper: mockRedisHelper
}))

let getAllSavedFilters: (storage: AzureTableStorageWrapper, userId: string, migrationConfig?: MigrationConfig) => Promise<SavedFilter[]>
let filterQuery: (query: FilterQuery, organizationId: string) => Promise<StatusResponse>

beforeAll(async () => {
  filterQuery = (await import('@/services/filter/filterQuery')).filterQuery
  getAllSavedFilters = (await import('@/services/savedFilters/getAllSavedFilters')).getAllSavedFilters
})


describe('filterQuery', () => {
  const testOrganizationId = `test-org-${dayjs.utc().valueOf()}`
  const testUserId = `test-user-${dayjs.utc().valueOf()}`
  let savedFilterStorage: AzureTableStorageWrapper

  beforeEach(() => {
    savedFilterStorage = new AzureTableStorageWrapper(
      StorageTables.SavedFilters
    )
  })

  // Cleanup helper function
  const cleanupTestFilters = async () => {
    const filters = await getAllSavedFilters(savedFilterStorage, testUserId)
    for (const filter of filters) {
      await savedFilterStorage.deleteEntity(filter.partitionKey, filter.rowKey)
    }
  }

  afterEach(async () => {
    await cleanupTestFilters()
  })

  it('should handle duplicate filter names', async () => {
    // Arrange
    const filterName = `Test Filter ${dayjs.utc().valueOf()}`

    const query: FilterQuery = {
      filterName,
      userId: testUserId,
      partnerId: '',
      isPartner: false,
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: ['measure1'],
      hospitals: ['hospital1'],
      groups: [],
      providers: ['provider1'],
      organizations: [],
      facilities: [],
    }

    // Act - First filter creation
    const firstResult = await filterQuery(query, testOrganizationId)

    // Assert first creation successful
    expect(firstResult.success).toBe(true)
    expect(firstResult.message).toBe('Filter saved successfully')

    // Act - Attempt to create duplicate
    const duplicateResult = await filterQuery(query, testOrganizationId)

    // Assert duplicate was rejected
    expect(duplicateResult.success).toBe(false)
    expect(duplicateResult.message).toBe('Filter name already exists')

    // Verify in storage
    const savedFilters = await getAllSavedFilters(
      savedFilterStorage,
      testUserId
    )
    expect(savedFilters.length).toBe(1)
  })

  it('should allow same filter name for different measure types', async () => {
    // Arrange
    const timestamp = dayjs.utc().valueOf()
    const filterName = `Measure Type Test ${timestamp}`

    const baseQuery: Omit<FilterQuery, 'measureType'> = {
      filterName,
      userId: testUserId,
      partnerId: '',
      isPartner: false,
      page: 'measures',
      measures: ['measure1'],
      hospitals: ['hospital1'],
      groups: [],
      providers: ['provider1'],
      organizations: [],
      facilities: [],
    }

    // Act & Assert - Ambulatory Measures
    const ambulatoryQuery = {
      ...baseQuery,
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
    }
    const ambulatoryResult = await filterQuery(
      ambulatoryQuery,
      testOrganizationId
    )

    expect(ambulatoryResult.success).toBe(true)
    expect(ambulatoryResult.message).toBe('Filter saved successfully')

    // Act & Assert - Hospital Measures
    const hospitalQuery = {
      ...baseQuery,
      measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
    }
    const hospitalResult = await filterQuery(hospitalQuery, testOrganizationId)

    expect(hospitalResult.success).toBe(true)
    expect(hospitalResult.message).toBe('Filter saved successfully')

    // Verify in storage
    const savedFilters = await getAllSavedFilters(
      savedFilterStorage,
      testUserId
    )
    expect(savedFilters.length).toBe(2)

    const measureTypes = savedFilters.map(
      (filter) => JSON.parse(filter.filterMetadata).measureType
    )
    expect(measureTypes).toContain(
      PrimaryMeasureTypeConstants.AmbulatoryMeasures
    )
    expect(measureTypes).toContain(PrimaryMeasureTypeConstants.HospitalMeasures)
  })

  it('should handle empty filter creation', async () => {
    // Arrange
    const query: FilterQuery = {
      filterName: `Empty Filter ${dayjs.utc().valueOf()}`,
      userId: testUserId,
      partnerId: '',
      isPartner: false,
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      organizations: [],
      facilities: [],
    }

    // Act
    const result = await filterQuery(query, testOrganizationId)

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe('Filter saved successfully')

    // Verify in storage
    const savedFilters = await getAllSavedFilters(
      savedFilterStorage,
      testUserId
    )
    expect(savedFilters.length).toBe(1)
  })
})
