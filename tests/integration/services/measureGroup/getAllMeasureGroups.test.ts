import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { getAllMeasureGroups } from '@/services/measureGroup/getAllMeasureGroups'
import { MeasureGroup } from '@/types/measureGroup'
import { SelectionType } from '@/enums/selectionType'
import { MigrationConfig } from '@/types/migrationConfig'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'

dayjs.extend(utc)

describe('getAllMeasureGroups', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.MeasureGroups}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return empty array when no measure groups exist', async () => {
    const results = await getAllMeasureGroups(tableStorageWrapper, testOrgId)
    expect(results).toEqual([])
  })

  test('should return all measure groups for an organization', async () => {
    // Arrange
    const testGroups: MeasureGroup[] = [
      {
        partitionKey: testOrgId,
        rowKey: `group1-${dayjs.utc().valueOf()}`,
        groupId: 1,
        measureGuid: 'measure-guid-1',
        percentileSourceId: 1,
      },
      {
        partitionKey: testOrgId,
        rowKey: `group2-${dayjs.utc().valueOf()}`,
        groupId: 2,
        measureGuid: 'measure-guid-2',
        percentileSourceId: 1,
      },
    ]

    // Add test groups
    for (const group of testGroups) {
      await tableStorageWrapper.insertEntity<MeasureGroup>(group)
    }

    // Act
    const results = await getAllMeasureGroups(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toHaveLength(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: testOrgId,
          groupId: 1,
          measureGuid: 'measure-guid-1',
          percentileSourceId: 1,
        }),
        expect.objectContaining({
          partitionKey: testOrgId,
          groupId: 2,
          measureGuid: 'measure-guid-2',
          percentileSourceId: 1,
        }),
      ])
    )
  })

  test('should only return measure groups for specified organization', async () => {
    // Arrange
    const otherOrgId = `other-org-${dayjs.utc().valueOf()}`
    const testGroups: MeasureGroup[] = [
      {
        partitionKey: testOrgId,
        rowKey: `group1-${dayjs.utc().valueOf()}`,
        groupId: 1,
        measureGuid: 'measure-guid-1',
        percentileSourceId: 1,
      },
      {
        partitionKey: otherOrgId,
        rowKey: `group2-${dayjs.utc().valueOf()}`,
        groupId: 2,
        measureGuid: 'measure-guid-2',
        percentileSourceId: 1,
      },
    ]

    // Add test groups
    for (const group of testGroups) {
      await tableStorageWrapper.insertEntity<MeasureGroup>(group)
    }

    // Act
    const results = await getAllMeasureGroups(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        groupId: 1,
        measureGuid: 'measure-guid-1',
        percentileSourceId: 1,
      })
    )
  })

  describe('with migrationConfig', () => {
    test('should migrate and return measure groups when migrationConfig is provided', async () => {
      // Mock SQL results
      const sqlMeasureGroups = [
        {
          Id: 1,
          GroupId: 101,
          MeasureGuid: 'measure-guid-1',
          PercentileSourceId: 1,
        },
        {
          Id: 2,
          GroupId: 102,
          MeasureGuid: 'measure-guid-2',
          PercentileSourceId: 2,
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          admClient: {
            measureGroup: {
              findMany: jest.fn().mockResolvedValue(sqlMeasureGroups as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      const measureGuid = 'measure-guid-1'

      // Act
      const results = await getAllMeasureGroups(
        tableStorageWrapper,
        measureGuid,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(2)
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            partitionKey: measureGuid,
            rowKey: '1',
            groupId: 101,
            measureGuid: 'measure-guid-1',
            percentileSourceId: 1,
          }),
          expect.objectContaining({
            partitionKey: measureGuid,
            rowKey: '2',
            groupId: 102,
            measureGuid: 'measure-guid-2',
            percentileSourceId: 2,
          }),
        ])
      )
    })

    test('should handle empty SQL results during migration', async () => {
      // Mock empty SQL results
      globalThis.prismaClients = {
        [testOrgId]: {
          admClient: {
            measureGroup: {
              findMany: jest.fn().mockResolvedValue([] as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllMeasureGroups(
        tableStorageWrapper,
        'measure-guid-1',
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should handle missing prisma client during migration', async () => {
      // Clear mock prisma client
      globalThis.prismaClients = {}

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllMeasureGroups(
        tableStorageWrapper,
        'measure-guid-1',
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should migrate when migrationConfig exists and no measure groups exist in table storage', async () => {
      // Mock SQL results
      const sqlMeasureGroups = [
        {
          Id: 1,
          GroupId: 101,
          MeasureGuid: 'measure-guid-1',
          PercentileSourceId: 1,
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          admClient: {
            measureGroup: {
              findMany: jest.fn().mockResolvedValue(sqlMeasureGroups as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      const measureGuid = 'measure-guid-1'

      // Verify no measure groups exist initially
      const initialGroups = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${measureGuid}'`
      )
      expect(initialGroups).toHaveLength(0)

      // Act
      const results = await getAllMeasureGroups(
        tableStorageWrapper,
        measureGuid,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: measureGuid,
          rowKey: '1',
          groupId: 101,
          measureGuid: 'measure-guid-1',
          percentileSourceId: 1,
        })
      )

      // Verify measure groups were migrated to table storage
      const storedGroups = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${measureGuid}'`
      )
      expect(storedGroups).toHaveLength(1)
    })

    test('should not migrate when migrationConfig exists but measure groups already exist in table storage', async () => {
      const measureGuid = 'measure-guid-1'

      // First create some existing measure groups
      const existingGroup = {
        partitionKey: measureGuid,
        rowKey: 'existing-1',
        groupId: 999,
        measureGuid: measureGuid,
        percentileSourceId: 1,
      }
      await tableStorageWrapper.insertEntity(existingGroup)

      // Mock SQL results that shouldn't be migrated
      const sqlMeasureGroups = [
        {
          Id: 1,
          GroupId: 101,
          MeasureGuid: measureGuid,
          PercentileSourceId: 2,
        },
      ]

      // Mock prisma client
      const mockFindMany = jest
        .fn()
        .mockResolvedValue(sqlMeasureGroups as never)
      globalThis.prismaClients = {
        [testOrgId]: {
          admClient: {
            measureGroup: {
              findMany: mockFindMany,
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllMeasureGroups(
        tableStorageWrapper,
        measureGuid,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: measureGuid,
          rowKey: 'existing-1',
          groupId: 999,
          measureGuid: measureGuid,
          percentileSourceId: 1,
        })
      )

      // Verify no additional measure groups were migrated
      const storedGroups = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${measureGuid}'`
      )
      expect(storedGroups).toHaveLength(1)
      expect(mockFindMany).not.toHaveBeenCalled()
    })
  })
})
