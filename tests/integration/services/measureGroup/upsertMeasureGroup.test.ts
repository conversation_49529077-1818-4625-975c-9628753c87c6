import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { upsertMeasureGroup } from '@/services/measureGroup/upsertMeasureGroup'
import { MeasureGroup } from '@/types/measureGroup'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('upsertMeasureGroup', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.MeasureGroups}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert a new measure group', async () => {
    // Arrange
    const measureGroup: MeasureGroup = {
      partitionKey: testOrgId,
      rowKey: `group1-${dayjs.utc().valueOf()}`,
      groupId: 1,
      measureGuid: 'measure-guid-1',
      percentileSourceId: 1,
    }

    // Act
    const result = await upsertMeasureGroup(tableStorageWrapper, measureGroup)

    // Assert
    expect(result).toBeDefined()
    expect(result.partitionKey).toBe(measureGroup.partitionKey)
    expect(result.rowKey).toBe(measureGroup.rowKey)
    expect(result.groupId).toBe(measureGroup.groupId)
    expect(result.measureGuid).toBe(measureGroup.measureGuid)
    expect(result.percentileSourceId).toBe(measureGroup.percentileSourceId)
  })

  test('should update an existing measure group', async () => {
    // Arrange
    const rowKey = `group1-${dayjs.utc().valueOf()}`
    const originalGroup: MeasureGroup = {
      partitionKey: testOrgId,
      rowKey,
      groupId: 1,
      measureGuid: 'measure-guid-1',
      percentileSourceId: 1,
    }
    await tableStorageWrapper.insertEntity<MeasureGroup>(originalGroup)

    const updatedGroup: MeasureGroup = {
      ...originalGroup,
      measureGuid: 'measure-guid-2',
      percentileSourceId: 2,
    }

    // Act
    const result = await upsertMeasureGroup(tableStorageWrapper, updatedGroup)

    // Assert
    expect(result).toBeDefined()
    expect(result.partitionKey).toBe(updatedGroup.partitionKey)
    expect(result.rowKey).toBe(updatedGroup.rowKey)
    expect(result.groupId).toBe(updatedGroup.groupId)
    expect(result.measureGuid).toBe(updatedGroup.measureGuid)
    expect(result.percentileSourceId).toBe(updatedGroup.percentileSourceId)

    // Verify the update in storage
    const storedGroup = await tableStorageWrapper.getEntity<MeasureGroup>(
      testOrgId,
      rowKey
    )
    expect(storedGroup).toEqual(
      expect.objectContaining({
        partitionKey: updatedGroup.partitionKey,
        rowKey: updatedGroup.rowKey,
        groupId: updatedGroup.groupId,
        measureGuid: updatedGroup.measureGuid,
        percentileSourceId: updatedGroup.percentileSourceId,
      })
    )
  })
})
