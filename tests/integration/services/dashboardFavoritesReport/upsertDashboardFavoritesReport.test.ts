import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardFavoritesReport } from '@/types/dashboardFavoritesReport'
import { upsertDashboardFavoritesReport } from '@/services/dashboardFavoritesReport/upsertDashboardFavoritesReport'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'
import { StorageTables } from '@/enums/storageTables'

dayjs.extend(utc)

describe('upsertDashboardFavoritesReport', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = 'test-user-123'

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.DashboardFavoritesReport}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert new dashboard favorite when it does not exist', async () => {
    // Arrange
    const testFavorite: DashboardFavoritesReport = {
      partitionKey: testUserId,
      rowKey: `favorite-${dayjs.utc().valueOf()}`,
      dashboardId: 'dashboard-1',
      isAddedToFavourites: true,
      userId: testUserId,
    }

    // Act
    const result = await upsertDashboardFavoritesReport(
      tableStorageWrapper,
      testFavorite
    )

    // Assert
    expect(result).toBeDefined()
    expect(result.partitionKey).toBe(testFavorite.partitionKey)
    expect(result.rowKey).toBe(testFavorite.rowKey)
    expect(result.dashboardId).toBe(testFavorite.dashboardId)
    expect(result.isAddedToFavourites).toBe(testFavorite.isAddedToFavourites)
    expect(result.userId).toBe(testFavorite.userId)

    // Verify in storage
    const savedFavorites =
      await tableStorageWrapper.queryEntities<DashboardFavoritesReport>(
        `PartitionKey eq '${testUserId}'`
      )
    expect(savedFavorites.length).toBe(1)
    expect(savedFavorites[0]).toMatchObject(testFavorite)
  })

  test('should update existing dashboard favorite', async () => {
    // Arrange
    const favoriteId = `favorite-${dayjs.utc().valueOf()}`
    const initialFavorite: DashboardFavoritesReport = {
      partitionKey: testUserId,
      rowKey: favoriteId,
      dashboardId: 'dashboard-1',
      isAddedToFavourites: true,
      userId: testUserId,
    }

    // Insert initial favorite
    await tableStorageWrapper.insertEntity(initialFavorite)

    // Update favorite
    const updatedFavorite: DashboardFavoritesReport = {
      ...initialFavorite,
      isAddedToFavourites: false,
    }

    // Act
    const result = await upsertDashboardFavoritesReport(
      tableStorageWrapper,
      updatedFavorite
    )

    // Assert
    expect(result).toBeDefined()
    expect(result.isAddedToFavourites).toBe(false)

    // Verify in storage
    const savedFavorites =
      await tableStorageWrapper.queryEntities<DashboardFavoritesReport>(
        `PartitionKey eq '${testUserId}'`
      )
    expect(savedFavorites.length).toBe(1)
    expect(savedFavorites[0]!.isAddedToFavourites).toBe(false)
  })

  test('should handle multiple upserts for same user', async () => {
    // Arrange
    const favorites: DashboardFavoritesReport[] = [
      {
        partitionKey: testUserId,
        rowKey: `favorite-${dayjs.utc().valueOf()}-1`,
        dashboardId: 'dashboard-1',
        isAddedToFavourites: true,
        userId: testUserId,
      },
      {
        partitionKey: testUserId,
        rowKey: `favorite-${dayjs.utc().valueOf()}-2`,
        dashboardId: 'dashboard-2',
        isAddedToFavourites: false,
        userId: testUserId,
      },
    ]

    // Act
    const results = await Promise.all(
      favorites.map((favorite) =>
        upsertDashboardFavoritesReport(tableStorageWrapper, favorite)
      )
    )

    // Assert
    expect(results).toHaveLength(2)

    // Verify in storage
    const savedFavorites =
      await tableStorageWrapper.queryEntities<DashboardFavoritesReport>(
        `PartitionKey eq '${testUserId}'`
      )
    expect(savedFavorites).toHaveLength(2)
    savedFavorites.forEach((saved, index) => {
      expect(saved).toMatchObject(favorites[index]!)
    })
  })

  test('should handle favorite with minimal required fields', async () => {
    // Arrange
    const minimalFavorite: DashboardFavoritesReport = {
      partitionKey: testUserId,
      rowKey: `favorite-${dayjs.utc().valueOf()}`,
      dashboardId: 'dashboard-1',
      isAddedToFavourites: true,
      userId: testUserId,
    }

    // Act
    const result = await upsertDashboardFavoritesReport(
      tableStorageWrapper,
      minimalFavorite
    )

    // Assert
    expect(result).toBeDefined()
    expect(result).toMatchObject(minimalFavorite)

    // Verify in storage
    const savedFavorites =
      await tableStorageWrapper.queryEntities<DashboardFavoritesReport>(
        `PartitionKey eq '${testUserId}'`
      )
    expect(savedFavorites.length).toBe(1)
    expect(savedFavorites[0]).toMatchObject(minimalFavorite)
  })

  test('should handle error from table storage', async () => {
    // Arrange
    const testFavorite: DashboardFavoritesReport = {
      partitionKey: testUserId,
      rowKey: `favorite-${dayjs.utc().valueOf()}`,
      dashboardId: 'dashboard-1',
      isAddedToFavourites: true,
      userId: testUserId,
    }

    // Mock table storage error
    jest
      .spyOn(tableStorageWrapper, 'upsertEntity')
      .mockRejectedValueOnce(new Error('Storage error'))

    // Act & Assert
    await expect(
      upsertDashboardFavoritesReport(tableStorageWrapper, testFavorite)
    ).rejects.toThrow('Storage error')
  })
})
