import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardFavoritesReport } from '@/types/dashboardFavoritesReport'
import { getAllDashboardFavoritesReport } from '@/services/dashboardFavoritesReport/getAllDashboardFavoritesReport'
import { MigrationConfig } from '@/types/migrationConfig'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { StorageTables } from '@/enums/storageTables'
import { SelectionType } from '@/enums/selectionType'
import { jest } from '@jest/globals'

dayjs.extend(utc)

describe('getAllDashboardFavoritesReport', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = 'test-user-123'
  const testOrgId = 'test-org-id'

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.DashboardFavoritesReport}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
    // Clear mock prisma client
    globalThis.prismaClients = {}
  })

  test('should return empty array when no favorites exist', async () => {
    const results = await getAllDashboardFavoritesReport(
      tableStorageWrapper,
      testUserId
    )

    expect(results).toBeDefined()
    expect(Array.isArray(results)).toBeTruthy()
    expect(results.length).toBe(0)
  })

  test('should return all dashboard favorites for a user', async () => {
    const testFavorites: DashboardFavoritesReport[] = [
      {
        partitionKey: testUserId,
        rowKey: `favorite-${dayjs.utc().valueOf()}-1`,
        dashboardId: 'dashboard-1',
        isAddedToFavourites: true,
        userId: testUserId,
      },
      {
        partitionKey: testUserId,
        rowKey: `favorite-${dayjs.utc().valueOf()}-2`,
        dashboardId: 'dashboard-2',
        isAddedToFavourites: false,
        userId: testUserId,
      },
    ]

    await Promise.all(
      testFavorites.map((favorite) =>
        tableStorageWrapper.insertEntity(favorite)
      )
    )

    const results = await getAllDashboardFavoritesReport(
      tableStorageWrapper,
      testUserId
    )

    expect(results.length).toBe(2)
    results.forEach((result, index) => {
      expect(result.partitionKey).toBe(testFavorites[index]!.partitionKey)
      expect(result.dashboardId).toBe(testFavorites[index]!.dashboardId)
      expect(result.isAddedToFavourites).toBe(
        testFavorites[index]!.isAddedToFavourites
      )
      expect(result.userId).toBe(testFavorites[index]!.userId)
    })
  })

  describe('with migrationConfig', () => {
    test('should migrate and return dashboard favorites when migrationConfig is provided', async () => {
      // Mock SQL results
      const sqlFavorites = [
        {
          Id: 1,
          DashboardId: 'dashboard-1',
          UserId: testUserId,
          IsAddedToFavourites: true,
        },
        {
          Id: 2,
          DashboardId: 'dashboard-2',
          UserId: testUserId,
          IsAddedToFavourites: false,
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardFavouritesReport: {
              findMany: jest.fn().mockResolvedValue(sqlFavorites as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardFavoritesReport(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(2)
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            partitionKey: testUserId,
            rowKey: '1',
            dashboardId: 'dashboard-1',
            isAddedToFavourites: true,
            userId: testUserId,
          }),
          expect.objectContaining({
            partitionKey: testUserId,
            rowKey: '2',
            dashboardId: 'dashboard-2',
            isAddedToFavourites: false,
            userId: testUserId,
          }),
        ])
      )

      // Verify favorites were migrated to table storage
      const storedFavorites =
        await tableStorageWrapper.queryEntities<DashboardFavoritesReport>(
          `PartitionKey eq '${testUserId}'`
        )
      expect(storedFavorites).toHaveLength(2)
    })

    test('should handle empty SQL results during migration', async () => {
      // Mock empty SQL results
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardFavouritesReport: {
              findMany: jest.fn().mockResolvedValue([] as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardFavoritesReport(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should handle missing prisma client during migration', async () => {
      // Clear mock prisma client
      globalThis.prismaClients = {}

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardFavoritesReport(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should migrate when migrationConfig exists and no favorites exist in table storage', async () => {
      // Mock SQL results
      const sqlFavorites = [
        {
          Id: 1,
          DashboardId: 'dashboard-1',
          UserId: testUserId,
          IsAddedToFavourites: true,
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardFavouritesReport: {
              findMany: jest.fn().mockResolvedValue(sqlFavorites as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Verify no favorites exist initially
      const initialFavorites = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(initialFavorites).toHaveLength(0)

      // Act
      const results = await getAllDashboardFavoritesReport(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: '1',
          dashboardId: 'dashboard-1',
          isAddedToFavourites: true,
          userId: testUserId,
        })
      )

      // Verify favorites were migrated to table storage
      const storedFavorites = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedFavorites).toHaveLength(1)
    })

    test('should not migrate when migrationConfig exists but favorites already exist in table storage', async () => {
      // First create some existing favorites
      const existingFavorite = {
        partitionKey: testUserId,
        rowKey: 'existing-1',
        dashboardId: 'existing-dashboard',
        isAddedToFavourites: true,
        userId: testUserId,
      }
      await tableStorageWrapper.insertEntity(existingFavorite)

      // Mock SQL results that shouldn't be migrated
      const sqlFavorites = [
        {
          Id: 'sql-1',
          DashboardId: 'sql-dashboard',
          UserId: testUserId,
          IsAddedToFavourites: false,
        },
      ]

      // Mock prisma client
      const mockFindMany = jest.fn().mockResolvedValue(sqlFavorites as never)
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardFavouritesReport: {
              findMany: mockFindMany,
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardFavoritesReport(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: 'existing-1',
          dashboardId: 'existing-dashboard',
        })
      )

      // Verify no additional favorites were migrated
      const storedFavorites = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedFavorites).toHaveLength(1)
      expect(mockFindMany).not.toHaveBeenCalled() // Changed this line
    })

    test('should migrate data when migrationConfig exists and no data exists', async () => {
      // Mock SQL results
      const sqlFavorites = [
        {
          Id: 1,
          DashboardId: 'dashboard-1',
          UserId: testUserId,
          IsAddedToFavourites: true,
        },
        {
          Id: 2,
          DashboardId: 'dashboard-2',
          UserId: testUserId,
          IsAddedToFavourites: false,
        },
      ]

      // Mock prisma client
      const mockFindMany = jest.fn().mockResolvedValue(sqlFavorites as never)
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardFavouritesReport: {
              findMany: mockFindMany,
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Verify no data exists initially
      const initialData = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(initialData).toHaveLength(0)

      // Act
      const results = await getAllDashboardFavoritesReport(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(mockFindMany).toHaveBeenCalledTimes(1)
      expect(results).toHaveLength(2)
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            partitionKey: testUserId,
            rowKey: '1',
            dashboardId: 'dashboard-1',
            isAddedToFavourites: true,
            userId: testUserId,
          }),
          expect.objectContaining({
            partitionKey: testUserId,
            rowKey: '2',
            dashboardId: 'dashboard-2',
            isAddedToFavourites: false,
            userId: testUserId,
          }),
        ])
      )

      // Verify data was migrated to table storage
      const storedData = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedData).toHaveLength(2)
    })

    test('should skip migration when migrationConfig exists but data already exists', async () => {
      // First create existing data
      const existingFavorite = {
        partitionKey: testUserId,
        rowKey: 'existing-1',
        dashboardId: 'existing-dashboard',
        isAddedToFavourites: true,
        userId: testUserId,
      }
      await tableStorageWrapper.insertEntity(existingFavorite)

      // Mock SQL results that shouldn't be migrated
      const sqlFavorites = [
        {
          Id: 1,
          DashboardId: 'sql-dashboard',
          UserId: testUserId,
          IsAddedToFavourites: false,
        },
      ]

      // Mock prisma client
      const mockFindMany = jest.fn().mockResolvedValue(sqlFavorites as never)
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardFavouritesReport: {
              findMany: mockFindMany,
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardFavoritesReport(
        tableStorageWrapper,
        testUserId,
        migrationConfig
      )

      // Assert
      expect(mockFindMany).not.toHaveBeenCalled()
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: 'existing-1',
          dashboardId: 'existing-dashboard',
          isAddedToFavourites: true,
          userId: testUserId,
        })
      )

      // Verify only original data exists in table storage
      const storedData = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedData).toHaveLength(1)
      expect(storedData[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: 'existing-1',
          dashboardId: 'existing-dashboard',
          isAddedToFavourites: true,
          userId: testUserId,
        })
      )
    })
  })
})
