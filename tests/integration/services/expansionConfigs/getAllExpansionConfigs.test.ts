import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { getAllExpansionConfigs } from '@/services/expansionConfigs/getAllExpansionConfigs'
import {
  EntityLevelConstants,
  ExpansionConfiguration,
} from '@/types/expansionConfiguration'
import { StorageTables } from '@/enums/storageTables'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('getAllExpansionConfigs', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = 'test-org-id'

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.ExpansionConfigs}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return empty array when no configs exist', async () => {
    // Act
    const results = await getAllExpansionConfigs(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toBeDefined()
    expect(Array.isArray(results)).toBeTruthy()
    expect(results.length).toBe(0)
  })

  test('should return all expansion configs for an organization', async () => {
    // Arrange
    const testConfigs: ExpansionConfiguration[] = [
      {
        partitionKey: testOrgId,
        rowKey: `config1-${dayjs.utc().valueOf()}`,
        name: 'Test Config 1',
        isExpanded: true,
        label: 'Test Label',
        measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
        entries: JSON.stringify([]), // Serialize the array
        selectedLevel: EntityLevelConstants.None,
      },
      {
        partitionKey: testOrgId,
        rowKey: `config2-${dayjs.utc().valueOf()}`,
        name: 'Test Config 2',
        isExpanded: false,
        label: 'Test Label 2',
        measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
        entries: JSON.stringify([]), // Serialize the array
        selectedLevel: EntityLevelConstants.None,
      },
    ]

    // Add test configs
    for (const config of testConfigs) {
      await tableStorageWrapper.insertEntity<ExpansionConfiguration>(config)
    }

    // Act
    const results = await getAllExpansionConfigs(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toBeDefined()
    expect(results.length).toBe(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: testOrgId,
          name: 'Test Config 1',
          isExpanded: true,
        }),
        expect.objectContaining({
          partitionKey: testOrgId,
          name: 'Test Config 2',
          isExpanded: false,
        }),
      ])
    )
  })

  test('should only return configs for specified organization', async () => {
    // Arrange
    const otherOrgId = 'other-org-id'
    const testConfigs: ExpansionConfiguration[] = [
      {
        partitionKey: testOrgId,
        rowKey: `config1-${dayjs.utc().valueOf()}`,
        name: 'Test Config 1',
        isExpanded: true,
        label: 'Test Label',
        measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
        entries: JSON.stringify([]), // Serialize the array
        selectedLevel: EntityLevelConstants.None,
      },
      {
        partitionKey: otherOrgId,
        rowKey: `config2-${dayjs.utc().valueOf()}`,
        name: 'Test Config 2',
        isExpanded: false,
        label: 'Test Label 2',
        measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
        entries: JSON.stringify([]), // Serialize the array
        selectedLevel: EntityLevelConstants.None,
      },
    ]

    // Add test configs
    for (const config of testConfigs) {
      await tableStorageWrapper.insertEntity<ExpansionConfiguration>(config)
    }

    // Act
    const results = await getAllExpansionConfigs(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toBeDefined()
    expect(results.length).toBe(1)
    expect(results[0]).toMatchObject({
      partitionKey: testOrgId,
      name: 'Test Config 1',
      isExpanded: true,
    })
  })
})
