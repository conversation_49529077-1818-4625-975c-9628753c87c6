import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { upsertExpansionConfigurations } from '@/services/expansionConfigs/upsertExpansionConfigs'
import {
  EntityLevelConstants,
  ExpansionConfiguration,
} from '@/types/expansionConfiguration'
import { StorageTables } from '@/enums/storageTables'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'

dayjs.extend(utc)

describe('upsertExpansionConfigurations', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = 'test-org-id'

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.ExpansionConfigs}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert new expansion configuration when it does not exist', async () => {
    // Arrange
    const testConfig: ExpansionConfiguration = {
      partitionKey: testOrgId,
      rowKey: `config-${dayjs.utc().valueOf()}`,
      configName: 'Test Config',
      configValue: JSON.stringify({ expanded: true }),
      label: 'Test Label',
      measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
      entries: JSON.stringify([]),
      selectedLevel: EntityLevelConstants.None,
    }

    // Act
    const result = await upsertExpansionConfigurations(
      tableStorageWrapper,
      testConfig
    )

    // Assert
    expect(result).toBeDefined()
    expect(result.partitionKey).toBe(testConfig.partitionKey)
    expect(result.rowKey).toBe(testConfig.rowKey)
    expect(result.configName).toBe(testConfig.configName)
    expect(result.configValue).toBe(testConfig.configValue)

    // Verify in storage
    const configs =
      await tableStorageWrapper.queryEntities<ExpansionConfiguration>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(configs.length).toBe(1)
    expect(configs[0]).toMatchObject({
      partitionKey: testOrgId,
      configName: 'Test Config',
      configValue: JSON.stringify({ expanded: true }),
    })
  })

  test('should update existing expansion configuration', async () => {
    // Arrange
    const configId = `config-${dayjs.utc().valueOf()}`
    const initialConfig: ExpansionConfiguration = {
      partitionKey: testOrgId,
      rowKey: configId,
      configName: 'Initial Config',
      configValue: JSON.stringify({ expanded: false }),
      label: 'Test Label',
      measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
      entries: JSON.stringify([]),
      selectedLevel: EntityLevelConstants.None,
    }

    // Insert initial config
    await tableStorageWrapper.insertEntity<ExpansionConfiguration>(
      initialConfig
    )

    // Updated config
    const updatedConfig: ExpansionConfiguration = {
      ...initialConfig,
      configName: 'Updated Config',
      configValue: JSON.stringify({ expanded: true }),
    }

    // Act
    const result = await upsertExpansionConfigurations(
      tableStorageWrapper,
      updatedConfig
    )

    // Assert
    expect(result).toBeDefined()
    expect(result.configName).toBe('Updated Config')
    expect(result.configValue).toBe(JSON.stringify({ expanded: true }))

    // Verify in storage
    const configs =
      await tableStorageWrapper.queryEntities<ExpansionConfiguration>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(configs.length).toBe(1)
    expect(configs[0]).toMatchObject({
      partitionKey: testOrgId,
      rowKey: configId,
      configName: 'Updated Config',
      configValue: JSON.stringify({ expanded: true }),
    })
  })

  test('should handle minimal required fields', async () => {
    // Arrange
    const minimalConfig: ExpansionConfiguration = {
      partitionKey: testOrgId,
      rowKey: `config-${dayjs.utc().valueOf()}`,
      configName: 'Minimal Config',
      configValue: '{}',
      label: 'Minimal Label',
      measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
      entries: JSON.stringify([]),
      selectedLevel: EntityLevelConstants.None,
    }

    // Act
    const result = await upsertExpansionConfigurations(
      tableStorageWrapper,
      minimalConfig
    )

    // Assert
    expect(result).toBeDefined()
    expect(result.partitionKey).toBe(minimalConfig.partitionKey)
    expect(result.rowKey).toBe(minimalConfig.rowKey)
    expect(result.configName).toBe(minimalConfig.configName)
    expect(result.configValue).toBe(minimalConfig.configValue)

    // Verify in storage
    const configs =
      await tableStorageWrapper.queryEntities<ExpansionConfiguration>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(configs.length).toBe(1)
    expect(configs[0]).toMatchObject({
      partitionKey: testOrgId,
      configName: 'Minimal Config',
      configValue: '{}',
    })
  })
})
