import { jest } from '@jest/globals'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import { getDropDownValues } from '@/services/admin/expansionConfiguration/getDropDownValues'
import MeasureResultsService from '@/services/measureResults'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { EntityTypeConstants } from '@/enums/entityTypeConstants'
import { EntityLevelConstants } from '@/types/expansionConfiguration'
import { ExtensionLevels } from '@/enums/extensionLevels'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('getDropDownValues', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  // Mock MeasureResultsService
  const mockMeasureResultsService = {
    findOrganizationTypesByEntityTypes: jest.fn(),
  } as unknown as MeasureResultsService

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.ExpansionConfigs}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()

    // Reset mock
    jest.clearAllMocks()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return hospital measures configurations for facility entity type', async () => {
    // Arrange
    mockMeasureResultsService.findOrganizationTypesByEntityTypes = jest
      .fn<
        () => Promise<
          { OrganizationTypeCode: string; EntityTypeCode: string }[]
        >
      >()
      .mockResolvedValue([
        {
          EntityTypeCode: EntityTypeConstants.Facility,
          OrganizationTypeCode: '1',
        },
      ] as never)

    // Act
    const result = await getDropDownValues(
      tableStorageWrapper,
      mockMeasureResultsService,
      false, // isPartner
      testOrgId,
      [PrimaryMeasureTypeConstants.HospitalMeasures]
    )

    // Assert
    expect(result).toHaveLength(1)
    expect(result[0]).toMatchObject({
      partitionKey: testOrgId,
      rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level1}`,
      level: ExtensionLevels.level1,
      label: 'Level 1 Entities',
      measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
      selectedLevel: EntityLevelConstants.None,
    })
    expect(JSON.parse(result[0]!.entries)).toEqual([
      EntityLevelConstants.TopLevel,
      EntityLevelConstants.DoNotDisplay,
    ])
  })

  test('should return different configurations for partner users', async () => {
    // Arrange
    mockMeasureResultsService.findOrganizationTypesByEntityTypes = jest
      .fn<
        () => Promise<
          { OrganizationTypeCode: string; EntityTypeCode: string }[]
        >
      >()
      .mockResolvedValue([
        {
          EntityTypeCode: EntityTypeConstants.Facility,
          OrganizationTypeCode: '1',
        },
      ])

    // Act
    const result = await getDropDownValues(
      tableStorageWrapper,
      mockMeasureResultsService,
      true, // isPartner
      testOrgId,
      [PrimaryMeasureTypeConstants.HospitalMeasures]
    )

    // Assert
    expect(result).toHaveLength(1)
    expect(result[0]).toMatchObject({
      partitionKey: testOrgId,
      rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level1}`,
      level: ExtensionLevels.level1,
      measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
    })
    expect(JSON.parse(result[0]!.entries)).toEqual([
      EntityLevelConstants.TopLevel,
    ])
  })

  test('should return ambulatory measures configurations for rendering provider entity type', async () => {
    // Arrange
    mockMeasureResultsService.findOrganizationTypesByEntityTypes = jest
      .fn<
        () => Promise<
          { OrganizationTypeCode: string; EntityTypeCode: string }[]
        >
      >()
      .mockResolvedValue([
        {
          EntityTypeCode: EntityTypeConstants.RenderingProvider,
          OrganizationTypeCode: '1',
        },
      ])

    // Act
    const result = await getDropDownValues(
      tableStorageWrapper,
      mockMeasureResultsService,
      false, // isPartner
      testOrgId,
      [PrimaryMeasureTypeConstants.AmbulatoryMeasures]
    )

    // Assert
    expect(result).toHaveLength(1)
    expect(result[0]).toMatchObject({
      partitionKey: testOrgId,
      rowKey: `${PrimaryMeasureTypeConstants.AmbulatoryMeasures}.${ExtensionLevels.level1}`,
      level: ExtensionLevels.level1,
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      selectedLevel: EntityLevelConstants.None,
    })
    expect(JSON.parse(result[0]!.entries)).toEqual([
      EntityLevelConstants.TopLevel,
      EntityLevelConstants.DoNotDisplay,
    ])
  })

  test('should use stored values for selectedLevel when available', async () => {
    // Arrange
    mockMeasureResultsService.findOrganizationTypesByEntityTypes = jest
      .fn<
        () => Promise<
          { OrganizationTypeCode: string; EntityTypeCode: string }[]
        >
      >()
      .mockResolvedValue([
        {
          EntityTypeCode: EntityTypeConstants.Facility,
          OrganizationTypeCode: '1',
        },
      ])

    // Insert existing config
    await tableStorageWrapper.insertEntity({
      partitionKey: testOrgId,
      rowKey: `${PrimaryMeasureTypeConstants.HospitalMeasures}.${ExtensionLevels.level1}`,
      level: ExtensionLevels.level1,
      measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
      id: ExtensionLevels.level1,
      selectedLevel: EntityLevelConstants.TopLevel,
      label: 'Existing Config',
    })

    // Act
    const result = await getDropDownValues(
      tableStorageWrapper,
      mockMeasureResultsService,
      false,
      testOrgId,
      [PrimaryMeasureTypeConstants.HospitalMeasures]
    )

    // Assert
    expect(result).toHaveLength(1)
    expect(result[0]!.selectedLevel).toBe(EntityLevelConstants.TopLevel)
  })
})
