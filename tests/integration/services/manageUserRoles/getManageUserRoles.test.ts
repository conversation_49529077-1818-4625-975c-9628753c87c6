import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import { getManageUserRoles } from '@/services/manageUserRoles/getManageUserRoles'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { ManageUserRole } from '@/types/manageUserRole'
import { randomUUID } from 'crypto'
import { jest } from '@jest/globals'
import { SelectionType } from '@/enums/selectionType'

dayjs.extend(utc)

describe('getManageUserRoles', () => {
  let tableStorageWrapper: AzureTableStorageWrapper

  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  const testUserId = `test-user-${dayjs.utc().valueOf()}`
  const testEntitiesIds = [
    BigInt('1111'),
    BigInt('2222'),
    BigInt('3333'),
    BigInt('4444'),
  ]

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.ManageUserRoles}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return empty array when no user roles exist', async () => {
    const results = await getManageUserRoles(
      tableStorageWrapper,
      testOrgId,
      testUserId,
      testEntitiesIds
    )
    expect(results).toEqual([])
  })

  test('should return user roles when they exist', async () => {
    // Arrange
    const testRole: ManageUserRole = {
      partitionKey: testOrgId,
      rowKey: randomUUID(),
      entitiesId: testEntitiesIds[0]!,
      organizationId: testOrgId,
      userId: testUserId,
      canAccess: true,
      canDrillDown: true,
    }

    await tableStorageWrapper.insertEntity(testRole)

    // Act
    const results = await getManageUserRoles(
      tableStorageWrapper,
      testOrgId,
      testUserId,
      testEntitiesIds
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toMatchObject({
      partitionKey: testOrgId,
      userId: testUserId,
      entitiesId: testEntitiesIds[0],
    })
  })

  test('should filter by organization ID and user ID', async () => {
    // Arrange
    const testRoles: ManageUserRole[] = [
      {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: testEntitiesIds[1]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      },
      {
        partitionKey: 'different-org',
        rowKey: randomUUID(),
        entitiesId: testEntitiesIds[2]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      },
      {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: testEntitiesIds[3]!,
        roleType: 'viewer',
        organizationId: testOrgId,
        userId: `different-${testUserId}`,
        canAccess: true,
        canDrillDown: true,
      },
    ]

    for (const role of testRoles) {
      await tableStorageWrapper.insertEntity(role)
    }

    // Act
    const results = await getManageUserRoles(
      tableStorageWrapper,
      testOrgId,
      testUserId,
      testEntitiesIds
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toMatchObject({
      partitionKey: testOrgId,
      userId: testUserId,
      entitiesId: testEntitiesIds[1],
    })
  })

  test('should filter by entities IDs', async () => {
    // Arrange
    const testRoles: ManageUserRole[] = [
      {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: testEntitiesIds[0]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      },
      {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: testEntitiesIds[1]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      },
    ]

    // Insert entities sequentially to avoid conflicts
    for (const role of testRoles) {
      await tableStorageWrapper.insertEntity(role)
    }

    // Act
    const results = await getManageUserRoles(
      tableStorageWrapper,
      testOrgId,
      testUserId,
      testEntitiesIds
    )

    // Assert
    expect(results).toHaveLength(2) // Changed to expect both roles
    expect(results.map((r) => r.entitiesId)).toEqual(
      expect.arrayContaining([testEntitiesIds[0], testEntitiesIds[1]])
    )
  })

  test('should handle large number of entity IDs by chunking', async () => {
    // Arrange
    // Reduce the test set size to stay within Azure limits
    const largeEntityIdSet = Array.from({ length: 250 }, (_, i) =>
      BigInt(`1${i.toString().padStart(4, '0')}`)
    )

    // Create test roles for first, middle and last entities to verify range
    const testRoles: ManageUserRole[] = [
      {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: largeEntityIdSet[0]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      },
      {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: largeEntityIdSet[Math.floor(largeEntityIdSet.length / 2)]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      },
      {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: largeEntityIdSet[largeEntityIdSet.length - 1]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      },
    ]

    // Insert entities sequentially to avoid conflicts
    for (const role of testRoles) {
      await tableStorageWrapper.insertEntity(role)
    }

    // Act
    const results = await getManageUserRoles(
      tableStorageWrapper,
      testOrgId,
      testUserId,
      largeEntityIdSet
    )

    // Assert
    expect(results).toHaveLength(3)
    expect(results.map((r) => r.entitiesId)).toEqual(
      expect.arrayContaining([
        largeEntityIdSet[0],
        largeEntityIdSet[Math.floor(largeEntityIdSet.length / 2)],
        largeEntityIdSet[largeEntityIdSet.length - 1],
      ])
    )
    expect(results.every((r) => r.partitionKey === testOrgId)).toBeTruthy()
    expect(results.every((r) => r.userId === testUserId)).toBeTruthy()
  })

  test('should handle string entity IDs', async () => {
    // Arrange
    const stringEntityIds = ['entity-1', 'entity-2', 'entity-3']
    const testRoles: ManageUserRole[] = [
      {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: stringEntityIds[0]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      },
      {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: stringEntityIds[1]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      },
    ]

    // Insert entities sequentially to avoid conflicts
    for (const role of testRoles) {
      await tableStorageWrapper.insertEntity(role)
    }

    // Act
    const results = await getManageUserRoles(
      tableStorageWrapper,
      testOrgId,
      testUserId,
      stringEntityIds
    )

    // Assert
    expect(results).toHaveLength(2)
    expect(results.map((r) => r.entitiesId)).toEqual(
      expect.arrayContaining([stringEntityIds[0], stringEntityIds[1]])
    )
    expect(results.every((r) => typeof r.entitiesId === 'string')).toBeTruthy()
  })

  describe('with migrationConfig', () => {
    test('should migrate and return user roles when migrationConfig is provided', async () => {
      // Mock SQL results
      const sqlUserRoles = [
        {
          Id: 1,
          OrganizationId: testOrgId,
          UserId: testUserId,
          EntitiesId: testEntitiesIds[0]!,
          CanAccess: true,
          CanDrillDown: true,
        },
        {
          Id: 2,
          OrganizationId: testOrgId,
          UserId: testUserId,
          EntitiesId: testEntitiesIds[1]!,
          CanAccess: true,
          CanDrillDown: false,
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          admClient: {
            manageUserRoles: {
              findMany: jest.fn().mockResolvedValue(sqlUserRoles as never),
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getManageUserRoles(
        tableStorageWrapper,
        testOrgId,
        testUserId,
        testEntitiesIds,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(2)
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            partitionKey: testOrgId,
            userId: testUserId,
            entitiesId: testEntitiesIds[0],
            canAccess: true,
            canDrillDown: true,
          }),
          expect.objectContaining({
            partitionKey: testOrgId,
            userId: testUserId,
            entitiesId: testEntitiesIds[1],
            canAccess: true,
            canDrillDown: false,
          }),
        ])
      )

      // Verify roles were migrated to table storage
      const storedRoles = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testOrgId}'`
      )
      expect(storedRoles).toHaveLength(2)
    })

    test('should handle empty SQL results during migration', async () => {
      // Mock empty SQL results
      globalThis.prismaClients = {
        [testOrgId]: {
          admClient: {
            manageUserRoles: {
              findMany: jest.fn().mockResolvedValue([] as never),
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getManageUserRoles(
        tableStorageWrapper,
        testOrgId,
        testUserId,
        testEntitiesIds,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should handle missing prisma client during migration', async () => {
      // Clear mock prisma client
      globalThis.prismaClients = {}

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getManageUserRoles(
        tableStorageWrapper,
        testOrgId,
        testUserId,
        testEntitiesIds,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should not migrate when migrationConfig exists but roles already exist in table storage', async () => {
      // First create some existing roles
      const existingRole = {
        partitionKey: testOrgId,
        rowKey: randomUUID(),
        entitiesId: testEntitiesIds[0]!,
        organizationId: testOrgId,
        userId: testUserId,
        canAccess: true,
        canDrillDown: true,
      }
      await tableStorageWrapper.insertEntity(existingRole)

      // Mock SQL results that shouldn't be migrated
      const sqlUserRoles = [
        {
          Id: 1,
          OrganizationId: testOrgId,
          UserId: testUserId,
          EntitiesId: testEntitiesIds[1]!.toString(),
          CanAccess: true,
          CanDrillDown: true,
        },
      ]

      // Mock prisma client
      const mockFindMany = jest.fn().mockResolvedValue(sqlUserRoles as never)
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            manageUserRoles: {
              findMany: mockFindMany,
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getManageUserRoles(
        tableStorageWrapper,
        testOrgId,
        testUserId,
        testEntitiesIds,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testOrgId,
          entitiesId: testEntitiesIds[0],
          userId: testUserId,
          canAccess: true,
          canDrillDown: true,
        })
      )

      // Verify no additional roles were migrated
      const storedRoles = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testOrgId}'`
      )
      expect(storedRoles).toHaveLength(1)
      expect(mockFindMany).not.toHaveBeenCalled()
    })
  })
})
