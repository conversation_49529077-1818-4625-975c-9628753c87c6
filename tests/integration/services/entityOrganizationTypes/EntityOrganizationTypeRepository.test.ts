import {
  closePool,
  factory,
} from '@/services/snowflake/SnowflakeRepositoryFactory'
import { createSnowflakeConfiguration } from '@/services/snowflake/SnowflakeHelper'
import {
  EntityOrganizationTypeRepository,
  EntityCodeType,
} from '@/services/entityOrganizationType/EntityOrganizationTypeRepository'
import { IntervalType } from '@/types/intervalType'
import { env } from '@/env'

// Clean up resources after all tests
afterAll(async () => {
  await closePool()
})

describe('OrganizationTypeRepository', () => {
  let organizationTypeRepository: EntityOrganizationTypeRepository

  beforeEach(() => {
    // Create the repository using the factory method with Snowflake configuration
    organizationTypeRepository = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createEntityOrganizationTypeRepository()
  })

  describe('findOrganizationTypesByMeasureAndStartDate', () => {
    test('should return organization types for monthly period type', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result: EntityCodeType) => {
          expect(result).toHaveProperty('Universe')
          expect(typeof result.Universe).toBe('string')
          expect(result.Universe).not.toBe('ACO') // As per the query condition
          expect(result).toHaveProperty('Code')
        })
      }
    })

    test('should return organization types for quarterly period type', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalType: IntervalType = 'Q'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-03-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result: EntityCodeType) => {
          expect(result).toHaveProperty('Universe')
          expect(typeof result.Universe).toBe('string')
          expect(result.Universe).not.toBe('ACO') // As per the query condition
          expect(result).toHaveProperty('Code')
        })
      }
    })

    test('should return organization types for yearly period type', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalType: IntervalType = 'Y'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-12-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result: EntityCodeType) => {
          expect(result).toHaveProperty('Universe')
          expect(typeof result.Universe).toBe('string')
          expect(result.Universe).not.toBe('ACO') // As per the query condition
          expect(result).toHaveProperty('Code')
        })
      }
    })

    test('should handle multiple measure names', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const measureIdentifiers = [
        '17b10d78-29ac-ea11-a8fe-4cedfb610c38',
        'a93bd441-7fb0-ee11-bea0-0022484ce73d',
        'd73bd441-7fb0-ee11-bea0-0022484ce73d',
      ]

      // Act
      const results =
        await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result: EntityCodeType) => {
          expect(result).toHaveProperty('Universe')
          expect(typeof result.Universe).toBe('string')
          expect(result.Universe).not.toBe('ACO') // As per the query condition
          expect(result).toHaveProperty('Code')
        })
      }
    })

    test('should handle date ranges with no data', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2010-01-01') // Using a very old date range
      const endDate = new Date('2010-01-31')

      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      // Likely no data for this date range
      expect(results.length).toBe(0)
    })

    test('should handle non-existent measure names', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const measureNames = ['NonExistentMeasure']

      // Act
      const results =
        await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
          intervalType,
          startDate,
          endDate,
          measureNames
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      // Likely no data for this measure name
      expect(results.length).toBe(0)
    })

    test('should handle future date ranges', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2030-01-01') // Future date
      const endDate = new Date('2030-01-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      // Likely no data for future dates
      expect(results.length).toBe(0)
    })

    test('should handle invalid date ranges (end before start)', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-31') // Start date after end date
      const endDate = new Date('2024-01-01')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      // Likely no data for invalid date range
      expect(results.length).toBe(0)
    })

    test('should handle large date ranges', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalType: IntervalType = 'Y'
      const startDate = new Date('2020-01-01') // 4-year range
      const endDate = new Date('2025-12-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result: EntityCodeType) => {
          expect(result).toHaveProperty('Universe')
          expect(typeof result.Universe).toBe('string')
          expect(result.Universe).not.toBe('ACO') // As per the query condition
          expect(result).toHaveProperty('Code')
        })
      }
    })

    test('should handle all period types', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const intervalTypes: IntervalType[] = ['M', 'Q', 'Y']
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2025-12-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act & Assert
      for (const intervalType of intervalTypes) {
        const results =
          await organizationTypeRepository.findEntityUniversesByMeasureAndStartDate(
            intervalType,
            startDate,
            endDate,
            measureIdentifiers
          )

        expect(results).toBeDefined()
        expect(Array.isArray(results)).toBe(true)
        expect(results.length).toBeGreaterThan(0)

        // If we have results, verify their structure
        if (results.length > 0) {
          results.forEach((result: EntityCodeType) => {
            expect(result).toHaveProperty('Universe')
            expect(typeof result.Universe).toBe('string')
            expect(result.Universe).not.toBe('ACO') // As per the query condition
            expect(result).toHaveProperty('Code')
          })
        }
      }
    })
  })

  describe('findHigherLevelOrganizationTypes', () => {
    test('should return higher level organization types for monthly period type', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      const universe = 'Organization'

      // Act
      const results =
        await organizationTypeRepository.findHigherLevelOrganizationTypes(
          code,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers,
          universe
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result: EntityCodeType) => {
          expect(result).toHaveProperty('Universe')
          expect(typeof result.Universe).toBe('string')
          expect(result.Universe).not.toBe(universe) // Should not include the provided universe
          expect(result).toHaveProperty('Code')
          expect(parseFloat(result.Code)).toBeLessThan(code) // Code should be less than the provided code
        })
      }
    })

    test('should return higher level organization types for quarterly period type', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalType: IntervalType = 'Q'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-03-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      const universe = 'Organization'

      // Act
      const results =
        await organizationTypeRepository.findHigherLevelOrganizationTypes(
          code,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers,
          universe
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result: EntityCodeType) => {
          expect(result).toHaveProperty('Universe')
          expect(typeof result.Universe).toBe('string')
          expect(result.Universe).not.toBe(universe) // Should not include the provided universe
          expect(result).toHaveProperty('Code')
          expect(parseFloat(result.Code)).toBeLessThan(code) // Code should be less than the provided code
        })
      }
    })

    test('should return higher level organization types for yearly period type', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalType: IntervalType = 'Y'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2025-12-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      const universe = 'Organization'

      // Act
      const results =
        await organizationTypeRepository.findHigherLevelOrganizationTypes(
          code,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers,
          universe
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result: EntityCodeType) => {
          expect(result).toHaveProperty('Universe')
          expect(typeof result.Universe).toBe('string')
          expect(result.Universe).not.toBe(universe) // Should not include the provided universe
          expect(result).toHaveProperty('Code')
          expect(parseFloat(result.Code)).toBeLessThan(code) // Code should be less than the provided code
        })
      }
    })

    test('should handle different universe values', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalType: IntervalType = 'Y'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-12-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      const universes = ['Organization', 'TIN'] // Different universe values to test

      // Act & Assert
      for (const universe of universes) {
        const results =
          await organizationTypeRepository.findHigherLevelOrganizationTypes(
            code,
            intervalType,
            startDate,
            endDate,
            measureIdentifiers,
            universe
          )

        expect(results).toBeDefined()
        expect(Array.isArray(results)).toBe(true)
        expect(results.length).toBeGreaterThan(0)

        // If we have results, verify their structure
        if (results.length > 0) {
          results.forEach((result: EntityCodeType) => {
            expect(result).toHaveProperty('Universe')
            expect(typeof result.Universe).toBe('string')
            expect(result.Universe).not.toBe(universe) // Should not include the provided universe
            expect(result).toHaveProperty('Code')
            expect(parseFloat(result.Code)).toBeLessThan(code) // Code should be less than the provided code
          })
        }
      }
    })

    test('should handle date ranges with no data', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2010-01-01') // Using a very old date range
      const endDate = new Date('2010-01-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      const universe = 'Organization'

      // Act
      const results =
        await organizationTypeRepository.findHigherLevelOrganizationTypes(
          code,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers,
          universe
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      // Likely no data for this date range
      expect(results.length).toBe(0)
    })

    test('should handle non-existent measure names', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const measureIdentifiers = ['12345']
      const universe = 'Organization'

      // Act
      const results =
        await organizationTypeRepository.findHigherLevelOrganizationTypes(
          code,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers,
          universe
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      // Likely no data for this measure name
      expect(results.length).toBe(0)
    })

    test('should handle future date ranges', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2030-01-01') // Future date
      const endDate = new Date('2030-01-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']
      const universe = 'Organization'

      // Act
      const results =
        await organizationTypeRepository.findHigherLevelOrganizationTypes(
          code,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers,
          universe
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      // Likely no data for future dates
      expect(results.length).toBe(0)
    })

    test('should handle invalid date ranges (end before start)', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-31') // Start date after end date
      const endDate = new Date('2024-01-01')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']
      const universe = 'Organization'

      // Act
      const results =
        await organizationTypeRepository.findHigherLevelOrganizationTypes(
          code,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers,
          universe
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      // Likely no data for invalid date range
      expect(results.length).toBe(0)
    })

    test('should handle large date ranges', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalType: IntervalType = 'Y'
      const startDate = new Date('2020-01-01') // 4-year range
      const endDate = new Date('2025-12-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']
      const universe = 'Organization'

      // Act
      const results =
        await organizationTypeRepository.findHigherLevelOrganizationTypes(
          code,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers,
          universe
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result: EntityCodeType) => {
          expect(result).toHaveProperty('Universe')
          expect(typeof result.Universe).toBe('string')
          expect(result.Universe).not.toBe(universe) // Should not include the provided universe
          expect(result).toHaveProperty('Code')
          expect(parseFloat(result.Code)).toBeLessThan(code) // Code should be less than the provided code
        })
      }
    })

    test('should handle all period types', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const code = 4 // Example code value
      const intervalTypes: IntervalType[] = ['M', 'Q', 'Y']
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2025-12-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']
      const universe = 'Organization'

      // Act & Assert
      for (const intervalType of intervalTypes) {
        const results =
          await organizationTypeRepository.findHigherLevelOrganizationTypes(
            code,
            intervalType,
            startDate,
            endDate,
            measureIdentifiers,
            universe
          )

        expect(results).toBeDefined()
        expect(Array.isArray(results)).toBe(true)
        expect(results.length).toBeGreaterThan(0)

        // If we have results, verify their structure
        if (results.length > 0) {
          results.forEach((result: EntityCodeType) => {
            expect(result).toHaveProperty('Universe')
            expect(typeof result.Universe).toBe('string')
            expect(result.Universe).not.toBe(universe) // Should not include the provided universe
            expect(result).toHaveProperty('Code')
            expect(parseFloat(result.Code)).toBeLessThan(code) // Code should be less than the provided code
          })
        }
      }
    })
  })

  describe('findEntityDescriptionByUniverseAndIntervalAndMeasure', () => {
    test('should return entity descriptions for monthly period type', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const universe = 'Organization'
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityDescriptionByUniverseAndIntervalAndMeasure(
          universe,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result) => {
          expect(result).toHaveProperty('Description')
          expect(typeof result.Description).toBe('string')
          expect(result.Description.length).toBeGreaterThan(0)
          expect(result).toHaveProperty('SourceContainerIdentifier')
          expect(typeof result.SourceContainerIdentifier).toBe('string')
        })
      }
    })

    test('should return entity descriptions for quarterly period type', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const universe = 'Organization'
      const intervalType: IntervalType = 'Q'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-03-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityDescriptionByUniverseAndIntervalAndMeasure(
          universe,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result) => {
          expect(result).toHaveProperty('Description')
          expect(typeof result.Description).toBe('string')
          expect(result.Description.length).toBeGreaterThan(0)
          expect(result).toHaveProperty('SourceContainerIdentifier')
          expect(typeof result.SourceContainerIdentifier).toBe('string')
        })
      }
    })

    test('should return entity descriptions for yearly period type', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const universe = 'Organization'
      const intervalType: IntervalType = 'Y'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-12-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityDescriptionByUniverseAndIntervalAndMeasure(
          universe,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result) => {
          expect(result).toHaveProperty('Description')
          expect(typeof result.Description).toBe('string')
          expect(result.Description.length).toBeGreaterThan(0)
          expect(result).toHaveProperty('SourceContainerIdentifier')
          expect(typeof result.SourceContainerIdentifier).toBe('string')
        })
      }
    })

    test('should handle different universe values', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const universes = ['Organization', 'TIN'] // Different universe values to test
      const intervalType: IntervalType = 'Y'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-12-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act & Assert
      for (const universe of universes) {
        const results =
          await organizationTypeRepository.findEntityDescriptionByUniverseAndIntervalAndMeasure(
            universe,
            intervalType,
            startDate,
            endDate,
            measureIdentifiers
          )

        expect(results).toBeDefined()
        expect(Array.isArray(results)).toBe(true)
        expect(results.length).toBeGreaterThan(0)

        // If we have results, verify their structure
        if (results.length > 0) {
          results.forEach((result) => {
            expect(result).toHaveProperty('Description')
            expect(typeof result.Description).toBe('string')
            expect(result.Description.length).toBeGreaterThan(0)
            expect(result).toHaveProperty('SourceContainerIdentifier')
            expect(typeof result.SourceContainerIdentifier).toBe('string')
          })
        }
      }
    })

    test('should handle different measure names', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const universe = 'Organization'
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const measureIdentifiers = [
        '17b10d78-29ac-ea11-a8fe-4cedfb610c38',
        '12345',
        '4565',
      ]

      // Act & Assert
      const results =
        await organizationTypeRepository.findEntityDescriptionByUniverseAndIntervalAndMeasure(
          universe,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      // If we have results, verify their structure
      if (results.length > 0) {
        results.forEach((result) => {
          expect(result).toHaveProperty('Description')
          expect(typeof result.Description).toBe('string')
          expect(result.Description.length).toBeGreaterThan(0)
          expect(result).toHaveProperty('SourceContainerIdentifier')
          expect(typeof result.SourceContainerIdentifier).toBe('string')
        })
      }
    })

    test('should handle non-existent universe', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const universe = 'NonExistentUniverse'
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Act
      const results =
        await organizationTypeRepository.findEntityDescriptionByUniverseAndIntervalAndMeasure(
          universe,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBe(0) // Expect no results for non-existent universe
    })

    test('should handle non-existent measure name', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const universe = 'Organization'
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const measureIdentifiers = ['17b10d7']

      // Act
      const results =
        await organizationTypeRepository.findEntityDescriptionByUniverseAndIntervalAndMeasure(
          universe,
          intervalType,
          startDate,
          endDate,
          measureIdentifiers
        )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBe(0) // Expect no results for non-existent measure
    })

    test('should handle all period types', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const universe = 'Organization'
      const intervalTypes: IntervalType[] = ['M', 'Q', 'Y']
      const measureIdentifiers = ['17b10d78-29ac-ea11-a8fe-4cedfb610c38']

      // Define date ranges for each interval type
      const dateRanges = {
        M: { start: new Date('2024-01-01'), end: new Date('2024-01-31') },
        Q: { start: new Date('2024-01-01'), end: new Date('2024-03-31') },
        Y: { start: new Date('2024-01-01'), end: new Date('2024-12-31') },
      }

      // Act & Assert
      for (const intervalType of intervalTypes) {
        const startDate = dateRanges[intervalType].start
        const endDate = dateRanges[intervalType].end

        const results =
          await organizationTypeRepository.findEntityDescriptionByUniverseAndIntervalAndMeasure(
            universe,
            intervalType,
            startDate,
            endDate,
            measureIdentifiers
          )

        expect(results).toBeDefined()
        expect(Array.isArray(results)).toBe(true)
        expect(results.length).toBeGreaterThan(0)

        // If we have results, verify their structure
        if (results.length > 0) {
          results.forEach((result) => {
            expect(result).toHaveProperty('Description')
            expect(typeof result.Description).toBe('string')
            expect(result.Description.length).toBeGreaterThan(0)
            expect(result).toHaveProperty('SourceContainerIdentifier')
            expect(typeof result.SourceContainerIdentifier).toBe('string')
          })
        }
      }
    })
  })
})
