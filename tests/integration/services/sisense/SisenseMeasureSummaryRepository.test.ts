import { TimePeriod } from '@/types/TimePeriod'
import {
  closePool,
  factory,
} from '@/services/snowflake/SnowflakeRepositoryFactory'
import { createSnowflakeConfiguration } from '@/services/snowflake/SnowflakeHelper'
import { IntervalType } from '@/types/intervalType'

// Clean up resources after all tests
afterAll(async () => {
  await closePool()
})

describe('SisenseMeasureSummaryRepository', () => {
  describe('findMeasurePerformanceByUniverse', () => {
    test('should return Measure Summary data for Yearly interval', async () => {
      // Arrange
      const sisenseMeasSummRepository = factory({
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }).createSisenseMeasureSummaryRepository()
      const intervalType: IntervalType = 'Y'
      const startDate = new Date('2024-01-01T00:00:00Z')
      const endDate = new Date('2024-02-01T00:00:00Z')
      const measures = [
        'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
      ]
      const entityDescription = 'MockingJay Ambulatory Roll Up'
      const sourceContainerIdentifier =
        'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
      const universe = 'Organization'

      // Act
      const results =
        await sisenseMeasSummRepository.findMeasurePerformanceByUniverse(
          intervalType,
          startDate,
          endDate,
          measures,
          universe,
          sourceContainerIdentifier
        )

      // Assert
      expect(results).not.toBeNull()
      expect(Array.isArray(results)).toBe(true)

      // If results are returned, verify their structure
      if (results && results.length == 1) {
        const firstResult = results[0]!
        expect(firstResult).toHaveProperty('Period', intervalType)
        expect(firstResult).toHaveProperty('MeasureName', measures[0])
        expect(firstResult).toHaveProperty(
          'EntityDescription',
          entityDescription
        )
        expect(firstResult).toHaveProperty('Performance', 85.29)
        expect(firstResult).toHaveProperty('Numerator', 661)
        expect(firstResult).toHaveProperty('PerformanceDenominator', 775)
        expect(firstResult).toHaveProperty('PTile', 3)
        expect(firstResult).toHaveProperty('MeasureIdentifier')
      }
    })
  })
  test('should return Measure Summary data for Quarterly interval', async () => {
    // Arrange
    const sisenseMeasSummRepository = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createSisenseMeasureSummaryRepository()
    const intervalType: IntervalType = 'Q'
    const startDate = new Date('2024-01-01T00:00:00Z')
    const endDate = new Date('2024-02-01T00:00:00Z')
    const measures = [
      'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
    ]
    const entityDescription = 'MockingJay Ambulatory Roll Up'
    const sourceContainerIdentifier = 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
    const universe = 'Organization'

    // Act
    const results =
      await sisenseMeasSummRepository.findMeasurePerformanceByUniverse(
        intervalType,
        startDate,
        endDate,
        measures,
        universe,
        sourceContainerIdentifier
      )

    // Assert
    expect(results).not.toBeNull()
    expect(Array.isArray(results)).toBe(true)

    // If results are returned, verify their structure
    if (results && results.length == 1) {
      const firstResult = results[0]!
      expect(firstResult).toHaveProperty('Period', intervalType)
      expect(firstResult).toHaveProperty('MeasureName', measures[0])
      expect(firstResult).toHaveProperty('EntityDescription', entityDescription)
      expect(firstResult).toHaveProperty('Performance', 85.16)
      expect(firstResult).toHaveProperty('Numerator', 654)
      expect(firstResult).toHaveProperty('PerformanceDenominator', 768)
      expect(firstResult).toHaveProperty('PTile', 3)
      expect(firstResult).toHaveProperty('MeasureIdentifier')
    }
  })

  test('should return Measure Summary data for Monthly interval', async () => {
    // Arrange
    const sisenseMeasSummRepository = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createSisenseMeasureSummaryRepository()
    const intervalType: IntervalType = 'Q'
    const startDate = new Date('2024-01-01T00:00:00Z')
    const endDate = new Date('2024-02-01T00:00:00Z')
    const measures = [
      'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
    ]
    const entityDescription = 'MockingJay Ambulatory Roll Up'
    const sourceContainerIdentifier = 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
    const universe = 'Organization'

    // Act
    const results =
      await sisenseMeasSummRepository.findMeasurePerformanceByUniverse(
        intervalType,
        startDate,
        endDate,
        measures,
        universe,
        sourceContainerIdentifier
      )

    // Assert
    expect(results).not.toBeNull()
    expect(Array.isArray(results)).toBe(true)

    // If results are returned, verify their structure
    if (results && results.length == 1) {
      const firstResult = results[0]!
      expect(firstResult).toHaveProperty('Period', intervalType)
      expect(firstResult).toHaveProperty('MeasureName', measures[0])
      expect(firstResult).toHaveProperty('EntityDescription', entityDescription)
      expect(firstResult).toHaveProperty('Performance', 85.16)
      expect(firstResult).toHaveProperty('Numerator', 654)
      expect(firstResult).toHaveProperty('PerformanceDenominator', 768)
      expect(firstResult).toHaveProperty('PTile', 3)
      expect(firstResult).toHaveProperty('MeasureIdentifier')
    }
  })

  test('should return Measure Summary data for Monthly interval at the Provider level', async () => {
    // Arrange
    const sisenseMeasSummRepository = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createSisenseMeasureSummaryRepository()
    const intervalType: IntervalType = 'Q'
    const startDate = new Date('2024-01-01T00:00:00Z')
    const endDate = new Date('2024-02-01T00:00:00Z')
    const measures = [
      'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
    ]
    const sourceContainerIdentifier = 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
    const universe = 'Medical Provider'

    // Act
    const results =
      await sisenseMeasSummRepository.findMeasurePerformanceByUniverse(
        intervalType,
        startDate,
        endDate,
        measures,
        universe,
        sourceContainerIdentifier
      )

    // Assert
    expect(results).not.toBeNull()
    expect(Array.isArray(results)).toBe(true)

    // If results are returned, verify their structure
    if (results && results.length == 83) {
      const firstResult = results[0]!
      expect(firstResult).toHaveProperty('Period', intervalType)
      expect(firstResult).toHaveProperty('MeasureName', measures[0])
      expect(firstResult).toHaveProperty(
        'Organization',
        'MockingJay Ambulatory Roll Up'
      )
      expect(firstResult).toHaveProperty(
        'EntityDescription',
        'Fast,  Alan (Mockingjay Medical Health System)'
      )
      expect(firstResult).toHaveProperty('Performance', 90.91)
      expect(firstResult).toHaveProperty('Numerator', 10)
      expect(firstResult).toHaveProperty('PerformanceDenominator', 11)
      expect(firstResult).toHaveProperty('PTile', 3)
      expect(firstResult).toHaveProperty('MeasureIdentifier')
    }
  })

  test('should return Measure Summary data for multiple measures', async () => {
    // Arrange
    const sisenseMeasSummRepository = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createSisenseMeasureSummaryRepository()
    const intervalType: IntervalType = 'M'
    const startDate = new Date('2024-01-01T00:00:00Z')
    const endDate = new Date('2024-02-01T00:00:00Z')
    const measures = [
      'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
      'Severe Obstetric Complications (Ethnicity - Not Hispanic Or Latino) (E)',
    ]
    const entityDescription = 'MockingJay Ambulatory Roll Up'
    const sourceContainerIdentifier = 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
    const universe = 'Organization'

    // Act
    const results =
      await sisenseMeasSummRepository.findMeasurePerformanceByUniverse(
        intervalType,
        startDate,
        endDate,
        measures,
        universe,
        sourceContainerIdentifier
      )

    // Assert
    expect(results).not.toBeNull()
    expect(Array.isArray(results)).toBe(true)

    // If results are returned, verify their structure
    if (results && results.length > 0) {
      // Verify structure of first result
      const firstResult = results[0]!
      expect(firstResult).toHaveProperty('Period', intervalType)
      expect(firstResult).toHaveProperty('EntityDescription', entityDescription)
      expect(firstResult).toHaveProperty('Performance')
      expect(firstResult).toHaveProperty('Numerator')
      expect(firstResult).toHaveProperty('PerformanceDenominator')
      expect(firstResult).toHaveProperty('PTile')
      expect(firstResult).toHaveProperty('MeasureIdentifier')
    }
  })
  describe('findPerformanceRatesByDateAndMeasureForHospital', () => {
    test('should return Performance Rates by Measure and Date for Hospital by Month', async () => {
      // Arrange
      const sisenseMeasSummRepository = factory({
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }).createSisenseMeasureSummaryRepository()
      const entityId = BigInt(38190)
      const medisolvMeasureId = 'F5B00D78-29AC-EA11-A8FE-4CEDFB610C38'
      const timePeriod = TimePeriod.Monthly
      const dateStart = new Date('2024-01-01T00:00:00Z')
      const dateEnd = new Date('2025-04-01T00:00:00Z')

      // Act
      const results =
        await sisenseMeasSummRepository.findPerformanceRatesByDateAndMeasureForHospital(
          entityId,
          medisolvMeasureId,
          timePeriod,
          dateStart,
          dateEnd
        )

      // Assert
      expect(results).not.toBeNull()
      expect(results).toHaveLength(4)

      expect(results[0]?.date.toUTCString()).toEqual(
        'Mon, 01 Jan 2024 00:00:00 GMT'
      )
      expect(results[0]?.performance).toEqual(50)
      expect(results[0]?.numerator).toEqual(1)
      expect(results[0]?.denominator).toEqual(1)

      expect(results[1]?.date.toUTCString()).toEqual(
        'Thu, 01 Feb 2024 00:00:00 GMT'
      )
      expect(results[1]?.performance).toEqual(100)
      expect(results[1]?.numerator).toEqual(18)
      expect(results[1]?.denominator).toEqual(0)

      expect(results[2]?.date.toUTCString()).toEqual(
        'Fri, 01 Mar 2024 00:00:00 GMT'
      )
      expect(results[2]?.performance).toEqual(96.77)
      expect(results[2]?.numerator).toEqual(30)
      expect(results[2]?.denominator).toEqual(1)

      expect(results[3]?.date.toUTCString()).toEqual(
        'Mon, 01 Apr 2024 00:00:00 GMT'
      )
      expect(results[3]?.performance).toEqual(100)
      expect(results[3]?.numerator).toEqual(1)
      expect(results[3]?.denominator).toEqual(0)
    })

    test('should return Performance Rates by Measure and Date for Hospital by Quarter', async () => {
      // Arrange
      const sisenseMeasSummRepository = factory({
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }).createSisenseMeasureSummaryRepository()
      const entityId = BigInt(38190)
      const medisolvMeasureId = 'F5B00D78-29AC-EA11-A8FE-4CEDFB610C38'
      const timePeriod = TimePeriod.Quarterly
      const dateStart = new Date('2024-01-01T00:00:00Z')
      const dateEnd = new Date('2025-04-01T00:00:00Z')

      // Act
      const results =
        await sisenseMeasSummRepository.findPerformanceRatesByDateAndMeasureForHospital(
          entityId,
          medisolvMeasureId,
          timePeriod,
          dateStart,
          dateEnd
        )

      // Assert
      expect(results).not.toBeNull()
      expect(results).toHaveLength(2)

      expect(results[0]?.date.toUTCString()).toEqual(
        'Mon, 01 Jan 2024 00:00:00 GMT'
      )
      expect(results[0]?.performance).toEqual(96.08)
      expect(results[0]?.numerator).toEqual(49)
      expect(results[0]?.denominator).toEqual(2)

      expect(results[1]?.date.toUTCString()).toEqual(
        'Mon, 01 Apr 2024 00:00:00 GMT'
      )
      expect(results[1]?.performance).toEqual(100)
      expect(results[1]?.numerator).toEqual(1)
      expect(results[1]?.denominator).toEqual(0)
    })

    test('should return Performance Rates by Measure and Date for Hospital by Year', async () => {
      // Arrange
      const sisenseMeasSummRepository = factory({
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }).createSisenseMeasureSummaryRepository()
      const entityId = BigInt(38190)
      const medisolvMeasureId = 'F5B00D78-29AC-EA11-A8FE-4CEDFB610C38'
      const timePeriod = TimePeriod.Yearly
      const dateStart = new Date('2024-01-01T00:00:00Z')
      const dateEnd = new Date('2025-04-01T00:00:00Z')

      // Act
      const results =
        await sisenseMeasSummRepository.findPerformanceRatesByDateAndMeasureForHospital(
          entityId,
          medisolvMeasureId,
          timePeriod,
          dateStart,
          dateEnd
        )

      // Assert
      expect(results).not.toBeNull()
      expect(results).toHaveLength(1)

      expect(results[0]?.date.toUTCString()).toEqual(
        'Mon, 01 Jan 2024 00:00:00 GMT'
      )
      expect(results[0]?.performance).toEqual(96.15)
      expect(results[0]?.numerator).toEqual(50)
      expect(results[0]?.denominator).toEqual(2)
    })
  })

  describe('findMeasurePerformanceByOrgCode', () => {
    test('should return Measure Summary data for Monthly interval by organization code', async () => {
      // Arrange
      const sisenseMeasSummRepository = factory({
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }).createSisenseMeasureSummaryRepository()
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01T00:00:00Z')
      const endDate = new Date('2024-02-01T00:00:00Z')
      const measures = [
        'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
      ]
      const sourceContainerIdentifier =
        'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
      const code = 2 // Organization type code

      // Act
      const results =
        await sisenseMeasSummRepository.findMeasurePerformanceByOrgCode(
          intervalType,
          startDate,
          endDate,
          measures,
          sourceContainerIdentifier,
          code
        )

      // Assert
      expect(results).not.toBeNull()
      expect(Array.isArray(results)).toBe(true)

      // If results are returned, verify their structure
      if (results && results.length > 0) {
        const firstResult = results[0]!
        expect(firstResult).toHaveProperty('Period', intervalType)
        expect(firstResult).toHaveProperty('MeasureName', measures[0])
        expect(firstResult).toHaveProperty('Performance')
        expect(firstResult).toHaveProperty('Numerator')
        expect(firstResult).toHaveProperty('PerformanceDenominator')
        expect(firstResult).toHaveProperty('PTile')
        expect(firstResult).toHaveProperty('MeasureIdentifier')
      }
    })

    test('should return Measure Summary data for Quarterly interval by organization code', async () => {
      // Arrange
      const sisenseMeasSummRepository = factory({
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }).createSisenseMeasureSummaryRepository()
      const intervalType: IntervalType = 'Q'
      const startDate = new Date('2024-01-01T00:00:00Z')
      const endDate = new Date('2024-02-01T00:00:00Z')
      const measures = [
        'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
      ]
      const sourceContainerIdentifier =
        'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
      const code = 2 // Organization type code

      // Act
      const results =
        await sisenseMeasSummRepository.findMeasurePerformanceByOrgCode(
          intervalType,
          startDate,
          endDate,
          measures,
          sourceContainerIdentifier,
          code
        )

      // Assert
      expect(results).not.toBeNull()
      expect(Array.isArray(results)).toBe(true)

      // If results are returned, verify their structure
      if (results && results.length > 0) {
        const firstResult = results[0]!
        expect(firstResult).toHaveProperty('Period', intervalType)
        expect(firstResult).toHaveProperty('MeasureName', measures[0])
        expect(firstResult).toHaveProperty('Performance')
        expect(firstResult).toHaveProperty('Numerator')
        expect(firstResult).toHaveProperty('PerformanceDenominator')
        expect(firstResult).toHaveProperty('PTile')
        expect(firstResult).toHaveProperty('MeasureIdentifier')
      }
    })

    test('should return Measure Summary data for Yearly interval by organization code', async () => {
      // Arrange
      const sisenseMeasSummRepository = factory({
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }).createSisenseMeasureSummaryRepository()
      const intervalType: IntervalType = 'Y'
      const startDate = new Date('2024-01-01T00:00:00Z')
      const endDate = new Date('2024-02-01T00:00:00Z')
      const measures = [
        'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
      ]
      const sourceContainerIdentifier =
        'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
      const code = 2 // Organization type code

      // Act
      const results =
        await sisenseMeasSummRepository.findMeasurePerformanceByOrgCode(
          intervalType,
          startDate,
          endDate,
          measures,
          sourceContainerIdentifier,
          code
        )

      // Assert
      expect(results).not.toBeNull()
      expect(Array.isArray(results)).toBe(true)

      // If results are returned, verify their structure
      if (results && results.length > 0) {
        const firstResult = results[0]!
        expect(firstResult).toHaveProperty('Period', intervalType)
        expect(firstResult).toHaveProperty('MeasureName', measures[0])
        expect(firstResult).toHaveProperty('Performance')
        expect(firstResult).toHaveProperty('Numerator')
        expect(firstResult).toHaveProperty('PerformanceDenominator')
        expect(firstResult).toHaveProperty('PTile')
        expect(firstResult).toHaveProperty('MeasureIdentifier')
      }
    })

    test('should return Measure Summary data for multiple measures by organization code', async () => {
      // Arrange
      const sisenseMeasSummRepository = factory({
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }).createSisenseMeasureSummaryRepository()
      const intervalType: IntervalType = 'M'
      const startDate = new Date('2024-01-01T00:00:00Z')
      const endDate = new Date('2024-02-01T00:00:00Z')
      const measures = [
        'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
        'Severe Obstetric Complications (Ethnicity - Not Hispanic Or Latino) (E)',
      ]
      const sourceContainerIdentifier =
        'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
      const code = 123 // Organization type code

      // Act
      const results =
        await sisenseMeasSummRepository.findMeasurePerformanceByOrgCode(
          intervalType,
          startDate,
          endDate,
          measures,
          sourceContainerIdentifier,
          code
        )

      // Assert
      expect(results).not.toBeNull()
      expect(Array.isArray(results)).toBe(true)

      // If results are returned, verify their structure
      if (results && results.length > 0) {
        // Verify structure of first result
        const firstResult = results[0]!
        expect(firstResult).toHaveProperty('Period', intervalType)
        expect(firstResult).toHaveProperty('Performance')
        expect(firstResult).toHaveProperty('Numerator')
        expect(firstResult).toHaveProperty('PerformanceDenominator')
        expect(firstResult).toHaveProperty('PTile')
        expect(firstResult).toHaveProperty('MeasureIdentifier')
      }
    })
  })
})
