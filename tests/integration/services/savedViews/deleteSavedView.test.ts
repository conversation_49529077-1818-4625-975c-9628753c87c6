import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { deleteSavedView } from '@/services/savedViews/deleteSavedView'
import type { SavedView } from '@/types/savedView'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('deleteSavedView', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = `test-user-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should delete an existing saved view', async () => {
    // Arrange
    const viewId = `view-${dayjs.utc().valueOf()}`
    const testView: SavedView = {
      partitionKey: testUserId,
      rowKey: viewId,
      userId: testUserId,
      viewName: 'Test View',
      viewMetadata: '{}',
      isDefault: false,
      isShared: false,
    }
    await tableStorageWrapper.insertEntity(testView)

    // Verify view was created
    let views = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(views.length).toBe(1)

    // Act
    await deleteSavedView(tableStorageWrapper, testUserId, viewId)

    // Assert
    views = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(views.length).toBe(0)
  })

  test('should only delete specified view when multiple views exist', async () => {
    // Arrange
    const viewId1 = `view1-${dayjs.utc().valueOf()}`
    const viewId2 = `view2-${dayjs.utc().valueOf()}`

    const testView1: SavedView = {
      partitionKey: testUserId,
      rowKey: viewId1,
      userId: testUserId,
      viewName: 'Test View 1',
      viewMetadata: '{}',
      isDefault: false,
      isShared: false,
    }

    const testView2: SavedView = {
      partitionKey: testUserId,
      rowKey: viewId2,
      userId: testUserId,
      viewName: 'Test View 2',
      viewMetadata: '{}',
      isDefault: false,
      isShared: false,
    }

    await tableStorageWrapper.insertEntity(testView1)
    await tableStorageWrapper.insertEntity(testView2)

    // Verify views were created
    let views = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(views.length).toBe(2)

    // Act
    await deleteSavedView(tableStorageWrapper, testUserId, viewId1)

    // Assert
    views = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(views.length).toBe(1)
    expect(views[0]!.rowKey).toBe(viewId2)
  })

  test('should delete view for specific user only', async () => {
    // Arrange
    const anotherUserId = `another-user-${dayjs.utc().valueOf()}`
    const viewId = `view-${dayjs.utc().valueOf()}`

    const testView1: SavedView = {
      partitionKey: testUserId,
      rowKey: viewId,
      userId: testUserId,
      viewName: 'Test View 1',
      viewMetadata: '{}',
      isDefault: false,
      isShared: false,
    }

    const testView2: SavedView = {
      partitionKey: anotherUserId,
      rowKey: viewId,
      userId: anotherUserId,
      viewName: 'Test View 2',
      viewMetadata: '{}',
      isDefault: false,
      isShared: false,
    }

    await tableStorageWrapper.insertEntity(testView1)
    await tableStorageWrapper.insertEntity(testView2)

    // Act
    await deleteSavedView(tableStorageWrapper, testUserId, viewId)

    // Assert
    const user1Views = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(user1Views.length).toBe(0)

    const user2Views = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${anotherUserId}'`
    )
    expect(user2Views.length).toBe(1)
  })
})
