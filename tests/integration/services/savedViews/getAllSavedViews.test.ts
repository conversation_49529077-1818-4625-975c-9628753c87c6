import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { SavedView } from '@/types/savedView'
import { SelectionType } from '@/enums/selectionType'
import { MigrationConfig } from '@/types/migrationConfig'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'

dayjs.extend(utc)

let tableStorageWrapper: AzureTableStorageWrapper
const testUserId = 'test-user-id'
const testOrgId = 'test-org-id'

beforeEach(async () => {
  tableStorageWrapper = new AzureTableStorageWrapper(
    `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
  )
  await tableStorageWrapper.createTable()
})

afterEach(async () => {
  await tableStorageWrapper.deleteTable()
})

describe('getAllSavedViews', () => {
  test('should return empty array when no views exist', async () => {
    const { getAllSavedViews } = await import(
      '@/services/savedViews/getAllSavedViews'
    )
    const results = await getAllSavedViews(tableStorageWrapper, testUserId, testOrgId)
    expect(results).toEqual([])
  })

  describe('with migrationConfig', () => {
    test('should migrate and return saved views when migrationConfig is provided', async () => {
      // Mock SQL results
      const sqlViews = [
        {
          Id: 1,
          UserId: testUserId,
          organizationId: testOrgId,
          ViewName: 'SQL View 1',
          ViewMetadata: JSON.stringify({ filters: ['filter1'] }),
          IsDefault: true,
          IsShared: false,
        },
        {
          Id: 2,
          UserId: testUserId,
          organizationId: testOrgId,
          ViewName: 'SQL View 2',
          ViewMetadata: JSON.stringify({ filters: ['filter2'] }),
          IsDefault: false,
          IsShared: true,
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            savedViews: {
              findMany: jest.fn().mockResolvedValue(sqlViews as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      const { getAllSavedViews } = await import(
        '@/services/savedViews/getAllSavedViews'
      )

      // Act
      const results = await getAllSavedViews(
        tableStorageWrapper,
        testUserId,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(2)
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            partitionKey: testUserId,
            rowKey: '1',
            viewName: 'SQL View 1',
            viewMetadata: JSON.stringify({ filters: ['filter1'] }),
            isDefault: true,
            isShared: false,
          }),
          expect.objectContaining({
            partitionKey: testUserId,
            rowKey: '2',
            viewName: 'SQL View 2',
            viewMetadata: JSON.stringify({ filters: ['filter2'] }),
            isDefault: false,
            isShared: true,
          }),
        ])
      )

      // Verify views were migrated to table storage
      const storedViews = await tableStorageWrapper.queryEntities<SavedView>(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedViews).toHaveLength(2)
    })

    test('should handle empty SQL results during migration', async () => {
      // Mock empty SQL results
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            savedViews: {
              findMany: jest.fn().mockResolvedValue([] as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      const { getAllSavedViews } = await import(
        '@/services/savedViews/getAllSavedViews'
      )

      // Act
      const results = await getAllSavedViews(
        tableStorageWrapper,
        testUserId,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should handle missing prisma client during migration', async () => {
      // Clear mock prisma client
      globalThis.prismaClients = {}

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      const { getAllSavedViews } = await import(
        '@/services/savedViews/getAllSavedViews'
      )

      // Act
      const results = await getAllSavedViews(
        tableStorageWrapper,
        testUserId,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should migrate when migrationConfig exists and no views exist in table storage', async () => {
      // Mock SQL results
      const sqlViews = [
        {
          Id: 1,
          UserId: testUserId,
          ViewName: 'SQL View 1',
          ViewMetadata: JSON.stringify({ filters: ['filter1'] }),
          IsDefault: true,
          IsShared: false,
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            savedViews: {
              findMany: jest.fn().mockResolvedValue(sqlViews as never),
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Verify no views exist initially
      const initialViews = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(initialViews).toHaveLength(0)

      const { getAllSavedViews } = await import(
        '@/services/savedViews/getAllSavedViews'
      )

      // Act
      const results = await getAllSavedViews(
        tableStorageWrapper,
        testUserId,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: '1',
          viewName: 'SQL View 1',
          viewMetadata: JSON.stringify({ filters: ['filter1'] }),
          isDefault: true,
          isShared: false,
        })
      )

      // Verify views were migrated to table storage
      const storedViews = await tableStorageWrapper.queryEntities<SavedView>(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedViews).toHaveLength(1)
    })

    test('should not migrate when migrationConfig exists but views already exist in table storage', async () => {
      // First create some existing views
      const existingView = {
        partitionKey: testUserId,
        organizationId: testOrgId,
        rowKey: 'existing-1',
        viewName: 'Existing View',
        viewMetadata: JSON.stringify({ filters: ['existing-filter'] }),
        isDefault: true,
        isShared: false,
      }
      await tableStorageWrapper.insertEntity(existingView)

      // Mock SQL results that shouldn't be migrated
      const sqlViews = [
        {
          Id: 'sql-1',
          UserId: testUserId,

          ViewName: 'SQL View',
          ViewMetadata: JSON.stringify({ filters: ['sql-filter'] }),
          IsDefault: true,
          IsShared: false,
        },
      ]

      // Mock prisma client
      const mockFindMany = jest.fn().mockResolvedValue(sqlViews as never)
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            savedViews: {
              findMany: mockFindMany,
            },
          },
        },
      } as any

      const migrationConfig: MigrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      const { getAllSavedViews } = await import(
        '@/services/savedViews/getAllSavedViews'
      )

      // Act
      const results = await getAllSavedViews(
        tableStorageWrapper,
        testUserId,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: 'existing-1',
          viewName: 'Existing View',
        })
      )

      // Verify no additional views were migrated
      const storedViews = await tableStorageWrapper.queryEntities<SavedView>(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedViews).toHaveLength(1)
      expect(storedViews[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: 'existing-1',
          viewName: 'Existing View',
        })
      )
    })
  })
})
