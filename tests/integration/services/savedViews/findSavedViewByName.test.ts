import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { findSavedViewByName } from '@/services/savedViews/findSavedViewByName'
import type { SavedView } from '@/types/savedView'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('findSavedViewByName', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = `test-user-${dayjs.utc().valueOf()}`
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should find view by name', async () => {
    // Arrange
    const viewName = 'Test View'
    const testView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName,
      viewMetadata: JSON.stringify({ key: 'value' }),
      isDefault: false,
      isShared: false,
    }
    await tableStorageWrapper.insertEntity(testView)

    // Act
    const results = await findSavedViewByName(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      viewName
    )

    // Assert
    expect(results.length).toBe(1)
    expect(results[0]).toMatchObject({
      partitionKey: testUserId,
      userId: testUserId,
      organizationId: testOrgId,
      viewName,
      viewMetadata: testView.viewMetadata,
      isDefault: false,
      isShared: false,
    })
  })

  test('should return empty array when view name does not exist', async () => {
    // Act
    const results = await findSavedViewByName(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      'Non-existent View'
    )

    // Assert
    expect(results).toEqual([])
  })

  test('should find view when multiple views exist', async () => {
    // Arrange
    const targetViewName = 'Target View'
    const views: SavedView[] = [
      {
        partitionKey: testUserId,
        rowKey: `view-1-${dayjs.utc().valueOf()}`,
        userId: testUserId,
        organizationId: testOrgId,

        viewName: 'First View',
        viewMetadata: JSON.stringify({ key: 'value1' }),
        isDefault: false,
        isShared: false,
      },
      {
        partitionKey: testUserId,
        rowKey: `view-2-${dayjs.utc().valueOf()}`,
        userId: testUserId,
        organizationId: testOrgId,
        viewName: targetViewName,
        viewMetadata: JSON.stringify({ key: 'value2' }),
        isDefault: true,
        isShared: false,
      },
      {
        partitionKey: testUserId,
        rowKey: `view-3-${dayjs.utc().valueOf()}`,
        userId: testUserId,
        organizationId: testOrgId,

        viewName: 'Third View',
        viewMetadata: JSON.stringify({ key: 'value3' }),
        isDefault: false,
        isShared: true,
      },
    ]

    for (const view of views) {
      await tableStorageWrapper.insertEntity(view)
    }

    // Act
    const results = await findSavedViewByName(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      targetViewName
    )

    // Assert
    expect(results.length).toBe(1)
    expect(results[0]).toMatchObject({
      partitionKey: testUserId,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: targetViewName,
      viewMetadata: views[1]!.viewMetadata,
      isDefault: true,
      isShared: false,
    })
  })

  test('should not find views from different users', async () => {
    // Arrange
    const viewName = 'Test View'
    const otherUserId = `other-user-${dayjs.utc().valueOf()}`

    const testViews: SavedView[] = [
      {
        partitionKey: testUserId,
        rowKey: `view-1-${dayjs.utc().valueOf()}`,
        userId: testUserId,

        organizationId: testOrgId,
        viewName,
        viewMetadata: JSON.stringify({ key: 'value1' }),
        isDefault: false,
        isShared: false,
      },
      {
        partitionKey: otherUserId,
        rowKey: `view-2-${dayjs.utc().valueOf()}`,
        userId: otherUserId,
        organizationId: testOrgId,
        viewName,
        viewMetadata: JSON.stringify({ key: 'value2' }),
        isDefault: false,
        isShared: false,
      },
    ]

    for (const view of testViews) {
      await tableStorageWrapper.insertEntity(view)
    }

    // Act
    const results = await findSavedViewByName(
      tableStorageWrapper,
      testUserId,
      testOrgId,

      viewName
    )

    // Assert
    expect(results.length).toBe(1)
    expect(results[0]!.userId).toBe(testUserId)
    expect(results[0]!.organizationId).toBe(testOrgId)
    expect(results[0]!.viewMetadata).toBe(testViews[0]!.viewMetadata)
  })
})
