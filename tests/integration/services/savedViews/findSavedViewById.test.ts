import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { findSavedViewById } from '@/services/savedViews/findSavedViewById'
import type { SavedView } from '@/types/savedView'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('findSavedViewById', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = `test-user-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return undefined when view does not exist', async () => {
    const result = await findSavedViewById(
      tableStorageWrapper,
      testUserId,
      'non-existent-view'
    )
    expect(result).toBeUndefined()
  })

  test('should return saved view when it exists', async () => {
    // Arrange
    const testView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      viewName: 'Test View',
      viewMetadata: JSON.stringify({ key: 'value' }),
      isDefault: false,
      isShared: false,
    }
    await tableStorageWrapper.upsertEntity(testView)

    // Act
    const result = await findSavedViewById(
      tableStorageWrapper,
      testUserId,
      testView.rowKey
    )

    // Assert
    expect(result).toBeDefined()
    expect(result).toMatchObject(testView)
  })

  test('should not return view for different user', async () => {
    // Arrange
    const testView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      viewName: 'Test View',
      viewMetadata: JSON.stringify({ key: 'value' }),
      isDefault: false,
      isShared: false,
    }
    await tableStorageWrapper.upsertEntity(testView)

    // Act
    const result = await findSavedViewById(
      tableStorageWrapper,
      'different-user',
      testView.rowKey
    )
    expect(result).toBeUndefined()
  })

  test('should handle view with minimal required fields', async () => {
    // Arrange
    const minimalView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      viewName: 'Minimal View',
      viewMetadata: '{}',
      isDefault: false,
      isShared: false,
    }
    await tableStorageWrapper.upsertEntity(minimalView)

    // Act
    const result = await findSavedViewById(
      tableStorageWrapper,
      testUserId,
      minimalView.rowKey
    )

    // Assert
    expect(result).toBeDefined()
    expect(result).toMatchObject(minimalView)
  })
})
