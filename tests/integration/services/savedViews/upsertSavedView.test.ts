import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { SavedView } from '@/types/savedView'
import { upsertSavedView } from '@/services/savedViews/upsertSavedView'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { ChunkMetadata } from '@/types/filterQuery'

dayjs.extend(utc)

let tableStorageWrapper: AzureTableStorageWrapper
const testUserId = 'test-user-id'
const testOrgId = 'test-org-id'
const SECONDS = 1000

describe('upsertSavedView', () => {
  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert new view when it does not exist', async () => {
    // Arrange
    const testView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'Test View',
      viewMetadata: JSON.stringify({ filters: ['filter1'] }),
      isDefault: true,
      isShared: false,
    }

    // Act
    const result = await upsertSavedView(tableStorageWrapper, testView)

    // Assert
    expect(result).toBeDefined()
    expect(result.partitionKey).toBe(testView.partitionKey)
    expect(result.rowKey).toBe(testView.rowKey)
    expect(result.viewName).toBe(testView.viewName)
    expect(result.viewMetadata).toBe(testView.viewMetadata)
    expect(result.isDefault).toBe(testView.isDefault)
    expect(result.isShared).toBe(testView.isShared)

    // Verify in storage
    const savedViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(savedViews.length).toBe(1)
    expect(savedViews[0]).toMatchObject(testView)
  })

  test('should update existing view', async () => {
    // Arrange
    const viewId = `view-${dayjs.utc().valueOf()}`
    const originalView: SavedView = {
      partitionKey: testUserId,
      rowKey: viewId,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'Original View',
      viewMetadata: JSON.stringify({ filters: ['filter1'] }),
      isDefault: true,
      isShared: false,
    }

    // Insert original view
    await upsertSavedView(tableStorageWrapper, originalView)

    // Create updated view
    const updatedView: SavedView = {
      ...originalView,
      viewName: 'Updated View',
      viewMetadata: JSON.stringify({ filters: ['filter2'] }),
      isDefault: false,
      isShared: true,
    }

    // Act
    const result = await upsertSavedView(tableStorageWrapper, updatedView)

    // Assert
    expect(result).toBeDefined()
    expect(result.partitionKey).toBe(updatedView.partitionKey)
    expect(result.rowKey).toBe(updatedView.rowKey)
    expect(result.viewName).toBe(updatedView.viewName)
    expect(result.viewMetadata).toBe(updatedView.viewMetadata)
    expect(result.isDefault).toBe(updatedView.isDefault)
    expect(result.isShared).toBe(updatedView.isShared)

    // Verify in storage
    const savedViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(savedViews.length).toBe(1)
    expect(savedViews[0]).toMatchObject(updatedView)
  })

  test('should handle view with minimal required fields', async () => {
    // Arrange
    const minimalView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'Minimal View',
      viewMetadata: '{}',
      isDefault: false,
      isShared: false,
    }

    // Act
    const result = await upsertSavedView(tableStorageWrapper, minimalView)

    // Assert
    expect(result).toBeDefined()
    expect(result).toMatchObject(minimalView)

    // Verify in storage
    const savedViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(savedViews.length).toBe(1)
    expect(savedViews[0]).toMatchObject(minimalView)
  })

  test('should handle multiple views for same user', async () => {
    // Arrange
    const view1: SavedView = {
      partitionKey: testUserId,
      rowKey: `view1-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'View 1',
      viewMetadata: JSON.stringify({ filters: ['filter1'] }),
      isDefault: true,
      isShared: false,
    }

    const view2: SavedView = {
      partitionKey: testUserId,
      rowKey: `view2-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'View 2',
      viewMetadata: JSON.stringify({ filters: ['filter2'] }),
      isDefault: false,
      isShared: true,
    }

    // Act
    await upsertSavedView(tableStorageWrapper, view1)
    await upsertSavedView(tableStorageWrapper, view2)

    // Assert
    const savedViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(savedViews.length).toBe(2)
    expect(savedViews).toEqual(
      expect.arrayContaining([
        expect.objectContaining(view1),
        expect.objectContaining(view2),
      ])
    )
  })

  describe('chunking', () => {
    test('should handle large view metadata by creating chunks', async () => {
      // Arrange
      const largeMetadata = {
        filters: Array.from({ length: 1000 }, (_, i) => ({
          id: `filter-${i}`,
          value: `value-${i}`.repeat(100), // Make each value large
        })),
      }

      const viewId = `view-${dayjs.utc().valueOf()}`
      const largeView: SavedView = {
        partitionKey: testUserId,
        rowKey: viewId,
        userId: testUserId,
        organizationId: testOrgId,
        viewName: 'Large View',
        viewMetadata: JSON.stringify(largeMetadata),
        isDefault: false,
        isShared: false,
      }

      // Act
      const result = await upsertSavedView(tableStorageWrapper, largeView)

      // Assert
      expect(result).toBeDefined()
      expect(result.partitionKey).toBe(largeView.partitionKey)
      expect(result.rowKey).toBe(largeView.rowKey)
      expect(result.viewName).toBe(largeView.viewName)

      // Verify chunks were created
      const chunks = await tableStorageWrapper.queryEntities<ChunkMetadata>(
        `PartitionKey eq '${testUserId}' and isChunk eq true and mainEntityId eq '${viewId}'`
      )
      expect(chunks.length).toBeGreaterThan(0)

      // Reconstruct full metadata from chunks
      const sortedChunks = chunks.sort((a, b) => a.chunkIndex - b.chunkIndex)
      const reconstructedMetadata = sortedChunks
        .map((c) => c.chunkData)
        .join('')
      expect(JSON.parse(reconstructedMetadata)).toEqual(largeMetadata)
    }, 70 * SECONDS)

    test('should update existing view with chunks', async () => {
      // Arrange
      const viewId = `view-${dayjs.utc().valueOf()}`
      const originalMetadata = {
        filters: Array.from({ length: 500 }, (_, i) => ({
          id: `filter-${i}`,
          value: `original-${i}`.repeat(100),
        })),
      }

      const originalView: SavedView = {
        partitionKey: testUserId,
        rowKey: viewId,
        userId: testUserId,
        organizationId: testOrgId,
        viewName: 'Original Large View',
        viewMetadata: JSON.stringify(originalMetadata),
        isDefault: false,
        isShared: false,
      }

      // Insert original view
      await upsertSavedView(tableStorageWrapper, originalView)

      // Create updated view with different large metadata
      const updatedMetadata = {
        filters: Array.from({ length: 500 }, (_, i) => ({
          id: `filter-${i}`,
          value: `updated-${i}`.repeat(100),
        })),
      }

      const updatedView: SavedView = {
        ...originalView,
        viewName: 'Updated Large View',
        viewMetadata: JSON.stringify(updatedMetadata),
      }

      // Act
      const result = await upsertSavedView(tableStorageWrapper, updatedView)

      // Assert
      expect(result).toBeDefined()
      expect(result.viewName).toBe(updatedView.viewName)

      // Verify chunks were updated
      const chunks = await tableStorageWrapper.queryEntities<ChunkMetadata>(
        `PartitionKey eq '${testUserId}' and isChunk eq true and mainEntityId eq '${viewId}'`
      )
      expect(chunks.length).toBeGreaterThan(0)

      // Reconstruct full metadata from chunks
      const sortedChunks = chunks.sort((a, b) => a.chunkIndex - b.chunkIndex)
      const reconstructedMetadata = sortedChunks
        .map((c) => c.chunkData)
        .join('')
      expect(JSON.parse(reconstructedMetadata)).toEqual(updatedMetadata)
    })

    test('should delete old chunks when updating view', async () => {
      // Arrange
      const viewId = `view-${dayjs.utc().valueOf()}`
      const originalMetadata = {
        filters: Array.from({ length: 500 }, (_, i) => ({
          id: `filter-${i}`,
          value: `original-${i}`.repeat(100),
        })),
      }

      const originalView: SavedView = {
        partitionKey: testUserId,
        rowKey: viewId,
        userId: testUserId,
        organizationId: testOrgId,
        viewName: 'Original Large View',
        viewMetadata: JSON.stringify(originalMetadata),
        isDefault: false,
        isShared: false,
      }

      // Insert original view
      await upsertSavedView(tableStorageWrapper, originalView)

      // Get original chunk count
      const originalChunks =
        await tableStorageWrapper.queryEntities<ChunkMetadata>(
          `PartitionKey eq '${testUserId}' and isChunk eq true and mainEntityId eq '${viewId}'`
        )
      const originalChunkCount = originalChunks.length

      // Verify chunks were created initially
      expect(originalChunkCount).toBeGreaterThan(0)

      // Create updated view with smaller metadata (no chunking needed)
      const updatedView: SavedView = {
        ...originalView,
        viewName: 'Updated View',
        viewMetadata: JSON.stringify({ filters: ['small-filter'] }),
      }

      // Act
      await upsertSavedView(tableStorageWrapper, updatedView)

      // Assert
      const remainingChunks =
        await tableStorageWrapper.queryEntities<ChunkMetadata>(
          `PartitionKey eq '${testUserId}' and isChunk eq true and mainEntityId eq '${viewId}'`
        )

      // Verify all chunks were deleted
      expect(remainingChunks.length).toBe(0)
    })
  })
})
