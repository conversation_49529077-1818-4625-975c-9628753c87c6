import { factory } from '@/services/snowflake/SnowflakeRepositoryFactory'
import { env } from '@/env'
import { createSnowflakeConfiguration } from '@/services/snowflake/SnowflakeHelper'
import fs from 'fs'
import { fileURLToPath } from 'url'
import path from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const fixtureData = JSON.parse(
  fs.readFileSync(`${__dirname}/expected-submission-groups.json`, 'utf8')
)

const expectedResults = fixtureData.ExpectedResults
const expectedPartnerResults = fixtureData.ExpectedPartnerResults

describe('SnowflakeSubmissionGroupRepository', () => {
  test('should return all SubmissionGroups for an organization', async () => {
    const repo = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createSubmissionGroupRepository()
    if (env.SNOWFLAKE_ACCOUNT) {
      const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
      const isPartner = false
      const results = await repo.findSubmissionGroupsByOrganization(
        organizationId,
        isPartner
      )
      expect(results.length).toBe(expectedResults.length)
      expect(results[0]?.submissionGroupId).toBe(
        expectedResults[0].submissionGroupId
      )
      expect(results[0]?.submissionGroupName).toBe(
        expectedResults[0].submissionGroupName
      )
      expect(results[0]?.processingStartDate).toBe(
        expectedResults[0].processingStartDate
      )
      expect(results[0]?.processingEndDate).toBe(
        expectedResults[0].processingEndDate
      )
    } else {
      console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
    }
  })

  test('should return all eMeasure SubmissionGroups for an organization', async () => {
    const repo = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createSubmissionGroupRepository()
    if (env.SNOWFLAKE_ACCOUNT) {
      const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
      const isPartner = false
      const measureIdentifiers: string[] = fixtureData.EMeasureIdentifiers
      const results =
        await repo.findSubmissionGroupsByOrganizationAndMeasureType(
          organizationId,
          isPartner,
          measureIdentifiers
        )
      expect(results.length).toBe(expectedResults.length)
      expect(results[0]?.submissionGroupId).toBe(
        expectedResults[0].submissionGroupId
      )
      expect(results[0]?.submissionGroupName).toBe(
        expectedResults[0].submissionGroupName
      )
      expect(results[0]?.processingStartDate).toBe(
        expectedResults[0].processingStartDate
      )
      expect(results[0]?.processingEndDate).toBe(
        expectedResults[0].processingEndDate
      )
    } else {
      console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
    }
  })

  test('should return all Registry Measure SubmissionGroups for an organization', async () => {
    const repo = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createSubmissionGroupRepository()
    if (env.SNOWFLAKE_ACCOUNT) {
      const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
      const isPartner = false
      const measureIdentifiers: string[] = fixtureData.QMeasureIdentifiers
      const results =
        await repo.findSubmissionGroupsByOrganizationAndMeasureType(
          organizationId,
          isPartner,
          measureIdentifiers
        )
      expect(results.length).toBe(0)
    }
  })

  test('should return all the SubmissionGroups for an organization and given providers', async () => {
    const repo = factory({
      ...createSnowflakeConfiguration(),
      role: 'PLATFORM_DEV_ROLE',
    }).createSubmissionGroupRepository()
    if (env.SNOWFLAKE_ACCOUNT) {
      const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
      const isPartner = false
      const NPIs = fixtureData.NPIs
      const results = await repo.findSubmissionGroupsByOrganizationAndProviders(
        organizationId,
        isPartner,
        NPIs
      )
      expect(results.length).toBe(expectedPartnerResults.length)
      expect(results[0]?.submissionGroupId).toBe(
          expectedPartnerResults[0].submissionGroupId
      )
      expect(results[0]?.submissionGroupName).toBe(
          expectedPartnerResults[0].submissionGroupName
      )
    }
  })
})
