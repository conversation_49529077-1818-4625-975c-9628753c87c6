import { jest } from '@jest/globals'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { getLoadStatus } from '@/services/loadStatus/getLoadStatus'
import { StorageTables } from '@/enums/storageTables'
import { LoadStatus } from '@/types/loadStatus'

dayjs.extend(utc)

describe('getLoadStatus', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  const testDate = '20240331'

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.PlatformJobStatus}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return EC load status records', async () => {
    // Arrange
    const loadStatus: LoadStatus = {
      partitionKey: testOrgId,
      rowKey: `ec${testDate}_001`,
      organizationId: testOrgId,
      startDateTime: dayjs.utc().toISOString(),
      endDateTime: dayjs.utc().toISOString(),
      status: 'Completed',
      categoryAssignmentCount: 0,
      count: 0,
      isEhLoadsDefective: false,
      ehCalculationsCompletionDate: dayjs.utc().toISOString(),
      isEcLoadsDefective: false,
      ecCalculationsCompletionDate: dayjs.utc().toISOString(),
      cdmLastLoadDate: dayjs.utc().toISOString(),
      type: 'EC',
      processingType: 'Standard',
      organizationName: 'Test Org',
      hospitalName: 'Test Hospital',
      mspLastPublishDate: dayjs.utc().toDate(),
      cdmLastLoadDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      mspLastPublishDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      admLoadStartDate: dayjs.utc().format('MM/DD/YYYY'),
      admLoadEndDate: dayjs.utc().format('MM/DD/YYYY'),
      startDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      endDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      duration: '0m',
      ehCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
      ecCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
    }
    await tableStorageWrapper.insertEntity(loadStatus)

    // Act
    const results = await getLoadStatus(
      tableStorageWrapper,
      testOrgId,
      'EC',
      testDate
    )

    // Assert
    expect(results).toBeDefined()
    expect(results.length).toBe(1)
    expect(results[0]!.rowKey).toMatch(/^ec20240331/)
    expect(results[0]!.partitionKey).toBe(testOrgId)
  })

  test('should return EH load status records', async () => {
    // Arrange
    const loadStatus: LoadStatus = {
      partitionKey: testOrgId,
      rowKey: `eh${testDate}_001`,
      organizationId: testOrgId,
      startDateTime: dayjs.utc().toISOString(),
      endDateTime: dayjs.utc().toISOString(),
      status: 'Completed',
      categoryAssignmentCount: 0,
      count: 0,
      isEhLoadsDefective: false,
      ehCalculationsCompletionDate: dayjs.utc().toISOString(),
      isEcLoadsDefective: false,
      ecCalculationsCompletionDate: dayjs.utc().toISOString(),
      cdmLastLoadDate: dayjs.utc().toISOString(),
      type: 'EH',
      processingType: 'Standard',
      organizationName: 'Test Org',
      hospitalName: 'Test Hospital',
      mspLastPublishDate: dayjs.utc().toDate(),
      cdmLastLoadDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      mspLastPublishDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      admLoadStartDate: dayjs.utc().format('MM/DD/YYYY'),
      admLoadEndDate: dayjs.utc().format('MM/DD/YYYY'),
      startDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      endDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      duration: '0m',
      ehCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
      ecCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
    }
    await tableStorageWrapper.insertEntity(loadStatus)

    // Act
    const results = await getLoadStatus(
      tableStorageWrapper,
      testOrgId,
      'EH',
      testDate
    )

    // Assert
    expect(results).toBeDefined()
    expect(results.length).toBe(1)
    expect(results[0]!.rowKey).toMatch(/^eh20240331/)
    expect(results[0]!.partitionKey).toBe(testOrgId)
  })

  test('should return multiple load status records for same date', async () => {
    // Arrange
    const loadStatuses: LoadStatus[] = [
      {
        partitionKey: testOrgId,
        rowKey: `ec${testDate}_001`,
        organizationId: testOrgId,
        startDateTime: dayjs.utc().toISOString(),
        endDateTime: dayjs.utc().toISOString(),
        status: 'Completed',
        categoryAssignmentCount: 0,
        count: 0,
        isEhLoadsDefective: false,
        ehCalculationsCompletionDate: dayjs.utc().toISOString(),
        isEcLoadsDefective: false,
        ecCalculationsCompletionDate: dayjs.utc().toISOString(),
        cdmLastLoadDate: dayjs.utc().toISOString(),
        type: 'EC',
        processingType: 'Standard',
        organizationName: 'Test Org',
        hospitalName: 'Test Hospital',
        mspLastPublishDate: dayjs.utc().toDate(),
        cdmLastLoadDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
        mspLastPublishDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
        admLoadStartDate: dayjs.utc().format('MM/DD/YYYY'),
        admLoadEndDate: dayjs.utc().format('MM/DD/YYYY'),
        startDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
        endDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
        duration: '0m',
        ehCalculationsCompletionDateDisplay: dayjs
          .utc()
          .format('MM/DD/YYYY hh:mm A'),
        ecCalculationsCompletionDateDisplay: dayjs
          .utc()
          .format('MM/DD/YYYY hh:mm A'),
      },
      {
        partitionKey: testOrgId,
        rowKey: `ec${testDate}_002`,
        organizationId: testOrgId,
        startDateTime: dayjs.utc().toISOString(),
        endDateTime: dayjs.utc().toISOString(),
        status: 'Completed',
        categoryAssignmentCount: 0,
        count: 0,
        isEhLoadsDefective: false,
        ehCalculationsCompletionDate: dayjs.utc().toISOString(),
        isEcLoadsDefective: false,
        ecCalculationsCompletionDate: dayjs.utc().toISOString(),
        cdmLastLoadDate: dayjs.utc().toISOString(),
        type: 'EC',
        processingType: 'Standard',
        organizationName: 'Test Org',
        hospitalName: 'Test Hospital',
        mspLastPublishDate: dayjs.utc().toDate(),
        cdmLastLoadDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
        mspLastPublishDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
        admLoadStartDate: dayjs.utc().format('MM/DD/YYYY'),
        admLoadEndDate: dayjs.utc().format('MM/DD/YYYY'),
        startDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
        endDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
        duration: '0m',
        ehCalculationsCompletionDateDisplay: dayjs
          .utc()
          .format('MM/DD/YYYY hh:mm A'),
        ecCalculationsCompletionDateDisplay: dayjs
          .utc()
          .format('MM/DD/YYYY hh:mm A'),
      },
    ]

    for (const status of loadStatuses) {
      await tableStorageWrapper.insertEntity(status)
    }

    // Act
    const results = await getLoadStatus(
      tableStorageWrapper,
      testOrgId,
      'EC',
      testDate
    )

    // Assert
    expect(results).toBeDefined()
    expect(results.length).toBe(2)
    results.forEach((result) => {
      expect(result.rowKey).toMatch(/^ec20240331/)
      expect(result.partitionKey).toBe(testOrgId)
    })
  })

  test('should return empty array when no records match', async () => {
    // Act
    const results = await getLoadStatus(
      tableStorageWrapper,
      testOrgId,
      'EC',
      testDate
    )

    // Assert
    expect(results).toBeDefined()
    expect(results).toEqual([])
  })

  test('should handle case sensitivity in loadType', async () => {
    // Arrange
    const loadStatus: LoadStatus = {
      partitionKey: testOrgId,
      rowKey: `ec${testDate}_001`,
      organizationId: testOrgId,
      startDateTime: dayjs.utc().toISOString(),
      endDateTime: dayjs.utc().toISOString(),
      status: 'Completed',
      categoryAssignmentCount: 0,
      count: 0,
      isEhLoadsDefective: false,
      ehCalculationsCompletionDate: dayjs.utc().toISOString(),
      isEcLoadsDefective: false,
      ecCalculationsCompletionDate: dayjs.utc().toISOString(),
      cdmLastLoadDate: dayjs.utc().toISOString(),
      type: 'EC',
      processingType: 'Standard',
      organizationName: 'Test Org',
      hospitalName: 'Test Hospital',
      mspLastPublishDate: dayjs.utc().toDate(),
      cdmLastLoadDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      mspLastPublishDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      admLoadStartDate: dayjs.utc().format('MM/DD/YYYY'),
      admLoadEndDate: dayjs.utc().format('MM/DD/YYYY'),
      startDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      endDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      duration: '0m',
      ehCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
      ecCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
    }
    await tableStorageWrapper.insertEntity(loadStatus)

    // Act
    const results = await getLoadStatus(
      tableStorageWrapper,
      testOrgId,
      'EC', // Using uppercase
      testDate
    )

    // Assert
    expect(results).toBeDefined()
    expect(results.length).toBe(1)
    expect(results[0]!.rowKey).toMatch(/^ec20240331/)
  })

  test('should handle query errors gracefully', async () => {
    // Arrange
    const mockError = new Error('Query failed')
    jest
      .spyOn(tableStorageWrapper, 'queryEntities')
      .mockRejectedValue(mockError)
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    // Act & Assert
    await expect(
      getLoadStatus(tableStorageWrapper, testOrgId, 'EC', testDate)
    ).rejects.toThrow('Query failed')

    // Cleanup
    consoleSpy.mockRestore()
  })

  test('should filter rows by prefix', async () => {
    // Arrange
    const loadStatus: LoadStatus = {
      partitionKey: testOrgId,
      rowKey: `ec20240101_001`,
      organizationId: testOrgId,
      startDateTime: dayjs.utc().toISOString(),
      endDateTime: dayjs.utc().toISOString(),
      status: 'Completed',
      categoryAssignmentCount: 0,
      count: 0,
      isEhLoadsDefective: false,
      ehCalculationsCompletionDate: dayjs.utc().toISOString(),
      isEcLoadsDefective: false,
      ecCalculationsCompletionDate: dayjs.utc().toISOString(),
      cdmLastLoadDate: dayjs.utc().toISOString(),
      type: 'EC',
      processingType: 'Standard',
      organizationName: 'Test Org',
      hospitalName: 'Test Hospital',
      mspLastPublishDate: dayjs.utc().toDate(),
      cdmLastLoadDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      mspLastPublishDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      admLoadStartDate: dayjs.utc().format('MM/DD/YYYY'),
      admLoadEndDate: dayjs.utc().format('MM/DD/YYYY'),
      startDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      endDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      duration: '0m',
      ehCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
      ecCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
    }
    await tableStorageWrapper.insertEntity(loadStatus)

    // Act
    const result = await getLoadStatus(
      tableStorageWrapper,
      testOrgId,
      'EC',
      '20240101'
    )

    // Assert
    expect(result.some((row) => row.rowKey.startsWith('ec20240101'))).toBe(true)
  })
})
