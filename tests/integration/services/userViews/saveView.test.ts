import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import saveView from '@/services/userViews/saveView'
import dayjs from 'dayjs'
import { randomUUID } from 'crypto'
import { StorageTables } from '@/enums/storageTables'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('saveView', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = `user-${randomUUID()}`
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should create a new view successfully', async () => {
    // Arrange
    const viewName = 'Test View'
    const page = 'measures'
    const periodType = 'monthly'
    const settings = JSON.stringify({ key: 'value' })
    const fromDate = dayjs.utc('2024-01-01')
    const toDate = dayjs.utc('2024-12-31')

    // Act
    const result = await saveView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      viewName,
      page,
      periodType,
      settings,
      false,
      [fromDate, toDate]
    )

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe('View saved successfully')
    expect(result.view).toBeDefined()
    expect(result.view?.viewName).toBe(viewName)
    expect(result.view?.page).toBe(page)
    expect(result.view?.viewType).toBe(periodType)
    expect(result.view?.settings).toEqual({ key: 'value' })
    expect(result.view?.from.isSame(fromDate)).toBe(true)
    expect(result.view?.to.isSame(toDate)).toBe(true)
    expect(result.view?.isDefault).toBe(false)
    expect(result.view?.isShared).toBe(false)
    expect(result.view?.isFavorite).toBe(false)
  })

  test('should fail when creating non-current view with existing name', async () => {
    // Arrange
    const viewName = 'Duplicate View'
    const page = 'measures'
    const periodType = 'monthly'
    const settings = JSON.stringify({ key: 'value' })
    const fromDate = dayjs.utc('2024-01-01')
    const toDate = dayjs.utc('2024-12-31')

    // Create first view
    await saveView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      viewName,
      page,
      periodType,
      settings,
      false,
      [fromDate, toDate]
    )

    // Act - Try to create duplicate
    const result = await saveView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      viewName,
      page,
      periodType,
      settings,
      false,
      [fromDate, toDate]
    )

    // Assert
    expect(result.success).toBe(false)
    expect(result.message).toBe(
      'View name already exists. Please select a different view name.'
    )
    expect(result.view).toBeUndefined()
  })

  test('should update existing view when isCurrentView is true', async () => {
    // Arrange
    const viewName = 'Current View'
    const page = 'measures'
    const periodType = 'monthly'
    const initialSettings = JSON.stringify({ key: 'initial' })
    const updatedSettings = JSON.stringify({ key: 'updated' })
    const fromDate = dayjs.utc('2024-01-01')
    const toDate = dayjs.utc('2024-12-31')

    // Create initial view
    const initialResult = await saveView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      viewName,
      page,
      periodType,
      initialSettings,
      false,
      [fromDate, toDate]
    )

    // Act - Update the view with isCurrentView = true
    const result = await saveView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      viewName,
      page,
      periodType,
      updatedSettings,
      true, // This should trigger an update of existing view
      [fromDate, toDate]
    )

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe('View saved successfully')
    expect(result.view).toBeDefined()
    expect(result.view?.id).toBe(initialResult.view?.id)
  })

  test('should handle null date range values', async () => {
    // Arrange
    const viewName = 'Null Dates View'
    const page = 'measures'
    const periodType = 'monthly'
    const settings = JSON.stringify({ key: 'value' })

    // Act
    const result = await saveView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      viewName,
      page,
      periodType,
      settings,
      false,
      [null, null]
    )

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe('View saved successfully')
    expect(result.view).toBeDefined()
  })
})
