import { jest } from '@jest/globals'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import { updateFavoriteView } from '@/services/userViews/updateFavoriteView'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { DefaultNames } from '@/enums/defaultNames'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { SavedView } from '@/types/savedView'
import { safeParse } from '@/lib/safeParse'
import { ViewMetaData } from '@/types/savedViewModel'

dayjs.extend(utc)

const SECOND = 1000

describe('updateFavoriteView', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = `test-user-${dayjs.utc().valueOf()}`
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  const createTestView = (
    viewId: string,
    viewName: string,
    isFavorite: boolean
  ): SavedView => ({
    partitionKey: testUserId,
    rowKey: viewId,
    userId: testUserId,
    organizationId: testOrgId,
    viewName,
    viewMetadata: JSON.stringify({
      Page: 'measures',
      Settings: '{}',
      From: dayjs.utc('2024-01-01').toDate(),
      To: dayjs.utc('2024-12-31').toDate(),
      IsFavorite: isFavorite,
      ViewType: 'Monthly',
    }),
    isDefault: false,
    isShared: false,
  })

  //Bug: MP- 479this test case has been removed : we need to maintain all favorite status of all view :should update favorite status and remove other favorites
  test('should update favorite status for all views', async () => {
    // Arrange
    const view1Id = `view1-${dayjs.utc().valueOf()}`
    const view2Id = `view2-${dayjs.utc().valueOf()}`
    const view3Id = `view3-${dayjs.utc().valueOf()}`

    await tableStorageWrapper.insertEntity(
      createTestView(view1Id, 'View 1', true)
    )
    await tableStorageWrapper.insertEntity(
      createTestView(view2Id, 'View 2', true)
    )
    await tableStorageWrapper.insertEntity(
      createTestView(view3Id, 'View 3', false)
    )

    // Act
    const result = await updateFavoriteView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      view3Id,
      true
    )

    // Assert
    expect(result.isSuccess).toBe(true)
    expect(result.statusMesasge).toBe('"View 3" was marked as a favorite View')

    // Verify all views
    const allViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )

    const view1Metadata = safeParse<ViewMetaData>(
      allViews.find((v) => v.rowKey === view1Id)?.viewMetadata!
    )
    const view2Metadata = safeParse<ViewMetaData>(
      allViews.find((v) => v.rowKey === view2Id)?.viewMetadata!
    )
    const view3Metadata = safeParse<ViewMetaData>(
      allViews.find((v) => v.rowKey === view3Id)?.viewMetadata!
    )

    expect(view1Metadata?.IsFavorite).toBe(true)
    expect(view2Metadata?.IsFavorite).toBe(true)
    expect(view3Metadata?.IsFavorite).toBe(true)
  }, 70 * SECOND)

  test('should handle default view creation and remove other favorites', async () => {
    // Arrange
    const existingViewId = `view-${dayjs.utc().valueOf()}`
    await tableStorageWrapper.insertEntity(
      createTestView(existingViewId, 'Existing View', true)
    )

    // Act
    const result = await updateFavoriteView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      '0',
      true,
      'measures',
      PrimaryMeasureTypeConstants.HospitalMeasures
    )

    // Assert
    expect(result.isSuccess).toBe(true)

    // Verify default view was created and is favorite
    const views = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )

    const defaultView = views.find((v) => v.rowKey === '0')
    const existingView = views.find((v) => v.rowKey === existingViewId)

    expect(defaultView).toBeDefined()
    expect(defaultView?.viewName).toBe(DefaultNames.SavedView)

    const defaultMetadata = safeParse<ViewMetaData>(defaultView?.viewMetadata!)
    const existingMetadata = safeParse<ViewMetaData>(
      existingView?.viewMetadata!
    )

    expect(defaultMetadata?.IsFavorite).toBe(true)
    expect(existingMetadata?.IsFavorite).toBe(true)
  })

  test('should handle unfavoriting the only favorite view', async () => {
    // Arrange
    const viewId = `view-${dayjs.utc().valueOf()}`
    await tableStorageWrapper.insertEntity(
      createTestView(viewId, 'Single Favorite', true)
    )

    // Act
    const result = await updateFavoriteView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      viewId,
      false
    )

    // Assert
    expect(result.isSuccess).toBe(true)
    expect(result.statusMesasge).toBe(
      '"Single Favorite" was removed from favorite views.'
    )

    const view = await tableStorageWrapper.getEntity<SavedView>(
      testUserId,
      viewId
    )
    const metadata = safeParse<ViewMetaData>(view?.viewMetadata!)
    expect(metadata?.IsFavorite).toBe(false)
  })

  test('should handle marking as favorite when no favorites exist', async () => {
    // Arrange
    const view1Id = `view1-${dayjs.utc().valueOf()}`
    const view2Id = `view2-${dayjs.utc().valueOf()}`

    await tableStorageWrapper.insertEntity(
      createTestView(view1Id, 'View 1', false)
    )
    await tableStorageWrapper.insertEntity(
      createTestView(view2Id, 'View 2', false)
    )

    // Act
    const result = await updateFavoriteView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      view1Id,
      true
    )

    // Assert
    expect(result.isSuccess).toBe(true)

    const views = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )

    const view1Metadata = safeParse<ViewMetaData>(
      views.find((v) => v.rowKey === view1Id)?.viewMetadata!
    )
    const view2Metadata = safeParse<ViewMetaData>(
      views.find((v) => v.rowKey === view2Id)?.viewMetadata!
    )

    expect(view1Metadata?.IsFavorite).toBe(true)
    expect(view2Metadata?.IsFavorite).toBe(false)
  })

  test('should handle non-existent view id', async () => {
    // Act
    const result = await updateFavoriteView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      'non-existent-id',
      true
    )

    // Assert
    expect(result.isSuccess).toBe(false)
    expect(result.statusMesasge).toBe('An unknown view id was passed')
  })

  test('should maintain other metadata fields when updating favorite status', async () => {
    // Arrange
    const viewId = `view-${dayjs.utc().valueOf()}`
    const originalMetadata = {
      Page: 'measures',
      Settings: JSON.stringify({ customSetting: 'value' }),
      From: dayjs.utc('2024-01-01').toDate(),
      To: dayjs.utc('2024-12-31').toDate(),
      IsFavorite: false,
      ViewType: 'Monthly',
      CustomField: 'should-remain',
    }

    const testView: SavedView = {
      partitionKey: testUserId,
      rowKey: viewId,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'Test View',
      viewMetadata: JSON.stringify(originalMetadata),
      isDefault: false,
      isShared: false,
    }
    await tableStorageWrapper.insertEntity(testView)

    // Act
    await updateFavoriteView(tableStorageWrapper, testUserId, testOrgId, viewId, true)

    // Assert
    const updatedView = await tableStorageWrapper.getEntity<SavedView>(
      testUserId,
      viewId
    )
    const metadata = safeParse<ViewMetaData>(updatedView?.viewMetadata!)

    expect(metadata?.IsFavorite).toBe(true)
    expect(metadata?.Settings).toBe(originalMetadata.Settings)
    expect(metadata?.ViewType).toBe(originalMetadata.ViewType)
  })
})
