import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import type { SavedView } from '@/types/savedView'
import deleteView from '@/services/userViews/deleteView'
import { DefaultNames } from '@/enums/defaultNames'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('deleteView', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = `test-user-${dayjs.utc().valueOf()}`
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should delete a non-default view', async () => {
    // Arrange
    const page = 'testPage'
    const viewMetadata = JSON.stringify({
      version: 'nextGen',
      Page: page,
      filters: [],
    })

    const testViewId = `view-${dayjs.utc().valueOf()}`

    const viewToDelete: SavedView = {
      partitionKey: testUserId,
      rowKey: testViewId,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'Test View',
      viewMetadata,
      isDefault: false,
      isShared: false,
    }

    await tableStorageWrapper.insertEntity(viewToDelete)

    // Act
    const result = await deleteView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      testViewId,
      page
    )

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe('View deleted successfully')

    const remainingViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(remainingViews.length).toBe(0)
  })

  test('should delete view', async () => {
    // Arrange
    const page = 'testPage'
    const timestamp1 = dayjs.utc().valueOf()
    const timestamp2 = timestamp1 + 1 // Ensure different timestamps

    const viewMetadata = JSON.stringify({
      version: '1.0',
      Page: page,
      filters: [],
    })

    const defaultView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${timestamp1}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'Default View',
      viewMetadata,
      isDefault: true,
      isShared: false,
    }

    const corporateView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${timestamp2}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: DefaultNames.SavedView,
      viewMetadata,
      isDefault: false,
      isShared: false,
    }

    await tableStorageWrapper.insertEntity(defaultView)
    await tableStorageWrapper.insertEntity(corporateView)

    // Act
    const result = await deleteView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      `view-${timestamp1}`,
      page
    )

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe('View deleted successfully')

    const remainingViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(remainingViews.length).toBe(1)
    expect(remainingViews[0]?.viewName).toBe(DefaultNames.SavedView)
  })

  test('should return error when view does not exist', async () => {
    // Act
    const result = await deleteView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      '999',
      'testPage'
    )

    // Assert
    expect(result.success).toBe(false)
    expect(result.message).toBe('View does not exist')
  })

  test('should handle deletion of default view without corporate report', async () => {
    // Arrange
    const page = 'testPage'
    const viewMetadata = JSON.stringify({
      version: '1.0',
      Page: page,
      filters: [],
    })

    const testViewId = `view-${dayjs.utc().valueOf()}`

    const defaultView: SavedView = {
      partitionKey: testUserId,
      rowKey: testViewId,
      organizationId: testOrgId,
      userId: testUserId,
      viewName: 'Default View',
      viewMetadata,
      isDefault: true,
      isShared: false,
    }

    await tableStorageWrapper.insertEntity(defaultView)

    // Act
    const result = await deleteView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      testViewId,
      page
    )

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe('View deleted successfully')

    const remainingViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(remainingViews.length).toBe(0)
  })
})
