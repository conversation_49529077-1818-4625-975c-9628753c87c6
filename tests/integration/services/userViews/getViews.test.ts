import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import type { SavedView } from '@/types/savedView'
import getViews from '@/services/userViews/getViews'
import { DefaultNames } from '@/enums/defaultNames'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { SelectionType } from '@/enums/selectionType'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { ViewMetaData } from '@/types/savedViewModel'

dayjs.extend(utc)

describe('getViews', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = `test-user-${dayjs.utc().valueOf()}`
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  const currentPage = 'testPage'
  const measureTypes = [
    PrimaryMeasureTypeConstants.HospitalMeasures,
    PrimaryMeasureTypeConstants.AbstractedMeasures,
  ]

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return existing views with correct sorting (favorites first)', async () => {
    // Arrange
    const timestamp1 = dayjs.utc().valueOf()
    const timestamp2 = timestamp1 + 1 // Ensure different timestamps

    const viewMetadata = JSON.stringify({
      Page: currentPage,
      Settings: JSON.stringify({
        measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
        version: 'nextGen',
      }),
      ViewType: 'Custom',
      From: dayjs.utc().subtract(1, 'month').toISOString(),
      To: dayjs.utc().toISOString(),
      IsFavorite: true,
    })

    const favoriteView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${timestamp1}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'Favorite View',
      viewMetadata,
      isDefault: false,
      isShared: false,
    }

    const nonFavoriteView: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${timestamp2}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'Regular View',
      viewMetadata: JSON.stringify({
        Page: currentPage,
        Settings: JSON.stringify({
          measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
          version: 'nextGen',
        }),
        ViewType: 'Custom',
        From: dayjs.utc().subtract(1, 'month').toISOString(),
        To: dayjs.utc().toISOString(),
        IsFavorite: false,
      }),
      isDefault: false,
      isShared: false,
    }

    await tableStorageWrapper.insertEntity(favoriteView)
    await tableStorageWrapper.insertEntity(nonFavoriteView)

    // Act
    const views = await getViews(
      tableStorageWrapper,
      testOrgId,
      testUserId,
      currentPage,
      SelectionType.Organization,
      measureTypes
    )

    // Assert
    expect(views.length).toBeGreaterThan(3) // Including default views
    expect(views[0]!.viewName).toBe('Favorite View')
    expect(views[0]!.isFavorite).toBe(true)
  })

  test('should handle views without version in settings', async () => {
    // Arrange
    const viewWithoutVersion: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      organizationId: testOrgId,
      viewName: 'No Version View',
      viewMetadata: JSON.stringify({
        Page: currentPage,
        Settings: JSON.stringify({
          measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
        }),
        ViewType: 'Custom',
        From: dayjs.utc().subtract(1, 'month').toISOString(),
        To: dayjs.utc().toISOString(),
        IsFavorite: false,
      }),
      isDefault: false,
      isShared: false,
    }

    await tableStorageWrapper.insertEntity(viewWithoutVersion)

    // Act
    const views = await getViews(
      tableStorageWrapper,
      testOrgId,
      testUserId,
      currentPage,
      SelectionType.Organization,
      measureTypes
    )

    // Assert
    expect(views.length).toBe(measureTypes.length) // Only default views should be present
    views.forEach((view) => {
      expect(view.viewName).toBe(DefaultNames.SavedView)
    })
  })
})
