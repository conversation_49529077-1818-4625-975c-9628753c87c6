import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import shareView from '@/services/userViews/shareView'
import type { SavedView } from '@/types/savedView'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'

dayjs.extend(utc)

describe('shareView', () => {
  let tableStorageWrapper: AzureTableStorageWrapper

  const testUserId = 'test-user-123'
  const testViewId = `view-${dayjs.utc().valueOf()}`
  const testOwnerName = 'John <PERSON>e'
  const testReceiverIds = ['user-1', 'user-2']

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return error when view does not exist', async () => {
    // Act
    const result = await shareView(
      tableStorageWrapper,
      testUserId,
      'non-existent-view',
      testReceiverIds,
      testOwnerName
    )

    // Assert
    expect(result.success).toBe(false)
    expect(result.message).toBe('View not found')
  })

  test('should share view with multiple users', async () => {
    // Arrange
    const originalView: SavedView = {
      partitionKey: testUserId,
      rowKey: testViewId,
      userId: testUserId,
      viewName: 'Test View',
      viewMetadata: '{"key": "value"}',
      isDefault: false,
      isShared: false,
    }
    await tableStorageWrapper.insertEntity(originalView)

    // Act
    const result = await shareView(
      tableStorageWrapper,
      testUserId,
      testViewId,
      testReceiverIds,
      testOwnerName
    )

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe('View shared successfully!')

    // Verify shared views were created
    for (const receiverId of testReceiverIds) {
      const sharedViews = await tableStorageWrapper.queryEntities<SavedView>(
        `PartitionKey eq '${receiverId}'`
      )
      expect(sharedViews.length).toBe(1)
      const sharedView = sharedViews[0]
      expect(sharedView).toBeTruthy()
      if (sharedView) {
        expect(sharedView.partitionKey).toBe(receiverId)
        expect(sharedView.userId).toBe(receiverId)
        expect(sharedView.viewName).toBe(`${testOwnerName}'s Test View`)
        expect(sharedView.viewMetadata).toBe(originalView.viewMetadata)
        expect(sharedView.isShared).toBe(true)
        expect(sharedView.isDefault).toBe(false)
      }
    }
  })

  test('should handle sharing corporate view', async () => {
    // Arrange
    const corporateView: SavedView = {
      partitionKey: testUserId,
      rowKey: testViewId,
      userId: testUserId,
      viewName: 'My Corporate View',
      viewMetadata: '{"key": "value"}',
      isDefault: false,
      isShared: false,
    }
    await tableStorageWrapper.insertEntity(corporateView)

    // Act
    const result = await shareView(
      tableStorageWrapper,
      testUserId,
      testViewId,
      testReceiverIds,
      testOwnerName
    )

    // Assert
    expect(result.success).toBe(true)

    // Verify corporate view naming
    const sharedViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testReceiverIds[0]}'`
    )
    expect(sharedViews[0]?.viewName).toBe(`${testOwnerName}'s Corporate View`)
  })

  test('should update existing shared view', async () => {
    // Arrange
    const originalView: SavedView = {
      partitionKey: testUserId,
      rowKey: testViewId,
      userId: testUserId,
      viewName: 'Test View',
      viewMetadata: '{"key": "value"}',
      isDefault: false,
      isShared: false,
    }
    await tableStorageWrapper.insertEntity(originalView)

    // Create existing shared view
    const existingSharedView: SavedView = {
      partitionKey: testReceiverIds[0]!,
      rowKey: '1',
      userId: testReceiverIds[0]!,
      viewName: `${testOwnerName}'s Test View`,
      viewMetadata: '{"key": "old-value"}',
      isDefault: false,
      isShared: true,
    }
    await tableStorageWrapper.insertEntity(existingSharedView)

    // Act
    const result = await shareView(
      tableStorageWrapper,
      testUserId,
      testViewId,
      [testReceiverIds[0]!],
      testOwnerName
    )

    // Assert
    expect(result.success).toBe(true)

    // Verify view was updated
    const updatedViews = await tableStorageWrapper.queryEntities<SavedView>(
      `PartitionKey eq '${testReceiverIds[0]}'`
    )
    expect(updatedViews.length).toBe(1)
    expect(updatedViews[0]?.viewMetadata).toBe(originalView.viewMetadata)
  })
})
