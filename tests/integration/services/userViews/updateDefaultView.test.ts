import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import type { SavedView } from '@/types/savedView'
import updateDefaultView from '@/services/userViews/updateDefaultView'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('updateDefaultView', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = `test-user-${dayjs.utc().valueOf()}`
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.SavedViews}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should handle viewId=0 case', async () => {
    // Act
    const result = await updateDefaultView(
      tableStorageWrapper,
      `${testUserId}-${dayjs.utc().valueOf()}`, // Make userId unique
      `${testOrgId}-${dayjs.utc().valueOf()}`, // Make orgId unique
      '0',
      'testPage'
    )

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe(
      'Medisolv Default View was marked as the default View'
    )
  })

  test('should return error for unknown view id', async () => {
    // Act
    const result = await updateDefaultView(
      tableStorageWrapper,
      testUserId,
      testOrgId,
      '999',
      'testPage'
    )

    // Assert
    expect(result.success).toBe(false)
    expect(result.message).toBe('An unknown view id was passed')
  })

  test('should handle views without version in metadata', async () => {
    // Arrange
    const viewWithoutVersion: SavedView = {
      partitionKey: testUserId,
      rowKey: `view-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      viewName: 'No Version View',
      viewMetadata: JSON.stringify({ Page: 'testPage' }),
      isDefault: false,
      isShared: false,
      Id: 1,
    }

    await tableStorageWrapper.insertEntity(viewWithoutVersion)

    // Act
    const result = await updateDefaultView(
      tableStorageWrapper,
      testUserId,
      testOrgId,

      '1',
      'testPage'
    )

    // Assert
    expect(result.success).toBe(false)
    expect(result.message).toBe('An unknown view id was passed')
  })
})
