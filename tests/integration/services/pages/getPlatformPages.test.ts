import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { getPlatformPages } from '@/services/pages/getPlatformPages'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'
import { SelectionType } from '@/enums/selectionType'

dayjs.extend(utc)

let pagesTableStorage: AzureTableStorageWrapper

const testOrgId = `test-org-${dayjs.utc().valueOf()}`
const testUserId = `test-user-${dayjs.utc().valueOf()}`

beforeEach(async () => {
  pagesTableStorage = new AzureTableStorageWrapper(
    `${StorageTables.ManagePages}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
  )
  await pagesTableStorage.createTable()
})

afterEach(async () => {
  await pagesTableStorage.deleteTable()
})

describe('getPlatformPages', () => {
  test('should create and return default pages when none exist', async () => {
    const results = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true, // hasDashboardAccess
      testUserId
    )

    expect(results).not.toBeNull()
    expect(Array.isArray(results)).toBeTruthy()
    expect(results.length).toBeGreaterThan(0)

    // Verify the structure of returned pages
    results.forEach((page) => {
      expect(page).toHaveProperty('partitionKey', testOrgId)
      expect(page).toHaveProperty('rowKey')
      expect(page).toHaveProperty('name')
      expect(page).toHaveProperty('isVisible')
      expect(page).toHaveProperty('routeOrExternalUrl')
      expect(page).toHaveProperty('isPrimary')
      expect(page).toHaveProperty('isExternal')
      expect(page).toHaveProperty('sortOrder')
    })

    // Verify Measures and Scorecards are visible by default
    const measuresPage = results.find(
      (p) => p.name.toLowerCase() === 'measures'
    )
    const scorecardsPage = results.find(
      (p) => p.name.toLowerCase() === 'scorecards'
    )
    expect(measuresPage?.isVisible).toBe(true)
    expect(scorecardsPage?.isVisible).toBe(true)
  })

  test('should handle visibility based on access', async () => {
    // Test with dashboard access
    const resultsWithAccess = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId
    )

    const dashboardPageWithAccess = resultsWithAccess.find(
      (p) => p.name.toLowerCase() === 'measures'
    )
    expect(dashboardPageWithAccess?.isVisible).toBe(true)

    // Test without dashboard access
    const resultsWithoutAccess = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      false,
      testUserId
    )

    const dashboardPageWithoutAccess = resultsWithoutAccess.find(
      (p) => p.name.toLowerCase() === 'dashboards'
    )
    expect(dashboardPageWithoutAccess?.isVisible).toBe(false)
  })

  test('should return existing pages when they exist', async () => {
    // First call to create pages
    const initialResults = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId
    )

    // Second call should return existing pages
    const subsequentResults = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId
    )

    expect(subsequentResults.length).toBe(initialResults.length)

    // Compare some key properties
    const initialPage = initialResults[0]
    const subsequentPage = subsequentResults.find(
      (p) => p.name === initialPage?.name
    )

    expect(subsequentPage).toBeDefined()
    expect(subsequentPage?.partitionKey).toBe(initialPage?.partitionKey)
    expect(subsequentPage?.name).toBe(initialPage?.name)
    expect(subsequentPage?.routeOrExternalUrl).toBe(
      initialPage?.routeOrExternalUrl
    )
  })

  test('should handle error cases gracefully', async () => {
    // Delete table to simulate error
    await pagesTableStorage.deleteTable()

    const results = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId
    )

    expect(results).toEqual([])
  })

  test('should maintain correct sort order', async () => {
    const results = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId
    )

    // Verify pages are returned in correct sort order
    const sortedResults = [...results].sort((a, b) => a.sortOrder - b.sortOrder)
    expect(results).toEqual(sortedResults)
  })

  test('should migrate and return platform pages when migrationConfig is provided', async () => {
    // Mock SQL results with correct property names
    const sqlPages = [
      {
        PageId: '1', // Changed from Id
        OrganizationId: testOrgId,
        Name: 'SQL Page 1',
        IsVisible: true,
        RouteOrExternalUrl: '/page1',
        IsPrimary: true,
        IsExternal: false,
        SortOrder: 1,
        OwnerId: testUserId, // Added missing required field
        Message: 'NULL', // Added missing required field
      },
      {
        PageId: '2', // Changed from Id
        OrganizationId: testOrgId,
        Name: 'SQL Page 2',
        IsVisible: true,
        RouteOrExternalUrl: '/page2',
        IsPrimary: false,
        IsExternal: false,
        SortOrder: 2,
        OwnerId: testUserId, // Added missing required field
        Message: 'NULL', // Added missing required field
      },
    ]

    // Mock prisma client
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          pages: {
            findMany: jest.fn().mockResolvedValue(sqlPages as never),
          },
        },
      },
    } as any

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Act
    const results = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: '1',
          name: 'SQL Page 1',
          isVisible: true,
          routeOrExternalUrl: '/page1',
          isPrimary: true,
          isExternal: false,
          sortOrder: 1,
          ownerId: testUserId,
          message: 'NULL',
        }),
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: '2',
          name: 'SQL Page 2',
          isVisible: true,
          routeOrExternalUrl: '/page2',
          isPrimary: false,
          isExternal: false,
          sortOrder: 2,
          ownerId: testUserId,
          message: 'NULL',
        }),
      ])
    )

    // Verify pages were migrated to table storage
    const storedPages = await pagesTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedPages).toHaveLength(2)
  })

  test('should handle empty SQL results during migration', async () => {
    // Mock empty SQL results
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          pages: {
            findMany: jest.fn().mockResolvedValue([] as never),
          },
        },
      },
    } as any

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Act
    const results = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId,
      migrationConfig
    )

    // Assert - should return default pages
    expect(results.length).toBeGreaterThan(0)
    expect(results.some((p) => p.name.toLowerCase() === 'measures')).toBe(true)
    expect(results.some((p) => p.name.toLowerCase() === 'scorecards')).toBe(
      true
    )
  })

  test('should handle missing prisma client during migration', async () => {
    // Clear mock prisma client
    globalThis.prismaClients = {}

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Act
    const results = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId,
      migrationConfig
    )

    // Assert - should return default pages
    expect(results.length).toBeGreaterThan(0)
    expect(results.some((p) => p.name.toLowerCase() === 'measures')).toBe(true)
    expect(results.some((p) => p.name.toLowerCase() === 'scorecards')).toBe(
      true
    )
  })

  test('should migrate pages when migrationConfig exists and no pages exist in table storage', async () => {
    // Mock SQL results
    const sqlPages = [
      {
        PageId: '1',
        OrganizationId: testOrgId,
        Name: 'SQL Page 1',
        IsVisible: true,
        RouteOrExternalUrl: '/page1',
        IsPrimary: true,
        IsExternal: false,
        SortOrder: 1,
        OwnerId: testUserId,
        Message: 'NULL',
      },
    ]

    // Mock prisma client
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          pages: {
            findMany: jest.fn().mockResolvedValue(sqlPages as never),
          },
        },
      },
    } as any

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Verify no pages exist initially
    const initialPages = await pagesTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(initialPages).toHaveLength(0)

    // Act
    const results = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        rowKey: '1',
        name: 'SQL Page 1',
        isVisible: true,
        routeOrExternalUrl: '/page1',
        isPrimary: true,
        isExternal: false,
        sortOrder: 1,
        ownerId: testUserId,
        message: 'NULL',
      })
    )

    // Verify pages were migrated to table storage
    const storedPages = await pagesTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedPages).toHaveLength(1)
  })

  test('should not migrate pages when migrationConfig exists but pages already exist in table storage', async () => {
    // First create some existing pages
    const existingPage = {
      partitionKey: testOrgId,
      rowKey: 'existing-1',
      name: 'Existing Page',
      isVisible: true,
      routeOrExternalUrl: '/existing',
      isPrimary: true,
      isExternal: false,
      sortOrder: 1,
      ownerId: testUserId,
      message: 'NULL',
    }
    await pagesTableStorage.insertEntity(existingPage)

    // Mock SQL results that shouldn't be migrated
    const sqlPages = [
      {
        PageId: 'sql-1',
        OrganizationId: testOrgId,
        Name: 'SQL Page',
        IsVisible: true,
        RouteOrExternalUrl: '/sql',
        IsPrimary: true,
        IsExternal: false,
        SortOrder: 2,
        OwnerId: testUserId,
        Message: 'NULL',
      },
    ]

    // Mock prisma client
    const mockFindMany = jest.fn().mockResolvedValue(sqlPages as never)
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          pages: {
            findMany: mockFindMany,
          },
        },
      },
    } as any

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Act
    const results = await getPlatformPages(
      pagesTableStorage,
      testOrgId,
      true,
      testUserId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        rowKey: 'existing-1',
        name: 'Existing Page',
      })
    )

    // Verify SQL query was not called
    expect(mockFindMany).not.toHaveBeenCalled()

    // Verify no additional pages were added to table storage
    const storedPages = await pagesTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedPages).toHaveLength(1)
  })
})
