import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { upsertPlatformPages } from '@/services/pages/upsertPlatformPages'
import { Pages } from '@/types/pages'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('upsertPlatformPages', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  const testUserId = `test-user-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.ManagePages}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )

    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert new page when it does not exist', async () => {
    // Arrange
    const testPage: Pages = {
      partitionKey: testOrgId,
      rowKey: `page-${dayjs.utc().valueOf()}`,
      name: 'Test Page',
      isVisible: true,
      routeOrExternalUrl: '/test-route',
      isPrimary: true,
      isExternal: false,
      sortOrder: 1,
      ownerId: testUserId,
      message: 'NULL',
    }

    // Act
    await upsertPlatformPages(tableStorageWrapper, testPage)

    // Assert
    const pages = await tableStorageWrapper.queryEntities<Pages>(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(pages.length).toBe(1)
    expect(pages[0]).toMatchObject({
      partitionKey: testOrgId,
      name: 'Test Page',
      isVisible: true,
      routeOrExternalUrl: '/test-route',
      isPrimary: true,
      isExternal: false,
      sortOrder: 1,
    })
  })

  test('should update existing page when it exists', async () => {
    // Arrange
    const pageId = `page-${dayjs.utc().valueOf()}`
    const initialPage: Pages = {
      partitionKey: testOrgId,
      rowKey: pageId,
      name: 'Initial Page',
      isVisible: true,
      routeOrExternalUrl: '/initial-route',
      isPrimary: true,
      isExternal: false,
      sortOrder: 1,
      ownerId: testUserId,
      message: 'NULL',
    }

    await tableStorageWrapper.insertEntity<Pages>(initialPage)

    const updatedPage: Pages = {
      ...initialPage,
      name: 'Updated Page',
      routeOrExternalUrl: '/updated-route',
      isVisible: false,
    }

    // Act
    await upsertPlatformPages(tableStorageWrapper, updatedPage)

    // Assert
    const pages = await tableStorageWrapper.queryEntities<Pages>(
      `PartitionKey eq '${testOrgId}' and RowKey eq '${pageId}'`
    )
    expect(pages.length).toBe(1)
    expect(pages[0]).toMatchObject({
      partitionKey: testOrgId,
      rowKey: pageId,
      name: 'Updated Page',
      isVisible: false,
      routeOrExternalUrl: '/updated-route',
    })
  })

  test('should handle multiple pages for same organization', async () => {
    // Arrange
    const testPages: Pages[] = [
      {
        partitionKey: testOrgId,
        rowKey: `page1-${dayjs.utc().valueOf()}`,
        name: 'Page 1',
        isVisible: true,
        routeOrExternalUrl: '/route-1',
        isPrimary: true,
        isExternal: false,
        sortOrder: 1,
        ownerId: testUserId,
        message: 'NULL',
      },
      {
        partitionKey: testOrgId,
        rowKey: `page2-${dayjs.utc().valueOf()}`,
        name: 'Page 2',
        isVisible: true,
        routeOrExternalUrl: '/route-2',
        isPrimary: false,
        isExternal: false,
        sortOrder: 2,
        ownerId: testUserId,
        message: 'NULL',
      },
    ]

    // Act
    for (const page of testPages) {
      await upsertPlatformPages(tableStorageWrapper, page)
    }

    // Assert
    const pages = await tableStorageWrapper.queryEntities<Pages>(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(pages.length).toBe(2)
    expect(pages.map((p) => p.name)).toEqual(['Page 1', 'Page 2'])
    expect(pages.map((p) => p.sortOrder)).toEqual([1, 2])
  })
})
