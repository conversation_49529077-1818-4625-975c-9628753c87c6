import {
    factory,
    closePool,
} from '@/services/snowflake/SnowflakeRepositoryFactory'
import { env } from '@/env'
import {
    createSnowflakeConfiguration
} from '@/services/snowflake/SnowflakeHelper'
import { IntervalType } from '@/types/intervalType'
import fs from 'fs'
import { fileURLToPath } from 'url'
import path from 'path'
import { jest } from '@jest/globals'
import { PrimaryMeasureTypeConstants } from "@/enums/primaryMeasureTypeConstants";
import dayjs from "dayjs";
import { MeasureSummary } from "@/types/measureSummary";

// Suppress console output during tests
beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation(() => { })
    jest.spyOn(console, 'log').mockImplementation(() => { })
})

// Clean up resources after all tests
afterAll(async () => {
    await closePool()
})

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const expectedResults = JSON.parse(
    fs.readFileSync(`${__dirname}/expected-measure-summary-results.json`, 'utf8')
)

describe('MeasureSummaryRepository', () => {
    describe('findByMeasuresAndDateRange', () => {

        test('should find measure summaries for monthly interval with specific ambulatory measures', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
                return
            }

            // Arrange
            const repo = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE',
            }).createMeasureSummaryRepository()

            // Test parameters
            const measureList = [
                "f3b00d78-29ac-ea11-a8fe-4cedfb610c38",
                "603bd441-7fb0-ee11-bea0-0022484ce73d"
            ]
            const interval: IntervalType = 'M'
            const startDate = dayjs('2024-01-01')
            const endDate = dayjs('2024-02-01')
            const measureType = PrimaryMeasureTypeConstants.AmbulatoryMeasures
            const includeLevel4 = false

            // Act
            const results = await repo.findByMeasuresAndDateRange(
                measureList,
                interval,
                startDate,
                endDate,
                measureType,
                includeLevel4
            )

            // Assert
            expect(results).toBeDefined()
            assertResults(results, expectedResults.expected1)
        })

        test('should find measure summaries for monthly interval with specific hospital measures', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
                return
            }

            // Arrange
            const repo = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE',
            }).createMeasureSummaryRepository()

            // Test parameters
            const measureList = [
                "f3b00d78-29ac-ea11-a8fe-4cedfb610c38",
                "603bd441-7fb0-ee11-bea0-0022484ce73d"
            ]
            const interval: IntervalType = 'M'
            const startDate = dayjs('2024-01-01')
            const endDate = dayjs('2024-02-01')
            const measureType = PrimaryMeasureTypeConstants.HospitalMeasures
            const includeLevel4 = false

            // Act
            const results = await repo.findByMeasuresAndDateRange(
                measureList,
                interval,
                startDate,
                endDate,
                measureType,
                includeLevel4
            )

            // Assert
            expect(results).toBeDefined()
            assertResults(results, expectedResults.expected2)
        })

        test('should find measure summaries for quarterly interval with specific Hospital measures', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
                return
            }

            // Arrange
            const repo = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE',
            }).createMeasureSummaryRepository()

            // Test parameters
            const measureList = [
                "f3b00d78-29ac-ea11-a8fe-4cedfb610c38",
                "603bd441-7fb0-ee11-bea0-0022484ce73d"
            ]
            const interval: IntervalType = 'Q'
            const startDate = dayjs('2024-01-01')
            const endDate = dayjs('2024-04-01')
            const measureType = PrimaryMeasureTypeConstants.HospitalMeasures
            const includeLevel4 = false

            // Act
            const results = await repo.findByMeasuresAndDateRange(
                measureList,
                interval,
                startDate,
                endDate,
                measureType,
                includeLevel4
            )

            // Assert
            expect(results).toBeDefined()
            assertResults(results, expectedResults.expected3)
        })

        test('should find measure summaries for yearly interval with all Ambulatory measures excluding level 4', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
                return
            }

            // Arrange
            const repo = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE',
            }).createMeasureSummaryRepository()

            // Test parameters
            const measureList = [
                "*"
            ]
            const interval: IntervalType = 'Y'
            const startDate = dayjs('2024-01-01')
            const endDate = dayjs('2024-12-30')
            const measureType = PrimaryMeasureTypeConstants.AmbulatoryMeasures
            const includeLevel4 = false

            // Act
            const results = await repo.findByMeasuresAndDateRange(
                measureList,
                interval,
                startDate,
                endDate,
                measureType,
                includeLevel4
            )

            // Assert
            expect(results).toBeDefined()
            assertResults(results, expectedResults.expected4)
        })

        test('should find measure summaries for yearly interval with all Ambulatory measures including level 4', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
                return
            }

            // Arrange
            const repo = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE',
            }).createMeasureSummaryRepository()

            // Test parameters
            const measureList = [
                "*"
            ]
            const interval: IntervalType = 'Y'
            const startDate = dayjs('2024-01-01')
            const endDate = dayjs('2024-12-30')
            const measureType = PrimaryMeasureTypeConstants.AmbulatoryMeasures
            const includeLevel4 = true

            // Act
            const results = await repo.findByMeasuresAndDateRange(
                measureList,
                interval,
                startDate,
                endDate,
                measureType,
                includeLevel4
            )

            // Assert
            expect(results).toBeDefined()
            assertResults(results, expectedResults.expected5)
        })

        test('should find measure summaries for quarterly interval that spans a year', async () => {
            // Skip test if Snowflake account is not defined
            if (!env.SNOWFLAKE_ACCOUNT) {
                console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
                return
            }

            // Arrange
            const repo = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE',
            }).createMeasureSummaryRepository()

            // Test parameters
            const measureList = [
                "*"
            ]
            const interval: IntervalType = 'Q'
            const startDate = dayjs('2024-01-01')
            const endDate = dayjs('2024-12-30')
            const measureType = PrimaryMeasureTypeConstants.AmbulatoryMeasures
            const includeLevel4 = false

            // Act
            const results = await repo.findByMeasuresAndDateRange(
                measureList,
                interval,
                startDate,
                endDate,
                measureType,
                includeLevel4
            )

            // Assert
            expect(results).toBeDefined()
            assertResults(results, expectedResults.expected6)
        })

    })
})

/**
 * Helper function to assert that the actual results match the expected results
 */
function assertResults(
    results: MeasureSummary[],
    expectedResults: MeasureSummary[]
) {
    expect(results).toBeDefined()
    expect(results.length).toBeGreaterThanOrEqual(expectedResults.length)

    expectedResults.forEach((expected: any) => {
        const actualList = results.filter((result) =>
            result.LongMeasureName === expected["Measure Name"] &&
            result.EntityDescription == expected["Entity Description"] &&
            result.EntityType == expected["Entity Type"] &&
            result.StartDate.toISOString() == dayjs(expected["Start Date"]).toISOString()
        )
        expect(actualList.length).toBe(1)

        const actual = actualList[0]!

        // Check key properties

        expect(actual.Id).toBe(expected.Id)
        expect(actual.MeasureId).toBe(expected["Measure ID"])
        expect(actual.Period).toBe(expected.Interval)

        // Check numeric values with rounding to handle floating point differences
        expect(Math.round(actual.Numerator * 100) / 100).toBe(Math.round(expected.Numerator * 100) / 100)
        expect(Math.round(actual.Denominator * 100) / 100).toBe(Math.round(expected.Denominator * 100) / 100)
        expect(Math.round(actual.DenominatorOnly * 100) / 100).toBe(Math.round(expected["Denominator Only"] * 100) / 100)
        expect(Math.round(actual.Performance * 100) / 100).toBe(Math.round(expected.Performance * 100) / 100)
        expect(Math.round(actual.IPP * 100) / 100).toBe(Math.round(expected.IPP * 100) / 100)

        // Check date values
        expect(dayjs(actual.StartDate).toISOString()).toBe(dayjs(expected["Start Date"]).toISOString())
    })
}
