{"expected1": [], "expected2": [{"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (E)", "Measure ID": "CMS 123", "Entity Description": "Blue Jay General Hospital", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "M", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (E)", "Measure ID": "CMS 123", "Entity Description": "Harris Hawk Hospital", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "M", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 31}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (E)", "Measure ID": "CMS 123", "Entity Description": "Mockingjay Hospital Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "M", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (E)", "Measure ID": "CMS 123", "Entity Description": "Mockingjay Medical Center", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "M", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (Ethnicity - Unknown) (E)", "Measure ID": "CMS 123", "Entity Description": "Blue Jay General Hospital", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "M", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (Ethnicity - Unknown) (E)", "Measure ID": "CMS 123", "Entity Description": "Harris Hawk Hospital", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "M", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 31}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (Ethnicity - Unknown) (E)", "Measure ID": "CMS 123", "Entity Description": "Mockingjay Hospital Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "M", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (Ethnicity - Unknown) (E)", "Measure ID": "CMS 123", "Entity Description": "Mockingjay Medical Center", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "M", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 19}], "expected3": [{"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (E)", "Measure ID": "CMS 123", "Entity Description": "Blue Jay General Hospital", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 51}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (E)", "Measure ID": "CMS 123", "Entity Description": "Harris Hawk Hospital", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 32}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (E)", "Measure ID": "CMS 123", "Entity Description": "Mockingjay Hospital Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 50}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (E)", "Measure ID": "CMS 123", "Entity Description": "Mockingjay Medical Center", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 50}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (Ethnicity - Unknown) (E)", "Measure ID": "CMS 123", "Entity Description": "Blue Jay General Hospital", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 51}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (Ethnicity - Unknown) (E)", "Measure ID": "CMS 123", "Entity Description": "Harris Hawk Hospital", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 32}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (Ethnicity - Unknown) (E)", "Measure ID": "CMS 123", "Entity Description": "Mockingjay Hospital Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 50}, {"Measure Name": "Anticoagulation Therapy for Atrial Fibrillation/Flutter (Ethnicity - Unknown) (E)", "Measure ID": "CMS 123", "Entity Description": "Mockingjay Medical Center", "Entity Type": "CCN", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 0, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 50}], "expected4": [{"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 138, "Denominator": 1286, "Denominator Only": 966, "Numerator Exception": 0, "Denominator Exclusion": 182, "Denominator Exception": 0, "Performance": 12.5, "Numerator Value": null, "Denominator Value": null, "IPP": 1286}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Alan (Mockingjay Medical Health System)", "Entity Type": "Partner", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 10, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 25, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 138, "Denominator": 1286, "Denominator Only": 966, "Numerator Exception": 0, "Denominator Exclusion": 182, "Denominator Exception": 0, "Performance": 12.5, "Numerator Value": null, "Denominator Value": null, "IPP": 1286}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 45, "Denominator": 320, "Denominator Only": 275, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.06, "Numerator Value": null, "Denominator Value": null, "IPP": 320}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 45, "Denominator": 320, "Denominator Only": 275, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.06, "Numerator Value": null, "Denominator Value": null, "IPP": 320}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1491, "Denominator": 2476, "Denominator Only": 783, "Numerator Exception": 0, "Denominator Exclusion": 202, "Denominator Exception": 0, "Performance": 65.57, "Numerator Value": null, "Denominator Value": null, "IPP": 2476}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1491, "Denominator": 2476, "Denominator Only": 783, "Numerator Exception": 0, "Denominator Exclusion": 202, "Denominator Exception": 0, "Performance": 65.57, "Numerator Value": null, "Denominator Value": null, "IPP": 2476}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 531, "Denominator": 3550, "Denominator Only": 3011, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 14.99, "Numerator Value": null, "Denominator Value": null, "IPP": 3550}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 531, "Denominator": 3550, "Denominator Only": 3011, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 14.99, "Numerator Value": null, "Denominator Value": null, "IPP": 3550}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 7275, "Denominator Only": 7143, "Numerator Exception": 0, "Denominator Exclusion": 132, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 7275}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 7275, "Denominator Only": 7143, "Numerator Exception": 0, "Denominator Exclusion": 132, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 7275}], "expected5": [{"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "Attenborough,  Luis (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 44, "Denominator Only": 37, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 11.9, "Numerator Value": null, "Denominator Value": null, "IPP": 44}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Vern (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 22, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 7, "Denominator Exception": 0, "Performance": 20, "Numerator Value": null, "Denominator Value": null, "IPP": 22}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>ure,  Huey (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 33, "Denominator Only": 28, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 9.68, "Numerator Value": null, "Denominator Value": null, "IPP": 33}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Spiro (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 24, "Denominator Only": 17, "Numerator Exception": 0, "Denominator Exclusion": 7, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 24}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 138, "Denominator": 1286, "Denominator Only": 966, "Numerator Exception": 0, "Denominator Exclusion": 182, "Denominator Exception": 0, "Performance": 12.5, "Numerator Value": null, "Denominator Value": null, "IPP": 1286}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Madison (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 53, "Denominator Only": 32, "Numerator Exception": 0, "Denominator Exclusion": 17, "Denominator Exception": 0, "Performance": 11.11, "Numerator Value": null, "Denominator Value": null, "IPP": 53}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 50, "Denominator Only": 32, "Numerator Exception": 0, "Denominator Exclusion": 13, "Denominator Exception": 0, "Performance": 13.51, "Numerator Value": null, "Denominator Value": null, "IPP": 50}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 10, "Denominator Only": 8, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 22, "Denominator Only": 14, "Numerator Exception": 0, "Denominator Exclusion": 5, "Denominator Exception": 0, "Performance": 17.65, "Numerator Value": null, "Denominator Value": null, "IPP": 22}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 19, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 5.88, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 6, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 10, "Denominator": 63, "Denominator Only": 53, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 15.87, "Numerator Value": null, "Denominator Value": null, "IPP": 63}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 2, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 50, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON>  (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 38, "Denominator Only": 32, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 5.88, "Numerator Value": null, "Denominator Value": null, "IPP": 38}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Alan (Mockingjay Medical Health System)", "Entity Type": "Partner", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 10, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 25, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Alan (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 10, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 25, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 40, "Denominator Only": 26, "Numerator Exception": 0, "Denominator Exclusion": 10, "Denominator Exception": 0, "Performance": 13.33, "Numerator Value": null, "Denominator Value": null, "IPP": 40}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": null, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 20, "Denominator Only": 14, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 17.65, "Numerator Value": null, "Denominator Value": null, "IPP": 20}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Apollo (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 6, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Murray (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 23, "Denominator Only": 18, "Numerator Exception": 0, "Denominator Exclusion": 5, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 23}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 39, "Denominator Only": 23, "Numerator Exception": 0, "Denominator Exclusion": 11, "Denominator Exception": 0, "Performance": 17.86, "Numerator Value": null, "Denominator Value": null, "IPP": 39}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 15, "Denominator Only": 14, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 6.67, "Numerator Value": null, "Denominator Value": null, "IPP": 15}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 8, "Denominator Only": 7, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 8}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 19, "Denominator Only": 15, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 6.25, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 18, "Denominator": 71, "Denominator Only": 52, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 25.71, "Numerator Value": null, "Denominator Value": null, "IPP": 71}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 10, "Denominator": 64, "Denominator Only": 45, "Numerator Exception": 0, "Denominator Exclusion": 9, "Denominator Exception": 0, "Performance": 18.18, "Numerator Value": null, "Denominator Value": null, "IPP": 64}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Lu (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 3, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 33.33, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 6, "Denominator": 19, "Denominator Only": 13, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 31.58, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 6, "Denominator": 41, "Denominator Only": 30, "Numerator Exception": 0, "Denominator Exclusion": 5, "Denominator Exception": 0, "Performance": 16.67, "Numerator Value": null, "Denominator Value": null, "IPP": 41}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 32, "Denominator Only": 25, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 10.71, "Numerator Value": null, "Denominator Value": null, "IPP": 32}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 6, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 138, "Denominator": 1286, "Denominator Only": 966, "Numerator Exception": 0, "Denominator Exclusion": 182, "Denominator Exception": 0, "Performance": 12.5, "Numerator Value": null, "Denominator Value": null, "IPP": 1286}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 15, "Denominator Only": 8, "Numerator Exception": 0, "Denominator Exclusion": 5, "Denominator Exception": 0, "Performance": 20, "Numerator Value": null, "Denominator Value": null, "IPP": 15}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 9, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 14.29, "Numerator Value": null, "Denominator Value": null, "IPP": 9}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 57, "Denominator Only": 39, "Numerator Exception": 0, "Denominator Exclusion": 13, "Denominator Exception": 0, "Performance": 11.36, "Numerator Value": null, "Denominator Value": null, "IPP": 57}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 54, "Denominator Only": 44, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 13.73, "Numerator Value": null, "Denominator Value": null, "IPP": 54}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Dane (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 7, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 25, "Numerator Value": null, "Denominator Value": null, "IPP": 7}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Marvin (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 17, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 14.29, "Numerator Value": null, "Denominator Value": null, "IPP": 17}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 8, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 28.57, "Numerator Value": null, "Denominator Value": null, "IPP": 8}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 11, "Denominator Only": 7, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 12.5, "Numerator Value": null, "Denominator Value": null, "IPP": 11}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 8, "Denominator": 175, "Denominator Only": 160, "Numerator Exception": 0, "Denominator Exclusion": 7, "Denominator Exception": 0, "Performance": 4.76, "Numerator Value": null, "Denominator Value": null, "IPP": 175}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 8, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 20, "Numerator Value": null, "Denominator Value": null, "IPP": 8}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 71, "Denominator Only": 61, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 3.17, "Numerator Value": null, "Denominator Value": null, "IPP": 71}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 6, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 9, "Denominator": 53, "Denominator Only": 34, "Numerator Exception": 0, "Denominator Exclusion": 10, "Denominator Exception": 0, "Performance": 20.93, "Numerator Value": null, "Denominator Value": null, "IPP": 53}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 9, "Denominator Only": 8, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 11.11, "Numerator Value": null, "Denominator Value": null, "IPP": 9}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "Attenborough,  Luis (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>ure,  Huey (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Spiro (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 2, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 50, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 45, "Denominator": 320, "Denominator Only": 275, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.06, "Numerator Value": null, "Denominator Value": null, "IPP": 320}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Madison (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 17, "Denominator Only": 15, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 11.76, "Numerator Value": null, "Denominator Value": null, "IPP": 17}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 19, "Denominator Only": 15, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 21.05, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 55, "Denominator Only": 55, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 55}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 18, "Denominator Only": 14, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 22.22, "Numerator Value": null, "Denominator Value": null, "IPP": 18}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 7, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 28.57, "Numerator Value": null, "Denominator Value": null, "IPP": 7}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Murray (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 23, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 30.43, "Numerator Value": null, "Denominator Value": null, "IPP": 23}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 9, "Denominator Only": 7, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 22.22, "Numerator Value": null, "Denominator Value": null, "IPP": 9}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 55, "Denominator Only": 48, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 12.73, "Numerator Value": null, "Denominator Value": null, "IPP": 55}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 10, "Denominator Only": 8, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 20, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 37, "Denominator Only": 32, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 13.51, "Numerator Value": null, "Denominator Value": null, "IPP": 37}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 45, "Denominator": 320, "Denominator Only": 275, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.06, "Numerator Value": null, "Denominator Value": null, "IPP": 320}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 33, "Denominator Only": 28, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 15.15, "Numerator Value": null, "Denominator Value": null, "IPP": 33}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Marvin (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 16, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 25, "Numerator Value": null, "Denominator Value": null, "IPP": 16}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "Attenborough,  Luis (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 40, "Denominator": 54, "Denominator Only": 13, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 75.47, "Numerator Value": null, "Denominator Value": null, "IPP": 54}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Vern (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 20, "Denominator": 30, "Denominator Only": 7, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 74.07, "Numerator Value": null, "Denominator Value": null, "IPP": 30}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>ure,  Huey (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 8, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 62.5, "Numerator Value": null, "Denominator Value": null, "IPP": 8}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Liana (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 18, "Denominator": 33, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 60, "Numerator Value": null, "Denominator Value": null, "IPP": 33}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Spiro (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 13, "Denominator": 18, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 72.22, "Numerator Value": null, "Denominator Value": null, "IPP": 18}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 14, "Denominator Only": 11, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 21.43, "Numerator Value": null, "Denominator Value": null, "IPP": 14}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1491, "Denominator": 2476, "Denominator Only": 783, "Numerator Exception": 0, "Denominator Exclusion": 202, "Denominator Exception": 0, "Performance": 65.57, "Numerator Value": null, "Denominator Value": null, "IPP": 2476}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Madison (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 14, "Denominator": 32, "Denominator Only": 15, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 48.28, "Numerator Value": null, "Denominator Value": null, "IPP": 32}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 10, "Denominator": 12, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 90.91, "Numerator Value": null, "Denominator Value": null, "IPP": 12}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 143, "Denominator": 181, "Denominator Only": 31, "Numerator Exception": 0, "Denominator Exclusion": 7, "Denominator Exception": 0, "Performance": 82.18, "Numerator Value": null, "Denominator Value": null, "IPP": 181}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 31, "Denominator": 55, "Denominator Only": 18, "Numerator Exception": 0, "Denominator Exclusion": 6, "Denominator Exception": 0, "Performance": 63.27, "Numerator Value": null, "Denominator Value": null, "IPP": 55}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 12, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 63.64, "Numerator Value": null, "Denominator Value": null, "IPP": 12}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 10, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 55.56, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 49, "Denominator": 81, "Denominator Only": 27, "Numerator Exception": 0, "Denominator Exclusion": 5, "Denominator Exception": 0, "Performance": 64.47, "Numerator Value": null, "Denominator Value": null, "IPP": 81}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 16, "Denominator": 32, "Denominator Only": 10, "Numerator Exception": 0, "Denominator Exclusion": 6, "Denominator Exception": 0, "Performance": 61.54, "Numerator Value": null, "Denominator Value": null, "IPP": 32}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 14, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 45.45, "Numerator Value": null, "Denominator Value": null, "IPP": 14}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 92, "Denominator": 126, "Denominator Only": 25, "Numerator Exception": 0, "Denominator Exclusion": 9, "Denominator Exception": 0, "Performance": 78.63, "Numerator Value": null, "Denominator Value": null, "IPP": 126}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 1, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 100, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 19, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 25, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 11, "Denominator": 15, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 73.33, "Numerator Value": null, "Denominator Value": null, "IPP": 15}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 21, "Denominator": 30, "Denominator Only": 8, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 72.41, "Numerator Value": null, "Denominator Value": null, "IPP": 30}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 12, "Denominator": 24, "Denominator Only": 9, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 57.14, "Numerator Value": null, "Denominator Value": null, "IPP": 24}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON>  (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 18, "Denominator": 36, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 6, "Denominator Exception": 0, "Performance": 60, "Numerator Value": null, "Denominator Value": null, "IPP": 36}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 14, "Denominator": 28, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 53.85, "Numerator Value": null, "Denominator Value": null, "IPP": 28}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Alan (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 17, "Denominator": 34, "Denominator Only": 17, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 50, "Numerator Value": null, "Denominator Value": null, "IPP": 34}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 75, "Denominator": 108, "Denominator Only": 25, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 75, "Numerator Value": null, "Denominator Value": null, "IPP": 108}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 25, "Denominator": 69, "Denominator Only": 36, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 40.98, "Numerator Value": null, "Denominator Value": null, "IPP": 69}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 63, "Denominator": 97, "Denominator Only": 26, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 70.79, "Numerator Value": null, "Denominator Value": null, "IPP": 97}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Apollo (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 14, "Denominator Only": 7, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 41.67, "Numerator Value": null, "Denominator Value": null, "IPP": 14}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 8, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 42.86, "Numerator Value": null, "Denominator Value": null, "IPP": 8}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 13, "Denominator": 17, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 86.67, "Numerator Value": null, "Denominator Value": null, "IPP": 17}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Murray (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 9, "Denominator": 18, "Denominator Only": 8, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 52.94, "Numerator Value": null, "Denominator Value": null, "IPP": 18}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 4, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 66.67, "Numerator Value": null, "Denominator Value": null, "IPP": 4}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 16, "Denominator": 32, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 50, "Numerator Value": null, "Denominator Value": null, "IPP": 32}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 102, "Denominator": 175, "Denominator Only": 56, "Numerator Exception": 0, "Denominator Exclusion": 17, "Denominator Exception": 0, "Performance": 64.56, "Numerator Value": null, "Denominator Value": null, "IPP": 175}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 16, "Denominator": 41, "Denominator Only": 19, "Numerator Exception": 0, "Denominator Exclusion": 6, "Denominator Exception": 0, "Performance": 45.71, "Numerator Value": null, "Denominator Value": null, "IPP": 41}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 26, "Denominator": 45, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 7, "Denominator Exception": 0, "Performance": 68.42, "Numerator Value": null, "Denominator Value": null, "IPP": 45}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 24, "Denominator": 36, "Denominator Only": 11, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 68.57, "Numerator Value": null, "Denominator Value": null, "IPP": 36}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 33, "Denominator": 54, "Denominator Only": 7, "Numerator Exception": 0, "Denominator Exclusion": 14, "Denominator Exception": 0, "Performance": 82.5, "Numerator Value": null, "Denominator Value": null, "IPP": 54}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 15, "Denominator": 30, "Denominator Only": 7, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 68.18, "Numerator Value": null, "Denominator Value": null, "IPP": 30}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 13, "Denominator": 23, "Denominator Only": 9, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 59.09, "Numerator Value": null, "Denominator Value": null, "IPP": 23}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 10, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 50, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 8, "Denominator": 18, "Denominator Only": 7, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 53.33, "Numerator Value": null, "Denominator Value": null, "IPP": 18}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 9, "Denominator": 16, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 64.29, "Numerator Value": null, "Denominator Value": null, "IPP": 16}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Lu (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 9, "Denominator": 15, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 75, "Numerator Value": null, "Denominator Value": null, "IPP": 15}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 10, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 77.78, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Julius (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 2, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 100, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 40, "Denominator": 70, "Denominator Only": 26, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 60.61, "Numerator Value": null, "Denominator Value": null, "IPP": 70}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 3, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 33.33, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 14, "Denominator Only": 10, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 28.57, "Numerator Value": null, "Denominator Value": null, "IPP": 14}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1491, "Denominator": 2476, "Denominator Only": 783, "Numerator Exception": 0, "Denominator Exclusion": 202, "Denominator Exception": 0, "Performance": 65.57, "Numerator Value": null, "Denominator Value": null, "IPP": 2476}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 101, "Denominator": 170, "Denominator Only": 49, "Numerator Exception": 0, "Denominator Exclusion": 20, "Denominator Exception": 0, "Performance": 67.33, "Numerator Value": null, "Denominator Value": null, "IPP": 170}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 8, "Denominator": 31, "Denominator Only": 19, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 29.63, "Numerator Value": null, "Denominator Value": null, "IPP": 31}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "Oman,  Ray (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 26, "Denominator": 36, "Denominator Only": 8, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 76.47, "Numerator Value": null, "Denominator Value": null, "IPP": 36}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 16, "Denominator": 34, "Denominator Only": 18, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 47.06, "Numerator Value": null, "Denominator Value": null, "IPP": 34}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 60, "Denominator": 91, "Denominator Only": 23, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 72.29, "Numerator Value": null, "Denominator Value": null, "IPP": 91}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Dane (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 14, "Denominator": 19, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 93.33, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 6, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 33.33, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 11, "Denominator": 19, "Denominator Only": 8, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 57.89, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Marvin (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 35, "Denominator": 60, "Denominator Only": 18, "Numerator Exception": 0, "Denominator Exclusion": 7, "Denominator Exception": 0, "Performance": 66.04, "Numerator Value": null, "Denominator Value": null, "IPP": 60}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 10, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 70, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 9, "Denominator": 11, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 81.82, "Numerator Value": null, "Denominator Value": null, "IPP": 11}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 50, "Denominator": 77, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 11, "Denominator Exception": 0, "Performance": 75.76, "Numerator Value": null, "Denominator Value": null, "IPP": 77}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 14, "Denominator": 18, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 77.78, "Numerator Value": null, "Denominator Value": null, "IPP": 18}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 33, "Denominator": 59, "Denominator Only": 22, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 60, "Numerator Value": null, "Denominator Value": null, "IPP": 59}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 8, "Denominator": 10, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 80, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 68, "Denominator": 115, "Denominator Only": 32, "Numerator Exception": 0, "Denominator Exclusion": 15, "Denominator Exception": 0, "Performance": 68, "Numerator Value": null, "Denominator Value": null, "IPP": 115}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 18, "Denominator": 46, "Denominator Only": 24, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 42.86, "Numerator Value": null, "Denominator Value": null, "IPP": 46}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 43, "Denominator": 72, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 13, "Denominator Exception": 0, "Performance": 72.88, "Numerator Value": null, "Denominator Value": null, "IPP": 72}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 50, "Denominator": 69, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 75.76, "Numerator Value": null, "Denominator Value": null, "IPP": 69}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 4, "Denominator Only": 0, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 100, "Numerator Value": null, "Denominator Value": null, "IPP": 4}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "Attenborough,  Luis (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 8, "Denominator": 50, "Denominator Only": 42, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 16, "Numerator Value": null, "Denominator Value": null, "IPP": 50}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Vern (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 6, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 6, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 33.33, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>ure,  Huey (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Liana (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 60, "Denominator Only": 58, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 3.33, "Numerator Value": null, "Denominator Value": null, "IPP": 60}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Spiro (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 24, "Denominator Only": 23, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 4.17, "Numerator Value": null, "Denominator Value": null, "IPP": 24}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 531, "Denominator": 3550, "Denominator Only": 3011, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 14.99, "Numerator Value": null, "Denominator Value": null, "IPP": 3550}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Madison (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 11, "Denominator Only": 11, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 11}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 24, "Denominator Only": 24, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 24}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 22, "Denominator": 169, "Denominator Only": 147, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 13.02, "Numerator Value": null, "Denominator Value": null, "IPP": 169}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 30, "Denominator Only": 26, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 13.33, "Numerator Value": null, "Denominator Value": null, "IPP": 30}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 6, "Denominator": 109, "Denominator Only": 103, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 5.5, "Numerator Value": null, "Denominator Value": null, "IPP": 109}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 12, "Denominator Only": 11, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 8.33, "Numerator Value": null, "Denominator Value": null, "IPP": 12}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 15, "Denominator Only": 15, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 15}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 21, "Denominator": 84, "Denominator Only": 63, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 25, "Numerator Value": null, "Denominator Value": null, "IPP": 84}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 48, "Denominator Only": 45, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 6.25, "Numerator Value": null, "Denominator Value": null, "IPP": 48}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 18, "Denominator Only": 17, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 5.56, "Numerator Value": null, "Denominator Value": null, "IPP": 18}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 25, "Denominator": 164, "Denominator Only": 139, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 15.24, "Numerator Value": null, "Denominator Value": null, "IPP": 164}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 23, "Denominator Only": 21, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 8.7, "Numerator Value": null, "Denominator Value": null, "IPP": 23}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 10, "Denominator": 41, "Denominator Only": 28, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 26.32, "Numerator Value": null, "Denominator Value": null, "IPP": 41}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 20, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 20, "Numerator Value": null, "Denominator Value": null, "IPP": 20}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 18, "Denominator Only": 18, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 18}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 42, "Denominator Only": 35, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 16.67, "Numerator Value": null, "Denominator Value": null, "IPP": 42}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 20, "Denominator Only": 18, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 10, "Numerator Value": null, "Denominator Value": null, "IPP": 20}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON> ,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 22, "Denominator Only": 22, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 22}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 60, "Denominator Only": 59, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 1.67, "Numerator Value": null, "Denominator Value": null, "IPP": 60}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON>  (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 9, "Denominator": 64, "Denominator Only": 55, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.06, "Numerator Value": null, "Denominator Value": null, "IPP": 64}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 19, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 15.79, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 12, "Denominator": 43, "Denominator Only": 31, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 27.91, "Numerator Value": null, "Denominator Value": null, "IPP": 43}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 38, "Denominator Only": 34, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 10.53, "Numerator Value": null, "Denominator Value": null, "IPP": 38}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Alan (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 45, "Denominator Only": 38, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 15.56, "Numerator Value": null, "Denominator Value": null, "IPP": 45}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 26, "Denominator": 115, "Denominator Only": 89, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 22.61, "Numerator Value": null, "Denominator Value": null, "IPP": 115}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 6, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 16.67, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 23, "Denominator": 129, "Denominator Only": 106, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 17.83, "Numerator Value": null, "Denominator Value": null, "IPP": 129}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 21, "Denominator Only": 18, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.29, "Numerator Value": null, "Denominator Value": null, "IPP": 21}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 16, "Denominator": 97, "Denominator Only": 81, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 16.49, "Numerator Value": null, "Denominator Value": null, "IPP": 97}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Apollo (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 10, "Denominator": 45, "Denominator Only": 35, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 22.22, "Numerator Value": null, "Denominator Value": null, "IPP": 45}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 23, "Denominator Only": 20, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 13.04, "Numerator Value": null, "Denominator Value": null, "IPP": 23}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 22, "Denominator Only": 21, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 4.55, "Numerator Value": null, "Denominator Value": null, "IPP": 22}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Murray (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 27, "Denominator Only": 27, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 27}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 10, "Denominator Only": 10, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 47, "Denominator Only": 44, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 6.38, "Numerator Value": null, "Denominator Value": null, "IPP": 47}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 55, "Denominator": 250, "Denominator Only": 195, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 22, "Numerator Value": null, "Denominator Value": null, "IPP": 250}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 23, "Denominator": 78, "Denominator Only": 55, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 29.49, "Numerator Value": null, "Denominator Value": null, "IPP": 78}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 28, "Denominator Only": 24, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.29, "Numerator Value": null, "Denominator Value": null, "IPP": 28}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 8, "Denominator": 55, "Denominator Only": 47, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.55, "Numerator Value": null, "Denominator Value": null, "IPP": 55}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 9, "Denominator": 74, "Denominator Only": 65, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 12.16, "Numerator Value": null, "Denominator Value": null, "IPP": 74}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 47, "Denominator Only": 42, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 10.64, "Numerator Value": null, "Denominator Value": null, "IPP": 47}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 46, "Denominator Only": 44, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 4.35, "Numerator Value": null, "Denominator Value": null, "IPP": 46}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 33, "Denominator Only": 31, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 6.06, "Numerator Value": null, "Denominator Value": null, "IPP": 33}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 19, "Denominator Only": 19, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 20, "Denominator Only": 18, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 10, "Numerator Value": null, "Denominator Value": null, "IPP": 20}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 15, "Denominator Only": 14, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 6.67, "Numerator Value": null, "Denominator Value": null, "IPP": 15}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 5, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 5}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Lu (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 42, "Denominator Only": 40, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 4.76, "Numerator Value": null, "Denominator Value": null, "IPP": 42}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "Manchester,  Kurt (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 8, "Denominator Only": 8, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 8}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 5, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 5}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Sigrid (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 34, "Denominator Only": 27, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 20.59, "Numerator Value": null, "Denominator Value": null, "IPP": 34}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 16, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 16}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Julius (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 4, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 25, "Numerator Value": null, "Denominator Value": null, "IPP": 4}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 11, "Denominator": 77, "Denominator Only": 66, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.29, "Numerator Value": null, "Denominator Value": null, "IPP": 77}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 15, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 20, "Numerator Value": null, "Denominator Value": null, "IPP": 15}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 14, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.29, "Numerator Value": null, "Denominator Value": null, "IPP": 14}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 531, "Denominator": 3550, "Denominator Only": 3011, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 14.99, "Numerator Value": null, "Denominator Value": null, "IPP": 3550}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 72, "Denominator": 304, "Denominator Only": 229, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 23.92, "Numerator Value": null, "Denominator Value": null, "IPP": 304}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 36, "Denominator Only": 34, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 5.56, "Numerator Value": null, "Denominator Value": null, "IPP": 36}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 2, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 50, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "Oman,  Ray (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 9, "Denominator": 41, "Denominator Only": 32, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 21.95, "Numerator Value": null, "Denominator Value": null, "IPP": 41}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 28, "Denominator": 114, "Denominator Only": 86, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 24.56, "Numerator Value": null, "Denominator Value": null, "IPP": 114}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Dane (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 28, "Denominator Only": 27, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 3.57, "Numerator Value": null, "Denominator Value": null, "IPP": 28}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 10, "Denominator Only": 9, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 10, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 46, "Denominator Only": 41, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 10.87, "Numerator Value": null, "Denominator Value": null, "IPP": 46}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Carson (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 2, "Denominator": 24, "Denominator Only": 22, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 8.33, "Numerator Value": null, "Denominator Value": null, "IPP": 24}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Marvin (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 16, "Denominator": 91, "Denominator Only": 75, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 17.58, "Numerator Value": null, "Denominator Value": null, "IPP": 91}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "Rhodes,  S<PERSON>ro (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 12, "Denominator Only": 11, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 8.33, "Numerator Value": null, "Denominator Value": null, "IPP": 12}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "Salus<PERSON>-<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 3, "Denominator": 22, "Denominator Only": 19, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 13.64, "Numerator Value": null, "Denominator Value": null, "IPP": 22}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 12, "Denominator Only": 11, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 8.33, "Numerator Value": null, "Denominator Value": null, "IPP": 12}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 5, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 20, "Numerator Value": null, "Denominator Value": null, "IPP": 5}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 19, "Denominator": 105, "Denominator Only": 86, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 18.1, "Numerator Value": null, "Denominator Value": null, "IPP": 105}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 32, "Denominator": 97, "Denominator Only": 65, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 32.99, "Numerator Value": null, "Denominator Value": null, "IPP": 97}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 50, "Denominator Only": 43, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14, "Numerator Value": null, "Denominator Value": null, "IPP": 50}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 5, "Denominator": 22, "Denominator Only": 17, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 22.73, "Numerator Value": null, "Denominator Value": null, "IPP": 22}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 22, "Denominator Only": 21, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 4.55, "Numerator Value": null, "Denominator Value": null, "IPP": 22}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 57, "Denominator": 230, "Denominator Only": 172, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 24.89, "Numerator Value": null, "Denominator Value": null, "IPP": 230}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 4, "Denominator": 34, "Denominator Only": 30, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 11.76, "Numerator Value": null, "Denominator Value": null, "IPP": 34}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 9, "Denominator": 125, "Denominator Only": 116, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 7.2, "Numerator Value": null, "Denominator Value": null, "IPP": 125}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 7, "Denominator": 97, "Denominator Only": 90, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 7.22, "Numerator Value": null, "Denominator Value": null, "IPP": 97}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 39, "Denominator": 125, "Denominator Only": 85, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 31.45, "Numerator Value": null, "Denominator Value": null, "IPP": 125}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 1, "Denominator": 2, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 50, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 34, "Denominator Only": 34, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 34}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 4, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 4}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Attenborough,  Luis (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 135, "Denominator Only": 131, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 135}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 10, "Denominator Only": 10, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Vern (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 59, "Denominator Only": 59, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 59}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 6, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>ure,  Huey (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 49, "Denominator Only": 48, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 49}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Liana (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 61, "Denominator Only": 61, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 61}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Spiro (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 61, "Denominator Only": 58, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 61}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 26, "Denominator Only": 26, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 26}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 7275, "Denominator Only": 7143, "Numerator Exception": 0, "Denominator Exclusion": 132, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 7275}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Madison (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 92, "Denominator Only": 92, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 92}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 24, "Denominator Only": 24, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 24}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 311, "Denominator Only": 297, "Numerator Exception": 0, "Denominator Exclusion": 14, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 311}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 65, "Denominator Only": 63, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 65}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 111, "Denominator Only": 111, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 111}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 12, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 12}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 17, "Denominator Only": 15, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 17}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON>,  Jordan (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 16, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 16}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 12, "Denominator Only": 12, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 12}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 36, "Denominator Only": 33, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 36}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 55, "Denominator Only": 55, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 55}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 13, "Denominator Only": 13, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 13}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 180, "Denominator Only": 173, "Numerator Exception": 0, "Denominator Exclusion": 7, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 180}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 48, "Denominator Only": 46, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 48}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 19, "Denominator Only": 19, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 19}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 199, "Denominator Only": 195, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 199}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 28, "Denominator Only": 28, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 28}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 31, "Denominator Only": 31, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 31}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 16, "Denominator Only": 16, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 16}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 58, "Denominator Only": 52, "Numerator Exception": 0, "Denominator Exclusion": 6, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 58}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 75, "Denominator Only": 72, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 75}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 11, "Denominator Only": 11, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 11}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Joshua (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 9, "Denominator Only": 9, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 9}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 22, "Denominator Only": 22, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 22}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 24, "Denominator Only": 21, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 24}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 73, "Denominator Only": 73, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 73}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 4, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 4}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 129, "Denominator Only": 127, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 129}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON> ,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 68, "Denominator Only": 68, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 68}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 63, "Denominator Only": 63, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 63}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON>  (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 67, "Denominator Only": 67, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 67}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 6, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 31, "Denominator Only": 31, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 31}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 67, "Denominator Only": 67, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 67}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 112, "Denominator Only": 112, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 112}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Alan (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 64, "Denominator Only": 60, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 64}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 232, "Denominator Only": 230, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 232}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 6, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 6}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 130, "Denominator Only": 130, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 130}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 68, "Denominator Only": 62, "Numerator Exception": 0, "Denominator Exclusion": 6, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 68}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 201, "Denominator Only": 201, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 201}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 7, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 7}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Apollo (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 55, "Denominator Only": 55, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 55}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 10, "Denominator Only": 10, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 25, "Denominator Only": 25, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 25}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 13, "Denominator Only": 13, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 13}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 23, "Denominator Only": 23, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 23}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Murray (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 61, "Denominator Only": 61, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 61}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Hesse,  Ned (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 28, "Denominator Only": 28, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 28}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 11, "Denominator Only": 11, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 11}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 57, "Denominator Only": 55, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 57}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 9, "Denominator Only": 9, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 9}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 394, "Denominator Only": 391, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 394}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Jastrow,  Jordan (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 10, "Denominator Only": 10, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 28, "Denominator Only": 28, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 28}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 25, "Denominator Only": 25, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 25}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 83, "Denominator Only": 83, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 83}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Reg (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 22, "Denominator Only": 21, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 22}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 28, "Denominator Only": 28, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 28}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 54, "Denominator Only": 53, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 54}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 123, "Denominator Only": 123, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 123}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 74, "Denominator Only": 72, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 74}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 71, "Denominator Only": 71, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 71}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 46, "Denominator Only": 46, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 46}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 76, "Denominator Only": 76, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 76}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 21, "Denominator Only": 21, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 21}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 128, "Denominator Only": 125, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 128}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 91, "Denominator Only": 91, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 91}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 18, "Denominator Only": 18, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 18}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Lu (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 47, "Denominator Only": 47, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 47}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 10, "Denominator Only": 10, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Manchester,  Kurt (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 9, "Denominator Only": 9, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 9}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 5, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 5}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Sigrid (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 56, "Denominator Only": 56, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 56}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 37, "Denominator Only": 37, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 37}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Julius (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 4, "Denominator Only": 4, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 4}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 190, "Denominator Only": 188, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 190}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 62, "Denominator Only": 62, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 62}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 29, "Denominator Only": 28, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 29}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 7275, "Denominator Only": 7143, "Numerator Exception": 0, "Denominator Exclusion": 132, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 7275}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 326, "Denominator Only": 314, "Numerator Exception": 0, "Denominator Exclusion": 12, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 326}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 58, "Denominator Only": 57, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 58}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Oman,  Ray (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 43, "Denominator Only": 43, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 43}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 10, "Denominator Only": 10, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 93, "Denominator Only": 90, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 93}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 35, "Denominator Only": 35, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 35}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 264, "Denominator Only": 263, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 264}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Dane (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 35, "Denominator Only": 35, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 35}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Phil (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 14, "Denominator Only": 14, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 14}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 10, "Denominator Only": 10, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 51, "Denominator Only": 51, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 51}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Carson (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 32, "Denominator Only": 32, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 32}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  Marvin (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 183, "Denominator Only": 183, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 183}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Rhodes,  S<PERSON>ro (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 30, "Denominator Only": 30, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 30}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 29, "Denominator Only": 29, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 29}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Salus<PERSON>-<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 60, "Denominator Only": 59, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 60}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 13, "Denominator Only": 11, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 13}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 5, "Denominator Only": 5, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 5}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 27, "Denominator Only": 24, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 27}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 40, "Denominator Only": 40, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 40}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 129, "Denominator Only": 126, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 129}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 125, "Denominator Only": 125, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 125}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  Reg (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 3, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 3}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 98, "Denominator Only": 98, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 98}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 27, "Denominator Only": 25, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 27}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 499, "Denominator Only": 489, "Numerator Exception": 0, "Denominator Exclusion": 10, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 499}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 21, "Denominator Only": 21, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 21}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 247, "Denominator Only": 247, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 247}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 204, "Denominator Only": 201, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 204}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 42, "Denominator Only": 40, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 42}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 136, "Denominator Only": 132, "Numerator Exception": 0, "Denominator Exclusion": 4, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 136}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 169, "Denominator Only": 158, "Numerator Exception": 0, "Denominator Exclusion": 11, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 169}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 156, "Denominator Only": 153, "Numerator Exception": 0, "Denominator Exclusion": 3, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 156}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON><PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 32, "Denominator Only": 32, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 32}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 2, "Denominator Only": 2, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 15, "Denominator Only": 15, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 15}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  <PERSON> (Mockingjay Medical Health System)", "Entity Type": "Provider", "Start Date": "2024-01-01T00:00:00", "Interval": "Y", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}], "expected6": [{"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 138, "Denominator": 1284, "Denominator Only": 965, "Numerator Exception": 0, "Denominator Exclusion": 181, "Denominator Exception": 0, "Performance": 12.51, "Numerator Value": null, "Denominator Value": null, "IPP": 1284}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 2, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "<PERSON>,  Alan (Mockingjay Medical Health System)", "Entity Type": "Partner", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 2, "Denominator": 10, "Denominator Only": 6, "Numerator Exception": 0, "Denominator Exclusion": 2, "Denominator Exception": 0, "Performance": 25, "Numerator Value": null, "Denominator Value": null, "IPP": 10}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 138, "Denominator": 1284, "Denominator Only": 965, "Numerator Exception": 0, "Denominator Exclusion": 181, "Denominator Exception": 0, "Performance": 12.51, "Numerator Value": null, "Denominator Value": null, "IPP": 1284}, {"Measure Name": "Cervical Cancer Screening (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 2, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 2}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 45, "Denominator": 319, "Denominator Only": 274, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.11, "Numerator Value": null, "Denominator Value": null, "IPP": 319}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 45, "Denominator": 319, "Denominator Only": 274, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 14.11, "Numerator Value": null, "Denominator Value": null, "IPP": 319}, {"Measure Name": "Childhood Immunization Status (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 1, "Denominator Only": 1, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 1}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 1475, "Denominator": 2455, "Denominator Only": 781, "Numerator Exception": 0, "Denominator Exclusion": 199, "Denominator Exception": 0, "Performance": 65.38, "Numerator Value": null, "Denominator Value": null, "IPP": 2455}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 24, "Denominator": 33, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 6, "Denominator Exception": 0, "Performance": 88.89, "Numerator Value": null, "Denominator Value": null, "IPP": 33}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 1475, "Denominator": 2455, "Denominator Only": 781, "Numerator Exception": 0, "Denominator Exclusion": 199, "Denominator Exception": 0, "Performance": 65.38, "Numerator Value": null, "Denominator Value": null, "IPP": 2455}, {"Measure Name": "Controlling High Blood Pressure (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 24, "Denominator": 33, "Denominator Only": 3, "Numerator Exception": 0, "Denominator Exclusion": 6, "Denominator Exception": 0, "Performance": 88.89, "Numerator Value": null, "Denominator Value": null, "IPP": 33}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 531, "Denominator": 3514, "Denominator Only": 2975, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 15.15, "Numerator Value": null, "Denominator Value": null, "IPP": 3514}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 61, "Denominator Only": 61, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 61}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 531, "Denominator": 3514, "Denominator Only": 2975, "Numerator Exception": 0, "Denominator Exclusion": 8, "Denominator Exception": 0, "Performance": 15.15, "Numerator Value": null, "Denominator Value": null, "IPP": 3514}, {"Measure Name": "Falls: Screening for Future Fall Risk (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 61, "Denominator Only": 61, "Numerator Exception": 0, "Denominator Exclusion": 0, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 61}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 7225, "Denominator Only": 7094, "Numerator Exception": 0, "Denominator Exclusion": 131, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 7225}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "Bluebird's Medical Group", "Entity Type": "Submission Group", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 84, "Denominator Only": 83, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 84}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-01-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 7225, "Denominator Only": 7094, "Numerator Exception": 0, "Denominator Exclusion": 131, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 7225}, {"Measure Name": "Preventive Care and Screening: Screening for Depression and Follow-Up Plan (E)", "Measure ID": "CMS 123", "Entity Description": "MockingJay Ambulatory Roll Up", "Entity Type": "Organization", "Start Date": "2024-04-01T00:00:00", "Interval": "Q", "Numerator": 0, "Denominator": 84, "Denominator Only": 83, "Numerator Exception": 0, "Denominator Exclusion": 1, "Denominator Exception": 0, "Performance": 0, "Numerator Value": null, "Denominator Value": null, "IPP": 84}]}