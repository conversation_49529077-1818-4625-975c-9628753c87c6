import {
  factory,
  closePool,
} from '@/services/snowflake/SnowflakeRepositoryFactory'
import { env } from '@/env'
import {
  createSnowflakeConfiguration,
  createConnection,
  executeAsAResultSet,
  executeAsSingleValue,
} from '@/services/snowflake/SnowflakeHelper'
import { SnowflakeFacility } from '@/types/facility'
import fs from 'fs'
import { fileURLToPath } from 'url'
import path from 'path'

const OrganizationTypeIdForFacility = 3
const OrganizationTypeIdForHospital = 1
const EntityId2 = 8888889
const EntityId = 8888888
const FacilityId1 = 888881
const FacilityId2 = 888882
const EnitityFacilityId = 555555
const EntityFacilityId2 = 555556

// Setup fixture data
beforeAll(async () => {
  const connectionConfiguration = {
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }
  const connection = createConnection(connectionConfiguration)

  const entityCount = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."Entities" where "Id"=${EntityId}`
  )
  if (!entityCount) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."Entities" 
            ("Id", "Code", "Description", "SourceContainerIdentifier", "EntityTypeId", "OrganizationTypeId", "UseForComparativeData") 
            VALUES 
            (${EntityId}, 'FacilityCode1', 'FacilityName1', 'test-org-123', 1, ${OrganizationTypeIdForFacility}, 1)`

    )

    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."Entities" 
            ("Id", "Code", "Description", "SourceContainerIdentifier", "EntityTypeId", "OrganizationTypeId", "UseForComparativeData") 
            VALUES 
            (${EntityId2}, 'FacilityCode2', 'FacilityName2', 'test-org-123', 1, ${OrganizationTypeIdForHospital}, 1)`

    )

  } else {
    console.log('skipping inserts')
  }

  const facility1Count = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM CSRC."Facilities" where "Id"=${FacilityId1}`
  )
  if (!facility1Count) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO CSRC."Facilities" 
            ("Id", "SourceCode", "SourceDescription", "SourceFacilityAddress1", "SourceFacilityAddress2", "SourceFacilityCity", "SourceFacilityState", "SourceFacilityZipPostal", "CCN", "DataSourceId", "EhrInstance", "EhrTypeId") 
            VALUES 
            (${FacilityId1}, 'SourceCode1', 'SourceDescription1', 'Address1', 'Address2', 'City1', 'State1', 'Zip1', 'CCN1', 1, 1, 1)`
    )
  }

  const facility2Count = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM CSRC."Facilities" where "Id"=${FacilityId2}`
  )
  if (!facility2Count) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO CSRC."Facilities" 
            ("Id", "SourceCode", "SourceDescription", "SourceFacilityAddress1", "SourceFacilityAddress2", "SourceFacilityCity", "SourceFacilityState", "SourceFacilityZipPostal", "CCN", "DataSourceId", "EhrInstance", "EhrTypeId") 
            VALUES 
            (${FacilityId2}, 'SourceCode2', 'SourceDescription2', 'Address3', 'Address4', 'City2', 'State2', 'Zip2', 'CCN2', 2, 2, 2)`
    )
  }

  const entityFacilityCount = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."EntityFacilities" where "EntitiesId"=${EntityId} AND "FacilitiesId"=${FacilityId1}`
  )
  if (!entityFacilityCount) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."EntityFacilities" ("Id","EntitiesId", "FacilitiesId") VALUES (${EnitityFacilityId}, ${EntityId}, ${FacilityId1})`
    )
  }

  const entityFacilityCount2 = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."EntityFacilities" where "EntitiesId"=${EntityId2} AND "FacilitiesId"=${FacilityId2}`
  )
  if (!entityFacilityCount2) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."EntityFacilities" ("Id","EntitiesId", "FacilitiesId") VALUES (${EntityFacilityId2}, ${EntityId2}, ${FacilityId1})`
    )
  }


  connection.destroy((err) => { })
})

// Clean up resources after all tests
afterAll(async () => {
  const connectionConfiguration = {
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }
  const connection = createConnection(connectionConfiguration)

  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."Entities" Where "Id" = ${EntityId} `
  )
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."Entities" Where "Id" = ${EntityId2} `
  )
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM CSRC."Facilities" Where "Id" IN (${FacilityId1}, ${FacilityId2}) `
  )
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."EntityFacilities" Where "Id" = ${EnitityFacilityId} `
  )
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."EntityFacilities" Where "Id" = ${EntityFacilityId2} `
  )

  connection.destroy((err) => { })
  await closePool()
})

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const expectedResults = JSON.parse(
  fs.readFileSync(`${__dirname}/expected-facility-results.json`, 'utf8')
)

test('should find facilities for an organization', async () => {
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }).createFacilityRepository()
  if (env.SNOWFLAKE_ACCOUNT) {
    const results = await repo.findFacilitiesByOrganization('test-org-123')
    expect(results).toBeDefined()
    expect(results.length).toBe(1)
    const expected = expectedResults[0]
    const actual = results[0] as SnowflakeFacility
    expect(actual.EntitiesId).toBe(expected.EntitiesId)
    expect(actual.EntityName).toBe(expected.EntityName)
    expect(actual.EntityCode).toBe(expected.EntityCode)
    expect(actual.FacilityID).toBe(expected.FacilityID)
    expect(actual.SourceContainerIdentifier).toBe(
      expected.SourceContainerIdentifier
    )
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})
