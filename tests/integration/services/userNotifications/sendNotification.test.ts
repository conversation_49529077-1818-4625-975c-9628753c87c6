import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import UserNotificationsService from '@/services/userNotifications'
import { UserNotification } from '@/types/userNotification'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

let notificationsTableStorage: AzureTableStorageWrapper
const testSenderId = `test-sender-${dayjs.utc().valueOf()}`
const testReceiverIds = [
  `test-receiver1-${dayjs.utc().valueOf()}`,
  `test-receiver2-${dayjs.utc().valueOf()}`,
  `test-receiver3-${dayjs.utc().valueOf()}`,
]
const SECOND = 1000

describe('UserNotificationsService - sendNotification', () => {
  beforeEach(async () => {
    notificationsTableStorage = new AzureTableStorageWrapper(
      `${StorageTables.UserNotifications}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await notificationsTableStorage.createTable()
  })

  afterEach(async () => {
    await notificationsTableStorage.deleteTable()
  })

  test('should send notification to multiple users concurrently', async () => {
    // Arrange
    const service = new UserNotificationsService()
    const testMessage = 'Test notification message'

    // Act
    const result = await service.sendNotification(
      notificationsTableStorage,
      testSenderId,
      testMessage,
      testReceiverIds
    )

    // Assert
    expect(result.success).toBe(true)
    expect(result.message).toBe('Notifications have been sent successfully')

    // Verify notifications were created for each receiver
    for (const receiverId of testReceiverIds) {
      const notifications =
        await notificationsTableStorage.queryEntities<UserNotification>(
          `PartitionKey eq '${receiverId}'`
        )

      expect(notifications).toHaveLength(1)
      expect(notifications[0]).toMatchObject({
        partitionKey: receiverId,
        ownerId: testSenderId,
        message: testMessage,
      })
      expect(dayjs.utc(notifications[0]?.sentDateTime).isValid()).toBeTruthy()
    }
  })

  test('should use same notification ID for all recipients', async () => {
    // Arrange
    const service = new UserNotificationsService()
    const testMessage = 'Test notification message'

    // Act
    await service.sendNotification(
      notificationsTableStorage,
      testSenderId,
      testMessage,
      testReceiverIds
    )

    // Assert
    const allNotifications = await Promise.all(
      testReceiverIds.map((receiverId) =>
        notificationsTableStorage.queryEntities<UserNotification>(
          `PartitionKey eq '${receiverId}'`
        )
      )
    )

    // All notifications should have the same rowKey (notification ID)
    const notificationIds = allNotifications.map(
      (notifications) => notifications[0]?.rowKey
    )
    expect(new Set(notificationIds).size).toBe(1)
  })

  test('should handle empty receivers list', async () => {
    // Arrange
    const service = new UserNotificationsService()
    const testMessage = 'Test notification message'

    // Act
    const result = await service.sendNotification(
      notificationsTableStorage,
      testSenderId,
      testMessage,
      []
    )

    // Assert
    expect(result.success).toBe(true)
    const notifications =
      await notificationsTableStorage.queryEntities<UserNotification>(
        `PartitionKey eq '${testSenderId}'`
      )
    expect(notifications).toHaveLength(0)
  })

  test('should handle large number of receivers', async () => {
    // Arrange
    const service = new UserNotificationsService()
    const testMessage = 'Test notification message'
    const largeReceiverList = Array.from(
      { length: 100 },
      (_, i) => `test-receiver-${i}-${dayjs.utc().valueOf()}`
    )

    // Act
    const result = await service.sendNotification(
      notificationsTableStorage,
      testSenderId,
      testMessage,
      largeReceiverList
    )

    // Assert
    expect(result.success).toBe(true)
    const allNotifications = await Promise.all(
      largeReceiverList.map((receiverId) =>
        notificationsTableStorage.queryEntities<UserNotification>(
          `PartitionKey eq '${receiverId}'`
        )
      )
    )
    expect(allNotifications.flat()).toHaveLength(largeReceiverList.length)
  }, 120  *SECOND)

  test('should handle special characters in message', async () => {
    // Arrange
    const service = new UserNotificationsService()
    const testMessage = 'Test 🚀 message with special chars: @#$%^&*()'

    // Act
    const result = await service.sendNotification(
      notificationsTableStorage,
      testSenderId,
      testMessage,
      testReceiverIds
    )

    // Assert
    expect(result.success).toBe(true)
    const notifications =
      await notificationsTableStorage.queryEntities<UserNotification>(
        `PartitionKey eq '${testReceiverIds[0]}'`
      )
    expect(notifications[0]?.message).toBe(testMessage)
  })
})
