import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { getUserNotifications } from '@/services/userNotifications/getUserNotifications'
import { StorageTables } from '@/enums/storageTables'
import { UserNotification } from '@/types/userNotification'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'
import CitCUsersService from '@/services/citc/citCUsers'
import { SelectionType } from '@/enums/selectionType'

dayjs.extend(utc)

let notificationsTableStorage: AzureTableStorageWrapper
const testUserId = `test-user-${dayjs.utc().valueOf()}`
const SECONDS = 1000

describe('getUserNotifications', () => {
  beforeEach(async () => {
    notificationsTableStorage = new AzureTableStorageWrapper(
      `${StorageTables.UserNotifications}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await notificationsTableStorage.createTable()
  }, 70 * SECONDS)

  afterEach(async () => {
    await notificationsTableStorage.deleteTable()
  })

  test(
    'should return user notifications with correct limit',
    async () => {
      // Arrange
      const notifications: UserNotification[] = Array.from(
        { length: 10 },
        (_, i) => ({
          partitionKey: testUserId,
          rowKey: `notification-${i}`,
          message: `Test message ${i}`,
          ownerId: 'test-owner',
          sentDateTime: dayjs.utc().toDate(),
          readDateTime: null,
        })
      )

      await Promise.all(
        notifications.map((notification) =>
          notificationsTableStorage.insertEntity<UserNotification>(notification)
        )
      )

      // Act
      const limit = 5
      const result = await getUserNotifications(
        notificationsTableStorage,
        testUserId,
        limit
      )

      // Assert
      expect(result).toHaveLength(limit)
      result.forEach((notification) => {
        expect(notification.partitionKey).toBe(testUserId)
        expect(notification.message).toMatch(/Test message \d/)
      })
    },
    70 * SECONDS
  )

  test('should return empty array when no notifications exist', async () => {
    // Act
    const result = await getUserNotifications(
      notificationsTableStorage,
      testUserId,
      10
    )

    // Assert
    expect(result).toEqual([])
  })

  test('should return all notifications when limit is greater than total count', async () => {
    // Arrange
    const notifications: UserNotification[] = Array.from(
      { length: 3 },
      (_, i) => ({
        partitionKey: testUserId,
        rowKey: `notification-${i}`,
        message: `Test message ${i}`,
        ownerId: 'test-owner',
        sentDateTime: dayjs.utc().toDate(),
        readDateTime: null,
      })
    )

    await Promise.all(
      notifications.map((notification) =>
        notificationsTableStorage.insertEntity<UserNotification>(notification)
      )
    )

    // Act
    const result = await getUserNotifications(
      notificationsTableStorage,
      testUserId,
      10
    )

    // Assert
    expect(result).toHaveLength(3)
  })

  test('should only return notifications for specified user', async () => {
    // Arrange
    const otherUserId = `other-user-${dayjs.utc().valueOf()}`

    const userNotifications: UserNotification[] = Array.from(
      { length: 3 },
      (_, i) => ({
        partitionKey: testUserId,
        rowKey: `notification-user-${i}`,
        message: `User message ${i}`,
        ownerId: 'test-owner',
        sentDateTime: dayjs.utc().toDate(),
        readDateTime: null,
      })
    )

    const otherUserNotifications: UserNotification[] = Array.from(
      { length: 3 },
      (_, i) => ({
        partitionKey: otherUserId,
        rowKey: `notification-other-${i}`,
        message: `Other message ${i}`,
        ownerId: 'test-owner',
        sentDateTime: dayjs.utc().toDate(),
        readDateTime: null,
      })
    )

    await Promise.all([
      ...userNotifications.map((notification) =>
        notificationsTableStorage.insertEntity<UserNotification>(notification)
      ),
      ...otherUserNotifications.map((notification) =>
        notificationsTableStorage.insertEntity<UserNotification>(notification)
      ),
    ])

    // Act
    const result = await getUserNotifications(
      notificationsTableStorage,
      testUserId,
      10
    )

    // Assert
    expect(result).toHaveLength(3)
    result.forEach((notification) => {
      expect(notification.partitionKey).toBe(testUserId)
      expect(notification.message).toMatch(/User message \d/)
    })
  })

  test('should handle special characters in messages', async () => {
    // Arrange
    const specialMessage = 'Test 🚀 message with special chars: @#$%^&*()'
    const notification: UserNotification = {
      partitionKey: testUserId,
      rowKey: 'special-notification',
      message: specialMessage,
      ownerId: 'test-owner',
      sentDateTime: dayjs.utc().toDate(),
      readDateTime: null,
    }

    await notificationsTableStorage.insertEntity<UserNotification>(notification)

    // Act
    const result = await getUserNotifications(
      notificationsTableStorage,
      testUserId,
      1
    )

    // Assert
    expect(result).toHaveLength(1)
    expect(result[0]?.message).toBe(specialMessage)
  })

  describe('with migrationConfig', () => {
    const testOrgId = 'test-org-id'

    test('should migrate and return user notifications when migrationConfig is provided', async () => {
      // Mock SQL results with the correct structure
      const sqlNotifications = [
        {
          UserId: testUserId,
          NotificationId: '1',
          UserNotifications: {
            Id: 1,
            Message: 'SQL Notification 1',
            OwnerId: 'test-owner',
            SentDateTime: dayjs.utc().toDate(),
            ReadDateTime: null,
          },
        },
        {
          UserId: testUserId,
          NotificationId: '2',
          UserNotifications: {
            Id: 2,
            Message: 'SQL Notification 2',
            OwnerId: 'test-owner',
            SentDateTime: dayjs.utc().toDate(),
            ReadDateTime: null,
          },
        },
      ]

      // Mock org users
      const mockOrgUsers = [
        {
          userId: 'test-owner',
          // add other required user properties
        },
      ]

      // Mock CitCUsersService
      jest
        .spyOn(CitCUsersService.prototype, 'GetAllUsersByOrganizationAsync')
        .mockResolvedValue(mockOrgUsers)

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            userNotificationMap: {
              findMany: jest.fn().mockResolvedValue(sqlNotifications as never),
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
        accessToken: 'mock-token', // Add this as it's required for CitCUsersService
      }

      // Act
      const results = await getUserNotifications(
        notificationsTableStorage,
        testUserId,
        10,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(2)
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            partitionKey: testUserId,
            rowKey: '1',
            message: 'SQL Notification 1',
            ownerId: 'test-owner',
          }),
          expect.objectContaining({
            partitionKey: testUserId,
            rowKey: '2',
            message: 'SQL Notification 2',
            ownerId: 'test-owner',
          }),
        ])
      )

      // Verify notifications were migrated to table storage
      const storedNotifications =
        await notificationsTableStorage.queryEntities<UserNotification>(
          `PartitionKey eq '${testUserId}'`
        )
      expect(storedNotifications).toHaveLength(2)
    })

    test('should handle empty SQL results during migration', async () => {
      // Mock empty SQL results
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            userNotifications: {
              findMany: jest.fn().mockResolvedValue([] as never),
            },
            userNotificationMap: {
              findMany: jest.fn().mockResolvedValue([] as never),
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getUserNotifications(
        notificationsTableStorage,
        testUserId,
        10,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should handle missing prisma client during migration', async () => {
      // Clear mock prisma client
      globalThis.prismaClients = {}

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getUserNotifications(
        notificationsTableStorage,
        testUserId,
        10,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should migrate when migrationConfig exists and no notifications exist in table storage', async () => {
      // Mock SQL results
      const sqlNotifications = [
        {
          UserId: testUserId,
          NotificationId: '1',
          UserNotifications: {
            Id: 1,
            Message: 'SQL Notification 1',
            OwnerId: 'test-owner',
            SentDateTime: dayjs.utc().toDate(),
            ReadDateTime: null,
          },
        },
      ]

      // Mock org users
      const mockOrgUsers = [
        {
          userId: 'test-owner',
        },
      ]

      // Mock CitCUsersService
      jest
        .spyOn(CitCUsersService.prototype, 'GetAllUsersByOrganizationAsync')
        .mockResolvedValue(mockOrgUsers)

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            userNotificationMap: {
              findMany: jest.fn().mockResolvedValue(sqlNotifications as never),
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
        accessToken: 'mock-token',
      }

      // Verify no notifications exist initially
      const initialNotifications =
        await notificationsTableStorage.queryEntities(
          `PartitionKey eq '${testUserId}'`
        )
      expect(initialNotifications).toHaveLength(0)

      // Act
      const results = await getUserNotifications(
        notificationsTableStorage,
        testUserId,
        10,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: '1',
          message: 'SQL Notification 1',
          ownerId: 'test-owner',
        })
      )

      // Verify notifications were migrated to table storage
      const storedNotifications = await notificationsTableStorage.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedNotifications).toHaveLength(1)
    })

    test('should not migrate when migrationConfig exists but notifications already exist in table storage', async () => {
      // First create some existing notifications
      const existingNotification = {
        partitionKey: testUserId,
        rowKey: 'existing-1',
        message: 'Existing Notification',
        ownerId: 'test-owner',
        sentDateTime: dayjs.utc().toDate(),
        readDateTime: null,
      }
      await notificationsTableStorage.insertEntity(existingNotification)

      // Mock SQL results that shouldn't be migrated
      const sqlNotifications = [
        {
          UserId: testUserId,
          NotificationId: 'sql-1',
          UserNotifications: {
            Id: 1,
            Message: 'SQL Notification',
            OwnerId: 'test-owner',
            SentDateTime: dayjs.utc().toDate(),
            ReadDateTime: null,
          },
        },
      ]

      // Mock prisma client
      const mockFindMany = jest
        .fn()
        .mockResolvedValue(sqlNotifications as never)
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            userNotificationMap: {
              findMany: mockFindMany,
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
        accessToken: 'mock-token',
      }

      // Act
      const results = await getUserNotifications(
        notificationsTableStorage,
        testUserId,
        10,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(1)
      expect(results[0]).toEqual(
        expect.objectContaining({
          partitionKey: testUserId,
          rowKey: 'existing-1',
          message: 'Existing Notification',
        })
      )

      // Verify the SQL notification was not migrated
      expect(mockFindMany).not.toHaveBeenCalled()

      // Verify only the existing notification is in storage
      const storedNotifications = await notificationsTableStorage.queryEntities(
        `PartitionKey eq '${testUserId}'`
      )
      expect(storedNotifications).toHaveLength(1)
      expect(storedNotifications[0]).toEqual(
        expect.objectContaining({
          rowKey: 'existing-1',
          message: 'Existing Notification',
        })
      )
    })
  })
})
