import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { Dashboard } from '@/types/dashboard'
import { MigrationConfig } from '@/types/migrationConfig'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'
import { SelectionType } from '@/enums/selectionType'

dayjs.extend(utc)

let dashboardTableStorage: AzureTableStorageWrapper
beforeEach(async () => {
  dashboardTableStorage = new AzureTableStorageWrapper(
    `${StorageTables.Dashboards}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
  )
  await dashboardTableStorage.createTable()
})

afterEach(async () => {
  await dashboardTableStorage.deleteTable()
})

test('should return all the dashboards', async () => {
  const { getAllDashboards } = await import(
    '@/services/dashboards/getAllDashboards'
  )
  const orgId = 'a7aaaa0b-ee26-4243-a05a-3e1a8d804cb5'
  const addedDashboard = await dashboardTableStorage.insertEntity({
    name: 'name',
    description: 'description',
    configurations: 'configurations',
    partitionKey: orgId,
    rowKey: Date.now().toString(),
  } as Dashboard)

  const results = await getAllDashboards(dashboardTableStorage, orgId)

  expect(results.length).toBe(1)

  const result = results[0]!
  expect(result.name).toBe(addedDashboard.name)
  expect(result.description).toBe(addedDashboard.description)
  expect(result.configurations).toBe(addedDashboard.configurations)
  expect(result.partitionKey).toBe(addedDashboard.partitionKey)
  expect(result.rowKey).toBe(addedDashboard.rowKey)
})

describe('with migrationConfig', () => {
  const testOrgId = 'test-org-id'

  test('should migrate and return dashboards when migrationConfig is provided', async () => {
    // Mock SQL results
    const sqlDashboards = [
      {
        Id: 1,
        DashboardId: '1', // Add DashboardId
        OrganizationId: testOrgId,
        Name: 'SQL Dashboard 1',
        Description: 'Description 1',
      },
      {
        Id: 2,
        DashboardId: '2', // Add DashboardId
        OrganizationId: testOrgId,
        Name: 'SQL Dashboard 2',
        Description: 'Description 2',
      },
    ]

    // Mock prisma client
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          dashboards: {
            findMany: jest.fn().mockResolvedValue(sqlDashboards as never),
          },
        },
      },
    } as any

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    const { getAllDashboards } = await import(
      '@/services/dashboards/getAllDashboards'
    )

    // Act
    const results = await getAllDashboards(
      dashboardTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: '1',
          name: 'SQL Dashboard 1',
          description: 'Description 1',
        }),
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: '2',
          name: 'SQL Dashboard 2',
          description: 'Description 2',
        }),
      ])
    )

    // Verify dashboards were migrated to table storage
    const storedDashboards =
      await dashboardTableStorage.queryEntities<Dashboard>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(storedDashboards).toHaveLength(2)
  })

  test('should handle empty SQL results during migration', async () => {
    // Mock empty SQL results
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          dashboards: {
            findMany: jest.fn().mockResolvedValue([] as never),
          },
        },
      },
    } as any

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    const { getAllDashboards } = await import(
      '@/services/dashboards/getAllDashboards'
    )

    // Act
    const results = await getAllDashboards(
      dashboardTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toEqual([])
  })

  test('should handle missing prisma client during migration', async () => {
    // Clear mock prisma client
    globalThis.prismaClients = {}

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    const { getAllDashboards } = await import(
      '@/services/dashboards/getAllDashboards'
    )

    // Act
    const results = await getAllDashboards(
      dashboardTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toEqual([])
  })

  test('should migrate when migrationConfig exists and no dashboards exist in table storage', async () => {
    // Mock SQL results
    const sqlDashboards = [
      {
        Id: 1,
        DashboardId: '1',
        OrganizationId: testOrgId,
        Name: 'SQL Dashboard 1',
        Description: 'Description 1',
      },
    ]

    // Mock prisma client
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          dashboards: {
            findMany: jest.fn().mockResolvedValue(sqlDashboards as never),
          },
        },
      },
    } as any

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Verify no dashboards exist initially
    const initialDashboards = await dashboardTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(initialDashboards).toHaveLength(0)

    const { getAllDashboards } = await import(
      '@/services/dashboards/getAllDashboards'
    )

    // Act
    const results = await getAllDashboards(
      dashboardTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        rowKey: '1',
        name: 'SQL Dashboard 1',
        description: 'Description 1',
      })
    )

    // Verify dashboards were migrated to table storage
    const storedDashboards = await dashboardTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedDashboards).toHaveLength(1)
  })

  test('should not migrate when migrationConfig exists but dashboards already exist in table storage', async () => {
    // First create some existing dashboards
    const existingDashboard = {
      partitionKey: testOrgId,
      rowKey: 'existing-1',
      name: 'Existing Dashboard',
      description: 'Existing Description',
    }
    await dashboardTableStorage.insertEntity(existingDashboard)

    // Mock SQL results that shouldn't be migrated
    const sqlDashboards = [
      {
        Id: 1,
        DashboardId: 'sql-1',
        OrganizationId: testOrgId,
        Name: 'SQL Dashboard',
        Description: 'SQL Description',
      },
    ]

    // Mock prisma client
    const mockFindMany = jest.fn().mockResolvedValue(sqlDashboards as never)
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          dashboards: {
            findMany: mockFindMany,
          },
        },
      },
    } as any

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    const { getAllDashboards } = await import(
      '@/services/dashboards/getAllDashboards'
    )

    // Act
    const results = await getAllDashboards(
      dashboardTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        rowKey: 'existing-1',
        name: 'Existing Dashboard',
        description: 'Existing Description',
      })
    )

    // Verify no additional dashboards were migrated
    const storedDashboards = await dashboardTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedDashboards).toHaveLength(1)
    expect(mockFindMany).not.toHaveBeenCalled()
  })
})
