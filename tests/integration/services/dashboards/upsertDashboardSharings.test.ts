import { jest } from '@jest/globals'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardDetails } from '@/types/dashboard'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { StorageTables } from '@/enums/storageTables'

dayjs.extend(utc)

jest.unstable_mockModule('posthog-js/lib/src/uuidv7', () => ({
  uuidv7: () => dayjs().valueOf().toString(),
}))

describe('upsertDashboardSharings', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  const testUserId = `test-user-${dayjs.utc().valueOf()}`
  const testDashboardId = `test-dashboard-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.DashboardDetails}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert new dashboard sharing when it does not exist', async () => {
    // Arrange
    const { upsertDashboardSharings } = await import('@/services/dashboards/upsertDashboardSharings')
    const testDashboardDetails: DashboardDetails = {
      partitionKey: testOrgId,
      rowKey: testDashboardId,
      dashboardRowKey: testDashboardId,
      userId: testUserId,
      organizationId: testOrgId,
      isDefault: false,
      isShared: true,
      isFavorite: false
    }

    // Act
    await upsertDashboardSharings(tableStorageWrapper, testDashboardDetails)

    // Assert
    const sharings = await tableStorageWrapper.queryEntities<DashboardDetails>(
      `PartitionKey eq '${testOrgId}' and RowKey eq '${testDashboardId}'`
    )
    expect(sharings.length).toBe(1)
    expect(sharings[0]).toMatchObject({
      partitionKey: testOrgId,
      rowKey: testDashboardId,
      dashboardRowKey: testDashboardId,
      userId: testUserId,
      organizationId: testOrgId,
      isDefault: false,
      isShared: true,
      isFavorite: false
    })
  })

  test('should update existing dashboard sharing when it exists', async () => {
    // Arrange
    const { upsertDashboardSharings } = await import('@/services/dashboards/upsertDashboardSharings')
    const initialSharing: DashboardDetails = {
      partitionKey: testOrgId,
      rowKey: testDashboardId,
      dashboardRowKey: testDashboardId,
      userId: testUserId,
      organizationId: testOrgId,
      isDefault: false,
      isShared: false,
      isFavorite: false
    }

    await tableStorageWrapper.insertEntity<DashboardDetails>(initialSharing)

    const updatedSharing: DashboardDetails = {
      ...initialSharing,
      isShared: true,
      isFavorite: true
    }

    // Act
    await upsertDashboardSharings(tableStorageWrapper, updatedSharing)

    // Assert
    const sharings = await tableStorageWrapper.queryEntities<DashboardDetails>(
      `PartitionKey eq '${testOrgId}' and RowKey eq '${testDashboardId}'`
    )
    expect(sharings.length).toBe(1)
    expect(sharings[0]).toMatchObject({
      partitionKey: testOrgId,
      rowKey: testDashboardId,
      dashboardRowKey: testDashboardId,
      userId: testUserId,
      organizationId: testOrgId,
      isDefault: false,
      isShared: true,
      isFavorite: true
    })
  })

  test('should handle multiple dashboard sharings for different users', async () => {
    // Arrange
    const { upsertDashboardSharings } = await import('@/services/dashboards/upsertDashboardSharings')
    const testUserId2 = `test-user-2-${dayjs.utc().valueOf()}`
    const testDashboardId2 = `test-dashboard-2-${dayjs.utc().valueOf()}`

    const sharing1: DashboardDetails = {
      partitionKey: testOrgId,
      rowKey: testDashboardId,
      dashboardRowKey: testDashboardId,
      userId: testUserId,
      organizationId: testOrgId,
      isDefault: false,
      isShared: true,
      isFavorite: false
    }

    const sharing2: DashboardDetails = {
      partitionKey: testOrgId,
      rowKey: testDashboardId2,
      dashboardRowKey: testDashboardId2,
      userId: testUserId2,
      organizationId: testOrgId,
      isDefault: false,
      isShared: true,
      isFavorite: false
    }

    // Act
    await upsertDashboardSharings(tableStorageWrapper, sharing1)
    await upsertDashboardSharings(tableStorageWrapper, sharing2)

    // Assert
    const sharings = await tableStorageWrapper.queryEntities<DashboardDetails>(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(sharings.length).toBe(2)
    expect(sharings).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ userId: testUserId }),
        expect.objectContaining({ userId: testUserId2 })
      ])
    )
  })
})