import { jest } from "@jest/globals";
import { AzureTableStorageWrapper } from "@/services/azure/tableStorageWrapper"
import dayjs from "dayjs";
import {StorageTables} from "@/enums/storageTables";

jest.unstable_mockModule('posthog-js/lib/src/uuidv7', () => ({
    "uuidv7" : () => dayjs().valueOf().toString()
}))

let tableStorageWrapper: AzureTableStorageWrapper
beforeEach(async() => {
    tableStorageWrapper = new AzureTableStorageWrapper('DashboardDetailsIntegrationTest-' + Date.now() as StorageTables)
    await tableStorageWrapper.createTable()
})

afterEach(async() => {
    await tableStorageWrapper.deleteTable()
})

test('should share a Dashboard', async () => {
    const { shareDashboard } = await import("@/services/dashboards/shareDashboard")
    let result = await shareDashboard(
        tableStorageWrapper,
        "dashboard-row-key",
        "user-to-share-with",
        "org-id"
    )
    expect(result.dashboardRowKey).toBe("dashboard-row-key")
    expect(result.userId).toBe("user-to-share-with")
    expect(result.organizationId).toBe("org-id")
    expect(result.isDefault).toBe(false)
    expect(result.isShared).toBe(true)
    expect(result.isFavorite).toBe(false)
    expect(result.partitionKey).toBe("org-id")
    expect(result.rowKey).not.toBeNull()
})
