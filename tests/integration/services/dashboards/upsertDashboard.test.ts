import { jest } from '@jest/globals'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { Dashboard } from '@/types/dashboard'
import dayjs from 'dayjs'
import { StorageTables } from '@/enums/storageTables'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

// Mock UUID generation to ensure consistent test behavior
jest.unstable_mockModule('posthog-js/lib/src/uuidv7', () => ({
  uuidv7: () => dayjs.utc().valueOf().toString(),
}))

let tableStorageWrapper: AzureTableStorageWrapper
const testOrgId = `test-org-${dayjs.utc().valueOf()}`

beforeEach(async () => {
  tableStorageWrapper = new AzureTableStorageWrapper(
    `${StorageTables.Dashboards}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
  )
  await tableStorageWrapper.createTable()
})

afterEach(async () => {
  await tableStorageWrapper.deleteTable()
})

describe('upsertDashboard', () => {
  test('should insert new dashboard when it does not exist', async () => {
    // Arrange
    const { upsertDashboard } = await import(
      '@/services/dashboards/upsertDashboard'
    )
    const testDashboard: Dashboard = {
      partitionKey: testOrgId,
      rowKey: `dashboard-${dayjs.utc().valueOf()}`,
      name: 'Test Dashboard',
      description: 'Test Description',
      configurations: JSON.stringify({ key: 'value' }),
    }

    // Act
    await upsertDashboard(tableStorageWrapper, testDashboard)

    // Assert
    const dashboards = await tableStorageWrapper.queryEntities<Dashboard>(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(dashboards.length).toBe(1)
    expect(dashboards[0]).toMatchObject({
      partitionKey: testOrgId,
      name: 'Test Dashboard',
      description: 'Test Description',
      configurations: JSON.stringify({ key: 'value' }),
    })
  })

  test('should update existing dashboard when it exists', async () => {
    // Arrange
    const { upsertDashboard } = await import(
      '@/services/dashboards/upsertDashboard'
    )
    const dashboardId = `dashboard-${dayjs.utc().valueOf()}`
    const initialDashboard: Dashboard = {
      partitionKey: testOrgId,
      rowKey: dashboardId,
      name: 'Initial Dashboard',
      description: 'Initial Description',
      configurations: JSON.stringify({ initial: 'config' }),
    }

    // Insert initial dashboard
    await tableStorageWrapper.insertEntity<Dashboard>(initialDashboard)

    // Create updated dashboard
    const updatedDashboard: Dashboard = {
      ...initialDashboard,
      name: 'Updated Dashboard',
      description: 'Updated Description',
      configurations: JSON.stringify({ updated: 'config' }),
    }

    // Act
    await upsertDashboard(tableStorageWrapper, updatedDashboard)

    // Assert
    const dashboards = await tableStorageWrapper.queryEntities<Dashboard>(
      `PartitionKey eq '${testOrgId}' and RowKey eq '${dashboardId}'`
    )
    expect(dashboards.length).toBe(1)
    expect(dashboards[0]).toMatchObject({
      partitionKey: testOrgId,
      rowKey: dashboardId,
      name: 'Updated Dashboard',
      description: 'Updated Description',
      configurations: JSON.stringify({ updated: 'config' }),
    })
  })

  test('should handle dashboard with minimal required fields', async () => {
    // Arrange
    const { upsertDashboard } = await import(
      '@/services/dashboards/upsertDashboard'
    )
    const minimalDashboard: Dashboard = {
      partitionKey: testOrgId,
      rowKey: `dashboard-${dayjs.utc().valueOf()}`,
      name: 'Minimal Dashboard',
      description: '',
      configurations: '{}',
    }

    // Act
    await upsertDashboard(tableStorageWrapper, minimalDashboard)

    // Assert
    const dashboards = await tableStorageWrapper.queryEntities<Dashboard>(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(dashboards.length).toBe(1)
    expect(dashboards[0]).toMatchObject({
      partitionKey: testOrgId,
      name: 'Minimal Dashboard',
      description: '',
      configurations: '{}',
    })
  })
})
