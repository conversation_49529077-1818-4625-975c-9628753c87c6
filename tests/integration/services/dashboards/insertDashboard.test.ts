import { jest } from '@jest/globals'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import dayjs from 'dayjs'
import { StorageTables } from '@/enums/storageTables'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

jest.unstable_mockModule('posthog-js/lib/src/uuidv7', () => ({
  uuidv7: () => dayjs().valueOf().toString(),
}))

let tableStorageWrapper: AzureTableStorageWrapper
beforeEach(async () => {
  tableStorageWrapper = new AzureTableStorageWrapper(
    `${StorageTables.Dashboards}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
  )
  await tableStorageWrapper.createTable()
})

afterEach(async () => {
  await tableStorageWrapper.deleteTable()
})

test('should insert a Dashboard', async () => {
  const { insertDashboard } = await import(
    '@/services/dashboards/insertDashboard'
  )
  const orgId = 'org-id'
  const result = await insertDashboard(
    tableStorageWrapper,
    'test-dashboard',
    'test-description',
    'test-configuration',
    orgId
  )
  expect(result.name).toBe('test-dashboard')
  expect(result.description).toBe('test-description')
  expect(result.configurations).toBe('test-configuration')
  expect(result.rowKey).not.toBeNull()
  expect(result.rowKey).not.toBeNull()
})
