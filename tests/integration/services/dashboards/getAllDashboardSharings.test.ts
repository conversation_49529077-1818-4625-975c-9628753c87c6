import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardDetails } from '@/types/dashboard'
import { MigrationConfig } from '@/types/migrationConfig'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'
import { SelectionType } from '@/enums/selectionType'

dayjs.extend(utc)

let dashboardDetailsTableStorage: AzureTableStorageWrapper
beforeEach(async () => {
  dashboardDetailsTableStorage = new AzureTableStorageWrapper(
    `${StorageTables.DashboardDetails}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
  )
  await dashboardDetailsTableStorage.createTable()
})

afterEach(async () => {
  await dashboardDetailsTableStorage.deleteTable()
})

test('should return all the dashboard sharings', async () => {
  const { getAllDashboardSharings } = await import(
    '@/services/dashboards/getAllDashboardSharings'
  )
  const orgId = 'a7aaaa0b-ee26-4243-a05a-3e1a8d804cb5'

  const detail1 = await dashboardDetailsTableStorage.insertEntity({
    dashboardRowKey: 'dashboard-row-key',
    userId: 'test-user-1',
    organizationId: 'test-organization',
    isDefault: false,
    isShared: true,
    isFavorite: false,
    partitionKey: orgId,
    rowKey: 'rowkey2' + Date.now(),
  } as DashboardDetails)

  const results = await getAllDashboardSharings(
    dashboardDetailsTableStorage,
    orgId
  )

  expect(results.length).toBe(1)

  const result = results[0]!
  expect(result.dashboardRowKey).toBe(detail1.dashboardRowKey)
  expect(result.userId).toBe(detail1.userId)
  expect(result.organizationId).toBe(detail1.organizationId)
  expect(result.isDefault).toBe(detail1.isDefault)
  expect(result.isShared).toBe(detail1.isShared)
  expect(result.isFavorite).toBe(detail1.isFavorite)
  expect(result.rowKey).not.toBeNull()
})

describe('with migrationConfig', () => {
  const testOrgId = 'test-org-id'

  test('should migrate and return dashboard sharings when migrationConfig is provided', async () => {
    // Mock SQL results
    const sqlSharings = [
      {
        Id: 1,
        DashboardId: 'dashboard-1',
        UserId: 'user-1',
        OrganizationId: testOrgId,
        IsDefault: false,
        IsShared: true,
        IsFavorite: false,
      },
      {
        Id: 2,
        DashboardId: 'dashboard-2',
        UserId: 'user-2',
        OrganizationId: testOrgId,
        IsDefault: true,
        IsShared: true,
        IsFavorite: true,
      },
    ]

    // Mock prisma client
    globalThis.prismaClients = {
      [testOrgId]: {
        admClient: {
          // Change from hubClient to admClient
          dashboardDetails: {
            findMany: jest.fn().mockResolvedValue(sqlSharings as never),
          },
        },
      },
    } as any

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    const { getAllDashboardSharings } = await import(
      '@/services/dashboards/getAllDashboardSharings'
    )

    // Act
    const results = await getAllDashboardSharings(
      dashboardDetailsTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: 'dashboard-1',
          dashboardRowKey: 'dashboard-1',
          userId: 'user-1',
          organizationId: testOrgId,
          isDefault: false,
          isShared: true,
          isFavorite: false,
        }),
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: 'dashboard-2',
          dashboardRowKey: 'dashboard-2',
          userId: 'user-2',
          organizationId: testOrgId,
          isDefault: true,
          isShared: true,
          isFavorite: true,
        }),
      ])
    )

    // Verify sharings were migrated to table storage
    const storedSharings =
      await dashboardDetailsTableStorage.queryEntities<DashboardDetails>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(storedSharings).toHaveLength(2)
  })

  test('should handle empty SQL results during migration', async () => {
    // Mock empty SQL results
    globalThis.prismaClients = {
      [testOrgId]: {
        admClient: {
          // Change from hubClient to admClient
          dashboardDetails: {
            findMany: jest.fn().mockResolvedValue([] as never),
          },
        },
      },
    } as any

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    const { getAllDashboardSharings } = await import(
      '@/services/dashboards/getAllDashboardSharings'
    )

    // Act
    const results = await getAllDashboardSharings(
      dashboardDetailsTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toEqual([])
  })

  test('should handle missing prisma client during migration', async () => {
    // Clear mock prisma client
    globalThis.prismaClients = {}

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    const { getAllDashboardSharings } = await import(
      '@/services/dashboards/getAllDashboardSharings'
    )

    // Act
    const results = await getAllDashboardSharings(
      dashboardDetailsTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toEqual([])
  })

  test('should migrate when migrationConfig exists and no sharings exist in table storage', async () => {
    // Mock SQL results
    const sqlSharings = [
      {
        Id: 1,
        DashboardId: 'dashboard-1',
        UserId: 'user-1',
        OrganizationId: testOrgId,
        IsDefault: false,
        IsShared: true,
        IsFavorite: false,
      },
    ]

    // Mock prisma client
    globalThis.prismaClients = {
      [testOrgId]: {
        admClient: {
          dashboardDetails: {
            findMany: jest.fn().mockResolvedValue(sqlSharings as never),
          },
        },
      },
    } as any

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Verify no sharings exist initially
    const initialSharings = await dashboardDetailsTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(initialSharings).toHaveLength(0)

    const { getAllDashboardSharings } = await import(
      '@/services/dashboards/getAllDashboardSharings'
    )

    // Act
    const results = await getAllDashboardSharings(
      dashboardDetailsTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        rowKey: 'dashboard-1',
        dashboardRowKey: 'dashboard-1',
        userId: 'user-1',
        organizationId: testOrgId,
        isDefault: false,
        isShared: true,
        isFavorite: false,
      })
    )

    // Verify sharings were migrated to table storage
    const storedSharings = await dashboardDetailsTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedSharings).toHaveLength(1)
  })

  test('should not migrate when migrationConfig exists but sharings already exist in table storage', async () => {
    // First create some existing sharings
    const existingSharing = {
      partitionKey: testOrgId,
      rowKey: 'existing-1',
      dashboardRowKey: 'existing-dashboard-1',
      userId: 'existing-user-1',
      organizationId: testOrgId,
      isDefault: true,
      isShared: true,
      isFavorite: true,
    }
    await dashboardDetailsTableStorage.insertEntity(existingSharing)

    // Mock SQL results that shouldn't be migrated
    const sqlSharings = [
      {
        Id: 1,
        DashboardId: 'sql-dashboard-1',
        UserId: 'sql-user-1',
        OrganizationId: testOrgId,
        IsDefault: false,
        IsShared: true,
        IsFavorite: false,
      },
    ]

    // Mock prisma client
    const mockFindMany = jest.fn().mockResolvedValue(sqlSharings as never)
    globalThis.prismaClients = {
      [testOrgId]: {
        admClient: {
          dashboardDetails: {
            findMany: mockFindMany,
          },
        },
      },
    } as any

    const migrationConfig: MigrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    const { getAllDashboardSharings } = await import(
      '@/services/dashboards/getAllDashboardSharings'
    )

    // Act
    const results = await getAllDashboardSharings(
      dashboardDetailsTableStorage,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        rowKey: 'existing-1',
        dashboardRowKey: 'existing-dashboard-1',
        userId: 'existing-user-1',
        organizationId: testOrgId,
        isDefault: true,
        isShared: true,
        isFavorite: true,
      })
    )

    // Verify no additional sharings were migrated
    const storedSharings = await dashboardDetailsTableStorage.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedSharings).toHaveLength(1)
    expect(mockFindMany).not.toHaveBeenCalled()
  })
})
