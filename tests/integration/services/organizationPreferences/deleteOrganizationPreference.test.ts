import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { deleteOrganizationPreference } from '@/services/organizationPreferences/deleteOrganizationPreference'
import { OrganizationPreference } from '@/types/organizationPreference'
import { StorageTables } from '@/enums/storageTables'

dayjs.extend(utc)

describe('deleteOrganizationPreference', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.OrganizationPreferences}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should delete an existing organization preference', async () => {
    // Arrange
    const preferenceId = `pref-${dayjs.utc().valueOf()}`
    const testPreference: OrganizationPreference = {
      partitionKey: testOrgId,
      rowKey: preferenceId,
      key: 'testKey',
      value: 'testValue',
    }
    await tableStorageWrapper.insertEntity(testPreference)

    // Verify preference was created
    let preferences =
      await tableStorageWrapper.queryEntities<OrganizationPreference>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(preferences.length).toBe(1)

    // Act
    await deleteOrganizationPreference(
      tableStorageWrapper,
      testOrgId,
      preferenceId
    )

    // Assert
    preferences =
      await tableStorageWrapper.queryEntities<OrganizationPreference>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(preferences.length).toBe(0)
  })

  test('should only delete specified preference', async () => {
    // Arrange
    const preference1: OrganizationPreference = {
      partitionKey: testOrgId,
      rowKey: `pref1-${dayjs.utc().valueOf()}`,
      key: 'key1',
      value: 'value1',
    }
    const preference2: OrganizationPreference = {
      partitionKey: testOrgId,
      rowKey: `pref2-${dayjs.utc().valueOf()}`,
      key: 'key2',
      value: 'value2',
    }

    await tableStorageWrapper.insertEntity(preference1)
    await tableStorageWrapper.insertEntity(preference2)

    // Verify both preferences were created
    let preferences =
      await tableStorageWrapper.queryEntities<OrganizationPreference>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(preferences.length).toBe(2)

    // Act
    await deleteOrganizationPreference(
      tableStorageWrapper,
      testOrgId,
      preference1.rowKey
    )

    // Assert
    preferences =
      await tableStorageWrapper.queryEntities<OrganizationPreference>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(preferences.length).toBe(1)
    expect(preferences[0]!.rowKey).toBe(preference2.rowKey)
  })
})
