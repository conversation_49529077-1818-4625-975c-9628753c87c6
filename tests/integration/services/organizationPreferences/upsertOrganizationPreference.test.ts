import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { upsertOrganizationPreference } from '@/services/organizationPreferences/upsertOrganizationPreference'
import { OrganizationPreference } from '@/types/organizationPreference'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('upsertOrganizationPreference', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.OrganizationPreferences}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert new preference when it does not exist', async () => {
    // Arrange
    const testPreference: OrganizationPreference = {
      partitionKey: testOrgId,
      rowKey: `pref-${dayjs.utc().valueOf()}`,
      key: 'testKey',
      value: 'testValue',
    }

    // Act
    await upsertOrganizationPreference(tableStorageWrapper, testPreference)

    // Assert
    const preferences =
      await tableStorageWrapper.queryEntities<OrganizationPreference>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(preferences.length).toBe(1)
    expect(preferences[0]).toMatchObject({
      partitionKey: testOrgId,
      key: 'testKey',
      value: 'testValue',
    })
  })

  test('should update existing preference when it exists', async () => {
    // Arrange
    const preferenceId = `pref-${dayjs.utc().valueOf()}`
    const initialPreference: OrganizationPreference = {
      partitionKey: testOrgId,
      rowKey: preferenceId,
      key: 'testKey',
      value: 'initialValue',
    }

    // Insert initial preference
    await tableStorageWrapper.insertEntity<OrganizationPreference>(
      initialPreference
    )

    // Create updated preference
    const updatedPreference: OrganizationPreference = {
      ...initialPreference,
      value: 'updatedValue',
    }

    // Act
    await upsertOrganizationPreference(tableStorageWrapper, updatedPreference)

    // Assert
    const preferences =
      await tableStorageWrapper.queryEntities<OrganizationPreference>(
        `PartitionKey eq '${testOrgId}' and RowKey eq '${preferenceId}'`
      )
    expect(preferences.length).toBe(1)
    expect(preferences[0]).toMatchObject({
      partitionKey: testOrgId,
      key: 'testKey',
      value: 'updatedValue',
    })
  })

  test('should handle multiple preferences for same organization', async () => {
    // Arrange
    const testPreferences: OrganizationPreference[] = [
      {
        partitionKey: testOrgId,
        rowKey: `pref1-${dayjs.utc().valueOf()}`,
        key: 'key1',
        value: 'value1',
      },
      {
        partitionKey: testOrgId,
        rowKey: `pref2-${dayjs.utc().valueOf()}`,
        key: 'key2',
        value: 'value2',
      },
    ]

    // Act
    for (const pref of testPreferences) {
      await upsertOrganizationPreference(tableStorageWrapper, pref)
    }

    // Assert
    const preferences =
      await tableStorageWrapper.queryEntities<OrganizationPreference>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(preferences.length).toBe(2)
    expect(preferences.map((p) => p.key)).toEqual(['key1', 'key2'])
    expect(preferences.map((p) => p.value)).toEqual(['value1', 'value2'])
  })
})
