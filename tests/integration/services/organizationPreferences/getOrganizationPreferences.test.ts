import { getOrganizationPreferences } from '@/services/organizationPreferences/getOrganizationPreferences'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { OrganizationPreference } from '@/types/organizationPreference'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { StorageTables } from '@/enums/storageTables'
import { jest, test } from '@jest/globals'
import { SelectionType } from '@/enums/selectionType'
import { OrganizationPreferenceKey } from "@/enums/organizationPreferenceKey";

dayjs.extend(utc)

describe('getOrganizationPreferences', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  const testFieldKey = OrganizationPreferenceKey.PRIMARYMEASURETYPE

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.OrganizationPreferences}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    // Create the table before each test
    await tableStorageWrapper.createTable()

    // Clean up any existing test data
    const existingPrefs =
      await tableStorageWrapper.queryEntities<OrganizationPreference>(
        `PartitionKey eq '${testOrgId}'`
      )
    for (const pref of existingPrefs) {
      await tableStorageWrapper.deleteEntity(pref.partitionKey, pref.rowKey)
    }
  })

  afterEach(async () => {
    // Clean up the table after each test
    await tableStorageWrapper.deleteTable()
  })

  test('should return empty array when no preferences exist', async () => {
    const results = await getOrganizationPreferences(
      tableStorageWrapper,
      testOrgId,
      testFieldKey
    )
    expect(results).toEqual([])
  })

  test('should migrate and return organization preferences when migrationConfig is provided', async () => {
    // Mock SQL results
    const sqlPreferences = [
      {
        Id: 1,
        OrganizationId: testOrgId,
        Key: testFieldKey,
        Value: 'SQL Preference 1',
      },
      {
        Id: 2,
        OrganizationId: testOrgId,
        Key: testFieldKey,
        Value: 'SQL Preference 2',
      },
    ]

    // Mock prisma client
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          organizationPreferences: {
            findMany: jest.fn().mockResolvedValue(sqlPreferences as never),
          },
        },
      },
    } as any

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Act
    const results = await getOrganizationPreferences(
      tableStorageWrapper,
      testOrgId,
      testFieldKey,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: '1',
          key: testFieldKey,
          value: 'SQL Preference 1',
        }),
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: '2',
          key: testFieldKey,
          value: 'SQL Preference 2',
        }),
      ])
    )

    // Verify preferences were migrated to table storage
    const storedPreferences =
      await tableStorageWrapper.queryEntities<OrganizationPreference>(
        `PartitionKey eq '${testOrgId}'`
      )
    expect(storedPreferences).toHaveLength(2)
  })

  test('should handle empty SQL results during migration', async () => {
    // Mock empty SQL results
    globalThis.prismaClients = {
      [testOrgId]: {
        hubClient: {
          organizationPreferences: {
            findMany: jest.fn().mockResolvedValue([] as never),
          },
        },
      },
    } as any

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Act
    const results = await getOrganizationPreferences(
      tableStorageWrapper,
      testOrgId,
      testFieldKey,
      migrationConfig
    )

    // Assert
    expect(results).toEqual([])
  })

  test('should handle missing prisma client during migration', async () => {
    // Clear mock prisma client
    globalThis.prismaClients = {}

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Act
    const results = await getOrganizationPreferences(
      tableStorageWrapper,
      testOrgId,
      testFieldKey,
      migrationConfig
    )

    // Assert
    expect(results).toEqual([])
  })

  test('should return organization preferences when they exist', async () => {
    // Arrange
    const testPref1: OrganizationPreference = {
      partitionKey: testOrgId,
      rowKey: `pref1-${dayjs.utc().valueOf()}`,
      key: testFieldKey,
      value: 'Test Preference 1',
    }

    const testPref2: OrganizationPreference = {
      partitionKey: testOrgId,
      rowKey: `pref2-${dayjs.utc().valueOf()}`,
      key: testFieldKey,
      value: 'Test Preference 2',
    }

    await tableStorageWrapper.insertEntity(testPref1)
    await tableStorageWrapper.insertEntity(testPref2)

    // Act
    const results = await getOrganizationPreferences(
      tableStorageWrapper,
      testOrgId,
      testFieldKey
    )

    // Assert
    expect(results).toHaveLength(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: testOrgId,
          key: testFieldKey,
          value: 'Test Preference 1',
        }),
        expect.objectContaining({
          partitionKey: testOrgId,
          key: testFieldKey,
          value: 'Test Preference 2',
        }),
      ])
    )
  })

  test('should only return preferences matching the field value', async () => {
    // Arrange
    const matchingPref: OrganizationPreference = {
      partitionKey: testOrgId,
      rowKey: `pref1-${dayjs.utc().valueOf()}`,
      key: testFieldKey,
      value: 'Matching Preference',
    }

    const nonMatchingPref: OrganizationPreference = {
      partitionKey: testOrgId,
      rowKey: `pref2-${dayjs.utc().valueOf()}`,
      key: 'differentField',
      value: 'Non-matching Preference',
    }

    await tableStorageWrapper.insertEntity(matchingPref)
    await tableStorageWrapper.insertEntity(nonMatchingPref)

    // Act
    const results = await getOrganizationPreferences(
      tableStorageWrapper,
      testOrgId,
      testFieldKey
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        key: testFieldKey,
        value: 'Matching Preference',
      })
    )
  })
})
