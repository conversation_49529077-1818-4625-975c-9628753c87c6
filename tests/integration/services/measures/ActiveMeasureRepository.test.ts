import {
  factory,
  closePool,
} from '@/services/snowflake/SnowflakeRepositoryFactory'
import { env } from '@/env'
import {
  createSnowflakeConfiguration,
  createConnection,
  executeAsAResultSet,
  executeAsSingleValue,
} from '@/services/snowflake/SnowflakeHelper'

const OrganizationTypeId = 98999
const EntityId = 9899999
const ActiveMeasureId1 = 989997
const ActiveMeasureId2 = 989998
const MeasureGUID1 = '06b10d78-29ac-ea11-a8fe-4cedfb610c38'
const MeasureGUID2 = '07b10d78-29ac-ea11-a8fe-4cedfb610c39'

// Setup fixture data
beforeAll(async () => {
  const connectionConfiguration = {
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }
  const connection = createConnection(connectionConfiguration)

  const orgTypeCount = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."OrganizationTypes" where "Id"=${OrganizationTypeId}`
  )
  if (!orgTypeCount) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."OrganizationTypes" ("Id", "Code", "Description") VALUES (${OrganizationTypeId}, '1', NULL)`
    )
  }

  const entityCount = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."Entities" where "Id"=${EntityId}`
  )
  if (!entityCount) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."Entities"
            ("Id", "Code", "Description", "SourceContainerIdentifier", "EntityTypeId", "OrganizationTypeId", "UseForComparativeData")
            VALUES
            (${EntityId}, '**********', 'Fast, Alan (Mockingjay Medical Health System)', 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC', 2, ${OrganizationTypeId}, 1)`
    )
  }

  const activeMeasure1Count = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."ActiveMeasures" where "Id"=${ActiveMeasureId1}`
  )
  if (!activeMeasure1Count) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."ActiveMeasures"
            ("Id", "MeasureGUID", "MeasureSubId", "EntitiesId", "OnDate", "ProcessingStartDate", "LastUpdateDateTime", "IsMIPSMeasure")
            VALUES
            (${ActiveMeasureId1}, '${MeasureGUID1}', 'a', ${EntityId}, '2024-01-01 00:00:00.0', '2024-01-01 00:00:00.0', '2024-01-01 00:00:00.0', 1)`
    )
  }

  const activeMeasure2Count = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."ActiveMeasures" where "Id"=${ActiveMeasureId2}`
  )
  if (!activeMeasure2Count) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."ActiveMeasures"
            ("Id", "MeasureGUID", "MeasureSubId", "EntitiesId", "OnDate", "ProcessingStartDate", "LastUpdateDateTime", "IsMIPSMeasure")
            VALUES
            (${ActiveMeasureId2}, '${MeasureGUID2}', 'b', ${EntityId}, '2024-01-01 00:00:00.0', '2024-01-01 00:00:00.0', '2024-01-01 00:00:00.0', 0)`
    )
  }

  connection.destroy((err) => {})
})

// Clean up resources after all tests
afterAll(async () => {
  const connectionConfiguration = {
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }
  const connection = createConnection(connectionConfiguration)
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."OrganizationTypes" Where "Id" = ${OrganizationTypeId} `
  )
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."Entities" Where "Id" = ${EntityId} `
  )
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."ActiveMeasures" Where "Id" IN (${ActiveMeasureId1}, ${ActiveMeasureId2}) `
  )
  connection.destroy((err) => {})
  await closePool()
})

test('should find all measure groups', async () => {
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }).createActiveMeasureRepository()

  if (env.SNOWFLAKE_ACCOUNT) {
    const results = await repo.findAllDetailedActiveMeasures()
    expect(results).toBeDefined()
    expect(results.length).toBeGreaterThan(0)

    // Verify structure of returned data
    expect(results.length).toBeGreaterThan(0)
    if (results.length > 0) {
      const firstResult = results[0]! // Non-null assertion
      expect(firstResult).toHaveProperty('Id')
      expect(firstResult).toHaveProperty('MeasureGUID')
      expect(firstResult).toHaveProperty('EntitiesId')
      expect(firstResult).toHaveProperty('SourceContainerIdentifier')
      expect(firstResult.SourceContainerIdentifier).toBe(
        'cc956396-c75a-43c0-a7fe-e7236dd41014_EC'
      )
    }
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

test('should find measure by GUID with description', async () => {
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }).createActiveMeasureRepository()

  if (env.SNOWFLAKE_ACCOUNT) {
    // Use the MeasureGUID from our test data
    const result =
      await repo.findFirstDetailedActiveMeasuresByGuid(MeasureGUID1)
    expect(result).toBeDefined()
    expect(result?.MeasureGUID).toBe(MeasureGUID1)
    expect(result?.Description).toBe("Bluebirds Medical Group 2")

    // Test with a non-existent GUID
    const nonExistentResult =
      await repo.findFirstDetailedActiveMeasuresByGuid('non-existent-guid')
    expect(nonExistentResult).toBeUndefined()
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

test('should inherit base repository methods', async () => {
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }).createActiveMeasureRepository()

  if (env.SNOWFLAKE_ACCOUNT) {
    // Test findAll method inherited from SnowflakeRepository
    const results = await repo.findAll()
    expect(results).toBeDefined()
    expect(results.length).toBeGreaterThan(0)

    // Test findById method inherited from SnowflakeRepository
    if (results.length > 0) {
      const firstResult = results[0]
      // Make sure firstResult is defined before accessing its properties
      if (firstResult) {
        const firstId = firstResult.Id
        const result = await repo.findById(firstId)
        expect(result).toBeDefined()
        expect(result?.Id).toEqual(firstId)
      }
    }
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})
