import {
  factory,
  closePool,
} from '@/services/snowflake/SnowflakeRepositoryFactory'
import { env } from '@/env'
import {
  createSnowflakeConfiguration,
  createConnection,
  executeAsAResultSet,
  executeAsSingleValue,
} from '@/services/snowflake/SnowflakeHelper'
import dayjs from 'dayjs'
import { ECMeasureResultSummary } from '@/types/eCMeasureResultSummary'
import { MeasureResultSummary } from '@/types/measureResultSummary'
import fs from 'fs'
import { fileURLToPath } from 'url'
import path from 'path'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { ParameterConstants } from '@/enums/parameterConstants'
import { Group } from '@/types/group'
import { Entity } from '@/types/entity'

const OrganizationTypeId = 99999
const EntityId = 9999999
const MeasureSummaryId1 = 999997
const MeasureSummaryId2 = 999998
const MeasureSummaryId3 = 999999
const SECONDS = 1000

// Setup fixture data
beforeAll(async () => {
  const connectionConfiguration = {
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }
  const connection = createConnection(connectionConfiguration)

  const orgTypeCount = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."OrganizationTypes" where "Id"=${OrganizationTypeId}`
  )
  if (!orgTypeCount) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."OrganizationTypes" ("Id", "Code", "Description") VALUES (${OrganizationTypeId}, '1', NULL)`
    )
  }

  const entityCount = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."Entities" where "Id"=${EntityId}`
  )
  if (!entityCount) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."Entities"
            ("Id", "Code", "Description", "SourceContainerIdentifier", "EntityTypeId", "OrganizationTypeId", "UseForComparativeData")
            VALUES
            (${EntityId}, '**********', 'Fast,  Alan (Mockingjay Medical Health System)', 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC', 2, ${OrganizationTypeId}, 1)`
    )
  }

  const measureSummary1Count = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."MeasureSummary" where "Id"=${MeasureSummaryId1}`
  )
  if (!measureSummary1Count) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."MeasureSummary"
            ("Id", "MeasureGUID", "MeasureSubId", "EntitiesId", "StartDate", "EndDate", "Period", "Numerator", "Denominator", "DenominatorOnly", "NumeratorExclusion","DenominatorExclusion","DenominatorException","Performance","PerformanceRaw","NumeratorValue","DenominatorValue","IncompleteCases","IPP","StringResult","HoverText")
            VALUES
            (${MeasureSummaryId1}, '06b10d78-29ac-ea11-a8fe-4cedfb610c38','a',${EntityId},'2024-01-01 00:00:00.0','2024-02-01 00:00:00.0','M',0.0,6.0,4.0,0.0,2.0,0.0,0.0,0.0,NULL,NULL,NULL,6.0,NULL,NULL)`
    )
  }

  const measureSummary2Count = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."MeasureSummary" where "Id"=${MeasureSummaryId2}`
  )
  if (!measureSummary2Count) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."MeasureSummary"
            ("Id", "MeasureGUID", "MeasureSubId", "EntitiesId", "StartDate", "EndDate", "Period", "Numerator", "Denominator", "DenominatorOnly", "NumeratorExclusion","DenominatorExclusion","DenominatorException","Performance","PerformanceRaw","NumeratorValue","DenominatorValue","IncompleteCases","IPP","StringResult","HoverText")
            VALUES
            (${MeasureSummaryId2}, '06b10d78-29ac-ea11-a8fe-4cedfb610c38','a',${EntityId},'2024-01-01 00:00:00.0','2024-04-01 00:00:00.0','Q',2.0,10.0,6.0,0.0,2.0,0.0,25.0,25.0,NULL,NULL,NULL,10.0,NULL,NULL)`
    )
  }

  const measureSummary3Count = await executeAsSingleValue<number>(
    connection,
    `SELECT count(*) as "cnt" FROM MECA."MeasureSummary" where "Id"=${MeasureSummaryId3}`
  )
  if (!measureSummary3Count) {
    await executeAsAResultSet(
      connection,
      `INSERT INTO MECA."MeasureSummary"
            ("Id", "MeasureGUID", "MeasureSubId", "EntitiesId", "StartDate", "EndDate", "Period", "Numerator", "Denominator", "DenominatorOnly", "NumeratorExclusion","DenominatorExclusion","DenominatorException","Performance","PerformanceRaw","NumeratorValue","DenominatorValue","IncompleteCases","IPP","StringResult","HoverText")
            VALUES
            (${MeasureSummaryId3}, '06b10d78-29ac-ea11-a8fe-4cedfb610c38','a',${EntityId},'2024-01-01 00:00:00.0','2025-01-01 00:00:00.0','Y',2.0,10.0,6.0,0.0,2.0,0.0,25.0,25.0,NULL,NULL,NULL,10.0,NULL,NULL)`
    )
  }
  connection.destroy((err) => {})
}, 70 * SECONDS)

// Clean up resources after all tests
afterAll(async () => {
  const connectionConfiguration = {
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }
  const connection = createConnection(connectionConfiguration)
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."OrganizationTypes" Where "Id" = ${OrganizationTypeId} `
  )
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."Entities" Where "Id" = ${EntityId} `
  )
  await executeAsSingleValue<number>(
    connection,
    `DELETE FROM MECA."MeasureSummary" Where "Id" IN (${MeasureSummaryId1},  ${MeasureSummaryId2}, ${MeasureSummaryId3}) `
  )
  connection.destroy((err) => {})
  await closePool()
})

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const expectedResults = JSON.parse(
  fs.readFileSync(`${__dirname}/expected-measure-results.json`, 'utf8')
)

test('should find Measure by Id in ADM_MOCKINGJAY', async () => {
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()
  if (env.SNOWFLAKE_ACCOUNT) {
    const result = await repo.findById('e814e2fe-506c-42c3-81b1-467c4b22fbf3')
    expect(result).toBeDefined()
    expect(result?.Id).toBe(1)
    expect(result?.MedisolvMeasureId).toBe(
      'e814e2fe-506c-42c3-81b1-467c4b22fbf3'
    )
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

test('should find Measure by Id in ADM_MOCKINGJAY irrespective of case', async () => {
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()
  if (env.SNOWFLAKE_ACCOUNT) {
    const result = await repo.findById('e814e2fe-506c-42c3-81b1-467c4b22fbf3'.toUpperCase())
    expect(result).toBeDefined()
    expect(result?.Id).toBe(1)
    expect(result?.MedisolvMeasureId).toBe(
        'e814e2fe-506c-42c3-81b1-467c4b22fbf3'
    )
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

test('should return all Measures', async () => {
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()
  if (env.SNOWFLAKE_ACCOUNT) {
    const results = await repo.findAll()
    expect(results).toBeDefined()
    expect(results.length).toBeGreaterThan(0)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

test("should fail accessing a DB for which you don't have rights for the wrong ROLE", async () => {
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MSPCD',
  }).createMeasureRepository()
  if (env.SNOWFLAKE_ACCOUNT) {
    expect(
      async () => await repo.findById('e814e2fe-506c-42c3-81b1-467c4b22fbf3')
    ).rejects.toThrow()
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

test('should calculate EC measure results', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Q'
  const isPartner = false
  const isSubmissionGroupLevel = true
  const submissionGroupIds = ['*'] // All submission groups
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected1)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

test('should calculate EC measure results - Organization View - All entities - Submission Group Level - Single submission group', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isSubmissionGroupLevel = true
  const submissionGroupIds = ['*'] // All submission groups
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected2)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 1.a.ii - Organization View, All entities, Provider Level
test('should calculate EC measure results - Organization View - All entities - Provider Level', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isSubmissionGroupLevel = false // Provider Level
  const submissionGroupIds = ['*'] // All submission groups
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected3)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 1.b - Organization View, Specific entities
test('should calculate EC measure results - Organization View - Specific entities', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isSubmissionGroupLevel = true
  const submissionGroupIds = [
    '6627bdd879564eb80253a0fc_660da37246bc4726049a607d',
  ] // Specific submission groups
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected4)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 1.c - Organization View, Specific entities
test('should calculate EC measure results - Organization View - not a submissiongrouplevel - Specific entities', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isSubmissionGroupLevel = false
  const submissionGroupIds = ['**********', '**********', '**********'] // Specific submission groups
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected5)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 2. Partner View (isPartner) tests

// 2.a.i - Partner View, All organizations, Submission Group Level
test('should calculate EC measure results - Partner View - All organizations - Submission Group Level', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'test-partner-123'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = true
  const isSubmissionGroupLevel = true
  const submissionGroupIds = [
    'ALLORGANIZATION',
    'cc956396-c75a-43c0-a7fe-e7236dd41014_EC',
  ] // All organizations
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected6)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 2.a.ii - Partner View, All organizations, Provider Level
test('should calculate EC measure results - Partner View - All organizations - Provider Level', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'test-partner-123'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = true
  const isSubmissionGroupLevel = false // Provider Level
  const submissionGroupIds = [
    'ALLORGANIZATION',
    'cc956396-c75a-43c0-a7fe-e7236dd41014_EC',
  ] // All organizations
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected7)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// // 2.b.i - Partner View, Specific entities, All entities
test('should calculate EC measure results - Partner View - Specific entities - All entities', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'test-partner-123'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = true
  const isSubmissionGroupLevel = true
  const submissionGroupIds = ['*'] // All entities
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected8)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// // 2.b.ii - Partner View, Specific entities, Specific entities
test('should calculate EC measure results - Partner View - Specific entities - Specific entities', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'test-partner-123'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = true
  const isSubmissionGroupLevel = true
  const submissionGroupIds = [
    '6627bdd879564eb80253a0fc_660da37246bc4726049a607d',
  ] // Specific entities
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected9)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// Test with a specific measure identifier
test('should calculate EC measure results with specific measure', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isSubmissionGroupLevel = true
  const submissionGroupIds = ['*'] // All submission groups
  const measureIdentifier = '06b10d78-29ac-ea11-a8fe-4cedfb610c38' // Specific measure

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected10)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// Test with a specific measure identifier
test('should calculate EC measure results with specific measure for a partner', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = true
  const isSubmissionGroupLevel = true
  const submissionGroupIds = ['*'] // All submission groups
  const measureIdentifier = '06b10d78-29ac-ea11-a8fe-4cedfb610c38' // Specific measure

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateECMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isSubmissionGroupLevel,
      submissionGroupIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected11)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// Tests for calculateMeasureResults method

// 1.a.i - Organization View, All entities, Facility Level
test('should calculate measure results - Organization View - All entities - Facility Level', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isFacilityLevel = true // Facility Level
  const isCombinedGroup = false // Not a combined group
  const subOrganizationIds = ['*'] // All entities
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isFacilityLevel,
      isCombinedGroup,
      subOrganizationIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected12)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 1.a.ii - Organization View, All entities, Hospital Level
test('should calculate measure results - Organization View - All entities - Hospital Level', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isFacilityLevel = false // Hospital Level
  const isCombinedGroup = false // Not a combined group
  const subOrganizationIds = ['*'] // All entities
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isFacilityLevel,
      isCombinedGroup,
      subOrganizationIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected13)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 1.a.iii - Organization View, All entities, Combined Group Level
test('should calculate measure results - Organization View - All entities - Combined Group Level', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isFacilityLevel = false // Not Facility Level
  const isCombinedGroup = true // Combined Group Level
  const subOrganizationIds = ['*'] // All entities
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isFacilityLevel,
      isCombinedGroup,
      subOrganizationIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected19) // Using the same expected results as Hospital Level for now
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 1.b - Organization View, Specific entities
test('should calculate measure results - Organization View - Specific entities', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isFacilityLevel = false
  const isCombinedGroup = false // Not a combined group
  const subOrganizationIds = [
    'efbfe0c0-4828-4fb9-a70e-e94918e8f184',
    '0bc871ba-043d-4f7f-800d-898c6f925255',
  ] // Specific entities
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isFacilityLevel,
      isCombinedGroup,
      subOrganizationIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected14)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 2.a - Partner View, All organizations (ALLORGANIZATION)
test('should calculate measure results - Partner View - All organizations', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'test-partner-123'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = true
  const isFacilityLevel = false
  const isCombinedGroup = false // Not a combined group
  const subOrganizationIds = [
    'ALLORGANIZATION',
    'cc956396-c75a-43c0-a7fe-e7236dd41014_EH',
  ] // All organizations
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isFacilityLevel,
      isCombinedGroup,
      subOrganizationIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected15)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 2.b - Partner View, All entities (*)
test('should calculate measure results - Partner View - All entities', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'test-partner-123'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = true
  const isFacilityLevel = false
  const isCombinedGroup = false // Not a combined group
  const subOrganizationIds = ['*'] // All entities
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isFacilityLevel,
      isCombinedGroup,
      subOrganizationIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected16)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// 2.c - Partner View, Specific entities
test('should calculate measure results - Partner View - Specific entities', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'test-partner-123'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = true
  const isFacilityLevel = false
  const isCombinedGroup = false // Not a combined group
  const subOrganizationIds = [
    'efbfe0c0-4828-4fb9-a70e-e94918e8f184',
    '0bc871ba-043d-4f7f-800d-898c6f925255',
  ] // Specific entities
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isFacilityLevel,
      isCombinedGroup,
      subOrganizationIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected17)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

test('should return an empty set with there are no suborganizations or measure identifier', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'test-partner-123'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Q' // Yearly
  const isPartner = true
  const isFacilityLevel = false
  const isCombinedGroup = false // Not a combined group
  const subOrganizationIds: string[] = []
  const measureIdentifier = '' // All measures

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateMeasureResults(
        organizationId,
        isPartner,
        periodType,
        startDate,
        endDate,
        isFacilityLevel,
        isCombinedGroup,
        subOrganizationIds,
        measureIdentifier
    )

    // Assert
    expect(results.length).toEqual(0)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// Test with a specific measure identifier
test('should calculate measure results with specific measure', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'ADM_MOCKINGJAY',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const startDate = dayjs('2024/03/31')
  const endDate = dayjs('2025/06/30')
  const periodType = 'Y' // Yearly
  const isPartner = false
  const isFacilityLevel = false
  const isCombinedGroup = false // Not a combined group
  const subOrganizationIds = ['*'] // All entities
  const measureIdentifier = '5f3ad441-7fb0-ee11-bea0-0022484ce73d'

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.calculateMeasureResults(
      organizationId,
      isPartner,
      periodType,
      startDate,
      endDate,
      isFacilityLevel,
      isCombinedGroup,
      subOrganizationIds,
      measureIdentifier
    )

    // Assert
    assertResults(results, expectedResults.expected18)
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

function assertResults(
  results: ECMeasureResultSummary[] | MeasureResultSummary[],
  expectedResults: any[]
) {
  expect(results).toBeDefined()
  expect(results.length).toBe(expectedResults.length)
  expectedResults.forEach((row: any) => {
    const actualList = results.filter((result: any) => result.id == row.Id)
    expect(actualList.length).toBe(1)
    const actual = actualList[0]!
    expect(Math.round(actual.ipp! * 100) / 100).toBe(
      Math.round(row.IPP! * 100) / 100
    )
    expect(dayjs(actual.endDate).toISOString()).toBe(
      dayjs(row.EndDate).toISOString()
    )
    expect(dayjs(actual.startDate).toISOString()).toBe(
      dayjs(row.StartDate).toISOString()
    )
    expect(Math.round(actual.denominator! * 100) / 100).toBe(
      Math.round(row.Denominator! * 100) / 100
    )
    expect(actual.entityCode).toBe(row.EntityCode)
    expect(actual.entityName).toBe(row.EntityName)
    expect(actual.measureGUID).toBe(row.MeasureGUID)
  })
}

//Tests for calculateEHPatientDetails method

test('should calculate EH patient details - Organization - All sub-organizations', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const isPartner = false
  const startDate = dayjs('2023-01-01')
  const endDate = dayjs('2023-03-31')
  const measureIdentifier = 'f3b00d78-29ac-ea11-a8fe-4cedfb610c38'
  const subOrganizationIds = ['*']
  const organizationParams: SimplifiedParameter[] = [
    { key: ParameterConstants.SuffixSourceContainerIdentifiers, value: 'true' },
  ]
  const subOrganizationIdsByOrganization: any[] = [
    '0bc871ba-043d-4f7f-800d-898c6f925255',
    '44e0b6e5-75fa-478b-aafe-7d7499f31a5d',
    '0bd4227a-7377-40f1-b1eb-af47f79b4046',
  ]
  const groups: Group[] = [
    {
      groupId: 'efbfe0c0-4828-4fb9-a70e-e94918e8f184',
      groupName: 'Mockingjay Hospital Roll Up',
      userId: '7d89888a-7074-4b30-b3ba-fb963fd90aef',
      displayName: 'Karthik Anant',
      organizationId: 'cc956396-c75a-43c0-a7fe-e7236dd41014',
      organizationName: 'Mockingjay Medical Health System - Valerian',
      createdDate: dayjs('2024-07-03T23:26:52.907'),
      modifiedDate: undefined,
      subOrganizations: [
        {
          subOrganizationId: '0bc871ba-043d-4f7f-800d-898c6f925255',
        },
        {
          subOrganizationId: '44e0b6e5-75fa-478b-aafe-7d7499f31a5d',
        },
        {
          subOrganizationId: '0bd4227a-7377-40f1-b1eb-af47f79b4046',
        },
      ],
      groupMetadata: [],
    },
  ]

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.getEHPatientDetails(
      organizationId,
      isPartner,
      startDate,
      endDate,
      measureIdentifier,
      subOrganizationIds,
      organizationParams,
      subOrganizationIdsByOrganization,
      groups
    )

    // Assert

    expect(results.length).toBe(expectedResults.expectedEHPatientDetails.length)
    expectedResults.expectedEHPatientDetails.forEach((row: any) => {
      const actualList = results.filter(
        (result: any) => result.patientsId == row.patientsId
      )
      expect(actualList.length).toBe(1)
      const actual = actualList[0]!
      expect(actual?.age).toBe(row.age)
      expect(actual?.patientsId).toBe(row.patientsId)
      expect(actual?.referenceDate).toBe(row.referenceDate)
      expect(actual?.patientIdentifier).toBe(row.patientIdentifier)
      expect(actual?.patientName).toBe(row.patientName)
      expect(actual?.result).toBe(row.result)
      expect(actual?.sex).toBe(row?.sex)
      expect(actual?.numeratorValue).toBe(row.numeratorValue)
      expect(actual?.denominatorValue).toBe(row.denominatorValue)
      expect(actual?.dischargeDateTime).toBe(row.dischargeDateTime)
      expect(actual?.encountersId).toBe(row.encountersId)
      expect(actual?.measureGUID).toBe(row.measureGUID)
    })
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

test('should calculate EH patient details - Organization - Specific sub-organizations', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const isPartner = false
  const startDate = dayjs('2023-01-01')
  const endDate = dayjs('2023-03-31')
  const measureIdentifier = 'f3b00d78-29ac-ea11-a8fe-4cedfb610c38'
  const subOrganizationIds = ['44e0b6e5-75fa-478b-aafe-7d7499f31a5d']
  const organizationParams: SimplifiedParameter[] = [
    { key: ParameterConstants.SuffixSourceContainerIdentifiers, value: 'true' },
  ]
  const subOrganizationIdsByOrganization: any[] = [
    '0bc871ba-043d-4f7f-800d-898c6f925255',
    '44e0b6e5-75fa-478b-aafe-7d7499f31a5d',
    '0bd4227a-7377-40f1-b1eb-af47f79b4046',
  ]
  const groups: Group[] = [
    {
      groupId: 'efbfe0c0-4828-4fb9-a70e-e94918e8f184',
      groupName: 'Mockingjay Hospital Roll Up',
      userId: '7d89888a-7074-4b30-b3ba-fb963fd90aef',
      displayName: 'Karthik Anant',
      organizationId: 'cc956396-c75a-43c0-a7fe-e7236dd41014',
      organizationName: 'Mockingjay Medical Health System - Valerian',
      createdDate: dayjs('2024-07-03T23:26:52.907'),
      modifiedDate: undefined,
      subOrganizations: [
        {
          subOrganizationId: '0bc871ba-043d-4f7f-800d-898c6f925255',
        },
        {
          subOrganizationId: '44e0b6e5-75fa-478b-aafe-7d7499f31a5d',
        },
        {
          subOrganizationId: '0bd4227a-7377-40f1-b1eb-af47f79b4046',
        },
      ],
      groupMetadata: [],
    },
  ]

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.getEHPatientDetails(
      organizationId,
      isPartner,
      startDate,
      endDate,
      measureIdentifier,
      subOrganizationIds,
      organizationParams,
      subOrganizationIdsByOrganization,
      groups
    )

    // Assert
    expect(results.length).toBe(expectedResults.expectedEHPatientDetails.length)
    expectedResults.expectedEHPatientDetails.forEach((row: any) => {
      const actualList = results.filter(
        (result: any) => result.patientsId == row.patientsId
      )
      expect(actualList.length).toBe(1)
      const actual = actualList[0]!
      expect(actual?.age).toBe(row.age)
      expect(actual?.patientsId).toBe(row.patientsId)
      expect(actual?.referenceDate).toBe(row.referenceDate)
      expect(actual?.patientIdentifier).toBe(row.patientIdentifier)
      expect(actual?.patientName).toBe(row.patientName)
      expect(actual?.result).toBe(row.result)
      expect(actual?.sex).toBe(row?.sex)
      expect(actual?.numeratorValue).toBe(row.numeratorValue)
      expect(actual?.denominatorValue).toBe(row.denominatorValue)
      expect(actual?.dischargeDateTime).toBe(row.dischargeDateTime)
      expect(actual?.encountersId).toBe(row.encountersId)
      expect(actual?.measureGUID).toBe(row.measureGUID)
    })
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

//Tests for getECPatientDetails method

//Test case 1: Organization View, All sub-organizations
test('should calculate EC patient details - Organization - All submission-groups', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const isPartner = false
  const startDate = dayjs('2024-04-01')
  const endDate = dayjs('2024-06-30')
  const measureIdentifier = '0fb10d78-29ac-ea11-a8fe-4cedfb610c38'
  const entityCodes = ['*']
  const organizationParams: SimplifiedParameter[] = [
    { key: ParameterConstants.SuffixSourceContainerIdentifiers, value: 'true' },
  ]
  const activeSubmissionGroupIds = [38190, 43329, 43737, 46303]

  const entities: Entity[] = [
    {
      id: 40403,
      code: '6627bdd879564eb80253a0fc',
      entityDisplayName: 'MockingJay Ambulatory Roll Up',
      entityName: 'MockingJay Ambulatory Roll Up',
      entityTypeName: 'Rendering Provider',
      organizationTypeCode: '2',
      sourceContainerIdentifier: 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC',
      entityTypeId: 2,
      organizationTypeId: 2,
    },
    {
      id: 46303,
      code: '6627bdd879564eb80253a0fc_660da37246bc4726049a607d',
      entityDisplayName: `Bluebird's Medical Group`,
      entityName: `Bluebird's Medical Group`,
      entityTypeName: 'Rendering Provider',
      organizationTypeCode: '3',
      sourceContainerIdentifier: 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC',
      entityTypeId: 2,
      organizationTypeId: 1,
    },
  ]

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.getECPatientDetails(
      organizationId,
      isPartner,
      startDate,
      endDate,
      measureIdentifier,
      entities,
      entityCodes,
      activeSubmissionGroupIds,
      organizationParams
    )

    // Assert
    expect(results.length).toBe(expectedResults.expectedECPatientDetails.length)
    expect(results.length).toBeGreaterThan(0)
    expectedResults.expectedECPatientDetails.forEach((row: any) => {
      const actualList = results.filter(
        (result: any) => result.patientsId == row.patientsId
      )
      expect(actualList.length).toBe(1)
      const actual = actualList[0]!
      expect(actual?.age).toBe(row.age)
      expect(actual?.patientsId).toBe(row.patientsId)
      expect(actual?.referenceDate).toBe(row.referenceDate)
      expect(actual?.patientIdentifier).toBe(row.patientIdentifier)
      expect(actual?.patientName).toBe(row.patientName)
      expect(actual?.result).toBe(row.result)
      expect(actual?.sex).toBe(row?.sex)
    })
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})

// Test case 2: Organization View, Specific sub-organizations
test('should calculate EC measure result details - Organization - Specific entity', async () => {
  // Arrange
  const repo = factory({
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }).createMeasureRepository()

  // Test parameters
  const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
  const isPartner = false
  const startDate = dayjs('2024-04-01')
  const endDate = dayjs('2024-06-30')
  const measureIdentifier = '0fb10d78-29ac-ea11-a8fe-4cedfb610c38'
  const entityCodes = ['6627bdd879564eb80253a0fc_660da37246bc4726049a607d']
  const organizationParams: SimplifiedParameter[] = [
    { key: ParameterConstants.SuffixSourceContainerIdentifiers, value: 'true' },
  ]

  const activeSubmissionGroupIds = [38190, 43329, 43737, 46303]

  const entities: Entity[] = [
    {
      id: 40403,
      code: '6627bdd879564eb80253a0fc',
      entityDisplayName: 'MockingJay Ambulatory Roll Up',
      entityName: 'MockingJay Ambulatory Roll Up',
      entityTypeName: 'Rendering Provider',
      organizationTypeCode: '2',
      sourceContainerIdentifier: 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC',
      entityTypeId: 2,
      organizationTypeId: 2,
    },
    {
      id: 46303,
      code: '6627bdd879564eb80253a0fc_660da37246bc4726049a607d',
      entityDisplayName: `Bluebird's Medical Group`,
      entityName: `Bluebird's Medical Group`,
      entityTypeName: 'Rendering Provider',
      organizationTypeCode: '3',
      sourceContainerIdentifier: 'cc956396-c75a-43c0-a7fe-e7236dd41014_EC',
      entityTypeId: 2,
      organizationTypeId: 1,
    },
  ]

  if (env.SNOWFLAKE_ACCOUNT) {
    // Act
    const results = await repo.getECPatientDetails(
      organizationId,
      isPartner,
      startDate,
      endDate,
      measureIdentifier,
      entities,
      entityCodes,
      activeSubmissionGroupIds,
      organizationParams
    )

    // Assert
    expect(results.length).toBe(expectedResults.expectedECPatientDetails.length)
    expect(results.length).toBeGreaterThan(0)
    expectedResults.expectedECPatientDetails.forEach((row: any) => {
      const actualList = results.filter(
        (result: any) => result.patientsId == row.patientsId
      )
      expect(actualList.length).toBe(1)
      const actual = actualList[0]!
      expect(actual?.age).toBe(row.age)
      expect(actual?.patientsId).toBe(row.patientsId)
      expect(actual?.referenceDate).toBe(row.referenceDate)
      expect(actual?.patientIdentifier).toBe(row.patientIdentifier)
      expect(actual?.patientName).toBe(row.patientName)
      expect(actual?.result).toBe(row.result)
      expect(actual?.sex).toBe(row?.sex)
    })
  } else {
    console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
  }
})
