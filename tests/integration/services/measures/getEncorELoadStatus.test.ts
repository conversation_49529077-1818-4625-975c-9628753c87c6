import { jest } from '@jest/globals'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { getEncorELoadStatus } from '@/services/measures/getEncorELoadStatus'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { StorageTables } from '@/enums/storageTables'
import { LoadStatus } from '@/types/loadStatus'

dayjs.extend(utc)

describe('getEncorELoadStatus', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`
  const today = dayjs.utc().format('YYYYMMDD')

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.PlatformJobStatus}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )

    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return EC load status when data is available', async () => {
    // Arrange
    const loadStatus: LoadStatus = {
      partitionKey: testOrgId,
      rowKey: `ec${today}_001`, // Modified to match expected format
      organizationId: testOrgId,
      startDateTime: dayjs.utc().toISOString(),
      endDateTime: dayjs.utc().toISOString(),
      status: 'Completed',
      categoryAssignmentCount: 0,
      count: 0,
      isEhLoadsDefective: false,
      ehCalculationsCompletionDate: dayjs.utc().toISOString(),
      isEcLoadsDefective: false,
      ecCalculationsCompletionDate: dayjs.utc().toISOString(),
      cdmLastLoadDate: dayjs.utc().toISOString(),
      type: 'EC',
      processingType: 'Standard',
      organizationName: 'Test Org',
      hospitalName: 'Test Hospital',
      mspLastPublishDate: dayjs.utc().toDate(),
      cdmLastLoadDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      mspLastPublishDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      admLoadStartDate: dayjs.utc().format('YYYYMMDD'),
      admLoadEndDate: dayjs.utc().format('YYYYMMDD'),
      startDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      endDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      duration: '0m',
      ehCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
      ecCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
    }
    await tableStorageWrapper.insertEntity(loadStatus)

    // Act
    const result = await getEncorELoadStatus(
      tableStorageWrapper,
      testOrgId,
      PrimaryMeasureTypeConstants.AbstractedMeasures
    )

    // Assert
    expect(result.isDataAvailable).toBe(true)
    expect(result.startDate).toBeDefined()
    expect(result.startDate).not.toBe('')
  })

  test('should return EH load status when data is available', async () => {
    // Arrange
    const loadStatus: LoadStatus = {
      partitionKey: testOrgId,
      rowKey: `eh${today}_001`,
      organizationId: testOrgId,
      startDateTime: dayjs.utc().toISOString(),
      endDateTime: dayjs.utc().toISOString(),
      status: 'Completed',
      categoryAssignmentCount: 0,
      count: 0,
      isEhLoadsDefective: false,
      ehCalculationsCompletionDate: dayjs.utc().toISOString(),
      isEcLoadsDefective: false,
      ecCalculationsCompletionDate: dayjs.utc().toISOString(),
      cdmLastLoadDate: dayjs.utc().toISOString(),
      type: 'EH',
      processingType: 'Standard',
      organizationName: 'Test Org',
      hospitalName: 'Test Hospital',
      mspLastPublishDate: dayjs.utc().toDate(),
      cdmLastLoadDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      mspLastPublishDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      admLoadStartDate: dayjs.utc().format('YYYYMMDD'),
      admLoadEndDate: dayjs.utc().format('YYYYMMDD'),
      startDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      endDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      duration: '0m',
      ehCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
      ecCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
    }
    await tableStorageWrapper.insertEntity(loadStatus)

    // Act
    const result = await getEncorELoadStatus(
      tableStorageWrapper,
      testOrgId,
      PrimaryMeasureTypeConstants.HospitalMeasures
    )

    // Assert
    expect(result.isDataAvailable).toBe(true)
    expect(result.startDate).toBeDefined()
    expect(result.startDate).not.toBe('')
  })

  test('should return false when no data is available', async () => {
    // Act
    const result = await getEncorELoadStatus(
      tableStorageWrapper,
      testOrgId,
      PrimaryMeasureTypeConstants.AbstractedMeasures
    )

    // Assert
    expect(result.isDataAvailable).toBe(false)
    expect(result.startDate).toBe('')
  })

  test('should handle PrimaryMeasureTypeConstants.None as hospital measures', async () => {
    // Arrange
    const loadStatus: LoadStatus = {
      partitionKey: testOrgId,
      rowKey: `eh${today}_001`,
      organizationId: testOrgId,
      startDateTime: dayjs.utc().toISOString(),
      endDateTime: dayjs.utc().toISOString(),
      status: 'Completed',
      categoryAssignmentCount: 0,
      count: 0,
      isEhLoadsDefective: false,
      ehCalculationsCompletionDate: dayjs.utc().toISOString(),
      isEcLoadsDefective: false,
      ecCalculationsCompletionDate: dayjs.utc().toISOString(),
      cdmLastLoadDate: dayjs.utc().toISOString(),
      type: 'EH',
      processingType: 'Standard',
      organizationName: 'Test Org',
      hospitalName: 'Test Hospital',
      mspLastPublishDate: dayjs.utc().toDate(),
      cdmLastLoadDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      mspLastPublishDateDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      admLoadStartDate: dayjs.utc().format('YYYYMMDD'),
      admLoadEndDate: dayjs.utc().format('YYYYMMDD'),
      startDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      endDateTimeDisplay: dayjs.utc().format('MM/DD/YYYY hh:mm A'),
      duration: '0m',
      ehCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
      ecCalculationsCompletionDateDisplay: dayjs
        .utc()
        .format('MM/DD/YYYY hh:mm A'),
    }
    await tableStorageWrapper.insertEntity(loadStatus)

    // Act
    const result = await getEncorELoadStatus(
      tableStorageWrapper,
      testOrgId,
      PrimaryMeasureTypeConstants.None
    )

    // Assert
    expect(result.isDataAvailable).toBe(true)
    expect(result.startDate).toBeDefined()
    expect(result.startDate).not.toBe('')
  })
})
