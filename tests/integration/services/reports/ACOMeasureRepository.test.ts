import {
  factory,
  closePool,
} from '@/services/snowflake/SnowflakeRepositoryFactory'
import { env } from '@/env'
import {
  createSnowflakeConfiguration,
  createConnection,
  executeAsAResultSet,
} from '@/services/snowflake/SnowflakeHelper'
import { jest } from '@jest/globals'

// Suppress console output during tests
beforeAll(() => {
  jest.spyOn(console, 'error').mockImplementation(() => {})
  jest.spyOn(console, 'log').mockImplementation(() => {})
})

// Clean up resources after all tests
afterAll(async () => {
  await closePool()
})

describe('MeasureRepository', () => {
  describe('findACOMeasures', () => {
    // Define common test parameters
    const getTestParams = () => {
      // Default interval type (Monthly)
      const intervalType = 'M'

      // Default date range (last 3 months)
      const endDate = new Date()
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - 3)

      return { intervalType, startDate, endDate }
    }

    test('should return ACO measures for a valid organization type code', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const connectionConfig = {
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }

      // First, let's get a valid organization type code
      const connection = createConnection(connectionConfig)

      // Query to get a sample organization type code
      const orgTypeCodes = await executeAsAResultSet(
        connection,
        `SELECT DISTINCT "Code" FROM "MECA"."OrganizationTypes" LIMIT 1`
      )

      // Close the connection
      connection.destroy((err) => {})

      // Skip if we couldn't find an organization type code
      if (
        !orgTypeCodes ||
        orgTypeCodes.length === 0 ||
        !orgTypeCodes[0]?.['Code']
      ) {
        console.log('No organization type codes found, skipping test...')
        return
      }

      const codeNumeric = String(orgTypeCodes[0]['Code'])

      // Create a real ACOMeasureRepository instance using the factory
      const repository = factory(connectionConfig).createACOMeasureRepository()

      // Get test parameters
      const { intervalType, startDate, endDate } = getTestParams()

      // Act
      const results = await repository.findACOMeasures(
        codeNumeric,
        intervalType,
        startDate,
        endDate
      )

      // Log first row of data for debugging
      if (results && results.length > 0) {
        console.log('First ACO measure:', results[0])
      } else {
        console.log('No ACO measures found')
      }

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)

      // If we have results, verify their structure
      if (results && results.length > 0) {
        const firstMeasure = results[0]!
        expect(firstMeasure).toHaveProperty('MeasureName')
        expect(typeof firstMeasure.MeasureName).toBe('string')
        expect(firstMeasure.MeasureName.length).toBeGreaterThan(0)

        expect(firstMeasure).toHaveProperty('MeasureId')
        expect(typeof firstMeasure.MeasureId).toBe('string')
        expect(firstMeasure.MeasureId.length).toBeGreaterThan(0)

        expect(firstMeasure).toHaveProperty('MeasureIdentifier')
      }
    })

    test('should return empty array for invalid organization type code', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const connectionConfig = {
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }

      // Create a real ACOMeasureRepository instance using the factory
      const repository = factory(connectionConfig).createACOMeasureRepository()

      // Use an invalid organization type code
      const invalidCodeNumeric = '999999'

      // Get test parameters
      const { intervalType, startDate, endDate } = getTestParams()

      // Act
      const results = await repository.findACOMeasures(
        invalidCodeNumeric,
        intervalType,
        startDate,
        endDate
      )

      // Assert
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results?.length).toBe(0)

      if (results && results.length > 0) {
        const firstMeasure = results[0]!
        expect(firstMeasure).toHaveProperty('MeasureIdentifier')
      }
    })

    test('should handle different interval types', async () => {
      // Skip test if Snowflake account is not defined
      if (!env.SNOWFLAKE_ACCOUNT) {
        console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
        return
      }

      // Arrange
      const connectionConfig = {
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE',
      }

      // First, let's get a valid organization type code
      const connection = createConnection(connectionConfig)
      const orgTypeCodes = await executeAsAResultSet(
        connection,
        `SELECT DISTINCT "Code" FROM "MECA"."OrganizationTypes" LIMIT 1`
      )
      connection.destroy((err) => {})

      if (
        !orgTypeCodes ||
        orgTypeCodes.length === 0 ||
        !orgTypeCodes[0]?.['Code']
      ) {
        console.log('No organization type codes found, skipping test...')
        return
      }

      const codeNumeric = String(orgTypeCodes[0]['Code'])

      // Create a real ACOMeasureRepository instance using the factory
      const repository = factory(connectionConfig).createACOMeasureRepository()

      // Get test parameters
      const { startDate, endDate } = getTestParams()

      // Test with different interval types
      const intervalTypes = ['M', 'Q', 'Y']

      for (const intervalType of intervalTypes) {
        // Act
        const results = await repository.findACOMeasures(
          codeNumeric,
          intervalType,
          startDate,
          endDate
        )

        // Assert
        expect(results).toBeDefined()
        expect(Array.isArray(results)).toBe(true)

        if (results && results.length > 0) {
          const firstMeasure = results[0]!
          expect(firstMeasure).toHaveProperty('MeasureIdentifier')
        }

        // Log results for debugging
        console.log(
          `Found ${results?.length} measures for interval type ${intervalType}`
        )
      }
    })
  })
})
