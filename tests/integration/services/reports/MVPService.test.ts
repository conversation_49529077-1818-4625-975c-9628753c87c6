import { describe, test, expect, jest } from '@jest/globals'
import { MVPService } from '@/services/reports/MVPService'
import { env } from '@/env'
import { createSnowflakeConfiguration } from '@/services/snowflake/SnowflakeHelper'
import { factory } from '@/services/snowflake/SnowflakeRepositoryFactory'

describe('MVPService Integration Tests', () => {
  // Skip all tests if Snowflake account is not defined
  const skipIfNoSnowflake = () => {
    if (!env.SNOWFLAKE_ACCOUNT) {
      console.log('env.SNOWFLAKE_ACCOUNT not defined, skipping...')
      return true
    }
    return false
  }

  // Test parameters
  const testYear = 2024
  const testMVP = 'Focusing on Womens Health'
  const testEntity = 'Bluebird Medical Group'
  const testPeriodType = 'M' // Monthly

  // Create service instance
  const connectionConfig = {
    ...createSnowflakeConfiguration(),
    role: 'PLATFORM_DEV_ROLE',
  }
  const repository = factory(connectionConfig).createMVPSnowflakeRepository()
  const service = new MVPService(repository)

  test('should fetch MVP summary for a specific year', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getMVPSummary(testYear)

    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // If we have results, check structure
    if (result && result.length > 0) {
      const firstItem = result[0]
      expect(firstItem).toHaveProperty('MVP')
      expect(firstItem).toHaveProperty('EntityCount')
      expect(firstItem).toHaveProperty('ProviderCount')
    }
  })

  test('should fetch Quality Score by MVP', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getQualityScoreByMVP(testYear, testPeriodType)

    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // If we have results, check structure
    if (result && result.length > 0) {
      const firstItem = result[0]
      expect(firstItem).toHaveProperty('MVP')
      expect(firstItem).toHaveProperty('Period')
      expect(firstItem).toHaveProperty('Score')
    }
  })

  test('should fetch Score for IA, PI & Quality Measure Type', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getScoreByMeasureType(
      testYear,
      testMVP,
      testEntity
    )

    // Assert
    expect(result).toBeDefined()

    // If we have results, check structure
    if (result) {
      expect(result).toHaveLength(3)
      expect(result[0]).toHaveProperty('Score')
      expect(result[0]).toHaveProperty('Max')
    }
  })

  test('should fetch Overall Weighted Score', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getOverallWeightedScore(
      testYear,
      testMVP,
      testEntity
    )

    // Assert
    expect(result).toBeDefined()

    // If we have results, check structure
    if (result) {
      expect(result).toHaveProperty('Score')
    }
  })

  test('should fetch Quality Measures Score', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getQualityMeasuresScore(
      testYear,
      testMVP,
      testEntity,
      testPeriodType
    )
    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // If we have results, check structure
    if (result && result.length > 0) {
      const firstItem = result[0]
      expect(firstItem).toHaveProperty('MeasureName')
      expect(firstItem).toHaveProperty('Period')
      expect(firstItem).toHaveProperty('Score')
    }
  })

  test('should fetch Provider Performance', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getProviderPerformance(
      testYear,
      testMVP,
      testEntity
    )

    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // If we have results, check structure
    if (result && result.length > 0) {
      const firstItem = result[0]
      expect(firstItem).toHaveProperty('ProviderName')
      expect(firstItem).toHaveProperty('MeasureCount')
      expect(firstItem).toHaveProperty('Points')
    }
  })

  test('should fetch Quality Measures', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getQualityMeasures(
      testYear,
      testMVP,
      testEntity
    )

    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // If we have results, check structure
    if (result && result.length > 0) {
      const firstItem = result[0]
      expect(firstItem).toHaveProperty('MeasureName')
    }
  })

  test('should fetch IA Measures', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getIAPIMeasures(
      testYear,
      testMVP,
      testEntity,
      'IA'
    )

    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // If we have results, check structure
    if (result && result.length > 0) {
      const firstItem = result[0]
      expect(firstItem).toHaveProperty('MeasureName')
    }
  })

  test('should fetch PI Measures', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getIAPIMeasures(
      testYear,
      testMVP,
      testEntity,
      'PI'
    )

    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // If we have results, check structure
    if (result && result.length > 0) {
      const firstItem = result[0]
      expect(firstItem).toHaveProperty('MeasureName')
    }
  })

  test('should fetch MVPs', async () => {
    if (skipIfNoSnowflake()) return

    // Act
    const result = await service.getMVPs()

    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // If we have results, check structure
    if (result && result.length > 0) {
      const firstItem = result[0]
      expect(firstItem).toHaveProperty('MVPCategory')
    }
  })

  test('should fetch Provider Details', async () => {
    if (skipIfNoSnowflake()) return

    const testProviderName = 'Provider1'

    // Act
    const result = await service.getProviderDetails(testYear, testProviderName)

    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // If we have results, check structure
    if (result && result.length > 0) {
      const firstItem = result[0]
      expect(firstItem).toHaveProperty('MeasureName')
      expect(firstItem).toHaveProperty('Numerator')
      expect(firstItem).toHaveProperty('Denominator')
      expect(firstItem).toHaveProperty('Performance')
      expect(firstItem).toHaveProperty('PTile')
      expect(firstItem).toHaveProperty('Points')
    }
  })
})
