import { SelectionType } from '@/enums/selectionType'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'
import { MedisolvReport, MedisolvReportOptions, } from '@/types/reports/medisolvReport'

dayjs.extend(utc)

jest.unstable_mockModule('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => {
    return fn()
  }),
  redisHelper: {
    get: jest.fn(),
    set: jest.fn()
  }
}))

// Mock the modules using jest.mock with a factory function
jest.mock('@/services/reports/getReports', () => ({
  getReports: jest.fn(),
}))

jest.mock('@/services/reports/getReportPreferences', () => ({
  getReportPreferences: jest.fn(),
}))

describe('getMeasureInsightReports', () => {
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  test('should return filtered and sorted reports and dashboards', async () => {
    // Arrange
    const { getReports } = jest.requireMock(
      '@/services/reports/getReports'
    ) as { getReports: jest.Mock }
    const { getReportPreferences } = jest.requireMock(
      '@/services/reports/getReportPreferences'
    ) as { getReportPreferences: jest.Mock }

    const mockReports: MedisolvReport[] = [
      {
        MedisolvReportId: '1',
        MedisolvReportName: 'Statistical Process Control Chart',
        ReportType: '2',
        ReportStatus: 'Active',
        LastUpdatedBy: '',
        LastUpdatedDate: new Date(),
        medisolvReportOptions: [] as MedisolvReportOptions[],
        IsMedisolvReportAssociated: false,
        ReportLevelHoverText: '',
        IsMedisolvOptionRequired: false,
        reportingContentType: 'report',
      },
      {
        MedisolvReportId: '2',
        MedisolvReportName: 'Single Entity Multiple Measure Dashboard',
        ReportType: '2',
        ReportStatus: 'Active',
        LastUpdatedBy: '',
        LastUpdatedDate: new Date(),
        medisolvReportOptions: [],
        IsMedisolvReportAssociated: false,
        ReportLevelHoverText: '',
        IsMedisolvOptionRequired: false,
        reportingContentType: 'dashboard',
      },
      {
        MedisolvReportId: '3',
        MedisolvReportName: 'Single Measure Multiple Entity Dashboard',
        ReportType: '2',
        ReportStatus: 'Active',
        LastUpdatedBy: '',
        LastUpdatedDate: new Date(),
        medisolvReportOptions: [],
        IsMedisolvReportAssociated: false,
        ReportLevelHoverText: '',
        IsMedisolvOptionRequired: false,
        reportingContentType: 'dashboard',
      },
      {
        MedisolvReportId: '4',
        MedisolvReportName: 'Geo Plot Map Dashboard',
        ReportType: '2',
        ReportStatus: 'Active',
        LastUpdatedBy: '',
        LastUpdatedDate: new Date(),
        medisolvReportOptions: [],
        IsMedisolvReportAssociated: false,
        ReportLevelHoverText: '',
        IsMedisolvOptionRequired: false,
        reportingContentType: 'dashboard',
      },
      {
        MedisolvReportId: '5',
        MedisolvReportName: 'SPC Alert Summary',
        ReportType: '2',
        ReportStatus: 'Active',
        LastUpdatedBy: '',
        LastUpdatedDate: new Date(),
        medisolvReportOptions: [],
        IsMedisolvReportAssociated: false,
        ReportLevelHoverText: '',
        IsMedisolvOptionRequired: false,
        reportingContentType: 'report',
      },
      {
        MedisolvReportId: '6',
        MedisolvReportName: 'Measure Stratification Report',
        ReportType: '2',
        ReportStatus: 'Active',
        LastUpdatedBy: '',
        LastUpdatedDate: new Date(),
        medisolvReportOptions: [],
        IsMedisolvReportAssociated: false,
        ReportLevelHoverText: '',
        IsMedisolvOptionRequired: false,
        reportingContentType: 'report',
      },
      {
        MedisolvReportId: '7',
        MedisolvReportName: 'Inactive Report',
        ReportType: '2',
        ReportStatus: 'Inactive',
        LastUpdatedBy: '',
        LastUpdatedDate: new Date(),
        medisolvReportOptions: [],
        IsMedisolvReportAssociated: false,
        ReportLevelHoverText: '',
        IsMedisolvOptionRequired: false,
        reportingContentType: 'report',
      },
    ]

    getReports.mockResolvedValue(mockReports as never)

    getReportPreferences.mockResolvedValue([] as never)


    const { getMeasureInsightReports } = await import('@/services/reports/getMeasureInsightReports')
    const results = await getMeasureInsightReports(
      testOrgId,
      SelectionType.Organization
    )

    // Assert
    expect(results).toBeDefined()
    expect(results).toHaveLength(6) // 1 regular report + 3 dashboards

    // Verify specific reports are included/excluded
    const reportNames = results?.map((r) => r.ReportName)
    expect(reportNames).toContain('Statistical Process Control Chart')
    expect(reportNames).toContain('Single Entity Multiple Measure Dashboard')
    expect(reportNames).toContain('Single Measure Multiple Entity Dashboard')
    expect(reportNames).toContain('Geo Plot Map Dashboard')

    // Verify excluded reports
    expect(reportNames).not.toContain('SPC Alert Summary')
    expect(reportNames).not.toContain('Measure Stratification Report')
    expect(reportNames).not.toContain('Inactive Report')

    // Verify sorting
    expect(results).toEqual(
      results?.sort((a, b) => a.ReportName.localeCompare(b.ReportName))
    )
  })

  test('should exclude disabled reports based on report preferences', async () => {
    // Arrange
    const { getReports } = jest.requireMock(
      '@/services/reports/getReports'
    ) as { getReports: jest.Mock }
    const { getReportPreferences } = jest.requireMock(
      '@/services/reports/getReportPreferences'
    ) as { getReportPreferences: jest.Mock }

    // Mock some reports
    const mockReports = [
      {
        MedisolvReportId: '1',
        MedisolvReportName: 'Regular Report 1',
        ReportStatus: 'Active',
        reportingContentType: 'report',
      },
      {
        MedisolvReportId: '2',
        MedisolvReportName: 'Single Entity Multiple Measure Dashboard',
        ReportStatus: 'Active',
        reportingContentType: 'dashboard',
      },
    ]

    // Mock report preferences that disable 'Regular Report 1'
    const mockPreferences = [
      {
        reportId: '1',
        isActive: false,
      },
    ]

    getReports.mockResolvedValue(mockReports as never)
    getReportPreferences.mockResolvedValue(mockPreferences as never)

    // Act
    const { getMeasureInsightReports } = await import('@/services/reports/getMeasureInsightReports')
    const results = await getMeasureInsightReports(
      testOrgId,
      SelectionType.Organization
    )

    // Assert
    const reportNames = results?.map((r) => r.ReportName)
    expect(reportNames).not.toContain('Regular Report 1')
    expect(reportNames).toContain('Single Entity Multiple Measure Dashboard')
  })

  test('should encode report IDs in base64', async () => {
    // Act
    const { getMeasureInsightReports } = await import('@/services/reports/getMeasureInsightReports')
    const results = await getMeasureInsightReports(
      testOrgId,
      SelectionType.Organization
    )

    // Assert
    results?.forEach((report) => {
      // Verify each ReportId is base64 encoded
      expect(() => atob(report.ReportId)).not.toThrow()
    })
  })
})
