import { factory, closePool } from '@/services/snowflake/SnowflakeRepositoryFactory'
import { env } from '@/env'
import {
    createSnowflakeConfiguration,
    createConnection,
    executeAsAResultSet
} from "@/services/snowflake/SnowflakeHelper"
import { ACOMeasureService } from '@/services/reports/ACOMeasureService'
import fs from 'fs'
import { fileURLToPath } from 'url'
import path from 'path'
import { jest } from '@jest/globals'

beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation(() => { });
    jest.spyOn(console, 'log').mockImplementation(() => { });
});

// Clean up resources after all tests
afterAll(async () => {
    await closePool()
})

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Create a directory for expected results if it doesn't exist
const expectedResultsDir = path.join(__dirname, 'expected-results')
if (!fs.existsSync(expectedResultsDir)) {
    fs.mkdirSync(expectedResultsDir, { recursive: true })
}

// Helper function to log the first row of data
const logFirstRow = (label: string, data: any) => {
    if (Array.isArray(data) && data.length > 0) {
        console.log(`${label} first row:`, data[0])
    } else {
        console.log(`${label}: No data found`)
    }
}

test("should fetch ACO measures from ACOMeasureService", async () => {
    // Skip test if Snowflake account is not defined
    if (!env.SNOWFLAKE_ACCOUNT) {
        console.log("env.SNOWFLAKE_ACCOUNT not defined, skipping...")
        return
    }

    // Arrange
    const connectionConfig = {
        ...createSnowflakeConfiguration(),
        role: 'PLATFORM_DEV_ROLE'
    }

    // First, let's get a valid organization type code
    const connection = createConnection(connectionConfig)

    // Query to get a sample organization type code
    const orgTypeCodes = await executeAsAResultSet(connection,
        `SELECT DISTINCT "Code" FROM "MECA"."OrganizationTypes" LIMIT 1`)
    console.log("Sample organization type code:", orgTypeCodes)

    // Close the connection
    connection.destroy(err => { })

    // Skip if we couldn't find an organization type code
    if (!orgTypeCodes || orgTypeCodes.length === 0 || !orgTypeCodes[0]?.["Code"]) {
        console.log("No organization type codes found, skipping test...")
        return
    }

    const codeNumeric = String(orgTypeCodes[0]["Code"])

    // Create a real ACOMeasureRepository instance using the factory
    const measureRepo = factory(connectionConfig).createACOMeasureRepository()
    const service = new ACOMeasureService(measureRepo)

    // Create request object with all required parameters
    const request = {
        codeNumeric,
        intervalType: 'M' as const, // Monthly interval
        startDate: new Date(new Date().setMonth(new Date().getMonth() - 3)), // 3 months ago
        endDate: new Date() // Current date
    }

    // Act
    const result = await service.getACOMeasures(request)

    // Log first row of data
    logFirstRow('Direct query for organization type code', orgTypeCodes)
    logFirstRow('API result for ACO measures', result)

    // Verify that the API data is valid
    if (result && result.length > 0) {
        // Check that the first item has the expected properties
        expect(result[0]).toHaveProperty('MeasureName')
        expect(result[0]).toHaveProperty('MeasureId')
    }

    // Assert
    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)

    // Check structure of results if we have any
    if (result && result.length > 0) {
        const firstItem = result[0]
        if (firstItem) {
            expect(firstItem).toHaveProperty('MeasureName')
            expect(firstItem).toHaveProperty('MeasureId')
        }
    }
})
