import { jest } from '@jest/globals'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { StorageTables } from '@/enums/storageTables'
import { getReportPreferences } from '@/services/reports/getReportPreferences'
import { SelectionType } from '@/enums/selectionType'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { ManageReport } from '@/types/manageReport'

dayjs.extend(utc)

const SECONDS = 1000

describe('getReportPreferences', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = `test-org-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.ManageReports}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  }, 70 * SECONDS)

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return empty array when no report preferences exist', async () => {
    const results = await getReportPreferences(tableStorageWrapper, testOrgId)
    expect(results).toEqual([])
  })

  test('should return report preferences when they exist', async () => {
    // Arrange
    const testReports: ManageReport[] = [
      {
        partitionKey: testOrgId,
        rowKey: `report1-${dayjs.utc().valueOf()}`,
        organizationId: testOrgId,
        entityId: '1111',
        reportId: '1111',
        isActive: true,
      },
      {
        partitionKey: testOrgId,
        rowKey: `report2-${dayjs.utc().valueOf()}`,
        organizationId: testOrgId,
        entityId: '2222',
        reportId: '2222',
        isActive: true,
      },
    ]

    // Add test reports
    for (const report of testReports) {
      await tableStorageWrapper.insertEntity<ManageReport>(report)
    }

    // Act
    const results = await getReportPreferences(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toHaveLength(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: testOrgId,
          organizationId: testOrgId,
          entityId: '1111',
          reportId: '1111',
          isActive: true,
        }),
        expect.objectContaining({
          partitionKey: testOrgId,
          organizationId: testOrgId,
          entityId: '2222',
          reportId: '2222',
          isActive: true,
        }),
      ])
    )
  })

  test('should only return reports for specified organization', async () => {
    // Arrange
    const otherOrgId = `other-org-${dayjs.utc().valueOf()}`
    const testReports: ManageReport[] = [
      {
        partitionKey: testOrgId,
        rowKey: `report1-${dayjs.utc().valueOf()}`,
        organizationId: testOrgId,
        entityId: '1111',
        reportId: '1111',
        isActive: true,
      },
      {
        partitionKey: otherOrgId,
        rowKey: `report2-${dayjs.utc().valueOf()}`,
        organizationId: testOrgId,
        entityId: '2222',
        reportId: '2222',
        isActive: true,
      },
    ]

    // Add test reports
    for (const report of testReports) {
      await tableStorageWrapper.insertEntity<ManageReport>(report)
    }

    // Act
    const results = await getReportPreferences(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        organizationId: testOrgId,
        entityId: '1111',
        reportId: '1111',
        isActive: true,
      })
    )
  })

  test('should migrate and return report preferences when migrationConfig is provided', async () => {
    // Arrange
    const mockReports = [
      {
        Id: 1,
        OrganizationId: testOrgId,
        EntityId: 'entity1',
        ReportId: 'report1',
        IsActive: true,
      },
      {
        Id: 2,
        OrganizationId: testOrgId,
        EntityId: 'entity2',
        ReportId: 'report2',
        IsActive: false,
      },
    ]

    // Mock Prisma client
    globalThis.prismaClients = {
      [testOrgId]: {
        admClient: {
          manageReports: {
            findMany: jest.fn().mockResolvedValue(mockReports as never),
          },
        },
      },
    } as any

    // Act
    const results = await getReportPreferences(tableStorageWrapper, testOrgId, {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    })

    // Assert
    expect(results).toHaveLength(2)
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: '1',
          entityId: 'entity1',
          reportId: 'report1',
          isActive: true,
        }),
        expect.objectContaining({
          partitionKey: testOrgId,
          rowKey: '2',
          entityId: 'entity2',
          reportId: 'report2',
          isActive: false,
        }),
      ])
    )

    // Verify data was migrated to table storage
    const storedReports = await tableStorageWrapper.queryEntities<ManageReport>(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedReports).toHaveLength(2)
  })

  test('should handle empty results during migration', async () => {
    // Mock empty Prisma results
    globalThis.prismaClients = {
      [testOrgId]: {
        admClient: {
          manageReports: {
            findMany: jest.fn().mockResolvedValue([] as never),
          },
        },
      },
    } as any

    // Act
    const results = await getReportPreferences(tableStorageWrapper, testOrgId, {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    })

    // Assert
    expect(results).toEqual([])
  })

  test('should handle missing prisma client during migration', async () => {
    // Clear mock prisma client
    globalThis.prismaClients = {}

    // Act
    const results = await getReportPreferences(tableStorageWrapper, testOrgId, {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    })

    // Assert
    expect(results).toEqual([])
  })

  test('should return existing report preferences without migration when migrationConfig is not provided', async () => {
    // Arrange
    const existingReport: ManageReport = {
      partitionKey: testOrgId,
      rowKey: '1',
      organizationId: testOrgId,
      entityId: 'entity1',
      reportId: 'report1',
      isActive: true,
    }
    await tableStorageWrapper.insertEntity(existingReport)

    // Act
    const results = await getReportPreferences(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(expect.objectContaining(existingReport))
  })

  test('should migrate when migrationConfig exists and no reports exist in table storage', async () => {
    // Mock SQL results
    const sqlReports = [
      {
        Id: 1,
        OrganizationId: testOrgId,
        EntityId: 'entity1',
        ReportId: 'report1',
        IsActive: true,
      },
    ]

    // Mock prisma client
    globalThis.prismaClients = {
      [testOrgId]: {
        admClient: {
          manageReports: {
            findMany: jest.fn().mockResolvedValue(sqlReports as never),
          },
        },
      },
    } as any

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Verify no reports exist initially
    const initialReports = await tableStorageWrapper.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(initialReports).toHaveLength(0)

    // Act
    const results = await getReportPreferences(
      tableStorageWrapper,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        rowKey: '1',
        organizationId: testOrgId,
        entityId: 'entity1',
        reportId: 'report1',
        isActive: true,
      })
    )

    // Verify reports were migrated to table storage
    const storedReports = await tableStorageWrapper.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedReports).toHaveLength(1)
  })

  test('should not migrate when migrationConfig exists but reports already exist in table storage', async () => {
    // First create some existing reports
    const existingReport = {
      partitionKey: testOrgId,
      rowKey: 'existing-1',
      organizationId: testOrgId,
      entityId: 'existing-entity',
      reportId: 'existing-report',
      isActive: true,
    }
    await tableStorageWrapper.insertEntity(existingReport)

    // Mock SQL results that shouldn't be migrated
    const sqlReports = [
      {
        Id: 'sql-1',
        OrganizationId: testOrgId,
        EntityId: 'sql-entity',
        ReportId: 'sql-report',
        IsActive: true,
      },
    ]

    // Mock prisma client
    const mockFindMany = jest.fn().mockResolvedValue(sqlReports as never)
    globalThis.prismaClients = {
      [testOrgId]: {
        admClient: {
          manageReports: {
            findMany: mockFindMany,
          },
        },
      },
    } as any

    const migrationConfig = {
      organizationId: testOrgId,
      selectionType: SelectionType.Organization,
    }

    // Act
    const results = await getReportPreferences(
      tableStorageWrapper,
      testOrgId,
      migrationConfig
    )

    // Assert
    expect(results).toHaveLength(1)
    expect(results[0]).toEqual(
      expect.objectContaining({
        partitionKey: testOrgId,
        rowKey: 'existing-1',
        organizationId: testOrgId,
        entityId: 'existing-entity',
        reportId: 'existing-report',
      })
    )

    // Verify no new reports were migrated
    const storedReports = await tableStorageWrapper.queryEntities(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(storedReports).toHaveLength(1)
    expect(mockFindMany).not.toHaveBeenCalled()
  })
})
