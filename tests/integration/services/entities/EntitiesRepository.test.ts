
// Clean up resources after all tests
import { closePool, factory } from "@/services/snowflake/SnowflakeRepositoryFactory";
import { createSnowflakeConfiguration } from "@/services/snowflake/SnowflakeHelper";

afterAll(async () => {
    await closePool()
})

describe('EntitiesRepository', () => {
    describe('findByOrganizationIdAndEntityTypeId', () => {
        test('should return entities for organization by ID and entity type ID', async () => {

            // Arrange
            const entitiesRepository = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE'
            }).createEntitiesRepository()
            const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014'
            const entityTypeId = 1 // Facility entity type

            // Act
            const results = await entitiesRepository.findByOrganizationIdAndEntityTypeId(
                organizationId,
                entityTypeId
            )

            // Verify the structure of the returned entities
            expect(results).not.toBeNull();
            expect(results).toHaveLength(4);
            results!.forEach(value => {
                expect(value.SourceContainerIdentifier?.startsWith('cc956396-c75a-43c0-a7fe-e7236dd41014_EH')).toBeTruthy()
                expect(value.EntityTypeId).toEqual(1)
            })
        })

        test('should return empty array when no entities match the criteria', async () => {
            // Arrange
            const entitiesRepository = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE'
            }).createEntitiesRepository()
            const organizationId = 'non-existent-organization-id'
            const entityTypeId = 999 // Non-existent entity type

            // Act
            const results = await entitiesRepository.findByOrganizationIdAndEntityTypeId(
                organizationId,
                entityTypeId
            )

            // Assert
            expect(results).toEqual([])
        })
    })

    describe('findDistinctSourceContainerIdentifier', () => {
        test('should return distinct source container identifiers', async () => {
            // Arrange
            const entitiesRepository = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE'
            }).createEntitiesRepository()

            // Act
            const results = await entitiesRepository.findDistinctSourceContainerIdentifiers()

            // Assert
            expect(results).not.toBeNull()
            expect(results.length).toBeGreaterThan(0)

            // Verify that the results are strings and don't contain _EC or _EH suffixes
            results.forEach(identifier => {
                expect(typeof identifier).toBe('string')
                expect(identifier).not.toContain('_EC')
                expect(identifier).not.toContain('_EH')
            })

            // Verify that there are no duplicates
            const uniqueResults = [...new Set(results)]
            expect(uniqueResults.length).toBe(results.length)
        })
    })

    describe('findAll', () => {
        test('should return all entities with type and organization information', async () => {
            // Arrange
            const entitiesRepository = factory({
                ...createSnowflakeConfiguration(),
                role: 'PLATFORM_DEV_ROLE'
            }).createEntitiesRepository()

            // Act
            const results = await entitiesRepository.findAll()

            // Assert
            expect(results).not.toBeNull()
            expect(results.length).toBeGreaterThan(0)

            // Verify the structure of the returned entities
            const firstEntity = results[0]
            expect(firstEntity).toHaveProperty('Id')
            expect(firstEntity).toHaveProperty('EntityName')
            expect(firstEntity).toHaveProperty('EntityTypeId')
            expect(firstEntity).toHaveProperty('EntityTypeName')
            expect(firstEntity).toHaveProperty('OrganizationTypeId')
            expect(firstEntity).toHaveProperty('OrganizationTypeCode')
            expect(firstEntity).toHaveProperty('SourceContainerIdentifier')

            // Verify that all entities have the required properties
            results.forEach(entity => {
                expect(entity).toHaveProperty('Id')
                expect(entity).toHaveProperty('EntityName')
                expect(entity).toHaveProperty('EntityTypeId')
                expect(entity).toHaveProperty('EntityTypeName')
                expect(entity).toHaveProperty('OrganizationTypeId')
                expect(entity).toHaveProperty('OrganizationTypeCode')
                expect(entity).toHaveProperty('SourceContainerIdentifier')
            })
        })
    })
})
