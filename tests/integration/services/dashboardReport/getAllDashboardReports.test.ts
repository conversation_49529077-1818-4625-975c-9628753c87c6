import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardReport } from '@/types/dashboardReport'
import { getAllDashboardReports } from '@/services/dashboardReport/getAllDashboardReports'
import dayjs from 'dayjs'
import { StorageTables } from '@/enums/storageTables'
import { SelectionType } from '@/enums/selectionType'
import { jest } from '@jest/globals'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('getAllDashboardReports', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = 'test-org-123'

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.DashboardReports}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should return empty array when no reports exist', async () => {
    // Act
    const results = await getAllDashboardReports(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toBeDefined()
    expect(Array.isArray(results)).toBeTruthy()
    expect(results.length).toBe(0)
  })

  test('should return all dashboard reports for an organization', async () => {
    // Arrange
    const reports: DashboardReport[] = [
      {
        partitionKey: testOrgId,
        rowKey: `report-${dayjs.utc().valueOf()}-1`,
        reportName: 'Test Report 1',
        sisenseDashboardId: 'dashboard-123',
        reportType: 1,
        filterSettings: '{}',
        status: true,
        lastUpdatedBy: 'user1',
        lastUpdatedDate: new Date(),
      },
      {
        partitionKey: testOrgId,
        rowKey: `report-${dayjs.utc().valueOf()}-2`,
        reportName: 'Test Report 2',
        sisenseDashboardId: 'dashboard-456',
        reportType: 2,
        filterSettings: '{}',
        status: false,
        lastUpdatedBy: 'user2',
        lastUpdatedDate: new Date(),
      },
    ]

    for (const report of reports) {
      await tableStorageWrapper.insertEntity(report)
    }

    // Act
    const results = await getAllDashboardReports(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toBeDefined()
    expect(results.length).toBe(2)
    expect(results).toEqual(
      expect.arrayContaining(
        reports.map((report) =>
          expect.objectContaining({
            partitionKey: report.partitionKey,
            rowKey: report.rowKey,
            reportName: report.reportName,
            sisenseDashboardId: report.sisenseDashboardId,
            reportType: report.reportType,
            filterSettings: report.filterSettings,
            status: report.status,
            lastUpdatedBy: report.lastUpdatedBy,
            lastUpdatedDate: report.lastUpdatedDate,
          })
        )
      )
    )
  })

  test('should not return reports from other organizations', async () => {
    // Arrange
    const otherOrgId = 'other-org-456'
    const reports: DashboardReport[] = [
      {
        partitionKey: testOrgId,
        rowKey: `report-${dayjs.utc().valueOf()}-1`,
        reportName: 'Test Report 1',
        sisenseDashboardId: 'dashboard-123',
        reportType: 1,
        filterSettings: '{}',
        status: true,
        lastUpdatedBy: 'user1',
        lastUpdatedDate: new Date(),
      },
      {
        partitionKey: otherOrgId,
        rowKey: `report-${dayjs.utc().valueOf()}-2`,
        reportName: 'Test Report 2',
        sisenseDashboardId: 'dashboard-456',
        reportType: 2,
        filterSettings: '{}',
        status: false,
        lastUpdatedBy: 'user2',
        lastUpdatedDate: new Date(),
      },
    ]

    for (const report of reports) {
      await tableStorageWrapper.insertEntity(report)
    }

    // Act
    const results = await getAllDashboardReports(tableStorageWrapper, testOrgId)

    // Assert
    expect(results).toBeDefined()
    expect(results.length).toBe(1)
    expect(results[0]!.partitionKey).toBe(testOrgId)
  })

  describe('with migrationConfig', () => {
    test('should migrate and return dashboard reports when migrationConfig is provided', async () => {
      // Mock SQL results
      const sqlReports = [
        {
          Id: 1,
          ReportName: 'SQL Report 1',
          SisenseDashboardId: 'dashboard-1',
          ReportType: 'type-1',
          FilterSettings: 'filter-1',
          Status: true,
          LastUpdatedBy: 'user-1',
          LastUpdatedDate: new Date(),
        },
        {
          Id: 2,
          ReportName: 'SQL Report 2',
          SisenseDashboardId: 'dashboard-2',
          ReportType: 'type-2',
          FilterSettings: 'filter-2',
          Status: false,
          LastUpdatedBy: 'user-2',
          LastUpdatedDate: new Date(),
        },
      ]

      // Mock prisma client
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardReport: {
              findMany: jest.fn().mockResolvedValue(sqlReports as never),
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardReports(
        tableStorageWrapper,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toHaveLength(2)
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            partitionKey: testOrgId,
            rowKey: '1',
            reportName: 'SQL Report 1',
            sisenseDashboardId: 'dashboard-1',
            reportType: 'type-1',
            filterSettings: 'filter-1',
            status: true,
          }),
          expect.objectContaining({
            partitionKey: testOrgId,
            rowKey: '2',
            reportName: 'SQL Report 2',
            sisenseDashboardId: 'dashboard-2',
            reportType: 'type-2',
            filterSettings: 'filter-2',
            status: false,
          }),
        ])
      )

      // Verify reports were migrated to table storage
      const storedReports = await tableStorageWrapper.queryEntities(
        `PartitionKey eq '${testOrgId}'`
      )
      expect(storedReports).toHaveLength(2)
    })

    test('should handle empty SQL results during migration', async () => {
      // Mock empty SQL results
      globalThis.prismaClients = {
        [testOrgId]: {
          hubClient: {
            dashboardReport: {
              findMany: jest.fn().mockResolvedValue([] as never),
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardReports(
        tableStorageWrapper,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })

    test('should handle missing prisma client during migration', async () => {
      // Clear mock prisma client
      globalThis.prismaClients = {}

      const migrationConfig = {
        organizationId: testOrgId,
        selectionType: SelectionType.Organization,
      }

      // Act
      const results = await getAllDashboardReports(
        tableStorageWrapper,
        testOrgId,
        migrationConfig
      )

      // Assert
      expect(results).toEqual([])
    })
  })
})
