import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { DashboardReport } from '@/types/dashboardReport'
import { upsertDashboardReport } from '@/services/dashboardReport/upsertDashboardReport'
import dayjs from 'dayjs'
import { StorageTables } from '@/enums/storageTables'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('upsertDashboardReport', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testOrgId = 'test-org-123'

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.DashboardReports}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert new dashboard report when it does not exist', async () => {
    // Arrange
    const dashboardReport: DashboardReport = {
      partitionKey: testOrgId,
      rowKey: `report-${dayjs.utc().valueOf()}`,
      name: 'Test Report',
      description: 'Test Description',
      configuration: JSON.stringify({ key: 'value' }),
      dashboardId: 'dashboard-123',
    }

    // Act
    const result = await upsertDashboardReport(
      tableStorageWrapper,
      dashboardReport
    )

    // Assert
    expect(result).toBeDefined()
    expect(result.partitionKey).toBe(dashboardReport.partitionKey)
    expect(result.rowKey).toBe(dashboardReport.rowKey)
    expect(result.name).toBe(dashboardReport.name)
    expect(result.description).toBe(dashboardReport.description)
    expect(result.configuration).toBe(dashboardReport.configuration)
    expect(result.dashboardId).toBe(dashboardReport.dashboardId)

    // Verify in storage
    const reports = await tableStorageWrapper.queryEntities<DashboardReport>(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(reports.length).toBe(1)
    expect(reports[0]).toMatchObject(dashboardReport)
  })

  test('should update existing dashboard report', async () => {
    // Arrange
    const rowKey = `report-${dayjs.utc().valueOf()}`
    const initialReport: DashboardReport = {
      partitionKey: testOrgId,
      rowKey,
      name: 'Initial Report',
      description: 'Initial Description',
      configuration: JSON.stringify({ initial: true }),
      dashboardId: 'dashboard-123',
    }
    await tableStorageWrapper.insertEntity(initialReport)

    const updatedReport: DashboardReport = {
      ...initialReport,
      name: 'Updated Report',
      description: 'Updated Description',
      configuration: JSON.stringify({ updated: true }),
    }

    // Act
    const result = await upsertDashboardReport(
      tableStorageWrapper,
      updatedReport
    )

    // Assert
    expect(result).toBeDefined()
    expect(result.name).toBe(updatedReport.name)
    expect(result.description).toBe(updatedReport.description)
    expect(result.configuration).toBe(updatedReport.configuration)

    // Verify in storage
    const reports = await tableStorageWrapper.queryEntities<DashboardReport>(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(reports.length).toBe(1)
    expect(reports[0]).toMatchObject(updatedReport)
  })

  test('should handle report with minimal required fields', async () => {
    // Arrange
    const minimalReport: DashboardReport = {
      partitionKey: testOrgId,
      rowKey: `report-${dayjs.utc().valueOf()}`,
      name: 'Minimal Report',
      description: '',
      configuration: '{}',
      dashboardId: 'dashboard-123',
    }

    // Act
    const result = await upsertDashboardReport(
      tableStorageWrapper,
      minimalReport
    )

    // Assert
    expect(result).toBeDefined()
    expect(result).toMatchObject(minimalReport)

    // Verify in storage
    const reports = await tableStorageWrapper.queryEntities<DashboardReport>(
      `PartitionKey eq '${testOrgId}'`
    )
    expect(reports.length).toBe(1)
    expect(reports[0]).toMatchObject(minimalReport)
  })
})
