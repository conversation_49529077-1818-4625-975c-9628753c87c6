import { StorageTables } from '@/enums/storageTables'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { getAllUserPreferences } from '@/services/userPreferences/getAllUserPreferences'
import { UserPreference } from '@/types/userPreference'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { jest } from '@jest/globals'
import { SelectionType } from '@/enums/selectionType'

dayjs.extend(utc)

let tableStorageWrapper: AzureTableStorageWrapper
const testUserId = `test-user-${dayjs.utc().valueOf()}`

beforeEach(async () => {
  tableStorageWrapper = new AzureTableStorageWrapper(
    `${StorageTables.UserPreferences}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
  )
  await tableStorageWrapper.createTable()
})

afterEach(async () => {
  await tableStorageWrapper.deleteTable()
})

test('should return all user preferences', async () => {
  // Insert test preferences
  await tableStorageWrapper.insertEntity<UserPreference>({
    partitionKey: testUserId,
    rowKey: 'preference1',
    userId: testUserId,
    defaultModuleId: 'measures',
    dashboardEnabledOverride: false,
  })

  await tableStorageWrapper.insertEntity<UserPreference>({
    partitionKey: testUserId,
    rowKey: 'preference2',
    userId: testUserId,
    defaultModuleId: 'dashboards',
    dashboardEnabledOverride: true,
  })

  // Get all preferences
  const results = await getAllUserPreferences(tableStorageWrapper, testUserId)

  // Assertions
  expect(results).not.toBeNull()
  expect(results.length).toBe(2)

  // Verify first preference
  const result1 = results.find((p) => p.rowKey === 'preference1')
  expect(result1).toBeDefined()
  expect(result1?.partitionKey).toBe(testUserId)
  expect(result1?.dashboardEnabledOverride).toBe(false)
  expect(result1?.defaultModuleId).toBe('measures')

  // Verify second preference
  const result2 = results.find((p) => p.rowKey === 'preference2')
  expect(result2).toBeDefined()
  expect(result2?.partitionKey).toBe(testUserId)
  expect(result2?.dashboardEnabledOverride).toBe(true)
  expect(result2?.defaultModuleId).toBe('dashboards')
})

test('should return empty array when no preferences exist', async () => {
  const results = await getAllUserPreferences(tableStorageWrapper, testUserId)

  expect(results).not.toBeNull()
  expect(results).toHaveLength(0)
})

test('should migrate and return user preferences when migrationConfig is provided', async () => {
  // Mock SQL results
  const sqlPreferences = [
    {
      Id: 1,
      UserId: testUserId,
      DefaultModuleId: 'measures',
      DashboardEnabledOverride: false,
    },
    {
      Id: 2,
      UserId: testUserId,
      DefaultModuleId: 'dashboards',
      DashboardEnabledOverride: true,
    },
  ]

  // Mock prisma client
  const testOrgId = 'test-org-id'
  globalThis.prismaClients = {
    [testOrgId]: {
      admClient: {
        userPreferences: {
          findMany: jest.fn().mockResolvedValue(sqlPreferences as never),
        },
      },
    },
  } as any

  const migrationConfig = {
    organizationId: testOrgId,
    selectionType: SelectionType.Organization,
  }

  // Act
  const results = await getAllUserPreferences(
    tableStorageWrapper,
    testUserId,
    migrationConfig
  )

  // Assert
  expect(results).toHaveLength(2)
  expect(results).toEqual(
    expect.arrayContaining([
      expect.objectContaining({
        partitionKey: testUserId,
        rowKey: '1',
        userId: testUserId,
        defaultModuleId: 'measures',
        dashboardEnabledOverride: false,
      }),
      expect.objectContaining({
        partitionKey: testUserId,
        rowKey: '2',
        userId: testUserId,
        defaultModuleId: 'dashboards',
        dashboardEnabledOverride: true,
      }),
    ])
  )

  // Verify preferences were migrated to table storage
  const storedPreferences =
    await tableStorageWrapper.queryEntities<UserPreference>(
      `PartitionKey eq '${testUserId}'`
    )
  expect(storedPreferences).toHaveLength(2)
})

test('should handle empty SQL results during migration', async () => {
  // Mock empty SQL results
  const testOrgId = 'test-org-id'
  globalThis.prismaClients = {
    [testOrgId]: {
      admClient: {
        userPreferences: {
          findMany: jest.fn().mockResolvedValue([] as never),
        },
      },
    },
  } as any

  const migrationConfig = {
    organizationId: testOrgId,
    selectionType: SelectionType.Organization,
  }

  // Act
  const results = await getAllUserPreferences(
    tableStorageWrapper,
    testUserId,
    migrationConfig
  )

  // Assert
  expect(results).toEqual([])
})

test('should handle missing prisma client during migration', async () => {
  // Clear mock prisma client
  globalThis.prismaClients = {}

  const migrationConfig = {
    organizationId: 'test-org-id',
    selectionType: SelectionType.Organization,
  }

  // Act
  const results = await getAllUserPreferences(
    tableStorageWrapper,
    testUserId,
    migrationConfig
  )

  // Assert
  expect(results).toEqual([])
})

test('should migrate when migrationConfig exists and no preferences exist in table storage', async () => {
  // Mock SQL results
  const sqlPreferences = [
    {
      Id: 1,
      UserId: testUserId,
      DefaultModuleId: 'measures',
      DashboardEnabledOverride: false,
    },
  ]

  const testOrgId = 'test-org-id'
  // Mock prisma client
  globalThis.prismaClients = {
    [testOrgId]: {
      admClient: {
        userPreferences: {
          findMany: jest.fn().mockResolvedValue(sqlPreferences as never),
        },
      },
    },
  } as any

  const migrationConfig = {
    organizationId: testOrgId,
    selectionType: SelectionType.Organization,
  }

  // Verify no preferences exist initially
  const initialPreferences = await tableStorageWrapper.queryEntities(
    `PartitionKey eq '${testUserId}'`
  )
  expect(initialPreferences).toHaveLength(0)

  // Act
  const results = await getAllUserPreferences(
    tableStorageWrapper,
    testUserId,
    migrationConfig
  )

  // Assert
  expect(results).toHaveLength(1)
  expect(results[0]).toEqual(
    expect.objectContaining({
      partitionKey: testUserId,
      rowKey: '1',
      userId: testUserId,
      defaultModuleId: 'measures',
      dashboardEnabledOverride: false,
    })
  )

  // Verify preferences were migrated to table storage
  const storedPreferences = await tableStorageWrapper.queryEntities(
    `PartitionKey eq '${testUserId}'`
  )
  expect(storedPreferences).toHaveLength(1)
})

test('should not migrate when migrationConfig exists but preferences already exist in table storage', async () => {
  // First create some existing preferences
  const existingPreference = {
    partitionKey: testUserId,
    rowKey: 'existing-1',
    userId: testUserId,
    defaultModuleId: 'existing-module',
    dashboardEnabledOverride: true,
  }
  await tableStorageWrapper.insertEntity(existingPreference)

  // Mock SQL results that shouldn't be migrated
  const sqlPreferences = [
    {
      Id: 1,
      UserId: testUserId,
      DefaultModuleId: 'sql-module',
      DashboardEnabledOverride: false,
    },
  ]

  const testOrgId = 'test-org-id'
  // Mock prisma client
  const mockFindMany = jest.fn().mockResolvedValue(sqlPreferences as never)
  globalThis.prismaClients = {
    [testOrgId]: {
      admClient: {
        userPreferences: {
          findMany: mockFindMany,
        },
      },
    },
  } as any

  const migrationConfig = {
    organizationId: testOrgId,
    selectionType: SelectionType.Organization,
  }

  // Act
  const results = await getAllUserPreferences(
    tableStorageWrapper,
    testUserId,
    migrationConfig
  )

  // Assert
  expect(results).toHaveLength(1)
  expect(results[0]).toEqual(
    expect.objectContaining({
      partitionKey: testUserId,
      rowKey: 'existing-1',
      userId: testUserId,
      defaultModuleId: 'existing-module',
      dashboardEnabledOverride: true,
    })
  )

  // Verify SQL query was not called
  expect(mockFindMany).not.toHaveBeenCalled()

  // Verify no additional preferences were migrated
  const storedPreferences = await tableStorageWrapper.queryEntities(
    `PartitionKey eq '${testUserId}'`
  )
  expect(storedPreferences).toHaveLength(1)
})
