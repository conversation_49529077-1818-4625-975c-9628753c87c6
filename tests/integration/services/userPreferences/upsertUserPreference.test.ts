import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { upsertUserPreference } from '@/services/userPreferences/upsertUserPreference'
import { UserPreference } from '@/types/userPreference'
import { StorageTables } from '@/enums/storageTables'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

describe('upsertUserPreference', () => {
  let tableStorageWrapper: AzureTableStorageWrapper
  const testUserId = `test-user-${dayjs.utc().valueOf()}`

  beforeEach(async () => {
    tableStorageWrapper = new AzureTableStorageWrapper(
      `${StorageTables.UserPreferences}IntegrationTest-${dayjs.utc().valueOf()}` as StorageTables
    )
    await tableStorageWrapper.createTable()
  })

  afterEach(async () => {
    await tableStorageWrapper.deleteTable()
  })

  test('should insert new preference when it does not exist', async () => {
    // Arrange
    const testPreference: UserPreference = {
      partitionKey: testUserId,
      rowKey: `pref-${dayjs.utc().valueOf()}`,
      userId: testUserId,
      defaultModuleId: 'measures',
      dashboardEnabledOverride: false,
      key: 'testKey',
      value: 'testValue',
    }

    // Act
    await upsertUserPreference(tableStorageWrapper, testPreference)

    // Assert
    const preferences = await tableStorageWrapper.queryEntities<UserPreference>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(preferences.length).toBe(1)

    // Destructure to remove system-generated fields
    const { etag, timestamp, ...actualPreference } =
      preferences[0] as UserPreference
    expect(actualPreference).toStrictEqual(testPreference)
  })

  test('should update existing preference when it exists', async () => {
    // Arrange
    const preferenceId = `pref-${dayjs.utc().valueOf()}`
    const initialPreference: UserPreference = {
      partitionKey: testUserId,
      rowKey: preferenceId,
      userId: testUserId,
      defaultModuleId: 'measures',
      dashboardEnabledOverride: false,
    }

    // Insert initial preference
    await tableStorageWrapper.insertEntity<UserPreference>(initialPreference)

    // Create updated preference
    const updatedPreference: UserPreference = {
      ...initialPreference,
      defaultModuleId: 'dashboards', // Changed from value to defaultModuleId since value isn't in the type
    }

    // Act
    await upsertUserPreference(tableStorageWrapper, updatedPreference)

    // Assert
    const preferences = await tableStorageWrapper.queryEntities<UserPreference>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(preferences.length).toBe(1)
    expect(preferences[0]).toMatchObject({
      partitionKey: testUserId,
      rowKey: preferenceId,
      userId: testUserId,
      defaultModuleId: 'dashboards',
      dashboardEnabledOverride: false,
    })
  })

  test('should handle multiple preferences for the same user', async () => {
    // Arrange
    const timestamp1 = dayjs.utc().valueOf()
    const timestamp2 = dayjs.utc().valueOf()
    const testPreferences: UserPreference[] = [
      {
        partitionKey: testUserId,
        rowKey: `pref1-${timestamp1}`,
        userId: testUserId,
        defaultModuleId: 'measures',
        dashboardEnabledOverride: false,
      },
      {
        partitionKey: testUserId,
        rowKey: `pref2-${timestamp2}`,
        userId: testUserId,
        defaultModuleId: 'dashboards',
        dashboardEnabledOverride: true,
      },
    ]

    // Act
    for (const preference of testPreferences) {
      await upsertUserPreference(tableStorageWrapper, preference)
    }

    // Assert
    const preferences = await tableStorageWrapper.queryEntities<UserPreference>(
      `PartitionKey eq '${testUserId}'`
    )
    expect(preferences.length).toBe(2)

    // Only check for specific fields we care about, ignoring etag and timestamp
    const expectedFields = preferences.map(
      ({
        partitionKey,
        userId,
        defaultModuleId,
        dashboardEnabledOverride,
      }) => ({
        partitionKey,
        userId,
        defaultModuleId,
        dashboardEnabledOverride,
      })
    )

    expect(expectedFields).toEqual(
      expect.arrayContaining([
        {
          partitionKey: testUserId,
          userId: testUserId,
          defaultModuleId: 'measures',
          dashboardEnabledOverride: false,
        },
        {
          partitionKey: testUserId,
          userId: testUserId,
          defaultModuleId: 'dashboards',
          dashboardEnabledOverride: true,
        },
      ])
    )
  })
})
