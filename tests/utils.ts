import { UserNotification } from '@/types/userNotification'

// Generate 20 notifications
const messages = [
  'New application submitted',
  'Your application has been approved',
  'Application update available',
  'Application has been rejected',
  'Your profile has been updated',
  'Password changed successfully',
  'New comment on your application',
  'Your document has been uploaded',
  'Notification settings updated',
  'You have a new message',
  'Payment has been processed',
  'Invoice has been generated',
  'Meeting is scheduled',
  'Reminder: Complete your application',
  'Action required on your submission',
  'Verification email sent',
  'Your subscription is active',
  'Subscription renewed',
  'Profile picture updated',
  'New login detected',
]

export const generateTestNotifications = (
  length: number = 20
): UserNotification[] => {
  return Array.from({length}, (_, i) => ({
    Id: (i + 1).toString(),
    Message: messages[Math.floor(Math.random() * messages.length)],
    ReadDateTime: Math.random() > 0.66
        ? new Date(
            new Date().setDate(
                new Date().getDate() - Math.floor(Math.random() * 10)
            )
        ).toString()
        : undefined,
    SentDateTime: new Date(
        new Date().setDate(
            new Date().getDate() - (5 + Math.floor(Math.random() * 10))
        )
    ).toString(),
  })) as unknown as UserNotification[]
}
