import { jest } from '@jest/globals'

const originalConsoleLog = console.log
console.log = jest.fn()

// Mock dependencies
jest.mock('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => fn()),
  redisHelper: {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
  },
}))

jest.mock('@/lib/applicationInsights', () => ({
  trackEvent: jest.fn(),
  trackTrace: jest.fn(),
}))

// Mock the env module to avoid the import error
jest.mock('@/env', () => ({
  env: {
    NEXT_PUBLIC_MAPLE_SERVER_URL: 'http://localhost:3000',
    REDIS_CACHE_TIME_DURATION_SECONDS: '3600',
  },
}))

// Mock the config
jest.mock('@/config', () => ({
  config: {
    customMeasures: {
      excludedMeasuresList: ['EXCLUDED-MEASURE-1', 'EXCLUDED-MEASURE-2'],
    },
  },
}))

// Mock the dependencies we need to test
jest.mock('@/prisma/activeMeasuresQuery', () => ({
  activeMeasuresQuery: jest.fn(),
}))

jest.mock('@/services/maple/mapleMeasuresQuery', () => ({
  mapleMeasureQuery: jest.fn(),
}))

jest.mock('@/services/performanceGoals/getDefaultEntities', () => ({
  getDefaultEntities: jest.fn(),
}))

// Import after mocking
import { getMeasuresForPerformanceGoals } from '@/services/performanceGoals/getMeasuresForPerformanceGoals'
import { activeMeasuresQuery } from '@/prisma/activeMeasuresQuery'
import { mapleMeasureQuery } from '@/services/maple/mapleMeasuresQuery'
import { getDefaultEntities } from '@/services/performanceGoals/getDefaultEntities'
import MapleMeasuresService from '@/services/mapleMeasures'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { INotation } from '@/enums/iNotation'

afterAll(() => {
  console.log = originalConsoleLog
})

describe('getMeasuresForPerformanceGoals', () => {
  // Create mock services
  const mockMeasureResultsService = {
    // @ts-ignore
    findAllMeasures: jest.fn().mockResolvedValue([]),
    // @ts-ignore
    findAllDetailedActiveMeasures: jest.fn().mockResolvedValue([]),
  }

  // Test data
  const mockOrganizationId = 'org-123'
  const mockAccessToken = 'mock-access-token'
  const mockIsPartner = false

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should match lowercase maple measure GUIDs with uppercase active measure GUIDs', async () => {
    // Create a lowercase GUID for maple measures
    const lowercaseGuid = '12345678-abcd-1234-abcd-1234567890ab'

    // Create an uppercase version of the same GUID for measure results
    const uppercaseGuid = lowercaseGuid.toUpperCase()

    // Setup mock for activeMeasuresQuery to return uppercase GUIDs
    // @ts-ignore
    ;(activeMeasuresQuery as jest.Mock).mockResolvedValue([
      {
        measureIdentifier: uppercaseGuid,
        measureName: 'Test Measure',
        measureSubId: 'sub-123',
        strata: 'strata-1',
        denominatorQualifyingType: 'qualifying-type-1',
        processingDate: new Date('2023-01-01'),
        sourceContainerIdentifier: 'source-123',
      },
    ])

    // Setup mock for mapleMeasureQuery to return lowercase GUIDs
    const mockMapleMeasures = [
      {
        measureIdentifier: lowercaseGuid,
        measureName: 'Test Measure',
        measureDescription: 'Test Measure Description',
        measureFriendlyName: 'Test Friendly Name',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Hospital eMeasures',
      },
    ]
    // @ts-ignore
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(mockMapleMeasures)

    // Setup mock for getDefaultEntities
    const mockDefaultEntities = {
      [PrimaryMeasureTypeConstants.HospitalMeasures]: 'entity-123',
    }
    // @ts-ignore
    ;(getDefaultEntities as jest.Mock).mockResolvedValue(mockDefaultEntities)

    // Execute the function under test
    const result = await getMeasuresForPerformanceGoals(
      mockOrganizationId,
      mockMeasureResultsService as any,
      mockIsPartner,
      mockAccessToken
    )

    // Verify the mocks were called with the expected arguments
    expect(activeMeasuresQuery).toHaveBeenCalledWith(
      mockMeasureResultsService,
      expect.any(Object), // MapleMeasuresService instance
      mockOrganizationId
    )
    expect(mapleMeasureQuery).toHaveBeenCalledWith(
      expect.any(Object),
      mockOrganizationId
    )
    expect(getDefaultEntities).toHaveBeenCalledWith(
      mockOrganizationId,
      mockIsPartner,
      mockAccessToken,
      [PrimaryMeasureTypeConstants.HospitalMeasures],
      mockMeasureResultsService
    )

    // Verify the result contains the expected data
    expect(result).toHaveLength(1)
    expect(result[0]).toEqual({
      measureIdentifier: uppercaseGuid,
      measureName: 'Test Measure',
      measureDescription: 'Test Measure Description',
      domainName: 'Test Domain',
      iNotationName: INotation.Higher,
      measureFriendlyName: 'Test Friendly Name',
      entityId: 'entity-123',
      applicationName: 'Hospital eMeasures',
    })
  })

  test('should filter out excluded measures', async () => {
    // Create GUIDs for regular and excluded measures
    const regularGuidLower = '12345678-abcd-1234-abcd-1234567890ab'
    const regularGuidUpper = regularGuidLower.toUpperCase()

    const excludedGuidLower = 'excluded-measure-1'
    const excludedGuidUpper = excludedGuidLower.toUpperCase()

    // Setup mock for activeMeasuresQuery to return both measures
    // @ts-ignore
    ;(activeMeasuresQuery as jest.Mock).mockResolvedValue([
      {
        measureIdentifier: regularGuidUpper,
        measureName: 'Regular Measure',
        measureSubId: 'sub-123',
        strata: 'strata-1',
        denominatorQualifyingType: 'qualifying-type-1',
        processingDate: new Date('2023-01-01'),
        sourceContainerIdentifier: 'source-123',
      },
      {
        measureIdentifier: excludedGuidUpper,
        measureName: 'Excluded Measure',
        measureSubId: 'sub-456',
        strata: 'strata-2',
        denominatorQualifyingType: 'qualifying-type-2',
        processingDate: new Date('2023-01-01'),
        sourceContainerIdentifier: 'source-456',
      },
    ])

    // Setup mock for mapleMeasureQuery to return both measures
    const mockMapleMeasures = [
      {
        measureIdentifier: regularGuidLower,
        measureName: 'Regular Measure',
        measureDescription: 'Regular Measure Description',
        measureFriendlyName: 'Regular Friendly Name',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Hospital eMeasures',
      },
      {
        measureIdentifier: excludedGuidLower,
        measureName: 'Excluded Measure',
        measureDescription: 'Excluded Measure Description',
        measureFriendlyName: 'Excluded Friendly Name',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Hospital eMeasures',
      },
    ]
    // @ts-ignore
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(mockMapleMeasures)

    // Setup mock for getDefaultEntities
    const mockDefaultEntities = {
      [PrimaryMeasureTypeConstants.HospitalMeasures]: 'entity-123',
    }
    // @ts-ignore
    ;(getDefaultEntities as jest.Mock).mockResolvedValue(mockDefaultEntities)

    // Execute the function under test
    const result = await getMeasuresForPerformanceGoals(
      mockOrganizationId,
      mockMeasureResultsService as any,
      mockIsPartner,
      mockAccessToken
    )

    // Verify the result only contains the non-excluded measure
    expect(result).toHaveLength(1)
    expect(result[0]!.measureIdentifier).toBe(regularGuidUpper)
    expect(result[0]!.measureName).toBe('Regular Measure')
  })

  test('should handle measures with different application names', async () => {
    // Create GUIDs for hospital and ambulatory measures
    const hospitalGuidLower = 'hospital-measure-1'
    const hospitalGuidUpper = hospitalGuidLower.toUpperCase()

    const ambulatoryGuidLower = 'ambulatory-measure-1'
    const ambulatoryGuidUpper = ambulatoryGuidLower.toUpperCase()

    // Setup mock for activeMeasuresQuery to return both measures
    // @ts-ignore
    ;(activeMeasuresQuery as jest.Mock).mockResolvedValue([
      {
        measureIdentifier: hospitalGuidUpper,
        measureName: 'Hospital Measure',
        measureSubId: 'sub-123',
        strata: 'strata-1',
        denominatorQualifyingType: 'qualifying-type-1',
        processingDate: new Date('2023-01-01'),
        sourceContainerIdentifier: 'source-123',
      },
      {
        measureIdentifier: ambulatoryGuidUpper,
        measureName: 'Ambulatory Measure',
        measureSubId: 'sub-456',
        strata: 'strata-2',
        denominatorQualifyingType: 'qualifying-type-2',
        processingDate: new Date('2023-01-01'),
        sourceContainerIdentifier: 'source-456',
      },
    ])

    // Setup mock for mapleMeasureQuery to return both measures with different application names
    const mockMapleMeasures = [
      {
        measureIdentifier: hospitalGuidLower,
        measureName: 'Hospital Measure',
        measureDescription: 'Hospital Measure Description',
        measureFriendlyName: 'Hospital Friendly Name',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Hospital eMeasures',
      },
      {
        measureIdentifier: ambulatoryGuidLower,
        measureName: 'Ambulatory Measure',
        measureDescription: 'Ambulatory Measure Description',
        measureFriendlyName: 'Ambulatory Friendly Name',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Ambulatory eMeasures',
      },
    ]
    // @ts-ignore
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(mockMapleMeasures)

    // Setup mock for getDefaultEntities with different entity IDs for different measure types
    const mockDefaultEntities = {
      [PrimaryMeasureTypeConstants.HospitalMeasures]: 'hospital-entity-123',
      [PrimaryMeasureTypeConstants.AmbulatoryMeasures]: 'ambulatory-entity-456',
    }
    // @ts-ignore
    ;(getDefaultEntities as jest.Mock).mockResolvedValue(mockDefaultEntities)

    // Execute the function under test
    const result = await getMeasuresForPerformanceGoals(
      mockOrganizationId,
      mockMeasureResultsService as any,
      mockIsPartner,
      mockAccessToken
    )

    // Verify the result contains both measures with the correct entity IDs
    expect(result).toHaveLength(2)

    // Find the hospital measure in the results
    const hospitalMeasure = result.find(
      (m) => m.measureIdentifier === hospitalGuidUpper
    )
    expect(hospitalMeasure).toBeDefined()
    expect(hospitalMeasure?.entityId).toBe('hospital-entity-123')

    // Find the ambulatory measure in the results
    const ambulatoryMeasure = result.find(
      (m) => m.measureIdentifier === ambulatoryGuidUpper
    )
    expect(ambulatoryMeasure).toBeDefined()
    expect(ambulatoryMeasure?.entityId).toBe('ambulatory-entity-456')
  })

  test('should handle duplicate measure identifiers (case-insensitive)', async () => {
    // Create multiple versions of the same GUID with different cases
    const guid1Lower = 'duplicate-measure-1'
    const guid1Upper = guid1Lower.toUpperCase()
    const guid1Mixed = 'Duplicate-Measure-1'

    // Setup mock for activeMeasuresQuery to return the measure
    // @ts-ignore
    ;(activeMeasuresQuery as jest.Mock).mockResolvedValue([
      {
        measureIdentifier: guid1Upper,
        measureName: 'Duplicate Measure',
        measureSubId: 'sub-123',
        strata: 'strata-1',
        denominatorQualifyingType: 'qualifying-type-1',
        processingDate: new Date('2023-01-01'),
        sourceContainerIdentifier: 'source-123',
      },
    ])

    // Setup mock for mapleMeasureQuery to return multiple versions of the same measure
    const mockMapleMeasures = [
      {
        measureIdentifier: guid1Lower,
        measureName: 'Duplicate Measure 1',
        measureDescription: 'Duplicate Measure Description 1',
        measureFriendlyName: 'Duplicate Friendly Name 1',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Hospital eMeasures',
      },
      {
        measureIdentifier: guid1Mixed,
        measureName: 'Duplicate Measure 2',
        measureDescription: 'Duplicate Measure Description 2',
        measureFriendlyName: 'Duplicate Friendly Name 2',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Hospital eMeasures',
      },
    ]
    // @ts-ignore
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(mockMapleMeasures)

    // Setup mock for getDefaultEntities
    const mockDefaultEntities = {
      [PrimaryMeasureTypeConstants.HospitalMeasures]: 'entity-123',
    }
    // @ts-ignore
    ;(getDefaultEntities as jest.Mock).mockResolvedValue(mockDefaultEntities)

    // Execute the function under test
    const result = await getMeasuresForPerformanceGoals(
      mockOrganizationId,
      mockMeasureResultsService as any,
      mockIsPartner,
      mockAccessToken
    )

    // Verify the result contains only one measure (deduplicated)
    expect(result).toHaveLength(1)
    expect(result[0]!.measureIdentifier).toBe(guid1Upper)
    // The first matching measure's details should be used
    expect(result[0]!.measureName).toBe('Duplicate Measure 1')
  })

  test('should sort measures by name', async () => {
    // Create GUIDs for measures with different names
    const guid1Lower = 'measure-b'
    const guid1Upper = guid1Lower.toUpperCase()

    const guid2Lower = 'measure-a'
    const guid2Upper = guid2Lower.toUpperCase()

    const guid3Lower = 'measure-c'
    const guid3Upper = guid3Lower.toUpperCase()

    // Setup mock for activeMeasuresQuery to return measures in random order
    // @ts-ignore
    ;(activeMeasuresQuery as jest.Mock).mockResolvedValue([
      {
        measureIdentifier: guid1Upper,
        measureName: 'Measure B',
        measureSubId: 'sub-123',
        strata: 'strata-1',
        denominatorQualifyingType: 'qualifying-type-1',
        processingDate: new Date('2023-01-01'),
        sourceContainerIdentifier: 'source-123',
        applicationName: 'Hospital eMeasures',
      },
      {
        measureIdentifier: guid2Upper,
        measureName: 'Measure A',
        measureSubId: 'sub-456',
        strata: 'strata-2',
        denominatorQualifyingType: 'qualifying-type-2',
        processingDate: new Date('2023-01-01'),
        sourceContainerIdentifier: 'source-456',
        applicationName: 'Hospital eMeasures',
      },
      {
        measureIdentifier: guid3Upper,
        measureName: 'Measure C',
        measureSubId: 'sub-789',
        strata: 'strata-3',
        denominatorQualifyingType: 'qualifying-type-3',
        processingDate: new Date('2023-01-01'),
        sourceContainerIdentifier: 'source-789',
        applicationName: 'Hospital eMeasures',
      },
    ])

    // Setup mock for mapleMeasureQuery to return measures in random order
    const mockMapleMeasures = [
      {
        measureIdentifier: guid1Lower,
        measureName: 'Measure B',
        measureDescription: 'Measure B Description',
        measureFriendlyName: 'B Friendly Name',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Hospital eMeasures',
      },
      {
        measureIdentifier: guid2Lower,
        measureName: 'Measure A',
        measureDescription: 'Measure A Description',
        measureFriendlyName: 'A Friendly Name',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Hospital eMeasures',
      },
      {
        measureIdentifier: guid3Lower,
        measureName: 'Measure C',
        measureDescription: 'Measure C Description',
        measureFriendlyName: 'C Friendly Name',
        domainName: 'Test Domain',
        iNotationName: INotation.Higher,
        applicationName: 'Hospital eMeasures',
      },
    ]
    // @ts-ignore
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(mockMapleMeasures)

    // Setup mock for getDefaultEntities
    const mockDefaultEntities = {
      [PrimaryMeasureTypeConstants.HospitalMeasures]: 'entity-123',
    }
    // @ts-ignore
    ;(getDefaultEntities as jest.Mock).mockResolvedValue(mockDefaultEntities)

    // Execute the function under test
    const result = await getMeasuresForPerformanceGoals(
      mockOrganizationId,
      mockMeasureResultsService as any,
      mockIsPartner,
      mockAccessToken
    )

    // Verify the result contains measures sorted by name
    expect(result).toHaveLength(3)
    expect(result[0]!.measureName).toBe('Measure A')
    expect(result[1]!.measureName).toBe('Measure B')
    expect(result[2]!.measureName).toBe('Measure C')
  })
})
