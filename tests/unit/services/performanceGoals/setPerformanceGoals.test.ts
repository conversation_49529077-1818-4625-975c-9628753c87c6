import { jest } from '@jest/globals'
import dayjs from 'dayjs'

// Mock dependencies
jest.mock('@/services/azure/tableStorageWrapper', () => ({
    AzureTableStorageWrapper: jest.fn().mockImplementation(() => ({
        generateODataQuery: jest.fn(),
        queryEntities: jest.fn(),
    })),
}))

const getAllPerformanceGoals = jest.fn();
jest.mock('@/services/performanceGoals/getAllPerformanceGoals', () => ({
    getAllPerformanceGoals
}))

// Import after mocking
import { setPerformanceGoals } from '@/services/performanceGoals/setPerformanceGoals'
import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { ScorecardResult } from '@/types/scorecards/scorecards'
import { ScorecardView } from '@/enums/scorecardView'
import { SelectionType } from '@/enums/selectionType'
import { PerformanceGoal } from '@/types/scorecards/performanceGoal'
import { INotation } from '@/enums/iNotation'
import { Performance } from '@/enums/performance'
import { StorageTables } from '@/enums/storageTables'

describe('setPerformanceGoals', () => {
    // Test data
    const mockOrganizationId = 'org-123'
    const mockUserId = 'user-123'
    const mockSelectionType = SelectionType.Organization

    beforeEach(() => {
        jest.clearAllMocks()
    })

    test('should set performance goals for scorecard results', async () => {
        // Setup mock data
        const mockMeasureId = 'measure-123'
        const mockStartDate = dayjs('2023-01-01')
        const mockEndDate = dayjs('2023-01-31')

        // Create mock scorecard results
        const mockScorecardResults: ScorecardResult[] = [
            {
                measureIdentifier: mockMeasureId,
                measureTitle: 'Test Measure',
                inotation: INotation.Higher,
                scorecardDetailsList: [
                    {
                        id: 1,
                        columnName: 'Jan 2023',
                        rate: 75,
                        startDate: mockStartDate,
                        endDate: mockEndDate,
                    },
                ],
            },
        ]

        // Create mock performance goals
        const mockPerformanceGoals: PerformanceGoal[] = [
            {
                partitionKey: mockOrganizationId,
                rowKey: 'goal-123',
                measureIdentifier: mockMeasureId,
                entityId: 'entity-123',
                startDate: mockStartDate.toISOString(),
                endDate: mockEndDate.toISOString(),
                goalLower: 70,
                goalUpper: 80,
                yellowZone: 60,
                isYellowZoneFixedNumber: true,
                exceptionalPerformance: 90,
                isExceptionalPerformanceNumber: true,
            },
        ]

        // @ts-ignore
        getAllPerformanceGoals.mockResolvedValue(mockPerformanceGoals)

        // Execute the function
        const result = await setPerformanceGoals(
            mockOrganizationId,
            mockScorecardResults,
            ScorecardView.Monthly,
            mockUserId,
            mockSelectionType
        )

        // Verify the mocks were called with the expected arguments
        expect(AzureTableStorageWrapper).toHaveBeenCalledWith(StorageTables.PerformanceGoals)
        expect(getAllPerformanceGoals).toHaveBeenCalledWith(
            expect.any(Object),
            mockOrganizationId,
            mockUserId,
            undefined,
            undefined,
            {
                organizationId: mockOrganizationId,
                selectionType: mockSelectionType,
            }
        )

        // Verify the result contains the expected data
        expect(result).toHaveLength(1)
        expect(result[0]?.measureIdentifier).toBe(mockMeasureId)
        expect(result[0]?.scorecardDetailsList?.[0]?.goal).toBe(70)
        expect(result[0]?.scorecardDetailsList?.[0]?.performance).toBe(Performance.Good)
    })

    test('should handle case where no performance goals match', async () => {
        // Setup mock data
        const mockMeasureId = 'measure-123'
        const mockStartDate = dayjs('2023-01-01')
        const mockEndDate = dayjs('2023-01-31')

        // Create mock scorecard results
        const mockScorecardResults: ScorecardResult[] = [
            {
                measureIdentifier: mockMeasureId,
                measureTitle: 'Test Measure',
                inotation: INotation.Higher,
                scorecardDetailsList: [
                    {
                        id: 1,
                        columnName: 'Jan 2023',
                        rate: 75,
                        startDate: mockStartDate,
                        endDate: mockEndDate,
                    },
                ],
            },
        ]

        // Create mock performance goals with different measure ID
        const mockPerformanceGoals: PerformanceGoal[] = [
            {
                partitionKey: mockOrganizationId,
                rowKey: 'goal-123',
                measureIdentifier: 'different-measure-id',
                entityId: 'entity-123',
                startDate: mockStartDate.toISOString(),
                endDate: mockEndDate.toISOString(),
                goalLower: 70,
                goalUpper: 80,
            },
        ]

        // @ts-ignore
        getAllPerformanceGoals.mockResolvedValue(mockPerformanceGoals)

        // Execute the function
        const result = await setPerformanceGoals(
            mockOrganizationId,
            mockScorecardResults,
            ScorecardView.Monthly,
            mockUserId,
            mockSelectionType
        )

        // Verify the result contains the expected data
        expect(result).toHaveLength(1)
        expect(result[0]?.measureIdentifier).toBe(mockMeasureId)
        expect(result[0]?.scorecardDetailsList?.[0]?.goal).toBeNull()
        expect(result[0]?.scorecardDetailsList?.[0]?.performance).toBe(Performance.NoData)
    })

    test('should handle case where rate is zero', async () => {
        // Setup mock data
        const mockMeasureId = 'measure-123'
        const mockStartDate = dayjs('2023-01-01')
        const mockEndDate = dayjs('2023-01-31')

        // Create mock scorecard results with zero rate
        const mockScorecardResults: ScorecardResult[] = [
            {
                measureIdentifier: mockMeasureId,
                measureTitle: 'Test Measure',
                inotation: INotation.Higher,
                scorecardDetailsList: [
                    {
                        id: 1,
                        columnName: 'Jan 2023',
                        rate: 0,
                        startDate: mockStartDate,
                        endDate: mockEndDate,
                    },
                ],
            },
        ]

        // Create mock performance goals
        const mockPerformanceGoals: PerformanceGoal[] = [
            {
                partitionKey: mockOrganizationId,
                rowKey: 'goal-123',
                measureIdentifier: mockMeasureId,
                entityId: 'entity-123',
                startDate: mockStartDate.toISOString(),
                endDate: mockEndDate.toISOString(),
                goalLower: 70,
                goalUpper: 80,
            },
        ]

        // @ts-ignore
        getAllPerformanceGoals.mockResolvedValue(mockPerformanceGoals)

        // Execute the function
        const result = await setPerformanceGoals(
            mockOrganizationId,
            mockScorecardResults,
            ScorecardView.Monthly,
            mockUserId,
            mockSelectionType
        )

        // Verify the result contains the expected data
        expect(result).toHaveLength(1)
        expect(result[0]?.measureIdentifier).toBe(mockMeasureId)
        expect(result[0]?.scorecardDetailsList?.[0]?.goal).toBe(70)
        expect(result[0]?.scorecardDetailsList?.[0]?.performance).toBe(Performance.Poor)
    })

    test('should handle case mismatch between scorecard results and performance goals', async () => {
        // Setup mock data
        const mockMeasureIdUpper = 'MEASURE-123'
        const mockMeasureIdLower = 'measure-123'
        const mockStartDate = dayjs('2023-01-01')
        const mockEndDate = dayjs('2023-01-31')

        // Create mock scorecard results with uppercase measure ID
        const mockScorecardResults: ScorecardResult[] = [
            {
                measureIdentifier: mockMeasureIdUpper,
                measureTitle: 'Test Measure',
                inotation: INotation.Higher,
                scorecardDetailsList: [
                    {
                        id: 1,
                        columnName: 'Jan 2023',
                        rate: 75,
                        startDate: mockStartDate,
                        endDate: mockEndDate,
                    },
                ],
            },
        ]

        // Create mock performance goals with lowercase measure ID
        const mockPerformanceGoals: PerformanceGoal[] = [
            {
                partitionKey: mockOrganizationId,
                rowKey: 'goal-123',
                measureIdentifier: mockMeasureIdLower,
                entityId: 'entity-123',
                startDate: mockStartDate.toISOString(),
                endDate: mockEndDate.toISOString(),
                goalLower: 70,
                goalUpper: 80,
            },
        ]

        // Setup mocks
        // @ts-ignore
        getAllPerformanceGoals.mockResolvedValue(mockPerformanceGoals)

        // Execute the function
        const result = await setPerformanceGoals(
            mockOrganizationId,
            mockScorecardResults,
            ScorecardView.Monthly,
            mockUserId,
            mockSelectionType
        )

        expect(result).toHaveLength(1)
        expect(result[0]?.measureIdentifier).toBe(mockMeasureIdUpper)
        expect(result[0]?.scorecardDetailsList?.[0]?.goal).toBe(70)
        expect(result[0]?.scorecardDetailsList?.[0]?.performance).toBe(Performance.Good)
    })

    test('should handle multiple scorecard results', async () => {
        // Setup mock data
        const mockMeasureId1 = 'measure-123'
        const mockMeasureId2 = 'measure-456'
        const mockStartDate = dayjs('2023-01-01')
        const mockEndDate = dayjs('2023-01-31')

        // Create mock scorecard results
        const mockScorecardResults: ScorecardResult[] = [
            {
                measureIdentifier: mockMeasureId1,
                measureTitle: 'Test Measure 1',
                inotation: INotation.Higher,
                scorecardDetailsList: [
                    {
                        id: 1,
                        columnName: 'Jan 2023',
                        rate: 75,
                        startDate: mockStartDate,
                        endDate: mockEndDate,
                    },
                ],
            },
            {
                measureIdentifier: mockMeasureId2,
                measureTitle: 'Test Measure 2',
                inotation: INotation.Lower,
                scorecardDetailsList: [
                    {
                        id: 2,
                        columnName: 'Jan 2023',
                        rate: 25,
                        startDate: mockStartDate,
                        endDate: mockEndDate,
                    },
                ],
            },
        ]

        // Create mock performance goals
        const mockPerformanceGoals: PerformanceGoal[] = [
            {
                partitionKey: mockOrganizationId,
                rowKey: 'goal-123',
                measureIdentifier: mockMeasureId1,
                entityId: 'entity-123',
                startDate: mockStartDate.toISOString(),
                endDate: mockEndDate.toISOString(),
                goalLower: 70,
                goalUpper: 80,
            },
            {
                partitionKey: mockOrganizationId,
                rowKey: 'goal-456',
                measureIdentifier: mockMeasureId2,
                entityId: 'entity-123',
                startDate: mockStartDate.toISOString(),
                endDate: mockEndDate.toISOString(),
                goalLower: 30,
                goalUpper: 40,
            },
        ]

        // @ts-ignore
        getAllPerformanceGoals.mockResolvedValue(mockPerformanceGoals)

        // Execute the function
        const result = await setPerformanceGoals(
            mockOrganizationId,
            mockScorecardResults,
            ScorecardView.Monthly,
            mockUserId,
            mockSelectionType
        )

        // Verify the result contains the expected data
        expect(result).toHaveLength(2)

        // First measure
        expect(result[0]?.measureIdentifier).toBe(mockMeasureId1)
        expect(result[0]?.scorecardDetailsList?.[0]?.goal).toBe(70)
        expect(result[0]?.scorecardDetailsList?.[0]?.performance).toBe(Performance.Good)

        // Second measure
        expect(result[1]?.measureIdentifier).toBe(mockMeasureId2)
        expect(result[1]?.scorecardDetailsList?.[0]?.goal).toBe(30)
        expect(result[1]?.scorecardDetailsList?.[0]?.performance).toBe(Performance.Good)
    })
})
