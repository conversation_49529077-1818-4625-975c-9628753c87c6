import { jest } from '@jest/globals'
import { ProcedureStatus } from '@/services/procedureStatus/ProcedureStatus'
import { Entity } from "@/types/entity";

const mockRedisHelperGet = jest.fn()
const mockRedisHelperSet = jest.fn()
// Mock the redis module
jest.mock('@/lib/redis', () => ({
    tryCache: jest.fn((key: string, fn: () => any) => fn()),
    redisHelper: {
        get: mockRedisHelperGet,
        set: mockRedisHelperSet
    }
}))

jest.mock('@/env', () => ({
    env: {},
}))

jest.mock('@/lib/applicationInsights', () => ({
    loadAppInsights: jest.fn(),
}))

jest.mock('@azure/storage-blob', () => ({
}))

jest.mock('@azure/data-tables', () => ({
}))

// Create mock repositories
const mockSnowflakeMeasureRepo = {
    findAll: jest.fn(),
    findById: jest.fn(),
    calculateECMeasureResults: jest.fn(),
    calculateMeasureResults: jest.fn()
}

const mockSnowflakeActiveMeasureRepository = {
    findAllDetailedActiveMeasures: jest.fn()
}

const mockProvidersRepository = {
    findProvidersBySubmissionGroup: jest.fn(),
    findActiveProvidersBySubmissionGroup: jest.fn()
}

const mockProcedureStatusRepository = {
    findByJobName: jest.fn(),
    isReportPrecompiling: jest.fn()
}

const mockSisenseMeasSummRepository = {
    findPerformanceRatesByDateAndMeasureForHospital: jest.fn()
}

const mockEntitiesRepository = {
    findDistinctSourceContainerIdentifiers: jest.fn(),
    findAll: jest.fn(),
    findByOrganizationIdAndEntityTypeId: jest.fn()
}

const mockSnowflakeSubmissionGroupRepository = {
    findAll: jest.fn(),
    findByOrganizationId: jest.fn(),
    findByOrganizationIdAndMeasureType: jest.fn()
}

const mockSnowflakeFacilityRepository = {
    findAll: jest.fn(),
    findByOrganizationId: jest.fn()
}

// Mock CitCOrganizationService
jest.mock('@/services/citCOrganizations', () => {
    return jest.fn().mockImplementation(() => {
        return {
            getOrganizationDetailsAsync: jest.fn()
        }
    })
})

const mockCitCParametersService = {
    getPartnerParametersAsync: jest.fn(),
    getOrganizationParametersAsync: jest.fn()
}

describe('MeasureResultsService - ProcedureStatus', () => {
    let measuresModule: { MeasureResultsService: any; default?: any }
    let measureResultsService: {
        getProcedureLoadStatus: (arg0: { organizationId: string; isPartner: boolean }) => any,
        getEntities: (arg0: { organizationId: string, isPartner: boolean }) => Promise<Entity[]>,
        getMeasureByIdFromDb: (measureId: string, organizationId: string) => Promise<any>
    }

    beforeAll(async () => {
        measuresModule = await import('@/services/measureResults')
    })
    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks()

        // Create the MeasureResultsService with the mocked repositories and services
        measureResultsService = new measuresModule.MeasureResultsService(
            'test-token',
            mockSnowflakeMeasureRepo,
            mockSnowflakeActiveMeasureRepository,
            mockProvidersRepository,
            mockProcedureStatusRepository,
            mockSisenseMeasSummRepository,
            mockEntitiesRepository,
            mockSnowflakeSubmissionGroupRepository,
            mockSnowflakeFacilityRepository,
            mockCitCParametersService
        )
    })

    describe('getProcedureLoadStatus', () => {
        test('should return procedure status using the ProcedureRepository', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Mock the repository responses
            const mockOakLoadJob: ProcedureStatus = {
                Id: BigInt(123),
                JobName: "OAK2ADMLoad",
                JobRunId: "user-123",
                SuccessStatus: true,
                StartDateTime: new Date(),
                EndDateTime: new Date()
            }

            // @ts-ignore
            mockProcedureStatusRepository.findByJobName.mockResolvedValue([mockOakLoadJob])
            // @ts-ignore
            mockProcedureStatusRepository.isReportPrecompiling.mockResolvedValue(false)

            // Act
            const result = await measureResultsService.getProcedureLoadStatus(options)

            // Assert
            expect(mockProcedureStatusRepository.findByJobName).toHaveBeenCalledWith("OAK2ADMLoad")

            expect(result).toBeDefined()
            if (result) {
                expect(result.jobName).toBe("OAK2ADMLoad")
                expect(result.jobId).toBe(123)
                expect(result.startDate).toBeDefined()
                expect(result.startDate instanceof Date).toBe(true)
                expect(result.startDate.getTime()).not.toBeNaN()
                expect(result.endDate).toBeDefined()
                expect(result.endDate instanceof Date).toBe(true)
                expect(result.endDate.getTime()).not.toBeNaN()
                expect(typeof result.successStatus).toBe('boolean')
                expect(result.successStatus).toBe(true)
                expect(result.procedureRunningStatus).toBe("")
            }
        })

        test('should set procedureRunningStatus when Precompile Reports job exists', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Mock the repository responses
            const mockOakLoadJob: ProcedureStatus = {
                Id: BigInt(123),
                JobName: "OAK2ADMLoad",
                JobRunId: "user-123",
                SuccessStatus: true,
                StartDateTime: new Date(),
                EndDateTime: new Date()
            }

            const mockPrecompileJob: ProcedureStatus = {
                Id: BigInt(17),
                JobName: "Precompile Reports",
                JobRunId: "user-456",
                SuccessStatus: true,
                StartDateTime: new Date(),
                EndDateTime: null
            }

            // @ts-ignore
            mockProcedureStatusRepository.findByJobName.mockResolvedValue([mockOakLoadJob])
            // @ts-ignore
            mockProcedureStatusRepository.isReportPrecompiling.mockResolvedValue(mockPrecompileJob)

            // Act
            const result = await measureResultsService.getProcedureLoadStatus(options)

            // Assert
            expect(mockProcedureStatusRepository.findByJobName).toHaveBeenCalledWith("OAK2ADMLoad")

            expect(result).toBeDefined()
            if (result) {
                expect(result.jobName).toBe("OAK2ADMLoad")
                expect(result.jobId).toBe(123)
                expect(result.startDate).toBeDefined()
                expect(result.startDate instanceof Date).toBe(true)
                expect(result.startDate.getTime()).not.toBeNaN()
                expect(result.endDate).toBeDefined()
                expect(result.endDate instanceof Date).toBe(true)
                expect(result.endDate.getTime()).not.toBeNaN()
                expect(typeof result.successStatus).toBe('boolean')
                expect(result.successStatus).toBe(true)
                expect(result.procedureRunningStatus).toBe("Report Precompile Running")
            }
        })

        test('should return null when no OAK2ADMLoad jobs exist', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Mock the repository responses
            // @ts-ignore
            mockProcedureStatusRepository.findByJobName.mockResolvedValue([])
            // @ts-ignore
            mockProcedureStatusRepository.isReportPrecompiling.mockResolvedValue(false)

            // Act
            const result = await measureResultsService.getProcedureLoadStatus(options)

            // Assert
            expect(mockProcedureStatusRepository.findByJobName).toHaveBeenCalledWith("OAK2ADMLoad")

            expect(result).toBeNull()
        })

        test('should handle failed job status correctly', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Mock the repository responses with a failed job
            const mockOakLoadJob: ProcedureStatus = {
                Id: BigInt(123),
                JobName: "OAK2ADMLoad",
                JobRunId: "user-123",
                SuccessStatus: false, // Failed job
                StartDateTime: new Date(),
                EndDateTime: new Date()
            }

            // @ts-ignore
            mockProcedureStatusRepository.findByJobName.mockResolvedValue([mockOakLoadJob])
            // @ts-ignore
            mockProcedureStatusRepository.isReportPrecompiling.mockResolvedValue(false)

            // Act
            const result = await measureResultsService.getProcedureLoadStatus(options)

            // Assert
            expect(mockProcedureStatusRepository.findByJobName).toHaveBeenCalledWith("OAK2ADMLoad")

            expect(result).toBeDefined()
            if (result) {
                expect(result.jobName).toBe("OAK2ADMLoad")
                expect(result.jobId).toBe(123)
                expect(result.startDate).toBeDefined()
                expect(result.startDate instanceof Date).toBe(true)
                expect(result.startDate.getTime()).not.toBeNaN()
                expect(result.endDate).toBeDefined()
                expect(result.endDate instanceof Date).toBe(true)
                expect(result.endDate.getTime()).not.toBeNaN()
                expect(typeof result.successStatus).toBe('boolean')
                expect(result.successStatus).toBe(false) // Should be false
                expect(result.procedureRunningStatus).toBe("")
            }
        })

        test('should handle running job (null EndDateTime) correctly', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Mock the repository responses with a running job (null EndDateTime)
            const mockOakLoadJob: ProcedureStatus = {
                Id: BigInt(123),
                JobName: "OAK2ADMLoad",
                JobRunId: "user-123",
                SuccessStatus: true,
                StartDateTime: new Date(),
                EndDateTime: null // Running job
            }

            // @ts-ignore
            mockProcedureStatusRepository.findByJobName.mockResolvedValue([mockOakLoadJob])
            // @ts-ignore
            mockProcedureStatusRepository.isReportPrecompiling.mockResolvedValue(false)

            // Act
            const result = await measureResultsService.getProcedureLoadStatus(options)

            // Assert
            expect(mockProcedureStatusRepository.findByJobName).toHaveBeenCalledWith("OAK2ADMLoad")

            expect(result).toBeDefined()
            if (result) {
                expect(result.jobName).toBe("OAK2ADMLoad")
                expect(result.jobId).toBe(123)
                expect(result.startDate).toBeDefined()
                expect(result.startDate instanceof Date).toBe(true)
                expect(result.startDate.getTime()).not.toBeNaN()
                expect(result.endDate).toBeDefined()
                expect(result.endDate instanceof Date).toBe(true)
                expect(result.endDate.getTime()).not.toBeNaN()
                expect(result.endDate.getTime()).toBe(new Date(0).getTime()) // Should be epoch date
                expect(typeof result.successStatus).toBe('boolean')
                expect(result.successStatus).toBe(true)
                expect(result.procedureRunningStatus).toBe("")
            }
        })

        test('should use the most recent job when multiple OAK2ADMLoad jobs exist', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Create multiple jobs with different IDs
            const olderJob: ProcedureStatus = {
                Id: BigInt(100),
                JobName: "OAK2ADMLoad",
                JobRunId: "user-100",
                SuccessStatus: true,
                StartDateTime: new Date(2023, 1, 1),
                EndDateTime: new Date(2023, 1, 1, 1)
            }

            const newerJob: ProcedureStatus = {
                Id: BigInt(200),
                JobName: "OAK2ADMLoad",
                JobRunId: "user-200",
                SuccessStatus: true,
                StartDateTime: new Date(2023, 2, 1),
                EndDateTime: new Date(2023, 2, 1, 1)
            }

            // Mock the repository to return multiple jobs
            // The first job in the array should be used (as per the implementation)
            // @ts-ignore
            mockProcedureStatusRepository.findByJobName.mockResolvedValue([newerJob, olderJob])
            // @ts-ignore
            mockProcedureStatusRepository.isReportPrecompiling.mockResolvedValue(false)

            // Act
            const result = await measureResultsService.getProcedureLoadStatus(options)

            // Assert
            expect(mockProcedureStatusRepository.findByJobName).toHaveBeenCalledWith("OAK2ADMLoad")

            expect(result).toBeDefined()
            if (result) {
                expect(result.jobName).toBe("OAK2ADMLoad")
                expect(result.jobId).toBe(200) // Should use the first job in the array (newer job)
                expect(result.applicationUserId).toBe("user-200")
                expect(result.startDate).toBeDefined()
                expect(result.startDate instanceof Date).toBe(true)
                expect(result.startDate.getTime()).not.toBeNaN()
                expect(result.startDate.getTime()).toBe(new Date(2023, 2, 1).getTime())
                expect(result.endDate).toBeDefined()
                expect(result.endDate instanceof Date).toBe(true)
                expect(result.endDate.getTime()).not.toBeNaN()
                expect(result.endDate.getTime()).toBe(new Date(2023, 2, 1, 1).getTime())
                expect(result.successStatus).toBe(true)
                expect(result.procedureRunningStatus).toBe("")
            }
        })

        test('should handle repository errors gracefully', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Mock the repository to throw an error
            // @ts-ignore
            mockProcedureStatusRepository.findByJobName.mockRejectedValue(new Error("Database connection error"))

            // Act & Assert
            await expect(measureResultsService.getProcedureLoadStatus(options)).rejects.toThrow("Database connection error")
            expect(mockProcedureStatusRepository.findByJobName).toHaveBeenCalledWith("OAK2ADMLoad")
        })

        test('should use cache when available', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Mock the cached response
            const cachedResponse = {
                jobId: 123,
                jobName: "OAK2ADMLoad",
                applicationUserId: "user-123",
                startDate: new Date(),
                endDate: new Date(),
                successStatus: true,
                procedureRunningStatus: ""
            }

            // Mock the redis module to return the cached value
            const redisMock = require('@/lib/redis')
            // @ts-ignore
            redisMock.tryCache.mockImplementation((key, fn) => Promise.resolve(cachedResponse))

            // Act
            const result = await measureResultsService.getProcedureLoadStatus(options)

            // Assert
            expect(redisMock.tryCache).toHaveBeenCalled()
            expect(mockProcedureStatusRepository.findByJobName).not.toHaveBeenCalled() // Repository should not be called when cache is used
            expect(result).toEqual(cachedResponse)
            expect(result.startDate instanceof Date).toBe(true)
            expect(result.startDate.getTime()).not.toBeNaN()
            expect(result.endDate instanceof Date).toBe(true)
            expect(result.endDate.getTime()).not.toBeNaN()
        })
    })

    describe('getEntities', () => {
        test('should return entities with display names for a single organization', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Mock the repository responses
            const mockEntities = [
                {
                    Id: BigInt(1),
                    Code: "ENTITY1",
                    EntityName: "Test Entity 1",
                    EntityTypeId: 1,
                    EntityTypeName: "Facility",
                    OrganizationTypeId: 3,
                    OrganizationTypeCode: "Hospital",
                    SourceContainerIdentifier: "cc956396-c75a-43c0-a7fe-e7236dd41014_EH"
                },
                {
                    Id: BigInt(2),
                    Code: "ENTITY2",
                    EntityName: "Test Entity 2",
                    EntityTypeId: 1,
                    EntityTypeName: "Facility",
                    OrganizationTypeId: 3,
                    OrganizationTypeCode: "Hospital",
                    SourceContainerIdentifier: "cc956396-c75a-43c0-a7fe-e7236dd41014_EC"
                }
            ]

            const mockOrg = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                organizationName: "Test Organization",
                isActive: true,
                isPartner: false
            }

            // Mock the repository and service responses
            // @ts-ignore
            mockEntitiesRepository.findAll.mockResolvedValue(mockEntities)

            // Get the CitCOrganizationService constructor mock
            const CitCOrganizationServiceMock = require('@/services/citCOrganizations')

            // Mock the getOrganizationDetailsAsync method
            // @ts-ignore
            const mockGetOrgDetails = jest.fn().mockResolvedValue(mockOrg)
            CitCOrganizationServiceMock.mockImplementation(() => ({
                getOrganizationDetailsAsync: mockGetOrgDetails
            }))

            // Act
            const result = await measureResultsService.getEntities(options)

            // Assert
            expect(mockEntitiesRepository.findAll).toHaveBeenCalled()
            expect(mockGetOrgDetails).toHaveBeenCalledWith(options.organizationId)

            expect(result).toHaveLength(2)

            // Check that display names are set correctly
            expect(result[0]!.entityDisplayName).toBe("Test Entity 1 (Test Organization)")
            expect(result[1]!.entityDisplayName).toBe("Test Entity 2 (Test Organization)")
        })

        test('should return entities with display names for partner organizations', async () => {
            // Arrange
            const options = {
                organizationId: "partner-123",
                isPartner: true
            }

            // Mock the repository responses
            const mockEntities = [
                {
                    Id: BigInt(1),
                    Code: "ENTITY1",
                    EntityName: "Test Entity 1",
                    EntityTypeId: 1,
                    EntityTypeName: "Facility",
                    OrganizationTypeId: 3,
                    OrganizationTypeCode: "Hospital",
                    SourceContainerIdentifier: "org1_EH"
                },
                {
                    Id: BigInt(2),
                    Code: "ENTITY2",
                    EntityName: "Test Entity 2",
                    EntityTypeId: 1,
                    EntityTypeName: "Facility",
                    OrganizationTypeId: 3,
                    OrganizationTypeCode: "Hospital",
                    SourceContainerIdentifier: "org2_EC"
                }
            ]

            const mockSourceContainerIds = ["org1", "org2"]

            const mockOrg1 = {
                organizationId: "org1",
                organizationName: "Organization 1",
                isActive: true,
                isPartner: false
            }

            const mockOrg2 = {
                organizationId: "org2",
                organizationName: "Organization 2",
                isActive: true,
                isPartner: false
            }

            // Mock the repository and service responses
            // @ts-ignore
            mockEntitiesRepository.findDistinctSourceContainerIdentifiers.mockResolvedValue(mockSourceContainerIds)
            // @ts-ignore
            mockEntitiesRepository.findAll.mockResolvedValue(mockEntities)

            // Get the CitCOrganizationService constructor mock
            const CitCOrganizationServiceMock = require('@/services/citCOrganizations')

            // Mock the getOrganizationDetailsAsync method to return different orgs based on ID
            const mockGetOrgDetails = jest.fn()
                .mockImplementation((id) => {
                    if (id === "org1") return Promise.resolve(mockOrg1)
                    if (id === "org2") return Promise.resolve(mockOrg2)
                    return Promise.resolve(null)
                })

            CitCOrganizationServiceMock.mockImplementation(() => ({
                getOrganizationDetailsAsync: mockGetOrgDetails
            }))

            // Act
            const result = await measureResultsService.getEntities(options)

            // Assert
            expect(mockEntitiesRepository.findDistinctSourceContainerIdentifiers).toHaveBeenCalled()
            expect(mockEntitiesRepository.findAll).toHaveBeenCalled()
            expect(mockGetOrgDetails).toHaveBeenCalledWith("org1")
            expect(mockGetOrgDetails).toHaveBeenCalledWith("org2")

            expect(result).toHaveLength(2)

            // Check that display names are set correctly
            expect(result[0]!.entityDisplayName).toBe("Test Entity 1 (Organization 1)")
            expect(result[1]!.entityDisplayName).toBe("Test Entity 2 (Organization 2)")
        })

        test('should use entity name as display name when no matching organization found', async () => {
            // Arrange
            const options = {
                organizationId: "cc956396-c75a-43c0-a7fe-e7236dd41014",
                isPartner: false
            }

            // Mock the repository responses
            const mockEntities = [
                {
                    Id: BigInt(1),
                    Code: "ENTITY1",
                    EntityName: "Test Entity 1",
                    EntityTypeId: 1,
                    EntityTypeName: "Facility",
                    OrganizationTypeId: 3,
                    OrganizationTypeCode: "Hospital",
                    SourceContainerIdentifier: "cc956396-c75a-43c0-a7fe-e7236dd41014_EH"
                }
            ]

            // Mock the repository and service responses
            // @ts-ignore
            mockEntitiesRepository.findAll.mockResolvedValue(mockEntities)

            // Get the CitCOrganizationService constructor mock
            const CitCOrganizationServiceMock = require('@/services/citCOrganizations')

            // Mock the getOrganizationDetailsAsync method to return null (no org found)
            CitCOrganizationServiceMock.mockImplementation(() => ({
                // @ts-ignore
                getOrganizationDetailsAsync: jest.fn().mockResolvedValue(null)
            }))

            // Act
            const result = await measureResultsService.getEntities(options)

            // Assert
            expect(mockEntitiesRepository.findAll).toHaveBeenCalled()

            expect(result).toHaveLength(1)

            // Check that display name defaults to entity name
            expect(result[0]!.entityDisplayName).toBe("Test Entity 1")
        })
    })

    describe('getMeasureByIdFromDb', () => {
        test('should return measure from cache when available', async () => {
            // Arrange
            const measureId = 'test-measure-id'
            const organizationId = 'test-org-id'
            const cachedMeasure = {
                Id: 123,
                MedisolvMeasureId: measureId,
                Name: 'Test Measure',
                MeasureSubId: 'a',
                MeasureScoring: 'PROP',
                SmallestInterval: 'M',
                LongMeasureName: 'Test Long Measure Name'
            }

            // @ts-ignore
            mockRedisHelperGet.mockResolvedValue(JSON.stringify(cachedMeasure))

            // Act
            const result = await measureResultsService.getMeasureByIdFromDb(measureId, organizationId)

            // Assert
            expect(mockRedisHelperGet).toHaveBeenCalledWith(`${organizationId}-measure-${measureId}`)
            expect(mockSnowflakeMeasureRepo.findById).not.toHaveBeenCalled() // Repository should not be called when cache is used
            expect(result).toEqual(cachedMeasure)
        })

        test('should return measure from repository when not in cache', async () => {
            // Arrange
            const measureId = 'test-measure-id'
            const organizationId = 'test-org-id'
            const repositoryMeasure = {
                Id: 123,
                MedisolvMeasureId: measureId,
                Name: 'Test Measure',
                MeasureSubId: 'a',
                MeasureScoring: 'PROP',
                SmallestInterval: 'M',
                LongMeasureName: 'Test Long Measure Name'
            }

            // @ts-ignore
            mockRedisHelperGet.mockResolvedValue(null)

            // Mock the repository to return the measure
            // @ts-ignore
            mockSnowflakeMeasureRepo.findById.mockResolvedValue(repositoryMeasure)

            // Act
            const result = await measureResultsService.getMeasureByIdFromDb(measureId, organizationId)

            // Assert
            expect(mockRedisHelperGet).toHaveBeenCalledWith(`${organizationId}-measure-${measureId}`)
            expect(mockSnowflakeMeasureRepo.findById).toHaveBeenCalledWith(measureId)
            expect(result).toEqual(repositoryMeasure)
        })

        test('should return undefined when measure not found in cache or repository', async () => {
            // Arrange
            const measureId = 'non-existent-measure-id'
            const organizationId = 'test-org-id'

            // @ts-ignore
            mockRedisHelperGet.mockResolvedValue(null)

            // Mock the repository to return undefined (not found)
            // @ts-ignore
            mockSnowflakeMeasureRepo.findById.mockResolvedValue(undefined)

            // Act
            const result = await measureResultsService.getMeasureByIdFromDb(measureId, organizationId)

            // Assert
            expect(mockRedisHelperGet).toHaveBeenCalledWith(`${organizationId}-measure-${measureId}`)
            expect(mockSnowflakeMeasureRepo.findById).toHaveBeenCalledWith(measureId)
            expect(result).toBeUndefined()
        })

        test('should handle repository errors gracefully', async () => {
            // Arrange
            const measureId = 'test-measure-id'
            const organizationId = 'test-org-id'

            // @ts-ignore
            mockRedisHelperGet.mockResolvedValue(null)

            // Mock the repository to throw an error
            // @ts-ignore
            mockSnowflakeMeasureRepo.findById.mockRejectedValue(new Error('Database connection error'))

            // Act & Assert
            await expect(measureResultsService.getMeasureByIdFromDb(measureId, organizationId)).rejects.toThrow('Database connection error')
            expect(mockRedisHelperGet).toHaveBeenCalledWith(`${organizationId}-measure-${measureId}`)
            expect(mockSnowflakeMeasureRepo.findById).toHaveBeenCalledWith(measureId)
        })

    })
})
