import type { ApiAuditLogData } from "@/types/auditLog";
import { jest } from '@jest/globals';
import dayjs from 'dayjs';

// Spy on console.log to suppress output during tests
let consoleLogSpy: any;

// Mock Application Insights
jest.mock('@/lib/applicationInsights', () => ({
    __esModule: true,
    default: {
        trackTrace: jest.fn(),
        trackException: jest.fn(),
        trackEvent: jest.fn(),
        loadAppInsights: jest.fn(),
    }
}));

// Mock the env module
jest.mock('@/env', () => ({
    __esModule: true,
    env: {
        NEXT_PUBLIC_AUDIT_LOG_SERVER_URL: 'http://test-audit-log-server.com',
    }
}));

// Mock the fetch function
global.fetch = jest.fn() as unknown as typeof fetch;

// Create mock audit log data
const createMockAuditLog = (overrides = {}): ApiAuditLogData => ({
    applicationId: 'MedisolvHubDev',
    auditLogDbId: Math.floor(Math.random() * 1000),
    currentRoles: 'Admin,User',
    logTimestamp: Date.now(),
    logUTCTime: new Date().toISOString(),
    miscData: {},
    organizationId: 'cc956396-c75a-43c0-a7fe-e7236dd41014',
    pageTitle: 'Dashboard',
    productId: 'MedisolvHub',
    requestIpAddress: '***********',
    url: '/dashboard',
    userAgent: 'Mozilla/5.0',
    userEmail: '<EMAIL>',
    userId: 'user-123',
    ...overrides
});

// Import the service after mocking dependencies
import AuditLogService from '@/services/admin/AuditLog/AuditLogService';

describe('AuditLogService Integration Tests', () => {
    let auditLogService: AuditLogService;

    beforeAll(() => {
        const testAccessToken = process.env.TEST_ACCESS_TOKEN || 'test-token';
        auditLogService = new AuditLogService(testAccessToken);

        // Suppress console.log output
        consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => { });
    });

    afterAll(() => {
        // Restore console.log after tests
        consoleLogSpy.mockRestore();
    });

    beforeEach(() => {
        // Reset the fetch mock before each test
        jest.resetAllMocks();
    });

    test('should retrieve audit logs for a given organization and application', async () => {
        // Arrange
        const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014';
        const applicationId = 'MedisolvHubDev';
        const startDate = dayjs().subtract(30, 'days').format('MMDDYYYY');
        const endDate = dayjs().format('MMDDYYYY');

        // Create mock audit logs
        const mockAuditLogs = [
            createMockAuditLog({ userEmail: '<EMAIL>' }),
            createMockAuditLog({ userEmail: '<EMAIL>' }),
            createMockAuditLog({ userEmail: '<EMAIL>' })
        ];

        // Mock the fetch response
        (fetch as jest.Mock).mockImplementationOnce(() =>
            Promise.resolve({
                ok: true,
                json: () => Promise.resolve(mockAuditLogs)
            })
        );

        // Act
        const results = await auditLogService.getAuditLogs(
            organizationId,
            applicationId,
            startDate,
            endDate
        );

        // Assert
        expect(results).toBeDefined();
        expect(Array.isArray(results)).toBeTruthy();
        expect(results.length).toBe(3);

        const firstLog = results[0];
        expect(firstLog).toHaveProperty('applicationId');
        expect(firstLog).toHaveProperty('auditLogDbId');
        expect(firstLog).toHaveProperty('logUTCTime');
        expect(firstLog).toHaveProperty('userEmail');
        expect(firstLog).toHaveProperty('url');
        expect(firstLog).toHaveProperty('organizationId');
    });

    test('should handle empty results gracefully', async () => {
        // Arrange
        const organizationId = 'non-existent-org';
        const applicationId = 'non-existent-app';
        const startDate = dayjs().format('MMDDYYYY');
        const endDate = dayjs().format('MMDDYYYY');

        // Mock the fetch response with empty array
        (fetch as jest.Mock).mockImplementationOnce(() =>
            Promise.resolve({
                ok: true,
                json: () => Promise.resolve([])
            })
        );

        // Act
        const results = await auditLogService.getAuditLogs(
            organizationId,
            applicationId,
            startDate,
            endDate
        );

        // Assert
        expect(results).toBeDefined();
        expect(Array.isArray(results)).toBeTruthy();
        expect(results).toHaveLength(0);
    });

    test('should handle date range queries correctly', async () => {
        // Arrange
        const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014';
        const applicationId = 'MedisolvHubDev';
        const startDate = dayjs().subtract(7, 'days').format('MMDDYYYY');
        const endDate = dayjs().format('MMDDYYYY');

        // Create dates within the range
        const startDateObj = dayjs(startDate, 'MMDDYYYY').add(1, 'day');
        const midDateObj = dayjs(startDate, 'MMDDYYYY').add(3, 'day');
        const endDateObj = dayjs(endDate, 'MMDDYYYY').subtract(1, 'day');

        // Create mock audit logs with dates in the range
        const mockAuditLogs = [
            createMockAuditLog({
                logUTCTime: startDateObj.toISOString(),
                logTimestamp: startDateObj.valueOf()
            }),
            createMockAuditLog({
                logUTCTime: midDateObj.toISOString(),
                logTimestamp: midDateObj.valueOf()
            }),
            createMockAuditLog({
                logUTCTime: endDateObj.toISOString(),
                logTimestamp: endDateObj.valueOf()
            })
        ];

        // Mock the fetch response
        (fetch as jest.Mock).mockImplementationOnce(() =>
            Promise.resolve({
                ok: true,
                json: () => Promise.resolve(mockAuditLogs)
            })
        );

        // Act
        const results = await auditLogService.getAuditLogs(
            organizationId,
            applicationId,
            startDate,
            endDate
        );

        // Assert
        expect(results).toBeDefined();
        expect(Array.isArray(results)).toBeTruthy();
        expect(results.length).toBe(3);

        // Verify logs fall within the date range
        results.forEach((log: ApiAuditLogData) => {
            const logDate = dayjs(log.logUTCTime);
            const start = dayjs(startDate, 'MMDDYYYY');
            const end = dayjs(endDate, 'MMDDYYYY').endOf('day');

            expect(logDate.isAfter(start) || logDate.isSame(start)).toBeTruthy();
            expect(logDate.isBefore(end) || logDate.isSame(end)).toBeTruthy();
        });
    });

    test('should handle invalid date formats by returning empty array', async () => {
        // Arrange
        const organizationId = 'cc956396-c75a-43c0-a7fe-e7236dd41014';
        const applicationId = 'MedisolvHubDev';
        const invalidStartDate = 'invalid-date';
        const invalidEndDate = 'invalid-date';

        // Mock the fetch response with an error
        (fetch as jest.Mock).mockImplementationOnce(() =>
            Promise.reject(new Error('Invalid date format'))
        );

        // Act
        const results = await auditLogService.getAuditLogs(
            organizationId,
            applicationId,
            invalidStartDate,
            invalidEndDate
        );

        // Assert
        expect(results).toBeDefined();
        expect(Array.isArray(results)).toBeTruthy();
        expect(results).toHaveLength(0);
    });
   describe('AuditLogService - postAuditLogs', () => {
    // Create mock audit log data for posting
    const mockPostAuditLogData = {
        url: '/dashboard',
        userAgent: 'Mozilla/5.0',
        pageTitle: 'Dashboard',
        actionType: 'PAGE_NAVIGATION',
        productId: 'MedisolvHub',
        userId: 'user-123',
        userEmail: '<EMAIL>',
        applicationId: 'PlatformReactDev',
        currentRoles: 'Admin,User',
        organizationId: 'cc956396-c75a-43c0-a7fe-e7236dd41236',
    };

    test('should successfully post audit logs', async () => {
        // Mock successful fetch response
        (fetch as jest.Mock).mockImplementationOnce(() =>
            Promise.resolve({
                ok: true,
                status: 200,
                statusText: 'OK',
                text: () => Promise.resolve('Success'),
            })
        );

        // Call the method
        const result = await auditLogService.postAuditLogs(mockPostAuditLogData);

        // Assertions
        expect(result).toBe(true);
        expect(fetch).toHaveBeenCalledTimes(1);
        expect(fetch).toHaveBeenCalledWith(
            'http://test-audit-log-server.com/api/audit-logs/add',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer test-token`,
                },
                body: JSON.stringify(mockPostAuditLogData),
            }
        );
    });

    test('should handle API error responses', async () => {
        // Mock error response
        (fetch as jest.Mock).mockImplementationOnce(() =>
            Promise.resolve({
                ok: false,
                status: 400,
                statusText: 'Bad Request',
                text: () => Promise.resolve('Invalid data format'),
            })
        );

        // Spy on console.error
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

        // Call the method
        const result = await auditLogService.postAuditLogs(mockPostAuditLogData);

        // Assertions
        expect(result).toBe(false);
        expect(fetch).toHaveBeenCalledTimes(1);
        expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
        expect(consoleErrorSpy).toHaveBeenCalledWith(
            'Error posting audit logs:',
            expect.any(Error)
        );

        // Restore console.error
        consoleErrorSpy.mockRestore();
    });

    test('should handle network errors', async () => {
        // Mock network error
        const networkError = new Error('Network error');
        (fetch as jest.Mock).mockImplementationOnce(() => Promise.reject(networkError));

        // Spy on console.error
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

        // Call the method
        const result = await auditLogService.postAuditLogs(mockPostAuditLogData);

        // Assertions
        expect(result).toBe(false);
        expect(fetch).toHaveBeenCalledTimes(1);
        expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
        expect(consoleErrorSpy).toHaveBeenCalledWith(
            'Error posting audit logs:',
            networkError
        );

        // Restore console.error
        consoleErrorSpy.mockRestore();
    });

    test('should append trailing slash to base URL if missing', async () => {
        // Create a temporary service with a modified env mock
        const originalEnv = require('@/env').env;
        
        // Temporarily modify the mock
        jest.resetModules();
        jest.mock('@/env', () => ({
            __esModule: true,
            env: {
                NEXT_PUBLIC_AUDIT_LOG_SERVER_URL: 'http://test-audit-log-server.com',
            }
        }));
        
        // Re-import the service with the new env
        const AuditLogServiceReloaded = require('@/services/admin/AuditLog/AuditLogService').default;
        const tempService = new AuditLogServiceReloaded('test-token');
        
        // Mock successful fetch response
        (fetch as jest.Mock).mockImplementationOnce(() =>
            Promise.resolve({
                ok: true,
                status: 200,
                statusText: 'OK',
                text: () => Promise.resolve('Success'),
            })
        );

        // Call the method
        await tempService.postAuditLogs(mockPostAuditLogData);

        // Assertions
        expect(fetch).toHaveBeenCalledWith(
            'http://test-audit-log-server.com/api/audit-logs/add',
            expect.any(Object)
        );
        
        // Reset the mock to its original state
        jest.resetModules();
        jest.mock('@/env', () => ({
            __esModule: true,
            env: originalEnv
        }));
    });
}); 
});


