import { AzureTableStorageWrapper } from '@/services/azure/tableStorageWrapper'
import { saveOrganizationPreference } from '@/services/organizationPreferences/saveOrganizationPreference'
import { getOrganizationPreferences } from '@/services/organizationPreferences/getOrganizationPreferences'
import { OrganizationPreference } from '@/types/organizationPreference'
import { getAllOrganizationPreferences } from '@/services/organizationPreferences/getAlllOrganizationPreferences'
import { jest } from '@jest/globals'
import { SelectionType } from '@/enums/selectionType'
import { OrganizationPreferenceKey } from '@/enums/organizationPreferenceKey'

jest.mock('@/lib/applicationInsights', () => ({
  loadAppInsights: jest.fn(),
  trackEvent: jest.fn(),
  trackException: jest.fn(),
  trackTrace: jest.fn(),
}))

jest.mock('@/services/azure/tableStorageWrapper')

jest.mock('@/env', () => ({
  env: {},
}))

jest.mock('@azure/storage-blob', () => ({}))

describe('Organization Preferences Service', () => {
  let mockTableStorageWrapper: jest.Mocked<AzureTableStorageWrapper>

  beforeEach(() => {
    mockTableStorageWrapper = {
      getEntity: jest.fn(),
      queryEntities: jest.fn(),
      insertEntity: jest.fn(),
      updateEntity: jest.fn(),
      generateODataQuery: jest.fn(),
      upsertEntity: jest.fn(),
    } as unknown as jest.Mocked<AzureTableStorageWrapper>

    // Clear global prisma clients before each test
    globalThis.prismaClients = {}
  })

  describe('saveOrganizationPreference', () => {
    const organizationId = 'test-org'
    const keyName = 'PrimaryMeasureType'
    const keyValue = 'testValue'

    test('should create new preference when id is not provided', async () => {
      const newPreference: OrganizationPreference = {
        partitionKey: organizationId,
        rowKey: expect.any(String),
        key: keyName,
        value: keyValue,
      }

      mockTableStorageWrapper.insertEntity.mockResolvedValue(newPreference)

      const result = await saveOrganizationPreference(
        mockTableStorageWrapper,
        organizationId,
        '',
        keyName,
        keyValue
      )

      expect(result.success).toBe(true)
      expect(result.message).toBe(
        'Organization preference created successfully'
      )
      expect(mockTableStorageWrapper.insertEntity).toHaveBeenCalledWith(
        expect.objectContaining({
          partitionKey: organizationId,
          key: keyName,
          value: keyValue,
        })
      )
    })

    test('should update existing preference when id is provided', async () => {
      const existingId = 'existing-id'
      const existingPreference: OrganizationPreference = {
        partitionKey: organizationId,
        rowKey: existingId,
        key: 'PrimaryMeasureType',
        value: 'oldValue',
      }

      const updatedPreference: OrganizationPreference = {
        partitionKey: organizationId,
        rowKey: existingId,
        key: keyName,
        value: keyValue,
      }

      mockTableStorageWrapper.getEntity.mockResolvedValue(existingPreference)
      mockTableStorageWrapper.updateEntity.mockResolvedValue(updatedPreference)

      const result = await saveOrganizationPreference(
        mockTableStorageWrapper,
        organizationId,
        existingId,
        keyName,
        keyValue
      )

      expect(result.success).toBe(true)
      expect(result.message).toBe(
        'Organization preference updated successfully'
      )
      expect(mockTableStorageWrapper.updateEntity).toHaveBeenCalledWith(
        expect.objectContaining({
          partitionKey: organizationId,
          rowKey: existingId,
          key: keyName,
          value: keyValue,
        })
      )
    })

    test('should return error when updating non-existent preference', async () => {
      const nonExistentId = 'non-existent-id'
      mockTableStorageWrapper.getEntity.mockResolvedValue(undefined)

      const result = await saveOrganizationPreference(
        mockTableStorageWrapper,
        organizationId,
        nonExistentId,
        keyName,
        keyValue
      )

      expect(result.success).toBe(false)
      expect(result.message).toBe('Organization preference not found')
      expect(mockTableStorageWrapper.updateEntity).not.toHaveBeenCalled()
    })
  })

  describe('getOrganizationPreferences', () => {
    const organizationId = 'test-org'
    const fieldKey = OrganizationPreferenceKey.PRIMARYMEASURETYPE
    const fieldValue = 'testValue'

    test('should return preferences matching organization id and field value', async () => {
      const mockPreferences: OrganizationPreference[] = [
        {
          partitionKey: organizationId,
          rowKey: '1',
          key: fieldKey,
          value: fieldValue,
        },
        {
          partitionKey: organizationId,
          rowKey: '2',
          key: fieldKey,
          value: fieldValue,
        },
      ]

      mockTableStorageWrapper.generateODataQuery.mockReturnValue(
        `PartitionKey eq '${organizationId}' and value eq '${fieldValue}'`
      )
      mockTableStorageWrapper.queryEntities.mockResolvedValue(mockPreferences)

      const result = await getOrganizationPreferences(
        mockTableStorageWrapper,
        organizationId,
        fieldKey
      )

      expect(result).toEqual(mockPreferences)
      expect(mockTableStorageWrapper.generateODataQuery).toHaveBeenCalledWith({
        PartitionKey: organizationId,
        key: fieldKey,
      })
    })

    test('should return empty array when no preferences match', async () => {
      mockTableStorageWrapper.generateODataQuery.mockReturnValue(
        `PartitionKey eq '${organizationId}' and value eq '${fieldValue}'`
      )
      mockTableStorageWrapper.queryEntities.mockResolvedValue([])

      const result = await getOrganizationPreferences(
        mockTableStorageWrapper,
        organizationId,
        fieldKey
      )

      expect(result).toEqual([])
    })
  })

  describe('getAllOrganizationPreferences', () => {
    const organizationId = 'test-org'

    test('should return all preferences for organization', async () => {
      const mockPreferences: OrganizationPreference[] = [
        {
          partitionKey: organizationId,
          rowKey: '1',
          key: 'key1',
          value: 'value1',
        },
        {
          partitionKey: organizationId,
          rowKey: '2',
          key: 'key2',
          value: 'value2',
        },
      ]

      mockTableStorageWrapper.generateODataQuery.mockReturnValue(
        `organizationId eq '${organizationId}'`
      )
      mockTableStorageWrapper.queryEntities.mockResolvedValue(mockPreferences)

      const result = await getAllOrganizationPreferences(
        mockTableStorageWrapper,
        organizationId
      )

      expect(result).toEqual(mockPreferences)
      expect(mockTableStorageWrapper.generateODataQuery).toHaveBeenCalledWith({
        PartitionKey: organizationId,
      })
    })

    test('should return empty array when no preferences exist', async () => {
      mockTableStorageWrapper.generateODataQuery.mockReturnValue(
        `organizationId eq '${organizationId}'`
      )
      mockTableStorageWrapper.queryEntities.mockResolvedValue([])

      const result = await getAllOrganizationPreferences(
        mockTableStorageWrapper,
        organizationId
      )

      expect(result).toEqual([])
    })
  })

  describe('getOrganizationPreferences with migration', () => {
    const organizationId = 'test-org'
    const keyName = OrganizationPreferenceKey.PRIMARYMEASURETYPE
    const fieldValue = 'testValue'

    test('should migrate preferences when no existing preferences and migrationConfig is provided', async () => {
      // Mock empty existing preferences for first call, then return migrated preferences for second call
      const expectedPreferences = [
        {
          partitionKey: organizationId,
          rowKey: '1',
          key: keyName,
          value: fieldValue,
        },
        {
          partitionKey: organizationId,
          rowKey: '2',
          key: keyName,
          value: fieldValue,
        },
      ]

      mockTableStorageWrapper.queryEntities
        .mockResolvedValueOnce([]) // First call returns empty array
        .mockResolvedValueOnce(expectedPreferences) // Second call returns migrated preferences

      // Mock SQL preferences
      const sqlPreferences = [
        {
          Id: 1,
          OrganizationId: organizationId,
          Key: keyName,
          Value: fieldValue,
        },
        {
          Id: 2,
          OrganizationId: organizationId,
          Key: keyName,
          Value: fieldValue,
        },
      ]

      // Setup prisma mock
      globalThis.prismaClients = {
        [organizationId]: {
          hubClient: {
            organizationPreferences: {
              findMany: jest.fn().mockResolvedValue(sqlPreferences as never),
            },
          },
        },
      } as any

      const migrationConfig = {
        organizationId,
        selectionType: SelectionType.Organization,
      }

      // Mock successful upserts
      mockTableStorageWrapper.upsertEntity.mockImplementation(
        async (entity) => entity
      )

      // Mock OData query
      mockTableStorageWrapper.generateODataQuery.mockReturnValue(
        `PartitionKey eq '${organizationId}' and value eq '${fieldValue}'`
      )

      // Act
      const result = await getOrganizationPreferences(
        mockTableStorageWrapper,
        organizationId,
        keyName,
        migrationConfig
      )

      // Assert
      expect(result).toEqual(expectedPreferences)
      expect(mockTableStorageWrapper.upsertEntity).toHaveBeenCalledTimes(2)
      expectedPreferences.forEach((pref) => {
        expect(mockTableStorageWrapper.upsertEntity).toHaveBeenCalledWith(
          expect.objectContaining(pref)
        )
      })
    })

    test('should migrate when migrationConfig selectionType is Partner', async () => {
      // Mock empty existing preferences
      mockTableStorageWrapper.queryEntities.mockResolvedValue([])

      const migrationConfig = {
        organizationId,
        selectionType: SelectionType.Partner,
      }

      const mockPrismaFindMany = jest.fn()
      globalThis.prismaClients = {
        [organizationId]: {
          hubClient: {
            organizationPreferences: {
              findMany: mockPrismaFindMany,
            },
          },
        },
      } as any

      // Act
      const result = await getOrganizationPreferences(
        mockTableStorageWrapper,
        organizationId,
        keyName,
        migrationConfig
      )

      // Assert
      expect(result).toEqual([])
      expect(mockPrismaFindMany).toHaveBeenCalled()
    })

    test('should handle missing prisma client during migration', async () => {
      // Mock empty existing preferences
      mockTableStorageWrapper.queryEntities.mockResolvedValue([])

      const migrationConfig = {
        organizationId,
        selectionType: SelectionType.Organization,
      }

      // Clear prisma clients
      globalThis.prismaClients = {}

      // Act
      const result = await getOrganizationPreferences(
        mockTableStorageWrapper,
        organizationId,
        keyName,
        migrationConfig
      )

      // Assert
      expect(result).toEqual([])
    })
  })
})
