import { jest } from '@jest/globals'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)

// Mock dependencies
jest.mock('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => fn()),
  redisHelper: {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
  },
}))

jest.mock('@/lib/applicationInsights', () => ({
  trackEvent: jest.fn(),
  trackTrace: jest.fn(),
}))

// Mock the env module to avoid the import error
jest.mock('@/env', () => ({
  env: {
    NEXT_PUBLIC_MAPLE_SERVER_URL: 'http://localhost:3000',
    REDIS_CACHE_TIME_DURATION_SECONDS: '3600',
  },
}))

// Mock the mapleMeasureQuery function
jest.mock('@/services/maple/mapleMeasuresQuery', () => ({
  mapleMeasureQuery: jest.fn(),
}))

const mockManageUserRolesQuery = jest.fn()
jest.mock('@/services/adm/users/manageUserRolesQuery', () => ({
  manageUserRolesQuery: mockManageUserRolesQuery,
}))

const mockGetSubmissionGroupsByOrganizationQuery = jest.fn()
jest.mock(
  '@/services/submissionGroups/getSubmissionGroupsByOrganizationQuery',
  () => ({
    getSubmissionGroupsByOrganizationQuery:
      mockGetSubmissionGroupsByOrganizationQuery,
  })
)

// Import after mocking
import { getECMeasureResultBySubmissionGroupQuery } from '@/services/measures/getECMeasureResultBySubmissionGroupQuery'
import { mapleMeasureQuery } from '@/services/maple/mapleMeasuresQuery'
import MapleMeasuresService from '@/services/mapleMeasures'
import { ScorecardView } from '@/enums/scorecardView'
import { INotation } from '@/enums/iNotation'
import { ExtensionLevels } from '@/enums/extensionLevels'
import { EntityLevelConstants } from '@/types/expansionConfiguration'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { DateRange } from '@/lib/dateRange'

describe('getECMeasureResultBySubmissionGroupQuery', () => {
  // Create mock services
  const mockMeasureResultsService = {
    getAllMeasures: jest.fn(),
    getECMeasureResults: jest.fn(),
    getEntities: jest.fn(),
    getSubmissionGroupsByOrganizationByMeasureType: jest.fn(),
    getSubmissionGroupsByOrganization: jest.fn(),
  }

  // Mock access token
  const mockAccessToken = 'mock-access-token'

  // Test data
  const mockOrganizationId = 'org-123'
  const mockUserId = 'user-123'

  // Create a lowercase GUID for maple measures
  const lowercaseGuid = '12345678-abcd-1234-abcd-1234567890ab'

  // Create an uppercase version of the same GUID for measure results
  const uppercaseGuid = lowercaseGuid.toUpperCase()

  // Create mock expansion configuration
  const mockExpansionConfig = [
    {
      id: ExtensionLevels.level2,
      label: 'Test Label',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      entries: 'Test Entries',
      selectedLevel: EntityLevelConstants.TopLevel,
      partitionKey: 'test',
      rowKey: 'test',
    },
  ]

  beforeEach(() => {
    jest.clearAllMocks()

    // Setup mock for getSubmissionGroupsByOrganizationQuery
    // @ts-ignore
    mockGetSubmissionGroupsByOrganizationQuery.mockResolvedValue([
      {
        submissionGroupId: 'sg-123',
        submissionGroupName: 'Test Submission Group',
      },
    ])
  })

  test('should match lowercase maple measure GUIDs with uppercase measure result GUIDs', async () => {
    // Setup mock for measureResultsService.getAllMeasures to return uppercase GUIDs
    // @ts-ignore
    mockMeasureResultsService.getAllMeasures.mockResolvedValue([
      {
        MedisolvMeasureId: uppercaseGuid,
        DenominatorQualifyingType: 'qualifying-type-1',
        SmallestInterval: 'M',
        NullRate: false,
      },
    ])

    // Setup mock for mapleMeasureQuery to return lowercase GUIDs
    const mockMapleMeasureQueryResult = [
      {
        measureIdentifier: lowercaseGuid,
        measureName: 'Test Measure',
        measureDescription: 'Test Measure Description',
        measureFriendlyName: 'Test Friendly Name',
        subDomainName: 'Test Subdomain',
        typeName: 'Test Type',
        domainName: 'Test Domain',
        cMSId: 'CMS-123',
        subTypeName: 'Test Subtype',
        applicationName: 'Test Application',
        programName: 'Test Program',
        iNotationName: INotation.Higher,
      },
    ]
    // @ts-ignore
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(
      mockMapleMeasureQueryResult as never
    )

    // @ts-ignore
    mockMeasureResultsService.getECMeasureResults.mockResolvedValue({
      statusResponse: 'Success',
      ecMeasureResultSummaryList: [
        {
          entityCode: 'sg-123',
          entityName: 'Test Submission Group',
          startDate: dayjs('2023-01-01'),
          endDate: dayjs('2023-01-31').add(1, 'day'),
          denominator: 100,
          performance: 85,
        },
      ],
    })

    // Mock DateRange.getColumnIntervalsByCategory to return a specific value
    jest
      .spyOn(DateRange, 'getColumnIntervalsByCategory')
      .mockReturnValue(['Monthly-2023'])

    // Mock DateRange.getDateFromDateRangeSpan to return a specific value
    jest.spyOn(DateRange, 'getDateFromDateRangeSpan').mockReturnValue({
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
    })

    // Setup mock for getSubmissionGroupsByOrganizationByMeasureType
    // @ts-ignore
    mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType.mockResolvedValue(
      [
        {
          submissionGroupId: 'sg-123',
          submissionGroupName: 'Test Submission Group',
        },
      ] as never
    )

    // Setup mock for getEntities
    // @ts-ignore
    mockMeasureResultsService.getEntities.mockResolvedValue([])

    // Create request object
    const request = {
      organizationId: mockOrganizationId,
      userId: mockUserId,
      measureIdentifier: uppercaseGuid,
      scorecardView: ScorecardView.Monthly,
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      entites: ['sg-123'],
      isPartner: false,
      hasLimitedAccess: false,
      measureType: 'Q', // Changed from 'E' to 'Q' to match the code path that calls getSubmissionGroupsByOrganizationByMeasureType
      organizationList: [],
      expansionConfiguration: mockExpansionConfig,
      partners: [],
    }

    // Execute the function under test
    const result = await getECMeasureResultBySubmissionGroupQuery(
      mockAccessToken,
      mockMeasureResultsService as any,
      request
    )

    // Verify the mocks were called with the expected arguments
    expect(mockMeasureResultsService.getAllMeasures).toHaveBeenCalledWith(
      mockOrganizationId
    )
    expect(mapleMeasureQuery).toHaveBeenCalledWith(
      expect.any(MapleMeasuresService),
      mockOrganizationId,
      [uppercaseGuid]
    )
    expect(
      mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType
    ).toHaveBeenCalledWith({
      organizationId: mockOrganizationId,
      isPartner: false,
      measureType: 'Q',
    })

    // Verify the result contains the expected data
    expect(result).toHaveLength(1)
    if (result && result.length > 0) {
      expect(result[0]).toEqual(
        expect.objectContaining({
          measureIdentifier: uppercaseGuid,
          measureDescription: 'Test Measure Description',
          friendlyName: 'Test Friendly Name',
          subDomain: 'Test Subdomain',
          type: 'Test Type',
          domain: 'Test Domain',
          cmsId: 'CMS-123',
          subType: 'Test Subtype',
          application: 'Test Application',
          programName: 'Test Program',
          entityCode: 'sg-123',
          entity: 'Test Submission Group',
          entityNameForDetails: 'Test Submission Group',
          order: 2,
          isEmptyIndicator: false,
        })
      )

      // Verify that the Monthly_2023 property exists and has the correct value
      expect(result[0]!.Monthly_2023).toBe('85.00')
    }
  })

  test('should handle multiple measures with different GUIDs', async () => {
    // Create a second set of GUIDs
    const lowercaseGuid2 = '87654321-dcba-4321-dcba-0987654321fe'
    const uppercaseGuid2 = lowercaseGuid2.toUpperCase()

    // Setup mock for measureResultsService.getAllMeasures to return uppercase GUIDs
    // @ts-ignore
    mockMeasureResultsService.getAllMeasures.mockResolvedValue([
      {
        MedisolvMeasureId: uppercaseGuid,
        DenominatorQualifyingType: 'qualifying-type-1',
        SmallestInterval: 'M',
        NullRate: false,
      },
      {
        MedisolvMeasureId: uppercaseGuid2,
        DenominatorQualifyingType: 'qualifying-type-2',
        SmallestInterval: 'M',
        NullRate: false,
      },
    ])

    // Setup mock for mapleMeasureQuery to return lowercase GUIDs
    const mockMapleMeasureQueryResult = [
      {
        measureIdentifier: lowercaseGuid,
        measureName: 'Test Measure 1',
        measureDescription: 'Test Measure Description 1',
        measureFriendlyName: 'Test Friendly Name 1',
        subDomainName: 'Test Subdomain 1',
        typeName: 'Test Type 1',
        domainName: 'Test Domain 1',
        cMSId: 'CMS-123',
        subTypeName: 'Test Subtype 1',
        applicationName: 'Test Application 1',
        programName: 'Test Program 1',
        iNotationName: INotation.Higher,
      },
    ]
    // @ts-ignore
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(
      mockMapleMeasureQueryResult as never
    )

    // Setup mock for getECMeasureResults
    // @ts-ignore
    mockMeasureResultsService.getECMeasureResults.mockResolvedValue({
      statusResponse: 'Success',
      ecMeasureResultSummaryList: [
        {
          entityCode: 'sg-123',
          entityName: 'Test Submission Group',
          startDate: dayjs('2023-01-01'),
          endDate: dayjs('2023-01-31').add(1, 'day'),
          denominator: 100,
          performance: 85,
        },
      ],
    })

    // Mock DateRange.getColumnIntervalsByCategory to return a specific value
    jest
      .spyOn(DateRange, 'getColumnIntervalsByCategory')
      .mockReturnValue(['Monthly-2023'])

    // Mock DateRange.getDateFromDateRangeSpan to return a specific value
    jest.spyOn(DateRange, 'getDateFromDateRangeSpan').mockReturnValue({
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
    })

    // Setup mock for getSubmissionGroupsByOrganizationByMeasureType
    // @ts-ignore
    mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType.mockResolvedValue(
      [
        {
          submissionGroupId: 'sg-123',
          submissionGroupName: 'Test Submission Group',
        },
      ] as never
    )

    // Setup mock for getEntities
    // @ts-ignore
    mockMeasureResultsService.getEntities.mockResolvedValue([])

    // Create request object
    const request = {
      organizationId: mockOrganizationId,
      userId: mockUserId,
      measureIdentifier: uppercaseGuid,
      scorecardView: ScorecardView.Monthly,
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      entites: ['sg-123'],
      isPartner: false,
      hasLimitedAccess: false,
      measureType: 'E',
      organizationList: [],
      expansionConfiguration: mockExpansionConfig,
      partners: [],
    }

    // Execute the function under test
    const result = await getECMeasureResultBySubmissionGroupQuery(
      mockAccessToken,
      mockMeasureResultsService as any,
      request
    )

    // Verify the mocks were called with the expected arguments
    expect(mockMeasureResultsService.getAllMeasures).toHaveBeenCalledWith(
      mockOrganizationId
    )
    expect(mapleMeasureQuery).toHaveBeenCalledWith(
      expect.any(MapleMeasuresService),
      mockOrganizationId,
      [uppercaseGuid]
    )

    // Verify the result contains the expected data
    expect(result).toHaveLength(1)
    if (result && result.length > 0) {
      expect(result[0]).toEqual(
        expect.objectContaining({
          measureIdentifier: uppercaseGuid,
          measureDescription: 'Test Measure Description 1',
          friendlyName: 'Test Friendly Name 1',
          entityCode: 'sg-123',
          entity: 'Test Submission Group',
        })
      )

      // Verify that the Monthly_2023 property exists and has the correct value
      expect(result[0]!.Monthly_2023).toBe('85.00')
    }
  })

  test('should handle empty results from measureResultsService', async () => {
    // Setup mock for measureResultsService.getAllMeasures to return uppercase GUIDs
    // @ts-ignore
    mockMeasureResultsService.getAllMeasures.mockResolvedValue([
      {
        MedisolvMeasureId: uppercaseGuid,
        DenominatorQualifyingType: 'qualifying-type-1',
        SmallestInterval: 'M',
        NullRate: false,
      },
    ])

    // Setup mock for mapleMeasureQuery to return lowercase GUIDs
    const mockMapleMeasureQueryResult = [
      {
        measureIdentifier: lowercaseGuid,
        measureName: 'Test Measure',
        measureDescription: 'Test Measure Description',
        measureFriendlyName: 'Test Friendly Name',
        subDomainName: 'Test Subdomain',
        typeName: 'Test Type',
        domainName: 'Test Domain',
        cMSId: 'CMS-123',
        subTypeName: 'Test Subtype',
        applicationName: 'Test Application',
        programName: 'Test Program',
        iNotationName: INotation.Higher,
      },
    ]
    // @ts-ignore
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(
      mockMapleMeasureQueryResult as never
    )

    // Setup mock for getECMeasureResults to return empty results
    // @ts-ignore
    mockMeasureResultsService.getECMeasureResults.mockResolvedValue({
      statusResponse: 'Success',
      ecMeasureResultSummaryList: [],
    })

    // Mock DateRange.getColumnIntervalsByCategory to return a specific value
    jest
      .spyOn(DateRange, 'getColumnIntervalsByCategory')
      .mockReturnValue(['Monthly-2023'])

    // Mock DateRange.getDateFromDateRangeSpan to return a specific value
    jest.spyOn(DateRange, 'getDateFromDateRangeSpan').mockReturnValue({
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
    })

    // Setup mock for getSubmissionGroupsByOrganizationByMeasureType
    // @ts-ignore
    mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType.mockResolvedValue(
      [
        {
          submissionGroupId: 'sg-123',
          submissionGroupName: 'Test Submission Group',
        },
      ] as never
    )

    // Setup mock for getEntities
    // @ts-ignore
    mockMeasureResultsService.getEntities.mockResolvedValue([])

    // Create request object
    const request = {
      organizationId: mockOrganizationId,
      userId: mockUserId,
      measureIdentifier: uppercaseGuid,
      scorecardView: ScorecardView.Monthly,
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      entites: ['sg-123'],
      isPartner: false,
      hasLimitedAccess: false,
      measureType: 'E',
      organizationList: [],
      expansionConfiguration: mockExpansionConfig,
      partners: [],
    }

    // Execute the function under test
    const result = await getECMeasureResultBySubmissionGroupQuery(
      mockAccessToken,
      mockMeasureResultsService as any,
      request
    )

    // Verify the mocks were called with the expected arguments
    expect(mockMeasureResultsService.getAllMeasures).toHaveBeenCalledWith(
      mockOrganizationId
    )
    expect(mapleMeasureQuery).toHaveBeenCalledWith(
      expect.any(MapleMeasuresService),
      mockOrganizationId,
      [uppercaseGuid]
    )

    // Verify the result is empty
    expect(result).toHaveLength(0)
    if (result && result.length > 0) {
      expect(result[0]).toEqual(
        expect.objectContaining({
          measureIdentifier: uppercaseGuid,
          entityCode: 'sg-123',
          entity: 'Test Submission Group',
        })
      )

      // Verify that the Monthly_2023 property exists and has the default value
      expect(result[0]!.Monthly_2023).toBe('-')
    }
  })

  test('should handle case where mapleMeasureQuery returns no results', async () => {
    // Setup mock for measureResultsService.getAllMeasures to return uppercase GUIDs
    // @ts-ignore
    mockMeasureResultsService.getAllMeasures.mockResolvedValue([
      {
        MedisolvMeasureId: uppercaseGuid,
        DenominatorQualifyingType: 'qualifying-type-1',
        SmallestInterval: 'M',
        NullRate: false,
      },
    ])

    // Setup mock for mapleMeasureQuery to return empty results
    // @ts-ignore
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue([])

    // Setup mock for getECMeasureResults
    // @ts-ignore
    mockMeasureResultsService.getECMeasureResults.mockResolvedValue({
      statusResponse: 'Success',
      ecMeasureResultSummaryList: [
        {
          entityCode: 'sg-123',
          entityName: 'Test Submission Group',
          startDate: dayjs('2023-01-01'),
          endDate: dayjs('2023-01-31').add(1, 'day'),
          denominator: 100,
          performance: 85,
        },
      ],
    })

    // Mock DateRange.getColumnIntervalsByCategory to return a specific value
    jest
      .spyOn(DateRange, 'getColumnIntervalsByCategory')
      .mockReturnValue(['Monthly-2023'])

    // Mock DateRange.getDateFromDateRangeSpan to return a specific value
    jest.spyOn(DateRange, 'getDateFromDateRangeSpan').mockReturnValue({
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
    })

    // Setup mock for getSubmissionGroupsByOrganizationByMeasureType
    // @ts-ignore
    mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType.mockResolvedValue(
      [
        {
          submissionGroupId: 'sg-123',
          submissionGroupName: 'Test Submission Group',
        },
      ] as never
    )

    // Setup mock for getEntities
    // @ts-ignore
    mockMeasureResultsService.getEntities.mockResolvedValue([])

    // Create request object
    const request = {
      organizationId: mockOrganizationId,
      userId: mockUserId,
      measureIdentifier: uppercaseGuid,
      scorecardView: ScorecardView.Monthly,
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      entites: ['sg-123'],
      isPartner: false,
      hasLimitedAccess: false,
      measureType: 'E',
      organizationList: [],
      expansionConfiguration: mockExpansionConfig,
      partners: [],
    }

    // Execute the function under test
    const result = await getECMeasureResultBySubmissionGroupQuery(
      mockAccessToken,
      mockMeasureResultsService as any,
      request
    )

    // Verify the mocks were called with the expected arguments
    expect(mockMeasureResultsService.getAllMeasures).toHaveBeenCalledWith(
      mockOrganizationId
    )
    expect(mapleMeasureQuery).toHaveBeenCalledWith(
      expect.any(MapleMeasuresService),
      mockOrganizationId,
      [uppercaseGuid]
    )

    // Verify the result contains the expected data
    expect(result).toHaveLength(1)
    if (result && result.length > 0) {
      expect(result[0]).toEqual(
        expect.objectContaining({
          measureIdentifier: uppercaseGuid,
          entityCode: 'sg-123',
          entity: 'Test Submission Group',
        })
      )

      // Verify that the Monthly_2023 property exists and has the correct value
      expect(result[0]!.Monthly_2023).toBe('85.00')
    }
  })
})
