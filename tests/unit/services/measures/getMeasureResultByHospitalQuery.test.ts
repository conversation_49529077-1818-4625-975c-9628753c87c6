// @ts-nocheck - Disable TypeScript checking for this test file
import { jest } from '@jest/globals'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)

// Mock dependencies
jest.mock('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => fn()),
  redisHelper: {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
  },
}))

jest.mock('@/lib/applicationInsights', () => ({
  trackEvent: jest.fn(),
  trackTrace: jest.fn(),
}))

// Mock the env module to avoid the import error
jest.mock('@/env', () => ({
  env: {
    NEXT_PUBLIC_MAPLE_SERVER_URL: 'http://localhost:3000',
    REDIS_CACHE_TIME_DURATION_SECONDS: '3600',
  },
}))

// Mock the mapleMeasureQuery function
jest.mock('@/services/maple/mapleMeasuresQuery', () => ({
  mapleMeasureQuery: jest.fn() as any,
}))

// Import after mocking
import { getMeasureResultByHospitalQuery } from '@/services/measures/getMeasureResultByHospitalQuery'
import { mapleMeasureQuery } from '@/services/maple/mapleMeasuresQuery'
import MapleMeasuresService from '@/services/mapleMeasures'
import { ScorecardView } from '@/enums/scorecardView'
import { INotation } from '@/enums/iNotation'
import { ExtensionLevels } from '@/enums/extensionLevels'
import { EntityLevelConstants } from '@/types/expansionConfiguration'
import { DateRange } from '@/lib/dateRange'
import { config } from '@/config'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'

describe('getMeasureResultByHospitalQuery', () => {
  // Create mock services with any type to bypass strict type checking in tests
  // @ts-ignore - Ignoring type errors in test mocks
  const mockMeasureResultsService: any = {
    getAllMeasures: jest.fn().mockResolvedValue([]),
    getMeasureResults: jest
      .fn()
      .mockResolvedValue({
        statusResponse: 'Success',
        measureResultSummaryList: [],
      }),
    getEntities: jest.fn().mockResolvedValue([]),
    getFacilityByOrganization: jest.fn().mockResolvedValue([]),
  }

  // Mock access token
  const mockAccessToken = 'mock-access-token'

  // Test data
  const mockOrganizationId = 'org-123'

  // Create a lowercase GUID for maple measures
  const lowercaseGuid = '12345678-abcd-1234-abcd-1234567890ab'

  // Create an uppercase version of the same GUID for measure results
  const uppercaseGuid = lowercaseGuid.toUpperCase()

  // Create mock expansion configuration
  const mockExpansionConfig = [
    {
      id: ExtensionLevels.level2,
      label: 'Test Label',
      measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
      entries: 'Test Entries',
      selectedLevel: EntityLevelConstants.TopLevel,
      partitionKey: 'test',
      rowKey: 'test',
    },
  ]

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock DateRange.getColumnIntervalsByCategory to return a specific value
    jest
      .spyOn(DateRange, 'getColumnIntervalsByCategory')
      .mockReturnValue(['Monthly-2023'])

    // Mock DateRange.getDateFromDateRangeSpan to return a specific value
    jest.spyOn(DateRange, 'getDateFromDateRangeSpan').mockReturnValue({
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
    })
  })

  test('should match lowercase maple measure GUIDs with uppercase measure result GUIDs', async () => {
    // Setup mock for measureResultsService.getAllMeasures to return uppercase GUIDs
    mockMeasureResultsService.getAllMeasures.mockResolvedValue([
      {
        MedisolvMeasureId: uppercaseGuid,
        DenominatorQualifyingType: 'qualifying-type-1',
        SmallestInterval: 'M',
        NullRate: false,
      },
    ])

    // Setup mock for mapleMeasureQuery to return lowercase GUIDs
    // @ts-ignore - Ignoring type errors in test mocks
    const mockMapleMeasureQueryResult = [
      {
        measureIdentifier: lowercaseGuid,
        measureName: 'Test Measure',
        measureDescription: 'Test Measure Description',
        measureFriendlyName: 'Test Friendly Name',
        subDomainName: 'Test Subdomain',
        typeName: 'Test Type',
        domainName: 'Test Domain',
        cMSId: 'CMS-123',
        subTypeName: 'Test Subtype',
        applicationName: 'Test Application',
        programName: 'Test Program',
        iNotationName: INotation.Higher,
      },
    ]
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(
      mockMapleMeasureQueryResult
    )

    // Setup mock for getMeasureResults
    mockMeasureResultsService.getMeasureResults.mockResolvedValue({
      statusResponse: 'Success',
      measureResultSummaryList: [
        {
          entityCode: 'hospital-123',
          entityName: 'Test Hospital',
          startDate: dayjs('2023-01-01'),
          endDate: dayjs('2023-01-31').add(1, 'day'),
          denominator: 100,
          performance: 85,
          measureGUID: uppercaseGuid,
          iNotationName: 'Higher',
        },
      ],
    })

    // Setup mock for getFacilityByOrganization
    mockMeasureResultsService.getFacilityByOrganization.mockResolvedValue([])

    // Create request object
    const request = {
      organizationId: mockOrganizationId,
      measureIdentifier: uppercaseGuid,
      scorecardView: ScorecardView.Monthly,
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      subOrganization: [
        {
          subOrganizationId: 'hospital-123',
          subOrganizationName: 'Test Hospital',
          ccnNumber: 'CCN-123',
        },
      ],
      isPartner: false,
      useSpecialEntityStructure: false,
      organizationList: [],
      expansionConfiguration: mockExpansionConfig,
      partners: [],
    }

    // Execute the function under test
    const result = await getMeasureResultByHospitalQuery(
      mockAccessToken,
      mockMeasureResultsService as any,
      request
    )

    // Verify the mocks were called with the expected arguments
    expect(mockMeasureResultsService.getAllMeasures).toHaveBeenCalledWith(
      mockOrganizationId
    )
    expect(mapleMeasureQuery).toHaveBeenCalledWith(
      expect.any(MapleMeasuresService),
      mockOrganizationId,
      [uppercaseGuid]
    )
    expect(mockMeasureResultsService.getMeasureResults).toHaveBeenCalledWith({
      organizationId: mockOrganizationId,
      periodType: ScorecardView.Monthly,
      subOrganizationId: ['hospital-123'],
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      isPartner: false,
      measureIdentifier: uppercaseGuid,
    })

    // Verify the result contains the expected data
    expect(result).toHaveLength(1)
    if (result && result.length > 0) {
      expect(result[0]).toEqual(
        expect.objectContaining({
          measureIdentifier: uppercaseGuid,
          measureDescription: 'Test Measure Description',
          friendlyName: 'Test Friendly Name',
          subDomain: 'Test Subdomain',
          type: 'Test Type',
          domain: 'Test Domain',
          cmsId: 'CMS-123',
          subType: 'Test Subtype',
          application: 'Test Application',
          programName: 'Test Program',
          hospital: 'Test Hospital',
          hospitalId: 'hospital-123',
          ccnNumber: 'CCN-123',
          isEmptyIndicator: false,
        })
      )

      // Verify that the Monthly_2023 property exists and has the correct value
      expect(result[0]?.Monthly_2023).toBe('85.00')
    }
  })

  test('should handle multiple measures with different GUIDs', async () => {
    // Create a second set of GUIDs
    const lowercaseGuid2 = '87654321-dcba-4321-dcba-0987654321fe'
    const uppercaseGuid2 = lowercaseGuid2.toUpperCase()

    // Setup mock for measureResultsService.getAllMeasures to return uppercase GUIDs
    mockMeasureResultsService.getAllMeasures.mockResolvedValue([
      {
        MedisolvMeasureId: uppercaseGuid,
        DenominatorQualifyingType: 'qualifying-type-1',
        SmallestInterval: 'M',
        NullRate: false,
      },
      {
        MedisolvMeasureId: uppercaseGuid2,
        DenominatorQualifyingType: 'qualifying-type-2',
        SmallestInterval: 'M',
        NullRate: false,
      },
    ])

    // Setup mock for mapleMeasureQuery to return lowercase GUIDs
    // @ts-ignore - Ignoring type errors in test mocks
    const mockMapleMeasureQueryResult = [
      {
        measureIdentifier: lowercaseGuid,
        measureName: 'Test Measure 1',
        measureDescription: 'Test Measure Description 1',
        measureFriendlyName: 'Test Friendly Name 1',
        subDomainName: 'Test Subdomain 1',
        typeName: 'Test Type 1',
        domainName: 'Test Domain 1',
        cMSId: 'CMS-123',
        subTypeName: 'Test Subtype 1',
        applicationName: 'Test Application 1',
        programName: 'Test Program 1',
        iNotationName: INotation.Higher,
      },
    ]
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(
      mockMapleMeasureQueryResult
    )

    // Setup mock for getMeasureResults
    mockMeasureResultsService.getMeasureResults.mockResolvedValue({
      statusResponse: 'Success',
      measureResultSummaryList: [
        {
          entityCode: 'hospital-123',
          entityName: 'Test Hospital',
          startDate: dayjs('2023-01-01'),
          endDate: dayjs('2023-01-31').add(1, 'day'),
          denominator: 100,
          performance: 85,
          measureGUID: uppercaseGuid,
          iNotationName: 'Higher',
        },
      ],
    })

    // Setup mock for getFacilityByOrganization
    mockMeasureResultsService.getFacilityByOrganization.mockResolvedValue([])

    // Create request object
    const request = {
      organizationId: mockOrganizationId,
      measureIdentifier: uppercaseGuid,
      scorecardView: ScorecardView.Monthly,
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      subOrganization: [
        {
          subOrganizationId: 'hospital-123',
          subOrganizationName: 'Test Hospital',
          ccnNumber: 'CCN-123',
        },
      ],
      isPartner: false,
      useSpecialEntityStructure: false,
      organizationList: [],
      expansionConfiguration: mockExpansionConfig,
      partners: [],
    }

    // Execute the function under test
    const result = await getMeasureResultByHospitalQuery(
      mockAccessToken,
      mockMeasureResultsService as any,
      request
    )

    // Verify the mocks were called with the expected arguments
    expect(mockMeasureResultsService.getAllMeasures).toHaveBeenCalledWith(
      mockOrganizationId
    )
    expect(mapleMeasureQuery).toHaveBeenCalledWith(
      expect.any(MapleMeasuresService),
      mockOrganizationId,
      [uppercaseGuid]
    )

    // Verify the result contains the expected data
    expect(result).toHaveLength(1)
    if (result && result.length > 0) {
      expect(result[0]).toEqual(
        expect.objectContaining({
          measureIdentifier: uppercaseGuid,
          measureDescription: 'Test Measure Description 1',
          friendlyName: 'Test Friendly Name 1',
          hospital: 'Test Hospital',
          hospitalId: 'hospital-123',
        })
      )

      // Verify that the Monthly_2023 property exists and has the correct value
      expect(result[0]?.Monthly_2023).toBe('85.00')
    }
  })

  test('should handle empty results from measureResultsService', async () => {
    // Setup mock for measureResultsService.getAllMeasures to return uppercase GUIDs
    mockMeasureResultsService.getAllMeasures.mockResolvedValue([
      {
        MedisolvMeasureId: uppercaseGuid,
        DenominatorQualifyingType: 'qualifying-type-1',
        SmallestInterval: 'M',
        NullRate: false,
      },
    ])

    // Setup mock for mapleMeasureQuery to return lowercase GUIDs
    // @ts-ignore - Ignoring type errors in test mocks
    const mockMapleMeasureQueryResult = [
      {
        measureIdentifier: lowercaseGuid,
        measureName: 'Test Measure',
        measureDescription: 'Test Measure Description',
        measureFriendlyName: 'Test Friendly Name',
        subDomainName: 'Test Subdomain',
        typeName: 'Test Type',
        domainName: 'Test Domain',
        cMSId: 'CMS-123',
        subTypeName: 'Test Subtype',
        applicationName: 'Test Application',
        programName: 'Test Program',
        iNotationName: INotation.Higher,
      },
    ]
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(
      mockMapleMeasureQueryResult
    )

    // Setup mock for getMeasureResults to return empty results
    mockMeasureResultsService.getMeasureResults.mockResolvedValue({
      statusResponse: 'Success',
      measureResultSummaryList: [],
    })

    // Setup mock for getFacilityByOrganization
    mockMeasureResultsService.getFacilityByOrganization.mockResolvedValue([])

    // Create request object
    const request = {
      organizationId: mockOrganizationId,
      measureIdentifier: uppercaseGuid,
      scorecardView: ScorecardView.Monthly,
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      subOrganization: [
        {
          subOrganizationId: 'hospital-123',
          subOrganizationName: 'Test Hospital',
          ccnNumber: 'CCN-123',
        },
      ],
      isPartner: false,
      useSpecialEntityStructure: false,
      organizationList: [],
      expansionConfiguration: mockExpansionConfig,
      partners: [],
    }

    // Execute the function under test
    const result = await getMeasureResultByHospitalQuery(
      mockAccessToken,
      mockMeasureResultsService as any,
      request
    )

    // Verify the mocks were called with the expected arguments
    expect(mockMeasureResultsService.getAllMeasures).toHaveBeenCalledWith(
      mockOrganizationId
    )
    expect(mapleMeasureQuery).toHaveBeenCalledWith(
      expect.any(MapleMeasuresService),
      mockOrganizationId,
      [uppercaseGuid]
    )

    // Verify the result is empty
    expect(result).toHaveLength(0)
  })

  test('should handle case where mapleMeasureQuery returns no results', async () => {
    // Setup mock for measureResultsService.getAllMeasures to return uppercase GUIDs
    mockMeasureResultsService.getAllMeasures.mockResolvedValue([
      {
        MedisolvMeasureId: uppercaseGuid,
        DenominatorQualifyingType: 'qualifying-type-1',
        SmallestInterval: 'M',
        NullRate: false,
      },
    ])

    // Setup mock for mapleMeasureQuery to return empty results
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue([])

    // Setup mock for getMeasureResults
    mockMeasureResultsService.getMeasureResults.mockResolvedValue({
      statusResponse: 'Success',
      measureResultSummaryList: [
        {
          entityCode: 'hospital-123',
          entityName: 'Test Hospital',
          startDate: dayjs('2023-01-01'),
          endDate: dayjs('2023-01-31').add(1, 'day'),
          denominator: 100,
          performance: 85,
          measureGUID: uppercaseGuid,
          iNotationName: 'Higher',
        },
      ],
    })

    // Setup mock for getFacilityByOrganization
    mockMeasureResultsService.getFacilityByOrganization.mockResolvedValue([])

    // Create request object
    const request = {
      organizationId: mockOrganizationId,
      measureIdentifier: uppercaseGuid,
      scorecardView: ScorecardView.Monthly,
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      subOrganization: [
        {
          subOrganizationId: 'hospital-123',
          subOrganizationName: 'Test Hospital',
          ccnNumber: 'CCN-123',
        },
      ],
      isPartner: false,
      useSpecialEntityStructure: false,
      organizationList: [],
      expansionConfiguration: mockExpansionConfig,
      partners: [],
    }

    // Execute the function under test
    const result = await getMeasureResultByHospitalQuery(
      mockAccessToken,
      mockMeasureResultsService as any,
      request
    )

    // Verify the mocks were called with the expected arguments
    expect(mockMeasureResultsService.getAllMeasures).toHaveBeenCalledWith(
      mockOrganizationId
    )
    expect(mapleMeasureQuery).toHaveBeenCalledWith(
      expect.any(MapleMeasuresService),
      mockOrganizationId,
      [uppercaseGuid]
    )

    // Verify the result contains the expected data
    expect(result).toHaveLength(1)
    if (result && result.length > 0) {
      expect(result[0]).toEqual(
        expect.objectContaining({
          measureIdentifier: uppercaseGuid,
          hospital: 'Test Hospital',
          hospitalId: 'hospital-123',
        })
      )

      // Verify that the Monthly_2023 property exists and has the correct value
      expect(result[0]?.Monthly_2023).toBe('85.00')
    }
  })

  test('should handle ratio measures correctly', async () => {
    // Mock config to include the measure as a ratio measure
    config.customMeasures.ratioMeasures = [uppercaseGuid]

    // Setup mock for measureResultsService.getAllMeasures to return uppercase GUIDs
    mockMeasureResultsService.getAllMeasures.mockResolvedValue([
      {
        MedisolvMeasureId: lowercaseGuid,
        DenominatorQualifyingType: 'qualifying-type-1',
        SmallestInterval: 'M',
        NullRate: false,
      },
    ])

    // Setup mock for mapleMeasureQuery to return lowercase GUIDs
    // @ts-ignore - Ignoring type errors in test mocks
    const mockMapleMeasureQueryResult = [
      {
        measureIdentifier: lowercaseGuid,
        measureName: 'Test Ratio Measure',
        measureDescription: 'Test Ratio Measure Description',
        measureFriendlyName: 'Test Ratio Friendly Name',
        subDomainName: 'Test Subdomain',
        typeName: 'Test Type',
        domainName: 'Test Domain',
        cMSId: 'CMS-123',
        subTypeName: 'Test Subtype',
        applicationName: 'Test Application',
        programName: 'Test Program',
        iNotationName: INotation.Higher,
      },
    ]
    ;(mapleMeasureQuery as jest.Mock).mockResolvedValue(
      mockMapleMeasureQueryResult
    )

    // Setup mock for getMeasureResults with numerator and denominator values
    mockMeasureResultsService.getMeasureResults.mockResolvedValue({
      statusResponse: 'Success',
      measureResultSummaryList: [
        {
          entityCode: 'hospital-123',
          entityName: 'Test Hospital',
          startDate: dayjs('2023-01-01'),
          endDate: dayjs('2023-01-31').add(1, 'day'),
          numeratorValue: 85,
          denominatorValue: 100,
          performance: 85,
          measureGUID: lowercaseGuid,
          iNotationName: 'Higher',
        },
      ],
    })

    // Setup mock for getFacilityByOrganization
    mockMeasureResultsService.getFacilityByOrganization.mockResolvedValue([])

    // Create request object
    const request = {
      organizationId: mockOrganizationId,
      measureIdentifier: lowercaseGuid,
      scorecardView: ScorecardView.Monthly,
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
      subOrganization: [
        {
          subOrganizationId: 'hospital-123',
          subOrganizationName: 'Test Hospital',
          ccnNumber: 'CCN-123',
        },
      ],
      isPartner: false,
      useSpecialEntityStructure: false,
      organizationList: [],
      expansionConfiguration: mockExpansionConfig,
      partners: [],
    }

    // Execute the function under test
    const result = await getMeasureResultByHospitalQuery(
      mockAccessToken,
      mockMeasureResultsService as any,
      request
    )

    // Verify the result contains the expected data
    expect(result).toHaveLength(1)
    if (result && result.length > 0) {
      // Verify that the Monthly_2023 property has the ratio format
      expect(result[0]?.Monthly_2023).toBe('85/100<br>85.00')
    }
  })
})
