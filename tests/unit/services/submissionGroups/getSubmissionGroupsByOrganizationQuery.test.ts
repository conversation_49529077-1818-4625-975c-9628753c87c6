import { jest } from '@jest/globals'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)

// Mock dependencies
jest.mock('@/lib/redis', () => ({
    tryCache: jest.fn((key: string, fn: () => any) => fn()),
    redisHelper: {
        get: jest.fn(),
        set: jest.fn(),
        del: jest.fn(),
    },
}))

jest.mock('@/lib/applicationInsights', () => ({
    trackEvent: jest.fn(),
    trackTrace: jest.fn(),
}))

// Mock the env module to avoid the import error
jest.mock('@/env', () => ({
    env: {
        NEXT_PUBLIC_MAPLE_SERVER_URL: 'http://localhost:3000',
        REDIS_CACHE_TIME_DURATION_SECONDS: '3600',
    },
}))

// Mock the getPartnersByUserCommand function
const mockGetPartnersByUserCommand: any = jest.fn()
jest.mock('@/services/partners/getPartnersByUserCommand', () => ({
    getPartnersByUserCommand: mockGetPartnersByUserCommand,
}))

const mockGetOrganizationParametersAsync = jest.fn()
// Mock CitCParametersService constructor
jest.mock('@/services/citc/citcParameters', () => {
    return jest.fn().mockImplementation(() => {
        return {
            getOrganizationParametersAsync: mockGetOrganizationParametersAsync,
            getPartnerParametersAsync: jest.fn(),
        }
    })
})

// Import after mocking
import { getSubmissionGroupsByOrganizationQuery } from '@/services/submissionGroups/getSubmissionGroupsByOrganizationQuery'
import { ScorecardView } from '@/enums/scorecardView'
import { Constants } from '@/enums/constants'
import type { SubmissionGroup } from '@/types/submissionGroup'
import CitCParametersService from '@/services/citc/citcParameters'

describe('getSubmissionGroupsByOrganizationQuery', () => {
    // Mock access token
    const mockAccessToken = 'mock-access-token'

    // Mock user ID
    const mockUserId = 'user-123'

    // Test data
    const mockOrganizationId = 'org-123'

    // Mock MeasureResultsService with any type to bypass TypeScript errors
    const mockMeasureResultsService: any = {
        getSubmissionGroupsByOrganization: jest.fn(),
        getSubmissionGroupsByOrganizationByMeasureType: jest.fn(),
    }

    beforeEach(() => {
        jest.clearAllMocks()
    })

    test('should deduplicate submission groups from different sources', async () => {
        // Create mock submission groups with some duplicates
        const submissionGroup1: SubmissionGroup = {
            submissionGroupId: 'sg-123',
            submissionGroupName: 'Test Submission Group 1',
            processingStartDate: dayjs('2023-01-01'),
            processingEndDate: dayjs('2023-01-31'),
            tin: '123456789',
            providerList: [],
        }

        const submissionGroup2: SubmissionGroup = {
            submissionGroupId: 'sg-456',
            submissionGroupName: 'Test Submission Group 2',
            processingStartDate: dayjs('2023-01-01'),
            processingEndDate: dayjs('2023-01-31'),
            tin: '987654321',
            providerList: [],
        }

        const submissionGroup3: SubmissionGroup = {
            submissionGroupId: 'sg-123',
            submissionGroupName: 'Test Submission Group 1 (Duplicate)',
            processingStartDate: dayjs('2023-01-01'),
            processingEndDate: dayjs('2023-01-31'),
            tin: '123456789',
            providerList: [],
        }

        // Setup mock for getSubmissionGroupsByOrganization
        mockMeasureResultsService.getSubmissionGroupsByOrganization.mockResolvedValue([
            submissionGroup1,
            submissionGroup2,
        ])

        // Setup mock for getSubmissionGroupsByOrganizationByMeasureType
        mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType.mockResolvedValue([
            submissionGroup3, // Duplicate of submissionGroup1
        ])

        // Create request object
        const request = {
            organizationId: mockOrganizationId,
            startDate: dayjs('2023-01-01'),
            endDate: dayjs('2023-01-31'),
            isPartner: false,
        }

        // Execute the function under test
        const result = await getSubmissionGroupsByOrganizationQuery(
            mockAccessToken,
            mockMeasureResultsService,
            mockUserId,
            request
        )

        // Verify the mocks were called with the expected arguments
        expect(mockMeasureResultsService.getSubmissionGroupsByOrganization).toHaveBeenCalledWith({
            organizationId: mockOrganizationId,
            isPartner: false,
        })

        expect(mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType).toHaveBeenCalledWith({
            organizationId: mockOrganizationId,
            isPartner: false,
            measureType: ScorecardView.Quarterly,
        })

        // Verify the result contains the expected data
        expect(result).toHaveLength(2) // Should have 2 items after deduplication

        // Check that the result contains the expected submission groups
        expect(result).toEqual(expect.arrayContaining([
            expect.objectContaining(submissionGroup3),
            expect.objectContaining(submissionGroup2),
        ]))

        // Ensure the duplicate was removed
        const duplicateCount = result.filter(sg => sg.submissionGroupId === 'sg-123').length
        expect(duplicateCount).toBe(1)
    })

    test('should filter submission groups by date range', async () => {
        // Create mock submission groups with different date ranges
        const inRangeGroup1: SubmissionGroup = {
            submissionGroupId: 'sg-123',
            submissionGroupName: 'In Range Group 1',
            processingStartDate: dayjs('2023-01-15'), // Within range
            processingEndDate: dayjs('2023-02-15'),   // Within range
            tin: '123456789',
            providerList: [],
        }

        const inRangeGroup2: SubmissionGroup = {
            submissionGroupId: 'sg-456',
            submissionGroupName: 'In Range Group 2',
            processingStartDate: dayjs('2022-12-15'), // Before range start
            processingEndDate: dayjs('2023-01-15'),   // Within range
            tin: '987654321',
            providerList: [],
        }

        const inRangeGroup3: SubmissionGroup = {
            submissionGroupId: 'sg-789',
            submissionGroupName: 'In Range Group 3',
            processingStartDate: dayjs('2023-02-15'), // Within range
            processingEndDate: dayjs('2023-03-15'),   // After range end
            tin: '456789123',
            providerList: [],
        }

        const outOfRangeGroup: SubmissionGroup = {
            submissionGroupId: 'sg-999',
            submissionGroupName: 'Out of Range Group',
            processingStartDate: dayjs('2022-11-01'), // Before range
            processingEndDate: dayjs('2022-12-01'),   // Before range
            tin: '111222333',
            providerList: [],
        }

        // Setup mock for getSubmissionGroupsByOrganization
        mockMeasureResultsService.getSubmissionGroupsByOrganization.mockResolvedValue([
            inRangeGroup1,
            inRangeGroup2,
            outOfRangeGroup,
        ])

        // Setup mock for getSubmissionGroupsByOrganizationByMeasureType
        mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType.mockResolvedValue([
            inRangeGroup3,
        ])

        // Create request object with specific date range
        const request = {
            organizationId: mockOrganizationId,
            startDate: dayjs('2023-01-01'),
            endDate: dayjs('2023-02-28'),
            isPartner: false,
        }

        // Execute the function under test
        const result = await getSubmissionGroupsByOrganizationQuery(
            mockAccessToken,
            mockMeasureResultsService,
            mockUserId,
            request
        )

        // Verify the result contains only the in-range groups
        expect(result).toHaveLength(3)

        // Check that the result contains the expected submission groups
        expect(result).toEqual(expect.arrayContaining([
            expect.objectContaining({
                submissionGroupId: 'sg-123',
                submissionGroupName: 'In Range Group 1',
            }),
            expect.objectContaining({
                submissionGroupId: 'sg-456',
                submissionGroupName: 'In Range Group 2',
            }),
            expect.objectContaining({
                submissionGroupId: 'sg-789',
                submissionGroupName: 'In Range Group 3',
            }),
        ]))

        // Ensure the out-of-range group was filtered out
        const outOfRangeCount = result.filter(sg => sg.submissionGroupId === 'sg-999').length
        expect(outOfRangeCount).toBe(0)
    })

    test('should handle partner organizations correctly', async () => {
        // Create mock submission groups
        const submissionGroup1: SubmissionGroup = {
            submissionGroupId: 'ec-123-sg-1',
            submissionGroupName: 'Partner Submission Group 1',
            processingStartDate: dayjs('2023-01-01'),
            processingEndDate: dayjs('2023-01-31'),
            tin: '123456789',
            providerList: [],
        }

        const submissionGroup2: SubmissionGroup = {
            submissionGroupId: 'ec-456-sg-2',
            submissionGroupName: 'Partner Submission Group 2',
            processingStartDate: dayjs('2023-01-01'),
            processingEndDate: dayjs('2023-01-31'),
            tin: '987654321',
            providerList: [],
        }

        const submissionGroup3: SubmissionGroup = {
            submissionGroupId: 'other-sg-3',
            submissionGroupName: 'Other Submission Group',
            processingStartDate: dayjs('2023-01-01'),
            processingEndDate: dayjs('2023-01-31'),
            tin: '456789123',
            providerList: [],
        }

        // Setup mock for getSubmissionGroupsByOrganization
        mockMeasureResultsService.getSubmissionGroupsByOrganization.mockResolvedValue([
            submissionGroup1,
            submissionGroup2,
            submissionGroup3,
        ])

        // Setup mock for getSubmissionGroupsByOrganizationByMeasureType
        mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType.mockResolvedValue([])

        // Setup mock for getPartnersByUserCommand
        mockGetPartnersByUserCommand.mockResolvedValue([
            {
                id: mockOrganizationId,
                organizations: [
                    {
                        organizationId: 'org-sub-1',
                        organizationName: 'Sub Organization 1',
                    },
                    {
                        organizationId: 'org-sub-2',
                        organizationName: 'Sub Organization 2',
                    },
                ],
            },
        ])

        // Setup mock for getOrganizationParametersAsync
        mockGetOrganizationParametersAsync.mockImplementation((orgId) => {
            if (orgId === 'org-sub-1') {
                return Promise.resolve([
                    {
                        key: Constants.EcOverallGroupId,
                        value: 'ec-123',
                    },
                ])
            } else if (orgId === 'org-sub-2') {
                return Promise.resolve([
                    {
                        key: Constants.EcOverallGroupId,
                        value: 'ec-456',
                    },
                ])
            }
            return Promise.resolve([])
        })

        // Create request object for partner
        const request = {
            organizationId: mockOrganizationId,
            startDate: dayjs('2023-01-01'),
            endDate: dayjs('2023-01-31'),
            isPartner: true,
        }

        // Execute the function under test
        const result = await getSubmissionGroupsByOrganizationQuery(
            mockAccessToken,
            mockMeasureResultsService,
            mockUserId,
            request
        )

        // Verify the mocks were called with the expected arguments
        expect(mockGetPartnersByUserCommand).toHaveBeenCalledWith(mockUserId, mockAccessToken)
        expect(mockGetOrganizationParametersAsync).toHaveBeenCalledWith('org-sub-1')
        expect(mockGetOrganizationParametersAsync).toHaveBeenCalledWith('org-sub-2')

        // Verify the result contains only the submission groups that match the EcOverallGroupId
        expect(result).toHaveLength(2)

        // Check that the result contains the expected submission groups
        expect(result).toEqual(expect.arrayContaining([
            expect.objectContaining({
                submissionGroupId: 'ec-123-sg-1',
                submissionGroupName: 'Partner Submission Group 1',
            }),
            expect.objectContaining({
                submissionGroupId: 'ec-456-sg-2',
                submissionGroupName: 'Partner Submission Group 2',
            }),
        ]))

        // Ensure the non-matching group was filtered out
        const nonMatchingCount = result.filter(sg => sg.submissionGroupId === 'other-sg-3').length
        expect(nonMatchingCount).toBe(0)
    })

    test('should sort submission groups by name when there are multiple groups', async () => {
        // Create mock submission groups with names in non-alphabetical order
        const submissionGroup1: SubmissionGroup = {
            submissionGroupId: 'sg-123',
            submissionGroupName: 'Z Submission Group',
            processingStartDate: dayjs('2023-01-01'),
            processingEndDate: dayjs('2023-01-31'),
            tin: '123456789',
            providerList: [],
        }

        const submissionGroup2: SubmissionGroup = {
            submissionGroupId: 'sg-456',
            submissionGroupName: 'A Submission Group',
            processingStartDate: dayjs('2023-01-01'),
            processingEndDate: dayjs('2023-01-31'),
            tin: '987654321',
            providerList: [],
        }

        const submissionGroup3: SubmissionGroup = {
            submissionGroupId: 'sg-789',
            submissionGroupName: 'M Submission Group',
            processingStartDate: dayjs('2023-01-01'),
            processingEndDate: dayjs('2023-01-31'),
            tin: '456789123',
            providerList: [],
        }

        // Setup mock for getSubmissionGroupsByOrganization
        mockMeasureResultsService.getSubmissionGroupsByOrganization.mockResolvedValue([
            submissionGroup1,
            submissionGroup2,
        ])

        // Setup mock for getSubmissionGroupsByOrganizationByMeasureType
        mockMeasureResultsService.getSubmissionGroupsByOrganizationByMeasureType.mockResolvedValue([
            submissionGroup3,
        ])

        // Create request object
        const request = {
            organizationId: mockOrganizationId,
            startDate: dayjs('2023-01-01'),
            endDate: dayjs('2023-01-31'),
            isPartner: false,
        }

        // Execute the function under test
        const result = await getSubmissionGroupsByOrganizationQuery(
            mockAccessToken,
            mockMeasureResultsService,
            mockUserId,
            request
        )

        // Verify the result is sorted by submissionGroupName
        expect(result).toHaveLength(3)
        expect(result[0]?.submissionGroupName).toBe('A Submission Group')
        expect(result[1]?.submissionGroupName).toBe('M Submission Group')
        expect(result[2]?.submissionGroupName).toBe('Z Submission Group')
    })
})
