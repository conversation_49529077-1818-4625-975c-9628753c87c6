import { jest } from '@jest/globals'
import { ACOPerformanceAnalysisService } from '@/services/reports/ACOPerformanceAnalysisService'
import {
  SisenseMeasureSummaryRepository,
  SisenseMeasureSummary,
} from '@/services/sisense/SisenseMeasureSummaryRepository'
import { IntervalType } from '@/types/intervalType'
import { ReportRequest } from '@/types/reports/medisolvReport'

jest.mock('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => fn()),
}))

jest.mock('@/env', () => ({
  env: {},
}))

// Create mock functions
const mockFindMeasurePerformanceByUniverse = jest
  .fn()
  .mockReturnValue(Promise.resolve([]))
const mockFindMeasurePerformanceByOrgCode = jest
  .fn()
  .mockReturnValue(Promise.resolve([]))

// Create a mock repository with the mocked methods
const mockRepository = {
  findMeasurePerformanceByUniverse: mockFindMeasurePerformanceByUniverse,
  findMeasurePerformanceByOrgCode: mockFindMeasurePerformanceByOrgCode,
} as unknown as SisenseMeasureSummaryRepository

describe('ACOPerformanceAnalysisService', () => {
  let service: ACOPerformanceAnalysisService

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()

    // Create a new instance of the service with the mocked repository
    service = new ACOPerformanceAnalysisService(mockRepository)
  })

  describe('getACOMeasurePerformanceByUniverse', () => {
    test('should call repository method with correct parameters', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        universe: 'TestUniverse',
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure2',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.9,
          Numerator: 90,
          PerformanceDenominator: 100,
          PTile: 80,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '456',
        },
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByUniverse.mockResolvedValue(mockResults)

      // Act
      await service.getACOMeasurePerformanceByUniverse(reportRequest)

      // Assert
      // Verify that the repository method was called with the correct parameters
      expect(mockFindMeasurePerformanceByUniverse).toHaveBeenCalledWith(
        reportRequest.intervalType,
        reportRequest.startDate,
        reportRequest.endDate,
        reportRequest.measureIdentifiers,
        reportRequest.universe,
        reportRequest.sourceContainerIdentifier
      )
    })

    test('should return complete results with filled in missing measures', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        universe: 'TestUniverse',
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        // Note: Measure2 is missing for Entity 1
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByUniverse.mockResolvedValue(mockResults)

      // Act
      const result =
        await service.getACOMeasurePerformanceByUniverse(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(2) // 1 entity x 2 measures = 2 results

      // Check that the results include the original data
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: 0.85,
        })
      )

      // Check that placeholder data was created for missing measure
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: null,
          Numerator: null,
          PerformanceDenominator: null,
        })
      )
    })

    test('should fill in gaps with placeholder data for multiple missing measures', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456', '789'],
        universe: 'TestUniverse',
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        // No data for Measure2 or Measure3
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByUniverse.mockResolvedValue(mockResults)

      // Act
      const result =
        await service.getACOMeasurePerformanceByUniverse(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(3) // 1 entity x 3 measures = 3 results

      // Check that the results include the original data
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: 0.85,
          MeasureIdentifier: '123',
        })
      )

      // Check that placeholder data was created for missing measures
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: null,
          Numerator: null,
          PerformanceDenominator: null,
          MeasureIdentifier: '456',
        })
      )

      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: null,
          Numerator: null,
          PerformanceDenominator: null,
          MeasureIdentifier: '789',
        })
      )
    })

    test('should handle empty results from repository', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        universe: 'TestUniverse',
        sourceContainerIdentifier: 'TestContainer',
      }

      // Mock the repository method to return an empty array
      // @ts-ignore
      mockFindMeasurePerformanceByUniverse.mockResolvedValue([])

      // Act
      const result =
        await service.getACOMeasurePerformanceByUniverse(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(0) // No entities, so no results
    })

    test('should handle multiple entities with different measures', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        universe: 'TestUniverse',
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure2',
          EntityId: 2,
          EntityDescription: 'Entity 2',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.9,
          Numerator: 90,
          PerformanceDenominator: 100,
          PTile: 80,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '456',
        },
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByUniverse.mockResolvedValue(mockResults)

      // Act
      const result =
        await service.getACOMeasurePerformanceByUniverse(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(4) // 2 entities x 2 measures = 4 results

      // Check that the results include the original data
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: 0.85,
        })
      )

      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure2',
          EntityId: 2,
          EntityDescription: 'Entity 2',
          Performance: 0.9,
        })
      )

      // Check that placeholder data was created for missing measures
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: null,
        })
      )

      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 2,
          EntityDescription: 'Entity 2',
          Performance: null,
        })
      )
    })

    test('should not fill in missing measures when hideEmptyIndicators is true', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        universe: 'TestUniverse',
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        // Note: Measure2 is missing for Entity 1
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByUniverse.mockResolvedValue(mockResults)

      // Act
      const result =
        await service.getACOMeasurePerformanceByUniverse(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(2) // Only the actual data, no placeholders

      // Check that the results include only the original data
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: 0.85,
        })
      )

      // Check that no placeholder data was created for missing measure
      const measure2Result = result!.find(
        (item) => item.MeasureName === 'Measure2' && item.EntityId === 1
      )
      expect(measure2Result).toBeUndefined()
    })
  })

  describe('getACOMeasurePerformanceByOrgCode', () => {
    test('should call repository method with correct parameters', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        code: 123,
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure2',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.9,
          Numerator: 90,
          PerformanceDenominator: 100,
          PTile: 80,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '456',
        },
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByOrgCode.mockResolvedValue(mockResults)

      // Act
      await service.getACOMeasurePerformanceByOrgCode(reportRequest)

      // Assert
      // Verify that the repository method was called with the correct parameters
      expect(mockFindMeasurePerformanceByOrgCode).toHaveBeenCalledWith(
        reportRequest.intervalType,
        reportRequest.startDate,
        reportRequest.endDate,
        reportRequest.measureIdentifiers,
        reportRequest.sourceContainerIdentifier,
        reportRequest.code
      )
    })

    test('should return complete results with filled in missing measures', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        code: 123,
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        // Note: Measure2 is missing for Entity 1
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByOrgCode.mockResolvedValue(mockResults)

      // Act
      const result =
        await service.getACOMeasurePerformanceByOrgCode(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(2) // 1 entity x 2 measures = 2 results

      // Check that the results include the original data
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: 0.85,
          MeasureIdentifier: '123',
        })
      )

      // Check that placeholder data was created for missing measure
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: null,
          Numerator: null,
          PerformanceDenominator: null,
          MeasureIdentifier: '456',
        })
      )
    })

    test('should fill in gaps with placeholder data for multiple missing measures', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456', '789'],
        code: 123,
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        // No data for Measure2 or Measure3
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByOrgCode.mockResolvedValue(mockResults)

      // Act
      const result =
        await service.getACOMeasurePerformanceByOrgCode(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(3) // 1 entity x 3 measures = 3 results

      // Check that the results include the original data
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: 0.85,
          MeasureIdentifier: '123',
        })
      )

      // Check that placeholder data was created for missing measures
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: null,
          Numerator: null,
          PerformanceDenominator: null,
          MeasureIdentifier: '456',
        })
      )

      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: null,
          Numerator: null,
          PerformanceDenominator: null,
          MeasureIdentifier: '789',
        })
      )
    })

    test('should handle empty results from repository', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        code: 123,
        sourceContainerIdentifier: 'TestContainer',
      }

      // Mock the repository method to return an empty array
      // @ts-ignore
      mockFindMeasurePerformanceByOrgCode.mockResolvedValue([])

      // Act
      const result =
        await service.getACOMeasurePerformanceByOrgCode(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(0) // No entities, so no results
    })

    test('should handle multiple entities with different measures', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        code: 123,
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure2',
          EntityId: 2,
          EntityDescription: 'Entity 2',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.9,
          Numerator: 90,
          PerformanceDenominator: 100,
          PTile: 80,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '456',
        },
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByOrgCode.mockResolvedValue(mockResults)

      // Act
      const result =
        await service.getACOMeasurePerformanceByOrgCode(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(4) // 2 entities x 2 measures = 4 results

      // Check that the results include the original data
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: 0.85,
          MeasureIdentifier: '123',
        })
      )

      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure2',
          EntityId: 2,
          EntityDescription: 'Entity 2',
          Performance: 0.9,
          MeasureIdentifier: '456',
        })
      )

      // Check that placeholder data was created for missing measures
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: null,
          MeasureIdentifier: '456',
        })
      )

      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: '-',
          EntityId: 2,
          EntityDescription: 'Entity 2',
          Performance: null,
          MeasureIdentifier: '123',
        })
      )
    })

    test('should not fill in missing measures when hideEmptyIndicators is true', async () => {
      // Arrange
      const reportRequest: ReportRequest = {
        intervalType: 'M' as IntervalType,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-02-01'),
        measureIdentifiers: ['123', '456'],
        code: 123,
        sourceContainerIdentifier: 'TestContainer',
      }

      const mockResults: SisenseMeasureSummary[] = [
        {
          Period: 'M',
          StartDate: '2023-01-01',
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Organization: 'Org1',
          SourceContainerIdentifier: 'TestContainer',
          Performance: 0.85,
          Numerator: 85,
          PerformanceDenominator: 100,
          PTile: 75,
          PercentileSourceCode: 'PS1',
          SummaryDataRowCount: 1,
          MeasureIdentifier: '123',
        },
        // Note: Measure2 is missing for Entity 1
      ]

      // Mock the repository method to return the test data
      // @ts-ignore
      mockFindMeasurePerformanceByOrgCode.mockResolvedValue(mockResults)

      // Act
      const result =
        await service.getACOMeasurePerformanceByOrgCode(reportRequest)

      // Assert
      expect(result).toBeDefined()
      expect(result!.length).toBe(2)

      // Check that the results include only the original data
      expect(result!).toContainEqual(
        expect.objectContaining({
          MeasureName: 'Measure1',
          EntityId: 1,
          EntityDescription: 'Entity 1',
          Performance: 0.85,
          MeasureIdentifier: '123',
        })
      )

      // Check that no placeholder data was created for missing measure
      const measure2Result = result!.find(
        (item) => item.MeasureName === 'Measure2' && item.EntityId === 1
      )
      expect(measure2Result).toBeUndefined()
    })
  })
})
