import { jest } from '@jest/globals'
import { ScorecardView } from '@/enums/scorecardView'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { OrganizationPaidMeasure } from '@/types/organizationPaidMeasure'
import { DateRange } from '@/lib/dateRange'
import TrendCSSHelper from '@/lib/trendCSSHelper'
import dayjs, { Dayjs } from 'dayjs'
import { INotation } from '@/enums/iNotation'
import { SimplifiedParameter } from '@/types/simplifiedParameter'
import { getOrganizationPaidMeasuresQuery } from '@/prisma/getOrganizationPaidMeasuresQuery'
import { ECMeasureResultSummary } from '@/types/eCMeasureResultSummary'
import { ScorecardDetails } from '@/types/scorecards/scorecardDetails'
import { ADMMeasure } from '@/types/admMeasure'
import { Performance } from '@/enums/performance'

jest.mock('@/lib/applicationInsights', () => ({
  trackTrace: jest.fn(),
  trackException: jest.fn(),
  trackEvent: jest.fn(),
  loadAppInsights: jest.fn(),
}))

// Mock dependencies
jest.mock('@/lib/redis', () => ({
  tryCache: jest.fn((key: string, fn: () => any) => fn()),
}))

// Mock dependencies
jest.mock('@/prisma/getOrganizationPaidMeasuresQuery', () => ({
  getOrganizationPaidMeasuresQuery: jest.fn(),
}))

jest.mock('@/lib/dateRange', () => ({
  DateRange: {
    getColumnIntervalsByCategory: jest.fn(),
    getDateFromDateRangeSpan: jest.fn(),
  },
}))

jest.mock('@/lib/trendCSSHelper', () => ({
  __esModule: true,
  default: {
    getTrendSlopeCss: jest.fn(),
  },
}))

// Mock getScorecardDetails
jest.mock('@/services/scorecard/baseScorecardProvider', () => ({
  getScorecardDetails: jest.fn(),
}))

// Mock CitCParametersService
const mockCitCParametersService = {
  getPartnerParametersAsync: jest.fn(),
  getOrganizationParametersAsync: jest.fn(),
}

jest.mock('@/services/citc/citcParameters', () => {
  return jest.fn().mockImplementation(() => mockCitCParametersService)
})

// Import the function to test
import { getECScorecardByMeasure } from '@/services/scorecard/getScorecardsByECMeasures'
import { getScorecardDetails } from '@/services/scorecard/baseScorecardProvider'

describe('getECScorecardByMeasure', () => {
  // Create mock services and data
  const mockMeasureResultsService = {
    getAllMeasures: jest.fn(),
    getECMeasureResults: jest.fn(),
  }

  const mockStartDate: Dayjs = dayjs('2023-01-01')
  const mockEndDate: Dayjs = dayjs('2023-12-31')
  const mockOrganizationId: string = 'org-123'
  const mockAccessToken: string = 'token-123'
  const mockSubmissionGroupIds: string[] = ['sg-1', 'sg-2']

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should return empty array when no measures are found', async () => {
    // Arrange
    const mockParameters: SimplifiedParameter[] = []
    const mockPaidMeasures: OrganizationPaidMeasure[] = []
    const mockDateRangeSpans: string[] = []

    // Mock the dependencies
    mockCitCParametersService.getOrganizationParametersAsync.mockResolvedValue(
        // @ts-ignore
        mockParameters
    )
    ;(getOrganizationPaidMeasuresQuery as jest.Mock).mockResolvedValue(
        // @ts-ignore
        mockPaidMeasures
    )
    ;(DateRange.getColumnIntervalsByCategory as jest.Mock).mockReturnValue(
        // @ts-ignore
        mockDateRangeSpans
    )
    ;(mockMeasureResultsService.getAllMeasures as jest.Mock).mockResolvedValue(
        // @ts-ignore
        []
    )
    ;(
        mockMeasureResultsService.getECMeasureResults as jest.Mock
    )
        // @ts-ignore
        .mockResolvedValue({
          ecMeasureResultSummaryList: [],
        })

    // Act
    const result = await getECScorecardByMeasure(
        mockMeasureResultsService as any,
        ScorecardView.Monthly,
        mockOrganizationId,
        false, // isPartner
        mockStartDate,
        mockEndDate,
        mockSubmissionGroupIds,
        PrimaryMeasureTypeConstants.HospitalMeasures,
        mockAccessToken
    )

    // Assert
    expect(result).toEqual([])
    expect(
        mockCitCParametersService.getOrganizationParametersAsync
    ).toHaveBeenCalledWith(mockOrganizationId)
    expect(getOrganizationPaidMeasuresQuery).toHaveBeenCalledWith(
        mockMeasureResultsService,
        mockStartDate,
        mockEndDate,
        PrimaryMeasureTypeConstants.HospitalMeasures,
        mockAccessToken,
        mockParameters,
        mockOrganizationId
    )
  })

  test('should process measures correctly with consistent casing of measureIdentifiers', async () => {
    // Arrange
    const mockParameters: SimplifiedParameter[] = [
      { key: 'param1', value: 'value1' },
    ]

    // Use consistent casing (uppercase) for measureIdentifier
    const measureGuid = 'A1B2C3D4-E5F6-7890-ABCD-EF1234567890'

    const mockPaidMeasures = [
      {
        measureIdentifier: measureGuid,
        measureTitle: 'Measure 1',
        measureName: 'Measure 1',
        measureDescription: 'Description 1 ',
        measureFriendlyName: 'Friendly Name 1',
        subDomainName: 'Sub Domain 1',
        typeName: 'Type 1',
        domainName: 'Domain 1',
        cMSId: 'CMS1',
        subTypeName: 'Rate',
        applicationName: 'App 1',
        programName: 'Program 1',
        iNotationName: INotation.Higher,
        versionNumber: '1.0',
        subId: 'a',
      },
    ] as OrganizationPaidMeasure[]

    // Use the same casing (uppercase) for MedisolvMeasureId
    const mockAdmMeasures = [
      {
        Id: 1,
        MedisolvMeasureId: measureGuid,
        Name: 'Measure 1',
        NullRate: false,
        SmallestInterval: 'M',
        DenominatorQualifyingType: 'type1',
        NullDenominator: false,
        NullNumerator: false,
        NullIPP: false,
        NullDenExcl: false,
        NullNumExcl: false,
      },
    ] as ADMMeasure[]

    // Use the same casing (uppercase) for measureGUID
    const mockMeasureRates = [
      {
        id: 1,
        entityName: 'Entity 1',
        measureGUID: measureGuid,
        measureSubId: 'measureSubId',
        entityCode: 'entityCode',
        sourceContainerIdentifier: 'sourceContainerIdentifier',
        period: 'period',
        startDate: mockStartDate,
        endDate: mockEndDate,
        performance: 85,
        denominator: 100,
        numerator: 85,
        ipp: 120,
        denominatorExclusion: 20,
        numeratorExclusion: 0,
        hoverText: 'Hover text',
      },
    ] as ECMeasureResultSummary[]

    const mockDateRangeSpans = ['Jan-2023']
    const mockDateRange = {
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
    }

    const mockScorecardDetails: ScorecardDetails = {
      id: 0,
      columnName: 'Jan_2023',
      performance: Performance.NoData,
      goal: null,
    }

    // Mock the dependencies
    mockCitCParametersService.getOrganizationParametersAsync.mockResolvedValue(
        // @ts-ignore
        mockParameters
    )
    ;(getOrganizationPaidMeasuresQuery as jest.Mock).mockResolvedValue(
        // @ts-ignore
        mockPaidMeasures
    )
    ;(DateRange.getColumnIntervalsByCategory as jest.Mock).mockReturnValue(
        // @ts-ignore
        mockDateRangeSpans
    )
    // @ts-ignore
    ;(DateRange.getDateFromDateRangeSpan as jest.Mock).mockReturnValue(
        mockDateRange
    )
    ;(mockMeasureResultsService.getAllMeasures as jest.Mock).mockResolvedValue(
        // @ts-ignore
        mockAdmMeasures
    )
    ;(
        mockMeasureResultsService.getECMeasureResults as jest.Mock
    )
        // @ts-ignore
        .mockResolvedValue({
          ecMeasureResultSummaryList: mockMeasureRates,
        })
    // @ts-ignore
    ;(TrendCSSHelper.getTrendSlopeCss as jest.Mock).mockReturnValue('trend-up')
    // @ts-ignore
    ;(getScorecardDetails as jest.Mock).mockReturnValue(mockScorecardDetails)

    // Act
    const result = await getECScorecardByMeasure(
        mockMeasureResultsService as any,
        ScorecardView.Monthly,
        mockOrganizationId,
        false, // isPartner
        mockStartDate,
        mockEndDate,
        mockSubmissionGroupIds,
        PrimaryMeasureTypeConstants.HospitalMeasures,
        mockAccessToken
    )

    // Assert
    expect(result).toHaveLength(1)

    // Use toMatchObject instead of toEqual(expect.objectContaining()) for more flexible matching
    expect(result[0]).toMatchObject({
      measureIdentifier: measureGuid, // Should maintain the same casing
      measureTitle: 'Measure 1',
      measureDescription: 'Description 1',
      friendlyName: 'Friendly Name 1',
      subDomain: 'Sub Domain 1',
      type: 'Type 1',
      domain: 'Domain 1',
      cmsId: 'CMS1',
      subType: 'Rate',
      application: 'App 1',
      programName: 'Program 1',
      smallestInterval: 'M',
      trendCss: 'trend-up',
      scorecardDetailsList: [mockScorecardDetails],
    })

    // Verify that getOrganizationPaidMeasuresQuery was called with the correct parameters
    expect(getOrganizationPaidMeasuresQuery).toHaveBeenCalledWith(
        mockMeasureResultsService,
        mockStartDate,
        mockEndDate,
        PrimaryMeasureTypeConstants.HospitalMeasures,
        mockAccessToken,
        mockParameters,
        mockOrganizationId
    )

    // Verify that getAllMeasures was called with the correct parameters
    expect(mockMeasureResultsService.getAllMeasures).toHaveBeenCalledWith(
        mockOrganizationId
    )

    // Verify that getECMeasureResults was called with the correct parameters
    expect(mockMeasureResultsService.getECMeasureResults).toHaveBeenCalledWith(
        expect.objectContaining({
          organizationId: mockOrganizationId,
          periodType: ScorecardView.Monthly,
          submissionGroupId: mockSubmissionGroupIds,
          startDate: mockStartDate,
          endDate: mockEndDate,
          isPartner: false,
          isSubmissionGroupLevel: true,
          measureIdentifier: '',
        })
    )

    // Verify that TrendCSSHelper.getTrendSlopeCss was called
    expect(TrendCSSHelper.getTrendSlopeCss).toHaveBeenCalled()
  })

  test('should handle partner selection correctly', async () => {
    // Arrange
    const mockParameters: SimplifiedParameter[] = [
      { key: 'param1', value: 'value1' },
    ]

    // Use consistent casing (uppercase) for measureIdentifier
    const measureGuid = 'A1B2C3D4-E5F6-7890-ABCD-EF1234567890'

    const mockPaidMeasures = [
      {
        measureIdentifier: measureGuid,
        measureTitle: 'Measure 1',
        measureName: 'Measure 1',
        measureDescription: 'Description 1 ',
        measureFriendlyName: 'Friendly Name 1',
        subDomainName: 'Sub Domain 1',
        typeName: 'Type 1',
        domainName: 'Domain 1',
        cMSId: 'CMS1',
        subTypeName: 'Rate',
        applicationName: 'App 1',
        programName: 'Program 1',
        iNotationName: INotation.Higher,
        versionNumber: '1.0',
        subId: 'a',
      },
    ] as OrganizationPaidMeasure[]

    // Use the same casing (uppercase) for MedisolvMeasureId
    const mockAdmMeasures = [
      {
        Id: 1,
        MedisolvMeasureId: measureGuid,
        Name: 'Measure 1',
        NullRate: false,
        SmallestInterval: 'M',
        DenominatorQualifyingType: 'type1',
        NullDenominator: false,
        NullNumerator: false,
        NullIPP: false,
        NullDenExcl: false,
        NullNumExcl: false,
      },
    ] as ADMMeasure[]

    // Use the same casing (uppercase) for measureGUID
    const mockMeasureRates = [
      {
        id: 1,
        entityName: 'Entity 1',
        measureGUID: measureGuid,
        measureSubId: 'measureSubId',
        entityCode: 'entityCode',
        sourceContainerIdentifier: 'sourceContainerIdentifier',
        period: 'period',
        startDate: mockStartDate,
        endDate: mockEndDate,
        performance: 85,
        denominator: 100,
        numerator: 85,
        ipp: 120,
        denominatorExclusion: 20,
        numeratorExclusion: 0,
        hoverText: 'Hover text',
      },
    ] as ECMeasureResultSummary[]

    const mockDateRangeSpans = ['Jan-2023']
    const mockDateRange = {
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
    }

    const mockScorecardDetails: ScorecardDetails = {
      id: 0,
      columnName: 'Jan_2023',
      performance: Performance.NoData,
      goal: null,
    }

    // Mock the dependencies
    mockCitCParametersService.getPartnerParametersAsync.mockResolvedValue(
        // @ts-ignore
        mockParameters
    )
    ;(getOrganizationPaidMeasuresQuery as jest.Mock).mockResolvedValue(
        // @ts-ignore
        mockPaidMeasures
    )
    // @ts-ignore
    ;(DateRange.getColumnIntervalsByCategory as jest.Mock).mockReturnValue(
        mockDateRangeSpans
    )
    // @ts-ignore
    ;(DateRange.getDateFromDateRangeSpan as jest.Mock).mockReturnValue(
        mockDateRange
    )
    ;(mockMeasureResultsService.getAllMeasures as jest.Mock).mockResolvedValue(
        // @ts-ignore
        mockAdmMeasures
    )
    ;(
        mockMeasureResultsService.getECMeasureResults as jest.Mock
    )
        // @ts-ignore
        .mockResolvedValue({
          ecMeasureResultSummaryList: mockMeasureRates,
        })
    // @ts-ignore
    ;(TrendCSSHelper.getTrendSlopeCss as jest.Mock).mockReturnValue('trend-up')
    // @ts-ignore
    ;(getScorecardDetails as jest.Mock).mockReturnValue(mockScorecardDetails)

    // Act
    const result = await getECScorecardByMeasure(
        mockMeasureResultsService as any,
        ScorecardView.Monthly,
        mockOrganizationId,
        true, // isPartner
        mockStartDate,
        mockEndDate,
        mockSubmissionGroupIds,
        PrimaryMeasureTypeConstants.HospitalMeasures,
        mockAccessToken
    )

    // Assert
    expect(result).toHaveLength(1)

    // Use toMatchObject instead of toEqual(expect.objectContaining()) for more flexible matching
    expect(result[0]).toMatchObject({
      measureIdentifier: measureGuid, // Should maintain the same casing
      measureTitle: 'Measure 1',
      measureDescription: 'Description 1',
      friendlyName: 'Friendly Name 1',
      subDomain: 'Sub Domain 1',
      type: 'Type 1',
      domain: 'Domain 1',
      cmsId: 'CMS1',
      subType: 'Rate',
      application: 'App 1',
      programName: 'Program 1',
      smallestInterval: 'M',
      trendCss: 'trend-up',
      scorecardDetailsList: [mockScorecardDetails],
    })

    // Verify that getPartnerParametersAsync was called with the correct parameters
    expect(
        mockCitCParametersService.getPartnerParametersAsync
    ).toHaveBeenCalledWith(mockOrganizationId)
    expect(
        mockCitCParametersService.getOrganizationParametersAsync
    ).not.toHaveBeenCalled()

    // Verify that getECMeasureResults was called with isPartner=true
    expect(mockMeasureResultsService.getECMeasureResults).toHaveBeenCalledWith(
        expect.objectContaining({
          organizationId: mockOrganizationId,
          periodType: ScorecardView.Monthly,
          submissionGroupId: mockSubmissionGroupIds,
          startDate: mockStartDate,
          endDate: mockEndDate,
          isPartner: true, // Should be true for Partner selection
          isSubmissionGroupLevel: true,
          measureIdentifier: '',
        })
    )
  })

  test('should skip IAPI measures when SmallestInterval is Y and no DenominatorQualifyingType', async () => {
    // Arrange
    const mockParameters: SimplifiedParameter[] = [
      { key: 'param1', value: 'value1' },
    ]

    // Use consistent casing (uppercase) for measureIdentifier
    const measureGuid = 'A1B2C3D4-E5F6-7890-ABCD-EF1234567890'

    const mockPaidMeasures = [
      {
        measureIdentifier: measureGuid,
        measureTitle: 'Measure 1',
        measureName: 'Measure 1',
        measureDescription: 'Description 1 ',
        measureFriendlyName: 'Friendly Name 1',
        subDomainName: 'Sub Domain 1',
        typeName: 'Type 1',
        domainName: 'Domain 1',
        cMSId: 'CMS1',
        subTypeName: 'Rate',
        applicationName: 'App 1',
        programName: 'Program 1',
        iNotationName: INotation.Higher,
        versionNumber: '1.0',
        subId: 'a',
      },
    ] as OrganizationPaidMeasure[]

    // Use the same casing (uppercase) for MedisolvMeasureId
    // This is an IAPI measure (SmallestInterval = 'Y' and no DenominatorQualifyingType)
    const mockAdmMeasures = [
      {
        Id: 1,
        MedisolvMeasureId: measureGuid,
        Name: 'Measure 1',
        NullRate: false,
        SmallestInterval: 'Y',
        DenominatorQualifyingType: null,
        NullDenominator: false,
        NullNumerator: false,
        NullIPP: false,
        NullDenExcl: false,
        NullNumExcl: false,
      },
    ] as unknown as ADMMeasure[]

    // Use the same casing (uppercase) for measureGUID
    const mockMeasureRates = [
      {
        id: 1,
        entityName: 'Entity 1',
        measureGUID: measureGuid,
        measureSubId: 'measureSubId',
        entityCode: 'entityCode',
        sourceContainerIdentifier: 'sourceContainerIdentifier',
        period: 'period',
        startDate: mockStartDate,
        endDate: mockEndDate,
        performance: 85,
        denominator: 100,
        numerator: 85,
        ipp: 120,
        denominatorExclusion: 20,
        numeratorExclusion: 0,
        hoverText: 'Hover text',
      },
    ] as ECMeasureResultSummary[]

    const mockDateRangeSpans = ['Jan-2023']
    const mockDateRange = {
      startDate: dayjs('2023-01-01'),
      endDate: dayjs('2023-01-31'),
    }

    // Mock the dependencies
    mockCitCParametersService.getOrganizationParametersAsync.mockResolvedValue(
        // @ts-ignore
        mockParameters
    )
    ;(getOrganizationPaidMeasuresQuery as jest.Mock).mockResolvedValue(
        // @ts-ignore
        mockPaidMeasures
    )
    ;(DateRange.getColumnIntervalsByCategory as jest.Mock).mockReturnValue(
        // @ts-ignore
        mockDateRangeSpans
    )
    ;(DateRange.getDateFromDateRangeSpan as jest.Mock).mockReturnValue(
        // @ts-ignore
        mockDateRange
    )
    ;(mockMeasureResultsService.getAllMeasures as jest.Mock).mockResolvedValue(
        // @ts-ignore
        mockAdmMeasures
    )
    ;(
        mockMeasureResultsService.getECMeasureResults as jest.Mock
    )
        // @ts-ignore
        .mockResolvedValue({
          ecMeasureResultSummaryList: mockMeasureRates,
        })

    // Act
    const result = await getECScorecardByMeasure(
        mockMeasureResultsService as any,
        ScorecardView.Monthly,
        mockOrganizationId,
        false, // isPartner
        mockStartDate,
        mockEndDate,
        mockSubmissionGroupIds,
        PrimaryMeasureTypeConstants.HospitalMeasures,
        mockAccessToken
    )

    // Assert
    expect(result).toHaveLength(0) // Should skip IAPI measures
  })

  test('should handle error gracefully', async () => {
    // Arrange
    mockCitCParametersService.getOrganizationParametersAsync.mockRejectedValue(
        // @ts-ignore
        new Error('Test error')
    )

    // Act & Assert
    await expect(
        getECScorecardByMeasure(
            mockMeasureResultsService as any,
            ScorecardView.Monthly,
            mockOrganizationId,
            false, // isPartner
            mockStartDate,
            mockEndDate,
            mockSubmissionGroupIds,
            PrimaryMeasureTypeConstants.HospitalMeasures,
            mockAccessToken
        )
    ).rejects.toThrow('Test error')
  })
})
