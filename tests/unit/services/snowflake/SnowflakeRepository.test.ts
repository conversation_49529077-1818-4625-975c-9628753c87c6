import { SnowflakeRepository } from '@/services/snowflake/SnowflakeRepository'
import * as SnowflakeHelper from '@/services/snowflake/SnowflakeHelper'
import { jest } from "@jest/globals"

// Mock Application Insights
jest.mock('@/lib/applicationInsights', () => ({
    __esModule: true,
    default: {
        trackTrace: jest.fn(),
        trackException: jest.fn(),
        trackEvent: jest.fn(),
        loadAppInsights: jest.fn(),
    }
}))

// Mock the execute function from SnowflakeHelper
jest.mock('@/services/snowflake/SnowflakeHelper', () => ({
    execute: jest.fn(),
    nextId: async () => 1,
    //@ts-ignore
    isPotentialSQLInjection: jest.requireActual('@/services/snowflake/SnowflakeHelper').isPotentialSQLInjection
}))

jest.mock('snowflake-sdk', () => ({
    "snowflake": jest.fn(),
}))

jest.mock('@/env', () => ({
    env: {},
}))

// Define a test entity type
interface TestEntity {
    Id: number
    name: string
    age?: number
    isActive: boolean
    createdAt?: Date
}

describe('SnowflakeRepository', () => {
    // Mock connection pool
    const mockConnectionPool = {
        // @ts-ignore
        use: jest.fn((callback) => callback({}))
    } as any

    // Test table and identity
    const tableName = 'test_table'
    const identity = 'Id'

    // Create repository instance
    let repository: SnowflakeRepository<TestEntity>

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks()

        // Create a new repository instance for each test
        repository = new SnowflakeRepository<TestEntity>(mockConnectionPool, tableName, identity)
    })

    describe('asUpdate', () => {
        it('should generate correct SQL and binds for a complete entity', () => {
            // Arrange
            const testDate = new Date('2025-01-01')
            const entity: TestEntity = {
                Id: 1,
                name: 'Test Entity',
                age: 30,
                isActive: true,
                createdAt: testDate
            }

            // Act
            const result = repository.asUpdate(entity)

            // Assert
            expect(result.sql).toBe('UPDATE test_table SET "name" = ?, "age" = ?, "isActive" = ?, "createdAt" = ? WHERE "Id" = ?')
            expect(result.binds).toEqual(['Test Entity', 30, true, testDate, 1])
        })

        it('should generate correct SQL and binds when some properties are undefined', () => {
            // Arrange
            const entity: TestEntity = {
                Id: 1,
                name: 'Test Entity',
                isActive: false
            }

            // Act
            const result = repository.asUpdate(entity)

            // Assert
            expect(result.sql).toBe('UPDATE test_table SET "name" = ?, "isActive" = ? WHERE "Id" = ?')
            expect(result.binds).toEqual(['Test Entity', false, 1])
        })

        it('should throw error when entity has no id', () => {
            // Arrange
            const entity = {
                name: 'Test Entity',
                isActive: true
            } as TestEntity

            // Act & Assert
            expect(() => repository.asUpdate(entity)).toThrow('Cannot update entity without id')
        })

        it('should throw error when there are no properties to update', () => {
            // Arrange
            const entity = { Id: 1 } as TestEntity

            // Act & Assert
            expect(() => repository.asUpdate(entity)).toThrow('No properties to update')
        })
    })

    describe('asInsert', () => {
        it('should generate correct SQL and binds for a complete entity', async () => {
            // Arrange
            const testDate = new Date('2025-01-01')
            const entity: TestEntity = {
                Id: 1,
                name: 'Test Entity',
                age: 30,
                isActive: true,
                createdAt: testDate
            }

            // Act
            const result = await repository.asInsert(entity)

            // Assert
            expect(result.sql).toBe('INSERT INTO test_table ("Id", "name", "age", "isActive", "createdAt") VALUES (?, ?, ?, ?, ?)')
            expect(result.binds).toEqual([1, 'Test Entity', 30, true, testDate])
        })

        it('should generate correct SQL and binds when some properties are undefined', async () => {
            // Arrange
            const entity: TestEntity = {
                Id: 1,
                name: 'Test Entity',
                isActive: false
            }

            // Act
            const result = await repository.asInsert(entity)

            // Assert
            expect(result.sql).toBe('INSERT INTO test_table ("Id", "name", "isActive") VALUES (?, ?, ?)')
            expect(result.binds).toEqual([1, 'Test Entity', false])
        })

        it('should throw error when there are no properties to insert', () => {
            // Arrange
            const entity = {} as TestEntity

            // Act & Assert
            expect(async () => await repository.asInsert(entity)).rejects.toThrow('No properties to insert')
        })
    })

    describe('save', () => {
        it('should call execute with correct SQL and binds for update', async () => {
            // Arrange
            const entity: TestEntity = {
                Id: 1,
                name: 'Test Entity',
                isActive: true
            }

            const mockExecuteSpy = jest.spyOn(SnowflakeHelper, 'execute').mockResolvedValue([])

            // Act
            await repository.save(entity)

            // Assert
            expect(mockConnectionPool.use).toHaveBeenCalled()
            expect(mockExecuteSpy).toHaveBeenCalledWith(
                expect.anything(),
                'UPDATE test_table SET "name" = ?, "isActive" = ? WHERE "Id" = ?',
                ['Test Entity', true, 1]
            )
        })

        it('should call execute with correct SQL and binds for insert', async () => {
            // Arrange
            const entity: TestEntity = {
                Id: 0, // Using 0 as a non-truthy value to trigger insert
                name: 'New Entity',
                isActive: true
            }

            const mockExecuteSpy = jest.spyOn(SnowflakeHelper, 'execute').mockResolvedValue([])

            // Act
            await repository.save(entity)

            // Assert
            expect(mockConnectionPool.use).toHaveBeenCalled()
            expect(mockExecuteSpy).toHaveBeenCalledWith(
                expect.anything(),
                'INSERT INTO test_table ("Id", "name", "isActive") VALUES (?, ?, ?)',
                [1, 'New Entity', true]
            )
            expect(entity.Id).toEqual(1)
        })
    })

    describe('findById', () => {
        it('should call execute with correct SQL and binds', async () => {
            // Arrange
            const id = 1
            const mockExecuteSpy = jest.spyOn(SnowflakeHelper, 'execute').mockResolvedValue([
                { Id: 1, name: 'Test Entity', isActive: true }
            ])

            // Act
            const result = await repository.findById(id)

            // Assert
            expect(mockConnectionPool.use).toHaveBeenCalled()
            expect(mockExecuteSpy).toHaveBeenCalledWith(
                expect.anything(),
                'SELECT TOP 1 * FROM test_table WHERE "Id" = ?',
                [1]
            )
            expect(result).toEqual({ Id: 1, name: 'Test Entity', isActive: true })
        })

        it('should return undefined when no results are found', async () => {
            // Arrange
            const id = 999
            jest.spyOn(SnowflakeHelper, 'execute').mockResolvedValue([])

            // Act
            const result = await repository.findById(id)

            // Assert
            expect(result).toBeUndefined()
        })
    })

    describe('findAll', () => {
        it('should call execute with correct SQL', async () => {
            // Arrange
            const mockEntities = [
                { Id: 1, name: 'Entity 1', isActive: true },
                { Id: 2, name: 'Entity 2', isActive: false }
            ]
            const mockExecuteSpy = jest.spyOn(SnowflakeHelper, 'execute').mockResolvedValue(mockEntities)

            // Act
            const results = await repository.findAll()

            // Assert
            expect(mockConnectionPool.use).toHaveBeenCalled()
            expect(mockExecuteSpy).toHaveBeenCalledWith(
                expect.anything(),
                'SELECT * FROM test_table',
                []
            )
            expect(results).toEqual(mockEntities)
        })
    })

    describe('asDelete', () => {
        it('should generate correct SQL and binds when given an entity object', () => {
            // Arrange
            const entity = {
                Id: 1,
                name: 'Test Entity',
                isActive: true
            }

            // Act
            const result = repository.asDelete(entity)

            // Assert
            expect(result.sql).toBe('DELETE FROM test_table WHERE "Id" = ?')
            expect(result.binds).toEqual([1])
        })

        it('should generate correct SQL and binds when given just an id', () => {
            // Arrange
            const id = 42

            // Act
            const result = repository.asDelete(id)

            // Assert
            expect(result.sql).toBe('DELETE FROM test_table WHERE "Id" = ?')
            expect(result.binds).toEqual([42])
        })

        it('should throw error when entity has no id', () => {
            // Arrange
            const entity = {
                name: 'Test Entity',
                isActive: true
            }

            // Act & Assert
            expect(() => repository.asDelete(entity)).toThrow('Cannot delete entity without id')
        })

        it('should throw error when id is null or undefined', () => {
            // Act & Assert
            expect(() => repository.asDelete(null)).toThrow('Cannot delete entity without id')
            expect(() => repository.asDelete(undefined)).toThrow('Cannot delete entity without id')
        })
    })

    describe('delete', () => {
        it('should call execute with correct SQL and binds when deleting an entity object', async () => {
            // Arrange
            const entity = {
                Id: 1,
                name: 'Test Entity',
                isActive: true
            }

            const mockExecuteSpy = jest.spyOn(SnowflakeHelper, 'execute').mockResolvedValue([])

            // Act
            const result = await repository.delete(entity)

            // Assert
            expect(mockConnectionPool.use).toHaveBeenCalled()
            expect(mockExecuteSpy).toHaveBeenCalledWith(
                expect.anything(),
                'DELETE FROM test_table WHERE "Id" = ?',
                [1]
            )
            expect(result).toBe(1) // Should return 1 to indicate success
        })

        it('should call execute with correct SQL and binds when deleting by id', async () => {
            // Arrange
            const id = 42

            const mockExecuteSpy = jest.spyOn(SnowflakeHelper, 'execute').mockResolvedValue([])

            // Act
            const result = await repository.delete(id)

            // Assert
            expect(mockConnectionPool.use).toHaveBeenCalled()
            expect(mockExecuteSpy).toHaveBeenCalledWith(
                expect.anything(),
                'DELETE FROM test_table WHERE "Id" = ?',
                [42]
            )
            expect(result).toBe(1) // Should return 1 to indicate success
        })
    })
})
