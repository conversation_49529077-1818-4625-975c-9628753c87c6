import React from 'react'
import {
  render,
  screen,
  fireEvent,
  waitFor,
  within,
} from '@testing-library/react'
import { jest } from "@jest/globals";

const mockPostMessage = jest.fn()

function channelMock() {
  channelMock.prototype.postMessage = mockPostMessage
}

// @ts-ignore
self.BroadcastChannel = channelMock

// Mock env
jest.mock('@/env', () => ({
  env: {
    NEXT_PUBLIC_APP_VERSION: '01/01/2025',
    // Add any other environment variables you need to mock
  },
}))

// Mock useSession
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: {
      user: {
        name: 'Test User',
        email: '<EMAIL>',
        // Add any other user properties you need
      },
      expires: '2023-01-01',
    },
    status: 'authenticated',
  })),
}))

jest.mock('next-auth', () => ({
  __esModule: true,
  default: () => ({
    auth: jest.fn(),
    handlers: {
      GET: jest.fn(),
      POST: jest.fn(),
    },
    signIn: jest.fn(),
    signOut: jest.fn(),
    unstable_update: jest.fn(),
  }),
  useSession: jest.fn(() => ({
    data: {
      user: {
        name: 'Test User',
        email: '<EMAIL>',
      },
      expires: '2023-01-01',
    },
    status: 'authenticated',
  })),
  getServerSession: jest.fn(() => null),
}))

jest.mock('@/lib/applicationInsights', () => ({
  __esModule: true,
  default: {
    trackEvent: jest.fn(),
    trackPageView: jest.fn(),
    flush: jest.fn(),
    // Add any other methods you're using from appInsights
  },
}))

import AuditLogGrid from '@/components/auditLog'
import { api } from '@/trpc/react'

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} alt={props.alt || ''} />
  },
}))

// Mock the Exporter component
jest.mock('@/components/ui/Exporter', () => {
  return jest.fn(({ exportDataset, buttonText }) => (
    <div data-testid="mock-exporter">
      <button onClick={() => {}}>{buttonText}</button>
      <div data-testid="export-dataset">{JSON.stringify(exportDataset)}</div>
    </div>
  ))
})

// Mock the api
jest.mock('@/trpc/react', () => ({
  api: {
    admin: {
      getAuditLogs: {
        useQuery: jest.fn(),
      },
    },
  },
}))

// Mock the DateRangeFilter component
jest.mock('@/components/ui/DateRangeFilter', () => {
  return function MockDateRangeFilter({
    onDateRangeChange,
  }: {
    onDateRangeChange: (start: Date | null, end: Date | null) => void
  }) {
    return (
      <div data-testid="date-range-filter">
        <button onClick={() => onDateRangeChange(new Date(), new Date())}>
          Set Dates
        </button>
      </div>
    )
  }
})

const mockAuditLogs = [
  {
    applicationId: 'app-1',
    auditLogDbId: 1,
    currentRoles: 'admin',
    logTimestamp: 1234567890,
    logUTCTime: '2023-01-01T00:00:00Z',
    miscData: {},
    organizationId: 'org-1',
    pageTitle: 'Dashboard',
    productId: 'prod-1',
    requestIpAddress: '127.0.0.1',
    url: 'http://example.com',
    userAgent: 'Mozilla',
    userEmail: '<EMAIL>',
    userId: 'user-1',
    userName: 'John Doe',
  },
]

describe('AuditLogGrid Component', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks()

    // Setup default mock implementation
    ;(api.admin.getAuditLogs.useQuery as jest.Mock).mockReturnValue({
      data: mockAuditLogs,
      refetch: jest.fn(),
      isLoading: false,
    })
  })

  it.skip('renders the component with basic elements', () => {
    render(<AuditLogGrid />)

    expect(screen.getByText('Audit Logs')).toBeInTheDocument()
    expect(screen.getByText('Export To Excel')).toBeInTheDocument()
    expect(screen.getByTestId('date-range-filter')).toBeInTheDocument()
  })

  it('displays the correct table headers', () => {
    render(<AuditLogGrid />)

    const expectedHeaders = [
      'User Name',
      'User Email',
      'Organization',
      'URL',
      'Audit Date Time',
    ]
    expectedHeaders.forEach((header) => {
      expect(screen.getByText(header)).toBeInTheDocument()
    })
  })

  it('handles search input correctly', async () => {
    render(<AuditLogGrid />)

    // Find all search inputs
    const searchInputs = screen.getAllByPlaceholderText('Search')
    const emailSearchInput = searchInputs[1] // User Email is the second column

    // Type in search
    fireEvent.change(emailSearchInput as HTMLInputElement, {
      target: { value: '<EMAIL>' },
    })

    // Verify the input value
    expect(emailSearchInput).toHaveValue('<EMAIL>')
  })

  it.skip('handles date range changes', async () => {
    const { getByTestId } = render(<AuditLogGrid />)

    // Click the date range filter button
    const dateRangeFilter = getByTestId('date-range-filter')
    const setDatesButton = within(dateRangeFilter).getByText('Set Dates')

    fireEvent.click(setDatesButton)

    // Verify that the api query was called with updated dates
    await waitFor(() => {
      expect(api.admin.getAuditLogs.useQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          startTimeMMDDYYYY: expect.any(String),
          endTimeMMDDYYYY: expect.any(String),
        }),
        expect.any(Object)
      )
    })
  })

  it.skip('handles export to excel functionality', () => {
    render(<AuditLogGrid />)

    // Verify that the Exporter component is rendered with correct props
    const exporter = screen.getByTestId('mock-exporter')
    expect(exporter).toBeInTheDocument()

    const exportDataset = JSON.parse(
      screen.getByTestId('export-dataset').textContent || '{}'
    )

    // Verify the export dataset contains the expected data
    expect(exportDataset).toEqual({
      headers: expect.arrayContaining([
        'User Name',
        'User Email',
        'Organization',
        'URL',
        'Audit Date Time',
      ]),
      dataset: [
        {
          'User Name': 'John Doe',
          'User Email': '<EMAIL>',
          Organization: '', // This field is empty in the actual output
          URL: 'http://example.com',
          'Audit Date Time': expect.any(String),
        },
      ],
    })
  })

  it('displays "No matching results found" when no data is available', () => {
    ;(api.admin.getAuditLogs.useQuery as jest.Mock).mockReturnValue({
      data: [],
      refetch: jest.fn(),
      isLoading: false,
    })

    render(<AuditLogGrid />)

    expect(screen.getByText('No matching results found.')).toBeInTheDocument()
  })

  it('handles pagination correctly', () => {
    render(<AuditLogGrid />)

    // Check if pagination controls are present
    expect(screen.getByText('Items per page')).toBeInTheDocument()

    // Check if page size selector works
    const pageSizeSelect = screen.getByRole('combobox')
    fireEvent.change(pageSizeSelect, { target: { value: '25' } })
    expect(pageSizeSelect).toHaveValue('25')
  })

  it('updates filtered data when search inputs change', async () => {
    render(<AuditLogGrid />)

    const searchInputs = screen.getAllByPlaceholderText('Search')
    const emailSearchInput = searchInputs[1]

    // Type a search term that doesn't match any records
    fireEvent.change(emailSearchInput as HTMLInputElement, {
      target: { value: '<EMAIL>' },
    })

    // Verify that "No matching results found" is displayed
    expect(screen.getByText('No matching results found.')).toBeInTheDocument()
  })
})
