import React from 'react'
import { render, waitFor } from '@testing-library/react'
import { AuditLogger } from '@/components/auditLog/Logging'
import { api } from '@/trpc/react'
import { useSession } from 'next-auth/react'
import { usePathname, useSearchParams } from 'next/navigation'
import { jest } from "@jest/globals";

const originalConsoleDebug = console.debug
console.debug = jest.fn()

// Mock the env module
jest.mock('@/env', () => ({
  __esModule: true,
  env: {
    NEXT_PUBLIC_AUDIT_LOG_SERVER_URL: 'http://test-audit-log-server.com',
    PRODUCT_ID: '13142b2a-0c80-43e8-914c-ed318c2c1236',
  },
}))

// Mock the next-auth/react module
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
  useSearchParams: jest.fn(),
}))

// Mock the trpc api
jest.mock('@/trpc/react', () => ({
  api: {
    admin: {
      postAuditLogs: {
        useMutation: jest.fn(),
      },
    },
  },
}))

afterAll(() => {
  console.debug = originalConsoleDebug
})

describe('AuditLogger Component', () => {
  // Setup default mocks
  const mockMutate = jest.fn().mockImplementation((data, options) => {
    // @ts-ignore
    if (options && options.onSuccess) {
      // @ts-ignore
      options.onSuccess()
    }
  })

  const mockSession = {
    data: {
      user: { email: '<EMAIL>' },
      uid: 'test-user-id',
      role: ['User'],
      accessToken: 'test-access-token',
    },
    status: 'authenticated',
  }

  const mockPathname = '/dashboard'
  const mockSearchParams = { toString: () => 'param=value' }

  beforeEach(() => {
    jest.clearAllMocks()

    // Setup session mock
    ;(useSession as jest.Mock).mockReturnValue(mockSession)

    // Setup navigation mocks
    ;(usePathname as jest.Mock).mockReturnValue(mockPathname)
    ;(useSearchParams as jest.Mock).mockReturnValue(mockSearchParams)

    // Setup API mock with success and error callbacks
    ;(api.admin.postAuditLogs.useMutation as jest.Mock).mockReturnValue({
      mutate: mockMutate,
      isLoading: false,
      isError: false,
      isSuccess: true,
    })

    // Mock document properties
    Object.defineProperty(document, 'title', {
      value: 'Test Dashboard',
      writable: true,
    })

    Object.defineProperty(document, 'referrer', {
      value: 'http://example.com/previous',
      writable: true,
    })

    // Mock navigator
    Object.defineProperty(window, 'navigator', {
      value: {
        userAgent: 'jest-test-agent',
      },
      writable: true,
    })
  })

  it('renders children without crashing', () => {
    const { getByText } = render(
      <AuditLogger>
        <div>Test Content</div>
      </AuditLogger>
    )

    expect(getByText('Test Content')).toBeInTheDocument()
  })

  it('logs page navigation when path changes and session exists', async () => {
    render(
      <AuditLogger>
        <div>Test Content</div>
      </AuditLogger>
    )

    // Wait for useEffect to run
    await waitFor(() => {
      expect(mockMutate).toHaveBeenCalledTimes(1)
      expect(mockMutate).toHaveBeenCalledWith(
        expect.objectContaining({
          url: '/dashboard?param=value',
          userAgent: 'jest-test-agent',
          pageTitle: 'Test Dashboard',
          actionType: 'PAGE_NAVIGATION',
        }),
        expect.any(Object)
      )
    })
  })

  it('does not log when session is not available', async () => {
    // Mock session as null
    ;(useSession as jest.Mock).mockReturnValue({ data: null })

    render(
      <AuditLogger>
        <div>Test Content</div>
      </AuditLogger>
    )

    // Wait to ensure useEffect has run
    await waitFor(() => {
      expect(mockMutate).not.toHaveBeenCalled()
    })
  })

  it('does not log when path has not changed', async () => {
    // First render to set initial path
    const { rerender } = render(
      <AuditLogger>
        <div>Test Content</div>
      </AuditLogger>
    )

    // Clear the mock after initial render
    mockMutate.mockClear()

    // Rerender with same path
    rerender(
      <AuditLogger>
        <div>Updated Content</div>
      </AuditLogger>
    )

    // Wait to ensure useEffect has run
    await waitFor(() => {
      expect(mockMutate).not.toHaveBeenCalled()
    })
  })

  it('handles errors during logging gracefully', async () => {
    // Mock console.error to test error handling
    const consoleErrorSpy = jest
      .spyOn(console, 'error')
      .mockImplementation(() => {})

    // Make the mutation throw an error
    mockMutate.mockImplementation(() => {
      throw new Error('Test error')
    })

    render(
      <AuditLogger>
        <div>Test Content</div>
      </AuditLogger>
    )

    // Wait for useEffect to run
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error in audit logging:',
        expect.any(Error)
      )
    })

    // Restore console.error
    consoleErrorSpy.mockRestore()
  })
})
