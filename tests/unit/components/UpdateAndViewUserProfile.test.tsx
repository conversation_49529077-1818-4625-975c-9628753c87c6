import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { AccountInfo } from '@/components/account/index'
import { api } from '@/trpc/react'

jest.mock('next/image', () => {
  const MockImage = () => null
  MockImage.propTypes = { source: () => null }
  return MockImage
})

// Mock the TRPC API calls
jest.mock('@/trpc/react', () => ({
  api: {
    users: {
      getUserInfo: {
        useQuery: jest.fn(() => ({
          data: {
            displayName: 'Test User',
            emailAddress: '<EMAIL>',
            cellPhone: '************',
            workPhone: '************',
            homePhone: '************',
            profileImage: null,
          },
          isPending: false,
        })),
      },
      updateUserInfo: {
        useMutation: jest.fn(() => ({
          mutateAsync: jest.fn(),
          isPending: false,
        })),
      },
    },
  },
}))

describe('AccountInfo Component', () => {
  it('renders without crashing', () => {
    render(<AccountInfo />)
    expect(screen.getByText(/SCREEN NAME*/i)).toBeInTheDocument()
  })

  it('displays user profile information', () => {
    render(<AccountInfo />)
    expect(screen.getByDisplayValue('Test User')).toBeInTheDocument()
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByDisplayValue('************')).toBeInTheDocument()
  })

  it('calls updateUserInfo mutation on save click', async () => {
    const mutateAsyncMock = jest.fn()
    jest.spyOn(api.users.updateUserInfo, 'useMutation').mockReturnValue({
      mutateAsync: mutateAsyncMock,
      isPending: false,
    } as any)

    render(<AccountInfo />)
    const saveButton = screen.getByText(/SAVE/)
    fireEvent.click(saveButton)
    expect(mutateAsyncMock).toHaveBeenCalled()
  })
})
