import type { ContactListType } from '@/types/contacts'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import React from 'react'

// Setup mocks before importing the components
jest.mock('@/stores/viewStore', () => ({
  useViewStore: jest.fn().mockReturnValue({
    setTableState: jest.fn(),
    tableState: {},
    visibleColumnsForView: [],
    currentView: null
  })
}))

jest.mock('@/stores/actions', () => ({
  useActionsStore: jest.fn().mockReturnValue({
    selectedOptions: [],
    setSelectedOptions: jest.fn(),
    setOptions: jest.fn()
  })
}))

jest.mock('@/trpc/react', () => ({
  api: {
    contacts: {
      getOrgContactList: {
        useSuspenseQuery: jest.fn()
      }
    }
  }
}))

jest.mock('tanstack-table-export-to-csv')

// Now import the component after all mocks are setup
import { ContactList } from '@/components/contactList'
import { useViewStore } from '@/stores/viewStore'
import { useActionsStore } from '@/stores/actions'
import { api } from '@/trpc/react'
import exportToCsv from 'tanstack-table-export-to-csv'

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: { src: string | { src: string }; height?: number; width?: number; 'data-testid'?: string }) => {
    // Handle both string and object src props
    const srcValue = typeof props.src === 'string' ? props.src : props.src.src
    // Ensure the src always starts with a forward slash
    const src = srcValue.startsWith('/') ? srcValue : `/${srcValue}`
    return <img {...props} src={src} data-testid={props['data-testid']} />
  }
}))

// Mock SVG imports
jest.mock('../../../public/images/checked-radio.svg', () => ({
  src: '/images/checked-radio.svg',
  height: 24,
  width: 24
}))

jest.mock('../../../public/images/download.svg', () => ({
  src: '/images/download.svg',
  height: 24,
  width: 24
}))

// Mock data
const mockContacts: ContactListType[] = [
  {
    organizationId: '1',
    organizationName: 'Org One',
    firstName: 'John',
    lastName: 'Doe',
    fullName: 'John Doe',
    contactEmail: '<EMAIL>',
    active: 'true'
  },
  {
    organizationId: '2',
    organizationName: 'Org Two',
    firstName: 'Jane',
    lastName: 'Smith',
    fullName: 'Jane Smith',
    contactEmail: '<EMAIL>',
    active: 'false'
  }
]

describe('ContactList', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks()

    // Setup default mock implementations
    ;((useActionsStore as unknown) as jest.Mock).mockReturnValue({
      selectedOptions: [],
      setSelectedOptions: jest.fn(),
      setOptions: jest.fn()
    })

    ;((useViewStore as unknown) as jest.Mock).mockReturnValue({
      setTableState: jest.fn(),
      tableState: {},
      visibleColumnsForView: [],
      currentView: null
    })

    ;(api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
      mockContacts,
      { isLoading: false }
    ])
  })

  describe('Initial Rendering', () => {
    test('renders without crashing', () => {
      render(<ContactList />)
      expect(screen.getByRole('table')).toBeInTheDocument()
    })

    test('displays all column headers', () => {
      render(<ContactList />)
      expect(screen.getByText('ORGANIZATION')).toBeInTheDocument()
      expect(screen.getByText('NAME')).toBeInTheDocument()
      expect(screen.getByText('EMAIL')).toBeInTheDocument()
      expect(screen.getByText('ACTIVE')).toBeInTheDocument()
    })

    test('displays loading state when data is being fetched', async () => {
      // Mock the stores with initial table state
      const initialTableState = {
        pagination: { pageIndex: 0, pageSize: 10 },
        sorting: [],
        columnFilters: []
      };

      (useViewStore as unknown as jest.Mock).mockReturnValue({
        tableState: initialTableState,  // Provide initial state
        setTableState: jest.fn(),
        visibleColumnsForView: [],
        currentView: null
      });

      (useActionsStore as unknown as jest.Mock).mockReturnValue({
        selectedOptions: [],
        setSelectedOptions: jest.fn(),
        setOptions: jest.fn()
      });

      // Mock the API to indicate loading state
      (api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
        [], // Empty array while loading
        { 
          isPending: true,
          isRefetching: false,
          status: 'loading'
        }
      ]);

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ContactList />
        </React.Suspense>
      );

      // Wait for the loading indicator
      await waitFor(() => {
        expect(screen.getByText('Loading...')).toBeInTheDocument();
      });
    })

    test('displays no data message when contacts array is empty', async () => {
      // Mock the stores
      (useViewStore as unknown as jest.Mock).mockReturnValue({
        tableState: {
          pagination: { pageIndex: 0, pageSize: 10 },
          sorting: [],
          columnFilters: []
        },
        setTableState: jest.fn(),
        visibleColumnsForView: [],
        currentView: null
      });

      (useActionsStore as unknown as jest.Mock).mockReturnValue({
        selectedOptions: [],
        setSelectedOptions: jest.fn(),
        setOptions: jest.fn()
      });

      // Mock the API to return an empty array
      (api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
        [], // Empty array for contacts
        { 
          isPending: false,
          isRefetching: false,
          status: 'success'
        }
      ]);

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ContactList />
        </React.Suspense>
      );

      // Wait for the component to render and show the no data message
      await waitFor(() => {
        const noDataElement = screen.getByText('No data available for Contacts');
        expect(noDataElement).toBeInTheDocument();
      });
    })
  })

  describe('Table Data Display', () => {
    test('displays contact data correctly', () => {
      render(<ContactList />)
      expect(screen.getByText('Org One')).toBeInTheDocument()
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    })

    test('displays correct number of rows', () => {
      render(<ContactList />)
      const rows = screen.getAllByRole('row')
      // Add 2 to mockContacts.length to account for header row and footer row
      expect(rows).toHaveLength(mockContacts.length + 2)
      
      // Alternatively, we can test just the data rows by excluding header and footer
      const dataRows = rows.slice(1, -1) // Exclude first (header) and last (footer) rows
      expect(dataRows).toHaveLength(mockContacts.length)
    })
  })

  // Removing the entire 'Filtering' describe block

  describe('Sorting', () => {
    test('sorts organization column in ascending order', async () => {
      render(<ContactList />)
      const orgHeader = screen.getByText('ORGANIZATION')
      fireEvent.click(orgHeader)

      const rows = screen.getAllByRole('row')
      expect(rows[1]).toHaveTextContent('Org One')
      expect(rows[2]).toHaveTextContent('Org Two')
    })

    test('sorts organization column in descending order', async () => {
      render(<ContactList />)
      const orgHeader = screen.getByText('ORGANIZATION')
      // Click twice for descending order
      fireEvent.click(orgHeader)
      fireEvent.click(orgHeader)

      const rows = screen.getAllByRole('row')
      expect(rows[1]).toHaveTextContent('Org Two')
      expect(rows[2]).toHaveTextContent('Org One')
    })
  })

  describe('Pagination', () => {
    beforeEach(() => {
      // Create mock data for pagination testing
      const manyContacts = Array(25).fill(null).map((_, i) => ({
        organizationId: `org${i}`,
        organizationName: `Organization ${i}`,
        fullName: `Contact ${i}`,
        contactEmail: `contact${i}@example.com`,
        active: true
      }));

      // Mock initial table state
      const initialTableState = {
        pagination: {
          pageIndex: 0,
          pageSize: 10
        },
        sorting: [],
        columnFilters: []
      };

      // Mock the view store
      (useViewStore as unknown as jest.Mock).mockReturnValue({
        setTableState: jest.fn(),
        tableState: initialTableState,
        visibleColumnsForView: [],
        currentView: null
      });

      // Mock the actions store
      (useActionsStore as unknown as jest.Mock).mockReturnValue({
        selectedOptions: [],
        setSelectedOptions: jest.fn(),
        setOptions: jest.fn()
      });
      
      (api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
        manyContacts,
        { isLoading: false }
      ]);
    });

    test('changes page size when dropdown is changed', async () => {
      render(<ContactList />);
      
      // Initial state - should show 10 items by default
      let allRows = screen.getAllByRole('row');
      let dataRows = allRows.slice(1, -1); // Exclude header and footer rows
      expect(dataRows).toHaveLength(10);

      // Find and change the page size select
      const pageSizeSelect = screen.getByRole('combobox');
      
      // Change to 25 items per page
      await act(async () => {
        fireEvent.change(pageSizeSelect, { target: { value: '25' } });
      });

      // Wait for and verify the new number of rows
      await waitFor(() => {
        const newAllRows = screen.getAllByRole('row');
        const newDataRows = newAllRows.slice(1, -1); // Exclude header and footer rows
        expect(newDataRows).toHaveLength(25); // Should show all 25 items
      });

      // Verify some content to ensure data is displayed correctly
      expect(screen.getByText('Organization 0')).toBeInTheDocument();
      expect(screen.getByText('Organization 24')).toBeInTheDocument();
    });

    test('shows correct initial page size', () => {
      // Create mock data
      const mockContacts = Array(15).fill(null).map((_, i) => ({
        organizationId: `org${i}`,
        organizationName: `Org ${i}`,
        firstName: `First${i}`,
        lastName: `Last${i}`,
        fullName: `Contact ${i}`,
        contactEmail: `contact${i}@example.com`,
        active: 'true'
      }));

      // Mock the API response
      (api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
        mockContacts,
        { isLoading: false }
      ]);

      render(<ContactList />);
      
      // Get the select element and verify its value
      const pageSizeSelect = screen.getByRole('combobox');
      expect(pageSizeSelect).toHaveValue('10');
      
      // Get all rows
      const allRows = screen.getAllByRole('row');
      
      // Should have 12 rows total (10 data rows + 1 header row + 1 footer row)
      expect(allRows).toHaveLength(12);
      
      // Verify data rows only (excluding header and footer)
      const dataRows = allRows.slice(1, -1); // Skip header and footer rows
      expect(dataRows).toHaveLength(10);
      
      // Verify first and last visible items
      expect(screen.getByText('Org 0')).toBeInTheDocument();
      expect(screen.getByText('Org 9')).toBeInTheDocument();
      expect(screen.queryByText('Org 10')).not.toBeInTheDocument();
    });

    test('shows correct number of rows per page', () => {
      render(<ContactList />);
      
      // Get the select element and verify its value
      const pageSizeSelect = screen.getByRole('combobox');
      expect(pageSizeSelect).toHaveValue('10');
      
      // Get all rows and filter to only include data rows
      // Exclude header (first row) and footer (last row) using slice
      const allRows = screen.getAllByRole('row');
      const dataRows = allRows.slice(1, -1);
      expect(dataRows).toHaveLength(10);
    });

    test('shows correct number of rows per page', () => {
      // Create mock data with more than 10 items to test pagination
      const manyContacts = Array(15).fill(null).map((_, i) => ({
        organizationId: `org${i}`,
        organizationName: `Org ${i}`,
        firstName: `First${i}`,
        lastName: `Last${i}`,
        fullName: `Contact ${i}`,
        contactEmail: `contact${i}@example.com`,
        active: 'true'
      }));

      // Mock the API response
      (api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
        manyContacts,
        { isLoading: false }
      ]);

      render(<ContactList />);
      
      // Get all rows including header
      const allRows = screen.getAllByRole('row');
      
      // Should have 12 rows total (10 data rows + 1 header row + 1 footer row)
      expect(allRows).toHaveLength(12);
      
      // Verify data rows only (excluding header and footer)
      const dataRows = allRows.slice(1, -1);
      expect(dataRows).toHaveLength(10);
      
      // Verify first and last visible items
      expect(screen.getByText('Org 0')).toBeInTheDocument();
      expect(screen.getByText('Org 9')).toBeInTheDocument();
      // Org 10 should not be visible due to pagination
      expect(screen.queryByText('Org 10')).not.toBeInTheDocument();
    });
  })

  describe('CSV Export', () => {
    beforeEach(() => {
      // Mock the exportToCsv function
      (exportToCsv as jest.Mock).mockImplementation(() => {});

      // Create actual objects for the SVG imports
      const downloadSvg = {
        src: '/images/download.svg',
        height: 18,
        width: 18
      };

      const checkedSvg = {
        src: '/images/checked-radio.svg',
        height: 18,
        width: 18
      };

      // Mock the SVG imports
      jest.mock('../../../public/images/download.svg', () => downloadSvg, { virtual: true });
      jest.mock('../../../public/images/checked-radio.svg', () => checkedSvg, { virtual: true });
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    test('exports data to CSV when export button is clicked', async () => {
      // Mock data
      const mockContacts = [
        { 
          organizationId: '1', 
          organizationName: 'Org One', 
          fullName: 'John Doe', 
          contactEmail: '<EMAIL>', 
          active: true 
        }
      ];

      // Mock API response
      (api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
        mockContacts,
        { isLoading: false }
      ]);

      render(<ContactList />)
      
      const exportButton = screen.getByRole('button', { name: /EXPORT TO EXCEL/i })
      await act(async () => {
        fireEvent.click(exportButton)
      })

      expect(exportToCsv).toHaveBeenCalled()
    });
  })

  describe('Error Handling', () => {
    test('handles API errors gracefully', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation()
      ;(api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
        [], // Return empty array as data
        { 
          isPending: false,
          isRefetching: false,
          status: 'error',
          error: new Error('API Error')
        }
      ])

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ContactList />
        </React.Suspense>
      )
      
      await waitFor(() => {
        expect(screen.getByText('No data available for Contacts')).toBeInTheDocument()
      })
      consoleErrorSpy.mockRestore()
    })

    test('handles empty API response', async () => {
      ;(api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
        [], // Empty array
        { 
          isPending: false,
          isRefetching: false,
          status: 'success'
        }
      ])

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ContactList />
        </React.Suspense>
      )
      
      await waitFor(() => {
        expect(screen.getByText('No data available for Contacts')).toBeInTheDocument()
      })
    })
  })

  describe('Store Integration', () => {
    test('updates view store when pagination changes', async () => {
      // Create mock function for setTableState
      const setTableStateMock = jest.fn();
      
      // Mock initial table state
      const initialTableState = {
        pagination: {
          pageIndex: 0,
          pageSize: 10
        },
        sorting: [],
        columnFilters: []
      };
      
      // Mock the view store
      (useViewStore as unknown as jest.Mock).mockReturnValue({
        setTableState: setTableStateMock,
        tableState: initialTableState,
        visibleColumnsForView: [],
        currentView: null
      });

      // Mock the actions store
      (useActionsStore as unknown as jest.Mock).mockReturnValue({
        selectedOptions: [],
        setSelectedOptions: jest.fn(),
        setOptions: jest.fn()
      });

      // Create mock data
      const mockContacts = Array(30).fill(null).map((_, i) => ({
        organizationId: `org${i}`,
        organizationName: `Org ${i}`,
        fullName: `Contact ${i}`,
        contactEmail: `contact${i}@example.com`,
        active: true
      }));

      // Mock the API response
      (api.contacts.getOrgContactList.useSuspenseQuery as jest.Mock).mockReturnValue([
        mockContacts,
        { isLoading: false }
      ]);

      render(<ContactList />);

      // Wait for initial render and table to be present
      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument();
      });

      // Clear previous mock calls
      setTableStateMock.mockClear();

      // Find and change the page size select
      const pageSizeSelect = screen.getByRole('combobox');
      
      // Change page size to 25
      await act(async () => {
        fireEvent.change(pageSizeSelect, { target: { value: '25' } });
      });

      // Wait for and verify the state update
      await waitFor(() => {
        const lastCall = setTableStateMock.mock.calls[setTableStateMock.mock.calls.length - 1];
        expect(lastCall[0]).toEqual({
          pagination: {
            pageIndex: 0,
            pageSize: 25
          },
          sorting: [],
          columnFilters: []
        });
      });

      // Verify we can also change to a different page size
      await act(async () => {
        fireEvent.change(pageSizeSelect, { target: { value: '10' } });
      });

      // Verify the final state
      await waitFor(() => {
        const lastCall = setTableStateMock.mock.calls[setTableStateMock.mock.calls.length - 1];
        expect(lastCall[0]).toEqual({
          pagination: {
            pageIndex: 0,
            pageSize: 10
          },
          sorting: [],
          columnFilters: []
        });
      });
    });
  })
})






















