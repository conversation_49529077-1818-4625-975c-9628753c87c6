import React from 'react'
import { render } from '@testing-library/react'
import { IntervalType } from '@/types/intervalType'
import { SisenseMeasureSummary } from '@/services/sisense/SisenseMeasureSummaryRepository'
import ACOPerformanceAnalysisReport from '@/components/reports/aco/ACOPerformanceAnalysisReport'

// Mock the trpc API
const mockUseQuery = jest.fn()
jest.mock('@/trpc/react', () => ({
  api: {
    report: {
      getACOMeasurePerformanceByUniverse: {
        useQuery: (...args: any[]) => mockUseQuery(...args),
      },
    },
  },
}))

// Mock the ACOPerformanceAnalysisTable component
jest.mock('@/components/reports/aco/ACOPerformanceAnalysisTable', () => {
  return function MockACOPerformanceAnalysisTable(props: any) {
    return <div data-testid="aco-table" data-props={JSON.stringify(props)} />
  }
})

describe('ACOPerformanceAnalysisReport', () => {
  // Default props for the component
  const defaultProps = {
    intervalType: 'M' as IntervalType,
    startDate: new Date('2024-04-01'),
    endDate: new Date('2024-04-30'),
    sourceContainerIdentifier: 'test-container',
    universe: 'Hospital',
    runtime: 1681234567890, // Add runtime timestamp
    measures: ['123', '456'],
  }

  // Mock data for API responses
  const mockACOPerformanceData: SisenseMeasureSummary[] = [
    {
      Period: 'M',
      StartDate: '2024-04-01',
      MeasureName: 'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
      EntityId: 1,
      EntityDescription: 'Test Entity 1',
      Organization: 'Test Organization 1',
      SourceContainerIdentifier: 'test-container',
      Performance: 85.5,
      Numerator: 171,
      PerformanceDenominator: 200,
      PTile: 80,
      PercentileSourceCode: 'Medisolv All Facilities',
      SummaryDataRowCount: 1,
      MeasureIdentifier: '123',
    },
  ]

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('passes loading state to table component', () => {
    // Mock the useQuery hook to return loading state
    mockUseQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    })

    // Render the component
    const { getByTestId } = render(
      <ACOPerformanceAnalysisReport {...defaultProps} />
    )

    // Get the props passed to the table component
    const tableProps = JSON.parse(
      getByTestId('aco-table').getAttribute('data-props') || '{}'
    )

    // Check that the loading state is passed correctly
    expect(tableProps.isLoading).toBe(true)
    expect(tableProps.error).toBeNull()
    expect(tableProps.data).toEqual([])
  })

  test('passes error state to table component', () => {
    // Mock the useQuery hook to return error state
    const error = { message: 'Failed to fetch data' }
    mockUseQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error,
    })

    // Render the component
    const { getByTestId } = render(
      <ACOPerformanceAnalysisReport {...defaultProps} />
    )

    // Get the props passed to the table component
    const tableProps = JSON.parse(
      getByTestId('aco-table').getAttribute('data-props') || '{}'
    )

    // Check that the error state is passed correctly
    expect(tableProps.isLoading).toBe(false)
    expect(tableProps.error).toEqual(error)
    expect(tableProps.data).toEqual([])
  })

  test('passes data to table component', () => {
    // Mock the useQuery hook to return data
    mockUseQuery.mockReturnValue({
      data: mockACOPerformanceData,
      isLoading: false,
      error: null,
    })

    // Render the component
    const { getByTestId } = render(
      <ACOPerformanceAnalysisReport {...defaultProps} />
    )

    // Get the props passed to the table component
    const tableProps = JSON.parse(
      getByTestId('aco-table').getAttribute('data-props') || '{}'
    )

    // Check that the data is passed correctly
    expect(tableProps.isLoading).toBe(false)
    expect(tableProps.error).toBeNull()
    expect(tableProps.data).toEqual(mockACOPerformanceData)
  })

  test('passes correct parameters to useQuery', () => {
    // Mock the useQuery hook
    mockUseQuery.mockReturnValue({
      data: mockACOPerformanceData,
      isLoading: false,
      error: null,
    })

    // Render the component
    render(<ACOPerformanceAnalysisReport {...defaultProps} />)

    // Check that useQuery was called with the correct parameters
    expect(mockUseQuery).toHaveBeenCalledWith({
      intervalType: 'M',
      startDate: new Date('2024-04-01'),
      endDate: new Date('2024-04-30'),
      sourceContainerIdentifier: 'test-container',
      universe: 'Hospital',
      runtime: 1681234567890, // Check that runtime is passed to the query
      measureIdentifiers: ['123', '456'],
    })
  })
})
