import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { MVPSummaryComponent } from '@/components/reports/mvp/MVPSummary'

let mockUseMVPStore: jest.Mock
let mockUseQuery: jest.Mock

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

// Mock the zustand store
jest.mock('@/stores/mvp', () => ({
  useMVPStore: () => mockUseMVPStore(),
}))

beforeEach(() => {
  jest.clearAllMocks()

  mockUseQuery = jest.fn()
  mockUseMVPStore = jest.fn()

  // Default store implementation
  mockUseMVPStore = jest.fn().mockReturnValue({
    mvpConfig: {
      year: 2024,
    },
    openConfigureReport: false,
    setConfigureReportOpen: jest.fn(),
    setMVPConfig: jest.fn(),
    summaryData: [],
    setSummaryData: jest.fn(),
    chartData: [],
    setChartData: jest.fn(),
  })

  // Setup default mock returns for queries
  mockUseQuery.mockReturnValue({
    data: [],
    isLoading: false,
  })
})

// Mock next/navigation
jest.mock('next/navigation', () => ({
  usePathname: () => '/mvp',
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
  }),
  useSearchParams: () => ({
    get: (key: any) => {
      if (key === 'reportId') return '123' // or whatever test value you need
      return null
    },
  }),
}))

// Mock the trpc API
jest.mock('@/trpc/react', () => ({
  api: {
    report: {
      getMVPSummaryResults: {
        useQuery: (...args: any[]) => ({
          data: [
            {
              Entity: 'Bluebird Medical Group',
              MVP: 'Focusing on Womens Health',
              Quality: 85,
              IA: 40,
              PI: 30,
              Overall: 90,
              Score: 155,
              Cost: 25,
              CostMax: 10,
              QualityMax: 100,
              IAMax: 100,
              PIScoreMax: 100,
            },
            {
              Entity: 'Redwood Health Partners',
              MVP: 'Heart Disease',
              Quality: 75,
              IA: 35,
              PI: 25,
              Overall: 80,
              Score: 130,
              Cost: 20,
              CostMax: 10,
              QualityMax: 100,
              IAMax: 100,
              PIScoreMax: 100,
            },
          ],
          isLoading: false,
        }),
      },
      getMVPs: {
        useQuery: (...args: any[]) => ({
          data: [{ MVPCategory: 'Focusing on Womens Health' }],
          isLoading: false,
        }),
      },
      getSubmissionGroups: {
        useQuery: (...args: any[]) => ({
          data: [{ submissionGroupName: 'Bluebird Medical Group' }],
          isLoading: false,
        }),
      },
      getQualityScoreByMVP: {
        useQuery: (...args: any[]) => ({
          data: [
            { MVP: 'Focusing on Womens Health', Period: '2024-Q1', Score: 85 },
            { MVP: 'Heart Disease', Period: '2024-Q1', Score: 75 },
          ],
          isLoading: false,
        }),
      },
      getReportById: {
        useQuery: (...args: any[]) => ({
          data: {
            partitionKey: 'reports',
            rowKey: 1,
            name: 'MVP Report',
            description: '',
            reportConfig: '',
            isActive: true,
            isCustom: true,
            isDeleted: false,
            userId: 13,
            lastModified: new Date().toISOString(),
            reportType: 'mvp',
          },
          isLoading: false,
        }),
      },
    },
  },
}))

// Mock ag-charts-react
jest.mock('ag-charts-react', () => ({
  AgCharts: () => <div data-testid="mock-ag-chart" />,
}))

// Mock ag-charts-react
jest.mock('ag-charts-community', () => ({
  AgCharts: () => <div data-testid="mock-ag-chart-community" />,
}))

// Mock the Loader component
jest.mock('@/components/ui/Loader', () => {
  return function MockLoader() {
    return <div data-testid="loading-spinner">Loading...</div>
  }
})

describe('MVPSummary', () => {
  test('renders placeholder content when no year is selected', () => {
    mockUseMVPStore = jest.fn().mockReturnValue({
      mvpConfig: { year: 0 },
      openConfigureReport: false,
      setConfigureReportOpen: jest.fn(),
      setMVPConfig: jest.fn(),
    })

    render(<MVPSummaryComponent />)

    expect(screen.getByTestId('placeholder-content')).toBeInTheDocument()
    expect(screen.getByText(/New Report Creation/i)).toBeInTheDocument()
  })
  test('renders summary table when data is available', () => {
    render(<MVPSummaryComponent />)

    // Check for table headers - use more specific queries
    const headers = screen.getAllByRole('columnheader')
    expect(
      headers.some((header) => header.textContent?.includes('SUBMISSION GROUP'))
    ).toBeTruthy()
    expect(
      headers.some((header) => header.textContent?.includes('MVP'))
    ).toBeTruthy()
    expect(
      headers.some((header) => header.textContent?.includes('QUALITY'))
    ).toBeTruthy()

    // Check for data rows - use getByText with a more specific container if possible
    expect(screen.getByText('Bluebird Medical Group')).toBeInTheDocument()
    expect(screen.getByText('Focusing on Womens Health')).toBeInTheDocument()
    expect(screen.getByText('Redwood Health Partners')).toBeInTheDocument()
    expect(screen.getByText('Heart Disease')).toBeInTheDocument()
  })

  test('handles row click to navigate to details', () => {
    // Mock the store with navigation functions
    const mockSetMVPConfig = jest.fn()

    mockUseMVPStore = jest.fn().mockReturnValue({
      mvpConfig: { year: 2024 },
      setMVPConfig: mockSetMVPConfig,
      summaryData: [],
      chartData: [],
      setSummaryData: jest.fn(),
      setChartData: jest.fn(),
    })
    render(<MVPSummaryComponent />)

    // Find and click on a row
    const row = screen.getByText('Bluebird Medical Group')
    fireEvent.click(row)

    // Verify the navigation functions were called
    expect(mockSetMVPConfig).toHaveBeenCalledWith(
      expect.objectContaining({
        year: 2024,
        mvp: 'Focusing on Womens Health',
        submissionGroup: 'Bluebird Medical Group',
      })
    )
  })

  test('opens configure report dialog when button is clicked', () => {
    const mockSetConfigureReportOpen = jest.fn()

    mockUseMVPStore = jest.fn().mockReturnValue({
      mvpConfig: { year: 0 },
      openConfigureReport: false,
      setConfigureReportOpen: mockSetConfigureReportOpen,
      setMVPConfig: jest.fn(),
    })

    render(<MVPSummaryComponent />)

    // Find and click configure report button
    const configureButton = screen.getAllByRole('button', {
      name: /Configure Report/i,
    })
    fireEvent.click(configureButton[0]!)

    // Verify the function was called
    expect(mockSetConfigureReportOpen).toHaveBeenCalledWith(true)
  })
})
