import React from 'react'
import { fireEvent, render, screen } from '@testing-library/react'
import { MVPDetails } from '@/components/reports/mvp/MVPDetails'

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

jest.mock('@/components/reports/mvp/GaugeChart', () => ({
  GaugeChart: jest.fn(({ score, isCostGauge, setUpdatedCost }) => (
    <div
      data-testid="mock-gauge-chart"
      data-score={score?.Score}
      data-is-cost={isCostGauge ? 'true' : 'false'}
    >
      Mock Gauge Chart: {score?.Score}%
      {isCostGauge && (
        <button
          data-testid="cost-update-button"
          onClick={() => setUpdatedCost && setUpdatedCost(10)}
        >
          Update Cost
        </button>
      )}
    </div>
  )),
}))

jest.mock('@/components/reports/common/ConfigureReportButton', () => ({
  __esModule: true,
  default: jest.fn(({ children, ref }) => (
    <button ref={ref} data-testid="configure-report-button">
      {children || 'Configure Report'}
    </button>
  )),
}))

let mockUseMVPStore: jest.Mock
let mockUseQuery: jest.Mock

// Mock the zustand store
jest.mock('@/stores/mvp', () => ({
  useMVPStore: () => mockUseMVPStore(),
}))

beforeEach(() => {
  jest.clearAllMocks()

  mockUseQuery = jest.fn()
  mockUseMVPStore = jest.fn()

  mockUseMVPStore = jest.fn().mockReturnValue({
    mvpConfig: {
      year: 2024,
      mvp: 'Focusing on Womens Health',
      submissionGroup: 'Bluebird Medical Group',
    },
    openConfigureReport: false,
    setConfigureReportOpen: jest.fn(),
    setMVPConfig: () => jest.fn(),
    detailsData: {
      overallScore: undefined,
      qualityScore: undefined,
      iaScore: undefined,
      piScore: undefined,
      costScore: undefined,
      qualityMeasures: [],
      providerPerformance: [],
      qualityMeasuresData: [],
      iaMeasuresData: [],
      piMeasuresData: [],
    },
    setDetailsData: jest.fn(),
  })

  // Setup default mock returns for queries
  mockUseQuery.mockReturnValue({
    data: [],
    isLoading: false,
  })
})

// Mock next/navigation
jest.mock('next/navigation', () => ({
  usePathname: () => '/mvp-details',
  useSearchParams: () => ({
    get: (key: any) => {
      if (key === 'reportId') return '123' // or whatever test value you need
      return null
    },
  }),
}))

const mockInvalidate = jest.fn()

const mockUseUtils = jest.fn(() => ({
  report: {
    getQualityMeasuresScore: {
      invalidate: mockInvalidate,
    },
  },
}))

// Mock the trpc API
jest.mock('@/trpc/react', () => ({
  api: {
    report: {
      getMVPs: {
        useQuery: (...args: any[]) => ({
          data: [{ MVPCategory: 'Focusing on Womens Health' }],
          isLoading: false,
        }),
      },
      getSubmissionGroups: {
        useQuery: (...args: any[]) => ({
          data: [{ submissionGroupName: 'Bluebird Medical Group' }],
          isLoading: false,
        }),
      },
      getQualityMeasuresScore: {
        useQuery: (...args: any[]) => ({
          data: [
            { MeasureName: 'Measure 1', Period: '2024-01', Score: 85 },
            { MeasureName: 'Measure 2', Period: '2024-01', Score: 75 },
          ],
          isLoading: false,
        }),
      },
      getQualityMeasures: {
        useQuery: (...args: any[]) => ({
          data: [
            {
              MeasureName: 'Quality Measure 1',
              Rank: 1,
              Numerator: 10,
              Denominator: 20,
              Performance: 0.5,
              PTile: 0.25,
              Points: 8.5,
            },
          ],
          isLoading: false,
        }),
      },
      getIAPIMeasures: {
        useQuery: (...args: any[]) => ({
          data: {
            IAMeasures: [
              { MeasureName: 'IA Measure 1', Completed: true, Points: 8.5 },
            ],
            PIMeasures: [
              { MeasureName: 'PI Measure 1', Completed: true, Points: 8.5 },
            ],
          },
          isLoading: false,
        }),
      },
      getScoreByMeasureType: {
        useQuery: (...args: any[]) => ({
          data: [
            { MeasureType: 'Quality', Score: 85, Max: 100, Min: 0 },
            { MeasureType: 'IA', Score: 40, Max: 100, Min: 0 },
            { MeasureType: 'PI', Score: 30, Max: 100, Min: 0 },
          ],
          isLoading: false,
        }),
      },
      getOverallWeightedScore: {
        useQuery: (...args: any[]) => ({
          data: { Score: 90, Max: 100, Min: 0 },
          isLoading: false,
        }),
      },
      getProviderPerformance: {
        useQuery: (...args: any[]) => ({
          data: [{ ProviderName: 'Provider 1', MeasureCount: 5, Points: 8.5 }],
          isLoading: false,
        }),
      },
      getProviderDetails: {
        useQuery: (...args: any[]) => ({
          data: [
            {
              MeasureName: 'Measure 3',
              Numerator: 10,
              Denominator: 20,
              Performance: 0.5,
              PTile: 0.25,
              Completed: true,
              Points: 8.5,
            },
            {
              MeasureName: 'Measure 4',
              Numerator: 15,
              Denominator: 30,
              Performance: 0.5,
              PTile: 0.25,
              Completed: false,
              Points: 7.5,
            },
          ],
          isLoading: false,
        }),
      },
      getReportById: {
        useQuery: (...args: any[]) => ({
          data: {
            partitionKey: 'reports',
            rowKey: 1,
            name: 'MVP Report',
            description: '',
            reportConfig: '',
            isActive: true,
            isCustom: true,
            isDeleted: false,
            userId: 13,
            lastModified: new Date().toISOString(),
            reportType: 'mvp',
          },
          isLoading: false,
        }),
      },
    },
    useUtils: () => mockUseUtils(),
  },
}))

// Mock ag-charts-react
jest.mock('ag-charts-react', () => ({
  AgCharts: () => <div data-testid="mock-ag-chart">Mock AG Chart</div>,
}))

// Mock ag-charts-community
jest.mock('ag-charts-community', () => ({
  AgCartesianChartOptions: jest.fn(),
  AgCartesianSeriesOptions: jest.fn(),
}))

// Mock the Chart.js
jest.mock('chart.js/auto', () => {
  return {
    Chart: class MockChart {
      constructor() {
        this.destroy()
      }
      destroy() {
        this.destroy()
      }
      static register() {}
      static overrides: Record<string, any> = {
        doughnut: {
          plugins: {
            legend: {},
          },
        },
      }
    },
  }
})

// Mock the Loader component
jest.mock('@/components/ui/Loader', () => {
  return function MockLoader() {
    return <div data-testid="loading-spinner">Loading...</div>
  }
})

// Mock the Lucide React icons
jest.mock('lucide-react', () => ({
  ArrowRight: () => <div data-testid="arrow-right-icon">Arrow Right Icon</div>,
  Check: () => <div data-testid="check-icon">Check Icon</div>,
  RefreshCw: () => <div data-testid="refresh-icon">Refresh Icon</div>,
  X: () => <div data-testid="x-icon">X Icon</div>,
  Star: () => <div data-testid="star-icon">Star Icon</div>,
  Trash2: () => <div data-testid="trash-icon">Trash Icon</div>,
}))

// Mock the getColor function
jest.mock('@/lib/generateColorsForChart', () => ({
  getColor: jest.fn().mockImplementation((index) => ({
    line: '#123456',
    background: 'rgba(18, 52, 86, 0.2)',
  })),
}))

describe('MVPDetails', () => {
  test('renders placeholder content when no year is selected', () => {
    mockUseMVPStore.mockReturnValue({
      mvpConfig: {
        year: 0,
        mvp: undefined,
        submissionGroup: undefined,
      },
      openConfigureReport: false,
      setConfigureReportOpen: jest.fn(),
      setMVPConfig: jest.fn(),
    })
    render(<MVPDetails />)
    expect(screen.getByText(/New Report Creation/i)).toBeInTheDocument()
  })
  test('renders report content when data is available', () => {
    // Setup your mock store with appropriate values
    mockUseMVPStore.mockReturnValue({
      mvpConfig: {
        year: 2023,
        mvp: 'Test MVP',
        submissionGroup: 'Test Group',
      },
      openConfigureReport: false,
      setConfigureReportOpen: jest.fn(),
      setMVPConfig: jest.fn(),
      detailsData: {
        overallScore: { Score: 90 },
        qualityScore: { Score: 85 },
        iaScore: { Score: 75 },
        piScore: { Score: 65 },
        costScore: { Score: 55 },
        qualityMeasures: [],
        providerPerformance: [],
        qualityMeasuresData: [],
        iaMeasuresData: [],
        piMeasuresData: [],
      },
      setDetailsData: jest.fn(),
    })

    render(<MVPDetails />)

    // Check for overall score
    expect(screen.getByText(/Overall Weighted Score/i)).toBeInTheDocument()

    // Check for gauge charts
    const gaugeCharts = screen.getAllByTestId('mock-gauge-chart')
    expect(gaugeCharts.length).toBeGreaterThan(0)

    // Test interaction with the cost gauge chart
    const costUpdateButton = screen.getByTestId('cost-update-button')
    fireEvent.click(costUpdateButton)

    // Check for quality score - use a more specific query
    const qualityScoreHeader = screen.getAllByText(/Quality Score/i)[0]
    expect(qualityScoreHeader).toBeInTheDocument()
    expect(qualityScoreHeader?.textContent).toContain('85')

    // Check for charts
    expect(screen.getByText(/Quality Score Over Time/i)).toBeInTheDocument()
    expect(screen.getByTestId('mock-ag-chart')).toBeInTheDocument()

    // Check for provider performance
    expect(screen.getByText(/Provider Performance/i)).toBeInTheDocument()
    expect(screen.getByText(/Provider 1/)).toBeInTheDocument()

    // Check for measure tables
    const qualityTab = screen.getAllByText(/Quality/i)[0]
    expect(qualityTab).toBeInTheDocument()
    expect(screen.getByText(/Activities/)).toBeInTheDocument()
    expect(screen.getByText(/Interoperability/)).toBeInTheDocument()
  })
  test('handles interval change for quality measures chart', () => {
    render(<MVPDetails />)
    // Find and click interval selector
    const monthlyButton = screen.getAllByText(/MONTHLY/)[0]
    fireEvent.click(monthlyButton!)
    // Verify the invalidate function was called
    expect(mockInvalidate).toHaveBeenCalled()
  })
  test('handles measure type change in measure tables', () => {
    render(<MVPDetails />)
    // Initially should show Quality measures
    expect(screen.getByText(/Quality Measure 1/)).toBeInTheDocument()
    // Click on Activities tab
    const activitiesTab = screen.getByText(/ACTIVITIES/)
    fireEvent.click(activitiesTab)
    // Should now show IA measures
    expect(screen.getByText(/IA Measure 1/)).toBeInTheDocument()
    // Click on Interoperability tab
    const interoperabilityTab = screen.getByText(/INTEROPERABILITY/)
    fireEvent.click(interoperabilityTab)
    // Should now show PI measures
    expect(screen.getByText(/PI Measure 1/)).toBeInTheDocument()
  })
  test('opens provider detail dialog when clicking on a provider', () => {
    render(<MVPDetails />)
    // Find and click on a provider
    const providerCell = screen.getByText(/Provider 1/)
    fireEvent.click(providerCell)
    // Should open the provider details dialog
    expect(screen.getByText(/Provider Detail View/i)).toBeInTheDocument()
    expect(screen.getByText(/Measure 3/)).toBeInTheDocument()
    expect(screen.getByText(/Measure 4/)).toBeInTheDocument()
  })
})
