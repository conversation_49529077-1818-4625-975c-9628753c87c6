import React from 'react'
import { render, screen } from '@testing-library/react'
import { SisenseMeasureSummary } from '@/services/sisense/SisenseMeasureSummaryRepository'
import ACOPerformanceAnalysisTable from '@/components/reports/aco/ACOPerformanceAnalysisTable'

// Mock the Loader component
jest.mock('@/components/ui/Loader', () => {
  return function MockLoader() {
    return <div data-testid="loader">Loading...</div>
  }
})

// Mock the Pagination component
jest.mock('@/components/measures/pagination', () => ({
  Pagination: () => <div data-testid="pagination">Pagination Component</div>,
}))

// Mock the getSortingIcon function
jest.mock('@/components/ui/sortIcon', () => ({
  getSortingIcon: jest
    .fn()
    .mockReturnValue(<span data-testid="sort-icon">↑</span>),
}))

describe('ACOPerformanceAnalysisTable', () => {
  // Mock data for the table
  const mockACOPerformanceData: SisenseMeasureSummary[] = [
    {
      Period: 'M',
      StartDate: '2024-04-01',
      MeasureName: 'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
      EntityId: 1,
      EntityDescription: 'Test Entity 1',
      Organization: 'Test Organization 1',
      SourceContainerIdentifier: 'test-container',
      Performance: 85.5,
      Numerator: 171,
      PerformanceDenominator: 200,
      PTile: 80,
      PercentileSourceCode: 'Medisolv All Facilities',
      SummaryDataRowCount: 1,
    },
    {
      Period: 'M',
      StartDate: '2024-04-01',
      MeasureName: 'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)',
      EntityId: 2,
      EntityDescription: 'Test Entity 2',
      Organization: 'Test Organization 2',
      SourceContainerIdentifier: 'test-container',
      Performance: 92.0,
      Numerator: 184,
      PerformanceDenominator: 200,
      PTile: 90,
      PercentileSourceCode: 'Medisolv All Facilities',
      SummaryDataRowCount: 1,
    },
  ]

  // Default props for the component
  const defaultProps = {
    isLoading: false,
    error: null,
    data: mockACOPerformanceData,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders loading state when isLoading is true', () => {
    // Render the component with loading state
    render(<ACOPerformanceAnalysisTable {...defaultProps} isLoading={true} />)

    // Check that the loading indicator is displayed
    expect(screen.getByTestId('loader')).toBeInTheDocument()
  })

  test('renders error state when there is an error', () => {
    // Render the component with error state
    render(
      <ACOPerformanceAnalysisTable
        {...defaultProps}
        error={{ message: 'Failed to fetch data' }}
      />
    )

    // Check that the error message is displayed
    expect(screen.getByText('Failed to fetch data')).toBeInTheDocument()
  })

  test('renders empty state when no data is available', () => {
    // Render the component with empty data
    render(<ACOPerformanceAnalysisTable {...defaultProps} data={[]} />)

    // Check that the empty state message is displayed
    expect(
      screen.getByText('No data available for the selected measures')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Try selecting different measures or date range')
    ).toBeInTheDocument()
  })

  test('renders table with data when data is available', () => {
    // Render the component with data
    render(<ACOPerformanceAnalysisTable {...defaultProps} />)

    // Check that the table headers are displayed
    expect(screen.getByText('ENTITY (ORGANIZATION)')).toBeInTheDocument()
    expect(screen.getByText('ORGANIZATION')).toBeInTheDocument()
    expect(screen.getByText('LONG MEASURE NAME')).toBeInTheDocument()
    expect(screen.getByText('PERFORMANCE')).toBeInTheDocument()
    expect(screen.getByText('NUMERATOR')).toBeInTheDocument()
    expect(screen.getByText('DENOMINATOR')).toBeInTheDocument()

    // Check that the data is displayed in the table
    expect(screen.getByText('Test Entity 1')).toBeInTheDocument()
    expect(screen.getByText('Test Organization 1')).toBeInTheDocument()
    const measureNameElements = screen.getAllByText(
      'Diabetes: Hemoglobin A1c (HbA1c) Poor Control (> 9%) (E)'
    )
    expect(measureNameElements.length).toBeGreaterThan(0)
    expect(screen.getByText('85.5%')).toBeInTheDocument()
    expect(screen.getByText('171')).toBeInTheDocument()
    const denominatorElements = screen.getAllByText('200')
    expect(denominatorElements.length).toBeGreaterThan(0)

    // Check that the second row of data is displayed
    expect(screen.getByText('Test Entity 2')).toBeInTheDocument()
    expect(screen.getByText('Test Organization 2')).toBeInTheDocument()
    expect(screen.getByText('92%')).toBeInTheDocument()
    expect(screen.getByText('184')).toBeInTheDocument()
  })

  test('uses custom transform function when provided', () => {
    // Create a custom transform function
    const customTransform = jest.fn().mockReturnValue([
      {
        entityOrganization: 'Custom Entity 1',
        organization: 'Custom Org 1',
        longMeasureName: 'Custom Measure 1',
        performance: '75%',
        numerator: '150',
        performanceDenominator: '200',
      },
    ])

    // Render the component with custom transform function
    render(
      <ACOPerformanceAnalysisTable
        {...defaultProps}
        transformData={customTransform}
      />
    )

    // Check that the custom transform function was called with the data
    expect(customTransform).toHaveBeenCalledWith(mockACOPerformanceData)

    // Check that the transformed data is displayed
    expect(screen.getByText('Custom Entity 1')).toBeInTheDocument()
    expect(screen.getByText('Custom Org 1')).toBeInTheDocument()
    expect(screen.getByText('Custom Measure 1')).toBeInTheDocument()
    expect(screen.getByText('75%')).toBeInTheDocument()
    expect(screen.getByText('150')).toBeInTheDocument()
    expect(screen.getByText('200')).toBeInTheDocument()
  })

  test('displays pagination when there are more than 10 rows', () => {
    // Create mock data with more than 10 rows
    const mockDataWithManyRows = Array(15)
      .fill(null)
      .map((_, index) => ({
        ...mockACOPerformanceData[0],
        EntityId: index + 1,
        EntityDescription: `Test Entity ${index + 1}`,
        Organization: `Test Organization ${index + 1}`,
      }))

    // Render the component with many rows
    render(
      <ACOPerformanceAnalysisTable
        {...defaultProps}
        data={mockDataWithManyRows}
      />
    )

    // Check that the pagination component is displayed
    expect(screen.getByTestId('pagination')).toBeInTheDocument()
  })

  test('does not display pagination when there are 10 or fewer rows', () => {
    // Create mock data with exactly 10 rows
    const mockDataWithTenRows = Array(10)
      .fill(null)
      .map((_, index) => ({
        ...mockACOPerformanceData[0],
        EntityId: index + 1,
        EntityDescription: `Test Entity ${index + 1}`,
        Organization: `Test Organization ${index + 1}`,
      }))

    // Render the component with 10 rows
    render(
      <ACOPerformanceAnalysisTable
        {...defaultProps}
        data={mockDataWithTenRows}
      />
    )

    // Check that the pagination component is not displayed
    expect(screen.queryByTestId('pagination')).not.toBeInTheDocument()
  })
})
