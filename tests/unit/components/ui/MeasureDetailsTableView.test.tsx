import React from 'react'
import { render, screen, fireEvent, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import { MeasureDetailsTableView } from '@/components/ui/MeasureDetailsTableView'
import dayjs from 'dayjs'
import { ScorecardView } from '@/enums/scorecardView'
import { EntityDetailType } from '@/enums/entityDetailType'
import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'

// Mock dependencies
let mockUseUIStore: jest.Mock
let mockUseQuery: jest.Mock
let mockUsePrefetchQuery: jest.Mock
let mockUseUserSessionStore: jest.Mock

// Mock MeasureDetailsPatientSubGrid component
jest.mock('@/components/ui/MeasureDetailsPatientSubGrid', () => ({
  MeasureDetailsPatientSubGrid: ({
    entityId,
    measureId,
    period,
    scorecardView,
    entityType,
    sourceContainerIdentifier,
    primaryMeasureType,
  }: {
    entityId: string
    measureId: string
    period: string
    scorecardView: ScorecardView
    entityType: EntityDetailType
    sourceContainerIdentifier?: string
    primaryMeasureType: PrimaryMeasureTypeConstants
  }) => (
    <div data-testid="mock-patient-subgrid">
      <div>Entity ID: {entityId}</div>
      <div>Measure ID: {measureId}</div>
      <div>Period: {period}</div>
      <div>Scorecard View: {scorecardView}</div>
      <div>Entity Type: {entityType}</div>
      <div>Source Container ID: {sourceContainerIdentifier || 'N/A'}</div>
      <div>Primary Measure Type: {primaryMeasureType}</div>
    </div>
  ),
}))

// Mock Loader component
jest.mock('@/components/ui/Loader', () => ({
  __esModule: true,
  default: () => <div data-testid="loader">Loading...</div>,
}))

// Mock Pagination component
jest.mock('@/components/measures/pagination', () => ({
  Pagination: ({ table }: any) => (
    <div data-testid="pagination">
      <button
        data-testid="prev-page"
        onClick={() => table.previousPage()}
        disabled={!table.getCanPreviousPage()}
      >
        Previous
      </button>
      <span data-testid="page-info">
        Page {table.getState().pagination.pageIndex + 1} of{' '}
        {table.getPageCount()}
      </span>
      <button
        data-testid="next-page"
        onClick={() => table.nextPage()}
        disabled={!table.getCanNextPage()}
      >
        Next
      </button>
    </div>
  ),
}))

// Mock sortIcon
jest.mock('@/components/ui/sortIcon', () => ({
  getSortingIcon: (isSorted: boolean, isDesc: boolean) => (
    <span
      data-testid={`sort-icon-${isSorted ? (isDesc ? 'desc' : 'asc') : 'none'}`}
    >
      {isSorted ? (isDesc ? '↓' : '↑') : ''}
    </span>
  ),
}))

// Mock the stores
jest.mock('@/stores/ui', () => ({
  useUIStore: () => mockUseUIStore(),
}))

jest.mock('@/stores/userSession', () => ({
  useUserSessionStore: () => mockUseUserSessionStore(),
}))

// Mock the API
jest.mock('@/trpc/react', () => ({
  api: {
    measures: {
      getIAPIMeasureDetails: {
        useQuery: (...args: any[]) => mockUseQuery(...args),
      },
      getMeasureResultDetails: {
        useQuery: (...args: any[]) => mockUseQuery(...args),
        usePrefetchQuery: (...args: any[]) => mockUsePrefetchQuery(...args),
      },
      getMeasureMetaData: {
        usePrefetchQuery: (...args: any[]) => mockUsePrefetchQuery(...args),
      },
    },
    patients: {
      getPatientDetails: {
        useQuery: () => ({
          data: [],
          isLoading: false,
        }),
      },
    },
  },
}))

// Mock dayjs
jest.mock('dayjs', () => {
  const mockDayjs = (date: any) => {
    return {
      format: () => '2024-01-01',
      utc: () => ({
        toDate: () => new Date('2024-01-01'),
      }),
      clone: () => mockDayjs(date),
      isBefore: () => false,
      isAfter: () => false,
      isSame: () => true,
      startOf: () => mockDayjs(date),
      endOf: () => mockDayjs(date),
      add: () => mockDayjs(date),
      subtract: () => mockDayjs(date),
      toDate: () => new Date('2024-01-01'),
      toISOString: () => new Date('2024-01-01').toISOString(),
    }
  }

  // Add static methods
  mockDayjs.utc = () => mockDayjs(new Date())

  return mockDayjs
})

// Mock cn utility
jest.mock('@/lib/utils', () => ({
  cn: (...inputs: any[]) => inputs.filter(Boolean).join(' '),
}))

// Setup before each test
beforeEach(() => {
  mockUseQuery = jest.fn()
  mockUsePrefetchQuery = jest.fn()
  mockUseUIStore = jest.fn()
  mockUseUserSessionStore = jest.fn().mockReturnValue({
    primaryMeasureType: PrimaryMeasureTypeConstants.HospitalMeasures,
  })

  // Reset console.log to avoid noise in tests
  jest.spyOn(console, 'log').mockImplementation(() => {})
  jest.spyOn(console, 'error').mockImplementation(() => {})
})

// Mock data for tests
const mockMeasureData = [
  {
    date: '01/2024',
    fullDate: '01/01/2024',
    rate: '60',
    numerator: '60',
    denominator: '100',
    goal: '-',
    benchmark: '-',
    population: '120',
    inDenominatorOnly: '40',
    denominatorExclusion: '20',
    exception: '-',
    endDate: '01/31/2024',
    noDrill: false,
    isPatientLevelAccessAvailable: true,
  },
  {
    date: '02/2024',
    fullDate: '02/01/2024',
    rate: '70',
    numerator: '70',
    denominator: '100',
    goal: '-',
    benchmark: '-',
    population: '130',
    inDenominatorOnly: '30',
    denominatorExclusion: '30',
    exception: '-',
    endDate: '02/29/2024',
    noDrill: false,
    isPatientLevelAccessAvailable: true,
  },
]

const mockIAPIData = [
  {
    ActivityID: '123',
    Groupname: 'Group A',
    ActivityName: 'Activity 1',
    MeasureID: 'M1',
    ImplementationStartDate: '2024-01-01',
    ImplementationEndDate: '2024-01-31',
    NumberofDays: '31',
    Status: 'Complete',
  },
  {
    ActivityID: '456',
    Groupname: 'Group B',
    ActivityName: 'Activity 2',
    MeasureID: 'M1',
    ImplementationStartDate: '2024-02-01',
    ImplementationEndDate: '2024-02-28',
    NumberofDays: '28',
    Status: 'In Progress',
  },
]

// Test cases
describe('MeasureDetailsTableView', () => {
  test('should show loader when data is loading', () => {
    // Setup UI store with required values
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'Test Measure',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'TABLE',
      isIAPIMeasure: false,
      currentStartDate: dayjs('2024-01-01'),
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return loading state
    mockUseQuery.mockReturnValue({
      isPending: true,
      data: undefined,
    })

    render(<MeasureDetailsTableView />)

    expect(screen.getByTestId('loader')).toBeInTheDocument()
  })

  test('should show loader when currentStartDate is not available', () => {
    // Setup UI store without currentStartDate
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'Test Measure',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'TABLE',
      isIAPIMeasure: false,
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return data
    mockUseQuery.mockReturnValue({
      isPending: false,
      data: mockMeasureData,
    })

    render(<MeasureDetailsTableView />)

    expect(screen.getByTestId('loader')).toBeInTheDocument()
  })

  test('should render regular measure data table correctly', () => {
    // Setup UI store
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'Test Measure',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'TABLE',
      isIAPIMeasure: false,
      currentStartDate: dayjs('2024-01-01'),
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return data
    mockUseQuery.mockReturnValue({
      isPending: false,
      data: mockMeasureData,
    })

    render(<MeasureDetailsTableView />)

    // Check table headers
    expect(screen.getByText('Date')).toBeInTheDocument()
    expect(screen.getByText('Rate')).toBeInTheDocument()
    expect(screen.getByText('Initial Population')).toBeInTheDocument()
    expect(screen.getByText('Denominator Only')).toBeInTheDocument()
    expect(screen.getByText('Den. Exclusion')).toBeInTheDocument()
    expect(screen.getByText('Numerator')).toBeInTheDocument()
    expect(screen.getByText('Denominator')).toBeInTheDocument()
    expect(screen.getByText('Exception')).toBeInTheDocument()

    // Check data rows
    expect(screen.getByText('01/2024')).toBeInTheDocument()
    expect(screen.getByText('02/2024')).toBeInTheDocument()
    // Use getAllByText for values that might appear multiple times
    expect(screen.getAllByText('60')).not.toHaveLength(0)
    expect(screen.getAllByText('70')).not.toHaveLength(0)
    expect(screen.getAllByText('120')).not.toHaveLength(0)
    expect(screen.getAllByText('130')).not.toHaveLength(0)

    // Check pagination is rendered
    expect(screen.getByTestId('pagination')).toBeInTheDocument()
  })

  test('should render IAPI measure data table correctly', () => {
    // Setup UI store for IAPI measure
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'IAPI Test Measure',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'TABLE',
      isIAPIMeasure: true,
      currentStartDate: dayjs('2024-01-01'),
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return IAPI data
    mockUseQuery.mockReturnValue({
      isPending: false,
      data: mockIAPIData,
    })

    render(<MeasureDetailsTableView />)

    // Check IAPI specific columns - using partial text matching
    expect(screen.getByText(/Implementation Start Date/i)).toBeInTheDocument()
    expect(screen.getByText(/Implementation End Date/i)).toBeInTheDocument()
    expect(screen.getByText('Number Of Days')).toBeInTheDocument()
    expect(screen.getByText(/Status/i)).toBeInTheDocument()

    // Check IAPI data
    expect(screen.getByText('2024-01-01')).toBeInTheDocument()
    expect(screen.getByText('2024-01-31')).toBeInTheDocument()
    expect(screen.getByText('31')).toBeInTheDocument()
    expect(screen.getByText('Complete')).toBeInTheDocument()
    expect(screen.getByText('2024-02-01')).toBeInTheDocument()
    expect(screen.getByText('2024-02-28')).toBeInTheDocument()
    expect(screen.getByText('28')).toBeInTheDocument()
    expect(screen.getByText('In Progress')).toBeInTheDocument()
  })

  test('should handle sorting when header is clicked', async () => {
    // Setup UI store
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'Test Measure',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'TABLE',
      isIAPIMeasure: false,
      currentStartDate: dayjs('2024-01-01'),
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return data
    mockUseQuery.mockReturnValue({
      isPending: false,
      data: mockMeasureData,
    })

    render(<MeasureDetailsTableView />)

    // Find all table headers and get the one containing 'Rate'
    const headers = screen.getAllByRole('columnheader')
    const rateHeader = headers.find((header) =>
      header.textContent?.includes('Rate')
    )

    // Click the Rate header to sort
    if (rateHeader) {
      await act(async () => {
        fireEvent.click(rateHeader)
      })

      // Check that sort icon appears
      expect(screen.getByTestId('sort-icon-asc')).toBeInTheDocument()

      // Click again to reverse sort
      await act(async () => {
        fireEvent.click(rateHeader)
      })

      // Check that sort icon changes to descending
      expect(screen.getByTestId('sort-icon-desc')).toBeInTheDocument()
    } else {
      // If we can't find the header, the test should fail
      expect(rateHeader).toBeDefined()
    }
  })

  test('should open patient detail dialog when clicking on a patient detail cell', async () => {
    // Setup UI store
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'Test Measure',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'TABLE',
      isIAPIMeasure: false,
      currentStartDate: dayjs('2024-01-01'),
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return data
    mockUseQuery.mockReturnValue({
      isPending: false,
      data: mockMeasureData,
    })

    // Setup user session store to include patientDetailAccess
    mockUseUserSessionStore.mockReturnValue({
      primaryMeasureType: PrimaryMeasureTypeConstants.HospitalMeasures,
      patientDetailAccess: {
        IsOrgLevelAccess: true,
      },
    })

    render(<MeasureDetailsTableView />)

    // Find all buttons in the component
    const buttons = screen.getAllByRole('button')

    // Find a button that contains the text '60' (this would be the patient detail cell button)
    const patientDetailButton = buttons.find((button) =>
      button.textContent?.includes('60')
    )
    expect(patientDetailButton).toBeDefined()

    if (patientDetailButton) {
      // Click on the patient detail button
      await act(async () => {
        fireEvent.click(patientDetailButton)
      })

      // Check that dialog is opened (would contain a close button with X)
      expect(screen.getByRole('button', { name: /close/i })).toBeInTheDocument()
      expect(screen.getByText(/Population Data for/i)).toBeInTheDocument()
    }
  })

  test('should handle pagination correctly', async () => {
    // Create more data to test pagination
    const paginationData = Array.from({ length: 15 }, (_, i) => ({
      date: `${(i + 1).toString().padStart(2, '0')}/2024`,
      fullDate: `${(i + 1).toString().padStart(2, '0')}/01/2024`,
      rate: `${60 + i}`,
      numerator: `${60 + i}`,
      denominator: '100',
      goal: '-',
      benchmark: '-',
      population: `${120 + i}`,
      inDenominatorOnly: `${40 - i}`,
      denominatorExclusion: `${20 + i}`,
      exception: '-',
      endDate: `${(i + 1).toString().padStart(2, '0')}/31/2024`,
      noDrill: false,
      isPatientLevelAccessAvailable: true,
    }))

    // Setup UI store
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'Test Measure',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'TABLE',
      isIAPIMeasure: false,
      currentStartDate: dayjs('2024-01-01'),
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return pagination data
    mockUseQuery.mockReturnValue({
      isPending: false,
      data: paginationData,
    })

    render(<MeasureDetailsTableView />)

    // Check initial page info
    expect(screen.getByTestId('page-info').textContent).toContain('Page 1')

    // Click next page button
    await act(async () => {
      fireEvent.click(screen.getByTestId('next-page'))
    })

    // Check page info updated
    expect(screen.getByTestId('page-info').textContent).toContain('Page 2')

    // Click previous page button
    await act(async () => {
      fireEvent.click(screen.getByTestId('prev-page'))
    })

    // Check page info back to page 1
    expect(screen.getByTestId('page-info').textContent).toContain('Page 1')
  })

  test('should not show pagination when not on TABLE tab', () => {
    // Setup UI store with a different tab
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'Test Measure',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'GRAPH', // Not TABLE
      isIAPIMeasure: false,
      currentStartDate: dayjs('2024-01-01'),
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return data
    mockUseQuery.mockReturnValue({
      isPending: false,
      data: mockMeasureData,
    })

    render(<MeasureDetailsTableView />)

    // Check that pagination is not rendered
    expect(screen.queryByTestId('pagination')).not.toBeInTheDocument()
  })

  test('should show "No Data available" message when IAPI data is empty', () => {
    // Setup UI store for IAPI measure
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'IAPI Test Measure',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'TABLE',
      isIAPIMeasure: true,
      currentStartDate: dayjs('2024-01-01'),
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return empty data
    mockUseQuery.mockReturnValue({
      isPending: false,
      data: [],
    })

    render(<MeasureDetailsTableView />)

    // Check for no data message
    expect(screen.getByText('No Data available')).toBeInTheDocument()
  })

  test('should use correct column title for median time measures', () => {
    // Setup UI store with a measure name containing "Median"
    mockUseUIStore.mockReturnValue({
      currentMeasureId: '1',
      currentMeasureName: 'Median Time to Treatment',
      entityId: 'entity1',
      entityType: EntityDetailType.Measure,
      aggregationType: ScorecardView.Monthly,
      currentMeasureDetailTab: 'TABLE',
      isIAPIMeasure: false,
      currentStartDate: dayjs('2024-01-01'),
      currentEndDate: dayjs('2024-03-31'),
    })

    // Setup query to return data
    mockUseQuery.mockReturnValue({
      isPending: false,
      data: mockMeasureData,
    })

    render(<MeasureDetailsTableView />)

    // Check that "Median Time" is used instead of "Rate"
    expect(screen.getByText('Median Time')).toBeInTheDocument()
    expect(screen.queryByText('Rate')).not.toBeInTheDocument()
  })
})
