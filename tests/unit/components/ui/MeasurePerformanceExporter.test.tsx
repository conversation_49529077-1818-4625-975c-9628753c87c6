import React from 'react'
import { render, screen } from '@testing-library/react'
import MeasurePerformanceExporter from '@/components/ui/MeasurePerformanceExporter'
import { CalculatedMeasure } from '@/types/calculatedMeasure'

// Mock the useMeasureResultsStore module
jest.mock('@/stores/measuresResultsStore')

// Import the mocked module after mocking
import { useMeasureResultsStore } from '@/stores/measuresResultsStore'
import { AggregatedMeasureDetails } from '@/types/aggregatedMeasureDetails'

// Cast the mocked function to the correct type
const mockedUseMeasureResultsStore = useMeasureResultsStore as unknown as jest.Mock

// Mock the Exporter component
jest.mock('@/components/ui/Exporter', () => {
    return jest.fn(({ exportDataset, buttonText, filePrefix }) => (
        <div data-testid="mock-exporter" data-disabled={!exportDataset}>
            <div data-testid="export-dataset">{JSON.stringify(exportDataset)}</div>
            <div data-testid="button-text">{buttonText}</div>
            <div data-testid="file-prefix">{filePrefix || ''}</div>
        </div>
    ))
})

// Test data used across multiple tests
const mockMeasureResults: CalculatedMeasure[] = [
    {
        measureIdentifier: 'measure1',
        measureTitle: 'Test Measure 1',
        cmsId: 'CMS123',
        domain: 'Test Domain',
        'Q1_2023': '85%',
        'Q2_2023': '90%',
    },
    {
        measureIdentifier: 'measure2',
        measureTitle: 'Test Measure 2',
        cmsId: 'CMS456',
        domain: 'Test Domain',
        'Q1_2023': '75%',
        'Q2_2023': '80%</br>Good',
    },
]

const mockAggregateMeasureResults: AggregatedMeasureDetails[] = [
    {
        fullDate: '2023-01-15',
        rate: '0.85',
        numerator: '85',
        denominator: '100',
        date: '',
        population: '',
        inDenominatorOnly: '',
        denominatorExclusion: '',
        exception: '',
        goal: '',
        benchmark: '',
        isPatientLevelAccessAvailable: false,
        noDrill: false
    },
    {
        fullDate: '2023-02-15',
        rate: '0.90',
        numerator: '90',
        denominator: '100',
        date: '',
        population: '',
        inDenominatorOnly: '',
        denominatorExclusion: '',
        exception: '',
        goal: '',
        benchmark: '',
        isPatientLevelAccessAvailable: false,
        noDrill: false
    },
    {
        fullDate: '2023-03-15',
        rate: '0.88',
        numerator: '88',
        denominator: '100',
        date: '',
        population: '',
        inDenominatorOnly: '',
        denominatorExclusion: '',
        exception: '',
        goal: '',
        benchmark: '',
        isPatientLevelAccessAvailable: false,
        noDrill: false
    }
]

// Clear mocks before each test
beforeEach(() => {
    jest.clearAllMocks()
})

test('should pass transformed measure performance to Exporter when performance data exists', () => {
    // Mock measure performance data
    mockedUseMeasureResultsStore.mockReturnValue({
        aggregatedMeasureDetails: mockAggregateMeasureResults,
    })

    render(<MeasurePerformanceExporter />)

    const exporter = screen.getByTestId('mock-exporter')
    expect(exporter).toBeInTheDocument()
    expect(exporter.getAttribute('data-disabled')).toBe('false')

    // Check that correct data is passed
    const exportDataset = JSON.parse(screen.getByTestId('export-dataset').textContent || '{}')

    // Verify headers
    expect(exportDataset.headers).toEqual(['date', 'performance', 'numerator', 'denominator'])

    // Verify dataset is correctly transformed
    expect(exportDataset.dataset).toHaveLength(3)
    expect(exportDataset.dataset[0].date).toBe('2023-01-15')
    expect(exportDataset.dataset[0].performance).toBe('0.85')
    expect(exportDataset.dataset[0].numerator).toBe('85')
    expect(exportDataset.dataset[0].denominator).toBe('100')
})

test('should pass "Export Data" as the button text', () => {
    mockedUseMeasureResultsStore.mockReturnValue({
        measureResults: mockMeasureResults,
    })

    render(<MeasurePerformanceExporter />)

    const buttonText = screen.getByTestId('button-text').textContent
    expect(buttonText).toBe('Export Data')
})

test('should pass "measure-performance" as the filePrefix', () => {
    mockedUseMeasureResultsStore.mockReturnValue({
        aggregatedMeasureDetails: mockAggregateMeasureResults,
    })

    render(<MeasurePerformanceExporter />)

    const filePrefix = screen.getByTestId('file-prefix').textContent
    expect(filePrefix).toBe('measure-performance')
})
