import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { api } from '@/trpc/react'
import { useActionsStore } from '@/stores/actions'
import { useViewStore } from '@/stores/viewStore'
import { useFilterStore } from '@/stores/filter'
import { useUserSessionStore } from '@/stores/userSession'
import { jest } from '@jest/globals'

const mockPostMessage = jest.fn()

function channelMock() {
  channelMock.prototype.postMessage = mockPostMessage
}

// @ts-ignore
self.BroadcastChannel = channelMock

jest.mock('@/env', () => ({
  env: {
    NEXT_PUBLIC_APPINSIGHTS_INSTRUMENTATIONKEY:
        'app-insights-instrumentation'
  },
}))

import { ActionsMenu } from '@/components/ui/ActionsMenu'

// Mock the necessary hooks and API
jest.mock('@/trpc/react', () => ({
  api: {
    users: {
      getOrganizationUsers: {
        useQuery: jest.fn(),
      },
    },
    savedViews: {
      saveView: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
        })),
      },
      deleteView: {
        useMutation: jest.fn(() => ({
          mutateAsync: jest.fn(),
        })),
      },
      shareView: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
        })),
      },
      markAsDefault: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
        })),
      },
    },
    measures: {
      getExportResults: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
        })),
      },
    },
    useUtils: jest.fn(() => ({
      savedViews: {
        getSavedViews: {
          invalidate: jest.fn(),
        },
      },
    })),
  },
}))

jest.mock('@/stores/actions', () => ({
  useActionsStore: jest.fn(),
}))

jest.mock('@/stores/viewStore', () => ({
  useViewStore: jest.fn(),
}))

jest.mock('@/stores/filter', () => ({
  useFilterStore: jest.fn(),
}))

jest.mock('@/stores/dates', () => ({
  useDateStore: jest.fn(),
}))

jest.mock('@/stores/useStore', () => ({
  __esModule: true,
  default: jest.fn((store: any) => store()),
}))

jest.mock('@/stores/userSession', () => ({
  useUserSessionStore: jest.fn(),
}))

jest.mock('@/hooks/useClickOutside', () => ({
  useClickOutside: jest.fn(),
}))

jest.mock('next/navigation', () => ({
  usePathname: jest.fn(() => '/dashboard'),
}))

jest.mock('@/hooks/use-toast', () => ({
  useToast: jest.fn(() => ({
    toast: jest.fn(),
  })),
}))

jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: {
    src: string | { src: string }
    height?: number
    width?: number
    'data-testid'?: string
  }) => {
    // Handle both string and object src props
    const srcValue = typeof props.src === 'string' ? props.src : props.src.src
    // Ensure the src always starts with a forward slash
    const src = srcValue.startsWith('/') ? srcValue : `/${srcValue}`
    return <img {...props} src={src} data-testid={props['data-testid']} />
  },
}))

describe('ActionsMenu', () => {
  beforeEach(() => {
    // Setup mock data
    const mockUsers = [
      { userId: '1', displayName: 'John Doe' },
      { userId: '2', displayName: 'Jane Smith' },
      { userId: '3', displayName: 'Bob Johnson' },
    ]

    // Setup mock store values
    ;(useActionsStore as unknown as jest.Mock).mockReturnValue({
      options: [],
      selectedOptions: [],
      toggleOption: jest.fn(),
      selectAllOptions: jest.fn(),
      SetShowProgress: jest.fn(),
      deselectAllOptions: jest.fn(),
    })
    ;(useViewStore as unknown as jest.Mock).mockReturnValue({
      tableState: {
        columnFilters: [],
        columnOrder: [],
        pagination: { pageSize: 10 },
      },
      currentView: { id: '123', viewName: 'Test View' },
      setCurrentView: jest.fn(),
    })
    ;(useFilterStore as unknown as jest.Mock).mockReturnValue({
      hideEmptyIndicators: false,
      appliedFilters: {
        measures: [],
        subOrganizations: [],
        submissionGroups: [],
        providers: [],
      },
    })
    ;(useUserSessionStore as unknown as jest.Mock).mockReturnValue({
      primaryMeasureType: 'test',
      organizationId: '123',
      organizationName: 'Test Org',
    })

    // Mock API response
    ;(api.users.getOrganizationUsers.useQuery as jest.Mock).mockReturnValue({
      data: mockUsers,
      isLoading: false,
    })
  })

  test('should filter users based on search term', async () => {
    render(<ActionsMenu />)

    // Open the actions menu
    const actionsButton = screen.getByText(/actions/i)
    fireEvent.click(actionsButton)

    // Open the share section
    const shareButton = screen.getByText(/share/i)
    fireEvent.click(shareButton)

    // Click on the user selection dropdown
    const userSelectInput = screen.getByPlaceholderText('Select User')
    fireEvent.click(userSelectInput)

    // Initially all users should be visible
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument()

    // Type in the search box
    fireEvent.change(userSelectInput, { target: { value: 'Jane' } })

    // Only Jane should be visible now
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    expect(screen.queryByText('Bob Johnson')).not.toBeInTheDocument()
  })

  test('should add user to selected users when clicked', async () => {
    render(<ActionsMenu />)

    // Open the actions menu
    const actionsButton = screen.getByText(/actions/i)
    fireEvent.click(actionsButton)

    // Open the share section
    const shareButton = screen.getByText(/share/i)
    fireEvent.click(shareButton)

    // Click on the user selection dropdown
    const userSelectInput = screen.getByPlaceholderText('Select User')
    fireEvent.click(userSelectInput)

    // Select a user
    const userOption = screen.getByText('Jane Smith')
    fireEvent.click(userOption)

    // The user should be added to the selected users list
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()

    // The search term should be cleared
    expect(userSelectInput).toHaveValue('')
  })

  test('should clear search term after selecting a user', async () => {
    render(<ActionsMenu />)

    // Open the actions menu
    const actionsButton = screen.getByText(/actions/i)
    fireEvent.click(actionsButton)

    // Open the share section
    const shareButton = screen.getByText(/share/i)
    fireEvent.click(shareButton)

    // Click on the user selection dropdown
    const userSelectInput = screen.getByPlaceholderText('Select User')
    fireEvent.click(userSelectInput)

    // Type in the search box
    fireEvent.change(userSelectInput, { target: { value: 'Jane' } })

    // Select the filtered user
    const userOption = screen.getByText('Jane Smith')
    fireEvent.click(userOption)

    // The search term should be cleared
    expect(userSelectInput).toHaveValue('')
  })
})
