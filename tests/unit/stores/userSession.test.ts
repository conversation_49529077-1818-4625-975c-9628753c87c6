import { processUserAccesses } from '../../../src/stores/userSession' // Adjust the import path as needed
import { SelectionType } from '@/enums/selectionType'
import { CitcUserRoles } from '@/shared/roles'
import { OrganizationRole } from '@/types/organizationRole'
import { hasPermission } from '@/lib/permissions'
describe('processUserAccesses', () => {
  test('should correctly map backend roles to frontend roles', () => {
    // Arrange
    const organizationId = 'org123'
    const orgRoles: OrganizationRole[] = [
      {
        organizationId: 'org123',
        roles: [CitcUserRoles.PATIENT_DETAIL_ACCESS, CitcUserRoles.REPORTING],
        subOrganizationRoles: []
      }
    ]

    // Act
    const result = processUserAccesses(organizationId, orgRoles, SelectionType.Organization)

    // Assert
    expect(result).toHaveProperty('permissions')
    expect(result.permissions).toBeInstanceOf(Set)
    const permissionsArray = Array.from(result.permissions)
    expect(permissionsArray).toContain(CitcUserRoles.PATIENT_DETAIL_ACCESS)
    expect(permissionsArray).toContain(CitcUserRoles.REPORTING)
  })

  test('should handle sub-organization roles correctly', () => {
    // Arrange
    const organizationId = 'org123'
    const orgRoles: OrganizationRole[] = [
      {
        organizationId: 'org123',
        roles: [CitcUserRoles.PATIENT_DETAIL_ACCESS],
        subOrganizationRoles: [
          {
            subOrganizationId: 'subOrg1',
            roles: [CitcUserRoles.PATIENT_DETAIL_ACCESS]
          },
          {
            subOrganizationId: 'subOrg2',
            roles: [CitcUserRoles.REPORTING]
          }
        ]
      }
    ]

    // Act
    const result = processUserAccesses(organizationId, orgRoles, SelectionType.Organization)

    // Assert
    expect(result).toHaveProperty('patientDetailAccess')
    expect(result.patientDetailAccess).toEqual({
      IsOrgLevelAccess: false,
      SubOrgs: ['subOrg1']
    })
  })

  test('should handle partner selection type', () => {
    // Arrange
    const organizationId = 'org123'
    const orgRoles: OrganizationRole[] = [
      {
        organizationId: 'org123',
        roles: [CitcUserRoles.PATIENT_DETAIL_ACCESS, CitcUserRoles.PATIENT_EXPLORER],
        subOrganizationRoles: []
      }
    ]

    // Act
    const result = processUserAccesses(organizationId, orgRoles, SelectionType.Partner)

    // Assert
    expect(result.patientDetailAccess.IsOrgLevelAccess).toBe(true)
    expect(result.hasPatientExplorerAccessAtPartner).toBe(true)
  })

  // hasPermissions Tests
  test('should handle empty permissions set correctly in hasPermission function', () => {
    // Arrange
    const emptyPermissions = new Set<CitcUserRoles>()
    const role = CitcUserRoles.PATIENT_DETAIL_ACCESS

    // Act
    const result = hasPermission(emptyPermissions, role)

    // Assert
    expect(result).toBe(false)
  })

  test('should handle permissions as a Record in hasPermission function', () => {
    // Arrange
    const permissionsRecord: Partial<Record<CitcUserRoles, boolean>> = {
      [CitcUserRoles.PATIENT_DETAIL_ACCESS]: true,
      [CitcUserRoles.REPORTING]: false,
      [CitcUserRoles.SCORECARD_MANAGER]: true
    }

    // Act & Assert
    expect(hasPermission(permissionsRecord as Record<CitcUserRoles, boolean>, CitcUserRoles.PATIENT_DETAIL_ACCESS)).toBe(true)
    expect(hasPermission(permissionsRecord as Record<CitcUserRoles, boolean>, CitcUserRoles.REPORTING)).toBe(false)
    expect(hasPermission(permissionsRecord as Record<CitcUserRoles, boolean>, CitcUserRoles.SCORECARD_MANAGER)).toBe(true)
    expect(hasPermission(permissionsRecord as Record<CitcUserRoles, boolean>, CitcUserRoles.PROVIDER_DETAILS_ACCESS)).toBe(false)
  })

  test('should return false for non-existent role in hasPermission function', () => {
    // Arrange
    const permissions = new Set<CitcUserRoles>([
      CitcUserRoles.PATIENT_DETAIL_ACCESS,
      CitcUserRoles.REPORTING
    ])
    const nonExistentRole = 'NON_EXISTENT_ROLE' as CitcUserRoles

    // Act
    const result = hasPermission(permissions, nonExistentRole)

    // Assert
    expect(result).toBe(false)
  })

  // ProcessUserAcessTests
  test('should handle case when organization is not found in orgRoles', () => {
    // Arrange
    const organizationId = 'nonexistentOrgId'
    const orgRoles: OrganizationRole[] = [
      {
        organizationId: 'someOtherOrgId',
        roles: [CitcUserRoles.PATIENT_DETAIL_ACCESS, CitcUserRoles.REPORTING],
        subOrganizationRoles: []
      }
    ]

    // Act
    const result = processUserAccesses(organizationId, orgRoles, SelectionType.Organization)

    // Assert
    expect(result.permissions).toBeInstanceOf(Set)
    expect(result.permissions.size).toBe(0)
    expect(result.patientDetailAccess).toEqual({
      IsOrgLevelAccess: false,
      SubOrgs: []
    })
    expect(result.hasPatientExplorerAccessAtPartner).toBe(false)
    expect(result.hasGlobalPatientExplorerAccess).toBe(false)
  })

  test('should correctly set isScorecardManager when SCORECARD_MANAGER role is present', () => {
    // Arrange
    const organizationId = 'org123'
    const orgRoles: OrganizationRole[] = [
      {
        organizationId: 'org123',
        roles: [CitcUserRoles.SCORECARD_MANAGER, CitcUserRoles.PATIENT_DETAIL_ACCESS],
        subOrganizationRoles: []
      }
    ]

    // Act
    const result = processUserAccesses(organizationId, orgRoles, SelectionType.Organization)

    // Assert
    expect(result.permissions).toContain(CitcUserRoles.SCORECARD_MANAGER)
  })

  test('should correctly set hasGlobalPatientExplorerAccess based on orgRoles', () => {
    // Arrange
    const organizationId = 'org123'
    const orgRoles: OrganizationRole[] = [
      {
        organizationId: 'org123',
        roles: [CitcUserRoles.PATIENT_EXPLORER],
        subOrganizationRoles: []
      },
      {
        organizationId: 'org456',
        roles: [CitcUserRoles.PATIENT_EXPLORER],
        subOrganizationRoles: []
      }
    ]

    // Mock the hasGlobalPatientExplorerAccess function
    jest.mock('@/lib/hasGlobalPatientExplorerAccess', () => ({
      hasGlobalPatientExplorerAccess: jest.fn().mockReturnValue(true)
    }))

    // Act
    const result = processUserAccesses(organizationId, orgRoles, SelectionType.Organization)

    // Assert
    expect(result.hasGlobalPatientExplorerAccess).toBe(true)
  })
})
