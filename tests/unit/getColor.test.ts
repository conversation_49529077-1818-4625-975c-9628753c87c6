// Mock Application Insights
jest.mock('@/lib/applicationInsights', () => ({
  __esModule: true,
  default: {
    trackTrace: jest.fn(),
    trackException: jest.fn(),
    trackEvent: jest.fn(),
    loadAppInsights: jest.fn(),
  }
}));

import { getColor } from '@/lib/getColor'
import { Cell, Row } from '@tanstack/react-table'
import { Performance } from '@/enums/performance'

describe('getColor', () => {
  const mockCell = (columnId: string, value: unknown): Cell<any, unknown> =>
    ({
      column: { id: columnId },
      getValue: () => value,
    }) as Cell<any, unknown>

  const mockRow = (index: number, scorecardDetailsList?: any[]): Row<any> =>
    ({
      index,
      original: { scorecardDetailsList },
    }) as Row<any>

  it('returns color for CY column with Poor performance', () => {
    const cell = mockCell('CY_2024', 100)
    const row = mockRow(0, [
      { columnName: 'CY_2024', performance: Performance.Poor },
    ])
    const result = getColor(cell, row, 'Other')
    expect(result).toBe('bg-[#fc4e47]')
  })

  it('returns color for Q column with Poor performance', () => {
    const cell = mockCell('Q1_2024', 100)
    const row = mockRow(0, [
      { columnName: 'Q1_2024', performance: Performance.Poor },
    ])
    const result = getColor(cell, row, 'Other')
    expect(result).toBe('bg-[#fc4e47]')
  })

  it('returns color for CY column with Caution performance', () => {
    const cell = mockCell('CY_2024', 100)
    const row = mockRow(0, [
      { columnName: 'CY_2024', performance: Performance.Caution },
    ])
    const result = getColor(cell, row, 'Other')
    expect(result).toBe('bg-[#f7ec9b]')
  })

  it('returns color for CY column with Good performance', () => {
    const cell = mockCell('CY_2024', 100)
    const row = mockRow(0, [
      { columnName: 'CY_2024', performance: Performance.Good },
    ])
    const result = getColor(cell, row, 'Other')
    expect(result).toBe('bg-[#9dde89]')
  })

  it('returns color for CY column with Exceptional performance', () => {
    const cell = mockCell('CY_2024', 100)
    const row = mockRow(0, [
      { columnName: 'CY_2024', performance: Performance.Exceptional },
    ])
    const result = getColor(cell, row, 'Other')
    expect(result).toBe('bg-[#57d348]')
  })

  it('returns default background for CY column with no performance', () => {
    const cell = mockCell('CY_2024', 100)
    const row = mockRow(0, [{ columnName: 'CY_2024' }])
    const result = getColor(cell, row, 'Other')
    expect(result).toBe('bg-transparent')
  })

  it('returns alternating row colors for non-performance columns', () => {
    const cell = mockCell('OtherColumn', 'value')
    const rowEven = mockRow(0)
    const rowOdd = mockRow(1)
    expect(getColor(cell, rowEven)).toBe('bg-white')
    expect(getColor(cell, rowOdd)).toBe('bg-[#F9F8F9]')
  })

  it('checks scorecardviewType to skip performance check for Hospital', () => {
    const cell = mockCell('CY_2024', 100)
    const row = mockRow(0, [
      { columnName: 'CY_2024', performance: Performance.Poor },
    ])
    const result = getColor(cell, row, 'Hospital')
    expect(result).toBe('bg-white')
  })
})
