import { jest } from '@jest/globals'
import { SelectionType } from '@/enums/selectionType'
import { CitcUserRoles } from '@/shared/roles'

// Mock console.log to prevent noise in test output
console.log = jest.fn()

jest.mock('@/lib/redis', jest.fn())
jest.mock('superjson', () => jest.fn())
jest.mock('@/auth', () => jest.fn())
jest.mock('@/lib/applicationInsights', () => ({
    loadAppInsights: jest.fn(),
}))
jest.mock('@/services/citc/citCUsers', () => jest.fn())
jest.mock('@/services/measureResults', () => jest.fn())
jest.mock('snowflake-sdk', () => jest.fn())
jest.mock('@/services/snowflake/SnowflakeRepositoryFactory', () => jest.fn())
jest.mock('@/services/azure/tableStorageWrapper', () => jest.fn())
jest.mock('@/services/reports/MVPService', () => jest.fn())

// Mock the env module
jest.mock('@/env', () => ({
    env: {
        NEXT_PUBLIC_OIDC_AUTHORITY: 'test-authority',
        NEXT_PUBLIC_OIDC_CLIENT_ID: 'test-client-id',
        NEXT_PUBLIC_OIDC_CLIENT_SECRET: 'test-client-secret',
        NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG: 'test-wellknown-config',
        NEXT_PUBLIC_OIDC_SCOPE: 'test-scope',
        NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNIN: 'test-redirect-url',
        NEXT_PUBLIC_OIDC_AUDIENCE: 'test-audience',
    },
}))

import { Context, requirePermissionsMiddleware } from "@/server/api/trpc"

// Mock CitCParametersService
const mockCitCParametersService = {
    // @ts-ignore
    getGlobalParametersAsync: jest.fn().mockResolvedValue([]),
    // @ts-ignore
    getOrganizationParametersAsync: jest.fn().mockResolvedValue([]),
    // @ts-ignore
    getPartnerParametersAsync: jest.fn().mockResolvedValue([]),
    // @ts-ignore
    GetAllOrganizationParametersByKeyNameAsync: jest.fn().mockResolvedValue([]),
    // @ts-ignore
    fetchData: jest.fn().mockResolvedValue([]),
    accessToken: 'mock-access-token',
    // @ts-ignore
    fetchWithType: jest.fn().mockResolvedValue({ data: [] }),
    ADMPrismaClient: jest.fn().mockReturnValue({}),
    HUBPrismaClient: jest.fn().mockReturnValue({})
}

// Create a mock session
const mockSession = {
    accessToken: 'mock-access-token',
    idToken: 'mock-id-token',
    uid: 'mock-user-id',
    role: ['Admin', 'User'],
    given_name: 'John',
    family_name: 'Doe',
    Organization: 'org123',
    'role:Organization': ['Admin', 'User'],
    expires: new Date(Date.now() + 3600 * 1000).toISOString(),
    user: {
        name: 'John Doe',
        email: '<EMAIL>',
    }
}

// Create mock headers
const mockHeaders = new Headers()

// Create mock organizations and partners
const mockOrganizations = [
    {
        id: 'org123',
        name: 'Organization 1',
        organizationId: 'org123',
        organizationName: 'Organization 1',
        organizationAddress: '123 Main St',
        organizationNotes: 'Test notes',
        brandingImageContent: 'base64-encoded-image',
        whitelabelImageContent: 'base64-encoded-image'
    },
    {
        id: 'org456',
        name: 'Organization 2',
        organizationId: 'org456',
        organizationName: 'Organization 2',
        organizationAddress: '456 Oak St',
        organizationNotes: 'Test notes 2',
        brandingImageContent: 'base64-encoded-image-2',
        whitelabelImageContent: 'base64-encoded-image-2'
    },
]

const mockPartners = [
    {
        id: 'partner1',
        name: 'Partner 1',
        organizations: [],
        roles: [CitcUserRoles.DASHBOARD_ADMINISTRATOR, CitcUserRoles.ADMINISTRATOR],
        isFullPartner: true
    },
    {
        id: 'partner2',
        name: 'Partner 2',
        organizations: [],
        roles: [CitcUserRoles.DASHBOARD_ADMINISTRATOR],
        isFullPartner: false
    },
]

const mockPartnersFrontEnd = [
    {
        id: 'partner1',
        name: 'Partner 1',
        organizations: [],
        roles: ['Dashboard Administrator', 'Administrator'],
        isFullPartner: true
    },
    {
        id: 'partner2',
        name: 'Partner 2',
        organizations: [],
        roles: ['Administrator'],
        isFullPartner: false
    },
]

// Create mock global parameters
const mockGlobalParameters = [
    { key: 'global1', value: 'value1' },
    { key: 'global2', value: 'value2' },
]

describe('requirePermissionsMiddleware', () => {
    // Reset mocks before each test
    beforeEach(() => {
        jest.clearAllMocks()
    })

    test('should throw FORBIDDEN error when no current organization ID is found', () => {
        // Arrange
        const requiredRoles = ['Admin', 'Editor']
        const ctx: Context = {
            organizationId: undefined, // No organization ID
            headers: mockHeaders,
            session: mockSession,
            organizations: mockOrganizations,
            partners: mockPartners,
            globalParameters: mockGlobalParameters,
            // @ts-ignore
            citCParametersService: mockCitCParametersService,
            selectionType: SelectionType.Organization,
            isCached: false,
        }
        // @ts-ignore
        const next = jest.fn().mockResolvedValue({ data: 'success' })

        // Act & Assert
        try {
            // @ts-ignore
            requirePermissionsMiddleware(requiredRoles, ctx, next)
            fail('Expected middleware to throw an error')
        } catch (error: any) {
            expect(error).toBeInstanceOf(Error)
            expect(error.name).toBe('TRPCError')
            expect(error.message).toBe('No current organization selected')
            expect(error.code).toBe('FORBIDDEN')
        }
        expect(next).not.toHaveBeenCalled()
        expect(console.log).toHaveBeenCalledWith('No current organization ID found in context')
    })

    test('should throw FORBIDDEN error when user has no required roles', () => {
        // Arrange
        const requiredRoles = ['Admin', 'Editor']
        const ctx: Context = {
            organizationId: 'org123',
            organizationRoles: [
                { organizationId: 'org123', roles: [CitcUserRoles.DASHBOARD_USER, CitcUserRoles.PATIENT_ADMINISTRATOR] },
            ],
            partnerRoles: [],
            headers: mockHeaders,
            session: mockSession,
            organizations: mockOrganizations,
            partners: mockPartners,
            globalParameters: mockGlobalParameters,
            // @ts-ignore
            citCParametersService: mockCitCParametersService,
            selectionType: SelectionType.Organization,
            isCached: false,
        }
        // @ts-ignore
        const next = jest.fn().mockResolvedValue({ data: 'success' })

        // Act & Assert
        try {
            // @ts-ignore
            requirePermissionsMiddleware(requiredRoles, ctx, next)
            fail('Expected middleware to throw an error')
        } catch (error: any) {
            expect(error).toBeInstanceOf(Error)
            expect(error.name).toBe('TRPCError')
            expect(error.message).toBe('You do not have the required permissions for this action in the current organization')
            expect(error.code).toBe('FORBIDDEN')
        }
        expect(next).not.toHaveBeenCalled()
        expect(console.log).toHaveBeenCalledWith(expect.stringContaining('Permission check failed'))
    })

    test('should allow next middleware when user has at least one required role in organization roles', async () => {
        // Arrange
        const requiredRoles = ['Admin', 'Editor']
        const ctx: Context = {
            organizationId: 'org123',
            organizationRoles: [
                { organizationId: 'org123', roles: ['Viewer', 'Admin'] },
            ],
            partnerRoles: [],
            headers: mockHeaders,
            session: mockSession,
            organizations: mockOrganizations,
            partners: mockPartners,
            globalParameters: mockGlobalParameters,
            // @ts-ignore
            citCParametersService: mockCitCParametersService,
            selectionType: SelectionType.Organization,
            isCached: false,
        }
        // @ts-ignore
        const next = jest.fn().mockResolvedValue({ data: 'success' })

        // Act
        // @ts-ignore
        const result = await requirePermissionsMiddleware(requiredRoles, ctx, next)

        // Assert
        expect(next).toHaveBeenCalled()
        expect(result).toEqual({ data: 'success' })
    })

    test('should allow next middleware when user has at least one required role in partner roles', async () => {
        // Arrange
        const requiredRoles = ['Admin', 'Editor']
        const ctx: Context = {
            organizationId: 'org123',
            organizationRoles: [
                { organizationId: 'org123', roles: ['Viewer', 'User'] },
            ],
            partnerRoles: [
                { organizationId: 'org123', roles: ['Editor', 'Partner'] },
            ],
            headers: mockHeaders,
            session: mockSession,
            organizations: mockOrganizations,
            partners: mockPartners,
            globalParameters: mockGlobalParameters,
            // @ts-ignore
            citCParametersService: mockCitCParametersService,
            selectionType: SelectionType.Partner,
            isCached: false,
        }
        // @ts-ignore
        const next = jest.fn().mockResolvedValue({ data: 'success' })

        // Act
        // @ts-ignore
        const result = await requirePermissionsMiddleware(requiredRoles, ctx, next)

        // Assert
        expect(next).toHaveBeenCalled()
        expect(result).toEqual({ data: 'success' })
    })

    test('should check roles for the current organization only', () => {
        // Arrange
        const requiredRoles = ['Admin']
        const ctx: Context = {
            organizationId: 'org123',
            organizationRoles: [
                { organizationId: 'org123', roles: ['Viewer'] }, // Current org, no required role
                { organizationId: 'org456', roles: ['Admin'] },  // Different org, has required role
            ],
            partnerRoles: [],
            headers: mockHeaders,
            session: mockSession,
            organizations: mockOrganizations,
            partners: mockPartners,
            globalParameters: mockGlobalParameters,
            // @ts-ignore
            citCParametersService: mockCitCParametersService,
            selectionType: SelectionType.Organization,
            isCached: false,
        }
        // @ts-ignore
        const next = jest.fn().mockResolvedValue({ data: 'success' })

        // Act & Assert
        try {
            // @ts-ignore
            requirePermissionsMiddleware(requiredRoles, ctx, next)
            fail('Expected middleware to throw an error')
        } catch (error: any) {
            expect(error).toBeInstanceOf(Error)
            expect(error.name).toBe('TRPCError')
            expect(error.message).toBe('You do not have the required permissions for this action in the current organization')
            expect(error.code).toBe('FORBIDDEN')
        }
        expect(next).not.toHaveBeenCalled()
        expect(console.log).toHaveBeenCalledWith(expect.stringContaining('Permission check failed'))
    })

    test('should handle empty roles arrays', () => {
        // Arrange
        const requiredRoles = ['Admin']
        const ctx: Context = {
            organizationId: 'org123',
            organizationRoles: [
                { organizationId: 'org123', roles: [] }, // Empty roles array
            ],
            partnerRoles: [],
            headers: mockHeaders,
            session: mockSession,
            organizations: mockOrganizations,
            partners: mockPartners,
            globalParameters: mockGlobalParameters,
            // @ts-ignore
            citCParametersService: mockCitCParametersService,
            selectionType: SelectionType.Organization,
            isCached: false,
        }
        // @ts-ignore
        const next = jest.fn().mockResolvedValue({ data: 'success' })

        // Act & Assert
        try {
            // @ts-ignore
            requirePermissionsMiddleware(requiredRoles, ctx, next)
            fail('Expected middleware to throw an error')
        } catch (error: any) {
            expect(error).toBeInstanceOf(Error)
            expect(error.name).toBe('TRPCError')
            expect(error.message).toBe('You do not have the required permissions for this action in the current organization')
            expect(error.code).toBe('FORBIDDEN')
        }
        expect(next).not.toHaveBeenCalled()
        expect(console.log).toHaveBeenCalledWith(expect.stringContaining('Permission check failed'))
    })

    test('should handle undefined roles', () => {
        // Arrange
        const requiredRoles = ['Admin']
        const ctx: Context = {
            organizationId: 'org123',
            organizationRoles: undefined,
            partnerRoles: undefined,
            headers: mockHeaders,
            session: mockSession,
            organizations: mockOrganizations,
            partners: mockPartners,
            globalParameters: mockGlobalParameters,
            // @ts-ignore
            citCParametersService: mockCitCParametersService,
            selectionType: SelectionType.Organization,
            isCached: false,
        }
        // @ts-ignore
        const next = jest.fn().mockResolvedValue({ data: 'success' })

        // Act & Assert
        try {
            // @ts-ignore
            requirePermissionsMiddleware(requiredRoles, ctx, next)
            fail('Expected middleware to throw an error')
        } catch (error: any) {
            expect(error).toBeInstanceOf(Error)
            expect(error.name).toBe('TRPCError')
            expect(error.message).toBe('You do not have the required permissions for this action in the current organization')
            expect(error.code).toBe('FORBIDDEN')
        }
        expect(next).not.toHaveBeenCalled()
        expect(console.log).toHaveBeenCalledWith(expect.stringContaining('Permission check failed'))
    })

    test('should combine organization and partner roles when checking permissions', async () => {
        // Arrange
        const requiredRoles = ['SuperAdmin']
        const ctx: Context = {
            organizationId: 'org123',
            organizationRoles: [
                { organizationId: 'org123', roles: ['Viewer'] },
            ],
            partnerRoles: [
                { organizationId: 'org123', roles: ['SuperAdmin'] },
            ],
            headers: mockHeaders,
            session: mockSession,
            organizations: mockOrganizations,
            partners: mockPartners,
            globalParameters: mockGlobalParameters,
            // @ts-ignore
            citCParametersService: mockCitCParametersService,
            selectionType: SelectionType.Organization,
            isCached: false,
        }
        // @ts-ignore
        const next = jest.fn().mockResolvedValue({ data: 'success' })

        // Act
        // @ts-ignore
        const result = await requirePermissionsMiddleware(requiredRoles, ctx, next)

        // Assert
        expect(next).toHaveBeenCalled()
        expect(result).toEqual({ data: 'success' })
    })

    test('should allow access if user has any of the required roles', async () => {
        // Arrange
        const requiredRoles = ['Admin', 'SuperAdmin', 'GlobalAdmin']
        const ctx: Context = {
            organizationId: 'org123',
            organizationRoles: [
                { organizationId: 'org123', roles: ['Viewer', 'GlobalAdmin'] }, // Has one of the required roles
            ],
            partnerRoles: [],
            headers: mockHeaders,
            session: mockSession,
            organizations: mockOrganizations,
            partners: mockPartners,
            globalParameters: mockGlobalParameters,
            // @ts-ignore
            citCParametersService: mockCitCParametersService,
            selectionType: SelectionType.Organization,
            isCached: false,
        }
        // @ts-ignore
        const next = jest.fn().mockResolvedValue({ data: 'success' })

        // Act
        // @ts-ignore
        const result = await requirePermissionsMiddleware(requiredRoles, ctx, next)

        // Assert
        expect(next).toHaveBeenCalled()
        expect(result).toEqual({ data: 'success' })
    })
})
