import React from 'react'
import { render } from '@testing-library/react'
import ACOPerformanceAnalysisPage from '@/app/(platform)/reports/aco-performance-analysis/page'

// Mock dayjs to return a consistent date for testing
jest.mock('dayjs', () => {
  const originalDayjs = jest.requireActual('dayjs')
  const mockDayjs = () => {
    return originalDayjs('2025-04-15') // Mock current date
  }
  mockDayjs.extend = originalDayjs.extend
  // Add any other dayjs methods you need
  return mockDayjs
})

// Mock the useSearchParams hook
jest.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: jest.fn((param) => (param === 'reportId' ? 'test-report-id' : null)),
  }),
}))

// Mock the client component
jest.mock('@/components/reports/aco/ACOPerformanceAnalysisPageContent', () => {
  return function MockACOPerformanceAnalysisPageContent({
    initialMeasures,
    initialDateRange,
  }: any) {
    return (
      <div data-testid="mock-client-component">
        <div data-testid="initial-measure-names">
          {initialMeasures.join(',')}
        </div>
        <div data-testid="initial-date-range">{initialDateRange}</div>
      </div>
    )
  }
})

describe('ACOPerformanceAnalysisPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the client component with initial props', async () => {
    // Arrange
    const expectedDateRange = '2024-01-01' // January 1st of previous year
    const mockMeasureNames: string[] = [] // Empty array to match updated behavior

    // Act
    const { findByTestId } = render(await ACOPerformanceAnalysisPage())

    // Assert
    const clientComponent = await findByTestId('mock-client-component')
    expect(clientComponent).toBeInTheDocument()

    const initialMeasureNames = await findByTestId('initial-measure-names')
    expect(initialMeasureNames).toHaveTextContent(mockMeasureNames.join(','))

    const initialDateRange = await findByTestId('initial-date-range')
    expect(initialDateRange).toHaveTextContent(expectedDateRange)
  })
})
