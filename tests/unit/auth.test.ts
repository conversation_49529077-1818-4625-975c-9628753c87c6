import { jest } from '@jest/globals'
import { JwtPayload } from 'jsonwebtoken'
import type { Account, Session, User } from 'next-auth'
import { env } from '@/env'
import * as authModule from '@/auth'

const originalConsoleLog = console.log
console.log = jest.fn()

// Mock the next-auth module
jest.mock('next-auth', () => {
    const mockAuth = jest.fn().mockReturnValue({
        auth: jest.fn(),
        handlers: {
            GET: jest.fn(),
            POST: jest.fn()
        },
        signIn: jest.fn(),
        signOut: jest.fn(),
        unstable_update: jest.fn()
    })

    return {
        __esModule: true,
        default: mockAuth
    }
})

// Mock the decodeAccessToken module
jest.mock('@/lib/decodeAccessToken', () => ({
    decodeToken: jest.fn(),
}))

// Mock the env module
jest.mock('@/env', () => ({
    env: {
        NEXT_PUBLIC_OIDC_AUTHORITY: 'test-authority',
        NEXT_PUBLIC_OIDC_CLIENT_ID: 'test-client-id',
        NEXT_PUBLIC_OIDC_CLIENT_SECRET: 'test-client-secret',
        NEXT_PUBLIC_CITC_WELLKNOWN_CONFIG: 'test-wellknown-config',
        NEXT_PUBLIC_OIDC_SCOPE: 'test-scope',
        NEXT_PUBLIC_OIDC_REDIRECT_URL_SIGNIN: 'test-redirect-url',
        NEXT_PUBLIC_OIDC_AUDIENCE: 'test-audience',
    },
}))

// Import the module after mocking dependencies
import { decodeToken } from '@/lib/decodeAccessToken'
import { jwtCallback, signInCallback, __resetTokenCache } from "@/auth"
import dayjs from 'dayjs'

afterAll(() => {
    console.log = originalConsoleLog
})

describe('signInCallback', () => {

    beforeEach(() => {
        // Reset all mocks before each test
        jest.resetAllMocks()
    })

    test('should return false when account is null', async () => {
        // Act
        const result = await signInCallback({ account: null })

        // Assert
        expect(result).toBe(false)
        expect(decodeToken).not.toHaveBeenCalled()
    })

    test('should return false when account is undefined', async () => {
        // Act
        const result = await signInCallback({ account: undefined })

        // Assert
        expect(result).toBe(false)
        expect(decodeToken).not.toHaveBeenCalled()
    })

    test('should return false when account has no access_token', async () => {
        // Arrange
        const account = {} as Account

        // Act
        const result = await signInCallback({ account })

        // Assert
        expect(result).toBe(false)
        expect(decodeToken).not.toHaveBeenCalled()
    })

    test('should return false when decodeToken returns null', async () => {
        // Arrange
        const account = { access_token: 'invalid-token' } as Account
        (decodeToken as jest.Mock).mockReturnValue(null)

        // Act
        const result = await signInCallback({ account })

        // Assert
        expect(result).toBe(false)
        expect(decodeToken).toHaveBeenCalledWith('invalid-token')
    })

    test('should return false when user has no required permissions', async () => {
        // Arrange
        const account = { access_token: 'valid-token' } as Account
        const decodedToken: JwtPayload = {
            // No 'role:Organization' or 'global:Partner' properties
            sub: 'user-123',
            exp: Math.floor(Date.now() / 1000) + 3600,
        };
        (decodeToken as jest.Mock).mockReturnValue(decodedToken)

        // Act
        const result = await signInCallback({ account })

        // Assert
        expect(result).toBe(false)
        expect(decodeToken).toHaveBeenCalledWith('valid-token')
    })

    test('should return false when user has empty organization roles array', async () => {
        // Arrange
        const account = { access_token: 'valid-token' } as Account
        const decodedToken: JwtPayload & { 'role:Organization': string[] } = {
            sub: 'user-123ABC',
            exp: Math.floor(Date.now() / 1000) + 3600,
            'role:Organization': [], // Empty array
        };
        (decodeToken as jest.Mock).mockReturnValue(decodedToken)

        // Act
        const result = await signInCallback({ account })

        // Assert
        expect(result).toBe(false)
        expect(decodeToken).toHaveBeenCalledWith('valid-token')
    })

    test('should return false when user has null organization roles', async () => {
        // Arrange
        const account = { access_token: 'valid-token' } as Account
        const decodedToken: JwtPayload & { 'role:Organization': object | null } = {
            sub: 'user-123',
            exp: Math.floor(Date.now() / 1000) + 3600,
            'role:Organization': null, // null "object"
        };
        (decodeToken as jest.Mock).mockReturnValue(decodedToken)

        // Act
        const result = await signInCallback({ account })

        // Assert
        expect(result).toBe(false)
        expect(decodeToken).toHaveBeenCalledWith('valid-token')
    })

    test('should return true when user has organization roles', async () => {
        // Arrange
        const account = { access_token: 'valid-token' } as Account
        const decodedToken: JwtPayload & { 'role:Organization': string[] } = {
            sub: 'user-123',
            exp: Math.floor(Date.now() / 1000) + 3600,
            'role:Organization': ['Admin', 'User'],
        };
        (decodeToken as jest.Mock).mockReturnValue(decodedToken)

        // Act
        const result = await signInCallback({ account })

        // Assert
        expect(result).toBe(true)
        expect(decodeToken).toHaveBeenCalledWith('valid-token')
    })

    test('should return true when user has organization roles as an object', async () => {
        // Arrange
        const account = { access_token: 'valid-token' } as Account
        const decodedToken: JwtPayload & { 'role:Organization': object } = {
            sub: 'user-123',
            exp: Math.floor(Date.now() / 1000) + 3600,
            'role:Organization': { Admin: true, User: true },
        };
        (decodeToken as jest.Mock).mockReturnValue(decodedToken)

        // Act
        const result = await signInCallback({ account })

        // Assert
        expect(result).toBe(true)
        expect(decodeToken).toHaveBeenCalledWith('valid-token')
    })

    test('should return true when user has global partner role', async () => {
        // Arrange
        const account = { access_token: 'valid-token' } as Account
        const decodedToken: JwtPayload & { 'global:Partner': string } = {
            sub: 'user-123',
            exp: Math.floor(Date.now() / 1000) + 3600,
            'global:Partner': 'Partner',
        };
        (decodeToken as jest.Mock).mockReturnValue(decodedToken)

        // Act
        const result = await signInCallback({ account })

        // Assert
        expect(result).toBe(true)
        expect(decodeToken).toHaveBeenCalledWith('valid-token')
    })

    test('should return true when user has both organization roles and global partner role', async () => {
        // Arrange
        const account = { access_token: 'valid-token' } as Account
        const decodedToken: JwtPayload & { 'role:Organization': string[], 'global:Partner': string } = {
            sub: 'user-123',
            exp: Math.floor(Date.now() / 1000) + 3600,
            'role:Organization': ['Admin', 'User'],
            'global:Partner': 'Partner',
        };
        (decodeToken as jest.Mock).mockReturnValue(decodedToken)

        // Act
        const result = await signInCallback({ account })

        // Assert
        expect(result).toBe(true)
        expect(decodeToken).toHaveBeenCalledWith('valid-token')
    })
})

describe('jwtCallback', () => {

    beforeEach(() => {
        jest.resetModules()
        jest.clearAllMocks()
        __resetTokenCache() 
    })

    // Now test jwtCallback
    test('should handle initial login by saving tokens and calculating expiration', async () => {
        // Arrange
        const account = {
            access_token: 'test_access_token',
            refresh_token: 'test_refresh_token',
            expires_in: 3600,
        } as Account
        const mockProps = {
            token: {},
            account,
        }
        const expectedExpiresAt = dayjs().utc().add(3600, 'second').unix()
    
        // Act
        const result = await jwtCallback(mockProps)
    
        // Assert
        expect(result).toEqual({
            access_token: 'test_access_token',
            refresh_token: 'test_refresh_token',
            expires_at: expect.any(Number),
        })
        expect(result.expires_at).toBeCloseTo(expectedExpiresAt, 1) // Allow 1 second difference
    })

    test('should return token if not expired', async () => {
        // Arrange
        const token = {
            access_token: 'valid_access_token',
            refresh_token: 'valid_refresh_token',
            expires_at: dayjs().add(1, 'hour').unix(), // Set expiration 1 hour from now
        }
        const mockProps = {
            token,
        }

        // Act
        const result = await jwtCallback(mockProps)

        // Assert
        expect(result).toEqual(token)
    })  
})
