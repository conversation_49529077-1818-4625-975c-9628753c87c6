// __tests__/sendEmail.test.ts
import sendEmail from '@/lib/sendEmail'
import sgMail from '@sendgrid/mail'

// Mock the SendGrid module
jest.mock('@sendgrid/mail', () => ({
  setApiKey: jest.fn(),
  send: jest.fn(),
}))

describe('sendEmail', () => {
  const mockedSend = sgMail.send as jest.Mock

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should send an email successfully', async () => {
    mockedSend.mockResolvedValueOnce([{ statusCode: 202 }, {}])

    const result = await sendEmail(
      {
        users: [
          {
            id: '1',
            email: '<EMAIL>',
            name: 'Test User',
          },
        ],
        subject: 'Test Subject',
        body: 'Hello',
      },
      'test-api-key'
    )

    expect(sgMail.setApiKey).toHaveBeenCalledWith(expect.any(String))
    expect(mockedSend).toHaveBeenCalledWith(
      [
        {
          to: '<EMAIL>',
          from: '<EMAIL>',
          subject: 'Test Subject',
          html: '',
          text: 'Hello',
        },
      ],
      true
    )
    expect(result[0].statusCode).toBe(202)
  })

  it('should use notification template when specified', async () => {
    mockedSend.mockResolvedValueOnce([{ statusCode: 202 }, {}])

    await sendEmail(
      {
        users: [
          {
            id: '1',
            email: '<EMAIL>',
            name: 'Test User',
          },
        ],
        subject: 'Test Subject',
        templateName: 'notification',
        templateData: {
          redirectUrl: 'https://example.com/notification',
          actionText: 'View Details',
        },
      },
      'test-api-key'
    )

    expect(mockedSend).toHaveBeenCalledWith(
      [
        {
          to: '<EMAIL>',
          from: '<EMAIL>',
          subject: 'Test Subject',
          html: expect.any(String),
        },
      ],
      true
    )
  })
})
