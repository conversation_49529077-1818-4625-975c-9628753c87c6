import { PerformanceGoal } from '@/types/scorecards/performanceGoal'
import { ScorecardDetails } from '@/types/scorecards/scorecardDetails'
import { INotation } from '@/enums/iNotation'
import { ScorecardView } from '@/enums/scorecardView'
import { Performance } from '@/enums/performance'
import dayjs from 'dayjs'
import { jest } from "@jest/globals";

// Mock TableClient implementation
const mockCreateTable = jest.fn()

jest.mock('@azure/data-tables', () => ({
  TableClient: jest.fn().mockImplementation(() => ({
    createTable: mockCreateTable,
    tableName: 'TestTable',
  })),
  odata: jest.fn((strings: TemplateStringsArray, ...values: any[]) => ''),
}))

jest.mock('@/env', () => ({
  env: {
    NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING:
      'AccountName=devstoreaccount1;AccountKey=key1234',
  },
}))

const getAllPerformanceGoals = jest.fn();
jest.mock('@/services/performanceGoals/getAllPerformanceGoals', () => ({
  getAllPerformanceGoals
}))

import { GetPerformanceByGoal } from '@/services/performanceGoals/setPerformanceGoals'

const getPerformanceGoals = (
  goal: number,
  yellowZone: number | undefined,
  exceptionalZone: number | undefined,
  startDate: dayjs.Dayjs = dayjs(),
  endDate: dayjs.Dayjs = dayjs(),
): PerformanceGoal[] => {
  return [
    {
      partitionKey: 'test',
      rowKey: 'test',
      measureIdentifier: 'test',
      entityId: 'test',
      goalLower: goal,
      goalUpper: goal,
      yellowZone: yellowZone,
      exceptionalPerformance: exceptionalZone,
      isYellowZoneFixedNumber: true,
      isExceptionalPerformanceNumber: true,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    },
  ]
}

describe('GetPerformanceByGoal', () => {
  it('should return Exceptional when rate is greater than or equal to exceptional zone start for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 90,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(75, 70, 85),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Exceptional)
  })

  it('should return Exceptional when rate is greater than or equal to exceptional zone start for Higher iNotation with a Quarterly View', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs('01/01/2023'),
      endDate: dayjs('03/31/2023'),
      rate: 90,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
        getPerformanceGoals(75, 70, 85, dayjs('01/01/2023'), dayjs('02/01/2023')),
        scorecardDetail,
        INotation.Higher,
        ScorecardView.Quarterly
    )
    expect(result.performance).toBe(Performance.Exceptional)
  })

  it('should return Good when rate is greater than goalEnd for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(65, 60, 85),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return Good when rate is greater than goalEnd for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
        getPerformanceGoals(65, 60, 85),
        scorecardDetail,
        INotation.Higher,
        ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return Good when rate is between goalStart and goalEnd for Higher iNotation with a Quarterly View', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs('01/01/2023'),
      endDate: dayjs('03/31/2023'),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(70, 65, 80, dayjs('01/01/2023'), dayjs('02/01/2023')),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Quarterly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return Caution when rate is between yellowZoneStart and yellowZoneEnd for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(75, 65, 80),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Caution)
  })

  it('should return Caution when rate is between yellowZoneStart and yellowZoneEnd for Higher iNotation with a Quarterly View', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs('01/01/2023'),
      endDate: dayjs('03/31/2023'),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
        getPerformanceGoals(75, 65, 80, dayjs('01/01/2023'), dayjs('02/01/2023')),
        scorecardDetail,
        INotation.Higher,
        ScorecardView.Quarterly
    )
    expect(result.performance).toBe(Performance.Caution)
  })

  it('should return Poor when rate is between redZoneStart and redZoneEnd for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(80, 75, 90),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return Poor when rate is less than goalStart and yellowZone is null for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails

    const result = GetPerformanceByGoal(
      getPerformanceGoals(75, undefined, 80),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return Exceptional when rate is less than or equal to exceptionalZoneStart for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 45,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(55, 60, 50),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Exceptional)
  })

  it('should return Good when rate is less than goalStart for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 55,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(60, 65, 50),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return Good when rate is between goalStart and goalEnd for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 45,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(45, 50, 40),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return Caution when rate is between yellowZoneStart and yellowZoneEnd for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 65,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(55, 70, 40),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Caution)
  })

  it('should return Poor when yellowZone is not defined rate is greater than goal', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 65,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
        getPerformanceGoals(55, undefined, 40),
        scorecardDetail,
        INotation.Lower,
        ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return Poor when rate is between redZoneStart and redZoneEnd for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 100,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(50, 70, 40),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return Poor when rate is between redZoneStart and redZoneEnd for Higher iNotation with rate of zero', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 0,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
        getPerformanceGoals(50, 70, 40),
        scorecardDetail,
        INotation.Higher,
        ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return Poor when rate is greater than goalStart and yellowZone is null for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 90,
    } as ScorecardDetails

    const result = GetPerformanceByGoal(
      getPerformanceGoals(75, undefined, 50),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return NoData when goal is null', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 75,
    } as ScorecardDetails
    const performanceGoals: PerformanceGoal[] = []
    const result = GetPerformanceByGoal(
      performanceGoals,
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.NoData)
  })

  it('should return a poor performance rating for a Quarterly breakdown, and an undefined exceptionalZone', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs('01-01-2023'),
      endDate: dayjs('03-31-2023'),
      rate: 74.84,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
        getPerformanceGoals(82, 80, undefined, dayjs('01/01/2023'), dayjs('02/01/2023')),
        scorecardDetail,
        INotation.Higher,
        ScorecardView.Quarterly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return a poor performance rating for a Quarterly breakdown and zero value, and an undefined exceptionalZone', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs('01-01-2023'),
      endDate: dayjs('03-31-2023'),
      rate: 0,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
        getPerformanceGoals(82, 80, undefined, dayjs('01/01/2023'), dayjs('02/01/2023')),
        scorecardDetail,
        INotation.Higher,
        ScorecardView.Quarterly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return a good performance rating for a Quarterly breakdown, and an undefined exceptionalZone', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs('01-01-2023'),
      endDate: dayjs('03-31-2023'),
      rate: 84.22,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
        getPerformanceGoals(82, 80, undefined, dayjs('01/01/2023'), dayjs('02/01/2023')),
        scorecardDetail,
        INotation.Higher,
        ScorecardView.Quarterly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return an caution performance rating for a Quarterly breakdown, and an undefined exceptionalZone', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs('01-01-2023'),
      endDate: dayjs('03-31-2023'),
      rate: 81.00,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
        getPerformanceGoals(82, 80, undefined, dayjs('01/01/2023'), dayjs('02/01/2023')),
        scorecardDetail,
        INotation.Higher,
        ScorecardView.Quarterly
    )
    expect(result.performance).toBe(Performance.Caution)
  })
})
