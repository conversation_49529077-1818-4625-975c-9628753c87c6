import { GetPerformanceByGoal } from '@/services/performanceGoals/setPerformanceGoals'
import { PerformanceGoal } from '@/types/scorecards/performanceGoal'
import { ScorecardDetails } from '@/types/scorecards/scorecardDetails'
import { INotation } from '@/enums/iNotation'
import { ScorecardView } from '@/enums/scorecardView'
import { Performance } from '@/enums/performance'
import dayjs from 'dayjs'

// Mock TableClient implementation
const mockCreateTable = jest.fn()

jest.mock('@azure/data-tables', () => ({
  TableClient: jest.fn().mockImplementation(() => ({
    createTable: mockCreateTable,
    tableName: 'TestTable',
  })),
  odata: jest.fn((strings: TemplateStringsArray, ...values: any[]) => ''),
}))

jest.mock('@/env', () => ({
  env: {
    NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING:
      'AccountName=devstoreaccount1;AccountKey=key1234',
  },
}))

const getPerformanceGoals = (
  goal: number,
  yellowZone: number | undefined,
  exceptionalZone: number
): PerformanceGoal[] => {
  return [
    {
      partitionKey: 'test',
      rowKey: 'test',
      measureIdentifier: 'test',
      entityId: 'test',
      goalLower: goal,
      goalUpper: goal,
      yellowZone: yellowZone,
      exceptionalPerformance: exceptionalZone,
      isYellowZoneFixedNumber: true,
      isExceptionalPerformanceNumber: true,
      startDate: dayjs().toISOString(),
      endDate: dayjs().toISOString(),
    },
  ]
}

describe('GetPerformanceByGoal', () => {
  it('should return Exceptional when rate is greater than or equal to exceptional zone start for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 90,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(75, 70, 85),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Exceptional)
  })

  it('should return Good when rate is greater than goalEnd for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(65, 60, 85),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return Good when rate is between goalStart and goalEnd for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(70, 65, 80),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return Caution when rate is between yellowZoneStart and yellowZoneEnd for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(75, 65, 80),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Caution)
  })

  it('should return Poor when rate is between redZoneStart and redZoneEnd for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(80, 75, 90),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return Poor when rate is less than goalStart and yellowZone is null for Higher iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 70,
    } as ScorecardDetails

    const result = GetPerformanceByGoal(
      getPerformanceGoals(75, undefined, 80),
      scorecardDetail,
      INotation.Higher,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return Exceptional when rate is less than or equal to exceptionalZoneStart for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 45,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(55, 60, 50),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Exceptional)
  })

  it('should return Good when rate is less than goalStart for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 55,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(60, 65, 50),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return Good when rate is between goalStart and goalEnd for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 45,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(45, 50, 40),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Good)
  })

  it('should return Caution when rate is between yellowZoneStart and yellowZoneEnd for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 65,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(55, 70, 40),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Caution)
  })

  it('should return Poor when rate is between redZoneStart and redZoneEnd for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 100,
    } as ScorecardDetails
    const result = GetPerformanceByGoal(
      getPerformanceGoals(50, 70, 40),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return Poor when rate is greater than goalStart and yellowZone is null for Lower iNotation', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 90,
    } as ScorecardDetails

    const result = GetPerformanceByGoal(
      getPerformanceGoals(75, undefined, 50),
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.Poor)
  })

  it('should return NoData when goal is null', () => {
    const scorecardDetail: ScorecardDetails = {
      startDate: dayjs(),
      endDate: dayjs(),
      rate: 75,
    } as ScorecardDetails
    const performanceGoals: PerformanceGoal[] = []
    const result = GetPerformanceByGoal(
      performanceGoals,
      scorecardDetail,
      INotation.Lower,
      ScorecardView.Monthly
    )
    expect(result.performance).toBe(Performance.NoData)
  })
})
