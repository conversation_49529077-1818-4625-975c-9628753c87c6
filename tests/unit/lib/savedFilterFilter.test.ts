import { PrimaryMeasureTypeConstants } from '@/enums/primaryMeasureTypeConstants'
import { savedFilterFilter } from '@/lib/savedFilterFilter'
import { FilterMetaData } from '@/types/filterMetaData'
import { FilterQuery } from '@/types/filterQuery'
import { SavedFilter } from '@/types/savedFilter'

describe('savedFilterFilter', () => {
  test('should match saved filter when all criteria match', () => {
    // Arrange
    const filterMetadata: FilterMetaData = {
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      facilities: [],
    }

    const savedFilter: SavedFilter = {
      id: 1,
      filterName: 'Test Filter',
      filterMetadata: JSON.stringify(filterMetadata),
      userId: 'user123',
    }

    const query: FilterQuery = {
      filterName: 'Test Filter',
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      organizations: [],
      facilities: [],
      userId: 'user123',
      partnerId: 'partner123',
      isPartner: false,
    }

    // Act
    const result = savedFilterFilter(savedFilter, query)

    // Assert
    expect(result).toBe(true)
  })

  test('should not match when filter name differs', () => {
    // Arrange
    const filterMetadata: FilterMetaData = {
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      facilities: [],
    }

    const savedFilter: SavedFilter = {
      id: 1,
      filterName: 'Test Filter',
      filterMetadata: JSON.stringify(filterMetadata),
      userId: 'user123',
    }

    const query: FilterQuery = {
      filterName: 'Different Filter',
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      organizations: [],
      facilities: [],
      userId: 'user123',
      partnerId: 'partner123',
      isPartner: false,
    }

    // Act
    const result = savedFilterFilter(savedFilter, query)

    // Assert
    expect(result).toBe(false)
  })

  test('should not match when page differs', () => {
    // Arrange
    const filterMetadata: FilterMetaData = {
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      facilities: [],
    }

    const savedFilter: SavedFilter = {
      id: 1,
      filterName: 'Test Filter',
      filterMetadata: JSON.stringify(filterMetadata),
      userId: 'user123',
    }

    const query: FilterQuery = {
      filterName: 'Test Filter',
      page: 'dashboard',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      organizations: [],
      facilities: [],
      userId: 'user123',
      partnerId: 'partner123',
      isPartner: false,
    }

    // Act
    const result = savedFilterFilter(savedFilter, query)

    // Assert
    expect(result).toBe(false)
  })

  test('should not match when measure type differs', () => {
    // Arrange
    const filterMetadata: FilterMetaData = {
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      facilities: [],
    }

    const savedFilter: SavedFilter = {
      id: 1,
      filterName: 'Test Filter',
      filterMetadata: JSON.stringify(filterMetadata),
      userId: 'user123',
    }

    const query: FilterQuery = {
      filterName: 'Test Filter',
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.HospitalMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      organizations: [],
      facilities: [],
      userId: 'user123',
      partnerId: 'partner123',
      isPartner: false,
    }

    // Act
    const result = savedFilterFilter(savedFilter, query)

    // Assert
    expect(result).toBe(false)
  })

  test('should handle malformed JSON in filterMetadata', () => {
    // Arrange
    const savedFilter: SavedFilter = {
      id: 1,
      filterName: 'Test Filter',
      filterMetadata: 'invalid json',
      userId: 'user123',
    }

    const query: FilterQuery = {
      filterName: 'Test Filter',
      page: 'measures',
      measureType: PrimaryMeasureTypeConstants.AmbulatoryMeasures,
      measures: [],
      hospitals: [],
      groups: [],
      providers: [],
      organizations: [],
      facilities: [],
      userId: 'user123',
      partnerId: 'partner123',
      isPartner: false,
    }

    // Act & Assert
    expect(() => savedFilterFilter(savedFilter, query)).toThrow()
  })
})
